# Repository Context Analysis - TalentForge Pro

## Project Overview

**TalentForge Pro** is an intelligent talent assessment and position matching system that leverages hybrid intelligence architecture (rule engine + LLM) to evaluate candidates across multiple dimensions and match them with suitable job positions.

### Project Type & Purpose
- **Type**: Full-stack enterprise web application
- **Domain**: Human Resources & Talent Management
- **Architecture**: Microservices with AI/ML integration
- **Target Users**: HR professionals, recruiters, hiring managers
- **Core Value Proposition**: AI-powered candidate evaluation and position matching

## Technology Stack Analysis

### Backend Infrastructure
- **Framework**: FastAPI 0.110+ (Python 3.12)
- **Database**: PostgreSQL 17 with pgvector 0.8.0 for vector operations
- **Caching**: Redis 7.4.4 with password authentication
- **Object Storage**: MinIO for resume and document storage
- **Task Queue**: Celery with Redis broker
- **API Architecture**: RESTful with async/await patterns
- **Authentication**: JWT with refresh tokens + RBAC permissions

### Frontend Technology
- **Framework**: Next.js 15.4.1 with React 19
- **Language**: TypeScript 5.5+ with strict mode
- **Package Manager**: pnpm 10.13.1
- **Styling**: Tailwind CSS 3.4.1 with component libraries
- **UI Components**: Radix UI + shadcn/ui design system
- **State Management**: Redux Toolkit + TanStack Query
- **Internationalization**: next-intl with locale support (en/zh)

### AI/ML Services
- **LLM Integration**: Multi-provider support (DeepSeek, Ollama, Moonshot, OpenRouter, Qwen, Zhipu)
- **Embedding Models**: BGE-M3 (1024-dim) primary, OpenAI (1536-dim) backup
- **Vector Operations**: pgvector with HNSW indexing
- **OCR Processing**: PaddleOCR 2.7.0 with GPU support
- **Model Architecture**: BERT + BiLSTM for matching, MLP for evaluation

### Infrastructure & DevOps
- **Containerization**: Docker with multi-service composition
- **Reverse Proxy**: Nginx with unified proxy architecture
- **Monitoring**: Prometheus integration (optional)
- **Development**: Hot-reload for both frontend and backend
- **Networks**: Isolated Docker network (**********/16)
- **Volumes**: Persistent data for PostgreSQL, Redis, MinIO

## Project Structure & Organization

### Root Directory Architecture
The project follows a **Clean Root Directory** principle with strict file organization:

```
talent_forge_pro/
├── app/                    # Application code (all Docker services)
│   ├── backend/           # FastAPI application
│   ├── frontend/          # Next.js application
│   ├── configs/           # Service configurations (nginx, prometheus)
│   ├── scripts/           # Development and deployment scripts
│   └── docker-compose.yml # Multi-service orchestration
├── docs/                  # Documentation and technical specs
├── archive/               # Historical artifacts and deprecated code
├── examples/              # Code examples and patterns
├── CLAUDE.md             # Main project documentation
├── Makefile              # Unified development commands
└── README.md             # Project overview
```

### Backend Application Structure
Classic **layered architecture** with domain separation:

```
app/backend/app/
├── api/v1/               # API endpoints and routing
├── core/                 # Configuration, database, security
├── crud/                 # Data access layer
├── models/               # SQLAlchemy database models  
├── schemas/              # Pydantic request/response models
├── services/             # Business logic layer
├── tasks.py              # Celery task definitions
└── worker.py             # Celery worker configuration
```

### Frontend Application Structure
Modern **Next.js App Router** with component organization:

```
app/frontend/
├── app/                  # Next.js 13+ App Router
├── components/           # Reusable UI components
├── hooks/                # Custom React hooks
├── lib/                  # Utilities and configurations
├── services/             # API service layer
├── store/                # Redux state management
├── types/                # TypeScript type definitions
└── messages/             # i18n translation files
```

## Development Patterns & Conventions

### Backend Patterns
- **Async Architecture**: All database operations use async/await
- **CRUD Pattern**: Generic base CRUD with specialized extensions
- **Service Layer**: Business logic separated from API controllers  
- **Pydantic Validation**: Comprehensive input/output validation
- **Snowflake IDs**: Custom ID generation for distributed systems
- **Permission-Based Access**: Fine-grained RBAC implementation

### Frontend Patterns
- **Component Architecture**: Functional components with hooks
- **Type Safety**: Strict TypeScript with comprehensive type definitions
- **API Integration**: Centralized API client with token management
- **State Management**: Redux Toolkit for global state, React Query for server state
- **Form Handling**: React Hook Form with Zod validation
- **Responsive Design**: Mobile-first with Tailwind utilities

### Code Quality Standards
- **Formatting**: Black (Python), Prettier (TypeScript)
- **Linting**: Ruff (Python), ESLint (TypeScript)
- **Type Checking**: mypy (Python), TypeScript compiler
- **Testing**: pytest (Backend), Jest + Playwright (Frontend)
- **Import Organization**: Absolute imports with consistent structure

## API Design & Architecture

### Authentication Flow
- **Token-based**: JWT access tokens (60min) + refresh tokens (7 days)
- **Development Mode**: Bypass token for development efficiency
- **RBAC Integration**: Role and permission-based access control
- **Cookie + LocalStorage**: Dual storage for SSR/CSR compatibility

### API Conventions
- **RESTful Design**: Standard HTTP methods with resource-based URLs
- **Unified Response Format**: Consistent JSON structure across endpoints
- **Error Handling**: Standardized error codes and messages
- **Pagination**: Uniform pagination with skip/limit parameters
- **Versioning**: API versioning with v1 namespace

### Core Domain Models
1. **User Management**: Users, roles, permissions with RBAC
2. **Candidate Management**: Candidates, resumes, evaluations
3. **Position Management**: Job positions, requirements, matching
4. **Assessment System**: Questionnaires, evaluations, scoring
5. **Vector Operations**: Embeddings, similarity matching
6. **Application Workflow**: Forms, submissions, processing

## Testing Architecture

### Backend Testing
- **Framework**: pytest with asyncio support
- **Coverage**: Unit tests (>80% target), integration tests
- **Mocking**: pytest-mock for external dependencies
- **Database**: Test database with transaction rollback
- **AI Services**: Mock providers for LLM/embedding calls

### Frontend Testing  
- **Unit Testing**: Jest with React Testing Library
- **E2E Testing**: Playwright with comprehensive user journeys
- **Visual Testing**: Accessibility testing with jest-axe
- **API Mocking**: MSW (Mock Service Worker) for HTTP mocking
- **Test Organization**: Feature-based test structure

### Existing E2E Test Coverage
Comprehensive user journey testing exists in `app/frontend/__tests__/e2e/`:
- **critical-user-journeys.spec.ts**: Complete CRUD operations for candidates
- **ai-questionnaire-generation.spec.ts**: AI-powered questionnaire workflows  
- **application-flow.spec.ts**: Full application submission process
- **Test Utilities**: Auth helpers, candidate helpers, test data fixtures

## Development Workflow

### Local Development Setup
```bash
# Unified development commands via Makefile
make setup        # Initial project setup
make up          # Start all services
make down        # Stop all services  
make logs        # View service logs
make status      # Check service health
```

### Service Architecture
- **Unified Proxy**: All services accessible via localhost:8088
- **Hot Reload**: Both frontend and backend support live reloading
- **Database Migrations**: Alembic for schema version control
- **Package Management**: Poetry (backend), pnpm (frontend)

### Quality Gates
The project implements comprehensive quality controls:
- **Pre-commit Validation**: File organization, dependency checks
- **Model Validation**: Database relationship verification on startup
- **Health Monitoring**: Multi-service health check endpoints
- **Storage Validation**: MinIO bucket auto-creation and verification

## Integration Points for E2E Testing

### Authentication Integration
- **Development Token**: `dev_bypass_token_2025_talentforge` for testing
- **Multiple Auth Methods**: Form-based and JSON-based login endpoints
- **Session Management**: Automatic token refresh with fallback handling

### API Endpoints for Testing
```
# Core CRUD Operations
GET /api/v1/candidates/              # List candidates with pagination
POST /api/v1/candidates/             # Create new candidate
GET /api/v1/candidates/{id}          # Get candidate details
PUT /api/v1/candidates/{id}          # Update candidate
DELETE /api/v1/candidates/{id}       # Delete candidate

# File Operations  
POST /api/v1/candidates/{id}/resume  # Upload resume
GET /api/v1/candidates/{id}/resume   # Download resume

# Search & Filtering
GET /api/v1/candidates/search        # Search candidates
GET /api/v1/candidates/filters       # Get filter options

# Health & Status
GET /health                          # Basic health check
GET /health/detailed                 # Comprehensive system status
```

### Frontend Routes for Testing
```
/login                    # Authentication page
/dashboard               # Main dashboard  
/candidates              # Candidate list view
/candidates/new          # Create candidate form
/candidates/{id}         # Candidate detail view
/candidates/{id}/edit    # Edit candidate form
/positions               # Job positions management
/assessments             # Assessment management
```

### Test Data Requirements
- **User Accounts**: Admin, recruiter, viewer roles
- **Sample Candidates**: Various status states and data completeness
- **Resume Files**: PDF, DOC, DOCX formats for upload testing
- **Position Data**: Job descriptions for matching tests
- **Questionnaires**: Assessment templates and responses

## Constraints & Considerations

### Performance Constraints
- **API Response Time**: <200ms target for standard operations
- **Frontend Load Time**: <3s first paint, <1s subsequent navigation
- **Vector Operations**: <200ms for similarity searches
- **OCR Processing**: <120s timeout for resume processing

### Security Considerations
- **JWT Validation**: All protected endpoints require valid tokens
- **Permission Checks**: RBAC validation on sensitive operations
- **File Upload**: Virus scanning and type validation for resumes
- **CORS Policy**: Restricted origins for API access

### Scalability Factors
- **Database**: PostgreSQL with connection pooling
- **Caching**: Redis for session and application caching
- **File Storage**: MinIO for distributed object storage
- **Background Tasks**: Celery for async processing

### Integration Limitations
- **AI Provider Dependencies**: Fallback chains for LLM services
- **GPU Requirements**: OCR and embedding services need GPU support
- **Network Dependencies**: External AI API availability
- **Storage Dependencies**: MinIO bucket accessibility

## Recommendations for E2E Testing Implementation

### Priority Test Scenarios
1. **Authentication Flow**: Login, token refresh, logout, session expiration
2. **Candidate CRUD**: Full lifecycle from creation to deletion
3. **File Upload**: Resume upload with various formats and sizes
4. **Search & Filter**: Advanced search with multiple criteria
5. **Bulk Operations**: Multi-select operations and export
6. **Responsive Design**: Mobile and desktop viewport testing
7. **Error Handling**: Network failures and API errors
8. **Internationalization**: Language switching and localized content

### Test Environment Setup
- **Isolated Test Database**: Separate database with seed data
- **Mock AI Services**: Stub external LLM/embedding providers
- **Test User Accounts**: Pre-created users with different permission levels  
- **Sample Data**: Representative candidates, positions, and assessments

### Integration Points
- **API Testing**: Direct HTTP calls for backend validation
- **UI Testing**: Playwright browser automation for user interactions
- **Visual Testing**: Screenshot comparison for UI regression detection
- **Performance Testing**: Response time and load testing
- **Accessibility Testing**: WCAG compliance validation

This repository analysis provides comprehensive context for implementing robust e2e testing strategies that align with the existing architecture, patterns, and constraints of the TalentForge Pro system.