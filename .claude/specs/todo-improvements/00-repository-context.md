# TalentForge Pro - Repository Context for TODO Improvements

## Project Summary
- **Type**: Full-stack AI-powered talent assessment platform
- **Stack**: FastAPI (Python 3.12) + Next.js (React 19) + PostgreSQL with pgvector
- **Architecture**: Microservices with Docker containerization
- **AI Integration**: Multi-provider LLM support with vector embeddings

## Relevant Context for TODO Items

### 1. MinIO Upload Service Implementation
**Location**: `/app/backend/app/services/recruitment_service.py:609`
**Context**:
- <PERSON><PERSON> already configured and running in Docker environment
- Storage service exists at `/app/backend/app/services/storage_service.py`
- Service has comprehensive MinIO integration with security validation
- Need to connect recruitment service to existing storage service

### 2. Debug Statements Cleanup
**Location**: `/app/backend/app/services/candidate_stats.py` (multiple lines)
**Context**:
- Project uses structured logging with Python logging module
- Backend has established logging patterns in other services
- Debug prints should be converted to proper logging statements
- Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL

### 3. Last Active Logic Improvement
**Location**: `/app/backend/app/services/candidate_stats.py:108`
**Context**:
- Database uses SQLAlchemy 2.0 with async support
- Candidates table has timestamp fields for tracking activity
- Need proper handling of last_active_at field
- Should follow existing datetime handling patterns in codebase

## Code Patterns to Follow
- **Service Layer**: Business logic in service files, not in API endpoints
- **Async/Await**: All database operations use async patterns
- **Error Handling**: Use structured exception handling with custom error codes
- **Logging**: Use Python logging module, not print statements
- **Type Hints**: All functions should have proper type annotations
- **Docstrings**: Document all public functions with Google-style docstrings

## Testing Requirements
- Unit tests using pytest with async support
- Integration tests for critical workflows
- Mock external services (MinIO, database) in tests
- Target >80% code coverage for new code

## File Organization
- Services in `/app/backend/app/services/`
- Tests in `/app/backend/tests/`
- No new files in root directory
- Follow existing module structure