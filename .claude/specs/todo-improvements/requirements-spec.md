# Technical Implementation Specification: Backend Service Improvements

## Problem Statement
- **Business Issue**: Three critical improvements needed in backend services: MinIO integration, debug statement cleanup, and last active tracking
- **Current State**: Recruitment service has TODO comment for MinIO upload, candidate stats service uses print statements, and last active logic is incomplete
- **Expected Outcome**: Fully functional file upload service, proper logging throughout, and accurate user activity tracking

## Solution Overview
- **Approach**: Leverage existing StorageService for MinIO integration, convert debug prints to structured logging, implement proper timestamp tracking with database updates
- **Core Changes**: Import and integrate storage service, replace print statements with logger calls, add last_active_at update mechanism
- **Success Criteria**: File uploads work through MinIO, no print statements in production code, accurate activity timestamps in database

## Technical Implementation

### Database Changes
**Tables to Modify**: None - `candidates` table already has `last_active_at` column (DateTime with timezone)
**New Tables**: None required
**Migration Scripts**: No migrations needed, leveraging existing schema

### Code Changes

#### 1. MinIO Upload Service Integration

**Files to Modify**: 
- `/app/backend/app/services/recruitment_service.py` (line 609)

**Import Statements**:
```python
# Add to existing imports at top of file
from app.services.storage_service import StorageService
from io import BytesIO
```

**Function Implementation**:
```python
# Replace TODO comment at line 609 with:
async def _upload_export_to_storage(
    self,
    file_content: bytes,
    file_name: str,
    content_type: str,
    export_id: str
) -> str:
    """
    Upload export file to MinIO storage
    
    Args:
        file_content: Binary file content
        file_name: Name of the file
        content_type: MIME type of the file
        export_id: Unique export identifier
        
    Returns:
        Public URL to access the uploaded file
        
    Raises:
        HTTPException: If upload fails
    """
    try:
        # Initialize storage service
        storage_service = StorageService()
        
        # Create object key for exports
        object_key = f"exports/{export_id}/{file_name}"
        
        # Prepare file stream
        file_stream = BytesIO(file_content)
        
        # Upload to MinIO
        upload_result = await storage_service.upload_file(
            file_data=file_stream,
            object_key=object_key,
            content_type=content_type,
            metadata={
                "export_id": export_id,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "file_type": "recruitment_export"
            }
        )
        
        # Generate presigned URL (24 hours expiry)
        file_url = storage_service.generate_presigned_url(
            object_name=object_key,
            expires=timedelta(hours=24)
        )
        
        return file_url
        
    except Exception as e:
        logger.error(f"Failed to upload export file {file_name}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"FILE_UPLOAD_FAILED: {str(e)}"
        )

# Update the existing export method to use this function:
# Replace line 611 with:
file_url = await self._upload_export_to_storage(
    file_content=file_content,
    file_name=file_name,
    content_type=content_type,
    export_id=export_id
)
```

#### 2. Debug Statements Cleanup

**Files to Modify**: 
- `/app/backend/app/services/candidate_stats.py` (lines 84, 90, 94, 96, 98)

**Import Statements** (add if not present):
```python
import logging

logger = logging.getLogger(__name__)
```

**Specific Line Replacements**:

**Line 84**: Replace `print(f"DEBUG: Cache disabled, executing fresh queries for user {current_user.id}")`
```python
logger.debug(f"Cache disabled, executing fresh queries for user {current_user.id}")
```

**Line 90**: Replace `print(f"DEBUG: Starting total candidates query for user {current_user.id}")`
```python
logger.debug(f"Starting total candidates query for user {current_user.id}")
```

**Line 94**: Replace `print(f"DEBUG: Total candidates query result: {total_count}")`
```python
logger.debug(f"Total candidates query result: {total_count}")
```

**Line 96**: Replace `print(f"DEBUG: Total candidates query failed: {str(e)}")`
```python
logger.error(f"Total candidates query failed: {str(e)}")
```

**Line 98**: Replace `print(f"DEBUG: Stack trace: {traceback.format_exc()}")`
```python
logger.error(f"Stack trace: {traceback.format_exc()}")
```

#### 3. Last Active Logic Implementation

**Files to Modify**:
- `/app/backend/app/services/candidate_stats.py` (line 108-109)
- `/app/backend/app/crud/candidate.py` (add new method)

**New Method in CRUDCandidate**:
```python
# Add to /app/backend/app/crud/candidate.py

async def update_last_active(
    self,
    db: AsyncSession,
    candidate_id: int
) -> bool:
    """
    Update candidate's last_active_at timestamp
    
    Args:
        db: Database session
        candidate_id: ID of the candidate
        
    Returns:
        True if update was successful
    """
    try:
        from datetime import datetime, timezone
        
        # Update the last_active_at field
        stmt = (
            update(Candidate)
            .where(Candidate.id == candidate_id)
            .values(last_active_at=datetime.now(timezone.utc))
        )
        
        result = await db.execute(stmt)
        await db.commit()
        
        # Check if any rows were affected
        return result.rowcount > 0
        
    except Exception as e:
        await db.rollback()
        logger.error(f"Failed to update last_active_at for candidate {candidate_id}: {str(e)}")
        return False

async def get_active_candidates_count(
    self,
    db: AsyncSession,
    days: int = 30
) -> int:
    """
    Get count of candidates active within specified days
    
    Args:
        db: Database session
        days: Number of days to look back (default 30)
        
    Returns:
        Count of active candidates
    """
    try:
        from datetime import datetime, timezone, timedelta
        
        active_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        # Count candidates with recent activity or creation
        active_query = select(func.count(Candidate.id)).where(
            and_(
                Candidate.is_deleted == False,
                or_(
                    # Recently active
                    Candidate.last_active_at >= active_date,
                    # Recently created (fallback for new candidates)
                    and_(
                        Candidate.last_active_at.is_(None),
                        Candidate.created_at >= active_date
                    )
                )
            )
        )
        
        result = await db.execute(active_query)
        return result.scalar() or 0
        
    except Exception as e:
        logger.error(f"Failed to get active candidates count: {str(e)}")
        return 0
```

**Update candidate_stats.py**:
```python
# Replace lines 108-109 with:
# Use improved active candidates logic
try:
    logger.debug(f"Starting active candidates query for user {current_user.id}")
    active_count = await candidate_crud.get_active_candidates_count(self.db, days=30)
    logger.debug(f"Active candidates (30 days) result: {active_count}")
except Exception as e:
    logger.error(f"Active candidates query failed: {str(e)}")
    active_count = 0
```

**Activity Tracking Integration** - Add to relevant endpoints:
```python
# In candidate view/update endpoints, add:
# Update last active timestamp when candidate is accessed
await candidate_crud.update_last_active(db, candidate_id=candidate.id)
```

### API Changes
**Endpoints**: No new endpoints required
**Request/Response**: No changes to existing API contracts
**Validation Rules**: Existing validation remains unchanged

### Configuration Changes
**Settings**: No configuration changes needed - leverages existing MinIO settings
**Environment Variables**: Uses existing `MINIO_*` environment variables
**Feature Flags**: None required

## Implementation Sequence

### Phase 1: MinIO Integration
**Specific tasks with file references**:
1. Add import statements to `recruitment_service.py`
2. Implement `_upload_export_to_storage` method
3. Replace TODO comment with actual upload logic
4. Test file upload functionality

### Phase 2: Debug Statements Cleanup  
**Specific tasks with file references**:
1. Add logging import to `candidate_stats.py` if not present
2. Replace all 5 print statements with appropriate logger calls
3. Verify logging configuration picks up debug messages
4. Test logging output in development environment

### Phase 3: Last Active Logic Implementation
**Specific tasks with file references**:
1. Add new methods to `candidate.py` CRUD class
2. Update `candidate_stats.py` to use new active count method
3. Integrate activity tracking in candidate endpoints
4. Test activity timestamp updates

Each phase should be independently deployable and testable.

## Validation Plan

### Unit Tests
**Specific test scenarios to implement**:

**MinIO Integration Tests**:
```python
async def test_upload_export_to_storage():
    """Test export file upload to MinIO"""
    # Test successful upload
    # Test upload failure handling
    # Test presigned URL generation
    
async def test_upload_different_file_types():
    """Test upload of PDF, Excel, CSV files"""
    # Verify content types are handled correctly
```

**Logging Tests**:
```python
def test_debug_statements_converted():
    """Verify no print statements remain"""
    # Scan code for print statements
    # Verify logger.debug calls exist
    
def test_logging_levels():
    """Test different logging levels are used appropriately"""
    # Debug for informational messages
    # Error for exceptions
```

**Last Active Tests**:
```python
async def test_update_last_active():
    """Test last_active_at timestamp updates"""
    # Test successful update
    # Test with non-existent candidate
    
async def test_active_candidates_count():
    """Test active candidates counting logic"""
    # Test with various activity scenarios
    # Test date range filtering
```

### Integration Tests
**End-to-end workflow tests**:
1. Export generation → MinIO upload → URL access verification
2. Candidate viewing → Activity tracking → Statistics update
3. Error scenarios → Proper logging → Error handling

### Business Logic Verification
**How to verify the solution solves the original problem**:
1. **MinIO Integration**: Verify exported files are accessible via generated URLs
2. **Debug Cleanup**: Confirm no print statements in logs, proper debug logging visible
3. **Activity Tracking**: Verify candidate statistics show accurate active user counts

## Error Handling Strategy

### MinIO Upload Errors
```python
# Handle MinIO service unavailable
# Handle file size limits
# Handle invalid file types
# Provide meaningful error codes for frontend
```

### Logging Errors  
```python
# Graceful degradation if logging fails
# Maintain functionality even with logging issues
# Structured error messages for debugging
```

### Database Update Errors
```python
# Transaction rollback on timestamp update failure
# Fallback logic for activity counting
# Connection resilience handling
```

## Dependencies and Integration Points

### Existing Service Dependencies
- **StorageService**: Already implemented, provides MinIO integration
- **Logging Framework**: Python logging module configured in application
- **Database Layer**: SQLAlchemy 2.0 async with PostgreSQL

### Integration Points
- **Recruitment Service → Storage Service**: Direct method calls
- **Statistics Service → CRUD Layer**: Enhanced with new methods  
- **Activity Tracking → Candidate Endpoints**: Middleware-like integration

### Backward Compatibility
- All changes maintain existing API contracts
- No breaking changes to database schema
- Logging changes are purely internal improvements

This specification provides complete implementation details for direct code generation while maintaining consistency with the existing TalentForge Pro architecture and patterns.