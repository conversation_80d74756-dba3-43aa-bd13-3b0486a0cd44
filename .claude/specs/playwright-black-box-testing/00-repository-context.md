# Repository Context Report - TalentForge Pro

*Generated: 2025-09-01*

## Project Overview

**TalentForge Pro** is an enterprise-grade **intelligent talent assessment and position matching system** with a hybrid AI architecture (rule engine + LLM). The system provides comprehensive candidate evaluation through five-dimensional assessment scoring and intelligent job matching capabilities.

## Project Type & Architecture

- **Type**: Full-stack web application with microservices architecture
- **Domain**: HR Technology, Talent Assessment, AI-powered Recruitment
- **Architecture Pattern**: Modern Docker-containerized, API-first architecture
- **Deployment**: Production-ready with monitoring and scaling capabilities

## Technology Stack Analysis

### Backend Stack
- **Framework**: FastAPI (0.110+) with async/await patterns
- **Language**: Python 3.12
- **ORM**: SQLAlchemy 2.0 with async support
- **Database**: PostgreSQL 17 + pgvector (0.8.0) for vector operations
- **Cache**: Redis 7.4.4
- **Task Queue**: Celery with Redis broker
- **Authentication**: JWT + RBAC with bcrypt password hashing

### Frontend Stack  
- **Framework**: Next.js 15.4.1 (App Router)
- **Language**: TypeScript 5.x (strict mode)
- **UI Library**: React 19 with shadcn/ui components
- **State Management**: TanStack Query + React Context
- **Styling**: Tailwind CSS with design system
- **Package Manager**: pnpm 10.13.1

### Infrastructure & DevOps
- **Containerization**: Docker Compose with multi-environment support
- **Reverse Proxy**: Nginx with service routing
- **Object Storage**: MinIO for file management
- **ML Services**: Ollama (v0.11.4) for local LLM embedding
- **Monitoring**: Prometheus + comprehensive logging

### AI/ML Components
- **Embedding Models**: BGE-M3 (1024d), OpenAI (1536d backup)
- **ML Models**: BERT+BiLSTM matching, MLP evaluation
- **Vector Database**: pgvector integrated with PostgreSQL
- **LLM Integration**: Ollama for local processing

## Project Structure & Organization

### Root Directory Structure
```
talent_forge_pro/
├── app/                    # Main application directory (MANDATORY location)
│   ├── backend/           # FastAPI backend service
│   ├── frontend/          # Next.js frontend application  
│   ├── configs/           # Configuration files (nginx, redis)
│   ├── docker-compose.yml # Docker orchestration
│   └── scripts/           # Development and automation scripts
├── docs/                  # Project documentation
│   ├── auto/             # Auto-generated technical docs
│   └── reports/          # Testing and implementation reports
├── archive/              # Archived files and legacy code
├── .claude/              # Claude Code configuration
├── CLAUDE.md             # Primary project documentation (45KB!)
├── Makefile              # Build automation and commands
└── README.md             # Context engineering template info
```

### Backend Architecture
```
app/backend/
├── app/                  # Core application code
│   ├── api/v1/          # RESTful API endpoints
│   ├── core/            # Configuration, security, database
│   ├── crud/            # Data access layer
│   ├── models/          # SQLAlchemy models
│   ├── schemas/         # Pydantic schemas
│   ├── services/        # Business logic layer
│   └── utils/           # Utility functions
├── alembic/             # Database migrations
├── tests/               # Test suite
└── pyproject.toml       # Poetry configuration
```

### Frontend Architecture
```
app/frontend/
├── app/                 # Next.js App Router pages
│   ├── (dashboard)/    # Dashboard routes group
│   └── [locale]/       # Internationalized routes
├── components/          # Reusable UI components
├── services/           # API client services
├── hooks/              # Custom React hooks
├── types/              # TypeScript definitions
├── messages/           # i18n translation files
└── package.json        # npm configuration
```

## Key Features & Capabilities

### Core Business Features
- **Five-Dimensional Assessment**: Digital literacy (20%), industry skills (25%), job skills (30%), innovation (15%), learning potential (10%)
- **Intelligent Matching**: AI-powered candidate-position matching with scoring
- **Multi-Language Support**: React Context + JSON translation files (next-intl)
- **Data Permissions**: PRIVATE, SHARED, TEAM, PUBLIC access levels
- **Assessment Analytics**: DCI scores, JFS scores, trend analysis

### Technical Features
- **Real-time Dashboard**: Comprehensive recruitment statistics and insights
- **Vector Search**: pgvector-enabled semantic search capabilities
- **File Processing**: Resume parsing with OCR (PaddleOCR)
- **API-First Design**: RESTful endpoints with OpenAPI/Swagger documentation
- **Development Token**: Bypass authentication for development (`dev_bypass_token_2025_talentforge`)

## Development Patterns & Conventions

### Backend Patterns
- **Layered Architecture**: API → Service → CRUD → Model
- **Async Programming**: Extensive use of async/await patterns
- **Dependency Injection**: FastAPI dependency system
- **Error Handling**: Structured error codes with i18n support
- **Schema Validation**: Pydantic models for request/response validation

### Frontend Patterns  
- **Component-Based**: Functional components with hooks
- **Type Safety**: Strict TypeScript with comprehensive type definitions
- **State Management**: TanStack Query for server state, Context for global state
- **Performance**: React.memo optimization, code splitting
- **Accessibility**: WCAG 2.1 AA compliance target

### Code Quality Standards
- **Backend**: ruff linting, black formatting, mypy type checking
- **Frontend**: ESLint + Prettier, TypeScript strict mode
- **Testing**: pytest (backend), Jest + Testing Library (frontend)
- **Coverage Targets**: >80% unit tests, 100% integration for critical flows

## Testing Architecture

### Backend Testing
- **Framework**: pytest + pytest-asyncio
- **Database**: SQLite in-memory for test isolation
- **Patterns**: Fixtures in conftest.py, async session management
- **Types**: Unit tests, integration tests, API tests

### Frontend Testing
- **Framework**: Jest + Testing Library + Playwright
- **Unit Tests**: Component testing with mocking
- **Integration Tests**: API integration flows
- **E2E Tests**: Playwright for user journey testing
- **Accessibility**: jest-axe for a11y validation

### Quality Gates
- **Pre-commit**: File organization, dependency validation
- **CI/CD**: Linting, type checking, test execution
- **Migration Validation**: Alembic migration testing
- **API Contract**: Tavern-based API testing

## Deployment & Infrastructure

### Docker Services
- **Application**: backend, frontend, celery worker
- **Data**: postgres, redis, minio
- **Monitoring**: prometheus, nginx
- **AI**: ollama service for LLM operations

### Environment Management
- **Development**: Docker Compose with hot reload
- **Production**: Optimized builds with monitoring
- **Testing**: Isolated test environment
- **Data Persistence**: Named volumes for data retention

### Build & Automation
- **Makefile**: Unified command interface (17KB of commands!)
- **Package Management**: Poetry (backend), pnpm (frontend)
- **Dependency Isolation**: Docker anonymous volumes
- **Hot Reload**: Development mode with instant updates

## Critical Development Guidelines

### Mandatory Patterns (from CLAUDE.md)
- **Root Directory Purity**: Only approved files in project root
- **Migration Management**: Immediate migration generation after model changes
- **Package Refresh**: Container dependency refresh after package changes
- **Error Code System**: Structured error codes for i18n support
- **ID Type Consistency**: Snowflake IDs (BigInteger) throughout system

### Development Workflow
- **Backend First**: Swagger documentation drives frontend development
- **Type Synchronization**: Backend Schema → Frontend TypeScript types
- **API Standards**: RESTful design, trailing slashes, JWT authentication
- **Database Changes**: Alembic migrations required for all schema changes

## Integration Points & Extensions

### External Services
- **LLM Integration**: Ollama for local AI processing
- **File Storage**: MinIO object storage with bucket management
- **Vector Operations**: pgvector for semantic search
- **Monitoring**: Prometheus metrics collection

### API Design
- **Authentication**: JWT tokens with user preferences
- **Pagination**: Standardized `{items, total, skip, limit}` format
- **Error Responses**: Consistent error_code field for i18n
- **Rate Limiting**: slowapi integration for API protection

### Future Considerations
- **Scalability**: Kubernetes deployment ready
- **Performance**: Vector indexing optimization
- **Security**: OAuth2 integration potential  
- **Analytics**: Advanced ML model integration

## Development Environment

### Quick Start Commands
```bash
make setup    # Initial environment setup
make up       # Start all services
make down     # Stop all services
make logs     # View service logs
make status   # Check service health
```

### Access Points
- **Frontend**: http://localhost:8088
- **API Docs**: http://localhost:8088/api/docs
- **Admin Login**: <EMAIL> / test123
- **Dev Token**: dev_bypass_token_2025_talentforge

### Key Files for New Developers
- **CLAUDE.md**: Comprehensive project documentation (45KB)
- **DOCS_INDEXES.md**: Documentation navigation
- **SCRIPT_INDEXES.md**: Available automation scripts
- **Makefile**: All available development commands

This repository represents a mature, production-ready talent assessment platform with extensive documentation, established patterns, and robust testing infrastructure suitable for enterprise deployment and continued development.