# PaddleOCR集成方案 - 简历OCR服务

## 📊 资源对比：为什么选择PaddleOCR？

### VLM vs PaddleOCR 资源消耗对比

| 方案 | 显存/内存需求 | CPU占用 | 处理速度 | OCR准确率 |
|------|-------------|---------|----------|----------|
| **dots.ocr (F16)** | 7-8GB | 高 | 5-10秒/页 | 95%+ |
| **LLaVA-7B** | 4-6GB | 高 | 3-5秒/页 | 85% |
| **Qwen-VL** | 4-6GB | 高 | 3-5秒/页 | 88% |
| **PaddleOCR** | **<1GB** | **低** | **<1秒/页** | **93%+** |

### 为什么PaddleOCR更适合OCR任务？

1. **专注性**：PaddleOCR专为OCR设计，而VLM是通用视觉理解模型
2. **轻量级**：模型仅几十MB，而非几GB
3. **速度快**：毫秒级响应 vs 秒级响应
4. **资源友好**：CPU即可运行，无需GPU

## 🚀 PaddleOCR技术规格

### 核心优势
- **超轻量**：检测模型3.5MB，识别模型4.3MB
- **多语言**：支持80+语言，中英文识别率>93%
- **结构化输出**：支持表格、版式分析、关键信息提取
- **PDF支持**：PP-StructureV3原生支持PDF转Markdown/JSON

### 性能指标
```yaml
CPU模式：
  - 单张图片: 200-500ms
  - PDF页面: 300-800ms
  - 内存占用: <500MB

GPU模式（可选）：
  - 单张图片: 50-100ms
  - PDF页面: 100-200ms
  - 显存占用: <1GB
```

## 📦 Docker集成方案

### 方案一：官方Docker镜像（推荐）

```yaml
# docker-compose.yml 添加
paddleocr:
  image: paddlepaddle/paddle:2.6.2-cpu
  container_name: talent_paddleocr
  restart: unless-stopped
  volumes:
    - ./ocr_service:/app
    - ./data/ocr:/data
  ports:
    - "8868:8868"
  environment:
    - PYTHONPATH=/app
    - LANG=C.UTF-8
  command: python /app/ocr_server.py
  networks:
    - hephaestus_network
  healthcheck:
    test: ["CMD", "curl", "-f", "http://localhost:8868/health"]
    interval: 30s
    timeout: 10s
    retries: 3
```

### 方案二：自定义API服务

```python
# ocr_service/ocr_server.py
from fastapi import FastAPI, File, UploadFile, HTTPException
from paddleocr import PaddleOCR
import numpy as np
from PIL import Image
import io
import json
import pdf2image
import base64

app = FastAPI(title="PaddleOCR Service")

# 初始化PaddleOCR
ocr = PaddleOCR(
    use_angle_cls=True,  # 使用方向分类器
    lang='ch',           # 中英文混合
    use_gpu=False,       # CPU模式
    show_log=False,
    det_model_dir='./models/det',
    rec_model_dir='./models/rec',
    cls_model_dir='./models/cls'
)

@app.post("/api/ocr/image")
async def ocr_image(file: UploadFile = File(...)):
    """处理图片OCR"""
    try:
        # 读取图片
        contents = await file.read()
        image = Image.open(io.BytesIO(contents))
        img_array = np.array(image)
        
        # OCR识别
        result = ocr.ocr(img_array, cls=True)
        
        # 结构化输出
        text_blocks = []
        for line in result[0]:
            text_blocks.append({
                "bbox": line[0],
                "text": line[1][0],
                "confidence": float(line[1][1])
            })
        
        return {
            "status": "success",
            "data": {
                "text_blocks": text_blocks,
                "full_text": "\n".join([b["text"] for b in text_blocks])
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/ocr/pdf")
async def ocr_pdf(file: UploadFile = File(...)):
    """处理PDF OCR"""
    try:
        # PDF转图片
        contents = await file.read()
        images = pdf2image.convert_from_bytes(contents)
        
        all_results = []
        for i, image in enumerate(images):
            img_array = np.array(image)
            result = ocr.ocr(img_array, cls=True)
            
            page_text = []
            for line in result[0]:
                page_text.append({
                    "bbox": line[0],
                    "text": line[1][0],
                    "confidence": float(line[1][1])
                })
            
            all_results.append({
                "page": i + 1,
                "text_blocks": page_text,
                "full_text": "\n".join([b["text"] for b in page_text])
            })
        
        return {
            "status": "success",
            "data": {
                "pages": all_results,
                "total_pages": len(all_results)
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/ocr/resume")
async def ocr_resume(file: UploadFile = File(...)):
    """专门处理简历的OCR，包含结构化解析"""
    try:
        # 判断文件类型
        is_pdf = file.filename.lower().endswith('.pdf')
        
        if is_pdf:
            contents = await file.read()
            images = pdf2image.convert_from_bytes(contents)
            full_text = ""
            
            for image in images:
                img_array = np.array(image)
                result = ocr.ocr(img_array, cls=True)
                for line in result[0]:
                    full_text += line[1][0] + "\n"
        else:
            contents = await file.read()
            image = Image.open(io.BytesIO(contents))
            img_array = np.array(image)
            result = ocr.ocr(img_array, cls=True)
            full_text = "\n".join([line[1][0] for line in result[0]])
        
        # 使用规则或LLM进行结构化解析
        structured_data = parse_resume_text(full_text)
        
        return {
            "status": "success",
            "data": {
                "raw_text": full_text,
                "structured": structured_data
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

def parse_resume_text(text: str) -> dict:
    """解析简历文本为结构化数据"""
    # 这里可以集成您的LLM服务进行智能解析
    # 或使用规则引擎
    
    sections = {
        "personal_info": extract_personal_info(text),
        "education": extract_education(text),
        "experience": extract_experience(text),
        "skills": extract_skills(text)
    }
    return sections

@app.get("/health")
async def health():
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8868)
```

## 🔧 与TalentForge集成

### 1. 后端服务集成

```python
# app/backend/app/services/ocr_service.py
import httpx
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class OCRService:
    def __init__(self):
        self.ocr_base_url = "http://paddleocr:8868"
        self.timeout = httpx.Timeout(30.0)
    
    async def extract_resume(
        self,
        file_path: str,
        file_type: str = "auto"
    ) -> Dict[str, Any]:
        """提取简历内容"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                with open(file_path, 'rb') as f:
                    files = {'file': (file_path, f, 'application/octet-stream')}
                    
                    response = await client.post(
                        f"{self.ocr_base_url}/api/ocr/resume",
                        files=files
                    )
                    response.raise_for_status()
                    
                result = response.json()
                
                # 如果需要，使用LLM进一步处理
                if result.get("data", {}).get("raw_text"):
                    enhanced = await self.enhance_with_llm(
                        result["data"]["raw_text"]
                    )
                    result["data"]["enhanced"] = enhanced
                
                return result
                
        except Exception as e:
            logger.error(f"OCR extraction failed: {e}")
            raise
    
    async def enhance_with_llm(self, raw_text: str) -> Dict[str, Any]:
        """使用现有的LLM服务增强OCR结果"""
        from app.services.ai_service_manager import AIServiceManager
        
        ai_manager = AIServiceManager()
        
        prompt = f"""
        请将以下简历文本结构化为JSON格式：
        
        {raw_text}
        
        输出格式：
        {{
            "name": "",
            "email": "",
            "phone": "",
            "education": [],
            "experience": [],
            "skills": []
        }}
        """
        
        response = await ai_manager.generate_llm_response(
            messages=[{"role": "user", "content": prompt}],
            provider="ollama",  # 使用您现有的Ollama
            model="qwen2.5:14b"
        )
        
        import json
        try:
            return json.loads(response)
        except:
            return {"raw_response": response}
```

### 2. API端点

```python
# app/backend/app/api/v1/endpoints/ocr.py
from fastapi import APIRouter, UploadFile, File, HTTPException
from app.services.ocr_service import OCRService
from app.services.minio_service import MinIOService

router = APIRouter()
ocr_service = OCRService()
minio_service = MinIOService()

@router.post("/extract")
async def extract_resume(
    file: UploadFile = File(...),
    candidate_id: Optional[str] = None
):
    """提取简历文本"""
    try:
        # 保存到MinIO
        file_path = await minio_service.upload_file(
            file=file,
            bucket="resumes",
            object_name=f"{candidate_id}/{file.filename}"
        )
        
        # OCR提取
        result = await ocr_service.extract_resume(file_path)
        
        # 保存结果到数据库
        if candidate_id:
            # 更新候选人信息
            pass
        
        return {
            "status": "success",
            "data": result["data"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

## 🔄 队列处理系统 (Celery + Redis)

### 队列架构设计

```yaml
# docker-compose.yml 更新
services:
  # OCR Worker 容器
  ocr-worker:
    image: paddlepaddle/paddle:2.6.2-cpu
    container_name: talent_ocr_worker
    volumes:
      - ./ocr_service:/app
      - ./data/ocr:/data
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/2
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - PYTHONPATH=/app
    command: celery -A ocr_worker.app worker --loglevel=info
    depends_on:
      - redis
      - paddleocr
    networks:
      - hephaestus_network
    
  # OCR API 服务
  paddleocr:
    image: paddlepaddle/paddle:2.6.2-cpu
    container_name: talent_paddleocr
    restart: unless-stopped
    volumes:
      - ./ocr_service:/app
      - ./data/ocr:/data
    ports:
      - "8868:8868"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/2
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
      - PYTHONPATH=/app
    command: python /app/ocr_server.py
    depends_on:
      - redis
    networks:
      - hephaestus_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8868/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 配置更新 (如果需要)
  redis:
    image: redis:7.2-alpine
    container_name: hephaestus_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    networks:
      - hephaestus_network
```

### Celery Worker 实现

```python
# ocr_service/ocr_worker.py
from celery import Celery, Task
from paddleocr import PaddleOCR
import pdf2image
import numpy as np
from PIL import Image
import io
import json
import base64
import logging
import traceback
import os

# Celery 配置
broker_url = os.getenv("CELERY_BROKER_URL", "redis://localhost:6379/2")
result_backend = os.getenv("CELERY_RESULT_BACKEND", "redis://localhost:6379/2")

app = Celery('ocr_worker', broker=broker_url, backend=result_backend)

app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    task_track_started=True,
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    result_expires=3600,  # 1小时后过期
    
    # 任务路由
    task_routes={
        'ocr_worker.process_resume': {'queue': 'ocr'},
        'ocr_worker.process_batch': {'queue': 'ocr_batch'},
    },
)

# 初始化PaddleOCR
ocr = None

class OCRTask(Task):
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        logging.error(f"Task {task_id} failed: {exc}")
        self.update_state(
            task_id=task_id,
            state='FAILURE',
            meta={'error': str(exc), 'traceback': str(einfo)}
        )

def get_ocr():
    """懒加载OCR实例"""
    global ocr
    if ocr is None:
        ocr = PaddleOCR(
            use_angle_cls=True,
            lang='ch',
            use_gpu=False,
            show_log=False
        )
    return ocr

@app.task(bind=True, name='ocr_worker.process_resume', base=OCRTask)
def process_resume(self, file_data: bytes, filename: str, user_id: str = None):
    """
    处理单个简历文件的OCR任务
    
    Args:
        file_data: 文件二进制数据(base64编码)
        filename: 文件名
        user_id: 用户ID(可选)
    
    Returns:
        dict: OCR结果
    """
    try:
        # 更新任务状态为进行中
        self.update_state(
            state='PROGRESS',
            meta={'current': 10, 'total': 100, 'status': '开始处理文件...'}
        )
        
        # 解码文件数据
        file_content = base64.b64decode(file_data)
        
        # 判断文件类型
        is_pdf = filename.lower().endswith('.pdf')
        
        # 获取OCR实例
        ocr_engine = get_ocr()
        
        self.update_state(
            state='PROGRESS',
            meta={'current': 20, 'total': 100, 'status': '初始化OCR引擎完成'}
        )
        
        if is_pdf:
            # 处理PDF文件
            self.update_state(
                state='PROGRESS',
                meta={'current': 30, 'total': 100, 'status': 'PDF转图片中...'}
            )
            
            images = pdf2image.convert_from_bytes(file_content)
            all_results = []
            full_text = ""
            
            total_pages = len(images)
            for i, image in enumerate(images):
                # 更新进度
                progress = 30 + (50 * (i + 1) / total_pages)
                self.update_state(
                    state='PROGRESS',
                    meta={
                        'current': int(progress),
                        'total': 100,
                        'status': f'处理第{i+1}/{total_pages}页...'
                    }
                )
                
                img_array = np.array(image)
                result = ocr_engine.ocr(img_array, cls=True)
                
                page_text = []
                if result and result[0]:
                    for line in result[0]:
                        if line and len(line) >= 2:
                            page_text.append({
                                "bbox": line[0],
                                "text": line[1][0],
                                "confidence": float(line[1][1])
                            })
                            full_text += line[1][0] + "\n"
                
                all_results.append({
                    "page": i + 1,
                    "text_blocks": page_text,
                    "full_text": "\n".join([b["text"] for b in page_text])
                })
            
            result_data = {
                "pages": all_results,
                "total_pages": len(all_results),
                "full_text": full_text
            }
            
        else:
            # 处理图片文件
            self.update_state(
                state='PROGRESS',
                meta={'current': 30, 'total': 100, 'status': '处理图片中...'}
            )
            
            image = Image.open(io.BytesIO(file_content))
            img_array = np.array(image)
            
            self.update_state(
                state='PROGRESS',
                meta={'current': 60, 'total': 100, 'status': 'OCR识别中...'}
            )
            
            result = ocr_engine.ocr(img_array, cls=True)
            
            text_blocks = []
            full_text = ""
            
            if result and result[0]:
                for line in result[0]:
                    if line and len(line) >= 2:
                        text_blocks.append({
                            "bbox": line[0],
                            "text": line[1][0],
                            "confidence": float(line[1][1])
                        })
                        full_text += line[1][0] + "\n"
            
            result_data = {
                "text_blocks": text_blocks,
                "full_text": full_text
            }
        
        # 最终结果
        self.update_state(
            state='PROGRESS',
            meta={'current': 90, 'total': 100, 'status': '处理完成，保存结果...'}
        )
        
        final_result = {
            "status": "success",
            "filename": filename,
            "file_type": "pdf" if is_pdf else "image",
            "user_id": user_id,
            "data": result_data,
            "metadata": {
                "processing_time": self.request.time_start,
                "file_size": len(file_content),
                "worker_id": self.request.hostname
            }
        }
        
        return final_result
        
    except Exception as e:
        logging.error(f"OCR processing failed: {str(e)}")
        logging.error(traceback.format_exc())
        
        self.update_state(
            state='FAILURE',
            meta={
                'error': str(e),
                'traceback': traceback.format_exc(),
                'status': '处理失败'
            }
        )
        raise

@app.task(bind=True, name='ocr_worker.process_batch', base=OCRTask)
def process_batch(self, file_list: list, user_id: str = None):
    """
    批量处理多个文件
    
    Args:
        file_list: 文件列表 [{'filename': 'xx', 'file_data': 'base64...'}]
        user_id: 用户ID
    
    Returns:
        list: 批量处理结果
    """
    try:
        total_files = len(file_list)
        results = []
        
        for i, file_info in enumerate(file_list):
            # 更新整体进度
            progress = int((i / total_files) * 100)
            self.update_state(
                state='PROGRESS',
                meta={
                    'current': progress,
                    'total': 100,
                    'status': f'批量处理中: {i+1}/{total_files}',
                    'completed_files': i,
                    'total_files': total_files
                }
            )
            
            # 处理单个文件
            try:
                result = process_resume.apply(
                    args=[file_info['file_data'], file_info['filename'], user_id]
                ).get()
                
                results.append({
                    'filename': file_info['filename'],
                    'status': 'success',
                    'result': result
                })
            except Exception as e:
                results.append({
                    'filename': file_info['filename'],
                    'status': 'error',
                    'error': str(e)
                })
        
        return {
            'status': 'completed',
            'total_files': total_files,
            'successful': len([r for r in results if r['status'] == 'success']),
            'failed': len([r for r in results if r['status'] == 'error']),
            'results': results
        }
        
    except Exception as e:
        logging.error(f"Batch processing failed: {str(e)}")
        self.update_state(
            state='FAILURE',
            meta={'error': str(e), 'status': '批量处理失败'}
        )
        raise

if __name__ == '__main__':
    app.start()
```

### OCR API 服务更新

```python
# ocr_service/ocr_server.py
from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Optional, List
import base64
import json
from celery.result import AsyncResult
from ocr_worker import app as celery_app, process_resume, process_batch
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="PaddleOCR Queue Service", version="1.0.0")

class OCRTaskResponse(BaseModel):
    task_id: str
    status: str
    message: str

class TaskStatus(BaseModel):
    task_id: str
    status: str
    progress: Optional[dict] = None
    result: Optional[dict] = None
    error: Optional[str] = None

@app.post("/api/ocr/submit", response_model=OCRTaskResponse)
async def submit_ocr_task(
    file: UploadFile = File(...),
    user_id: Optional[str] = None
):
    """
    提交OCR任务到队列
    """
    try:
        # 读取文件内容并编码
        file_content = await file.read()
        file_data = base64.b64encode(file_content).decode('utf-8')
        
        # 提交到Celery队列
        task = process_resume.delay(file_data, file.filename, user_id)
        
        logger.info(f"OCR task submitted: {task.id} for file: {file.filename}")
        
        return OCRTaskResponse(
            task_id=task.id,
            status="submitted",
            message=f"OCR任务已提交，任务ID: {task.id}"
        )
        
    except Exception as e:
        logger.error(f"Failed to submit OCR task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/ocr/submit-batch", response_model=OCRTaskResponse)
async def submit_batch_ocr_task(
    files: List[UploadFile] = File(...),
    user_id: Optional[str] = None
):
    """
    提交批量OCR任务到队列
    """
    try:
        file_list = []
        
        for file in files:
            file_content = await file.read()
            file_data = base64.b64encode(file_content).decode('utf-8')
            file_list.append({
                'filename': file.filename,
                'file_data': file_data
            })
        
        # 提交批量任务
        task = process_batch.delay(file_list, user_id)
        
        logger.info(f"Batch OCR task submitted: {task.id} for {len(files)} files")
        
        return OCRTaskResponse(
            task_id=task.id,
            status="submitted",
            message=f"批量OCR任务已提交，任务ID: {task.id}，文件数量: {len(files)}"
        )
        
    except Exception as e:
        logger.error(f"Failed to submit batch OCR task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/ocr/status/{task_id}", response_model=TaskStatus)
async def get_task_status(task_id: str):
    """
    获取任务状态
    """
    try:
        result = AsyncResult(task_id, app=celery_app)
        
        response_data = {
            "task_id": task_id,
            "status": result.status,
            "progress": None,
            "result": None,
            "error": None
        }
        
        if result.status == 'PENDING':
            response_data.update({
                "progress": {"current": 0, "total": 100, "status": "任务排队中..."}
            })
        elif result.status == 'PROGRESS':
            response_data.update({
                "progress": result.info
            })
        elif result.status == 'SUCCESS':
            response_data.update({
                "result": result.result,
                "progress": {"current": 100, "total": 100, "status": "任务完成"}
            })
        elif result.status == 'FAILURE':
            response_data.update({
                "error": str(result.info),
                "progress": {"current": 0, "total": 100, "status": "任务失败"}
            })
        
        return TaskStatus(**response_data)
        
    except Exception as e:
        logger.error(f"Failed to get task status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/ocr/result/{task_id}")
async def get_task_result(task_id: str):
    """
    获取任务结果（任务完成后）
    """
    try:
        result = AsyncResult(task_id, app=celery_app)
        
        if result.status == 'SUCCESS':
            return {
                "task_id": task_id,
                "status": "completed",
                "result": result.result
            }
        elif result.status == 'FAILURE':
            raise HTTPException(
                status_code=500, 
                detail=f"任务执行失败: {str(result.info)}"
            )
        else:
            return {
                "task_id": task_id,
                "status": result.status,
                "message": "任务尚未完成，请稍后再试"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get task result: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.delete("/api/ocr/cancel/{task_id}")
async def cancel_task(task_id: str):
    """
    取消任务
    """
    try:
        celery_app.control.revoke(task_id, terminate=True)
        
        return {
            "task_id": task_id,
            "status": "cancelled",
            "message": "任务已取消"
        }
        
    except Exception as e:
        logger.error(f"Failed to cancel task: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/ocr/queue-stats")
async def get_queue_stats():
    """
    获取队列统计信息
    """
    try:
        inspect = celery_app.control.inspect()
        
        # 获取活跃任务
        active_tasks = inspect.active()
        # 获取等待任务
        reserved_tasks = inspect.reserved()
        # 获取已注册任务
        registered_tasks = inspect.registered()
        
        stats = {
            "active_workers": len(active_tasks) if active_tasks else 0,
            "active_tasks": sum(len(tasks) for tasks in active_tasks.values()) if active_tasks else 0,
            "pending_tasks": sum(len(tasks) for tasks in reserved_tasks.values()) if reserved_tasks else 0,
            "registered_tasks": list(registered_tasks.values())[0] if registered_tasks else [],
            "worker_details": active_tasks
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Failed to get queue stats: {str(e)}")
        return {
            "active_workers": 0,
            "active_tasks": 0,
            "pending_tasks": 0,
            "error": str(e)
        }

@app.get("/health")
async def health_check():
    """健康检查"""
    try:
        # 检查Celery连接
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        return {
            "status": "healthy",
            "service": "PaddleOCR Queue Service",
            "celery_workers": len(stats) if stats else 0,
            "timestamp": "2025-08-28T10:00:00Z"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": "2025-08-28T10:00:00Z"
        }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8868)
```

### 后端服务集成更新

```python
# app/backend/app/services/ocr_service.py
import httpx
import asyncio
from typing import Optional, Dict, Any, List
import logging
import base64
from celery.result import AsyncResult

logger = logging.getLogger(__name__)

class OCRService:
    def __init__(self):
        self.ocr_base_url = "http://paddleocr:8868"
        self.timeout = httpx.Timeout(60.0)  # 增加超时时间
    
    async def submit_ocr_task(
        self, 
        file_content: bytes, 
        filename: str,
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        提交OCR任务到队列
        
        Returns:
            dict: 包含task_id的响应
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                # 构建multipart形式数据
                files = {
                    'file': (filename, file_content, 'application/octet-stream')
                }
                data = {}
                if user_id:
                    data['user_id'] = user_id
                
                response = await client.post(
                    f"{self.ocr_base_url}/api/ocr/submit",
                    files=files,
                    data=data
                )
                response.raise_for_status()
                
                return response.json()
                
        except Exception as e:
            logger.error(f"Failed to submit OCR task: {e}")
            raise
    
    async def submit_batch_ocr_task(
        self, 
        files_data: List[Dict[str, Any]],
        user_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        提交批量OCR任务
        
        Args:
            files_data: [{'filename': str, 'content': bytes}, ...]
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                # 构建多文件数据
                files = []
                for file_info in files_data:
                    files.append(
                        ('files', (
                            file_info['filename'], 
                            file_info['content'], 
                            'application/octet-stream'
                        ))
                    )
                
                data = {}
                if user_id:
                    data['user_id'] = user_id
                
                response = await client.post(
                    f"{self.ocr_base_url}/api/ocr/submit-batch",
                    files=files,
                    data=data
                )
                response.raise_for_status()
                
                return response.json()
                
        except Exception as e:
            logger.error(f"Failed to submit batch OCR task: {e}")
            raise
    
    async def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.ocr_base_url}/api/ocr/status/{task_id}"
                )
                response.raise_for_status()
                
                return response.json()
                
        except Exception as e:
            logger.error(f"Failed to get task status: {e}")
            raise
    
    async def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """获取任务结果"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.ocr_base_url}/api/ocr/result/{task_id}"
                )
                response.raise_for_status()
                
                return response.json()
                
        except Exception as e:
            logger.error(f"Failed to get task result: {e}")
            raise
    
    async def cancel_task(self, task_id: str) -> Dict[str, Any]:
        """取消任务"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.delete(
                    f"{self.ocr_base_url}/api/ocr/cancel/{task_id}"
                )
                response.raise_for_status()
                
                return response.json()
                
        except Exception as e:
            logger.error(f"Failed to cancel task: {e}")
            raise
    
    async def wait_for_completion(
        self, 
        task_id: str, 
        max_wait_time: int = 300,
        poll_interval: int = 2
    ) -> Dict[str, Any]:
        """
        等待任务完成（轮询方式）
        
        Args:
            task_id: 任务ID
            max_wait_time: 最大等待时间（秒）
            poll_interval: 轮询间隔（秒）
        """
        start_time = asyncio.get_event_loop().time()
        
        while True:
            status_response = await self.get_task_status(task_id)
            status = status_response.get('status')
            
            if status == 'SUCCESS':
                # 获取结果
                result_response = await self.get_task_result(task_id)
                return result_response
            elif status == 'FAILURE':
                error_msg = status_response.get('error', 'Unknown error')
                raise Exception(f"OCR task failed: {error_msg}")
            
            # 检查超时
            elapsed = asyncio.get_event_loop().time() - start_time
            if elapsed > max_wait_time:
                raise TimeoutError(f"OCR task timeout after {max_wait_time} seconds")
            
            # 等待下次轮询
            await asyncio.sleep(poll_interval)
    
    async def get_queue_stats(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(
                    f"{self.ocr_base_url}/api/ocr/queue-stats"
                )
                response.raise_for_status()
                
                return response.json()
                
        except Exception as e:
            logger.error(f"Failed to get queue stats: {e}")
            return {"error": str(e)}
    
    # 兼容性方法：同步式接口
    async def extract_resume_sync(
        self,
        file_content: bytes,
        filename: str,
        user_id: Optional[str] = None,
        max_wait_time: int = 180
    ) -> Dict[str, Any]:
        """
        同步方式提取简历（内部使用异步队列）
        适用于需要立即返回结果的场景
        """
        # 提交任务
        task_response = await self.submit_ocr_task(file_content, filename, user_id)
        task_id = task_response['task_id']
        
        # 等待完成
        result = await self.wait_for_completion(task_id, max_wait_time)
        
        return result

# 单例实例
ocr_service = OCRService()
```

现在让我完成前端轮询机制的实现：

<function_calls>
<invoke name="TodoWrite">
<parameter name="todos">[{"content": "\u8bbe\u8ba1OCR\u961f\u5217\u5904\u7406\u67b6\u6784", "status": "completed", "activeForm": "\u5df2\u5b8c\u6210\u961f\u5217\u67b6\u6784\u8bbe\u8ba1"}, {"content": "\u5b9e\u73b0\u5f02\u6b65\u4efb\u52a1\u5904\u7406\u65b9\u6848", "status": "completed", "activeForm": "\u5df2\u5b9e\u73b0\u5f02\u6b65\u4efb\u52a1\u5904\u7406"}, {"content": "\u5b8c\u6210\u72b6\u6001\u7ba1\u7406\u548c\u76d1\u63a7\u7cfb\u7edf", "status": "completed", "activeForm": "\u5df2\u5b8c\u6210\u72b6\u6001\u7ba1\u7406\u7cfb\u7edf"}, {"content": "\u5b9e\u73b0\u524d\u7aef\u8f6e\u8be2\u673a\u5236", "status": "in_progress", "activeForm": "\u6b63\u5728\u5b9e\u73b0\u524d\u7aef\u8f6e\u8be2\u673a\u5236"}, {"content": "\u6dfb\u52a0\u961f\u5217\u76d1\u63a7\u4eea\u8868\u677f", "status": "pending", "activeForm": "\u6dfb\u52a0\u961f\u5217\u76d1\u63a7\u4eea\u8868\u677f"}]