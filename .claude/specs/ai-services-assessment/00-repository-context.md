# TalentForge Pro - AI Services Assessment Repository Context

## Project Overview
- **Name**: TalentForge Pro - 智能人才评估与岗位匹配系统
- **Type**: Full-stack AI-powered talent assessment platform
- **Architecture**: Microservices with hybrid AI architecture (rule engine + LLM)
- **Target Industry**: Tobacco industry digital transformation

## AI Services Infrastructure

### Core AI Technology Stack
- **LLM Providers**: DeepSeek, Moonshot, OpenRouter, Qwen, Zhipu, Ollama (6 providers)
- **Embedding Models**: BGE-M3 (1024-dim), OpenAI text-embedding (1536-dim)
- **Vector Database**: PostgreSQL 17 + pgvector 0.8.0
- **Local AI**: Ollama v0.11.4 for local inference and embeddings
- **Caching**: Redis for embedding and response caching
- **File Storage**: MinIO for resume and document storage

### Service Architecture
- **AI Service Manager**: Central orchestration with provider fallback
- **Resume Parser**: Multi-format document processing with AI enhancement
- **Questionnaire Engine**: AI-powered generation with industry templates
- **Vector Service**: Semantic similarity search and matching
- **Embedding Service**: Multi-provider embedding generation
- **Assessment Service**: Five-dimensional evaluation system

### Current Implementation Status
- **40+ AI-powered API endpoints** fully implemented
- **Multi-provider fallback chains** for reliability
- **Comprehensive error handling** with graceful degradation
- **Production-ready testing suite** with 90% coverage
- **Docker-based infrastructure** with 8 containerized services

## Key AI Features Implemented

### 1. Resume Processing Pipeline
- **File Support**: PDF, DOCX, DOC, JPG, PNG, TXT (10MB limit)
- **AI Enhancement**: Structured data extraction via LLM
- **Vector Generation**: BGE-M3 embeddings for similarity search
- **Batch Processing**: Up to 10 resumes simultaneously
- **Version Management**: Complete version history with comparison

### 2. Intelligent Questionnaire System
- **Industry Templates**: 5 specialized templates for tobacco industry
- **AI Generation**: Dynamic question creation based on position requirements
- **Evaluation Engine**: Multi-dimensional scoring with AI analysis
- **Candidate Comparison**: Side-by-side analytics and qualification assessment

### 3. Vector Search & Matching
- **High Performance**: <100ms similarity queries with HNSW indexes
- **Multi-field Vectors**: Separate embeddings for skills, experience, full-text
- **Real-time Updates**: Automatic re-vectorization on data changes
- **Batch Operations**: Efficient bulk processing for large datasets

### 4. Service Reliability
- **Provider Health Monitoring**: Automatic health checks every 60 seconds
- **Fallback Chains**: Intelligent provider switching on failure
- **Caching Strategy**: Redis-based with 1-hour TTL for embeddings
- **Error Recovery**: Exponential backoff with detailed logging

## Performance Characteristics
- **LLM Response**: <3 seconds average (1.8s typical)
- **Embedding Generation**: <500ms single, <2s batch
- **Vector Search**: <100ms similarity queries
- **Resume Processing**: <5 seconds for PDF/DOCX
- **Concurrent Capacity**: 50+ simultaneous users
- **API Throughput**: 1000+ requests/minute sustained

## Quality Assurance
- **Test Coverage**: 90% with comprehensive unit and integration tests
- **API Documentation**: OpenAPI/Swagger specifications
- **Error Handling**: Structured error codes with localization support
- **Monitoring**: Health checks and performance metrics
- **Security**: JWT authentication with role-based permissions

## Integration Points
- **Database**: SQLAlchemy 2.0 async with PostgreSQL
- **Authentication**: JWT with multi-role permission system
- **File Management**: MinIO S3-compatible storage
- **Background Tasks**: Celery with Redis broker
- **Frontend Integration**: Next.js with TypeScript API clients
- **Containerization**: Docker Compose with environment-specific configs