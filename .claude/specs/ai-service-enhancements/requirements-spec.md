# AI Service Manager Enhancements - Technical Specification

## Problem Statement

**Business Issue**: Current AI Service Manager lacks essential service methods for text generation, embedding generation, and document reranking capabilities, limiting its utility in production applications.

**Current State**: The existing AIServiceManager provides client management and health checking but missing core service methods that application code needs to interact with AI providers effectively.

**Expected Outcome**: Enhanced AIServiceManager with production-ready service methods that provide provider selection, retry logic, fallback handling, caching, and comprehensive error management.

## Solution Overview

**Approach**: Extend the existing AIServiceManager singleton with three core service methods while maintaining backward compatibility and existing patterns.

**Core Changes**: 
- Add `generate_text()` method with provider selection and fallback chain
- Add `generate_embedding()` method with Redis caching integration
- Add `rerank_documents()` method with native API and embedding similarity fallback
- Add provider property methods for runtime switching
- Enhance error handling with exponential backoff retry logic

**Success Criteria**: All methods achieve <200ms cached response times, provide graceful fallback handling, and maintain thread safety for concurrent requests.

## Technical Implementation

### Database Changes
**Tables to Modify**: None required - uses existing Redis cache infrastructure

**Cache Schema Extensions**:
```python
# Redis cache keys for new functionality
EMBEDDING_CACHE_KEY = "ai:embedding:{provider}:{model}:{text_hash}"
LLM_CACHE_KEY = "ai:llm:{provider}:{model}:{prompt_hash}:{params_hash}"
RERANK_CACHE_KEY = "ai:rerank:{provider}:{query_hash}:{docs_hash}"
```

### Code Changes

**Files to Modify**: 
- `/app/backend/app/services/ai_service_manager.py` (core implementation)
- `/app/backend/app/services/ai_example_usage.py` (usage examples)

**New Dependencies**: 
- `numpy` for cosine similarity calculations in rerank fallback
- Enhanced logging for service method monitoring

**Function Signatures**:

```python
# Core service methods to add
async def generate_text(
    self,
    messages: Union[str, List[Dict[str, str]]],
    provider: Optional[str] = None,
    model: Optional[str] = None,
    temperature: Optional[float] = None,
    max_tokens: Optional[int] = None,
    use_cache: bool = True,
    max_retries: int = 3
) -> str

async def generate_embedding(
    self,
    text: str,
    provider: Optional[str] = None,
    model: Optional[str] = None,
    use_cache: bool = True,
    max_retries: int = 3
) -> List[float]

async def rerank_documents(
    self,
    query: str,
    documents: List[str],
    provider: Optional[str] = None,
    top_k: Optional[int] = None,
    use_cache: bool = True,
    max_retries: int = 3
) -> List[Dict[str, Any]]

# Provider property methods
@property
def llm_provider(self) -> str

@property
def embedding_provider(self) -> str

@property
def rerank_provider(self) -> str

# Health monitoring enhancements
async def check_service_method_health(self) -> Dict[str, Any]
```

### Retry and Fallback Logic

**Exponential Backoff Implementation**:
```python
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

@retry(
    stop=stop_after_attempt(3),
    wait=wait_exponential(multiplier=1, min=1, max=10),
    retry=retry_if_exception_type((httpx.RequestError, asyncio.TimeoutError))
)
async def _execute_with_retry(self, operation_func, *args, **kwargs):
    """Execute operation with exponential backoff retry"""
    return await operation_func(*args, **kwargs)
```

**Provider Fallback Chain**:
```python
async def _execute_with_fallback(
    self,
    service_type: AIServiceType,
    operation_func,
    *args,
    provider: Optional[str] = None,
    **kwargs
) -> Any:
    """Execute operation with provider fallback chain"""
    attempted_providers = []
    
    # Try primary provider first
    if provider:
        primary_provider = provider
    else:
        primary_provider = self._resolve_provider(service_type)
    
    providers_to_try = [primary_provider]
    
    # Add fallback chain
    if service_type == AIServiceType.LLM:
        fallback_chain = ai_settings.get_llm_fallback_chain()
    elif service_type == AIServiceType.EMBEDDING:
        fallback_chain = ai_settings.get_embedding_fallback_chain()
    else:
        fallback_chain = []
    
    providers_to_try.extend([p for p in fallback_chain if p != primary_provider])
    
    for provider_name in providers_to_try:
        try:
            attempted_providers.append(provider_name)
            result = await self._execute_provider_operation(
                service_type, provider_name, operation_func, *args, **kwargs
            )
            logger.info(f"Successfully executed {operation_func.__name__} with provider: {provider_name}")
            return result
            
        except Exception as e:
            logger.warning(f"Provider {provider_name} failed for {operation_func.__name__}: {e}")
            if provider_name == providers_to_try[-1]:  # Last provider
                raise Exception(f"All providers failed. Attempted: {attempted_providers}. Last error: {e}")
            continue
```

### Caching Implementation

**Cache Key Generation**:
```python
def _generate_cache_key(self, operation: str, **params) -> str:
    """Generate consistent cache keys for operations"""
    # Create deterministic hash from parameters
    param_str = json.dumps(params, sort_keys=True, ensure_ascii=True)
    param_hash = hashlib.sha256(param_str.encode()).hexdigest()[:16]
    return f"ai:{operation}:{param_hash}"

async def _get_cached_result(self, cache_key: str) -> Optional[Any]:
    """Get cached result with deserialization"""
    try:
        redis = await get_redis()
        cached_data = await redis.get(cache_key)
        if cached_data:
            return json.loads(cached_data)
    except Exception as e:
        logger.warning(f"Cache retrieval failed: {e}")
    return None

async def _set_cached_result(self, cache_key: str, result: Any, ttl: int = None) -> None:
    """Set cached result with serialization"""
    try:
        redis = await get_redis()
        if ttl is None:
            ttl = ai_settings.EMBEDDING_CACHE_TTL
        await redis.setex(cache_key, ttl, json.dumps(result))
    except Exception as e:
        logger.warning(f"Cache storage failed: {e}")
```

### Rerank Implementation Details

**Native Rerank API (SiliconFlow via OpenRouter)**:
```python
async def _native_rerank(
    self,
    query: str,
    documents: List[str],
    client,
    config: Dict[str, Any],
    top_k: Optional[int] = None
) -> List[Dict[str, Any]]:
    """Execute native rerank API call"""
    try:
        # SiliconFlow rerank through OpenRouter
        response = await client.rerank.create(
            model=config.get("rerank_model", "BAAI/bge-reranker-v2-m3"),
            query=query,
            documents=documents,
            top_k=top_k or len(documents)
        )
        
        return [
            {
                "document": documents[result.index],
                "score": float(result.relevance_score),
                "index": result.index
            }
            for result in response.results
        ]
    except Exception as e:
        logger.error(f"Native rerank API failed: {e}")
        raise
```

**Embedding Similarity Fallback**:
```python
async def _embedding_similarity_rerank(
    self,
    query: str,
    documents: List[str],
    provider: str,
    top_k: Optional[int] = None
) -> List[Dict[str, Any]]:
    """Fallback rerank using embedding similarity"""
    import numpy as np
    
    # Generate embeddings
    query_embedding = await self.generate_embedding(
        text=query,
        provider=provider,
        use_cache=True
    )
    
    doc_embeddings = []
    for doc in documents:
        doc_emb = await self.generate_embedding(
            text=doc,
            provider=provider,
            use_cache=True
        )
        doc_embeddings.append(doc_emb)
    
    # Calculate cosine similarity
    query_vec = np.array(query_embedding)
    similarities = []
    
    for i, doc_emb in enumerate(doc_embeddings):
        doc_vec = np.array(doc_emb)
        similarity = np.dot(query_vec, doc_vec) / (
            np.linalg.norm(query_vec) * np.linalg.norm(doc_vec)
        )
        similarities.append((i, float(similarity)))
    
    # Sort by similarity score (descending)
    similarities.sort(key=lambda x: x[1], reverse=True)
    
    # Apply top_k limit
    if top_k:
        similarities = similarities[:top_k]
    
    # Format results
    return [
        {
            "document": documents[idx],
            "score": score,
            "index": idx
        }
        for idx, score in similarities
    ]
```

### Error Handling Strategy

**Exception Hierarchy**:
```python
class AIServiceError(Exception):
    """Base exception for AI service operations"""
    pass

class ProviderError(AIServiceError):
    """Provider-specific error"""
    def __init__(self, provider: str, message: str, original_error: Exception = None):
        self.provider = provider
        self.original_error = original_error
        super().__init__(f"Provider {provider}: {message}")

class AllProvidersFailedError(AIServiceError):
    """All providers in fallback chain failed"""
    def __init__(self, attempted_providers: List[str], last_error: Exception):
        self.attempted_providers = attempted_providers
        self.last_error = last_error
        super().__init__(f"All providers failed: {attempted_providers}")
```

**Comprehensive Error Logging**:
```python
async def _log_operation_metrics(
    self,
    operation: str,
    provider: str,
    success: bool,
    duration_ms: float,
    error: Optional[Exception] = None
) -> None:
    """Log operation metrics for monitoring"""
    log_data = {
        "operation": operation,
        "provider": provider,
        "success": success,
        "duration_ms": duration_ms,
        "timestamp": datetime.now(timezone.utc).isoformat()
    }
    
    if error:
        log_data["error_type"] = type(error).__name__
        log_data["error_message"] = str(error)
    
    if success:
        logger.info(f"AI operation succeeded: {log_data}")
    else:
        logger.error(f"AI operation failed: {log_data}")
```

## Implementation Sequence

### Phase 1: Core Service Methods (Priority 1)
**Tasks**:
1. Add `generate_text()` method with basic retry logic
2. Add `generate_embedding()` method with caching integration
3. Add provider property methods (`llm_provider`, `embedding_provider`, `rerank_provider`)
4. Update existing example usage with new methods
5. Add comprehensive error handling and logging

**File Changes**:
- Extend `AIServiceManager` class in `/app/backend/app/services/ai_service_manager.py`
- Update examples in `/app/backend/app/services/ai_example_usage.py`

### Phase 2: Rerank Implementation (Priority 2)
**Tasks**:
1. Add `rerank_documents()` method with native API support
2. Implement embedding similarity fallback logic
3. Add score normalization (0-1 range)
4. Add SiliconFlow integration via OpenRouter configuration
5. Add rerank-specific caching logic

**Dependencies**: Requires numpy for similarity calculations

### Phase 3: Enhanced Monitoring (Priority 3)
**Tasks**:
1. Add `check_service_method_health()` for comprehensive health monitoring
2. Implement auto-failover logic based on health status
3. Add performance metrics collection
4. Add Redis cache health monitoring
5. Add provider status tracking with TTL

**Monitoring Endpoints**: Health checks validate all service methods work correctly

## Validation Plan

### Unit Tests
**Test Scenarios**:
```python
# test_ai_service_manager.py
async def test_generate_text_with_fallback():
    """Test text generation with provider fallback"""
    # Mock primary provider failure, verify fallback works

async def test_generate_embedding_caching():
    """Test embedding generation with Redis caching"""
    # Verify cache hit/miss behavior and TTL

async def test_rerank_documents_native_and_fallback():
    """Test rerank with both native API and embedding fallback"""
    # Test both execution paths

async def test_provider_properties():
    """Test provider property getters and setters"""
    # Test thread-safe runtime provider switching

async def test_error_handling_and_retry():
    """Test comprehensive error handling"""
    # Test exponential backoff and exception propagation
```

### Integration Tests
**End-to-end Workflow Tests**:
```python
async def test_full_ai_pipeline():
    """Test complete AI workflow: text→embedding→rerank"""
    # Verify all service methods work together

async def test_provider_failover_scenario():
    """Test automatic failover when primary provider fails"""
    # Simulate provider outage and verify system resilience

async def test_cache_performance():
    """Test caching performance under load"""
    # Verify <200ms cached response requirement
```

### Business Logic Verification
**Success Criteria Validation**:
- Generate embeddings and verify vector dimensions match provider specs
- Execute text generation and validate response format and content quality  
- Perform document reranking and verify score ordering correctness
- Measure cached response times to confirm <200ms requirement
- Test provider switching without service interruption
- Validate error recovery and fallback chain execution

**Performance Benchmarks**:
- Cached embedding generation: <200ms (target: <100ms)
- Text generation with retry: <5s total
- Document reranking (10 docs): <2s total
- Provider health check: <1s for all providers
- Memory usage: <50MB additional for all enhancements

**Thread Safety Verification**:
- Concurrent requests to different providers
- Provider switching during active operations
- Cache access under high concurrency
- Singleton pattern integrity under load