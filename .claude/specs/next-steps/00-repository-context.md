# Repository Context Analysis - TalentForge Pro

## Project Overview

**TalentForge Pro** is an intelligent talent assessment and job matching system built with a hybrid intelligence architecture (rule engine + LLM). The system provides comprehensive candidate evaluation across five dimensions with advanced vector-based matching capabilities.

### Project Classification
- **Type**: Full-stack web application with AI/ML integration
- **Domain**: Human Resources Technology (HR-Tech) / Talent Management
- **Architecture**: Microservices-oriented containerized application
- **Scale**: Enterprise-grade with multi-tenant capabilities

## Technology Stack Discovery

### Backend Stack
- **Framework**: FastAPI 0.110+ (Python 3.12)
- **Database**: PostgreSQL 17 + pgvector 0.8.0 (vector database)
- **Cache**: Redis 7.4.4
- **ORM**: SQLAlchemy 2.0 with Alembic migrations
- **Authentication**: JWT + bcrypt password hashing
- **File Storage**: MinIO object storage
- **Task Queue**: Celery with Redis broker
- **API Documentation**: OpenAPI/Swagger auto-generation

### Frontend Stack
- **Framework**: Next.js 15.4.1 (React 19, TypeScript 5.x)
- **UI Library**: Radix UI + shadcn/ui components
- **Styling**: Tailwind CSS
- **State Management**: Redux Toolkit + TanStack Query
- **Internationalization**: next-intl (English/Chinese support)
- **Testing**: Jest + Playwright E2E
- **Icons**: Lucide React

### AI/ML Services
- **Local Embedding**: Ollama v0.11.4 (BGE-M3 model, 1024-dim)
- **External LLM**: OpenAI API compatible endpoints
- **Vector Search**: pgvector with HNSW indexing
- **Models**: BERT+BiLSTM (matching), MLP (evaluation)

### Infrastructure
- **Containerization**: Docker + Docker Compose
- **Reverse Proxy**: Nginx (unified access port 8088)
- **Monitoring**: Prometheus (optional)
- **Package Management**: Poetry (backend), pnpm (frontend)
- **Build Tools**: Make-based automation

## Code Organization Patterns

### Directory Structure (Enforced Architecture Rules)
```
talent_forge_pro/                    # ROOT: Clean architecture enforced
├── CLAUDE.md                        # Project documentation & rules
├── Makefile                         # Unified command orchestration
├── app/                             # Application directory (MANDATORY)
│   ├── backend/                     # FastAPI application
│   │   ├── app/                     # Python application code
│   │   │   ├── api/v1/             # REST API endpoints
│   │   │   ├── core/               # Configuration & utilities  
│   │   │   ├── crud/               # Database operations
│   │   │   ├── models/             # SQLAlchemy models
│   │   │   ├── schemas/            # Pydantic schemas
│   │   │   └── services/           # Business logic
│   │   ├── tests/                  # Backend test suite
│   │   └── pyproject.toml          # Poetry configuration
│   ├── frontend/                   # Next.js application
│   │   ├── app/                    # App Router structure
│   │   ├── components/             # React components
│   │   ├── services/               # API client services
│   │   ├── store/                  # Redux state management
│   │   ├── types/                  # TypeScript definitions
│   │   └── package.json            # pnpm configuration
│   ├── scripts/                    # Automation scripts
│   │   ├── backend/                # Backend utilities
│   │   ├── frontend/               # Frontend utilities
│   │   ├── test/                   # Testing scripts
│   │   └── validation/             # Quality checks
│   └── docker-compose.yml          # Container orchestration
├── docs/                           # Documentation (centralized)
├── examples/                       # Code examples & patterns
└── archive/                        # Historical artifacts
```

### Architecture Enforcement Rules
**CRITICAL**: The project enforces strict directory organization:

1. **Root Directory Purity** (MANDATORY):
   - Only 9 allowed files/directories in root
   - All Docker configs MUST be in `app/` directory
   - No test files, reports, or temporary files in root
   - AI agents FORBIDDEN from creating root-level files

2. **Application Sub-directory Rules**:
   - Backend: Only `alembic/`, `app/`, `tests/`, `migrations/`
   - Frontend: Standard Next.js structure only
   - No `docs/`, `scripts/`, `backup/` in app subdirectories

### Code Structure Patterns

#### Backend Patterns
- **Layered Architecture**: API → Service → CRUD → Model
- **Dependency Injection**: FastAPI's built-in DI system
- **Async/Await**: All database operations are async
- **Exception Handling**: Centralized with custom exceptions
- **ID System**: Snowflake IDs (64-bit integers) - NOT UUIDs

```python
# Typical service pattern
class CandidateService:
    async def create_candidate(self, data: CandidateCreate) -> Candidate:
        # Business logic here
        return await candidate_crud.create(data)

# CRUD instance access (CORRECT pattern)
candidates = await candidate_crud.get_multi_with_permission()
# NOT: candidate_crud.candidate.get_multi() (this fails)
```

#### Frontend Patterns
- **Component Architecture**: Functional components + hooks
- **State Management**: Redux Toolkit slices + TanStack Query
- **API Layer**: Centralized axios client with interceptors
- **Type Safety**: Strict TypeScript with backend schema sync
- **Internationalization**: Hook-based translation system

```typescript
// API client pattern (response interceptor handles .data)
const response = await apiClient.get<UserListResponse>('/users/');
return response; // NOT response.data (already extracted)

// Translation pattern
const t = useTranslations();
return <div>{t('auth.loginTitle')}</div>;
```

## Integration Points & Dependencies

### Critical Integration Patterns

1. **Frontend-Backend Type Sync**:
   - Backend Pydantic schemas are source of truth
   - Frontend TypeScript types MUST match exactly
   - Manual synchronization required (no auto-generation)

2. **API Communication**:
   - All API endpoints use trailing slashes (FastAPI requirement)
   - JWT tokens stored in both localStorage + cookies (SSR/CSR compatibility)
   - Development bypass token: `dev_bypass_token_2025_talentforge`

3. **Database Integration**:
   - pgvector for embeddings (1024-dim BGE-M3, 1536-dim OpenAI backup)
   - Alembic migrations with auto-merge conflict resolution
   - Snowflake ID system throughout

4. **Container Dependencies**:
   - All Docker operations in `app/` directory
   - Service discovery via container names
   - Data persistence in `~/dev_data/hephaestus/`

## Development Workflow & Conventions

### Build & Development Process
```bash
# Standard workflow
make setup          # Initial setup
make up             # Start all services  
make down           # Stop services
make logs           # View logs
make status         # Service health check

# Package management (Docker isolated)
make frontend-package-refresh    # After package.json changes
make backend-package-refresh     # After pyproject.toml changes
```

### Code Quality Standards
- **Testing**: 80%+ coverage requirement (Jest/pytest)
- **Type Safety**: Strict TypeScript, Pydantic validation
- **API Standards**: RESTful, unified response format, error codes
- **Security**: JWT+RBAC, input validation, HTTPS in production

### Internationalization Requirements
- **Backend**: Error codes only (no hardcoded messages)
- **Frontend**: All text via `t()` function, semantic keys
- **Languages**: English (primary), Chinese (secondary)
- **Format**: `pages.modules.content` key structure

## Current Sprint Status

Based on file analysis and documentation:

- **Sprint 1**: ✅ Infrastructure setup (complete)
- **Sprint 2**: ✅ Authentication & user management (complete)  
- **Sprint 3**: 🔄 Candidate management (in progress)
- **Sprint 4**: 📅 ML service integration (planned)
- **Sprint 5**: 📅 Performance optimization & deployment (planned)

## Constraints & Considerations

### Technical Constraints
1. **Docker Network**: All services communicate via container names
2. **Port Strategy**: Single entry point (8088) via nginx proxy
3. **File Upload**: MinIO object storage, not filesystem
4. **Vector Operations**: pgvector only, no external vector DBs
5. **LLM Integration**: OpenAI-compatible API endpoints only

### Development Constraints  
1. **Root Directory**: Strictly enforced file placement rules
2. **Package Sync**: Manual refresh required after dependency changes
3. **Schema Sync**: Manual frontend type updates after backend changes
4. **Testing**: All new features require unit + integration tests

### Performance Considerations
- **API Response**: <200ms target
- **Frontend Load**: <3s first paint
- **ML Inference**: <200ms for matching
- **Database**: Connection pooling + Redis caching

## Quality Assurance Framework

The project implements an 8-step validation cycle:
1. Syntax validation
2. Type checking  
3. Linting & formatting
4. Security scanning
5. Test execution (unit/integration)
6. Performance benchmarking
7. Documentation verification
8. Integration testing

### Automated Checks
- Pre-commit hooks for file organization
- CI/CD validation (when configured)
- Make-based quality gates
- Docker health checks

## Next Steps Integration Points

Based on this analysis, new development should:

1. **Follow Architecture**: Respect the strict directory organization
2. **Maintain Type Sync**: Update both backend schemas and frontend types
3. **Use Existing Patterns**: Follow established CRUD/service/component patterns  
4. **Package Management**: Use make commands for dependency updates
5. **Testing**: Include comprehensive test coverage
6. **Documentation**: Update relevant docs in `docs/` directory

The codebase is well-structured for requirements-driven development with clear patterns, comprehensive tooling, and established quality gates. The hybrid intelligence architecture provides a solid foundation for AI-enhanced talent management features.