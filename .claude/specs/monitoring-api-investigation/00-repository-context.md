# Repository Context Analysis for TalentForge Pro

## Project Overview

**TalentForge Pro** is an intelligent talent assessment and position matching system built with modern full-stack technologies. It uses a hybrid intelligence architecture combining rule engines with LLM capabilities.

### Core Purpose
- **Domain**: Human Resources & Talent Management
- **Primary Function**: AI-driven candidate evaluation, position matching, and recruitment workflow automation
- **Target Users**: HR professionals, recruiters, hiring managers, and administrative users

## Technology Stack Summary

### Backend (FastAPI + PostgreSQL)
- **Runtime**: Python 3.12 with FastAPI 0.110+
- **Database**: PostgreSQL 17 with pgvector 0.8.0 (vector database capabilities)
- **ORM**: SQLAlchemy 2.0 with Alembic migrations
- **Caching**: Redis 7.4.4
- **Task Queue**: Celery 5.4.0 with Redis broker
- **Object Storage**: MinIO for file storage
- **AI/ML**: Ollama 0.11.4, OpenAI API, PaddleOCR for document processing
- **Validation**: Pydantic 2.7+
- **Authentication**: JWT with python-jose, bcrypt password hashing

### Frontend (Next.js + TypeScript)
- **Framework**: Next.js 15.4.1 with React 19
- **Language**: TypeScript 5.5 strict mode
- **UI Components**: Radix UI + Tailwind CSS 3.4.1 + shadcn/ui
- **State Management**: Redux Toolkit 2.2.5 + TanStack Query 5.50
- **Package Manager**: pnpm 10.13.1
- **Testing**: Jest + Playwright for E2E
- **Internationalization**: next-intl 4.3.4

### Infrastructure & DevOps
- **Containerization**: Docker with multi-service docker-compose
- **Reverse Proxy**: Nginx for unified API gateway
- **Monitoring**: Prometheus 3.5.0 (optional)
- **Build Tools**: Poetry (backend), pnpm (frontend)
- **Development**: Makefile-based workflow automation

## Project Structure & Organization

### Root Directory Architecture
```
talent_forge_pro/
├── app/                    # Main application container
│   ├── backend/           # FastAPI backend service
│   ├── frontend/          # Next.js frontend application
│   ├── configs/           # Configuration files (nginx, prometheus)
│   ├── docker-compose.yml # Docker orchestration
│   └── scripts/           # Development and automation scripts
├── docs/                  # Documentation and reports
├── archive/               # Historical files and cleanup records
├── examples/              # Code examples and patterns
└── [root config files]    # Makefile, README, etc.
```

**Key Architectural Principle**: Root directory is kept clean with strict file organization rules to prevent architectural chaos.

### Backend Architecture (App Layer)
```
app/backend/
├── app/
│   ├── api/v1/           # API endpoints with versioning
│   │   ├── admin/        # Admin-specific endpoints (monitoring, roles)
│   │   └── endpoints/    # Feature-specific endpoints
│   ├── core/             # Core utilities (config, database, exceptions)
│   ├── crud/             # Database operations layer
│   ├── models/           # SQLAlchemy ORM models
│   ├── schemas/          # Pydantic request/response schemas
│   ├── services/         # Business logic layer
│   └── utils/            # Utility functions
├── tests/                # Comprehensive test suite
├── alembic/              # Database migrations
└── scripts/              # Backend-specific scripts
```

### Frontend Architecture (App Layer)
```
app/frontend/
├── app/                  # Next.js app router pages
├── components/           # Reusable UI components
├── services/             # API client services
├── hooks/                # Custom React hooks
├── types/                # TypeScript type definitions
├── lib/                  # Utility libraries
├── messages/             # i18n translation files
├── store/                # Redux store configuration
└── __tests__/            # Frontend test suites
```

## Code Patterns & Conventions

### API Design Patterns
- **RESTful Architecture**: Consistent HTTP methods and status codes
- **Unified Response Format**: Standardized error responses with error codes
- **Layered Architecture**: API → Service → CRUD → Model pattern
- **Permission-Based Access**: Role-based access control (RBAC) with granular permissions
- **Error Code System**: Structured error codes format: `[MODULE]_[CATEGORY]_[ERROR]`

### Backend Development Patterns
```python
# Standardized CRUD pattern
from app.crud.base import CRUDBase
from app.models.example import Example
from app.schemas.example import ExampleCreate, ExampleUpdate

class CRUDExample(CRUDBase[Example, ExampleCreate, ExampleUpdate]):
    async def get_with_permission(self, db: AsyncSession, user: User) -> List[Example]:
        # Custom business logic with permissions
        pass

example_crud = CRUDExample(Example)  # Instance creation
```

```python
# Service layer pattern
from app.core.exceptions import NotFoundError, service_error

class ExampleService:
    async def process_example(self, db: AsyncSession, data: ExampleCreate) -> Example:
        try:
            # Business logic implementation
            return await example_crud.create(db, obj_in=data)
        except Exception as e:
            raise service_error(f"Failed to process: {str(e)}")
```

### Frontend Development Patterns
```typescript
// API service pattern
class ExampleService {
  async getList(): Promise<ExampleListResponse> {
    const response = await apiClient.get<ExampleListResponse>('/examples/');
    return response; // Note: No .data access - handled by interceptors
  }
}

// React component pattern with i18n
const ExampleComponent: React.FC = () => {
  const t = useTranslations();
  const { data, isLoading } = useQuery(['examples'], () => exampleService.getList());
  
  return (
    <Card>
      <CardTitle>{t('example.title')}</CardTitle>
      {/* Component implementation */}
    </Card>
  );
};
```

### Testing Architecture
- **Backend**: pytest + pytest-asyncio, >80% coverage target
- **Frontend**: Jest + React Testing Library + Playwright for E2E
- **API Testing**: Integration tests using FastAPI TestClient
- **Test Data**: Factory patterns for consistent test data generation

## Development Workflow

### Build & Deployment
```makefile
# Makefile-driven development
make setup    # First-time setup
make up       # Start all services
make down     # Stop all services
make logs     # View logs
make status   # Check service health
```

### Docker Architecture
```yaml
services:
  postgres:    # PostgreSQL 17 + pgvector
  redis:       # Caching and Celery broker  
  minio:       # Object storage
  ollama:      # LLM embedding service
  backend:     # FastAPI application
  frontend:    # Next.js application
  celery:      # Background task processor
  nginx:       # Reverse proxy (unified access)
```

### Package Management
- **Backend**: Poetry with dependency isolation
- **Frontend**: pnpm with package-lock
- **Dependency Refresh**: Docker volume isolation requires specific refresh commands

## Monitoring & Admin System Analysis

### Existing Monitoring Infrastructure

#### Backend Monitoring APIs (`/admin/monitoring/*`)
- **System Health**: `/admin/monitoring/health` - Comprehensive health overview
- **Service Status**: `/admin/monitoring/services` - Individual service health
- **Metrics**: `/admin/monitoring/metrics` - System resource metrics
- **Historical Data**: `/admin/monitoring/history/*` - Time-series monitoring data
- **Cache Management**: `/admin/monitoring/cache/*` - Redis cache control

#### Monitoring Architecture Components
```python
# Core monitoring models
class ServiceHealth(BaseModel):
    name: str
    display_name: str  
    status: ServiceStatus  # HEALTHY, DEGRADED, UNHEALTHY, UNKNOWN
    response_time: Optional[float]
    last_check: str
    details: Optional[Dict]

class SystemMetrics(BaseModel):
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    services_total: int
    services_healthy: int
    # ... additional metrics

class MonitoringSnapshot(BaseModel):
    overall_status: ServiceStatus
    health_score: float  # 0-100
    # Historical tracking capabilities
```

#### Permission System
- **Required Permission**: `monitoring.view` for all monitoring endpoints
- **Super Admin**: Additional access to cleanup and initialization endpoints
- **Role-Based Access**: Granular permission system with role inheritance

#### Caching Strategy
- **Redis-Based**: 5-minute cached health checks to prevent performance impact
- **Cache Control**: Manual refresh and clear capabilities
- **Background Tasks**: Celery-based cache refresh every 5 minutes

#### Frontend Monitoring Components
- **Page**: `/admin/monitoring` - Comprehensive monitoring dashboard
- **Service**: `monitoringService` - Backend API integration
- **Components**: `SystemOverviewCard`, `StatusPage`, `MonitoringSkeleton`
- **Types**: Full TypeScript definitions matching backend schemas

## Integration Points & Constraints

### API Integration
- **Base URL**: All APIs accessed through `/api/v1/*`
- **Authentication**: JWT tokens required for admin endpoints
- **Error Handling**: Structured error codes with i18n support
- **Rate Limiting**: slowapi implementation for DoS protection

### Database Constraints
- **ID System**: Snowflake IDs (64-bit integers) throughout system
- **Migrations**: Alembic-managed with careful dependency checking
- **Vector Support**: pgvector extension for ML/AI capabilities

### Development Constraints
- **File Organization**: Strict rules about root directory cleanliness
- **Dependency Management**: Container-based isolation with refresh requirements
- **Testing Requirements**: Comprehensive test coverage expectations

## Potential Integration Considerations

### For New Monitoring Features
1. **Permission Integration**: Must integrate with existing RBAC system
2. **Caching Strategy**: Should leverage Redis caching patterns
3. **Error Handling**: Follow established error code patterns
4. **Frontend Integration**: Must support i18n and existing UI patterns
5. **Database Schema**: Consider historical data storage patterns

### Performance Considerations
- **Async Patterns**: All database operations should be async
- **Caching**: Heavy operations should use Redis caching
- **Background Tasks**: Long-running operations should use Celery
- **Resource Monitoring**: System monitoring should have minimal performance impact

## Repository Health & Maintenance

### Documentation Quality
- **Comprehensive**: Extensive documentation in `docs/` directory
- **Architecture Guides**: Detailed technical specifications
- **Development Guides**: Step-by-step implementation guides
- **Cleanup History**: Well-documented cleanup and refactoring efforts

### Code Quality Indicators
- **Testing Coverage**: Extensive test suites with multiple testing strategies
- **Error Handling**: Comprehensive exception handling with proper logging
- **Type Safety**: Full TypeScript usage with strict mode
- **Dependency Management**: Up-to-date dependencies with security considerations

### Development Maturity
- **Sprint-Based Development**: Organized development cycles with clear milestones
- **Version Control**: Clean git history with meaningful commit messages
- **Configuration Management**: Environment-specific configurations
- **Automation**: Makefile and script-driven development workflows

This repository demonstrates enterprise-grade development practices with strong architectural foundations, comprehensive testing, and well-organized code structure suitable for complex monitoring system integrations.