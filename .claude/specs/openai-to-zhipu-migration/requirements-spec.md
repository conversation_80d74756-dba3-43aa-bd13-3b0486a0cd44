# OpenAI to Zhipu GLM Migration Technical Specification

## Problem Statement
- **Business Issue**: Replace OpenAI dependency with Zhipu GLM to reduce costs and improve Chinese language performance
- **Current State**: OpenAI integrated as cloud provider with AsyncOpenAI client and fallback chain positioning
- **Expected Outcome**: Complete OpenAI removal with Zhipu GLM as direct replacement maintaining API compatibility

## Solution Overview
- **Approach**: Replace OpenAI provider with Zhipu GLM using existing multi-provider architecture patterns
- **Core Changes**: Provider enum update, client initialization, configuration class, environment variables, dependency updates
- **Success Criteria**: All OpenAI references removed, Zhipu GLM fully functional, fallback chain operational, health checks passing

## Technical Implementation

### Database Changes
- **Tables to Modify**: None required
- **New Tables**: None required
- **Migration Scripts**: None required

### Code Changes

#### 1. AIProvider Enum Update
**File**: `/home/<USER>/source_code/talent_forge_pro/app/backend/app/services/ai_service_manager.py`
**Lines**: 33-41

```python
# REMOVE line 36
OPENAI = "openai"

# ADD after line 40 (before closing of enum)
ZHIPU = "zhipu"
```

#### 2. Client Initialization Replacement
**File**: `/home/<USER>/source_code/talent_forge_pro/app/backend/app/services/ai_service_manager.py`
**Lines**: 77-96

**REMOVE entire OpenAI client initialization block:**
```python
# 初始化OpenAI客户端 (lines 77-95)
if ai_settings.OPENAI_API_KEY:
    # ... entire block
```

**ADD Zhipu GLM client initialization block after line 96:**
```python
# 初始化Zhipu GLM客户端
if ai_settings.ZHIPU_API_KEY:
    try:
        self._clients[AIProvider.ZHIPU] = {
            "client": AsyncOpenAI(
                api_key=ai_settings.ZHIPU_API_KEY,
                base_url=ai_settings.ZHIPU_API_BASE
            ),
            "config": {
                "llm_model": ai_settings.get_llm_model("zhipu"),
                "embedding_model": ai_settings.get_embedding_model("zhipu"),
                "temperature": ai_settings.ZHIPU_LLM_TEMPERATURE,
                "max_tokens": ai_settings.ZHIPU_LLM_MAX_TOKENS,
                "dimension": ai_settings.ZHIPU_EMBEDDING_DIMENSION,
                "rate_limit": ai_settings.ZHIPU_RATE_LIMIT_RPM
            }
        }
        logger.info("Zhipu GLM client initialized")
    except Exception as e:
        logger.error(f"Failed to initialize Zhipu GLM client: {e}")
```

#### 3. Configuration Class Addition
**File**: `/home/<USER>/source_code/talent_forge_pro/app/backend/app/core/ai_config.py`

**REMOVE OpenAI configuration block (lines 73-95):**
```python
# ===========================
# OpenAI Configuration
# ===========================
# ... entire OpenAI section
```

**ADD Zhipu GLM configuration after line 95:**
```python
# ===========================
# Zhipu GLM Configuration
# ===========================
ZHIPU_API_KEY: Optional[str] = None
ZHIPU_API_BASE: str = "https://open.bigmodel.cn/api/paas/v4/"

# Zhipu GLM LLM Models
ZHIPU_LLM_MODEL: str = "glm-4"
ZHIPU_LLM_TEMPERATURE: float = 0.7
ZHIPU_LLM_MAX_TOKENS: int = 4000

# Zhipu GLM Embedding Models
ZHIPU_EMBEDDING_MODEL: str = "embedding-3"
ZHIPU_EMBEDDING_DIMENSION: int = 1024

# Zhipu GLM Performance
ZHIPU_RATE_LIMIT_RPM: int = 60
ZHIPU_MAX_RETRIES: int = 3
ZHIPU_TIMEOUT: int = 60
```

#### 4. Helper Methods Update
**File**: `/home/<USER>/source_code/talent_forge_pro/app/backend/app/core/ai_config.py`

**ADD get_llm_model method before line 198:**
```python
def get_llm_model(self, provider: str) -> Optional[str]:
    """Get LLM model for specific provider"""
    if provider == "ollama":
        return self.OLLAMA_LLM_MODEL
    elif provider == "deepseek":
        return self.DEEPSEEK_LLM_MODEL
    elif provider == "zhipu":
        return self.ZHIPU_LLM_MODEL
    elif provider == "moonshot":
        return self.MOONSHOT_LLM_MODEL
    elif provider == "openrouter":
        return self.OPENROUTER_LLM_MODEL
    elif provider == "qwen":
        return self.QWEN_LLM_MODEL
    return None
```

**UPDATE get_embedding_model method (lines 198-214):**
```python
def get_embedding_model(self, provider: str) -> Optional[str]:
    """Get embedding model for specific provider"""
    if provider == "ollama":
        # Use new OLLAMA_EMBEDDING_MODEL if set, otherwise fallback to OLLAMA_MODEL
        return self.OLLAMA_EMBEDDING_MODEL or self.OLLAMA_MODEL
    elif provider == "deepseek":
        return self.DEEPSEEK_EMBEDDING_MODEL
    elif provider == "zhipu":
        return self.ZHIPU_EMBEDDING_MODEL
    elif provider == "moonshot":
        return self.MOONSHOT_EMBEDDING_MODEL
    elif provider == "openrouter":
        return self.OPENROUTER_EMBEDDING_MODEL
    elif provider == "qwen":
        return self.QWEN_EMBEDDING_MODEL
    return None
```

**UPDATE get_embedding_dimension method (lines 216-234):**
```python
def get_embedding_dimension(self, provider: Optional[str] = None) -> int:
    """Get embedding dimension based on provider"""
    if not provider:
        provider = self.EMBEDDING_PROVIDER
        
    if provider == "deepseek":
        return self.DEEPSEEK_EMBEDDING_DIMENSION
    elif provider == "zhipu":
        return self.ZHIPU_EMBEDDING_DIMENSION
    elif provider == "ollama":
        return self.OLLAMA_EMBEDDING_DIMENSION
    elif provider == "openrouter":
        return self.OPENROUTER_EMBEDDING_DIMENSION
    elif provider == "qwen":
        return self.QWEN_EMBEDDING_DIMENSION
    else:
        # Default dimension
        return self.VECTOR_DIMENSION
```

**UPDATE is_provider_available method (lines 235-256):**
```python
def is_provider_available(self, provider: str, task: str = "embedding") -> bool:
    """Check if a provider is available for specific task"""
    if provider == "ollama":
        # Ollama is available if host is configured
        if task == "embedding":
            return bool(self.OLLAMA_HOST and (self.OLLAMA_EMBEDDING_MODEL or self.OLLAMA_MODEL))
        else:  # LLM
            return bool(self.OLLAMA_HOST and self.OLLAMA_LLM_MODEL)
    
    # Cloud providers need API key
    if provider == "deepseek":
        return bool(self.DEEPSEEK_API_KEY)
    elif provider == "zhipu":
        return bool(self.ZHIPU_API_KEY)
    elif provider == "moonshot":
        return bool(self.MOONSHOT_API_KEY)
    elif provider == "openrouter":
        return bool(self.OPENROUTER_API_KEY)
    elif provider == "qwen":
        return bool(self.QWEN_API_KEY)
    
    return False
```

**REMOVE is_openai_available method (lines 270-272)**

**ADD is_zhipu_available method after line 272:**
```python
def is_zhipu_available(self) -> bool:
    """Check if Zhipu GLM configuration is available"""
    return self.is_provider_available("zhipu")
```

#### 5. Fallback Chain Configuration Updates
**File**: `/home/<USER>/source_code/talent_forge_pro/app/backend/app/core/ai_config.py`

**UPDATE line 22:**
```python
EMBEDDING_FALLBACK_CHAIN: str = "ollama,deepseek,zhipu"
```

### Environment Variable Updates

#### 1. Docker Compose Configuration
**File**: `/home/<USER>/source_code/talent_forge_pro/app/docker-compose.yml`

**REMOVE OpenAI environment variables (lines 213-226 and 450-463):**
```yaml
# OpenAI Configuration (Separated Models)
OPENAI_API_KEY: ${OPENAI_API_KEY}
# ... entire OpenAI sections in both backend and celery_worker
```

**ADD Zhipu GLM environment variables after removed OpenAI sections:**
```yaml
# Zhipu GLM Configuration (Separated Models)
ZHIPU_API_KEY: ${ZHIPU_API_KEY}
ZHIPU_API_BASE: ${ZHIPU_API_BASE:-https://open.bigmodel.cn/api/paas/v4/}
# LLM Models
ZHIPU_LLM_MODEL: ${ZHIPU_LLM_MODEL:-glm-4}
ZHIPU_LLM_TEMPERATURE: ${ZHIPU_LLM_TEMPERATURE:-0.7}
ZHIPU_LLM_MAX_TOKENS: ${ZHIPU_LLM_MAX_TOKENS:-4000}
# Embedding Models
ZHIPU_EMBEDDING_MODEL: ${ZHIPU_EMBEDDING_MODEL:-embedding-3}
ZHIPU_EMBEDDING_DIMENSION: ${ZHIPU_EMBEDDING_DIMENSION:-1024}
# Performance
ZHIPU_RATE_LIMIT_RPM: ${ZHIPU_RATE_LIMIT_RPM:-60}
ZHIPU_MAX_RETRIES: ${ZHIPU_MAX_RETRIES:-3}
ZHIPU_TIMEOUT: ${ZHIPU_TIMEOUT:-60}
```

**UPDATE fallback chain references (lines 179 and 416):**
```yaml
EMBEDDING_FALLBACK_CHAIN: ${EMBEDDING_FALLBACK_CHAIN:-ollama,deepseek,zhipu}
```

#### 2. Environment Example File
**File**: `/home/<USER>/source_code/talent_forge_pro/app/.env.example`

**UPDATE line 85:**
```bash
EMBEDDING_FALLBACK_CHAIN=ollama,deepseek,zhipu
```

**ADD Zhipu GLM configuration section after line 100:**
```bash
# --- Zhipu GLM配置 (智谱AI) ---
ZHIPU_API_KEY=
ZHIPU_API_BASE=https://open.bigmodel.cn/api/paas/v4/
# LLM Models
ZHIPU_LLM_MODEL=glm-4
ZHIPU_LLM_TEMPERATURE=0.7
ZHIPU_LLM_MAX_TOKENS=4000
# Embedding Models
ZHIPU_EMBEDDING_MODEL=embedding-3
ZHIPU_EMBEDDING_DIMENSION=1024
# Performance
ZHIPU_RATE_LIMIT_RPM=60
ZHIPU_MAX_RETRIES=3
ZHIPU_TIMEOUT=60
```

### Dependency Updates

#### 1. Python Requirements
**File**: `/home/<USER>/source_code/talent_forge_pro/app/backend/requirements.txt`

**REMOVE line 28:**
```
openai>=1.35.0
```

**ADD after line 27:**
```
zhipuai>=2.1.5.20241029
```

### API Changes
**Endpoints**: No API endpoint changes required - internal provider switch only
**Request/Response**: Maintains existing API contract
**Validation Rules**: No validation changes needed

### Configuration Changes
**Settings**: Add ZHIPU_* environment variables
**Environment Variables**: Update .env.example and docker-compose.yml as specified above
**Feature Flags**: No feature flags needed

## Implementation Sequence

### Phase 1: Dependency and Configuration Setup
1. **Update requirements.txt** - Remove openai, add zhipuai
2. **Add Zhipu configuration** - Update ai_config.py with Zhipu settings
3. **Update environment files** - Add Zhipu variables to .env.example and docker-compose.yml

### Phase 2: Provider Integration
1. **Update AIProvider enum** - Remove OPENAI, add ZHIPU
2. **Replace client initialization** - Remove OpenAI client, add Zhipu client
3. **Update helper methods** - Add Zhipu support to model getters and availability checks

### Phase 3: Fallback Chain Updates
1. **Update fallback chains** - Replace "openai" with "zhipu" in default chains
2. **Remove OpenAI references** - Clean up all OpenAI-specific code
3. **Validate configuration** - Ensure all config methods support Zhipu

## Validation Plan

### Unit Tests
- Test Zhipu provider initialization with valid API key
- Test Zhipu provider failure handling with invalid API key
- Test get_embedding_model returns correct Zhipu model
- Test get_embedding_dimension returns correct Zhipu dimension
- Test is_zhipu_available returns correct status
- Test fallback chain excludes OpenAI and includes Zhipu

### Integration Tests
- Test Zhipu client creation and configuration loading
- Test embedding generation with Zhipu models
- Test LLM generation with Zhipu models
- Test fallback chain behavior when Zhipu unavailable
- Test health check endpoint includes Zhipu status
- Test provider switching from primary to Zhipu fallback

### Business Logic Verification
- Verify embedding dimensions match expected Zhipu model outputs
- Verify LLM responses maintain quality with Zhipu models
- Verify no OpenAI API calls are made after migration
- Verify system continues to function when Zhipu API is unavailable (fallback)
- Verify cost reduction achieved through Zhipu pricing model

### Health Check Implementation
The existing health check in `ai_service_manager.py` (lines 299-375) will automatically support Zhipu GLM through the provider detection logic. No additional health check implementation required.

### Model Selection Strategy
- **LLM Primary**: GLM-4 (general purpose, high performance)
- **LLM Alternative**: GLM-4.5 (latest version, if available)
- **Embedding Primary**: Embedding-3 (1024 dimensions, optimized for Chinese)
- **Embedding Alternative**: Embedding-2 (fallback option)

### Provider Positioning in Fallback Chains
- **Embedding Chain**: ollama → deepseek → zhipu (replaces openai position)
- **LLM Chain**: No change needed (OpenAI not in default LLM chain)
- **Reasoning**: Position Zhipu as third option after local (Ollama) and primary cloud (DeepSeek)