# TalentForge Pro - Repository Context Analysis

## Project Overview

**TalentForge Pro** is a comprehensive intelligent talent assessment and job matching system built with a hybrid architecture combining rule engines and LLM capabilities. The system provides advanced candidate evaluation, intelligent questionnaires, resume parsing, and position matching services.

**Key Purpose**: Multi-dimensional talent evaluation with DCI/JFS scoring, AI-powered assessment, and automated recruitment workflows.

## Project Type & Architecture

**Type**: Full-stack web application with microservices architecture
**Architecture Pattern**: Hybrid Intelligence (Rules Engine + LLM) with containerized services
**Deployment**: Docker-based development and production environments

### Core Architecture Components

1. **Backend API**: FastAPI with async programming, layered architecture
2. **Frontend SPA**: Next.js 15 + React 19 with TypeScript
3. **AI/ML Services**: Multiple LLM providers + vector embeddings
4. **Data Layer**: PostgreSQL + pgvector, Redis caching
5. **Storage**: MinIO S3-compatible object storage
6. **Task Queue**: Celery with Redis broker
7. **Proxy/Gateway**: Nginx reverse proxy

## Technology Stack Analysis

### Backend Stack
- **Framework**: FastAPI 0.110+ (Python 3.12)
- **Database**: PostgreSQL 17 + pgvector 0.8.0 for vector operations
- **ORM**: SQLAlchemy 2.0 with async support
- **Cache**: Redis 7 for caching and session management
- **Task Queue**: Celery 5.4+ for background processing
- **Auth**: JWT with python-jose, bcrypt password hashing
- **Package Management**: Poetry for dependency management
- **Code Quality**: Black, Ruff, mypy for linting and formatting

### Frontend Stack
- **Framework**: Next.js 15.4.1 with App Router
- **Runtime**: React 19 with TypeScript 5.5
- **UI Library**: Radix UI components with Tailwind CSS 3.4.1
- **State Management**: Redux Toolkit + TanStack Query
- **Forms**: React Hook Form with Zod validation
- **Internationalization**: next-intl for multi-language support
- **Package Management**: pnpm 10.13.1
- **Testing**: Jest + Testing Library + Playwright

### AI/ML Infrastructure
- **Local LLM**: Ollama 0.11.4 with GPU support
- **Embedding Models**: BGE-M3 (1024-dim), OpenAI embeddings (1536-dim)
- **LLM Providers**: DeepSeek, Moonshot, OpenRouter, Qwen, Zhipu
- **Vector Storage**: pgvector with HNSW indexing
- **ML Models**: BERT+BiLSTM for matching, MLP for assessment

### Infrastructure & DevOps
- **Containerization**: Docker Compose with optimized images
- **Reverse Proxy**: Nginx with environment-specific configs
- **Monitoring**: Prometheus + structured logging (structlog)
- **Storage**: MinIO for file storage and processing
- **Development**: Hot reload, volume mounting, unified proxy

## Code Organization Patterns

### Backend Structure (Clean Architecture)
```
app/
├── api/v1/           # API endpoints and routing
├── core/             # Configuration, security, database
├── crud/             # Data access layer
├── models/           # SQLAlchemy models
├── schemas/          # Pydantic schemas (API contracts)
├── services/         # Business logic layer
├── tasks.py          # Celery background tasks
└── worker.py         # Celery worker configuration
```

**Key Patterns**:
- **Layered Architecture**: API → Service → CRUD → Model
- **Async-First**: All database operations use async/await
- **Dependency Injection**: FastAPI dependency system
- **Schema Validation**: Pydantic models for request/response
- **Snowflake IDs**: BigInteger IDs with snowflake generation

### Frontend Structure (App Router)
```
app/
├── (auth)/           # Authentication routes
├── (dashboard)/      # Protected dashboard routes
├── [locale]/         # Internationalized routes
├── i18n/            # Internationalization setup
├── globals.css       # Global styles
├── layout.tsx        # Root layout
└── providers.tsx     # Context providers
```

**Key Patterns**:
- **Route Groups**: Organized by authentication state
- **Internationalization**: Built-in i18n with locale routing
- **Component Co-location**: Tests alongside components
- **Service Layer**: Separated API client services
- **Type Safety**: Comprehensive TypeScript coverage

## Development Workflow

### Package Management Rules
- **Backend**: Poetry with lock file management
- **Frontend**: pnpm with workspace support
- **Container Dependencies**: Isolated in Docker volumes
- **Refresh Commands**: `make frontend-package-refresh` / `make backend-package-refresh`

### Environment Management
- **Multi-Environment**: Development, Production, Testing, Staging
- **Environment Switching**: `make env-switch TO=production`
- **Configuration**: Environment-specific Docker Compose files
- **Service Profiles**: Different services per environment

### Database Management
- **Migrations**: Alembic for schema versioning
- **Seeding**: Environment-specific data seeding
- **Vector Support**: pgvector extension for embeddings
- **ID Strategy**: Snowflake IDs for distributed systems

## Coding Standards & Conventions

### Backend Standards
- **Code Style**: Black formatting, Ruff linting, 100-char line limit
- **Type Hints**: Comprehensive typing with mypy validation
- **Async Programming**: async/await for all I/O operations
- **Error Handling**: Structured exceptions with error codes
- **Testing**: pytest with async support, >80% coverage target

### Frontend Standards  
- **TypeScript**: Strict mode enabled, comprehensive typing
- **Component Pattern**: Functional components with React 19 hooks
- **Styling**: Tailwind CSS with component variants (CVA)
- **State Management**: Redux Toolkit for global state, TanStack Query for server state
- **Testing**: Jest + RTL for unit tests, Playwright for E2E

### API Design Standards
- **RESTful Design**: Resource-based URLs, standard HTTP methods
- **Authentication**: JWT tokens in Authorization header
- **Error Responses**: Consistent format with error_code field
- **Pagination**: Unified format `{items, total, skip, limit}`
- **Internationalization**: Error codes mapped to translated messages

## Testing Architecture

### Backend Testing
- **Framework**: pytest with pytest-asyncio
- **Coverage**: pytest-cov with >80% requirement
- **Test Types**: Unit, integration, API endpoint tests
- **Fixtures**: Comprehensive test fixtures for models and services
- **Database**: Test database isolation with transaction rollback

### Frontend Testing
- **Unit Tests**: Jest + Testing Library with jsdom
- **Coverage**: 80% threshold across branches/functions/lines/statements  
- **E2E Tests**: Playwright for cross-browser testing
- **Mock Strategy**: MSW for API mocking
- **Component Tests**: Isolated component testing with mocks

## Integration Points & Constraints

### External Integrations
- **AI Providers**: Multiple LLM providers with fallback chains
- **File Processing**: PDF/DOCX parsing for resume analysis
- **Vector Search**: pgvector for similarity matching
- **Background Tasks**: Celery for async processing
- **Storage**: MinIO S3-compatible object storage

### Development Constraints
- **Root Directory**: Strict file organization rules, no pollution allowed
- **App Directory**: Standardized subdirectory structure enforcement
- **Docker Dependencies**: Container-isolated package management
- **Network Access**: External DNS configuration for AI provider access
- **GPU Support**: Optional NVIDIA GPU support for Ollama

### Performance Requirements
- **API Response**: <200ms target response time
- **Frontend**: <3s first load, <1s navigation
- **ML Inference**: <200ms for embeddings and scoring
- **Database**: Optimized queries with proper indexing
- **Caching**: Redis-based caching for frequent operations

## Documentation & Knowledge Management

### Project Documentation
- **Comprehensive Docs**: Detailed architecture, API, and development guides
- **Auto-Generated**: OpenAPI/Swagger documentation
- **Internationalization**: Multi-language documentation support
- **Archive System**: Historical documentation preservation
- **PRP Workflow**: Project Requirements and Process documentation

### Code Documentation
- **API Docs**: Automatic OpenAPI generation from Pydantic schemas
- **Type Hints**: Self-documenting code through comprehensive typing
- **Inline Comments**: Strategic commenting for complex business logic
- **README Files**: Component and service-specific documentation

## Security & Compliance

### Authentication & Authorization
- **JWT Strategy**: Access + refresh token pattern
- **RBAC**: Role-based access control system
- **Token Management**: Secure storage in HttpOnly cookies + localStorage
- **Session Management**: Redis-based session storage
- **Development Bypass**: Special development-only tokens

### Data Protection
- **Input Validation**: Pydantic schemas for all API inputs
- **SQL Injection**: SQLAlchemy ORM protection
- **File Upload**: Secure file handling with MinIO
- **Sensitive Data**: Proper environment variable management
- **Error Handling**: No sensitive information in error responses

## Deployment & Operations

### Development Environment
- **Unified Access**: Single port (8088) for all services
- **Hot Reload**: Live code updates for both frontend and backend
- **Service Discovery**: Docker Compose networking
- **Log Aggregation**: Structured logging with proper formatting
- **Health Checks**: Comprehensive service health monitoring

### Production Considerations
- **Container Optimization**: Multi-stage builds, minimal base images
- **Monitoring Stack**: Prometheus metrics collection
- **Security Hardening**: Production-specific configurations
- **Performance Tuning**: Optimized for production workloads
- **Backup Strategy**: Data persistence and recovery planning

---

## Key Takeaways for Requirements-Driven Development

1. **Mature Architecture**: Well-established patterns and conventions to follow
2. **Comprehensive Testing**: Strong testing culture with specific coverage requirements  
3. **Multi-Language Support**: Built-in internationalization framework
4. **AI/ML Integration**: Sophisticated LLM integration with fallback strategies
5. **Development Efficiency**: Optimized development workflow with automation
6. **Security Focus**: Production-ready security implementations
7. **Performance Oriented**: Clear performance targets and optimization strategies
8. **Documentation Culture**: Comprehensive documentation and knowledge management

This system demonstrates enterprise-level architecture maturity with clear development patterns, comprehensive tooling, and strong operational practices. New features should follow established conventions and integrate seamlessly with existing infrastructure.