# Service Consolidation Dependency Analysis

## Import Dependency Map

### Current Import Patterns

#### Assessment Service Dependencies
```bash
# Files importing assessment_service:
app/tasks.py:365 -> from app.services.assessment_service import assessment_service

# Files importing assessment_service_enhanced: 
app/services/assessment_service_enhanced.py:12 -> from app.services.ai_service_manager import ai_service_manager

# Risk Level: LOW - Only one main import to update
```

#### Embedding Service Dependencies  
```bash
# Files importing embedding_service:
app/tasks.py:367 -> from app.services.embedding_service import embedding_service
tests/test_vectors.py:318,336 -> from app.services.embedding_service import EmbeddingService

# Files importing embedding_service_refactored:
app/services/embedding_service_refactored.py:17 -> from app.services.ai_service_manager import ai_service_manager

# Risk Level: LOW - Few imports to update
```

#### Resume Parsing Dependencies
```bash
# Files importing resume_parsing_service:
app/tasks.py:363 -> from app.services.resume_parsing_service import resume_parsing_service

# Files importing resume_parser: 
# (No current imports found - consolidation target)

# Risk Level: LOW - Single main import to update  
```

#### Storage Service Dependencies
```bash
# Files importing storage_service:
app/services/resume_version_service.py:15 -> from app.services.storage_service import storage_service
tests/test_resume_management.py:14,88 -> from app.services.storage_service import StorageService
tests/test_resume_regression.py:162,175,252 -> from app.services.storage_service import StorageService

# Files importing file_service:
# (No direct imports found)

# Risk Level: MEDIUM - Multiple test files to update
```

#### Permission/Role Service Dependencies
```bash
# Files importing role service:
app/services/role.py -> RoleService (singleton: role_service)
tests/api/v1/test_roles.py:12 -> from app.services.role import role_service  
tests/api/v1/test_monitoring.py:12 -> from app.services.role import role_service

# Files importing permission service:
app/services/permission.py -> PermissionService (singleton: permission_service)
tests/api/v1/test_permissions.py:11 -> from app.services.permission import permission_service
tests/api/v1/test_monitoring.py:11 -> from app.services.permission import permission_service

# Risk Level: HIGH - Multiple API endpoints and tests to update
```

#### Monitoring Service Dependencies
```bash
# Files importing monitoring service:
app/services/monitoring.py:26 -> from app.services.health import health_service
app/tasks.py:75,204,279 -> from app.services.monitoring import monitoring_service  
tests/api/v1/test_monitoring.py:13 -> from app.services.monitoring import monitoring_service

# Files importing celery_monitoring:
# (Celery monitoring is used internally - no external imports)

# Risk Level: MEDIUM - Multiple task imports to update
```

## Consolidation Impact Analysis

### Phase 1: Assessment & Embedding (Low Risk)
**Files to Update**: 4 total
- `app/tasks.py` (2 import lines)  
- `tests/test_vectors.py` (2 import lines)
- Delete 2 duplicate service files

**Validation Required**:
- AI service integration still works
- Task queue processing unaffected
- Test coverage maintained

### Phase 2: Resume & Storage (Medium Risk)  
**Files to Update**: 8 total
- `app/tasks.py` (1 import line)
- `app/services/resume_version_service.py` (1 import line)
- 5 test files with storage service imports
- Delete 2 duplicate service files  

**Validation Required**:
- File upload/download functionality
- Resume parsing pipeline
- MinIO integration intact
- Test suite passes

### Phase 3: Permission & Monitoring (High Risk)
**Files to Update**: 12+ total
- Multiple API route files (need to be identified)
- 6 test files with permission/role imports  
- `app/tasks.py` (monitoring imports)
- Create 1 new unified permission service
- Delete 3 old service files

**Validation Required**:  
- RBAC authorization working
- API security intact
- Monitoring dashboards functional
- All authentication flows work

## Breaking Change Analysis

### Potential Breaking Changes

#### Import Path Changes
```python
# BEFORE (multiple inconsistent imports):
from app.services.assessment_service import assessment_service
from app.services.assessment_service_enhanced import AssessmentService
from app.services.embedding_service import embedding_service  
from app.services.embedding_service_refactored import EmbeddingService
from app.services.role import role_service
from app.services.permission import permission_service

# AFTER (clean, consistent imports):
from app.services.assessment_service import assessment_service
from app.services.embedding_service import embedding_service
from app.services.resume_parser import resume_parser
from app.services.storage_service import storage_service  
from app.services.permission_service import permission_service
from app.services.monitoring import monitoring_service
```

#### Service Instance Names
```python
# Need to ensure singleton instance names remain consistent:
assessment_service -> assessment_service ✓ (no change)
embedding_service -> embedding_service ✓ (no change)  
resume_parsing_service -> resume_parser ⚠️ (name change)
storage_service -> storage_service ✓ (no change)
role_service + permission_service -> permission_service ⚠️ (consolidation)
monitoring_service -> monitoring_service ✓ (no change)
```

### Mitigation Strategies

#### Gradual Import Updates
```python
# Step 1: Update service files with backward compatibility
# In new consolidated permission_service.py:
from app.services.permission_service import permission_service

# Backward compatibility shims:
role_service = permission_service  # Temporary alias
```

#### Import Validation Script
```bash
# Create script to validate all imports resolve:
#!/bin/bash
echo "Validating service imports..."
python -c "
from app.services.assessment_service import assessment_service
from app.services.embedding_service import embedding_service  
from app.services.resume_parser import resume_parser
from app.services.storage_service import storage_service
from app.services.permission_service import permission_service
from app.services.monitoring import monitoring_service
print('✅ All service imports successful')
"
```

## Critical Dependency Chains

### AI Service Manager Chain
```
ai_service_manager -> assessment_service_enhanced
                 -> embedding_service_refactored  
                 -> resume_parsing_service
                 
# Must preserve this chain in consolidated services
```

### Database Session Chain
```
AsyncSession -> CRUD operations -> Service layer -> API endpoints

# All consolidated services must maintain proper session management
```

### Redis Cache Chain  
```
redis_client -> cache_service -> enhanced services -> API responses

# Enhanced caching behavior must be preserved
```

## Testing Strategy for Dependencies

### Import Resolution Tests
```python
def test_service_imports():
    """Test that all consolidated services can be imported"""
    try:
        from app.services.assessment_service import assessment_service
        from app.services.embedding_service import embedding_service
        from app.services.resume_parser import resume_parser
        from app.services.storage_service import storage_service  
        from app.services.permission_service import permission_service
        from app.services.monitoring import monitoring_service
        assert True
    except ImportError as e:
        pytest.fail(f"Import failed: {e}")
```

### Service Instance Tests
```python
def test_service_singletons():
    """Test that service singletons are properly initialized"""
    from app.services.assessment_service import assessment_service
    from app.services.embedding_service import embedding_service
    
    assert assessment_service is not None
    assert hasattr(assessment_service, 'generate_assessment')
    assert embedding_service is not None  
    assert hasattr(embedding_service, 'generate_embedding')
```

### Integration Chain Tests
```python
async def test_service_integration_chains():
    """Test that service dependency chains work end-to-end"""
    # Test: File upload -> parsing -> embedding -> storage
    # Test: User auth -> role check -> permission validation
    # Test: Health check -> metrics -> monitoring dashboard
```

## Rollback Strategy

### Git Branch Protection
```bash
# Create feature branch for consolidation
git checkout -b feature/service-consolidation
git checkout -b backup/pre-consolidation  # Safety backup

# Work in consolidation branch
git checkout feature/service-consolidation
```

### Service File Backups
```bash
# Before deleting duplicates, archive them
mkdir -p archive/services/pre-consolidation/
cp app/services/assessment_service_enhanced.py archive/services/pre-consolidation/
cp app/services/embedding_service_refactored.py archive/services/pre-consolidation/
cp app/services/resume_parsing_service.py archive/services/pre-consolidation/
cp app/services/file_service.py archive/services/pre-consolidation/
cp app/services/role.py archive/services/pre-consolidation/
cp app/services/celery_monitoring.py archive/services/pre-consolidation/
```

### Database State Preservation
```bash
# No database changes expected, but backup for safety
pg_dump talent_forge_dev > backup/pre_consolidation_$(date +%Y%m%d_%H%M%S).sql
```

### Quick Rollback Commands
```bash
# If consolidation fails, quick rollback:
git checkout backup/pre-consolidation
docker-compose restart backend
```

This dependency analysis ensures we have a complete understanding of the consolidation impact and proper mitigation strategies for each phase of the implementation.