# Service Consolidation Implementation Guide

## Pre-Implementation Checklist

### Environment Setup
```bash
# 1. Create backup branch
cd /home/<USER>/source_code/talent_forge_pro
git checkout -b backup/pre-service-consolidation
git checkout -b feature/service-consolidation

# 2. Verify current system health
make status
make logs backend | tail -20
curl http://localhost:8088/api/v1/health

# 3. Run full test suite to establish baseline
cd app/backend
pytest --tb=short -v

# 4. Document current service file sizes/structure
find app/services -name "*.py" -exec wc -l {} \; | sort -n > /tmp/service_baseline.txt
```

### Dependency Validation
```bash
# Verify all imports are working before changes
python -c "
import sys
sys.path.append('app')
try:
    from app.services.assessment_service import assessment_service
    from app.services.embedding_service import embedding_service
    from app.services.resume_parsing_service import resume_parsing_service
    from app.services.storage_service import storage_service
    from app.services.role import role_service
    from app.services.permission import permission_service
    from app.services.monitoring import monitoring_service
    print('✅ All baseline imports successful')
except Exception as e:
    print(f'❌ Baseline import failed: {e}')
    exit(1)
"
```

## Phase 1 Implementation: Assessment & Embedding Services

### Step 1: Assessment Service Consolidation

#### 1.1 Compare Service Implementations
```bash
# Compare the two assessment services
diff app/services/assessment_service.py app/services/assessment_service_enhanced.py
```

#### 1.2 Merge Implementation
```python
# Target file: app/services/assessment_service.py
# Strategy: Replace content with enhanced version, keeping filename

# Key changes to preserve from enhanced version:
# - AI Service Manager integration
# - Enhanced caching with Redis  
# - Multi-provider fallback
# - Comprehensive error handling

# Implementation steps:
# 1. Read both files carefully
# 2. Copy enhanced version content to main file
# 3. Ensure singleton instance name stays "assessment_service"
# 4. Test imports work correctly
```

#### 1.3 Update Assessment Service
```bash
# Backup original
cp app/services/assessment_service.py app/services/assessment_service.py.backup

# Replace with enhanced version content (manual merge required)
# Ensure these key elements are preserved:
# - from app.services.ai_service_manager import ai_service_manager
# - Enhanced caching logic
# - Error handling and fallback mechanisms
# - Singleton instance: assessment_service = AssessmentService()
```

#### 1.4 Validate Assessment Service
```python
# Test the consolidated service
python -c "
from app.services.assessment_service import assessment_service
print('✅ Assessment service import successful')
print(f'Service type: {type(assessment_service)}')
print(f'Has AI manager: {hasattr(assessment_service, \"ai_manager\")}')
"
```

### Step 2: Embedding Service Consolidation

#### 2.1 Merge Embedding Implementation  
```bash
# Backup original
cp app/services/embedding_service.py app/services/embedding_service.py.backup

# Compare implementations
diff app/services/embedding_service.py app/services/embedding_service_refactored.py

# Manual merge required - preserve refactored enhancements:
# - AI Service Manager integration  
# - Hash fallback embedding method
# - Enhanced error handling
# - Health check functionality
```

#### 2.2 Validate Embedding Service
```python  
# Test consolidated embedding service
python -c "
from app.services.embedding_service import embedding_service
print('✅ Embedding service import successful') 
print(f'Service type: {type(embedding_service)}')
print(f'Has hash fallback: {hasattr(embedding_service, \"_hash_fallback_embedding\")}')
"
```

### Step 3: Phase 1 Validation
```bash
# Run tests for consolidated services
pytest app/tests/services/test_assessment_service.py -v
pytest app/tests/services/test_embedding_service.py -v

# Test task imports still work  
python -c "
from app.tasks import *
print('✅ Task imports successful')
"

# If all tests pass, delete duplicate files
rm app/services/assessment_service_enhanced.py
rm app/services/embedding_service_refactored.py

# Commit Phase 1
git add -A
git commit -m "Phase 1: Consolidate assessment and embedding services

- Merged assessment_service_enhanced.py into assessment_service.py
- Merged embedding_service_refactored.py into embedding_service.py  
- Preserved AI service manager integration and enhanced features
- All tests passing"
```

## Phase 2 Implementation: Resume & Storage Services

### Step 1: Resume Parsing Consolidation

#### 1.1 Analyze Resume Services
```bash
# Compare the resume parsing implementations
wc -l app/services/resume_parser.py app/services/resume_parsing_service.py
diff app/services/resume_parser.py app/services/resume_parsing_service.py
```

#### 1.2 Consolidation Strategy Decision
```python
# Decision: Use resume_parser.py as target (better name for core functionality)
# Merge database integration methods from resume_parsing_service.py

# Key elements to merge:
# - Database CRUD operations
# - Embedding generation and storage
# - Batch processing capabilities  
# - AI Service Manager integration
```

#### 1.3 Merge Resume Services
```bash
# Backup files
cp app/services/resume_parser.py app/services/resume_parser.py.backup
cp app/services/resume_parsing_service.py app/services/resume_parsing_service.py.backup

# Manual merge required:
# 1. Keep core parsing logic from resume_parser.py
# 2. Add database methods from resume_parsing_service.py
# 3. Unify into single ResumeParser class
# 4. Ensure singleton is named appropriately for new primary name
```

#### 1.4 Update Task Import
```python
# Update app/tasks.py line 363:
# FROM: from app.services.resume_parsing_service import resume_parsing_service  
# TO:   from app.services.resume_parser import resume_parser

# Update task function calls accordingly
```

### Step 2: Storage Service Consolidation

#### 2.1 Merge Storage Services  
```bash
# Compare storage implementations
diff app/services/storage_service.py app/services/file_service.py

# Strategy: Keep storage_service.py as primary (better architecture)
# Add file validation methods from file_service.py
```

#### 2.2 Enhanced Storage Service
```python
# Merge key methods from file_service.py into storage_service.py:
# - File upload validation
# - Security checks
# - File type validation  
# - Health check methods

# Ensure MinIO functionality is preserved
# Maintain singleton name: storage_service
```

#### 2.3 Update Storage Dependencies
```bash
# Files to update (based on dependency analysis):
# - app/services/resume_version_service.py (line 15)
# - Multiple test files

# Update imports but keep same singleton name
```

### Step 3: Phase 2 Validation
```bash
# Test consolidated services
pytest app/tests/services/test_resume_parser.py -v  
pytest app/tests/services/test_storage_service.py -v
pytest app/tests/test_resume_management.py -v

# Test file operations work
python -c "
from app.services.storage_service import storage_service
print('✅ Storage service health:', storage_service.get_storage_stats())
"

# If tests pass, cleanup
rm app/services/resume_parsing_service.py
rm app/services/file_service.py

# Commit Phase 2
git add -A
git commit -m "Phase 2: Consolidate resume and storage services

- Merged resume_parsing_service.py into resume_parser.py
- Merged file_service.py into storage_service.py
- Updated task imports and dependencies
- All file operations and parsing tests passing"
```

## Phase 3 Implementation: Permission & Monitoring Services

### Step 1: Permission Service Creation

#### 1.1 Create Unified Permission Service
```bash
# Create new unified service
touch app/services/permission_service.py
```

#### 1.2 Merge Permission Logic
```python
# In app/services/permission_service.py:
# Combine functionality from role.py and permission.py

class PermissionService:
    """Unified service for role-based access control (RBAC)"""
    
    # Role management methods (from role.py)
    async def get_role(self, db: AsyncSession, role_id: int) -> RoleResponse
    async def list_roles(self, db: AsyncSession, skip: int = 0, limit: int = 100, filters: RoleFilter = None) -> RoleListResponse
    async def create_role(self, db: AsyncSession, role_in: RoleCreate) -> RoleResponse
    async def update_role(self, db: AsyncSession, role_id: int, role_in: RoleUpdate) -> RoleResponse
    async def delete_role(self, db: AsyncSession, role_id: int) -> bool
    async def assign_role_to_user(self, db: AsyncSession, assignment: RoleAssignment) -> UserResponse
    async def bulk_assign_role(self, db: AsyncSession, bulk_assignment: BulkRoleAssignment) -> List[UserResponse]
    
    # Permission management methods (from permission.py)  
    async def get_permission(self, db: AsyncSession, permission_id: int) -> PermissionResponse
    async def list_permissions(self, db: AsyncSession, skip: int = 0, limit: int = 100, filters: PermissionFilter = None) -> PermissionListResponse
    async def create_permission(self, db: AsyncSession, permission_in: PermissionCreate) -> PermissionResponse
    async def update_permission(self, db: AsyncSession, permission_id: int, permission_in: PermissionUpdate) -> PermissionResponse
    async def delete_permission(self, db: AsyncSession, permission_id: int) -> bool
    
    # Authorization methods (combined functionality)
    async def get_user_permissions(self, db: AsyncSession, user_id: int) -> List[str]
    async def check_user_permission(self, db: AsyncSession, user_id: int, permission_code: str) -> bool
    
    # Initialization methods
    async def initialize_default_roles(self, db: AsyncSession) -> List[RoleResponse]
    async def initialize_default_permissions(self, db: AsyncSession) -> List[PermissionResponse]

# Create singleton
permission_service = PermissionService()
```

#### 1.3 Update API Routes
```bash
# Find all API routes using role_service and permission_service
grep -r "role_service\|permission_service" app/api/ --include="*.py"

# Update imports in API routes:
# FROM: from app.services.role import role_service
#       from app.services.permission import permission_service  
# TO:   from app.services.permission_service import permission_service

# Update route handlers to use single service
```

### Step 2: Monitoring Service Consolidation

#### 2.1 Merge Monitoring Services
```python
# Strategy: Enhance monitoring.py with celery_monitoring.py functionality
# Add Celery-specific monitoring methods to existing MonitoringService class

# Key methods to add from celery_monitoring.py:
# - get_overall_health() -> Celery task health
# - get_queue_metrics(queue_name) -> Queue monitoring  
# - get_task_history() -> Task execution history
# - record_task_event() -> Task event recording
# - get_performance_analytics() -> Performance analysis

# Preserve existing system monitoring functionality
```

#### 2.2 Update Task Monitoring Integration
```bash
# Update app/tasks.py monitoring imports
# Keep existing monitoring_service imports but ensure they work with enhanced service
```

### Step 3: Phase 3 Validation
```bash
# Critical security validation
python -c "
from app.services.permission_service import permission_service
print('✅ Permission service created')
print(f'Role methods: {[m for m in dir(permission_service) if \"role\" in m]}')
print(f'Permission methods: {[m for m in dir(permission_service) if \"permission\" in m]}')
"

# Test RBAC functionality
pytest app/tests/api/v1/test_roles.py -v
pytest app/tests/api/v1/test_permissions.py -v
pytest app/tests/api/v1/test_monitoring.py -v

# Test monitoring functionality  
python -c "
from app.services.monitoring import monitoring_service
import asyncio
async def test():
    # Test basic monitoring
    print('✅ Monitoring service functional')
test()
"

# If all tests pass, cleanup old files
rm app/services/role.py
rm app/services/permission.py  
rm app/services/celery_monitoring.py

# Commit Phase 3
git add -A
git commit -m "Phase 3: Consolidate permission and monitoring services

- Created unified permission_service.py from role.py + permission.py
- Enhanced monitoring.py with celery_monitoring.py functionality  
- Updated API routes to use consolidated permission service
- All RBAC and monitoring tests passing"
```

## Post-Implementation Validation

### Full System Test
```bash
# Restart application with consolidated services
make down
make up

# Wait for startup
sleep 30

# Test all major endpoints
curl http://localhost:8088/api/v1/health
curl -H "Authorization: Bearer dev_bypass_token_2025_talentforge" http://localhost:8088/api/v1/auth/me
curl -H "Authorization: Bearer dev_bypass_token_2025_talentforge" http://localhost:8088/api/v1/candidates/
curl -H "Authorization: Bearer dev_bypass_token_2025_talentforge" http://localhost:8088/api/v1/monitoring/health
```

### Comprehensive Test Suite
```bash
# Run full test suite
cd app/backend
pytest --tb=short -x

# Run specific service tests
pytest app/tests/services/ -v
pytest app/tests/api/v1/ -v

# Performance regression test
time python -c "
from app.services.assessment_service import assessment_service
from app.services.embedding_service import embedding_service
from app.services.resume_parser import resume_parser  
from app.services.storage_service import storage_service
from app.services.permission_service import permission_service
from app.services.monitoring import monitoring_service
print('✅ All consolidated services loaded successfully')
"
```

### Service Directory Cleanup Verification
```bash
# Verify only clean service files remain
ls -la app/services/*.py

# Should see clean names only:
# - assessment_service.py
# - embedding_service.py  
# - resume_parser.py
# - storage_service.py
# - permission_service.py
# - monitoring.py
# - (other non-duplicate services)

# Should NOT see:
# - assessment_service_enhanced.py
# - embedding_service_refactored.py
# - resume_parsing_service.py
# - file_service.py
# - role.py
# - celery_monitoring.py
```

## Rollback Procedures

### Quick Rollback (if immediate issues)
```bash
# Return to backup branch
git checkout backup/pre-service-consolidation
make down && make up

# Verify rollback successful
curl http://localhost:8088/api/v1/health
```

### Partial Rollback (if specific phase fails)
```bash
# Rollback specific consolidation phase
git revert <commit-hash-of-failed-phase>

# Restore specific duplicate files if needed
git checkout backup/pre-service-consolidation -- app/services/<specific-duplicate-file>.py
```

### Recovery from Archive
```bash
# If git rollback insufficient, restore from backups
cp app/services/*.backup app/services/
# Rename backup files to original names
# Test system recovery
```

## Success Validation Checklist

### ✅ Code Quality
- [ ] All consolidated services use clean names (no suffixes)
- [ ] All enhanced functionality preserved in consolidated services
- [ ] No code duplication between services
- [ ] Proper error handling and logging maintained
- [ ] All imports resolve correctly

### ✅ Functionality  
- [ ] All API endpoints work correctly
- [ ] File upload/download operations work
- [ ] User authentication and authorization work
- [ ] System monitoring and health checks work
- [ ] AI service integrations work
- [ ] Task queue processing works

### ✅ Performance
- [ ] API response times ≤ baseline
- [ ] Memory usage ≤ baseline  
- [ ] Application startup time ≤ baseline
- [ ] No new error patterns in logs

### ✅ Testing
- [ ] All unit tests pass
- [ ] All integration tests pass
- [ ] All API tests pass
- [ ] Manual smoke tests pass
- [ ] Test coverage maintained >80%

The consolidation is complete when all checkboxes are marked and the system operates normally with the clean, consolidated service architecture.