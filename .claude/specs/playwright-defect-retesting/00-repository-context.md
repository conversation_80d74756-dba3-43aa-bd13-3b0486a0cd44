# Repository Context Analysis

## Project Overview
**TalentForge Pro** is an intelligent talent assessment and job matching system using hybrid intelligence architecture (Rule Engine + LLM).

## Technology Stack

### Backend (FastAPI)
- **Framework**: FastAPI 0.110.0 with Python 3.12
- **Database**: PostgreSQL with pgvector extension, Redis for caching
- **ORM**: SQLAlchemy 2.0 with asyncio support
- **Authentication**: JWT + RBAC with python-jose
- **ML Integration**: BERT+BiLSTM models, Ollama for embeddings
- **Background Tasks**: Celery for async processing
- **API Documentation**: Auto-generated Swagger/OpenAPI

### Frontend (Next.js)
- **Framework**: Next.js with React and TypeScript
- **UI Library**: Transitioning to shadcn/ui from @headlessui/react
- **State Management**: React Query (TanStack Query)
- **Testing**: Jest for unit tests, Playwright for E2E
- **Development**: TypeScript strict mode, ESLint, Prettier

### Infrastructure
- **Containerization**: Docker with docker-compose orchestration
- **Proxy**: Nginx reverse proxy
- **Storage**: MinIO for object storage
- **Orchestration**: Comprehensive Makefile with 50+ commands

## Testing Architecture

### Existing Test Framework
- **E2E Testing**: Playwright with MCP integration
- **API Testing**: Tavern framework with Docker
- **Unit Testing**: pytest (backend), Jest (frontend)
- **Test Environment**: http://localhost:8088
- **Authentication**: Development token `dev_bypass_token_2025_talentforge`

### Testing Structure
```
app/scripts/test/
├── playwright/          # Playwright E2E tests
├── api/                # API integration tests  
├── backend/            # Backend unit tests
└── frontend/           # Frontend unit tests
```

## Code Patterns & Conventions

### Backend Architecture
- **Layered Architecture**: API → Service → CRUD → Model
- **Async/Await**: All I/O operations are async
- **Type Safety**: Pydantic for validation and serialization
- **Error Handling**: Structured error codes and responses
- **Database**: Alembic migrations, BigInteger IDs (Snowflake)

### Frontend Architecture
- **Component Structure**: Functional components with hooks
- **TypeScript**: Strict type checking enabled
- **API Client**: Centralized axios client with interceptors
- **Routing**: Next.js App Router
- **Internationalization**: next-intl with JSON translation files

## Current Testing Context

### Previous Test Execution
- **Date**: 2025-09-01 08:21 - 08:30 UTC
- **Coverage**: 5 phases - Admin Panel, Candidate Assessment, Dashboard, Job Management, AI-Questionnaire
- **Result**: 100% success rate with 6 identified defects
- **Focus Areas**: UI translation issues, monitoring performance, Celery service configuration

### Identified Defects (Fixed)
1. **Translation Issues** (3 defects): Missing Chinese translations in dashboard and positions
2. **Performance Issues** (2 defects): Monitoring page loading, console errors
3. **Service Configuration** (1 defect): Celery worker configuration

## Development Workflow

### Quality Standards
- **Code Quality Gates**: 8-step validation cycle
- **Testing Requirements**: >80% unit test coverage, critical path integration tests
- **API Standards**: RESTful design, unified response format, JWT authentication
- **Performance**: API < 200ms, Frontend < 3s load time

### Git Workflow
- **Branches**: feature/*, bugfix/*, hotfix/*
- **Commits**: Conventional commits (feat:, fix:, docs:, etc.)
- **Review Process**: PR must pass code review and CI tests

## Integration Points for Testing

### Authentication Flow
- **Login URL**: http://localhost:8088/login
- **Test Credentials**: <EMAIL> / test123
- **Dev Token**: dev_bypass_token_2025_talentforge (bypass authentication)
- **Token Storage**: Both localStorage and cookies for SSR/CSR compatibility

### Key Testing Endpoints
- **Health Check**: /api/v1/health
- **Authentication**: /api/v1/auth/me
- **User Management**: /api/v1/users/
- **Dashboard Data**: /api/v1/dashboard/stats
- **Monitoring**: /api/v1/admin/monitoring/health

### Critical User Workflows
1. **Admin Panel**: User management, system monitoring, configuration
2. **Candidate Assessment**: CRUD operations, ML scoring, search/filtering
3. **Dashboard**: Data visualization, analytics, real-time updates
4. **Job Management**: Position creation, candidate matching, vector generation
5. **AI Questionnaire**: Template management, question types, import/export

## Testing Constraints

### Environment Requirements
- **Docker Services**: All services must be running via `make up`
- **Data Requirements**: Test environment contains real data (451 candidates, 96 positions)
- **Browser**: Chrome (Playwright MCP default)
- **Network**: Local environment only (localhost:8088)

### Known Issues to Monitor
- **Monitoring Performance**: Previous 60s loading time (should be fixed)
- **Translation Coverage**: Missing Chinese translations (should be fixed)
- **Celery Status**: Service health configuration (should be fixed)
- **API Response Time**: FastAPI trailing slash redirects
- **UI Library Migration**: shadcn/ui transition completeness

This context provides the foundation for understanding the codebase structure, testing requirements, and integration points needed for effective defect retesting.