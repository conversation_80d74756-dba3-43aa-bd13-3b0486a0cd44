# Repository Context Analysis Report

## Project Overview

**TalentForge Pro** is an intelligent talent assessment and job matching system built with a hybrid intelligence architecture combining rule engines with Large Language Models (LLMs). The project is a comprehensive full-stack application with microservices architecture.

### Project Type & Purpose
- **Type**: Web-based talent assessment platform
- **Domain**: HR Technology / AI-powered recruitment
- **Primary Functions**:
  - Five-dimensional candidate evaluation (Digital Literacy, Industry Skills, Job Skills, Innovation Capability, Learning Potential)
  - Resume parsing and vector-based matching
  - AI-powered questionnaire generation
  - Comprehensive dashboard analytics
  - Multi-language internationalization support

## Technology Stack

### Backend Stack
```yaml
Language: Python 3.12
Framework: FastAPI 0.110+
ORM: SQLAlchemy 2.0 (async)
Database: PostgreSQL 17 with pgvector 0.8.0
Cache: Redis 7.4.4
Task Queue: Celery 5.4.0
Authentication: JWT with passlib/bcrypt
Validation: Pydantic 2.7.0
Migration: Alembic 1.13.0
Storage: MinIO (object storage)
Monitoring: Prometheus + custom health checks
Package Manager: Poetry
```

### Frontend Stack
```yaml
Framework: Next.js 15.4.1
Runtime: React 19.0.0
Language: TypeScript 5.5.0
State Management: Redux Toolkit + TanStack Query
UI Library: Radix UI + shadcn/ui components
Styling: Tailwind CSS 3.4.1
Internationalization: next-intl 4.3.4
Testing: Jest + Playwright
Package Manager: pnpm 10.13.1
```

### ML & AI Services
```yaml
Vector Database: pgvector (integrated with PostgreSQL)
Embedding Models: BGE-M3 (1024-dim), OpenAI (1536-dim backup)
Local LLM: Ollama v0.11.4
Document Processing: PaddleOCR, PyPDF2, python-docx
ML Models: BERT+BiLSTM (matching), MLP (evaluation)
```

### Infrastructure
```yaml
Containerization: Docker with docker-compose
Reverse Proxy: Nginx
Development Data: ~/dev_data/hephaestus/
Environment Management: Make-based workflow
```

## Project Structure

```
talent_forge_pro/
├── app/                          # Main application directory
│   ├── backend/                  # FastAPI backend service
│   │   ├── app/
│   │   │   ├── api/             # API routes and endpoints
│   │   │   ├── core/            # Core services (auth, config, database)
│   │   │   ├── crud/            # Database CRUD operations
│   │   │   ├── models/          # SQLAlchemy models
│   │   │   ├── schemas/         # Pydantic schemas
│   │   │   ├── services/        # Business logic layer
│   │   │   └── utils/           # Utility functions
│   │   ├── alembic/             # Database migrations
│   │   └── tests/               # Backend tests
│   ├── frontend/                # Next.js frontend application
│   │   ├── app/                 # Next.js app directory
│   │   │   ├── (auth)/         # Authentication routes
│   │   │   ├── (dashboard)/    # Dashboard routes
│   │   │   └── i18n/           # Internationalization
│   │   ├── components/         # React components
│   │   │   ├── ui/             # shadcn/ui components
│   │   │   └── [domain]/       # Domain-specific components
│   │   ├── contexts/           # React contexts
│   │   ├── public/             # Static assets
│   │   └── types/              # TypeScript definitions
│   ├── configs/                # Configuration files
│   │   ├── nginx/              # Nginx configurations
│   │   └── redis/              # Redis configurations
│   ├── scripts/                # Automation scripts
│   │   ├── development/        # Development utilities
│   │   ├── test/               # Testing scripts
│   │   └── validation/         # Validation scripts
│   └── docker-compose.yml      # Docker services configuration
├── docs/                       # Project documentation
│   ├── reports/                # Test and implementation reports
│   └── [various].md            # Technical documentation
├── archive/                    # Historical files and backups
├── .claude/                    # Claude Code specifications
├── Makefile                    # Main orchestration tool
└── CLAUDE.md                   # Comprehensive project guide
```

## Code Organization Patterns

### Backend Architecture
- **Layered Architecture**: API → Service → CRUD → Model
- **Dependency Injection**: Service layer abstracts business logic
- **Async/Await**: Full async support throughout the stack
- **CRUD Pattern**: Standardized database operations
- **Service Layer**: Business logic isolation
- **Schema Validation**: Pydantic for request/response validation

### Frontend Architecture
- **Component-Based**: React functional components with hooks
- **Context Providers**: Authentication, theme, internationalization
- **Custom Hooks**: Reusable business logic
- **Route Groups**: Next.js app router with grouped routes
- **State Management**: Redux Toolkit Query for server state
- **Type Safety**: Strict TypeScript configuration

### API Design Standards
- **RESTful Endpoints**: Consistent REST patterns
- **Standardized Responses**: Unified response format with pagination
- **JWT Authentication**: Bearer token with automatic refresh
- **Error Handling**: Structured error codes with i18n support
- **Validation**: Request/response validation at all layers

## Development Workflow

### Package Management
- **Backend**: Poetry for dependency management and virtual environments
- **Frontend**: pnpm for fast, efficient package management
- **Container Isolation**: Dependencies isolated in Docker volumes
- **Refresh Commands**: `make frontend-package-refresh`, `make backend-package-refresh`

### Docker Development Environment
- **Services**: PostgreSQL, Redis, MinIO, Ollama, Backend, Frontend, Celery, Nginx
- **Data Persistence**: Named volumes for development data
- **Hot Reloading**: Live reload for both frontend and backend
- **Environment Variables**: Comprehensive .env configuration
- **Service Health Checks**: Built-in health monitoring

### Build & Deployment
- **Make-based Commands**: Unified command interface via Makefile
- **Environment Support**: development, production, testing, staging
- **Service Orchestration**: Docker Compose with profiles
- **Quality Gates**: Pre-commit hooks, type checking, linting

### Testing Strategy
- **Backend**: pytest + pytest-asyncio, >80% coverage target
- **Frontend**: Jest + React Testing Library + Playwright E2E
- **API Testing**: Tavern for contract testing
- **Integration Testing**: Docker-based full-stack testing

## Development Standards & Conventions

### Root Directory Organization
**MANDATORY Clean Architecture**:
- ✅ **Allowed**: CLAUDE.md, README.md, Makefile, LICENSE, app/, docs/, archive/
- ❌ **Prohibited**: Docker configs, test files, reports in root
- **Rationale**: Prevents architecture pollution and file conflicts

### Database Migration Rules
**MANDATORY Migration Workflow**:
- Model changes → Immediate migration generation → Execution → Verification
- **Tools**: Alembic for schema migrations
- **Validation**: Pre-deployment migration testing

### Internationalization
- **Architecture**: React Context + JSON translation files
- **Error Handling**: Backend error codes + frontend translation
- **Language Support**: English, Chinese (extensible)

### ID Type Consistency
- **Standard**: Snowflake IDs (64-bit integers)
- **API Schema**: `Union[int, str]` or custom `SnowflakeID` type
- **Frontend**: String representation (JSON serialization)

## Quality Assurance & Standards

### Code Quality
- **Backend**: Ruff linting, Black formatting, MyPy type checking
- **Frontend**: ESLint + Prettier, TypeScript strict mode
- **Testing**: Comprehensive test coverage requirements
- **Pre-commit**: Automated quality checks

### Performance Requirements
- **API Response**: <200ms target
- **Frontend Load**: <3s first contentful paint
- **ML Inference**: <200ms for matching operations
- **Database**: Connection pooling and query optimization

### Security Standards
- **Authentication**: JWT with refresh tokens
- **Authorization**: RBAC with granular permissions
- **Data Protection**: bcrypt password hashing, HTTPS enforcement
- **Input Validation**: Comprehensive Pydantic validation
- **Environment Isolation**: Secure development token system

## Integration Points & APIs

### Core API Endpoints
```
Authentication: /api/v1/auth/*
User Management: /api/v1/users/*
Candidates: /api/v1/candidates/*
Positions: /api/v1/positions/*
Assessments: /api/v1/assessments/*
Monitoring: /api/v1/admin/monitoring/*
```

### External Services
- **Ollama**: Local LLM service on port 11434
- **MinIO**: Object storage for resumes/documents
- **PostgreSQL**: Primary database with vector extensions
- **Redis**: Caching and session storage

## Development Environment Access

### Service Ports
- **Frontend**: http://localhost:8088
- **Backend API**: http://localhost:8001
- **PostgreSQL**: localhost:15432
- **Redis**: localhost:16379
- **MinIO Console**: Accessible via nginx proxy
- **Prometheus**: localhost:19090

### Development Authentication
- **Bypass Token**: `dev_bypass_token_2025_talentforge`
- **Default Admin**: <EMAIL> / test123
- **Environment**: Development-only token system

## Potential Constraints & Considerations

### Technical Constraints
1. **Docker Dependency**: All services require Docker environment
2. **Network Host**: Docker builds require `--network host` for dependencies
3. **Memory Requirements**: ML services require adequate system memory
4. **Python Version**: Strictly Python 3.12 compatibility

### Development Constraints
1. **Root Directory**: Strict file organization rules
2. **Migration Discipline**: Database changes require immediate migration
3. **Package Refresh**: Container dependency isolation requires refresh commands
4. **ID Type Consistency**: Snowflake ID system throughout

### Integration Considerations
1. **Microservices Communication**: Service-to-service authentication
2. **State Management**: Frontend state sync with backend changes
3. **Error Handling**: Consistent error code system across all layers
4. **Internationalization**: Full localization support for user-facing content

## Recommended Development Approach

### For New Features
1. **Backend-First**: Design API schema and models first
2. **Schema Sync**: Update frontend types immediately after backend changes
3. **Test-Driven**: Write tests alongside feature development
4. **Documentation**: Update API documentation and user guides

### For Bug Fixes
1. **Root Cause Analysis**: Use systematic debugging approach
2. **Test Coverage**: Ensure fixes include regression test coverage
3. **Impact Assessment**: Consider downstream effects of changes
4. **Validation**: Full integration testing before deployment

### For Architecture Changes
1. **PRP Process**: Follow Project Requirements and Process workflow
2. **Migration Planning**: Comprehensive database and API migration strategy
3. **Backward Compatibility**: Maintain API compatibility when possible
4. **Performance Impact**: Assess and mitigate performance implications

This repository represents a mature, production-ready intelligent talent assessment platform with comprehensive development standards, extensive documentation, and robust quality assurance processes.