# Repository Context Report: TalentForge Pro

## Project Overview

**TalentForge Pro** is an intelligent talent assessment and job matching system that leverages hybrid AI architecture (rule-based engine + LLM) to provide comprehensive candidate evaluation and position matching capabilities.

### Core Purpose
The system evaluates candidates across five dimensions:
- Digital Literacy (20%)
- Industry Skills (25%) 
- Position Skills (30%)
- Innovation Capability (15%)
- Learning Potential (10%)

## Technology Stack Analysis

### Backend Architecture
- **Framework**: FastAPI 0.110+ (Python 3.12)
- **Database**: PostgreSQL 17 + pgvector 0.8.0 for vector storage
- **ORM**: SQLAlchemy 2.0 with async support
- **Task Queue**: Celery with Redis broker
- **Authentication**: JWT with RBAC (Role-Based Access Control)
- **API Design**: RESTful with unified pagination and error handling

### Frontend Architecture  
- **Framework**: Next.js 15.4.1 + React 19
- **Language**: TypeScript 5.x with strict mode
- **State Management**: Redux Toolkit + TanStack Query
- **UI Components**: Radix UI primitives with shadcn/ui
- **Styling**: Tailwind CSS 3.4.1
- **Internationalization**: next-intl with JSON translation files

### Infrastructure & Services
- **Containerization**: Docker with docker-compose
- **Reverse Proxy**: Nginx with unified proxy architecture
- **Object Storage**: MinIO (S3-compatible)
- **Cache**: Redis 7.4.4
- **Monitoring**: Prometheus + Grafana (production)
- **Vector Database**: pgvector within PostgreSQL
- **Package Management**: Poetry (backend), pnpm (frontend)

### AI & ML Services
- **LLM Providers**: Multi-provider support (DeepSeek, Moonshot, OpenRouter, Qwen, Ollama)
- **Embedding Models**: BGE-M3 (1024-dim), OpenAI embeddings (1536-dim)
- **Local LLM**: Ollama 0.11.4 with GPU support
- **ML Models**: BERT+BiLSTM for matching, MLP for evaluation
- **Vector Operations**: HNSW indexing with similarity thresholds

## Project Structure

```
talent_forge_pro/
├── app/                    # Main application directory
│   ├── backend/           # FastAPI backend service
│   │   ├── app/          # Application code
│   │   │   ├── api/      # API endpoints (v1 structure)
│   │   │   ├── core/     # Core configuration and utilities  
│   │   │   ├── crud/     # Database operations
│   │   │   ├── models/   # SQLAlchemy models
│   │   │   ├── schemas/  # Pydantic schemas
│   │   │   ├── services/ # Business logic services
│   │   │   └── utils/    # Utility functions
│   │   ├── tests/        # Comprehensive test suite
│   │   └── scripts/      # Database and utility scripts
│   ├── frontend/         # Next.js frontend application
│   │   ├── app/          # Next.js 13+ app directory
│   │   ├── components/   # React components
│   │   ├── hooks/        # Custom React hooks
│   │   ├── services/     # API service layers
│   │   ├── store/        # Redux store configuration
│   │   └── types/        # TypeScript type definitions
│   ├── configs/          # Configuration files (nginx, prometheus)
│   └── scripts/          # Development and deployment scripts
├── docs/                 # Project documentation
├── examples/             # Code examples and patterns
└── archive/              # Archived legacy files
```

## Code Organization Patterns

### Backend Architecture Layers
1. **API Layer** (`app/api/`): FastAPI routers with dependency injection
2. **Service Layer** (`app/services/`): Business logic and orchestration
3. **CRUD Layer** (`app/crud/`): Database operations with async patterns
4. **Model Layer** (`app/models/`): SQLAlchemy models with relationships

### Frontend Architecture Patterns
1. **Component Layer**: Functional components with hooks
2. **Service Layer**: API clients with axios and response interceptors
3. **State Layer**: Redux slices with TanStack Query integration
4. **Type Layer**: Comprehensive TypeScript interfaces

## Development Workflow & Standards

### Code Quality Standards
- **Backend**: Ruff linting, Black formatting, MyPy type checking, pytest >80% coverage
- **Frontend**: ESLint + Prettier, TypeScript strict mode, Jest + React Testing Library
- **API Design**: OpenAPI/Swagger documentation, consistent error codes
- **Database**: Alembic migrations, pgvector indexing strategies

### Git Workflow
- **Branch Strategy**: `feature/*`, `bugfix/*`, `hotfix/*`
- **Commit Convention**: `feat:`, `fix:`, `docs:`, `style:`, `refactor:`
- **Quality Gates**: Pre-commit hooks, CI/CD validation, code review requirements

### Development Environment
- **Quick Start**: `make setup && make up` (unified Docker environment)
- **Access Points**: 
  - Application: http://localhost:8088
  - API Documentation: http://localhost:8088/docs
  - Development Token: `dev_bypass_token_2025_talentforge`
- **Hot Reload**: Backend and frontend support development mode changes

## API Architecture

### Current API Structure
```
/api/v1/
├── auth/                 # Authentication endpoints
├── users/                # User management
├── candidates/           # Candidate operations
├── positions/            # Job position management
├── resume/               # Resume processing (Sprint 4)
├── matching/             # Intelligent matching algorithms
├── assessment/           # Capability assessment
├── questionnaires/       # Dynamic questionnaire system
├── ai/                   # AI-powered questionnaire generation
├── analytics/            # Analytics and reporting
├── admin/                # Administrative functions
└── public/               # Public endpoints (no auth)
```

### Authentication System
- **JWT Tokens**: Access (60min) + Refresh (7 days)
- **Development Mode**: Bypass token for testing
- **RBAC**: Role-based permissions with granular controls
- **Security**: bcrypt passwords, HTTPS enforcement

## Data Models & Storage

### Core Entities
- **User**: Authentication and preferences
- **Candidate**: Candidate profiles with skills and experience
- **Position**: Job positions with requirements
- **Resume**: File management with version control
- **Assessment**: Capability evaluation results
- **Questionnaire**: Dynamic question generation

### Vector Storage Strategy
- **Primary**: pgvector within PostgreSQL for unified storage
- **Embeddings**: BGE-M3 (1024-dim) for Chinese content, OpenAI (1536-dim) backup
- **Indexing**: HNSW with optimized parameters (M=16, ef_construction=64)
- **Similarity**: Cosine similarity with 0.7 threshold

## Key Development Constraints

### Architecture Rules (MANDATORY)
1. **Root Directory**: Only 12 allowed files, no test/config files in root
2. **App Structure**: All application code in `app/` directory
3. **Docker Context**: All docker-compose operations from `app/` directory  
4. **File Organization**: Scripts in `app/scripts/`, docs in `docs/`
5. **ID System**: Snowflake IDs (64-bit integers) throughout system

### Development Standards (CRITICAL)  
1. **Backend-Frontend Sync**: Schema changes require immediate frontend type updates
2. **API Consistency**: All endpoints use trailing slashes, unified error codes
3. **Package Management**: Container-isolated dependencies with refresh commands
4. **Internationalization**: All user-facing text must be translatable
5. **Model Validation**: Comprehensive startup validation with health checks

## Integration Points for New Features

### Current Sprint Status
- **Sprint 1-2**: ✅ Infrastructure and authentication completed
- **Sprint 3**: 🔄 Candidate management in progress  
- **Sprint 4**: 📅 ML service integration planned
- **Recent Work**: AI questionnaire generation with i18n support

### Extension Areas
1. **Resume Processing**: OCR integration point identified
2. **Vector Operations**: Embedding service with multi-provider support
3. **Assessment Engine**: Capability evaluation with ML models
4. **Batch Processing**: Async task processing infrastructure
5. **Analytics**: Comprehensive reporting and insights

### Testing Infrastructure
- **Unit Tests**: pytest with async support, >80% coverage target
- **Integration Tests**: API contract validation with Tavern
- **E2E Tests**: Playwright for browser automation
- **Load Testing**: Performance validation for production readiness

## Deployment & Operations

### Environment Support
- **Development**: Hot reload, exposed ports, comprehensive logging
- **Production**: Security hardened, monitoring enabled, optimized builds
- **Docker Services**: Unified proxy via port 8088, service-specific ports disabled

### Monitoring & Health
- **Health Checks**: Comprehensive service validation at startup
- **Metrics**: Prometheus integration with custom metrics
- **Logging**: Structured JSON logging with correlation IDs
- **Alerting**: Production monitoring with Grafana dashboards

## Security Considerations

### Current Security Measures
- **Input Validation**: Pydantic schemas with comprehensive validation
- **Authentication**: JWT with secure token management
- **Authorization**: RBAC with granular permission controls  
- **Data Protection**: bcrypt password hashing, HTTPS enforcement
- **CORS**: Configured origins with credential support

### Potential Security Integration Points
- **File Upload Security**: Resume processing with validation
- **API Rate Limiting**: slowapi integration for abuse prevention
- **Audit Logging**: Comprehensive action logging for compliance

## Recommendations for PaddleOCR Integration

### Integration Strategy
1. **Service Layer**: Add OCR service to existing AI service manager
2. **Model Consistency**: Align with existing Snowflake ID system
3. **Async Processing**: Leverage existing Celery task queue
4. **Storage Integration**: Use existing MinIO for file management
5. **Vector Integration**: Connect OCR results to embedding pipeline

### Architecture Alignment
- Follow existing multi-provider AI service pattern
- Use established health check and monitoring patterns
- Maintain consistent error handling and logging
- Integrate with existing user preference and i18n systems

This comprehensive analysis provides the foundation for implementing PaddleOCR integration while maintaining consistency with the existing TalentForge Pro architecture and development standards.