# PaddleOCR Backend Integration - Technical Design Document

## 1. Architecture Overview

### Integration Strategy: Direct Backend Embedding

```
┌─────────────────────────────────────────────────────────────┐
│                    TalentForge Pro Backend                   │
├─────────────────────────────────────────────────────────────┤
│  FastAPI Application                                        │
│  ┌─────────────────────────────────────────────────────────┤
│  │ Celery Task: parse_resume_async                         │
│  │                                                         │
│  │  1. File Retrieval (MinIO)                            │
│  │         ↓                                              │
│  │  2. File Type Detection                                │
│  │         ↓                                              │
│  │  3. OCR Processing (NEW)                               │
│  │     ├─ PaddleOCR Service ← GPU/CPU                     │
│  │     ├─ Text Extraction                                 │
│  │     └─ Confidence Validation                           │
│  │         ↓                                              │
│  │  4. LLM Processing (Existing)                          │
│  │         ↓                                              │
│  │  5. Data Storage & Embedding                           │
│  └─────────────────────────────────────────────────────────┤
│                                                             │
│  Dependencies: paddlepaddle, paddleocr                     │
└─────────────────────────────────────────────────────────────┘
```

### Design Principles
- **Zero Disruption**: Existing functionality remains unchanged
- **Graceful Degradation**: OCR failures don't break pipeline
- **Resource Efficiency**: GPU-first, CPU fallback
- **Configuration Driven**: Environment-based settings

## 2. Component Design

### 2.1 OCR Service Module

**File**: `app/backend/app/services/ocr_service.py`

```python
from typing import Optional, Dict, Any, Tuple
import logging
from pathlib import Path

class OCRService:
    """PaddleOCR integration service with GPU/CPU fallback"""
    
    def __init__(self):
        self.ocr_engine = None
        self.use_gpu = True
        self.confidence_threshold = 0.3
        self._initialize_engine()
    
    def _initialize_engine(self) -> None:
        """Initialize PaddleOCR with GPU/CPU detection"""
        
    def extract_text(self, file_path: str, file_type: str) -> Dict[str, Any]:
        """
        Extract text from image/PDF file
        
        Returns:
        {
            "text": str,
            "confidence": float,
            "success": bool,
            "method": str,  # "ocr_gpu", "ocr_cpu", "fallback"
            "processing_time": float
        }
        """
        
    def _should_use_ocr(self, file_path: str, file_type: str) -> bool:
        """Determine if OCR is needed for this file"""
        
    def _extract_with_ocr(self, file_path: str) -> Tuple[str, float]:
        """Core OCR processing with confidence scoring"""
        
    def _fallback_text_extraction(self, file_path: str) -> str:
        """Fallback to regular text extraction for PDFs"""
```

### 2.2 Task Integration Points

**Modified**: `app/backend/app/tasks.py`

```python
@celery_app.task(bind=True, max_retries=3, default_retry_delay=60)
def parse_resume_async(
    self,
    file_content_base64: str,
    filename: str,
    user_id: int,
    candidate_id: Optional[int] = None,
    generate_embeddings: bool = True,
    embedding_provider: Optional[str] = None,
    enable_ocr: bool = True  # NEW PARAMETER
):
    """Enhanced resume parsing with OCR support"""
    
    # Existing preprocessing...
    
    # NEW: OCR Processing Step
    if enable_ocr and _requires_ocr(temp_file_path, file_type):
        ocr_result = ocr_service.extract_text(temp_file_path, file_type)
        if ocr_result["success"]:
            extracted_text = ocr_result["text"]
            processing_metadata["ocr_used"] = True
            processing_metadata["ocr_confidence"] = ocr_result["confidence"]
            processing_metadata["ocr_method"] = ocr_result["method"]
        else:
            # Fallback to existing extraction
            extracted_text = existing_text_extraction(temp_file_path)
            processing_metadata["ocr_used"] = False
            processing_metadata["ocr_fallback"] = True
    
    # Continue with existing LLM processing...
```

### 2.3 Configuration Management

**File**: `app/backend/app/core/config.py`

```python
class Settings(BaseSettings):
    # Existing settings...
    
    # OCR Configuration
    OCR_ENABLED: bool = True
    OCR_USE_GPU: bool = True
    OCR_CONFIDENCE_THRESHOLD: float = 0.3
    OCR_MAX_PROCESSING_TIME: int = 120  # seconds
    OCR_SUPPORTED_LANGUAGES: str = "ch,en"  # Chinese, English
    OCR_CPU_THREAD_NUM: int = 2
    
    # Resource Limits
    OCR_MAX_IMAGE_SIZE: int = 10 * 1024 * 1024  # 10MB
    OCR_TIMEOUT_SECONDS: int = 60
```

## 3. File Processing Logic

### 3.1 File Type Detection

```python
def _requires_ocr(file_path: str, file_type: str) -> bool:
    """
    Determine if file requires OCR processing
    
    Logic:
    - PDF: Check if text-extractable, if not -> OCR
    - Images (PNG, JPG, JPEG): Always OCR
    - Other formats: Skip OCR
    """
    
    if file_type.lower() == 'pdf':
        # Quick text extraction test
        try:
            with open(file_path, 'rb') as f:
                pdf_text = extract_text_from_pdf(f)
                return len(pdf_text.strip()) < 100  # Likely scanned
        except:
            return True  # Extraction failed, try OCR
    
    elif file_type.lower() in ['png', 'jpg', 'jpeg']:
        return True
    
    return False
```

### 3.2 OCR Processing Pipeline

```python
async def _process_with_ocr(file_path: str) -> Dict[str, Any]:
    """
    OCR processing pipeline with error handling
    
    Steps:
    1. File validation
    2. Image preprocessing (if needed)
    3. OCR execution (GPU/CPU)
    4. Result validation
    5. Confidence scoring
    """
    
    start_time = time.time()
    result = {
        "text": "",
        "confidence": 0.0,
        "success": False,
        "method": "unknown",
        "processing_time": 0.0,
        "error": None
    }
    
    try:
        # Step 1: Validate file
        if not _validate_file(file_path):
            result["error"] = "File validation failed"
            return result
        
        # Step 2: PaddleOCR processing
        ocr_result = self.ocr_engine.ocr(file_path, use_angle_cls=True, use_gpu=self.use_gpu)
        
        # Step 3: Parse results
        extracted_text, avg_confidence = _parse_ocr_results(ocr_result)
        
        # Step 4: Validate results
        if avg_confidence >= self.confidence_threshold:
            result.update({
                "text": extracted_text,
                "confidence": avg_confidence,
                "success": True,
                "method": "ocr_gpu" if self.use_gpu else "ocr_cpu"
            })
        else:
            result["error"] = f"Low confidence: {avg_confidence}"
            
    except Exception as e:
        logger.error(f"OCR processing failed: {e}")
        result["error"] = str(e)
        
        # CPU fallback if GPU failed
        if self.use_gpu:
            logger.info("Retrying OCR with CPU...")
            return await self._process_with_cpu_fallback(file_path)
    
    finally:
        result["processing_time"] = time.time() - start_time
    
    return result
```

## 4. Dependencies & Environment

### 4.1 Python Dependencies

**File**: `app/backend/pyproject.toml` (additions)

```toml
[tool.poetry.dependencies]
# Existing dependencies...

# OCR Processing
paddlepaddle = "^2.5.2"
paddleocr = "^2.7.0"
opencv-python = "^4.8.0"
Pillow = "^10.0.0"

[tool.poetry.group.dev.dependencies]
# For OCR testing
pytest-mock = "^3.11.1"
```

### 4.2 Docker Configuration

**File**: `app/docker-compose.yml` (modifications)

```yaml
services:
  backend:
    # Existing configuration...
    environment:
      # Existing env vars...
      
      # OCR Configuration
      OCR_ENABLED: "true"
      OCR_USE_GPU: "true" 
      OCR_CONFIDENCE_THRESHOLD: "0.3"
      OCR_SUPPORTED_LANGUAGES: "ch,en"
    
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]  # Enable GPU access
    
    # Additional volume for OCR models (optional caching)
    volumes:
      - ./backend:/app
      - paddle_models:/root/.paddleocr  # Model cache
      
volumes:
  paddle_models:  # Persist downloaded models
```

### 4.3 System Requirements

```dockerfile
# Additional system packages in Dockerfile
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && rm -rf /var/lib/apt/lists/*
```

## 5. Error Handling & Fallback Strategy

### 5.1 Error Categories

```python
class OCRError(Exception):
    """Base OCR exception"""
    pass

class OCRTimeoutError(OCRError):
    """OCR processing timeout"""
    pass

class OCRResourceError(OCRError):
    """Insufficient resources for OCR"""
    pass

class OCRQualityError(OCRError):
    """OCR quality below threshold"""
    pass
```

### 5.2 Fallback Chain

```
GPU OCR ──✗──→ CPU OCR ──✗──→ Traditional Text Extraction ──✗──→ Return Original File
    │              │                        │                           │
    ├─ Timeout     ├─ Low Confidence       ├─ Extraction Error         └─ Log & Continue
    ├─ CUDA Error  ├─ Processing Error     └─ Unsupported Format
    └─ Memory      └─ File Corruption
```

### 5.3 Monitoring & Logging

```python
# OCR metrics collection
ocr_metrics = {
    "total_requests": 0,
    "successful_extractions": 0,
    "gpu_usage": 0,
    "cpu_fallbacks": 0,
    "failures": 0,
    "avg_processing_time": 0.0,
    "avg_confidence": 0.0
}

# Structured logging
logger.info("OCR processing completed", extra={
    "file_type": file_type,
    "processing_time": processing_time,
    "confidence": confidence,
    "method": method,
    "success": success,
    "file_size": file_size
})
```

## 6. Performance Optimization

### 6.1 Model Loading Strategy

```python
class OCRService:
    _instance = None
    _ocr_engine = None
    
    def __new__(cls):
        """Singleton pattern for model reuse"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def _lazy_load_engine(self):
        """Load OCR engine only when needed"""
        if self._ocr_engine is None:
            self._ocr_engine = PaddleOCR(
                use_angle_cls=True,
                lang=self.supported_languages,
                use_gpu=self.use_gpu,
                show_log=False
            )
```

### 6.2 Processing Optimizations

```python
def _optimize_image_for_ocr(image_path: str) -> str:
    """
    Preprocess image for better OCR accuracy
    
    Optimizations:
    - Resize if too large (>2048px)
    - Convert to grayscale
    - Adjust contrast/brightness
    - Noise reduction
    """
    
    try:
        from PIL import Image, ImageEnhance
        import cv2
        
        # Load and assess image
        img = cv2.imread(image_path)
        height, width = img.shape[:2]
        
        # Resize if necessary
        if max(height, width) > 2048:
            scale = 2048 / max(height, width)
            new_width = int(width * scale)
            new_height = int(height * scale)
            img = cv2.resize(img, (new_width, new_height))
        
        # Convert to grayscale
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
        
        # Apply optimizations
        enhanced = cv2.medianBlur(gray, 3)  # Noise reduction
        
        # Save optimized image
        optimized_path = f"{image_path}_optimized.jpg"
        cv2.imwrite(optimized_path, enhanced)
        
        return optimized_path
        
    except Exception as e:
        logger.warning(f"Image optimization failed: {e}")
        return image_path  # Return original
```

## 7. Testing Strategy

### 7.1 Unit Tests

```python
# tests/test_ocr_service.py
import pytest
from app.services.ocr_service import OCRService

class TestOCRService:
    @pytest.fixture
    def ocr_service(self):
        return OCRService()
    
    def test_pdf_ocr_processing(self, ocr_service):
        """Test OCR on scanned PDF"""
        
    def test_image_ocr_processing(self, ocr_service):
        """Test OCR on image files"""
        
    def test_gpu_cpu_fallback(self, ocr_service):
        """Test GPU to CPU fallback"""
        
    def test_low_confidence_handling(self, ocr_service):
        """Test handling of low confidence results"""
```

### 7.2 Integration Tests

```python
# tests/test_resume_parsing_with_ocr.py
def test_parse_resume_with_ocr_enabled():
    """Test full pipeline with OCR enabled"""
    
def test_parse_resume_ocr_fallback():
    """Test resume parsing when OCR fails"""
```

## 8. Deployment Considerations

### 8.1 Model Download Strategy

```python
def ensure_ocr_models():
    """Download OCR models during container startup"""
    try:
        # PaddleOCR will auto-download models on first use
        # We can warm up the models during startup
        dummy_ocr = PaddleOCR(use_angle_cls=True, lang='en', show_log=False)
        logger.info("OCR models initialized successfully")
    except Exception as e:
        logger.error(f"OCR model initialization failed: {e}")
```

### 8.2 Resource Monitoring

```python
# Add to health check endpoint
@app.get("/health/ocr")
async def ocr_health_check():
    """OCR service health check"""
    try:
        # Quick OCR test with small image
        test_result = ocr_service.test_connection()
        return {
            "status": "healthy" if test_result else "degraded",
            "gpu_available": torch.cuda.is_available(),
            "model_loaded": ocr_service.is_loaded(),
            "last_used": ocr_service.last_used_time
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e)
        }
```

---

## Summary

This technical design provides a robust PaddleOCR integration that:

✅ **Maintains existing functionality** while adding OCR capabilities  
✅ **Handles resource constraints** with GPU/CPU fallback  
✅ **Provides graceful degradation** on failures  
✅ **Integrates seamlessly** with current Celery task system  
✅ **Supports comprehensive monitoring** and error handling  

The implementation follows established patterns in the TalentForge Pro codebase and leverages existing infrastructure optimally.