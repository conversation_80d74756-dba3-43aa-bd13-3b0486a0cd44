# TalentForge Pro Frontend Critical Fixes - Technical Specification

## Problem Statement

### Business Issue
The TalentForge Pro frontend lacks critical functionality for AI questionnaire generation, evaluation report visualization, batch resume processing, and has type inconsistencies that block basic operations.

### Current State
- Backend AI questionnaire APIs exist but no frontend interface
- Type mismatches between frontend (string IDs) and backend (Snowflake BigInt IDs) causing 422 errors
- Missing evaluation report visualization components
- No batch resume processing capabilities
- Incomplete user management interface

### Expected Outcome
- Fully functional AI questionnaire generation and management interface
- Type-safe frontend-backend communication with consistent ID handling
- Comprehensive evaluation report visualization with charts and PDF export
- Batch resume upload with progress tracking
- Complete user management system with CRUD operations

## Solution Overview

### Approach
Implement a systematic frontend infrastructure with proper type definitions, service layer abstraction, and component libraries following Next.js 15 and React 19 patterns with shadcn/ui components.

### Core Changes
1. **Type System Unification**: Standardize ID types across frontend-backend boundary
2. **Service Layer Implementation**: Create abstraction for API communication
3. **Component Architecture**: Build reusable UI components for complex operations
4. **State Management**: Implement React Query patterns for data fetching and caching

### Success Criteria
- All API endpoints accessible through type-safe frontend interfaces
- Real-time progress tracking for long-running operations
- Comprehensive error handling with internationalized messages
- Responsive design with accessibility compliance

## Technical Implementation

### Frontend Directory Structure
```
app/frontend/
├── app/                                    # Next.js app router
│   ├── (dashboard)/                        # Protected dashboard routes
│   │   ├── ai-questionnaire/               # AI questionnaire management
│   │   │   ├── generate/                   # Generation interface
│   │   │   │   ├── page.tsx                # Generation form page
│   │   │   │   └── components/             # Generation-specific components
│   │   │   ├── [id]/                       # Questionnaire detail
│   │   │   │   ├── page.tsx                # Questionnaire view/edit
│   │   │   │   ├── evaluate/               # Evaluation interface
│   │   │   │   │   └── page.tsx            # Evaluation form
│   │   │   │   └── components/             # Detail-specific components
│   │   │   └── page.tsx                    # Questionnaire list
│   │   ├── candidates/                     # Candidate management
│   │   │   ├── [id]/                       # Candidate detail
│   │   │   │   ├── page.tsx                # Candidate profile
│   │   │   │   ├── evaluation/             # Evaluation reports
│   │   │   │   │   └── page.tsx            # Evaluation list/view
│   │   │   │   └── components/             # Candidate-specific components
│   │   │   ├── bulk-upload/                # Batch resume processing
│   │   │   │   ├── page.tsx                # Upload interface
│   │   │   │   └── components/             # Upload-specific components
│   │   │   └── page.tsx                    # Candidate list
│   │   ├── users/                          # User management
│   │   │   ├── [id]/                       # User detail
│   │   │   │   └── page.tsx                # User profile/edit
│   │   │   ├── create/                     # User creation
│   │   │   │   └── page.tsx                # User creation form
│   │   │   └── page.tsx                    # User list
│   │   └── layout.tsx                      # Dashboard layout
│   ├── auth/                               # Authentication pages
│   │   ├── login/page.tsx                  # Login form
│   │   └── layout.tsx                      # Auth layout
│   ├── globals.css                         # Global styles
│   ├── layout.tsx                          # Root layout
│   └── page.tsx                            # Landing page
├── components/                             # Reusable UI components
│   ├── ui/                                 # shadcn/ui components
│   ├── common/                             # Common components
│   │   ├── Header.tsx                      # Navigation header
│   │   ├── Sidebar.tsx                     # Dashboard sidebar
│   │   ├── LanguageSwitcher.tsx            # I18n language selector
│   │   ├── ProgressTracker.tsx             # Generic progress component
│   │   └── ErrorBoundary.tsx               # Error handling
│   ├── forms/                              # Form components
│   │   ├── QuestionnaireGenerationForm.tsx # AI generation form
│   │   ├── EvaluationForm.tsx              # Evaluation form
│   │   ├── UserForm.tsx                    # User create/edit form
│   │   └── BulkUploadForm.tsx              # Batch upload form
│   ├── charts/                             # Data visualization
│   │   ├── RadarChart.tsx                  # Skills radar chart
│   │   ├── BarChart.tsx                    # Score comparison chart
│   │   └── ProgressChart.tsx               # Progress visualization
│   ├── evaluation/                         # Evaluation components
│   │   ├── EvaluationReport.tsx            # Main report component
│   │   ├── ScoreSummary.tsx                # Score overview
│   │   ├── CandidateComparison.tsx         # Multi-candidate comparison
│   │   └── ReportExport.tsx                # PDF export functionality
│   └── tables/                             # Data table components
│       ├── CandidateTable.tsx              # Candidate list table
│       ├── UserTable.tsx                   # User list table
│       └── QuestionnaireTable.tsx          # Questionnaire list table
├── services/                               # API service layer
│   ├── api/                                # API client configuration
│   │   ├── client.ts                       # Axios configuration
│   │   ├── interceptors.ts                 # Request/response interceptors
│   │   └── types.ts                        # API response types
│   ├── aiQuestionnaireService.ts           # AI questionnaire operations
│   ├── evaluationService.ts                # Evaluation operations
│   ├── candidateService.ts                 # Candidate operations
│   ├── userService.ts                      # User operations
│   ├── uploadService.ts                    # File upload operations
│   └── progressService.ts                  # Progress tracking
├── types/                                  # TypeScript type definitions
│   ├── index.ts                            # Main type exports
│   ├── api.ts                              # API-related types
│   ├── auth.ts                             # Authentication types
│   ├── questionnaire.ts                    # Questionnaire types
│   ├── evaluation.ts                       # Evaluation types
│   ├── candidate.ts                        # Candidate types
│   └── user.ts                             # User types
├── hooks/                                  # Custom React hooks
│   ├── useAuth.ts                          # Authentication hook
│   ├── useProgress.ts                      # Progress tracking hook
│   ├── useWebSocket.ts                     # WebSocket connection hook
│   └── useInternationalization.ts         # I18n hook
├── utils/                                  # Utility functions
│   ├── auth.ts                             # Authentication utilities
│   ├── formatting.ts                       # Data formatting
│   ├── validation.ts                       # Form validation
│   └── constants.ts                        # Application constants
├── i18n/                                   # Internationalization
│   ├── client.ts                           # Client-side i18n
│   ├── server.ts                           # Server-side i18n
│   └── config.ts                           # I18n configuration
├── messages/                               # Translation files
│   ├── en.json                             # English translations
│   └── zh.json                             # Chinese translations
├── lib/                                    # External library configurations
│   ├── auth.ts                             # NextAuth configuration
│   ├── query-client.ts                     # React Query configuration
│   └── utils.ts                            # Utility functions
└── middleware.ts                           # Next.js middleware
```

### Database Changes
No database migrations required - leveraging existing backend schema with proper type mapping.

### Code Changes

#### 1. Type System Unification (Critical Fix)

**File: `app/frontend/types/index.ts`**
```typescript
// Unified ID type system matching backend Snowflake IDs
export type SnowflakeID = string; // Always string in frontend (JSON serialization)

// User types
export interface User {
  id: SnowflakeID;
  email: string;
  username: string;
  full_name?: string;
  phone?: string;
  is_active: boolean;
  role: UserRole;
  department?: string;
  position?: string;
  is_verified: boolean;
  avatar_url?: string;
  created_at: string; // ISO string
  updated_at: string;
  last_login_at?: string;
  locked_until?: string;
  permissions: string[];
}

// Candidate types
export interface Candidate {
  id: SnowflakeID;
  name: string;
  email?: string;
  phone?: string;
  gender?: string;
  birth_date?: string;
  current_position?: string;
  current_company?: string;
  years_of_experience: number;
  current_salary?: number;
  expected_salary?: number;
  education: Education[];
  work_experience: WorkExperience[];
  skills: string[];
  status: CandidateStatus;
  data_permission: DataPermission;
  shared_with: SnowflakeID[];
  source?: string;
  referrer_id?: SnowflakeID;
  notes?: string;
  tags: string[];
  resume_url?: string;
  resume_text?: string;
  resume_parsed_at?: string;
  dci_score?: number;
  jfs_score?: number;
  assessment_data?: Record<string, any>;
  created_by: SnowflakeID;
  created_at: string;
  updated_at: string;
  is_deleted: boolean;
  deleted_at?: string;
}

// Questionnaire types
export interface Questionnaire {
  id: SnowflakeID;
  title: string;
  description?: string;
  category: string;
  status: QuestionnaireStatus;
  position_type: string;
  industry: string;
  dimensions: string[];
  evaluation_criteria?: Record<string, any>;
  ai_generated: boolean;
  created_by: SnowflakeID;
  created_at: string;
  updated_at: string;
  question_count: number;
  scoring_enabled: boolean;
  passing_score?: number;
  slug?: string;
  valid_until?: string;
  access_type?: string;
  max_submissions?: number;
}

// Evaluation Report types
export interface EvaluationReport {
  id: SnowflakeID;
  questionnaire_id: SnowflakeID;
  candidate_id?: SnowflakeID;
  submission_id?: SnowflakeID;
  dimension_scores?: Record<string, number>;
  total_score?: number;
  overall_evaluation?: string;
  strengths?: string[];
  weaknesses?: string[];
  recommendations?: string;
  key_insights?: string;
  is_qualified?: boolean;
  match_score?: number;
  risk_level?: string;
  percentile_rank?: number;
  evaluated_at?: string;
  created_at?: string;
  updated_at?: string;
}

// API response types
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  skip: number;
  limit: number;
  has_next: boolean;
  has_prev: boolean;
  current_page: number;
  total_pages: number;
}

// Enums
export enum UserRole {
  HR_SPECIALIST = "HR_SPECIALIST",
  HIRING_MANAGER = "HIRING_MANAGER",
  ADMIN = "ADMIN"
}

export enum CandidateStatus {
  NEW = "NEW",
  SCREENING = "SCREENING",
  INTERVIEWING = "INTERVIEWING",
  PENDING_DECISION = "PENDING_DECISION",
  HIRED = "HIRED",
  REJECTED = "REJECTED",
  WITHDRAWN = "WITHDRAWN"
}

export enum DataPermission {
  PRIVATE = "PRIVATE",
  SHARED = "SHARED",
  TEAM = "TEAM",
  PUBLIC = "PUBLIC"
}

export enum QuestionnaireStatus {
  DRAFT = "DRAFT",
  REVIEW = "REVIEW",
  APPROVED = "APPROVED",
  PUBLISHED = "PUBLISHED",
  ARCHIVED = "ARCHIVED",
  REJECTED = "REJECTED"
}
```

#### 2. API Client Configuration

**File: `app/frontend/services/api/client.ts`**
```typescript
import axios, { AxiosInstance, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';
import { toast } from 'sonner';
import Cookies from 'js-cookie';

interface ApiError {
  error_code?: string;
  detail: string;
  details?: Record<string, any>;
}

interface ApiResponse<T = any> {
  data?: T;
  error?: ApiError;
  message?: string;
}

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8088/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Token management
class TokenManager {
  private static ACCESS_TOKEN_KEY = 'access_token';
  private static REFRESH_TOKEN_KEY = 'refresh_token';

  static getAccessToken(): string | undefined {
    // Try localStorage first, then cookies for SSR compatibility
    if (typeof window !== 'undefined') {
      return localStorage.getItem(this.ACCESS_TOKEN_KEY) || Cookies.get(this.ACCESS_TOKEN_KEY);
    }
    return Cookies.get(this.ACCESS_TOKEN_KEY);
  }

  static getRefreshToken(): string | undefined {
    if (typeof window !== 'undefined') {
      return localStorage.getItem(this.REFRESH_TOKEN_KEY) || Cookies.get(this.REFRESH_TOKEN_KEY);
    }
    return Cookies.get(this.REFRESH_TOKEN_KEY);
  }

  static setTokens(accessToken: string, refreshToken: string): void {
    // Store in both localStorage and cookies
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.ACCESS_TOKEN_KEY, accessToken);
      localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    }
    // Cookies for SSR and middleware access
    Cookies.set(this.ACCESS_TOKEN_KEY, accessToken, { expires: 1, sameSite: 'lax' });
    Cookies.set(this.REFRESH_TOKEN_KEY, refreshToken, { expires: 7, sameSite: 'lax' });
  }

  static clearTokens(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.ACCESS_TOKEN_KEY);
      localStorage.removeItem(this.REFRESH_TOKEN_KEY);
    }
    Cookies.remove(this.ACCESS_TOKEN_KEY);
    Cookies.remove(this.REFRESH_TOKEN_KEY);
  }
}

// Request interceptor
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = TokenManager.getAccessToken();
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add development bypass token for testing
    if (process.env.NODE_ENV === 'development' && !token) {
      config.headers.Authorization = 'Bearer dev_bypass_token_2025_talentforge';
    }
    
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // Return data directly - apiClient response interceptor handles extraction
    return response.data;
  },
  async (error: AxiosError<ApiError>) => {
    const { response, config } = error;
    
    if (!response) {
      toast.error('Network connection failed');
      return Promise.reject(error);
    }

    const { status, data } = response;
    
    // Handle authentication errors
    if (status === 401 && config && !config.url?.includes('/auth/')) {
      TokenManager.clearTokens();
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/login';
      }
      return Promise.reject(error);
    }

    // Handle API errors with error codes
    if (data?.error_code) {
      // Let components handle error code translation
      const errorWithCode = new Error(data.detail || 'An error occurred');
      (errorWithCode as any).error_code = data.error_code;
      (errorWithCode as any).details = data.details;
      return Promise.reject(errorWithCode);
    }

    // Handle other HTTP errors
    const errorMessage = data?.detail || `Request failed with status ${status}`;
    toast.error(errorMessage);
    
    return Promise.reject(error);
  }
);

export { apiClient, TokenManager };
export type { ApiResponse, ApiError };
```

#### 3. AI Questionnaire Service

**File: `app/frontend/services/aiQuestionnaireService.ts`**
```typescript
import { apiClient } from './api/client';
import type { 
  Questionnaire, 
  EvaluationReport, 
  PaginatedResponse,
  SnowflakeID 
} from '../types';

export interface QuestionnaireGenerateRequest {
  position_type: string;
  dimensions?: string[];
  question_count: number;
  industry?: string;
  custom_requirements?: string;
  difficulty_level?: 'easy' | 'medium' | 'hard';
}

export interface QuestionnaireEvaluateRequest {
  candidate_id: SnowflakeID;
  submission_id?: SnowflakeID;
  responses: Record<string, any>;
}

export interface TemplateResponse {
  position_type: string;
  description: string;
  dimensions: string[];
  question_types: string[];
  focus_areas: string[];
}

export interface GenerationProgress {
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number; // 0-100
  message?: string;
  result?: Questionnaire;
  error?: string;
}

class AIQuestionnaireService {
  private baseUrl = '/ai-questionnaire';

  async generate(request: QuestionnaireGenerateRequest): Promise<Questionnaire> {
    const response = await apiClient.post<Questionnaire>(
      `${this.baseUrl}/generate`,
      request
    );
    return response;
  }

  async evaluate(
    questionnaireId: SnowflakeID, 
    request: QuestionnaireEvaluateRequest
  ): Promise<EvaluationReport> {
    const response = await apiClient.post<EvaluationReport>(
      `${this.baseUrl}/evaluate/${questionnaireId}`,
      request
    );
    return response;
  }

  async getTemplates(): Promise<TemplateResponse[]> {
    const response = await apiClient.get<TemplateResponse[]>(
      `${this.baseUrl}/templates`
    );
    return response;
  }

  async getProgress(generationId: string): Promise<GenerationProgress> {
    const response = await apiClient.get<GenerationProgress>(
      `${this.baseUrl}/progress/${generationId}`
    );
    return response;
  }

  async publishQuestionnaire(
    questionnaireId: SnowflakeID,
    options: {
      slug: string;
      valid_until?: string;
      access_type?: 'private' | 'public' | 'password';
      max_submissions?: number;
    }
  ): Promise<{
    id: SnowflakeID;
    slug: string;
    public_url: string;
    access_type: string;
    valid_until?: string;
    max_submissions?: number;
    status: string;
  }> {
    const response = await apiClient.post(
      `${this.baseUrl}/${questionnaireId}/publish`,
      options
    );
    return response;
  }

  async reviewQuestionnaire(
    questionnaireId: SnowflakeID,
    review: {
      approved: boolean;
      comments?: string;
      rejection_reason?: string;
    }
  ): Promise<{
    id: SnowflakeID;
    status: string;
    reviewed_by: SnowflakeID;
    review_comments?: string;
    rejection_reason?: string;
    approved_at?: string;
  }> {
    const response = await apiClient.post(
      `${this.baseUrl}/${questionnaireId}/review`,
      review
    );
    return response;
  }

  async compareCandidates(
    questionnaireId: SnowflakeID,
    candidateIds: SnowflakeID[]
  ): Promise<{
    questionnaire_id: SnowflakeID;
    candidates: Array<{
      candidate_id: SnowflakeID;
      total_score: number;
      dimension_scores: Record<string, number>;
      rank: number;
    }>;
    comparison_insights: string[];
    top_performers: SnowflakeID[];
  }> {
    const response = await apiClient.post(
      `${this.baseUrl}/compare-candidates`,
      {
        questionnaire_id: questionnaireId,
        candidate_ids: candidateIds
      }
    );
    return response;
  }
}

export const aiQuestionnaireService = new AIQuestionnaireService();
```

#### 4. AI Questionnaire Generation Form Component

**File: `app/frontend/components/forms/QuestionnaireGenerationForm.tsx`**
```typescript
'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { useTranslations } from '@/i18n/client';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Loader2, Sparkles, Settings, Target } from 'lucide-react';

import { aiQuestionnaireService, type QuestionnaireGenerateRequest } from '@/services/aiQuestionnaireService';
import { ProgressTracker } from '@/components/common/ProgressTracker';

const generationSchema = z.object({
  position_type: z.string().min(1, 'Position type is required'),
  question_count: z.number().min(5).max(50),
  industry: z.string().optional(),
  dimensions: z.array(z.string()).optional(),
  custom_requirements: z.string().optional(),
  difficulty_level: z.enum(['easy', 'medium', 'hard']).default('medium')
});

type GenerationFormData = z.infer<typeof generationSchema>;

export function QuestionnaireGenerationForm() {
  const t = useTranslations();
  const router = useRouter();
  const [generationId, setGenerationId] = useState<string | null>(null);
  
  const { data: templates, isLoading: loadingTemplates } = useQuery({
    queryKey: ['questionnaire-templates'],
    queryFn: () => aiQuestionnaireService.getTemplates(),
  });

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isSubmitting }
  } = useForm<GenerationFormData>({
    resolver: zodResolver(generationSchema),
    defaultValues: {
      question_count: 20,
      industry: '烟草',
      difficulty_level: 'medium',
      dimensions: []
    }
  });

  const selectedPositionType = watch('position_type');
  const selectedTemplate = templates?.find(t => t.position_type === selectedPositionType);

  const generateMutation = useMutation({
    mutationFn: (data: QuestionnaireGenerateRequest) => 
      aiQuestionnaireService.generate(data),
    onSuccess: (questionnaire) => {
      toast.success(t('aiQuestionnaire.generation.success'));
      router.push(`/dashboard/ai-questionnaire/${questionnaire.id}`);
    },
    onError: (error: any) => {
      const errorCode = error.error_code;
      if (errorCode) {
        toast.error(t(`errors.${errorCode}`));
      } else {
        toast.error(t('errors.genericError'));
      }
    }
  });

  const onSubmit = async (data: GenerationFormData) => {
    try {
      await generateMutation.mutateAsync(data);
    } catch (error) {
      console.error('Generation failed:', error);
    }
  };

  const handleTemplateSelect = (positionType: string) => {
    setValue('position_type', positionType);
    const template = templates?.find(t => t.position_type === positionType);
    if (template) {
      setValue('dimensions', template.dimensions);
    }
  };

  if (loadingTemplates) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">{t('common.loading')}</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <Sparkles className="h-6 w-6 text-blue-600" />
        <h1 className="text-2xl font-bold">{t('aiQuestionnaire.generation.title')}</h1>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Template Selection */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Target className="h-5 w-5" />
              <span>{t('aiQuestionnaire.templates.title')}</span>
            </CardTitle>
            <CardDescription>
              {t('aiQuestionnaire.templates.description')}
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            {templates?.map((template) => (
              <div
                key={template.position_type}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedPositionType === template.position_type
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleTemplateSelect(template.position_type)}
              >
                <h4 className="font-medium">{template.position_type}</h4>
                <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                <div className="flex flex-wrap gap-1 mt-2">
                  {template.dimensions.slice(0, 3).map((dim) => (
                    <span 
                      key={dim}
                      className="text-xs bg-gray-100 px-2 py-1 rounded"
                    >
                      {dim}
                    </span>
                  ))}
                  {template.dimensions.length > 3 && (
                    <span className="text-xs text-gray-500">
                      +{template.dimensions.length - 3}
                    </span>
                  )}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Generation Form */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>{t('aiQuestionnaire.generation.settings')}</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Position Type */}
              <div className="space-y-2">
                <Label htmlFor="position_type">
                  {t('aiQuestionnaire.generation.positionType')} *
                </Label>
                <Controller
                  name="position_type"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      placeholder={t('aiQuestionnaire.generation.positionTypePlaceholder')}
                      className={errors.position_type ? 'border-red-500' : ''}
                    />
                  )}
                />
                {errors.position_type && (
                  <p className="text-sm text-red-600">{errors.position_type.message}</p>
                )}
              </div>

              {/* Question Count */}
              <div className="space-y-2">
                <Label htmlFor="question_count">
                  {t('aiQuestionnaire.generation.questionCount')}
                </Label>
                <Controller
                  name="question_count"
                  control={control}
                  render={({ field }) => (
                    <Input
                      {...field}
                      type="number"
                      min="5"
                      max="50"
                      onChange={(e) => field.onChange(parseInt(e.target.value))}
                    />
                  )}
                />
              </div>

              {/* Industry */}
              <div className="space-y-2">
                <Label htmlFor="industry">
                  {t('aiQuestionnaire.generation.industry')}
                </Label>
                <Controller
                  name="industry"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectTrigger>
                        <SelectValue placeholder={t('aiQuestionnaire.generation.selectIndustry')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="烟草">{t('industries.tobacco')}</SelectItem>
                        <SelectItem value="互联网">{t('industries.internet')}</SelectItem>
                        <SelectItem value="金融">{t('industries.finance')}</SelectItem>
                        <SelectItem value="制造业">{t('industries.manufacturing')}</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>

              {/* Difficulty Level */}
              <div className="space-y-2">
                <Label htmlFor="difficulty_level">
                  {t('aiQuestionnaire.generation.difficulty')}
                </Label>
                <Controller
                  name="difficulty_level"
                  control={control}
                  render={({ field }) => (
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="easy">{t('difficulty.easy')}</SelectItem>
                        <SelectItem value="medium">{t('difficulty.medium')}</SelectItem>
                        <SelectItem value="hard">{t('difficulty.hard')}</SelectItem>
                      </SelectContent>
                    </Select>
                  )}
                />
              </div>

              {/* Evaluation Dimensions */}
              {selectedTemplate && (
                <div className="space-y-3">
                  <Label>{t('aiQuestionnaire.generation.dimensions')}</Label>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                    {selectedTemplate.dimensions.map((dimension) => (
                      <Controller
                        key={dimension}
                        name="dimensions"
                        control={control}
                        render={({ field }) => (
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id={dimension}
                              checked={field.value?.includes(dimension)}
                              onCheckedChange={(checked) => {
                                if (checked) {
                                  field.onChange([...(field.value || []), dimension]);
                                } else {
                                  field.onChange(
                                    field.value?.filter((d) => d !== dimension) || []
                                  );
                                }
                              }}
                            />
                            <Label
                              htmlFor={dimension}
                              className="text-sm font-normal cursor-pointer"
                            >
                              {dimension}
                            </Label>
                          </div>
                        )}
                      />
                    ))}
                  </div>
                </div>
              )}

              {/* Custom Requirements */}
              <div className="space-y-2">
                <Label htmlFor="custom_requirements">
                  {t('aiQuestionnaire.generation.customRequirements')}
                </Label>
                <Controller
                  name="custom_requirements"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      {...field}
                      placeholder={t('aiQuestionnaire.generation.customRequirementsPlaceholder')}
                      rows={3}
                    />
                  )}
                />
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-3">
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={() => router.back()}
                >
                  {t('common.cancel')}
                </Button>
                <Button 
                  type="submit" 
                  disabled={isSubmitting}
                  className="min-w-[120px]"
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {t('aiQuestionnaire.generation.generating')}
                    </>
                  ) : (
                    <>
                      <Sparkles className="mr-2 h-4 w-4" />
                      {t('aiQuestionnaire.generation.generate')}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
```

#### 5. Evaluation Report Component

**File: `app/frontend/components/evaluation/EvaluationReport.tsx`**
```typescript
'use client';

import React, { useState } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Download, 
  FileText, 
  TrendingUp, 
  TrendingDown, 
  Target,
  AlertTriangle,
  CheckCircle,
  XCircle
} from 'lucide-react';

import { RadarChart } from '@/components/charts/RadarChart';
import { ScoreSummary } from './ScoreSummary';
import { CandidateComparison } from './CandidateComparison';
import { useTranslations } from '@/i18n/client';
import type { EvaluationReport, SnowflakeID } from '@/types';

interface EvaluationReportProps {
  report: EvaluationReport;
  candidateId?: SnowflakeID;
  showComparison?: boolean;
  onExportPDF?: () => void;
}

export function EvaluationReport({ 
  report, 
  candidateId,
  showComparison = false,
  onExportPDF 
}: EvaluationReportProps) {
  const t = useTranslations();
  const [activeTab, setActiveTab] = useState<'overview' | 'details' | 'comparison'>('overview');

  const getRiskColor = (riskLevel?: string) => {
    switch (riskLevel?.toLowerCase()) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getQualificationIcon = (isQualified?: boolean) => {
    if (isQualified === true) {
      return <CheckCircle className="h-5 w-5 text-green-600" />;
    } else if (isQualified === false) {
      return <XCircle className="h-5 w-5 text-red-600" />;
    }
    return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
  };

  const getQualificationBadge = (isQualified?: boolean) => {
    if (isQualified === true) {
      return <Badge variant="default" className="bg-green-600">{t('evaluation.qualified')}</Badge>;
    } else if (isQualified === false) {
      return <Badge variant="destructive">{t('evaluation.notQualified')}</Badge>;
    }
    return <Badge variant="secondary">{t('evaluation.pending')}</Badge>;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          {getQualificationIcon(report.is_qualified)}
          <div>
            <h1 className="text-2xl font-bold">{t('evaluation.report.title')}</h1>
            <p className="text-gray-600">
              {t('evaluation.report.evaluatedAt', { 
                date: new Date(report.evaluated_at || '').toLocaleString() 
              })}
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          {getQualificationBadge(report.is_qualified)}
          {onExportPDF && (
            <Button onClick={onExportPDF} variant="outline">
              <Download className="mr-2 h-4 w-4" />
              {t('evaluation.report.exportPDF')}
            </Button>
          )}
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-4 border-b">
        {['overview', 'details', showComparison && 'comparison'].filter(Boolean).map((tab) => (
          <button
            key={tab}
            className={`pb-2 px-1 font-medium transition-colors ${
              activeTab === tab 
                ? 'border-b-2 border-blue-600 text-blue-600' 
                : 'text-gray-600 hover:text-gray-900'
            }`}
            onClick={() => setActiveTab(tab as any)}
          >
            {t(`evaluation.tabs.${tab}`)}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Score Summary */}
          <div className="lg:col-span-2 space-y-6">
            <ScoreSummary report={report} />
            
            {/* Dimension Scores */}
            {report.dimension_scores && (
              <Card>
                <CardHeader>
                  <CardTitle>{t('evaluation.dimensionScores.title')}</CardTitle>
                  <CardDescription>
                    {t('evaluation.dimensionScores.description')}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(report.dimension_scores).map(([dimension, score]) => (
                      <div key={dimension} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="font-medium">{dimension}</span>
                          <span className="text-2xl font-bold text-blue-600">
                            {score.toFixed(1)}
                          </span>
                        </div>
                        <Progress 
                          value={(score / 100) * 100} 
                          className="h-2"
                        />
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Sidebar Info */}
          <div className="space-y-4">
            {/* Key Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>{t('evaluation.keyMetrics.title')}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {report.total_score !== undefined && (
                  <div className="flex justify-between">
                    <span>{t('evaluation.totalScore')}</span>
                    <span className="font-bold text-2xl text-blue-600">
                      {report.total_score.toFixed(1)}
                    </span>
                  </div>
                )}
                
                {report.match_score !== undefined && (
                  <div className="flex justify-between">
                    <span>{t('evaluation.matchScore')}</span>
                    <span className="font-bold">{(report.match_score * 100).toFixed(0)}%</span>
                  </div>
                )}
                
                {report.percentile_rank !== undefined && (
                  <div className="flex justify-between">
                    <span>{t('evaluation.percentileRank')}</span>
                    <span className="font-bold">{report.percentile_rank.toFixed(0)}</span>
                  </div>
                )}
                
                {report.risk_level && (
                  <div className="flex justify-between items-center">
                    <span>{t('evaluation.riskLevel')}</span>
                    <Badge className={getRiskColor(report.risk_level)}>
                      {t(`evaluation.risk.${report.risk_level.toLowerCase()}`)}
                    </Badge>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Radar Chart */}
            {report.dimension_scores && (
              <Card>
                <CardHeader>
                  <CardTitle>{t('evaluation.skillsRadar.title')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <RadarChart 
                    data={Object.entries(report.dimension_scores).map(([key, value]) => ({
                      dimension: key,
                      score: value,
                      fullMark: 100
                    }))}
                    height={250}
                  />
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      )}

      {/* Details Tab */}
      {activeTab === 'details' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Strengths */}
          {report.strengths && report.strengths.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  <span>{t('evaluation.strengths.title')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {report.strengths.map((strength, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{strength}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Weaknesses */}
          {report.weaknesses && report.weaknesses.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingDown className="h-5 w-5 text-red-600" />
                  <span>{t('evaluation.weaknesses.title')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-3">
                  {report.weaknesses.map((weakness, index) => (
                    <li key={index} className="flex items-start space-x-2">
                      <XCircle className="h-4 w-4 text-red-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{weakness}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Overall Evaluation */}
          {report.overall_evaluation && (
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileText className="h-5 w-5" />
                  <span>{t('evaluation.overallEvaluation.title')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {report.overall_evaluation}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Recommendations */}
          {report.recommendations && (
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-blue-600" />
                  <span>{t('evaluation.recommendations.title')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {report.recommendations}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Key Insights */}
          {report.key_insights && (
            <Card className="lg:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Target className="h-5 w-5 text-purple-600" />
                  <span>{t('evaluation.keyInsights.title')}</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 leading-relaxed whitespace-pre-wrap">
                  {report.key_insights}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      )}

      {/* Comparison Tab */}
      {activeTab === 'comparison' && showComparison && candidateId && (
        <CandidateComparison 
          questionnaireId={report.questionnaire_id}
          currentCandidateId={candidateId}
        />
      )}
    </div>
  );
}
```

### API Changes
No backend API changes required - leveraging existing endpoints with proper type mapping.

### Configuration Changes
No configuration changes required - using existing environment variables and Docker setup.

## Implementation Sequence

### Phase 1: Foundation Setup (Priority 0 - Week 1, Days 1-2)
1. **Create directory structure** - Set up frontend folder structure
2. **Type system implementation** - Create unified type definitions
3. **API client configuration** - Set up axios with interceptors
4. **Basic routing setup** - Configure Next.js app router structure

### Phase 2: Core Services (Priority 0 - Week 1, Days 3-4)
1. **Service layer implementation** - Create service classes for API communication
2. **Authentication integration** - Implement token management and auth flow
3. **Error handling system** - Set up error boundaries and toast notifications
4. **Progress tracking system** - Implement WebSocket integration for real-time updates

### Phase 3: AI Questionnaire Features (Priority 0 - Week 1, Days 5-7)
1. **Generation form component** - Build AI questionnaire generation interface
2. **Template selection system** - Implement position type templates
3. **Progress tracking UI** - Create progress indicators for generation process
4. **Questionnaire management** - Build list/detail views for questionnaires

### Phase 4: Evaluation System (Priority 1 - Week 2, Days 1-3)
1. **Evaluation report component** - Build comprehensive report visualization
2. **Chart components** - Implement radar charts, bar charts, progress visualization
3. **Candidate comparison** - Multi-candidate comparison interface
4. **PDF export functionality** - Implement report export capabilities

### Phase 5: User Management & Batch Processing (Priority 1 - Week 2, Days 4-7)
1. **User management interface** - CRUD operations for user management
2. **Batch upload system** - Multi-file resume upload with progress tracking
3. **Real-time notifications** - WebSocket integration for system notifications
4. **Final testing & optimization** - End-to-end testing and performance optimization

Each phase is independently deployable and testable with proper rollback capabilities.

## Validation Plan

### Unit Tests
- **Component Testing**: React component unit tests using React Testing Library
- **Service Testing**: API service layer tests with mock responses
- **Hook Testing**: Custom React hooks testing with proper state management
- **Type Validation**: TypeScript strict mode compilation tests

### Integration Tests
- **API Integration**: End-to-end API communication testing
- **Authentication Flow**: Complete login/logout and token refresh testing
- **File Upload**: Multi-file upload with progress tracking validation
- **Real-time Updates**: WebSocket connection and message handling testing

### Business Logic Verification
- **AI Questionnaire Generation**: Verify generation flow from template selection to completion
- **Evaluation Report Creation**: Validate report generation and visualization accuracy
- **Candidate Comparison**: Verify multi-candidate comparison algorithms and display
- **Permission System**: Test data access controls and user role permissions
- **Internationalization**: Verify all UI text is translatable and error codes map correctly

### Performance Testing
- **Load Testing**: Verify application performance with multiple concurrent users
- **File Upload Testing**: Large file upload handling and progress accuracy
- **Real-time Testing**: WebSocket connection stability under load
- **Memory Usage**: Monitor for memory leaks in React components and service workers

## Success Metrics
- **Functionality**: 100% of confirmed requirements implemented and working
- **Type Safety**: Zero TypeScript compilation errors in strict mode
- **API Coverage**: All backend APIs accessible through frontend interfaces
- **Performance**: <3s initial page load, <200ms API response handling
- **Accessibility**: WCAG 2.1 AA compliance for all interactive components
- **Internationalization**: Complete English/Chinese language support with error code translation