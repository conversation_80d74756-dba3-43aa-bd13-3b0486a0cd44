# TalentForge Pro - Comprehensive Repository Context Report

## Project Overview

**TalentForge Pro** is an intelligent talent assessment and job matching system built with a hybrid intelligent architecture (rule engine + LLM). This is a professional HR-tech platform designed for comprehensive talent evaluation and position matching.

### Project Identity
- **Name**: TalentForge Pro
- **Type**: Full-stack web application (HR-tech platform)
- **Purpose**: Intelligent talent assessment and job matching system
- **Development Stage**: Active development (Sprint 3 - Candidate Management)
- **Architecture**: Microservices with Docker containerization

## Technology Stack Analysis

### Backend Infrastructure
- **Language**: Python 3.12
- **Framework**: FastAPI 0.110+ (Modern async web framework)
- **Database**: PostgreSQL 17 + pgvector 0.8.0 (Vector database for ML)
- **ORM**: SQLAlchemy 2.0 (Async support)
- **Authentication**: JWT with bcrypt password hashing
- **Task Queue**: Celery with Redis
- **Object Storage**: MinIO (S3-compatible)
- **Package Manager**: Poetry

### Frontend Infrastructure  
- **Framework**: Next.js 15.4.1 (App Router)
- **UI Library**: React 19
- **Language**: TypeScript 5.x (Strict mode)
- **UI Components**: Radix UI primitives + shadcn/ui
- **Styling**: Tailwind CSS 3.4.1
- **State Management**: Redux Toolkit + TanStack React Query
- **Package Manager**: pnpm 10.13.1
- **Internationalization**: next-intl (English/Chinese support)

### ML/AI Services
- **Embedding Models**: BGE-M3 (1024-dim), OpenAI (1536-dim backup)
- **Local LLM**: Ollama 0.11.4
- **ML Models**: BERT+BiLSTM, MLP
- **Vector Storage**: pgvector integrated with PostgreSQL

### Infrastructure & DevOps
- **Containerization**: Docker + Docker Compose
- **Reverse Proxy**: Nginx (environment-optimized)
- **Caching**: Redis 7
- **Monitoring**: Prometheus + Grafana (production profile)
- **Deployment**: Environment-aware (development/production/testing/staging)

## Project Structure & Architecture

### Root Directory Organization (Strictly Enforced)
```
talent_forge_pro/
├── CLAUDE.md              # Primary project documentation
├── README.md              # Project overview  
├── DOCS_INDEXES.md        # Documentation index
├── SCRIPT_INDEXES.md      # Scripts index
├── Makefile               # Primary orchestration tool
├── LICENSE                # MIT License
├── app/                   # ⭐ Main application directory
│   ├── backend/           # FastAPI backend
│   ├── frontend/          # Next.js frontend  
│   ├── configs/           # Configuration files
│   ├── scripts/           # Development/deployment scripts
│   └── docker-compose.yml # Container orchestration
├── docs/                  # ⭐ Comprehensive documentation
├── archive/               # Historical files/cleanup
└── .claude/              # Claude Code specifications
```

**Critical Architecture Rule**: Root directory follows "Clean Root" principle - only essential files allowed, all application code in `app/` directory.

### Application Architecture
```
app/
├── backend/app/
│   ├── api/              # API endpoints (v1 routing)
│   ├── core/             # Core configuration and security
│   ├── crud/             # Database CRUD operations
│   ├── models/           # SQLAlchemy models
│   ├── schemas/          # Pydantic request/response schemas
│   ├── services/         # Business logic layer
│   └── utils/            # Utility functions
├── frontend/app/
│   ├── (auth)/           # Authentication routes
│   ├── (dashboard)/      # Dashboard application routes
│   ├── [locale]/         # Internationalization
│   ├── i18n/             # I18n configuration
│   └── globals.css       # Global styles
└── scripts/
    ├── development/      # Development tools
    ├── test/            # Testing scripts  
    ├── deployment/      # Deployment automation
    └── monitoring/      # System monitoring
```

## Development Workflow & Standards

### Docker-First Development
- **Orchestration**: Make commands handle all operations
- **Environment Isolation**: All services run in containers
- **Data Persistence**: `~/dev_data/hephaestus/` (PostgreSQL, Redis, MinIO)
- **Network**: Internal `hephaestus_network`
- **Environment Management**: `.env` files with environment-specific overrides

### Code Quality Standards

#### Backend (Python)
- **Architecture**: Layered (API → Service → CRUD → Model)
- **Async Programming**: All database operations async
- **Type Annotations**: Required with Pydantic validation
- **Code Quality**: Ruff linter, Black formatter, MyPy type checking
- **Testing**: pytest with asyncio support (>80% coverage target)

#### Frontend (TypeScript)
- **Strict Mode**: TypeScript strict compilation
- **Component Pattern**: Functional components with hooks
- **State Management**: React Query for server state, Redux for client state  
- **Performance**: React.memo optimization, code splitting
- **Testing**: Jest + Testing Library, Playwright E2E

### API Design Consistency (Critical)
- **Authentication**: JWT in Authorization header: `Bearer {token}`
- **Response Format**: Unified `{items, total, skip, limit}` for pagination
- **Error Handling**: Must include `error_code` field format: `[MODULE]_[CATEGORY]_[ERROR]`
- **ID System**: Snowflake IDs (BigInteger) - NOT UUID

### Internationalization Architecture
- **System**: React Context + JSON translation files (next-intl)
- **Languages**: English (en), Chinese (zh) with RTL support planned
- **Error Messages**: Backend uses error codes, frontend translates via `t()`
- **Critical Rule**: No hardcoded user-facing text allowed

## Testing Architecture

### Test Layers
1. **Unit Tests**: pytest (backend), Jest (frontend) - >80% coverage
2. **Integration Tests**: Critical business flows - 100% coverage  
3. **API Tests**: Tavern + Docker for contract validation
4. **E2E Tests**: Playwright (Chrome-focused, admin workflows)

### E2E Testing Setup
- **Configuration**: `playwright.config.ts` in frontend
- **Base URL**: `http://localhost:8088` 
- **Authentication**: Development token bypass
- **Test Structure**: `/e2e/tests/` organized by feature domains
- **Reporting**: HTML, JSON, JUnit formats with screenshots/videos

## Database Architecture

### Primary Database (PostgreSQL + pgvector)
- **Strategy**: Single unified vector-enabled database
- **Benefits**: Transaction consistency, simplified operations
- **Vector Search**: HNSW indexing for high-performance similarity search
- **Migration Management**: Alembic with strict change control

### Data Models (Key Entities)
- **Users**: Authentication, preferences, roles
- **Candidates**: Profile data with vector embeddings  
- **Positions**: Job requirements with skill vectors
- **Assessments**: Multi-dimensional evaluation results
- **Applications**: Candidate-position matching records

## Business Domain Knowledge

### Core Functionality
- **Five-Dimensional Assessment**:
  - Digital Literacy (20%)
  - Industry Skills (25%) 
  - Position Skills (30%)
  - Innovation Capacity (15%)
  - Learning Potential (10%)

### Scoring Systems
- **DCI Score**: Digital Capability Index
- **JFS Score**: Job Fit Score  
- **Data Permissions**: PRIVATE, SHARED, TEAM, PUBLIC

### User Roles & Permissions
- **Admin**: Full system access (<EMAIL>)
- **HR Manager**: Candidate and position management
- **Recruiter**: Application review and assessment
- **Candidate**: Profile management and application submission

## Integration Points & APIs

### External Services
- **Ollama**: Local LLM embeddings (v0.11.4)
- **OpenAI**: Backup embedding service
- **Redis**: Caching and session management  
- **MinIO**: Document and file storage

### Internal API Structure
- **Base URL**: `/api/v1/`
- **Authentication**: `/auth/` endpoints
- **Admin**: `/admin/` protected routes
- **Public**: `/public/` open endpoints

## Development Tools & Scripts

### Make Commands (Primary Interface)
```bash
make setup    # First-time environment setup
make up       # Start all services (ENV aware)
make down     # Stop services  
make logs     # View aggregated logs
make status   # Check service health
make help     # Show all available commands
```

### Environment Management
- **Development**: Default mode with debug tools
- **Production**: Optimized with monitoring stack
- **Testing**: Isolated testing environment
- **Staging**: Pre-production validation

### Package Management (Docker-Isolated)
- **Backend**: Poetry in container, refresh via `make backend-package-refresh`
- **Frontend**: pnpm in container, refresh via `make frontend-package-refresh`
- **Critical Rule**: Host package managers have no effect on containers

## Quality Assurance Framework

### Mandatory Standards
- **Database Changes**: Must use Alembic migrations (never manual schema)
- **Type Safety**: Frontend/backend model consistency enforced
- **Error Handling**: Backend error codes + frontend translation
- **Authentication**: Development token bypass for testing
- **File Organization**: Strict directory structure compliance

### Quality Gates (8-Step Validation)
1. Syntax validation with Context7
2. Type checking with Sequential analysis  
3. Linting with Context7 rules
4. Security scanning with Sequential OWASP compliance
5. Testing with Playwright E2E (≥80% unit, ≥70% integration)
6. Performance analysis with Sequential benchmarking
7. Documentation validation with Context7 patterns
8. Integration testing with Playwright deployment verification

### Code Review Requirements
- **Architecture**: All API changes need architect review
- **Backward Compatibility**: Breaking changes require migration plan
- **Test Coverage**: New features must include unit tests
- **Documentation**: API changes must update docs synchronously

## Constraints & Considerations

### Technical Constraints
- **ID System**: Must use Snowflake IDs (BigInteger), not UUID
- **API Responses**: Must include error_code field for internationalization
- **Docker Dependencies**: All operations assume containerized environment  
- **Python Version**: Locked to 3.12 for consistency
- **Node Version**: Requires ≥18.17.0

### Development Constraints  
- **Root Directory**: Strict file placement rules enforced
- **Container Isolation**: Package dependencies completely isolated from host
- **Migration Discipline**: All DB changes through Alembic (mandatory)
- **Testing Requirements**: E2E tests for all critical user flows

### Performance Requirements
- **API Response**: <200ms target
- **First Paint**: <3 seconds on 3G
- **ML Inference**: <200ms for recommendations

## Sprint Progress & Development Status

### Current Status: Sprint 3 - Candidate Management
- ✅ Sprint 1: Basic infrastructure setup
- ✅ Sprint 2: Authentication & user management  
- 🔄 Sprint 3: Candidate management (in progress)
- 📅 Sprint 4: ML service integration (planned)
- 📅 Sprint 5: Optimization & deployment (planned)

### Recent Major Changes
- Root directory cleanup and organization (2025-08-11)
- Monitoring system implementation with proper migrations
- International

ization system overhaul with error code standardization
- Docker service optimization and environment management

## Key Success Patterns

### What Works Well
1. **Docker-First Architecture**: Consistent environments across dev/prod
2. **Make-Driven Workflow**: Single command interface for all operations  
3. **Strict Directory Organization**: Clean separation of concerns
4. **Comprehensive Documentation**: CLAUDE.md as single source of truth
5. **Environment-Aware Configuration**: Seamless dev/prod transitions

### Areas Requiring Attention
1. **Migration Discipline**: Ensure all DB changes use proper migrations
2. **Type Consistency**: Maintain alignment between frontend/backend models
3. **Error Code Coverage**: Complete translation coverage for all error scenarios
4. **Performance Monitoring**: Establish baseline metrics and alerting
5. **Test Coverage**: Achieve target coverage percentages consistently

---

*This analysis provides comprehensive context for requirements-driven development within the TalentForge Pro ecosystem. All architectural decisions and constraints should be considered when implementing new features or modifications.*