/**
 * AI Questionnaire Service
 * Handles AI-generated questionnaire creation, templates, and progress monitoring
 */

import { apiClient } from '@/lib/api/client';
import {
  QuestionnaireGenerateRequest,
  QuestionnaireGenerateResponse,
  GenerationProgress,
  QuestionnaireTemplate,
  TemplateListResponse,
  Questionnaire,
  QuestionnaireListResponse,
  QuestionnaireSearchParams,
  SnowflakeID
} from '@/types/questionnaire';

class AIQuestionnaireService {
  
  /**
   * Generate a new AI questionnaire
   */
  async generateQuestionnaire(request: QuestionnaireGenerateRequest): Promise<QuestionnaireGenerateResponse> {
    const response = await apiClient.post<QuestionnaireGenerateResponse>(
      '/ai/ai-questionnaire/generate',
      request
    );
    return response;
  }

  /**
   * Get generation progress by generation ID
   */
  async getGenerationProgress(generationId: string): Promise<GenerationProgress> {
    const response = await apiClient.get<GenerationProgress>(
      `/ai/ai-questionnaire/progress/${generationId}`
    );
    return response;
  }

  /**
   * Cancel ongoing generation
   */
  async cancelGeneration(generationId: string): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.post<{ success: boolean; message: string }>(
      `/ai/ai-questionnaire/cancel/${generationId}`
    );
    return response;
  }

  /**
   * Get available questionnaire templates
   */
  async getTemplates(params?: { 
    position_type?: string;
    industry?: string;
    skip?: number;
    limit?: number;
  }): Promise<TemplateListResponse> {
    const response = await apiClient.get<TemplateListResponse>(
      '/ai/ai-questionnaire/templates/',
      { params }
    );
    return response;
  }

  /**
   * Get a specific template by ID
   */
  async getTemplate(templateId: SnowflakeID): Promise<QuestionnaireTemplate> {
    const response = await apiClient.get<QuestionnaireTemplate>(
      `/ai/ai-questionnaire/templates/${templateId}`
    );
    return response;
  }

  /**
   * Create a new template
   */
  async createTemplate(template: Omit<QuestionnaireTemplate, 'id' | 'created_at' | 'updated_at' | 'is_system'>): Promise<QuestionnaireTemplate> {
    const response = await apiClient.post<QuestionnaireTemplate>(
      '/ai/ai-questionnaire/templates/',
      template
    );
    return response;
  }

  /**
   * Update an existing template
   */
  async updateTemplate(
    templateId: SnowflakeID,
    template: Partial<Omit<QuestionnaireTemplate, 'id' | 'created_at' | 'updated_at'>>
  ): Promise<QuestionnaireTemplate> {
    const response = await apiClient.put<QuestionnaireTemplate>(
      `/ai/ai-questionnaire/templates/${templateId}`,
      template
    );
    return response;
  }

  /**
   * Delete a template
   */
  async deleteTemplate(templateId: SnowflakeID): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete<{ success: boolean; message: string }>(
      `/ai/ai-questionnaire/templates/${templateId}`
    );
    return response;
  }

  /**
   * Get all questionnaires with search and filters
   */
  async getQuestionnaires(params?: QuestionnaireSearchParams): Promise<QuestionnaireListResponse> {
    const response = await apiClient.get<QuestionnaireListResponse>(
      '/ai/ai-questionnaire/',
      { params }
    );
    return response;
  }

  /**
   * Get a specific questionnaire by ID
   */
  async getQuestionnaire(questionnaireId: SnowflakeID): Promise<Questionnaire> {
    const response = await apiClient.get<Questionnaire>(
      `/ai/ai-questionnaire/${questionnaireId}`
    );
    return response;
  }

  /**
   * Save generated questionnaire
   */
  async saveQuestionnaire(questionnaire: {
    title: string;
    description: string;
    position_type: string;
    industry: string;
    difficulty_level: string;
    questions: Array<{
      question_text: string;
      question_type: string;
      category: string;
      difficulty: string;
      estimated_time: number;
      options?: Array<{ text: string; is_correct?: boolean; score?: number }>;
      evaluation_criteria: string[];
      scoring_weights: {
        digital_literacy: number;
        industry_skill: number;
        position_skill: number;
        innovation: number;
        learning_potential: number;
      };
    }>;
    generation_metadata?: any;
  }): Promise<Questionnaire> {
    const response = await apiClient.post<Questionnaire>(
      '/ai/ai-questionnaire/',
      questionnaire
    );
    return response;
  }

  /**
   * Update a questionnaire
   */
  async updateQuestionnaire(
    questionnaireId: SnowflakeID,
    updates: Partial<Omit<Questionnaire, 'id' | 'created_at' | 'updated_at' | 'created_by'>>
  ): Promise<Questionnaire> {
    const response = await apiClient.put<Questionnaire>(
      `/ai/ai-questionnaire/${questionnaireId}`,
      updates
    );
    return response;
  }

  /**
   * Delete a questionnaire
   */
  async deleteQuestionnaire(questionnaireId: SnowflakeID): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.delete<{ success: boolean; message: string }>(
      `/ai/ai-questionnaire/${questionnaireId}`
    );
    return response;
  }

  /**
   * Activate/deactivate a questionnaire
   */
  async toggleQuestionnaireStatus(
    questionnaireId: SnowflakeID, 
    isActive: boolean
  ): Promise<{ success: boolean; message: string }> {
    const response = await apiClient.patch<{ success: boolean; message: string }>(
      `/ai/ai-questionnaire/${questionnaireId}/status`,
      { is_active: isActive }
    );
    return response;
  }

  /**
   * Get AI generation statistics
   */
  async getGenerationStats(): Promise<{
    total_generated: number;
    successful_generations: number;
    average_generation_time: number;
    most_popular_position_types: Array<{ position_type: string; count: number }>;
    most_popular_industries: Array<{ industry: string; count: number }>;
    quality_score_distribution: Array<{ score_range: string; count: number }>;
  }> {
    const response = await apiClient.get(
      '/ai/ai-questionnaire/stats'
    );
    return response;
  }

  /**
   * Regenerate questions for an existing questionnaire
   */
  async regenerateQuestions(
    questionnaireId: SnowflakeID,
    regenerateRequest: {
      question_indices?: number[];
      new_requirements?: Partial<QuestionnaireGenerateRequest>;
    }
  ): Promise<QuestionnaireGenerateResponse> {
    const response = await apiClient.post<QuestionnaireGenerateResponse>(
      `/ai/ai-questionnaire/${questionnaireId}/regenerate`,
      regenerateRequest
    );
    return response;
  }

  /**
   * Preview questionnaire generation without saving
   */
  async previewQuestionnaire(request: QuestionnaireGenerateRequest): Promise<{
    preview_questions: Array<{
      question_text: string;
      category: string;
      difficulty: string;
      estimated_time: number;
    }>;
    estimated_total_duration: number;
    quality_preview: {
      coverage_score: number;
      difficulty_distribution: Record<string, number>;
      category_distribution: Record<string, number>;
    };
  }> {
    const response = await apiClient.post(
      '/ai/ai-questionnaire/preview',
      request
    );
    return response;
  }

  /**
   * Get questionnaire quality assessment
   */
  async assessQuestionnaireQuality(questionnaireId: SnowflakeID): Promise<{
    overall_quality_score: number;
    detailed_assessment: {
      question_clarity: number;
      difficulty_balance: number;
      category_coverage: number;
      duration_accuracy: number;
    };
    recommendations: string[];
    improvement_suggestions: Array<{
      question_index: number;
      suggestion: string;
      impact: 'low' | 'medium' | 'high';
    }>;
  }> {
    const response = await apiClient.get(
      `/ai/ai-questionnaire/${questionnaireId}/quality-assessment`
    );
    return response;
  }
}

// Export singleton instance
export const aiQuestionnaireService = new AIQuestionnaireService();
export default aiQuestionnaireService;