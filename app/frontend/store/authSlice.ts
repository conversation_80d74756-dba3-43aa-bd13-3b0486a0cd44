import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { authService } from '@/services/auth';
import { LoginCredentials, User } from '@/types';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  initialized: boolean;
}

// Simple initial state - no complex logic during SSR
const initialState: AuthState = {
  user: null,
  isAuthenticated: false, // Always start as false
  isLoading: true, // Start as loading to prevent premature redirects during auth init
  error: null,
  initialized: false, // Track whether auth has been initialized
};

// Async thunks
export const login = createAsyncThunk(
  'auth/login',
  async (credentials: LoginCredentials) => {
    const response = await authService.login(credentials);
    return response.user;
  }
);

export const logout = createAsyncThunk(
  'auth/logout',
  async () => {
    await authService.logout();
  }
);

export const fetchCurrentUser = createAsyncThunk(
  'auth/fetchCurrentUser',
  async () => {
    const user = await authService.getCurrentUser();
    return user;
  }
);

export const initializeAuth = createAsyncThunk(
  'auth/initialize',
  async () => {
    if (authService.isAuthenticated()) {
      try {
        const user = await authService.getCurrentUser();
        return user;
      } catch (error: any) {
        // If getCurrentUser fails (likely due to expired token),
        // try to refresh the token first
        console.log('🔄 Auth initialization: getCurrentUser failed, attempting token refresh...');
        
        try {
          await authService.refreshToken();
          // After successful refresh, try getCurrentUser again
          const user = await authService.getCurrentUser();
          return user;
        } catch (refreshError) {
          console.error('❌ Auth initialization: Token refresh failed during initialization:', refreshError);
          // Token refresh failed, user needs to login again
          return null;
        }
      }
    }
    return null;
  }
);

// Auth slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    setUser: (state, action: PayloadAction<User | null>) => {
      state.user = action.payload;
      state.isAuthenticated = !!action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    checkComplete: (state, action: PayloadAction<{ isAuthenticated: boolean }>) => {
      state.isLoading = false;
      state.isAuthenticated = action.payload.isAuthenticated;
      if (!action.payload.isAuthenticated) {
        state.user = null;
      }
    },
  },
  extraReducers: (builder) => {
    // Login
    builder
      .addCase(login.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload || null;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(login.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.error.message || 'Login failed';
      });
    
    // Logout
    builder
      .addCase(logout.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(logout.fulfilled, (state) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
        state.error = null;
      })
      .addCase(logout.rejected, (state) => {
        // Even if logout fails, clear the user state
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
      });
    
    // Fetch current user
    builder
      .addCase(fetchCurrentUser.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(fetchCurrentUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.isAuthenticated = true;
        state.error = null;
      })
      .addCase(fetchCurrentUser.rejected, (state) => {
        state.isLoading = false;
        state.user = null;
        state.isAuthenticated = false;
      });
    
    // Initialize auth
    builder
      .addCase(initializeAuth.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(initializeAuth.fulfilled, (state, action) => {
        state.isLoading = false;
        state.initialized = true;
        if (action.payload) {
          state.user = action.payload;
          state.isAuthenticated = true;
        } else {
          state.user = null;
          state.isAuthenticated = false;
        }
        state.error = null;
      })
      .addCase(initializeAuth.rejected, (state) => {
        state.isLoading = false;
        state.initialized = true;
        state.user = null;
        state.isAuthenticated = false;
      });
  },
});

export const { setUser, clearError, checkComplete } = authSlice.actions;
export default authSlice.reducer;