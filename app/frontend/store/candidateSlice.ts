import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { candidateService, CandidateListParams, BulkExportParams, ShareCandidateParams } from '@/services/candidate';
import { 
  Candidate, 
  CandidateCreate, 
  CandidateUpdate, 
  CandidateListResponse,
  CandidateSearch,
  CandidateStatus,
  DataPermission,
  ResumeUploadResponse 
} from '@/types';

// Loading states interface
interface LoadingStates {
  list: boolean;
  detail: boolean;
  create: boolean;
  update: boolean;
  delete: boolean;
  uploadResume: boolean;
  share: boolean;
  bulkImport: boolean;
  bulkExport: boolean;
}

// Error states interface
interface ErrorStates {
  list: string | null;
  detail: string | null;
  create: string | null;
  update: string | null;
  delete: string | null;
  uploadResume: string | null;
  share: string | null;
  bulkImport: string | null;
  bulkExport: string | null;
}

// Pagination state
interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// Search filters state
interface SearchFiltersState extends CandidateSearch {
  order_by?: string;
  order_desc?: boolean;
}

// Bulk operations state
interface BulkOperationsState {
  selectedIds: string[];
  operation: string | null;
  progress: number;
}

// Main candidate state interface
interface CandidateState {
  // Data
  candidates: Candidate[];
  currentCandidate: Candidate | null;
  
  // UI state
  pagination: PaginationState;
  filters: SearchFiltersState;
  bulkOperations: BulkOperationsState;
  
  // Loading states
  loading: LoadingStates;
  
  // Error states
  errors: ErrorStates;
  
  // Cache metadata
  lastFetch: number | null;
  cacheValid: boolean;
}

// Initial state
const initialState: CandidateState = {
  // Data
  candidates: [],
  currentCandidate: null,
  
  // UI state
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  },
  filters: {
    search: '',
    status: undefined,
    skills: [],
    min_experience: undefined,
    max_experience: undefined,
    min_salary: undefined,
    max_salary: undefined,
    data_permission: undefined,
    source: undefined,
    tags: [],
    created_after: undefined,
    created_before: undefined,
    order_by: 'created_at',
    order_desc: true,
  },
  bulkOperations: {
    selectedIds: [],
    operation: null,
    progress: 0,
  },
  
  // Loading states
  loading: {
    list: false,
    detail: false,
    create: false,
    update: false,
    delete: false,
    uploadResume: false,
    share: false,
    bulkImport: false,
    bulkExport: false,
  },
  
  // Error states
  errors: {
    list: null,
    detail: null,
    create: null,
    update: null,
    delete: null,
    uploadResume: null,
    share: null,
    bulkImport: null,
    bulkExport: null,
  },
  
  // Cache metadata
  lastFetch: null,
  cacheValid: false,
};

// Async thunks
export const fetchCandidates = createAsyncThunk(
  'candidates/fetchList',
  async (params: CandidateListParams = {}) => {
    const response = await candidateService.getList(params);
    return { response, params };
  }
);

export const fetchCandidateById = createAsyncThunk(
  'candidates/fetchById',
  async (id: string) => {
    const candidate = await candidateService.getById(id);
    return candidate;
  }
);

export const createCandidate = createAsyncThunk(
  'candidates/create',
  async (data: CandidateCreate) => {
    const candidate = await candidateService.create(data);
    return candidate;
  }
);

export const createCandidateWithResume = createAsyncThunk(
  'candidates/createWithResume',
  async ({ data, resumeFile }: { data: CandidateCreate; resumeFile: File }) => {
    const candidate = await candidateService.createWithResume(data, resumeFile);
    return candidate;
  }
);

export const updateCandidate = createAsyncThunk(
  'candidates/update',
  async ({ id, data }: { id: string; data: CandidateUpdate }) => {
    const candidate = await candidateService.update(id, data);
    return candidate;
  }
);

export const deleteCandidate = createAsyncThunk(
  'candidates/delete',
  async (id: string) => {
    await candidateService.delete(id);
    return id;
  }
);

export const uploadResume = createAsyncThunk(
  'candidates/uploadResume',
  async ({ candidateId, resumeFile }: { candidateId: string; resumeFile: File }) => {
    const response = await candidateService.uploadResume(candidateId, resumeFile);
    return { candidateId, response };
  }
);

export const shareCandidate = createAsyncThunk(
  'candidates/share',
  async ({ candidateId, params }: { candidateId: string; params: ShareCandidateParams }) => {
    await candidateService.shareCandidate(candidateId, params);
    return { candidateId, userIds: params.user_ids };
  }
);

export const bulkImportCandidates = createAsyncThunk(
  'candidates/bulkImport',
  async (file: File) => {
    const response = await candidateService.bulkImport(file);
    return response;
  }
);

export const bulkExportCandidates = createAsyncThunk(
  'candidates/bulkExport',
  async (params: BulkExportParams) => {
    const response = await candidateService.bulkExport(params);
    return response;
  }
);

export const checkDuplicateCandidate = createAsyncThunk(
  'candidates/checkDuplicate',
  async (params: {
    name: string;
    email?: string;
    phone?: string;
    exclude_id?: string;
  }) => {
    const response = await candidateService.checkDuplicate(params);
    return response;
  }
);

export const fetchCandidateStats = createAsyncThunk(
  'candidates/fetchStats',
  async () => {
    const stats = await candidateService.getStats();
    return stats;
  }
);

// Candidate slice
const candidateSlice = createSlice({
  name: 'candidates',
  initialState,
  reducers: {
    // Filter actions
    setFilters: (state, action: PayloadAction<Partial<SearchFiltersState>>) => {
      state.filters = { ...state.filters, ...action.payload };
      state.cacheValid = false;
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
      state.cacheValid = false;
    },
    
    // Pagination actions
    setPagination: (state, action: PayloadAction<Partial<PaginationState>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    
    // Current candidate actions
    setCurrentCandidate: (state, action: PayloadAction<Candidate | null>) => {
      state.currentCandidate = action.payload;
    },
    clearCurrentCandidate: (state) => {
      state.currentCandidate = null;
    },
    
    // Bulk operations actions
    toggleCandidateSelection: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      const index = state.bulkOperations.selectedIds.indexOf(id);
      if (index >= 0) {
        state.bulkOperations.selectedIds.splice(index, 1);
      } else {
        state.bulkOperations.selectedIds.push(id);
      }
    },
    selectAllCandidates: (state) => {
      state.bulkOperations.selectedIds = state.candidates.map(c => c.id);
    },
    clearCandidateSelection: (state) => {
      state.bulkOperations.selectedIds = [];
    },
    setBulkOperation: (state, action: PayloadAction<string | null>) => {
      state.bulkOperations.operation = action.payload;
      state.bulkOperations.progress = 0;
    },
    setBulkProgress: (state, action: PayloadAction<number>) => {
      state.bulkOperations.progress = action.payload;
    },
    
    // Error actions
    clearError: (state, action: PayloadAction<keyof ErrorStates>) => {
      state.errors[action.payload] = null;
    },
    clearAllErrors: (state) => {
      Object.keys(state.errors).forEach(key => {
        state.errors[key as keyof ErrorStates] = null;
      });
    },
    
    // Cache actions
    invalidateCache: (state) => {
      state.cacheValid = false;
    },
    
    // Local candidate updates (optimistic updates)
    updateCandidateLocal: (state, action: PayloadAction<Candidate>) => {
      const index = state.candidates.findIndex(c => c.id === action.payload.id);
      if (index >= 0) {
        state.candidates[index] = action.payload;
      }
      if (state.currentCandidate?.id === action.payload.id) {
        state.currentCandidate = action.payload;
      }
    },
    
    // Add candidate locally (optimistic create)
    addCandidateLocal: (state, action: PayloadAction<Candidate>) => {
      state.candidates.unshift(action.payload);
      state.pagination.total += 1;
    },
    
    // Remove candidate locally (optimistic delete)
    removeCandidateLocal: (state, action: PayloadAction<string>) => {
      state.candidates = state.candidates.filter(c => c.id !== action.payload);
      state.pagination.total -= 1;
      if (state.currentCandidate?.id === action.payload) {
        state.currentCandidate = null;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch candidates list
    builder
      .addCase(fetchCandidates.pending, (state) => {
        state.loading.list = true;
        state.errors.list = null;
      })
      .addCase(fetchCandidates.fulfilled, (state, action) => {
        state.loading.list = false;
        state.candidates = action.payload.response.items;
        state.pagination = {
          page: Math.floor((action.payload.params.skip || 0) / (action.payload.params.limit || 20)) + 1,
          pageSize: action.payload.params.limit || 20,
          total: action.payload.response.total,
          totalPages: Math.ceil(action.payload.response.total / (action.payload.params.limit || 20)),
        };
        state.lastFetch = Date.now();
        state.cacheValid = true;
      })
      .addCase(fetchCandidates.rejected, (state, action) => {
        state.loading.list = false;
        state.errors.list = action.error.message || 'Failed to fetch candidates';
      });

    // Fetch candidate by ID
    builder
      .addCase(fetchCandidateById.pending, (state) => {
        state.loading.detail = true;
        state.errors.detail = null;
      })
      .addCase(fetchCandidateById.fulfilled, (state, action) => {
        state.loading.detail = false;
        state.currentCandidate = action.payload;
        
        // Update in list if exists
        const index = state.candidates.findIndex(c => c.id === action.payload.id);
        if (index >= 0) {
          state.candidates[index] = action.payload;
        }
      })
      .addCase(fetchCandidateById.rejected, (state, action) => {
        state.loading.detail = false;
        state.errors.detail = action.error.message || 'Failed to fetch candidate';
      });

    // Create candidate
    builder
      .addCase(createCandidate.pending, (state) => {
        state.loading.create = true;
        state.errors.create = null;
      })
      .addCase(createCandidate.fulfilled, (state, action) => {
        state.loading.create = false;
        state.candidates.unshift(action.payload);
        state.pagination.total += 1;
        state.currentCandidate = action.payload;
      })
      .addCase(createCandidate.rejected, (state, action) => {
        state.loading.create = false;
        state.errors.create = action.error.message || 'Failed to create candidate';
      });

    // Create candidate with resume
    builder
      .addCase(createCandidateWithResume.pending, (state) => {
        state.loading.create = true;
        state.errors.create = null;
      })
      .addCase(createCandidateWithResume.fulfilled, (state, action) => {
        state.loading.create = false;
        state.candidates.unshift(action.payload);
        state.pagination.total += 1;
        state.currentCandidate = action.payload;
      })
      .addCase(createCandidateWithResume.rejected, (state, action) => {
        state.loading.create = false;
        state.errors.create = action.error.message || 'Failed to create candidate with resume';
      });

    // Update candidate
    builder
      .addCase(updateCandidate.pending, (state) => {
        state.loading.update = true;
        state.errors.update = null;
      })
      .addCase(updateCandidate.fulfilled, (state, action) => {
        state.loading.update = false;
        
        // Update in list
        const index = state.candidates.findIndex(c => c.id === action.payload.id);
        if (index >= 0) {
          state.candidates[index] = action.payload;
        }
        
        // Update current candidate if it matches
        if (state.currentCandidate?.id === action.payload.id) {
          state.currentCandidate = action.payload;
        }
      })
      .addCase(updateCandidate.rejected, (state, action) => {
        state.loading.update = false;
        state.errors.update = action.error.message || 'Failed to update candidate';
      });

    // Delete candidate
    builder
      .addCase(deleteCandidate.pending, (state) => {
        state.loading.delete = true;
        state.errors.delete = null;
      })
      .addCase(deleteCandidate.fulfilled, (state, action) => {
        state.loading.delete = false;
        state.candidates = state.candidates.filter(c => c.id !== action.payload);
        state.pagination.total -= 1;
        
        // Clear current if it was deleted
        if (state.currentCandidate?.id === action.payload) {
          state.currentCandidate = null;
        }
        
        // Remove from selection
        state.bulkOperations.selectedIds = state.bulkOperations.selectedIds.filter(
          id => id !== action.payload
        );
      })
      .addCase(deleteCandidate.rejected, (state, action) => {
        state.loading.delete = false;
        state.errors.delete = action.error.message || 'Failed to delete candidate';
      });

    // Upload resume
    builder
      .addCase(uploadResume.pending, (state) => {
        state.loading.uploadResume = true;
        state.errors.uploadResume = null;
      })
      .addCase(uploadResume.fulfilled, (state, action) => {
        state.loading.uploadResume = false;
        
        // Update candidate with resume info
        const candidateId = action.payload.candidateId;
        const response = action.payload.response;
        
        // Generate resume URL from response id if not provided
        const resumeUrl = response.resume_url || `/api/v1/resume/${response.id}/download/`;
        
        // Update in list
        const index = state.candidates.findIndex(c => c.id === candidateId);
        if (index >= 0) {
          state.candidates[index].resume_url = resumeUrl;
          state.candidates[index].has_resume = true;
        }
        
        // Update current candidate
        if (state.currentCandidate?.id === candidateId) {
          state.currentCandidate.resume_url = resumeUrl;
          state.currentCandidate.has_resume = true;
        }
      })
      .addCase(uploadResume.rejected, (state, action) => {
        state.loading.uploadResume = false;
        state.errors.uploadResume = action.error.message || 'Failed to upload resume';
      });

    // Share candidate
    builder
      .addCase(shareCandidate.pending, (state) => {
        state.loading.share = true;
        state.errors.share = null;
      })
      .addCase(shareCandidate.fulfilled, (state, action) => {
        state.loading.share = false;
        
        // Update shared_with field
        const candidateId = action.payload.candidateId;
        const userIds = action.payload.userIds;
        
        // Update in list
        const index = state.candidates.findIndex(c => c.id === candidateId);
        if (index >= 0) {
          state.candidates[index].shared_with = [
            ...new Set([...state.candidates[index].shared_with, ...userIds])
          ];
        }
        
        // Update current candidate
        if (state.currentCandidate?.id === candidateId) {
          state.currentCandidate.shared_with = [
            ...new Set([...state.currentCandidate.shared_with, ...userIds])
          ];
        }
      })
      .addCase(shareCandidate.rejected, (state, action) => {
        state.loading.share = false;
        state.errors.share = action.error.message || 'Failed to share candidate';
      });

    // Bulk import
    builder
      .addCase(bulkImportCandidates.pending, (state) => {
        state.loading.bulkImport = true;
        state.errors.bulkImport = null;
        state.bulkOperations.operation = 'import';
        state.bulkOperations.progress = 0;
      })
      .addCase(bulkImportCandidates.fulfilled, (state) => {
        state.loading.bulkImport = false;
        state.bulkOperations.operation = null;
        state.bulkOperations.progress = 100;
        // Invalidate cache to force refresh
        state.cacheValid = false;
      })
      .addCase(bulkImportCandidates.rejected, (state, action) => {
        state.loading.bulkImport = false;
        state.errors.bulkImport = action.error.message || 'Failed to import candidates';
        state.bulkOperations.operation = null;
      });

    // Bulk export
    builder
      .addCase(bulkExportCandidates.pending, (state) => {
        state.loading.bulkExport = true;
        state.errors.bulkExport = null;
        state.bulkOperations.operation = 'export';
        state.bulkOperations.progress = 0;
      })
      .addCase(bulkExportCandidates.fulfilled, (state) => {
        state.loading.bulkExport = false;
        state.bulkOperations.operation = null;
        state.bulkOperations.progress = 100;
      })
      .addCase(bulkExportCandidates.rejected, (state, action) => {
        state.loading.bulkExport = false;
        state.errors.bulkExport = action.error.message || 'Failed to export candidates';
        state.bulkOperations.operation = null;
      });
  },
});

// Export actions
export const {
  setFilters,
  clearFilters,
  setPagination,
  setCurrentCandidate,
  clearCurrentCandidate,
  toggleCandidateSelection,
  selectAllCandidates,
  clearCandidateSelection,
  setBulkOperation,
  setBulkProgress,
  clearError,
  clearAllErrors,
  invalidateCache,
  updateCandidateLocal,
  addCandidateLocal,
  removeCandidateLocal,
} = candidateSlice.actions;

// Export reducer
export default candidateSlice.reducer;