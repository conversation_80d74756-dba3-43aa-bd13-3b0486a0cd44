import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { reportsService } from '@/services/reports.service';
import { Report, ReportTemplate, ReportStatistics } from '@/types/reports';

interface ReportsState {
  reports: Report[];
  templates: ReportTemplate[];
  statistics: ReportStatistics | null;
  selectedReport: Report | null;
  loading: boolean;
  error: string | null;
  filters: {
    status: string;
    type: string;
    search: string;
  };
  pagination: {
    skip: number;
    limit: number;
    total: number;
  };
}

const initialState: ReportsState = {
  reports: [],
  templates: [],
  statistics: null,
  selectedReport: null,
  loading: false,
  error: null,
  filters: {
    status: 'all',
    type: 'all',
    search: ''
  },
  pagination: {
    skip: 0,
    limit: 10,
    total: 0
  }
};

// Async thunks
export const fetchReportStatistics = createAsyncThunk(
  'reports/fetchStatistics',
  async () => {
    return await reportsService.getStatistics();
  }
);

export const fetchReports = createAsyncThunk(
  'reports/fetchReports',
  async (params: {
    status?: string;
    type?: string;
    search?: string;
    skip?: number;
    limit?: number;
  }) => {
    return await reportsService.getReports(params);
  }
);

export const fetchReportTemplates = createAsyncThunk(
  'reports/fetchTemplates',
  async () => {
    return await reportsService.getTemplates();
  }
);

export const generateReport = createAsyncThunk(
  'reports/generate',
  async (params: {
    templateId: string;
    title?: string;
    format: string;
    parameters: Record<string, any>;
  }) => {
    return await reportsService.generateReport(params);
  }
);

export const deleteReport = createAsyncThunk(
  'reports/delete',
  async (id: string) => {
    await reportsService.deleteReport(id);
    return id;
  }
);

export const downloadReport = createAsyncThunk(
  'reports/download',
  async (id: string) => {
    const blob = await reportsService.downloadReport(id);
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `report-${id}.pdf`;
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    return id;
  }
);

export const scheduleReport = createAsyncThunk(
  'reports/schedule',
  async (params: {
    id: string;
    schedule: {
      frequency: 'daily' | 'weekly' | 'monthly';
      time: string;
      recipients: string[];
    };
  }) => {
    return await reportsService.scheduleReport(params.id, params.schedule);
  }
);

export const shareReport = createAsyncThunk(
  'reports/share',
  async (params: { id: string; recipients: string[] }) => {
    await reportsService.shareReport(params.id, params.recipients);
    return params.id;
  }
);

export const regenerateReport = createAsyncThunk(
  'reports/regenerate',
  async (id: string) => {
    return await reportsService.regenerateReport(id);
  }
);

const reportsSlice = createSlice({
  name: 'reports',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<ReportsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPagination: (state, action: PayloadAction<Partial<ReportsState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    setSelectedReport: (state, action: PayloadAction<Report | null>) => {
      state.selectedReport = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    // Fetch statistics
    builder
      .addCase(fetchReportStatistics.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchReportStatistics.fulfilled, (state, action) => {
        state.statistics = action.payload;
        state.loading = false;
      })
      .addCase(fetchReportStatistics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch statistics';
      });

    // Fetch reports
    builder
      .addCase(fetchReports.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchReports.fulfilled, (state, action) => {
        state.reports = action.payload.items;
        state.pagination.total = action.payload.total;
        state.loading = false;
      })
      .addCase(fetchReports.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch reports';
      });

    // Fetch templates
    builder
      .addCase(fetchReportTemplates.fulfilled, (state, action) => {
        state.templates = action.payload;
      });

    // Generate report
    builder
      .addCase(generateReport.pending, (state) => {
        state.loading = true;
      })
      .addCase(generateReport.fulfilled, (state, action) => {
        state.reports.unshift(action.payload);
        state.loading = false;
      })
      .addCase(generateReport.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to generate report';
      });

    // Delete report
    builder
      .addCase(deleteReport.fulfilled, (state, action) => {
        state.reports = state.reports.filter(r => r.id !== action.payload);
      });

    // Regenerate report
    builder
      .addCase(regenerateReport.fulfilled, (state, action) => {
        const index = state.reports.findIndex(r => r.id === action.payload.id);
        if (index !== -1) {
          state.reports[index] = action.payload;
        }
      });

    // Schedule report
    builder
      .addCase(scheduleReport.fulfilled, (state, action) => {
        const index = state.reports.findIndex(r => r.id === action.payload.id);
        if (index !== -1) {
          state.reports[index] = action.payload;
        }
      });
  }
});

export const { setFilters, setPagination, setSelectedReport, clearError } = reportsSlice.actions;
export default reportsSlice.reducer;