/**
 * Integration tests for AI Questionnaire Generation Flow
 * Tests the complete user journey from form submission to progress monitoring
 */
import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AIQuestionnaireForm } from '@/components/ai-questionnaire/AIQuestionnaireForm';
import {
  QuestionnaireGenerateRequest,
  QuestionnaireGenerateResponse,
  GenerationProgress,
  GenerationStatus,
  QuestionType,
  QuestionCategory,
  DifficultyLevel
} from '@/types/questionnaire';
import aiQuestionnaireService from '@/services/aiQuestionnaireService';

// Mock the service
jest.mock('@/services/aiQuestionnaireService');
const mockAiQuestionnaireService = aiQuestionnaireService as jest.Mocked<typeof aiQuestionnaireService>;

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Integration test data - simulates realistic API responses
const mockProgressSequence: GenerationProgress[] = [
  {
    generation_id: 'gen-integration-test',
    status: GenerationStatus.PENDING,
    progress_percentage: 0,
    current_step: 'Initializing generation process...',
    estimated_remaining_time: 120,
    created_at: '2024-01-01T12:00:00Z',
    updated_at: '2024-01-01T12:00:00Z'
  },
  {
    generation_id: 'gen-integration-test',
    status: GenerationStatus.ANALYZING_REQUIREMENTS,
    progress_percentage: 15,
    current_step: 'Analyzing position requirements...',
    estimated_remaining_time: 100,
    created_at: '2024-01-01T12:00:00Z',
    updated_at: '2024-01-01T12:00:15Z'
  },
  {
    generation_id: 'gen-integration-test',
    status: GenerationStatus.GENERATING_QUESTIONS,
    progress_percentage: 40,
    current_step: 'Generating technical questions...',
    estimated_remaining_time: 75,
    created_at: '2024-01-01T12:00:00Z',
    updated_at: '2024-01-01T12:00:45Z'
  },
  {
    generation_id: 'gen-integration-test',
    status: GenerationStatus.GENERATING_QUESTIONS,
    progress_percentage: 65,
    current_step: 'Generating behavioral questions...',
    estimated_remaining_time: 45,
    created_at: '2024-01-01T12:00:00Z',
    updated_at: '2024-01-01T12:01:15Z'
  },
  {
    generation_id: 'gen-integration-test',
    status: GenerationStatus.REVIEWING_QUALITY,
    progress_percentage: 85,
    current_step: 'Reviewing question quality...',
    estimated_remaining_time: 20,
    created_at: '2024-01-01T12:00:00Z',
    updated_at: '2024-01-01T12:01:45Z'
  },
  {
    generation_id: 'gen-integration-test',
    status: GenerationStatus.FINALIZING,
    progress_percentage: 95,
    current_step: 'Finalizing questionnaire...',
    estimated_remaining_time: 5,
    created_at: '2024-01-01T12:00:00Z',
    updated_at: '2024-01-01T12:02:00Z'
  },
  {
    generation_id: 'gen-integration-test',
    status: GenerationStatus.COMPLETED,
    progress_percentage: 100,
    current_step: 'Generation completed successfully',
    estimated_remaining_time: 0,
    created_at: '2024-01-01T12:00:00Z',
    updated_at: '2024-01-01T12:02:15Z'
  }
];

const mockGeneratedQuestionnaire: QuestionnaireGenerateResponse = {
  questionnaire_id: '1234567890123456789',
  title: 'Senior Software Engineer Assessment - Cloud Computing Focus',
  description: 'Comprehensive assessment for senior software engineer position with emphasis on cloud computing and distributed systems',
  questions: [
    {
      id: '1234567890123456790',
      question_text: 'Describe your experience with microservices architecture and the challenges you\'ve faced when implementing them.',
      question_type: QuestionType.TEXT_LONG,
      category: QuestionCategory.TECHNICAL,
      difficulty: DifficultyLevel.HARD,
      estimated_time: 8,
      evaluation_criteria: [
        'Understanding of microservices principles',
        'Practical implementation experience',
        'Problem-solving approach',
        'Knowledge of related technologies'
      ],
      ai_rationale: 'This question evaluates both theoretical knowledge and practical experience with microservices, which is crucial for senior positions.'
    },
    {
      id: '1234567890123456791',
      question_text: 'How do you ensure code quality and maintainability in a fast-paced development environment?',
      question_type: QuestionType.TEXT_LONG,
      category: QuestionCategory.BEHAVIORAL,
      difficulty: DifficultyLevel.MEDIUM,
      estimated_time: 6,
      evaluation_criteria: [
        'Quality practices knowledge',
        'Team collaboration skills',
        'Process optimization mindset',
        'Leadership potential'
      ],
      ai_rationale: 'This behavioral question assesses the candidate\'s approach to maintaining quality while meeting deadlines.'
    },
    {
      id: '1234567890123456792',
      question_text: 'Given a scenario where your team needs to migrate a legacy monolith to cloud-native architecture, what would be your approach?',
      question_type: QuestionType.SCENARIO,
      category: QuestionCategory.SITUATIONAL,
      difficulty: DifficultyLevel.HARD,
      estimated_time: 10,
      evaluation_criteria: [
        'Strategic thinking',
        'Migration planning skills',
        'Risk assessment',
        'Communication abilities'
      ],
      ai_rationale: 'This scenario-based question evaluates strategic thinking and practical experience with cloud migration projects.'
    }
  ],
  estimated_duration: 45,
  generation_metadata: {
    generation_time: 135,
    ai_model_used: 'gpt-4',
    generation_id: 'gen-integration-test',
    quality_score: 0.92
  },
  created_at: '2024-01-01T12:02:15Z'
};

describe('AI Questionnaire Generation Flow Integration', () => {
  let progressIndex = 0;
  let onGenerateCallback: jest.Mock;
  let onProgressCallback: jest.Mock;
  let onErrorCallback: jest.Mock;

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    progressIndex = 0;
    
    onGenerateCallback = jest.fn();
    onProgressCallback = jest.fn();
    onErrorCallback = jest.fn();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe('Complete Success Flow', () => {
    it('should complete the full generation process successfully', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

      // Mock API responses
      mockAiQuestionnaireService.generateQuestionnaire.mockResolvedValue(mockGeneratedQuestionnaire);
      
      // Set up progress polling sequence
      mockAiQuestionnaireService.getGenerationProgress.mockImplementation(() => {
        const currentProgress = mockProgressSequence[progressIndex];
        progressIndex = Math.min(progressIndex + 1, mockProgressSequence.length - 1);
        return Promise.resolve(currentProgress);
      });

      render(
        <AIQuestionnaireForm
          onGenerate={onGenerateCallback}
          onProgress={onProgressCallback}
          onError={onErrorCallback}
        />
      );

      // Step 1: Fill out the form
      await user.type(screen.getByLabelText(/position type/i), 'Senior Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/large/i));

      // Adjust question count
      const slider = screen.getByRole('slider');
      fireEvent.change(slider, { target: { value: '25' } });

      // Select difficulty
      const hardDifficulty = screen.getByRole('radio', { name: /hard - advanced/i });
      await user.click(hardDifficulty);

      // Select focus areas
      const innovationCheckbox = screen.getByRole('checkbox', { name: /innovation/i });
      await user.click(innovationCheckbox);

      // Add additional requirements
      const additionalReqTextarea = screen.getByLabelText(/specific requirements/i);
      await user.type(additionalReqTextarea, 'Focus on cloud computing, microservices, and distributed systems');

      // Step 2: Submit the form
      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      // Verify API call
      await waitFor(() => {
        expect(mockAiQuestionnaireService.generateQuestionnaire).toHaveBeenCalledWith(
          expect.objectContaining({
            position_type: 'Senior Software Engineer',
            industry: 'Technology',
            company_size: 'large',
            question_count: 25,
            difficulty_level: 'hard',
            focus_areas: expect.arrayContaining(['digital_literacy', 'position_skill', 'innovation']),
            additional_requirements: 'Focus on cloud computing, microservices, and distributed systems'
          })
        );
      });

      expect(onGenerateCallback).toHaveBeenCalledWith(mockGeneratedQuestionnaire);

      // Step 3: Simulate progress polling
      expect(screen.getByText(/generating/i)).toBeInTheDocument();
      expect(submitButton).toBeDisabled();

      // Fast-forward through all progress updates
      for (let i = 0; i < mockProgressSequence.length; i++) {
        act(() => {
          jest.advanceTimersByTime(2000); // Advance by polling interval
        });

        await waitFor(() => {
          expect(mockAiQuestionnaireService.getGenerationProgress).toHaveBeenCalledWith('gen-integration-test');
        });

        // Verify progress callback is called with current progress
        expect(onProgressCallback).toHaveBeenCalledWith(mockProgressSequence[Math.min(i, mockProgressSequence.length - 1)]);

        // Check UI updates for specific progress states
        if (i < mockProgressSequence.length - 1) {
          const currentStep = mockProgressSequence[i];
          expect(screen.getByText(currentStep.current_step)).toBeInTheDocument();
          expect(screen.getByText(`${currentStep.progress_percentage}%`)).toBeInTheDocument();
        }
      }

      // Step 4: Verify completion
      await waitFor(() => {
        expect(screen.getByText('Generation completed successfully')).toBeInTheDocument();
        expect(screen.getByText('100%')).toBeInTheDocument();
      });

      // Verify no errors were called
      expect(onErrorCallback).not.toHaveBeenCalled();

      // Verify final callbacks
      expect(onGenerateCallback).toHaveBeenCalledTimes(1);
      expect(onProgressCallback).toHaveBeenCalledTimes(mockProgressSequence.length);
    });

    it('should handle complex form configurations correctly', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

      mockAiQuestionnaireService.generateQuestionnaire.mockResolvedValue(mockGeneratedQuestionnaire);
      mockAiQuestionnaireService.getGenerationProgress.mockResolvedValue(mockProgressSequence[mockProgressSequence.length - 1]);

      render(
        <AIQuestionnaireForm
          onGenerate={onGenerateCallback}
          onProgress={onProgressCallback}
          onError={onErrorCallback}
        />
      );

      // Test complex form configuration
      await user.type(screen.getByLabelText(/position type/i), 'Full Stack Developer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Finance'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/enterprise/i));

      // Set maximum questions
      const slider = screen.getByRole('slider');
      fireEvent.change(slider, { target: { value: '50' } });

      // Select expert difficulty
      const expertDifficulty = screen.getByRole('radio', { name: /expert - senior level/i });
      await user.click(expertDifficulty);

      // Select all focus areas
      const digitalLiteracyCheckbox = screen.getByRole('checkbox', { name: /digital literacy/i });
      const industrySkillCheckbox = screen.getByRole('checkbox', { name: /industry skills/i });
      const innovationCheckbox = screen.getByRole('checkbox', { name: /innovation/i });
      const learningPotentialCheckbox = screen.getByRole('checkbox', { name: /learning potential/i });
      
      await user.click(industrySkillCheckbox);
      await user.click(innovationCheckbox);
      await user.click(learningPotentialCheckbox);

      // Disable some question types
      const behavioralCheckbox = screen.getByRole('checkbox', { name: /behavioral questions/i });
      await user.click(behavioralCheckbox); // Uncheck

      // Select Chinese language
      const languageSelect = screen.getByLabelText(/language/i);
      await user.click(languageSelect);
      await user.click(screen.getByText('中文'));

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockAiQuestionnaireService.generateQuestionnaire).toHaveBeenCalledWith(
          expect.objectContaining({
            position_type: 'Full Stack Developer',
            industry: 'Finance',
            company_size: 'enterprise',
            question_count: 50,
            difficulty_level: 'expert',
            focus_areas: expect.arrayContaining([
              'digital_literacy',
              'position_skill',
              'industry_skill',
              'innovation',
              'learning_potential'
            ]),
            include_behavioral: false,
            include_technical: true,
            include_situational: true,
            language: 'zh'
          })
        );
      });
    });
  });

  describe('Error Handling Scenarios', () => {
    it('should handle generation API errors gracefully', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

      const apiError = {
        error_code: 'AI_GENERATION_FAILED',
        detail: 'AI service is temporarily unavailable. Please try again later.',
        statusCode: 503
      };

      mockAiQuestionnaireService.generateQuestionnaire.mockRejectedValue(apiError);

      render(
        <AIQuestionnaireForm
          onGenerate={onGenerateCallback}
          onProgress={onProgressCallback}
          onError={onErrorCallback}
        />
      );

      // Fill minimal required fields
      await user.type(screen.getByLabelText(/position type/i), 'Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(onErrorCallback).toHaveBeenCalledWith('AI service is temporarily unavailable. Please try again later.');
      });

      expect(onGenerateCallback).not.toHaveBeenCalled();
      expect(onProgressCallback).not.toHaveBeenCalled();
    });

    it('should handle progress polling failures', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

      mockAiQuestionnaireService.generateQuestionnaire.mockResolvedValue(mockGeneratedQuestionnaire);
      mockAiQuestionnaireService.getGenerationProgress.mockRejectedValue(
        new Error('Network error while fetching progress')
      );

      render(
        <AIQuestionnaireForm
          onGenerate={onGenerateCallback}
          onProgress={onProgressCallback}
          onError={onErrorCallback}
        />
      );

      // Submit form
      await user.type(screen.getByLabelText(/position type/i), 'Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      // Initial generation should succeed
      expect(onGenerateCallback).toHaveBeenCalledWith(mockGeneratedQuestionnaire);

      // But progress polling should fail
      act(() => {
        jest.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        expect(onErrorCallback).toHaveBeenCalledWith('Failed to check generation progress');
      });
    });

    it('should handle generation failure during progress', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

      const failedProgress: GenerationProgress = {
        generation_id: 'gen-integration-test',
        status: GenerationStatus.FAILED,
        progress_percentage: 45,
        current_step: 'Generation failed due to AI service error',
        estimated_remaining_time: 0,
        error_message: 'AI model encountered an internal error',
        created_at: '2024-01-01T12:00:00Z',
        updated_at: '2024-01-01T12:01:30Z'
      };

      mockAiQuestionnaireService.generateQuestionnaire.mockResolvedValue(mockGeneratedQuestionnaire);
      mockAiQuestionnaireService.getGenerationProgress
        .mockResolvedValueOnce(mockProgressSequence[0])
        .mockResolvedValueOnce(mockProgressSequence[1])
        .mockResolvedValue(failedProgress);

      render(
        <AIQuestionnaireForm
          onGenerate={onGenerateCallback}
          onProgress={onProgressCallback}
          onError={onErrorCallback}
        />
      );

      // Submit form
      await user.type(screen.getByLabelText(/position type/i), 'Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      // Initial generation succeeds
      expect(onGenerateCallback).toHaveBeenCalledWith(mockGeneratedQuestionnaire);

      // Progress through initial steps
      act(() => {
        jest.advanceTimersByTime(2000);
      });
      act(() => {
        jest.advanceTimersByTime(2000);
      });
      act(() => {
        jest.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        expect(onErrorCallback).toHaveBeenCalledWith('AI model encountered an internal error');
      });

      expect(onProgressCallback).toHaveBeenCalledWith(failedProgress);
    });
  });

  describe('Form Validation Integration', () => {
    it('should prevent submission with invalid form data', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

      render(
        <AIQuestionnaireForm
          onGenerate={onGenerateCallback}
          onProgress={onProgressCallback}
          onError={onErrorCallback}
        />
      );

      // Try to submit without filling required fields
      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      // Should show validation errors, not call API
      await waitFor(() => {
        expect(screen.getByText(/position type is required/i)).toBeInTheDocument();
        expect(screen.getByText(/industry is required/i)).toBeInTheDocument();
        expect(screen.getByText(/company size is required/i)).toBeInTheDocument();
      });

      expect(mockAiQuestionnaireService.generateQuestionnaire).not.toHaveBeenCalled();
      expect(onGenerateCallback).not.toHaveBeenCalled();
      expect(onProgressCallback).not.toHaveBeenCalled();
      expect(onErrorCallback).not.toHaveBeenCalled();
    });

    it('should validate focus area requirements', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

      render(
        <AIQuestionnaireForm
          onGenerate={onGenerateCallback}
          onProgress={onProgressCallback}
          onError={onErrorCallback}
        />
      );

      // Fill required fields
      await user.type(screen.getByLabelText(/position type/i), 'Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      // Uncheck all focus areas (default has some checked)
      const digitalLiteracyCheckbox = screen.getByRole('checkbox', { name: /digital literacy/i });
      const positionSkillCheckbox = screen.getByRole('checkbox', { name: /position skills/i });
      
      await user.click(digitalLiteracyCheckbox); // Uncheck
      await user.click(positionSkillCheckbox); // Uncheck

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/at least one focus area is required/i)).toBeInTheDocument();
      });

      expect(mockAiQuestionnaireService.generateQuestionnaire).not.toHaveBeenCalled();
    });
  });

  describe('Real-world Usage Patterns', () => {
    it('should handle typical software engineer questionnaire generation', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });

      mockAiQuestionnaireService.generateQuestionnaire.mockResolvedValue(mockGeneratedQuestionnaire);
      mockAiQuestionnaireService.getGenerationProgress.mockImplementation(() => {
        const currentProgress = mockProgressSequence[progressIndex];
        progressIndex = Math.min(progressIndex + 1, mockProgressSequence.length - 1);
        return Promise.resolve(currentProgress);
      });

      render(
        <AIQuestionnaireForm
          onGenerate={onGenerateCallback}
          onProgress={onProgressCallback}
          onError={onErrorCallback}
        />
      );

      // Realistic software engineer configuration
      await user.type(screen.getByLabelText(/position type/i), 'Senior Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/large/i));

      // Adjust for senior level
      const slider = screen.getByRole('slider');
      fireEvent.change(slider, { target: { value: '30' } });

      const hardDifficulty = screen.getByRole('radio', { name: /hard - advanced/i });
      await user.click(hardDifficulty);

      // Select relevant focus areas
      const innovationCheckbox = screen.getByRole('checkbox', { name: /innovation/i });
      const learningPotentialCheckbox = screen.getByRole('checkbox', { name: /learning potential/i });
      
      await user.click(innovationCheckbox);
      await user.click(learningPotentialCheckbox);

      const additionalReqTextarea = screen.getByLabelText(/specific requirements/i);
      await user.type(additionalReqTextarea, 'Focus on system design, scalability, and team leadership capabilities');

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      // Verify comprehensive request
      await waitFor(() => {
        expect(mockAiQuestionnaireService.generateQuestionnaire).toHaveBeenCalledWith(
          expect.objectContaining({
            position_type: 'Senior Software Engineer',
            industry: 'Technology',
            company_size: 'large',
            question_count: 30,
            difficulty_level: 'hard',
            focus_areas: expect.arrayContaining([
              'digital_literacy',
              'position_skill',
              'innovation',
              'learning_potential'
            ]),
            include_behavioral: true,
            include_technical: true,
            include_situational: true,
            language: 'en',
            additional_requirements: 'Focus on system design, scalability, and team leadership capabilities'
          })
        );
      });

      // Complete the generation process
      for (let i = 0; i < mockProgressSequence.length; i++) {
        act(() => {
          jest.advanceTimersByTime(2000);
        });
      }

      await waitFor(() => {
        expect(screen.getByText('Generation completed successfully')).toBeInTheDocument();
      });

      expect(onGenerateCallback).toHaveBeenCalledWith(mockGeneratedQuestionnaire);
      expect(onErrorCallback).not.toHaveBeenCalled();
    });

    it('should handle estimated duration calculations correctly', async () => {
      render(
        <AIQuestionnaireForm
          onGenerate={onGenerateCallback}
          onProgress={onProgressCallback}
          onError={onErrorCallback}
        />
      );

      // Check default estimation
      expect(screen.getByText(/estimated duration/i)).toBeInTheDocument();

      // Change question count and verify estimation updates
      const slider = screen.getByRole('slider');
      fireEvent.change(slider, { target: { value: '40' } });

      await waitFor(() => {
        expect(screen.getByText('40')).toBeInTheDocument(); // Question count
        // Duration should update accordingly
        expect(screen.getByText(/estimated duration/i)).toBeInTheDocument();
      });

      // Select more focus areas to increase complexity
      const innovationCheckbox = screen.getByRole('checkbox', { name: /innovation/i });
      const learningPotentialCheckbox = screen.getByRole('checkbox', { name: /learning potential/i });
      const industrySkillCheckbox = screen.getByRole('checkbox', { name: /industry skills/i });
      
      await userEvent.click(innovationCheckbox);
      await userEvent.click(learningPotentialCheckbox);
      await userEvent.click(industrySkillCheckbox);

      // Duration should increase with complexity
      // The exact calculation is internal, but we verify it's still showing
      expect(screen.getByText(/estimated duration/i)).toBeInTheDocument();
    });
  });
});