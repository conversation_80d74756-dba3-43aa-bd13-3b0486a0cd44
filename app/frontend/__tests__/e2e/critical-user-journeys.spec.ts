/**
 * E2E tests for critical user journeys in candidate management
 */
import { test, expect } from '@playwright/test'
import { AuthHelper } from './utils/auth-helper'
import { CandidateHelper } from './utils/candidate-helper'
import { testCandidates, searchQueries, filterOptions } from './fixtures/test-data'

// User Journey 1: Login → View candidates list → Filter/search → View candidate details
test.describe('User Journey 1: Browse and View Candidates', () => {
  test('should complete full candidate browsing journey', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    // Step 1: Login as admin user
    await auth.login('admin')
    
    // Step 2: Navigate to candidates page
    await candidate.goToCandidatesPage()
    
    // Verify candidates list is displayed
    await expect(page.locator('table')).toBeVisible()
    await expect(page.locator('thead th:has-text("Name")')).toBeVisible()
    await expect(page.locator('thead th:has-text("Email")')).toBeVisible()
    await expect(page.locator('thead th:has-text("Status")')).toBeVisible()
    
    // Step 3: Search for specific candidate
    await candidate.searchCandidates(searchQueries.byName)
    
    // Verify search results
    const candidateCount = await candidate.getCandidateCount()
    expect(candidateCount).toBeGreaterThan(0)
    
    // Step 4: Apply advanced filters
    await candidate.applyAdvancedFilters({
      status: filterOptions.status.new,
      minExperience: filterOptions.experience.min,
      maxExperience: filterOptions.experience.max,
    })
    
    // Verify filters are applied
    await expect(page.locator('text=Advanced Filters')).toBeVisible()
    
    // Step 5: View candidate details
    const firstCandidateName = await page.locator('tbody tr:first-child td:nth-child(2)').textContent()
    if (firstCandidateName) {
      await candidate.viewCandidateDetails(firstCandidateName.trim())
      
      // Verify details page
      await expect(page.locator('text=Candidate Details')).toBeVisible()
      await expect(page.locator('text=Contact Information')).toBeVisible()
      await expect(page.locator('text=Professional Information')).toBeVisible()
    }
    
    // Step 6: Navigate back to list
    await page.goBack()
    await expect(page.locator('table')).toBeVisible()
    
    // Step 7: Clear filters
    await candidate.clearFilters()
    
    // Verify all candidates are shown again
    const finalCount = await candidate.getCandidateCount()
    expect(finalCount).toBeGreaterThanOrEqual(candidateCount)
  })

  test('should handle search with no results gracefully', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    await auth.login('admin')
    await candidate.goToCandidatesPage()
    
    // Search for non-existent candidate
    await candidate.searchCandidates(searchQueries.noResults)
    
    // Verify no results message
    await expect(page.locator('text=No candidates found')).toBeVisible()
    await expect(page.locator('text=Try adjusting your search criteria')).toBeVisible()
  })
})

// User Journey 2: Create new candidate → Add basic info → Upload resume → Submit → Verify creation
test.describe('User Journey 2: Create New Candidate', () => {
  test('should create candidate with basic information', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    // Step 1: Login as admin user
    await auth.login('admin')
    
    // Step 2: Navigate to create candidate page
    await candidate.goToCreateCandidatePage()
    
    // Step 3: Fill candidate form with basic information
    await candidate.fillCandidateForm(testCandidates.newCandidate)
    
    // Step 4: Submit form
    await candidate.submitCandidateForm()
    
    // Step 5: Verify successful creation
    await expect(page.locator('text=Candidate created successfully')).toBeVisible()
    
    // Step 6: Verify redirect to candidate details or list
    const url = page.url()
    expect(url).toMatch(/\/candidates(\/[^\/]+)?$/)
    
    // Step 7: Navigate to candidates list and verify new candidate exists
    await candidate.goToCandidatesPage()
    const exists = await candidate.verifyCandidateExists(testCandidates.newCandidate.name)
    expect(exists).toBe(true)
  })

  test('should create candidate with resume upload', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    await auth.login('admin')
    
    // Create candidate with resume
    await candidate.createCandidateWithResume({
      ...testCandidates.newCandidate,
      name: 'Candidate with Resume',
    })
    
    // Verify creation and resume attachment
    await expect(page.locator('text=Resume uploaded successfully')).toBeVisible()
    
    // Navigate to candidates list and verify candidate with resume icon
    await candidate.goToCandidatesPage()
    const candidateRow = page.locator('tr:has-text("Candidate with Resume")')
    await expect(candidateRow.locator('[data-testid="resume-icon"]')).toBeVisible()
  })

  test('should validate required fields', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    await auth.login('admin')
    await candidate.goToCreateCandidatePage()
    
    // Try to submit empty form
    await candidate.submitCandidateForm()
    
    // Verify validation errors
    await expect(page.locator('text=Name is required')).toBeVisible()
    await expect(page.locator('text=Email is required')).toBeVisible()
    
    // Fill required fields and verify validation passes
    await page.fill('[name="name"]', 'Test Name')
    await page.fill('[name="email"]', '<EMAIL>')
    
    await candidate.submitCandidateForm()
    
    // Should now succeed
    await expect(page.locator('text=Candidate created successfully')).toBeVisible()
  })

  test('should validate email format', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    await auth.login('admin')
    await candidate.goToCreateCandidatePage()
    
    // Fill form with invalid email
    await page.fill('[name="name"]', 'Test Name')
    await page.fill('[name="email"]', 'invalid-email')
    
    await candidate.submitCandidateForm()
    
    // Verify email validation error
    await expect(page.locator('text=Please enter a valid email address')).toBeVisible()
  })
})

// User Journey 3: Edit existing candidate → Update information → Save → Verify changes
test.describe('User Journey 3: Edit Existing Candidate', () => {
  test('should edit candidate information successfully', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    // Step 1: Login and navigate to candidates
    await auth.login('admin')
    await candidate.goToCandidatesPage()
    
    // Step 2: Find an existing candidate to edit
    const firstCandidateName = await page.locator('tbody tr:first-child td:nth-child(2)').textContent()
    
    if (firstCandidateName) {
      const candidateName = firstCandidateName.trim()
      
      // Step 3: Edit the candidate
      await candidate.editCandidate(candidateName, testCandidates.candidateUpdate)
      
      // Step 4: Verify changes are saved
      await expect(page.locator('text=Candidate updated successfully')).toBeVisible()
      
      // Step 5: Navigate back to list and verify changes
      await candidate.goToCandidatesPage()
      
      if (testCandidates.candidateUpdate.name) {
        const exists = await candidate.verifyCandidateExists(testCandidates.candidateUpdate.name)
        expect(exists).toBe(true)
      }
    }
  })

  test('should upload resume for existing candidate', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    await auth.login('admin')
    await candidate.goToCandidatesPage()
    
    // Find a candidate without resume
    const firstCandidateName = await page.locator('tbody tr:first-child td:nth-child(2)').textContent()
    
    if (firstCandidateName) {
      const candidateName = firstCandidateName.trim()
      
      // Upload resume
      await candidate.uploadResumeForCandidate(candidateName)
      
      // Verify resume upload success
      await expect(page.locator('text=Resume uploaded successfully')).toBeVisible()
      
      // Navigate back to list and verify resume icon appears
      await candidate.goToCandidatesPage()
      const candidateRow = page.locator(`tr:has-text("${candidateName}")`)
      await expect(candidateRow.locator('[data-testid="resume-icon"]')).toBeVisible()
    }
  })

  test('should handle concurrent edit conflicts', async ({ page, context }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    // This test simulates what would happen if two users try to edit the same candidate
    await auth.login('admin')
    await candidate.goToCandidatesPage()
    
    const firstCandidateName = await page.locator('tbody tr:first-child td:nth-child(2)').textContent()
    
    if (firstCandidateName) {
      const candidateName = firstCandidateName.trim()
      
      // Navigate to edit page
      const row = page.locator(`tr:has-text("${candidateName}")`)
      const moreButton = row.locator('[data-testid="more-actions"]')
      await moreButton.click()
      await page.click('text=Edit')
      
      // Make changes but don't submit yet
      await page.fill('[name="notes"]', 'Updated notes from test')
      
      // Simulate another user making changes (this would normally be handled by the backend)
      // For now, just ensure the form still works
      await page.click('button[type="submit"]')
      
      // Should either succeed or show conflict resolution
      const success = await page.locator('text=Candidate updated successfully').isVisible()
      const conflict = await page.locator('text=Conflict detected').isVisible()
      
      expect(success || conflict).toBe(true)
    }
  })
})

// User Journey 4: Bulk operations → Select multiple candidates → Export → Verify download
test.describe('User Journey 4: Bulk Operations', () => {
  test('should select multiple candidates and export', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    // Step 1: Login and navigate to candidates
    await auth.login('admin')
    await candidate.goToCandidatesPage()
    
    // Step 2: Select multiple candidates
    await candidate.selectAllCandidates()
    
    // Step 3: Verify bulk operations panel appears
    await expect(page.locator('text=selected')).toBeVisible()
    await expect(page.locator('text=Export Selected')).toBeVisible()
    
    // Step 4: Export selected candidates
    const download = await candidate.exportSelectedCandidates()
    
    // Step 5: Verify download
    expect(download.suggestedFilename()).toMatch(/\.xlsx?$/)
    
    // Step 6: Clear selection
    await page.click('text=Clear Selection')
    
    // Verify bulk operations panel disappears
    await expect(page.locator('text=Export Selected')).not.toBeVisible()
  })

  test('should handle individual candidate selection', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    await auth.login('admin')
    await candidate.goToCandidatesPage()
    
    // Get names of first few candidates
    const candidateNames = await page.locator('tbody tr td:nth-child(2)').allTextContents()
    const selectedNames = candidateNames.slice(0, 2)
    
    // Select specific candidates
    await candidate.selectCandidates(selectedNames)
    
    // Verify selection count
    const selectionText = await page.locator('text=selected').textContent()
    expect(selectionText).toContain('2')
    
    // Export selected
    const download = await candidate.exportSelectedCandidates()
    expect(download.suggestedFilename()).toMatch(/\.xlsx?$/)
  })

  test('should handle bulk delete operation', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    await auth.login('admin')
    await candidate.goToCandidatesPage()
    
    // Create test candidates first (to avoid deleting real data)
    await candidate.createCandidate({
      ...testCandidates.newCandidate,
      name: 'Bulk Delete Test 1',
      email: '<EMAIL>',
    })
    
    await candidate.createCandidate({
      ...testCandidates.newCandidate,
      name: 'Bulk Delete Test 2', 
      email: '<EMAIL>',
    })
    
    // Navigate back to list
    await candidate.goToCandidatesPage()
    
    // Select test candidates
    await candidate.selectCandidates(['Bulk Delete Test 1', 'Bulk Delete Test 2'])
    
    // Perform bulk delete
    await page.click('text=Delete Selected')
    await page.click('text=Confirm Delete')
    
    // Verify deletion success
    await expect(page.locator('text=Candidates deleted successfully')).toBeVisible()
    
    // Verify candidates are removed from list
    const exists1 = await candidate.verifyCandidateExists('Bulk Delete Test 1')
    const exists2 = await candidate.verifyCandidateExists('Bulk Delete Test 2')
    
    expect(exists1).toBe(false)
    expect(exists2).toBe(false)
  })

  test('should handle pagination during bulk operations', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    await auth.login('admin')
    await candidate.goToCandidatesPage()
    
    // If there's pagination, test cross-page selection
    const nextButton = page.locator('text=Next')
    const isNextButtonVisible = await nextButton.isVisible()
    
    if (isNextButtonVisible && !await nextButton.isDisabled()) {
      // Select candidates on first page
      await candidate.selectCandidates(['John Doe']) // Assuming this exists
      
      // Navigate to next page
      await candidate.goToNextPage()
      
      // Selection should be maintained
      await expect(page.locator('text=1 selected')).toBeVisible()
      
      // Select additional candidates on second page
      const candidateNames = await page.locator('tbody tr td:nth-child(2)').allTextContents()
      if (candidateNames.length > 0) {
        await candidate.selectCandidates([candidateNames[0]])
        
        // Should now show 2 selected
        await expect(page.locator('text=2 selected')).toBeVisible()
      }
      
      // Export should include candidates from both pages
      const download = await candidate.exportSelectedCandidates()
      expect(download.suggestedFilename()).toMatch(/\.xlsx?$/)
    }
  })

  test('should validate permissions for bulk operations', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    // Test with limited permissions user
    await auth.login('recruiter') // Assuming recruiter has read-only access
    await candidate.goToCandidatesPage()
    
    // Try to select candidates
    await candidate.selectAllCandidates()
    
    // Export button should not be visible for users without export permission
    const exportButton = page.locator('text=Export Selected')
    const deleteButton = page.locator('text=Delete Selected')
    
    // Verify restricted access
    if (await exportButton.isVisible()) {
      // If button is visible, clicking should show permission error
      await exportButton.click()
      await expect(page.locator('text=Insufficient permissions')).toBeVisible()
    }
    
    if (await deleteButton.isVisible()) {
      await deleteButton.click()
      await expect(page.locator('text=Insufficient permissions')).toBeVisible()
    }
  })
})

// Cross-cutting concerns tests
test.describe('Cross-cutting Concerns', () => {
  test('should maintain responsive design on mobile devices', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 })
    
    await auth.login('admin')
    await candidate.goToCandidatesPage()
    
    // Verify mobile-friendly layout
    await expect(page.locator('table')).toBeVisible()
    
    // Check that horizontal scroll is available if needed
    const tableWidth = await page.locator('table').boundingBox()
    expect(tableWidth?.width).toBeGreaterThan(0)
    
    // Test mobile navigation
    await page.click('[data-testid="mobile-menu"]')
    await expect(page.locator('[data-testid="mobile-nav"]')).toBeVisible()
  })

  test('should handle network failures gracefully', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    await auth.login('admin')
    await candidate.goToCandidatesPage()
    
    // Simulate network failure
    await page.route('**/api/v1/candidates/**', route => {
      route.abort('failed')
    })
    
    // Try to refresh candidates
    await page.reload()
    
    // Should show error message
    await expect(page.locator('text=Failed to load candidates')).toBeVisible()
    await expect(page.locator('text=Retry')).toBeVisible()
    
    // Restore network and retry
    await page.unroute('**/api/v1/candidates/**')
    await page.click('text=Retry')
    
    // Should load successfully
    await expect(page.locator('table')).toBeVisible()
  })

  test('should handle session expiration', async ({ page }) => {
    const auth = new AuthHelper(page)
    const candidate = new CandidateHelper(page)

    await auth.login('admin')
    await candidate.goToCandidatesPage()
    
    // Simulate session expiration by returning 401
    await page.route('**/api/v1/candidates/**', route => {
      route.fulfill({ status: 401, body: 'Unauthorized' })
    })
    
    // Try to perform an action
    await page.reload()
    
    // Should redirect to login
    await expect(page).toHaveURL('/login')
    await expect(page.locator('text=Session expired')).toBeVisible()
  })
})