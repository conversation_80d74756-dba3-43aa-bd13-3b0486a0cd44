import { test, expect } from '@playwright/test';

test.describe('Public Application Flow', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to public application page
    await page.goto('/apply/software-engineer');
  });

  test('should display application form', async ({ page }) => {
    // Check form title is visible
    await expect(page.locator('h1')).toContainText('Software Engineer Application');
    
    // Check form fields are present
    await expect(page.locator('input[name="full_name"]')).toBeVisible();
    await expect(page.locator('input[name="email"]')).toBeVisible();
    await expect(page.locator('textarea[name="cover_letter"]')).toBeVisible();
  });

  test('should validate required fields', async ({ page }) => {
    // Try to submit without filling required fields
    await page.locator('button[type="submit"]').click();
    
    // Check validation messages
    await expect(page.locator('text=Full name is required')).toBeVisible();
    await expect(page.locator('text=Email is required')).toBeVisible();
  });

  test('should complete multi-step application', async ({ page }) => {
    // Step 1: Fill personal information
    await page.fill('input[name="full_name"]', 'John Doe');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="phone"]', '+1234567890');
    
    // Click next
    await page.locator('button:has-text("Next")').click();
    
    // Step 2: Upload resume
    await expect(page.locator('text=Upload Your Resume')).toBeVisible();
    
    // Upload file
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('button:has-text("Browse")').click();
    const fileChooser = await fileChooserPromise;
    await fileChooser.setFiles('./tests/fixtures/sample-resume.pdf');
    
    // Click next
    await page.locator('button:has-text("Next")').click();
    
    // Step 3: Answer questionnaire
    await expect(page.locator('text=Additional Questions')).toBeVisible();
    
    // Answer questions
    await page.locator('label:has-text("Yes")').first().click();
    await page.fill('textarea[name="experience"]', 'I have 5 years of experience...');
    
    // Submit application
    await page.locator('button:has-text("Submit Application")').click();
    
    // Check success message
    await expect(page.locator('text=Application submitted successfully')).toBeVisible();
  });

  test('should handle file upload', async ({ page }) => {
    // Navigate to resume upload step
    await page.fill('input[name="full_name"]', 'Jane Smith');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.locator('button:has-text("Next")').click();
    
    // Test drag and drop
    const dropzone = page.locator('[data-testid="resume-dropzone"]');
    await expect(dropzone).toBeVisible();
    
    // Simulate file drop
    await dropzone.hover();
    
    // Test file size validation
    const largeFile = './tests/fixtures/large-file.pdf'; // > 5MB
    const fileChooserPromise = page.waitForEvent('filechooser');
    await page.locator('button:has-text("Browse")').click();
    const fileChooser = await fileChooserPromise;
    
    // This should show error for large file
    await fileChooser.setFiles(largeFile);
    await expect(page.locator('text=File size must be less than 5MB')).toBeVisible();
  });

  test('should handle conditional questions', async ({ page }) => {
    // Fill initial form
    await page.fill('input[name="full_name"]', 'Test User');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.locator('button:has-text("Next")').click();
    
    // Skip resume for this test
    await page.locator('button:has-text("Skip")').click();
    
    // Answer conditional question
    await page.locator('label:has-text("Yes")').click();
    
    // Check conditional field appears
    await expect(page.locator('input[name="current_employer"]')).toBeVisible();
    
    // Change answer
    await page.locator('label:has-text("No")').click();
    
    // Check conditional field disappears
    await expect(page.locator('input[name="current_employer"]')).not.toBeVisible();
  });

  test('should save draft and resume', async ({ page }) => {
    // Fill partial form
    await page.fill('input[name="full_name"]', 'Draft User');
    await page.fill('input[name="email"]', '<EMAIL>');
    
    // Save draft
    await page.locator('button:has-text("Save Draft")').click();
    
    // Check draft saved message
    await expect(page.locator('text=Draft saved')).toBeVisible();
    
    // Reload page
    await page.reload();
    
    // Check form is pre-filled
    await expect(page.locator('input[name="full_name"]')).toHaveValue('Draft User');
    await expect(page.locator('input[name="email"]')).toHaveValue('<EMAIL>');
  });
});

test.describe('Admin Forms Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'test123');
    await page.locator('button[type="submit"]').click();
    
    // Wait for redirect
    await page.waitForURL('/recruitment');
    
    // Navigate to forms management
    await page.goto('/applications/forms');
  });

  test('should display forms list', async ({ page }) => {
    // Check page title
    await expect(page.locator('h1')).toContainText('Application Forms');
    
    // Check table headers
    await expect(page.locator('th:has-text("Title")')).toBeVisible();
    await expect(page.locator('th:has-text("Type")')).toBeVisible();
    await expect(page.locator('th:has-text("Status")')).toBeVisible();
    await expect(page.locator('th:has-text("Submissions")')).toBeVisible();
  });

  test('should create new form', async ({ page }) => {
    // Click create button
    await page.locator('button:has-text("Create Form")').click();
    
    // Fill form details
    await page.fill('input[name="title"]', 'New Test Form');
    await page.fill('input[name="slug"]', 'new-test-form');
    await page.fill('textarea[name="description"]', 'This is a test form');
    
    // Add a text field
    await page.locator('button:has-text("Add Field")').click();
    await page.fill('input[name="fields[0].label"]', 'Test Field');
    await page.fill('input[name="fields[0].name"]', 'test_field');
    await page.selectOption('select[name="fields[0].type"]', 'text');
    await page.check('input[name="fields[0].required"]');
    
    // Save form
    await page.locator('button:has-text("Save Form")').click();
    
    // Check success message
    await expect(page.locator('text=Form created successfully')).toBeVisible();
    
    // Check form appears in list
    await expect(page.locator('td:has-text("New Test Form")')).toBeVisible();
  });

  test('should edit existing form', async ({ page }) => {
    // Click edit on first form
    await page.locator('button[aria-label="Edit form"]').first().click();
    
    // Update form title
    await page.fill('input[name="title"]', 'Updated Form Title');
    
    // Add another field
    await page.locator('button:has-text("Add Field")').click();
    const newFieldIndex = await page.locator('[data-testid="form-field"]').count() - 1;
    await page.fill(`input[name="fields[${newFieldIndex}].label"]`, 'New Field');
    await page.fill(`input[name="fields[${newFieldIndex}].name"]`, 'new_field');
    
    // Save changes
    await page.locator('button:has-text("Save Changes")').click();
    
    // Check success message
    await expect(page.locator('text=Form updated successfully')).toBeVisible();
  });

  test('should reorder form fields with drag and drop', async ({ page }) => {
    // Create or edit a form with multiple fields
    await page.locator('button:has-text("Create Form")').click();
    
    // Add multiple fields
    for (let i = 0; i < 3; i++) {
      await page.locator('button:has-text("Add Field")').click();
      await page.fill(`input[name="fields[${i}].label"]`, `Field ${i + 1}`);
      await page.fill(`input[name="fields[${i}].name"]`, `field_${i + 1}`);
    }
    
    // Get initial order
    const firstFieldLabel = await page.locator('[data-testid="form-field"]').first().locator('input[name*="label"]').inputValue();
    
    // Drag first field to last position
    const firstField = page.locator('[data-testid="form-field"]').first();
    const lastField = page.locator('[data-testid="form-field"]').last();
    
    await firstField.dragTo(lastField);
    
    // Check order changed
    const newLastFieldLabel = await page.locator('[data-testid="form-field"]').last().locator('input[name*="label"]').inputValue();
    expect(newLastFieldLabel).toBe(firstFieldLabel);
  });

  test('should preview form', async ({ page }) => {
    // Click preview on first form
    await page.locator('button[aria-label="Preview form"]').first().click();
    
    // Check preview modal opens
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('text=Form Preview')).toBeVisible();
    
    // Check form fields are displayed
    await expect(page.locator('[role="dialog"] input')).toBeVisible();
    
    // Close preview
    await page.locator('button[aria-label="Close"]').click();
    await expect(page.locator('[role="dialog"]')).not.toBeVisible();
  });

  test('should delete form', async ({ page }) => {
    // Get form title to verify deletion
    const formTitle = await page.locator('tbody tr').first().locator('td').first().textContent();
    
    // Click delete on first form
    await page.locator('button[aria-label="Delete form"]').first().click();
    
    // Confirm deletion
    await page.locator('button:has-text("Confirm")').click();
    
    // Check success message
    await expect(page.locator('text=Form deleted successfully')).toBeVisible();
    
    // Check form is removed from list
    await expect(page.locator(`td:has-text("${formTitle}")`)).not.toBeVisible();
  });
});

test.describe('Submissions Management', () => {
  test.beforeEach(async ({ page }) => {
    // Login as admin
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'test123');
    await page.locator('button[type="submit"]').click();
    
    // Wait for redirect and navigate to submissions
    await page.waitForURL('/recruitment');
    await page.goto('/applications/submissions');
  });

  test('should display submissions list', async ({ page }) => {
    // Check page elements
    await expect(page.locator('h1')).toContainText('Form Submissions');
    
    // Check filters
    await expect(page.locator('select[name="form"]')).toBeVisible();
    await expect(page.locator('select[name="status"]')).toBeVisible();
    
    // Check table
    await expect(page.locator('table')).toBeVisible();
  });

  test('should filter submissions', async ({ page }) => {
    // Filter by status
    await page.selectOption('select[name="status"]', 'pending');
    
    // Check filtered results
    await expect(page.locator('td:has-text("Pending")')).toBeVisible();
    
    // Filter by form
    await page.selectOption('select[name="form"]', { index: 1 });
    
    // Check results updated
    await page.waitForTimeout(500); // Wait for filter to apply
  });

  test('should view submission details', async ({ page }) => {
    // Click on first submission
    await page.locator('tbody tr').first().click();
    
    // Check detail view opens
    await expect(page.locator('[role="dialog"]')).toBeVisible();
    await expect(page.locator('text=Submission Details')).toBeVisible();
    
    // Check submission data is displayed
    await expect(page.locator('[role="dialog"] dl')).toBeVisible();
  });

  test('should approve submission', async ({ page }) => {
    // Click on pending submission
    await page.locator('tr:has-text("Pending")').first().click();
    
    // Click approve button
    await page.locator('button:has-text("Approve")').click();
    
    // Add approval notes
    await page.fill('textarea[name="notes"]', 'Approved for next stage');
    
    // Confirm approval
    await page.locator('button:has-text("Confirm Approval")').click();
    
    // Check success message
    await expect(page.locator('text=Submission approved')).toBeVisible();
    
    // Check status updated
    await expect(page.locator('tr:has-text("Approved")')).toBeVisible();
  });

  test('should reject submission', async ({ page }) => {
    // Click on pending submission
    await page.locator('tr:has-text("Pending")').first().click();
    
    // Click reject button
    await page.locator('button:has-text("Reject")').click();
    
    // Add rejection reason
    await page.fill('textarea[name="reason"]', 'Incomplete information');
    
    // Confirm rejection
    await page.locator('button:has-text("Confirm Rejection")').click();
    
    // Check success message
    await expect(page.locator('text=Submission rejected')).toBeVisible();
    
    // Check status updated
    await expect(page.locator('tr:has-text("Rejected")')).toBeVisible();
  });

  test('should export submissions', async ({ page }) => {
    // Click export button
    const downloadPromise = page.waitForEvent('download');
    await page.locator('button:has-text("Export")').click();
    
    // Select export format
    await page.locator('button:has-text("Export as CSV")').click();
    
    // Check download starts
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toContain('.csv');
  });
});