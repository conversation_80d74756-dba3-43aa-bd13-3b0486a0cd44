/**
 * Authentication helper for E2E tests
 */
import { Page, expect } from '@playwright/test'
import { testUsers } from '../fixtures/test-data'

export class AuthHelper {
  constructor(private page: Page) {}

  /**
   * Login with specified user role
   */
  async login(role: keyof typeof testUsers = 'admin') {
    const user = testUsers[role]
    
    await this.page.goto('/login')
    
    // Fill login form
    await this.page.fill('[name="email"]', user.email)
    await this.page.fill('[name="password"]', user.password)
    
    // Submit form
    await this.page.click('button[type="submit"]')
    
    // Wait for successful login (redirect to dashboard)
    await expect(this.page).toHaveURL('/dashboard')
    
    // Verify user is logged in
    await expect(this.page.locator('text=Dashboard')).toBeVisible()
  }

  /**
   * Logout user
   */
  async logout() {
    // Open user menu
    await this.page.click('[data-testid="user-menu"]')
    
    // Click logout
    await this.page.click('text=Logout')
    
    // Verify redirect to login
    await expect(this.page).toHaveURL('/login')
  }

  /**
   * Check if user is logged in
   */
  async isLoggedIn(): Promise<boolean> {
    try {
      await this.page.locator('[data-testid="user-menu"]').waitFor({ timeout: 5000 })
      return true
    } catch {
      return false
    }
  }

  /**
   * Setup authentication state for tests
   */
  async setupAuth(role: keyof typeof testUsers = 'admin') {
    if (!(await this.isLoggedIn())) {
      await this.login(role)
    }
  }
}