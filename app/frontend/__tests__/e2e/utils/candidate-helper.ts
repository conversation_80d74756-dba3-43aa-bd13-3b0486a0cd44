/**
 * Candidate helper for E2E tests
 */
import { Page, expect, Locator } from '@playwright/test'
import { testCandidates, testFiles, searchQueries, filterOptions } from '../fixtures/test-data'

export class CandidateHelper {
  constructor(private page: Page) {}

  /**
   * Navigate to candidates page
   */
  async goToCandidatesPage() {
    await this.page.goto('/candidates')
    await expect(this.page.locator('h2:has-text("Candidates")')).toBeVisible()
  }

  /**
   * Navigate to create candidate page
   */
  async goToCreateCandidatePage() {
    await this.page.goto('/candidates/new')
    await expect(this.page.locator('h2:has-text("Add New Candidate")')).toBeVisible()
  }

  /**
   * Fill candidate form
   */
  async fillCandidateForm(candidate = testCandidates.newCandidate) {
    await this.page.fill('[name="name"]', candidate.name)
    await this.page.fill('[name="email"]', candidate.email)
    await this.page.fill('[name="phone"]', candidate.phone)
    await this.page.fill('[name="currentPosition"]', candidate.currentPosition)
    await this.page.fill('[name="currentCompany"]', candidate.currentCompany)
    await this.page.fill('[name="yearsOfExperience"]', candidate.yearsOfExperience.toString())
    await this.page.fill('[name="expectedSalary"]', candidate.expectedSalary.toString())
    
    // Add skills
    for (const skill of candidate.skills) {
      await this.page.fill('[name="skills"]', skill)
      await this.page.press('[name="skills"]', 'Enter')
    }
    
    // Add notes
    if (candidate.notes) {
      await this.page.fill('[name="notes"]', candidate.notes)
    }
  }

  /**
   * Submit candidate form
   */
  async submitCandidateForm() {
    await this.page.click('button[type="submit"]')
  }

  /**
   * Create a new candidate
   */
  async createCandidate(candidate = testCandidates.newCandidate) {
    await this.goToCreateCandidatePage()
    await this.fillCandidateForm(candidate)
    await this.submitCandidateForm()
    
    // Wait for success message or redirect
    await expect(this.page.locator('text=Candidate created successfully')).toBeVisible()
  }

  /**
   * Create candidate with resume file
   */
  async createCandidateWithResume(candidate = testCandidates.newCandidate) {
    await this.goToCreateCandidatePage()
    await this.fillCandidateForm(candidate)
    
    // Upload resume file
    const fileInput = this.page.locator('input[type="file"]')
    await fileInput.setInputFiles({
      name: testFiles.resume.name,
      mimeType: testFiles.resume.mimeType,
      buffer: testFiles.resume.buffer,
    })
    
    await this.submitCandidateForm()
    
    // Wait for success
    await expect(this.page.locator('text=Candidate created successfully')).toBeVisible()
  }

  /**
   * Search for candidates
   */
  async searchCandidates(query: string) {
    await this.page.fill('[placeholder*="Search"]', query)
    await this.page.press('[placeholder*="Search"]', 'Enter')
    
    // Wait for search results to load
    await this.page.waitForTimeout(1000)
  }

  /**
   * Apply advanced filters
   */
  async applyAdvancedFilters(filters: {
    status?: string
    permission?: string
    minExperience?: number
    maxExperience?: number
    minSalary?: number
    maxSalary?: number
  }) {
    // Open advanced filters
    await this.page.click('text=Advanced Filters')
    
    if (filters.status) {
      await this.page.click('[data-testid="status-select"]')
      await this.page.click(`text=${filters.status}`)
    }
    
    if (filters.permission) {
      await this.page.click('[data-testid="permission-select"]')
      await this.page.click(`text=${filters.permission}`)
    }
    
    if (filters.minExperience !== undefined) {
      await this.page.fill('[placeholder*="Min Experience"]', filters.minExperience.toString())
    }
    
    if (filters.maxExperience !== undefined) {
      await this.page.fill('[placeholder*="Max Experience"]', filters.maxExperience.toString())
    }
    
    if (filters.minSalary !== undefined) {
      await this.page.fill('[placeholder*="Min Salary"]', filters.minSalary.toString())
    }
    
    if (filters.maxSalary !== undefined) {
      await this.page.fill('[placeholder*="Max Salary"]', filters.maxSalary.toString())
    }
    
    // Wait for filters to apply
    await this.page.waitForTimeout(1000)
  }

  /**
   * Clear all filters
   */
  async clearFilters() {
    await this.page.click('text=Clear Filters')
    await this.page.waitForTimeout(1000)
  }

  /**
   * Select candidates for bulk operations
   */
  async selectCandidates(candidateNames: string[]) {
    for (const name of candidateNames) {
      const row = this.page.locator(`tr:has-text("${name}")`)
      const checkbox = row.locator('input[type="checkbox"]')
      await checkbox.check()
    }
  }

  /**
   * Select all candidates
   */
  async selectAllCandidates() {
    const headerCheckbox = this.page.locator('thead input[type="checkbox"]')
    await headerCheckbox.check()
  }

  /**
   * Export selected candidates
   */
  async exportSelectedCandidates() {
    await this.page.click('text=Export Selected')
    
    // Wait for download to start
    const downloadPromise = this.page.waitForEvent('download')
    const download = await downloadPromise
    
    return download
  }

  /**
   * View candidate details
   */
  async viewCandidateDetails(candidateName: string) {
    const candidateLink = this.page.locator(`a:has-text("${candidateName}")`)
    await candidateLink.click()
    
    // Wait for details page to load
    await expect(this.page.locator('text=Candidate Details')).toBeVisible()
  }

  /**
   * Edit candidate
   */
  async editCandidate(candidateName: string, updates = testCandidates.candidateUpdate) {
    // Navigate to candidate row and click edit
    const row = this.page.locator(`tr:has-text("${candidateName}")`)
    const moreButton = row.locator('[data-testid="more-actions"]')
    await moreButton.click()
    
    await this.page.click('text=Edit')
    
    // Wait for edit form
    await expect(this.page.locator('text=Edit Candidate')).toBeVisible()
    
    // Apply updates
    if (updates.name) {
      await this.page.fill('[name="name"]', updates.name)
    }
    if (updates.currentPosition) {
      await this.page.fill('[name="currentPosition"]', updates.currentPosition)
    }
    if (updates.yearsOfExperience) {
      await this.page.fill('[name="yearsOfExperience"]', updates.yearsOfExperience.toString())
    }
    
    // Submit updates
    await this.page.click('button[type="submit"]')
    
    // Wait for success
    await expect(this.page.locator('text=Candidate updated successfully')).toBeVisible()
  }

  /**
   * Delete candidate
   */
  async deleteCandidate(candidateName: string) {
    // Navigate to candidate row and click delete
    const row = this.page.locator(`tr:has-text("${candidateName}")`)
    const moreButton = row.locator('[data-testid="more-actions"]')
    await moreButton.click()
    
    await this.page.click('text=Delete')
    
    // Confirm deletion
    await this.page.click('text=Confirm Delete')
    
    // Wait for success
    await expect(this.page.locator('text=Candidate deleted successfully')).toBeVisible()
  }

  /**
   * Upload resume for existing candidate
   */
  async uploadResumeForCandidate(candidateName: string) {
    await this.viewCandidateDetails(candidateName)
    
    // Find and click upload resume button
    await this.page.click('text=Upload Resume')
    
    // Upload file
    const fileInput = this.page.locator('input[type="file"]')
    await fileInput.setInputFiles({
      name: testFiles.resume.name,
      mimeType: testFiles.resume.mimeType,
      buffer: testFiles.resume.buffer,
    })
    
    // Submit upload
    await this.page.click('text=Upload')
    
    // Wait for success
    await expect(this.page.locator('text=Resume uploaded successfully')).toBeVisible()
  }

  /**
   * Get candidate count from table
   */
  async getCandidateCount(): Promise<number> {
    const rows = await this.page.locator('tbody tr').count()
    return rows
  }

  /**
   * Verify candidate exists in table
   */
  async verifyCandidateExists(candidateName: string): Promise<boolean> {
    try {
      await expect(this.page.locator(`text=${candidateName}`)).toBeVisible({ timeout: 5000 })
      return true
    } catch {
      return false
    }
  }

  /**
   * Get pagination info
   */
  async getPaginationInfo() {
    const paginationText = await this.page.locator('[data-testid="pagination-info"]').textContent()
    return paginationText
  }

  /**
   * Navigate to next page
   */
  async goToNextPage() {
    await this.page.click('text=Next')
    await this.page.waitForTimeout(1000)
  }

  /**
   * Navigate to previous page
   */
  async goToPreviousPage() {
    await this.page.click('text=Previous')
    await this.page.waitForTimeout(1000)
  }
}