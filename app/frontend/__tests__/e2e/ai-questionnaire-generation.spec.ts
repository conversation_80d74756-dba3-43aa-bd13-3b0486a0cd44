/**
 * End-to-End tests for AI Questionnaire Generation
 * Tests the complete user journey in a browser environment
 */
import { test, expect, Page } from '@playwright/test';

test.describe('AI Questionnaire Generation E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Set up authentication - use development bypass token
    await page.goto('/');
    
    // Set authentication tokens in localStorage
    await page.evaluate(() => {
      localStorage.setItem('access_token', 'dev_bypass_token_2025_talentforge');
      localStorage.setItem('user_preferences', JSON.stringify({
        locale: 'en',
        timezone: 'UTC'
      }));
      
      // Set cookies for SSR compatibility
      document.cookie = 'access_token=dev_bypass_token_2025_talentforge; path=/; SameSite=lax';
    });

    // Navigate to AI questionnaire generation page
    await page.goto('/ai-questionnaire/generate');
  });

  test('should complete the full questionnaire generation flow', async ({ page }) => {
    // Step 1: Fill out the form
    await page.fill('[data-testid="position-type-input"]', 'Senior Software Engineer');
    
    // Select industry
    await page.click('[data-testid="industry-select"]');
    await page.click('[data-testid="industry-option-technology"]');
    
    // Select company size
    await page.click('[data-testid="company-size-select"]');
    await page.click('[data-testid="company-size-option-large"]');
    
    // Adjust question count using slider
    const slider = page.locator('[data-testid="question-count-slider"]');
    await slider.click();
    await page.keyboard.press('ArrowRight');
    await page.keyboard.press('ArrowRight');
    
    // Select hard difficulty
    await page.click('[data-testid="difficulty-hard"]');
    
    // Select additional focus areas
    await page.check('[data-testid="focus-area-innovation"]');
    await page.check('[data-testid="focus-area-learning-potential"]');
    
    // Add specific requirements
    await page.fill('[data-testid="additional-requirements"]', 
      'Focus on cloud computing, microservices, and system design'
    );

    // Step 2: Verify estimated duration updates
    const estimationElement = page.locator('[data-testid="estimated-duration"]');
    await expect(estimationElement).toBeVisible();
    await expect(estimationElement).toContainText('minutes');

    // Step 3: Submit the form
    const generateButton = page.locator('[data-testid="generate-button"]');
    await expect(generateButton).toBeEnabled();
    await generateButton.click();

    // Step 4: Verify generation starts
    await expect(generateButton).toBeDisabled();
    await expect(page.locator('[data-testid="generating-indicator"]')).toBeVisible();

    // Step 5: Monitor progress updates
    const progressCard = page.locator('[data-testid="progress-card"]');
    await expect(progressCard).toBeVisible();

    // Wait for progress to start
    await expect(page.locator('[data-testid="progress-percentage"]')).toBeVisible();
    await expect(page.locator('[data-testid="progress-step"]')).toContainText('Initializing');

    // Wait for progress to advance
    await page.waitForFunction(() => {
      const progressElement = document.querySelector('[data-testid="progress-percentage"]');
      return progressElement && parseInt(progressElement.textContent || '0') > 0;
    }, { timeout: 30000 });

    // Step 6: Wait for completion
    await page.waitForFunction(() => {
      const progressElement = document.querySelector('[data-testid="progress-percentage"]');
      return progressElement && progressElement.textContent?.includes('100%');
    }, { timeout: 60000 });

    await expect(page.locator('[data-testid="progress-step"]')).toContainText('completed');

    // Step 7: Verify generation results
    await expect(page.locator('[data-testid="generation-success"]')).toBeVisible();
    await expect(page.locator('[data-testid="questionnaire-title"]')).toBeVisible();
    await expect(page.locator('[data-testid="questionnaire-title"]')).toContainText('Senior Software Engineer');

    // Verify question count matches request
    const questionsList = page.locator('[data-testid="generated-questions"] .question-item');
    await expect(questionsList).toHaveCountGreaterThan(0);

    // Verify generation metadata is displayed
    await expect(page.locator('[data-testid="generation-metadata"]')).toBeVisible();
    await expect(page.locator('[data-testid="quality-score"]')).toBeVisible();
    await expect(page.locator('[data-testid="generation-time"]')).toBeVisible();
  });

  test('should handle form validation errors', async ({ page }) => {
    // Try to submit empty form
    const generateButton = page.locator('[data-testid="generate-button"]');
    await generateButton.click();

    // Verify validation errors appear
    await expect(page.locator('[data-testid="position-type-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="industry-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="company-size-error"]')).toBeVisible();

    // Verify form is not submitted
    await expect(page.locator('[data-testid="progress-card"]')).not.toBeVisible();

    // Fill in some fields and verify validation updates
    await page.fill('[data-testid="position-type-input"]', 'Software Engineer');
    await expect(page.locator('[data-testid="position-type-error"]')).not.toBeVisible();

    // Test focus area validation - uncheck all default selections
    await page.uncheck('[data-testid="focus-area-digital-literacy"]');
    await page.uncheck('[data-testid="focus-area-position-skill"]');

    await generateButton.click();
    await expect(page.locator('[data-testid="focus-areas-error"]')).toBeVisible();
    await expect(page.locator('[data-testid="focus-areas-error"]')).toContainText('at least one');
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Set up a position type that will trigger an error (based on mock handler)
    await page.fill('[data-testid="position-type-input"]', 'Fail Test Position');
    
    await page.click('[data-testid="industry-select"]');
    await page.click('[data-testid="industry-option-technology"]');
    
    await page.click('[data-testid="company-size-select"]');
    await page.click('[data-testid="company-size-option-medium"]');

    const generateButton = page.locator('[data-testid="generate-button"]');
    await generateButton.click();

    // Verify error handling
    await expect(page.locator('[data-testid="error-message"]')).toBeVisible();
    await expect(page.locator('[data-testid="error-message"]')).toContainText('temporarily unavailable');

    // Verify form becomes interactive again
    await expect(generateButton).toBeEnabled();
    await expect(page.locator('[data-testid="progress-card"]')).not.toBeVisible();
  });

  test('should handle different form configurations correctly', async ({ page }) => {
    // Test with minimum configuration
    await page.fill('[data-testid="position-type-input"]', 'Junior Developer');
    
    await page.click('[data-testid="industry-select"]');
    await page.click('[data-testid="industry-option-education"]');
    
    await page.click('[data-testid="company-size-select"]');
    await page.click('[data-testid="company-size-option-startup"]');

    // Set minimum question count
    const slider = page.locator('[data-testid="question-count-slider"]');
    await slider.click();
    await page.keyboard.press('ArrowLeft');
    await page.keyboard.press('ArrowLeft');

    // Select easy difficulty
    await page.click('[data-testid="difficulty-easy"]');

    // Disable some question types
    await page.uncheck('[data-testid="include-behavioral"]');

    // Select Chinese language
    await page.click('[data-testid="language-select"]');
    await page.click('[data-testid="language-option-zh"]');

    const generateButton = page.locator('[data-testid="generate-button"]');
    await generateButton.click();

    // Verify request goes through with minimal config
    await expect(page.locator('[data-testid="generating-indicator"]')).toBeVisible();
    await expect(generateButton).toBeDisabled();
  });

  test('should display progress information accurately', async ({ page }) => {
    // Fill minimal form
    await page.fill('[data-testid="position-type-input"]', 'Product Manager');
    
    await page.click('[data-testid="industry-select"]');
    await page.click('[data-testid="industry-option-finance"]');
    
    await page.click('[data-testid="company-size-select"]');
    await page.click('[data-testid="company-size-option-enterprise"]');

    const generateButton = page.locator('[data-testid="generate-button"]');
    await generateButton.click();

    // Monitor progress updates
    const progressBar = page.locator('[data-testid="progress-bar"]');
    const progressPercentage = page.locator('[data-testid="progress-percentage"]');
    const progressStep = page.locator('[data-testid="progress-step"]');
    const estimatedRemaining = page.locator('[data-testid="estimated-remaining"]');

    await expect(progressBar).toBeVisible();
    await expect(progressPercentage).toBeVisible();
    await expect(progressStep).toBeVisible();

    // Verify progress advances
    let lastProgress = 0;
    let progressIncreased = false;

    for (let i = 0; i < 10 && !progressIncreased; i++) {
      await page.waitForTimeout(2000); // Wait for progress update
      
      const currentProgressText = await progressPercentage.textContent();
      const currentProgress = parseInt(currentProgressText?.replace('%', '') || '0');
      
      if (currentProgress > lastProgress) {
        progressIncreased = true;
      }
      lastProgress = currentProgress;
    }

    expect(progressIncreased).toBe(true);

    // Verify progress icons change with status
    const progressIcon = page.locator('[data-testid="progress-icon"]');
    await expect(progressIcon).toBeVisible();

    // Wait for completion
    await page.waitForFunction(() => {
      const element = document.querySelector('[data-testid="progress-percentage"]');
      return element?.textContent?.includes('100%');
    }, { timeout: 60000 });

    // Verify completion state
    await expect(progressStep).toContainText('completed');
    await expect(progressIcon).toHaveAttribute('data-status', 'completed');
  });

  test('should support template selection and application', async ({ page }) => {
    // Navigate to templates section if available
    const templatesTab = page.locator('[data-testid="templates-tab"]');
    if (await templatesTab.isVisible()) {
      await templatesTab.click();

      // Wait for templates to load
      await expect(page.locator('[data-testid="templates-list"]')).toBeVisible();

      // Select a template
      const firstTemplate = page.locator('[data-testid="template-item"]').first();
      await firstTemplate.click();

      const applyTemplateButton = page.locator('[data-testid="apply-template"]');
      await applyTemplateButton.click();

      // Verify form is pre-filled with template defaults
      const positionTypeInput = page.locator('[data-testid="position-type-input"]');
      await expect(positionTypeInput).not.toHaveValue('');

      // Verify other fields are populated
      await expect(page.locator('[data-testid="industry-select"]')).not.toBeEmpty();
      
      // Submit with template defaults
      const generateButton = page.locator('[data-testid="generate-button"]');
      await generateButton.click();

      await expect(page.locator('[data-testid="generating-indicator"]')).toBeVisible();
    }
  });

  test('should handle generation cancellation', async ({ page }) => {
    // Start generation
    await page.fill('[data-testid="position-type-input"]', 'Data Scientist');
    
    await page.click('[data-testid="industry-select"]');
    await page.click('[data-testid="industry-option-technology"]');
    
    await page.click('[data-testid="company-size-select"]');
    await page.click('[data-testid="company-size-option-large"]');

    const generateButton = page.locator('[data-testid="generate-button"]');
    await generateButton.click();

    // Wait for generation to start
    await expect(page.locator('[data-testid="generating-indicator"]')).toBeVisible();

    // Cancel generation if cancel button is available
    const cancelButton = page.locator('[data-testid="cancel-generation"]');
    if (await cancelButton.isVisible()) {
      await cancelButton.click();

      // Confirm cancellation if needed
      const confirmButton = page.locator('[data-testid="confirm-cancel"]');
      if (await confirmButton.isVisible()) {
        await confirmButton.click();
      }

      // Verify cancellation
      await expect(page.locator('[data-testid="generation-cancelled"]')).toBeVisible();
      await expect(generateButton).toBeEnabled();
    }
  });

  test('should maintain form state during navigation', async ({ page }) => {
    // Fill out part of the form
    await page.fill('[data-testid="position-type-input"]', 'UX Designer');
    
    await page.click('[data-testid="industry-select"]');
    await page.click('[data-testid="industry-option-media"]');

    // Navigate away and back
    await page.goto('/');
    await page.goto('/ai-questionnaire/generate');

    // Note: This test would need actual form state persistence implementation
    // For now, we'll verify the form renders correctly after navigation
    await expect(page.locator('[data-testid="position-type-input"]')).toBeVisible();
    await expect(page.locator('[data-testid="industry-select"]')).toBeVisible();
    await expect(page.locator('[data-testid="generate-button"]')).toBeVisible();
  });

  test('should be responsive and accessible', async ({ page }) => {
    // Test mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    await expect(page.locator('[data-testid="form-container"]')).toBeVisible();
    await expect(page.locator('[data-testid="position-type-input"]')).toBeVisible();

    // Test basic accessibility
    const generateButton = page.locator('[data-testid="generate-button"]');
    await expect(generateButton).toHaveAttribute('type', 'submit');

    // Test keyboard navigation
    await page.keyboard.press('Tab');
    const focusedElement = await page.evaluate(() => document.activeElement?.getAttribute('data-testid'));
    
    // Should focus on first interactive element
    expect(['position-type-input', 'industry-select']).toContain(focusedElement);

    // Test form labels
    await expect(page.locator('label[for*="position"]')).toBeVisible();
    await expect(page.locator('label[for*="industry"]')).toBeVisible();

    // Reset to desktop viewport
    await page.setViewportSize({ width: 1280, height: 720 });
  });
});