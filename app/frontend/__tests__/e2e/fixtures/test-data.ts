/**
 * Test data fixtures for E2E tests
 */
export const testUsers = {
  admin: {
    email: '<EMAIL>',
    password: 'test123',
    permissions: ['CANDIDATES_READ', 'CANDIDATES_WRITE', 'CANDIDATES_DELETE', 'CA<PERSON>IDATES_EXPORT'],
  },
  hr: {
    email: '<EMAIL>', 
    password: 'test123',
    permissions: ['CANDIDATES_READ', 'CANDIDATES_WRITE'],
  },
  recruiter: {
    email: '<EMAIL>',
    password: 'test123', 
    permissions: ['CANDIDATES_READ'],
  },
}

export const testCandidates = {
  newCandidate: {
    name: 'Test Candidate',
    email: '<EMAIL>',
    phone: '13800138001',
    currentPosition: 'Software Engineer',
    currentCompany: 'TestCorp',
    yearsOfExperience: 3,
    expectedSalary: 20000,
    skills: ['JavaScript', 'React', 'Node.js'],
    notes: 'Excellent problem-solving skills',
  },
  existingCandidate: {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '13800138000',
    currentPosition: 'Senior Developer',
    currentCompany: 'Tech Corp',
    yearsOfExperience: 5,
    expectedSalary: 25000,
    skills: ['React', 'TypeScript', 'Node.js'],
  },
  candidateUpdate: {
    name: 'John Doe Updated',
    currentPosition: 'Lead Developer',
    yearsOfExperience: 6,
  },
}

export const testFiles = {
  resume: {
    name: 'test-resume.pdf',
    mimeType: 'application/pdf',
    buffer: Buffer.from('Mock PDF content for testing'),
  },
  csvImport: {
    name: 'candidates-import.csv',
    mimeType: 'text/csv',
    content: `Name,Email,Phone,Position,Company,Experience,Salary
Jane Smith,<EMAIL>,13900139001,Product Manager,StartupXYZ,4,22000
Bob Wilson,<EMAIL>,13700137001,DevOps Engineer,CloudCorp,6,28000`,
  },
}

export const searchQueries = {
  byName: 'John',
  byEmail: 'john.doe',
  byPosition: 'Senior Developer',
  byCompany: 'Tech Corp',
  noResults: 'NonExistentCandidate',
}

export const filterOptions = {
  status: {
    new: 'NEW',
    screening: 'SCREENING', 
    interview: 'INTERVIEW',
    offer: 'OFFER',
    hired: 'HIRED',
    rejected: 'REJECTED',
    withdrawn: 'WITHDRAWN',
  },
  permission: {
    private: 'PRIVATE',
    shared: 'SHARED',
    team: 'TEAM',
    public: 'PUBLIC',
  },
  experience: {
    min: 2,
    max: 10,
  },
  salary: {
    min: 15000,
    max: 30000,
  },
}