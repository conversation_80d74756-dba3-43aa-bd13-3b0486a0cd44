/**
 * MSW handlers for AI Questionnaire API endpoints
 * Provides realistic mock responses for testing
 */
import { rest } from 'msw';
import {
  QuestionnaireGenerateRequest,
  QuestionnaireGenerateResponse,
  GenerationProgress,
  GenerationStatus,
  QuestionnaireTemplate,
  TemplateListResponse,
  Questionnaire,
  QuestionnaireListResponse,
  QuestionType,
  QuestionCategory,
  DifficultyLevel
} from '@/types/questionnaire';

// Mock data
const mockTemplates: QuestionnaireTemplate[] = [
  {
    id: '1234567890123456789',
    name: 'Software Engineer Template',
    description: 'Standard template for software engineering positions',
    position_types: ['Software Engineer', 'Full Stack Developer', 'Backend Developer'],
    industries: ['Technology', 'Finance', 'Healthcare'],
    default_settings: {
      question_count: 25,
      difficulty_level: 'medium',
      focus_areas: ['digital_literacy', 'position_skill', 'innovation'],
      include_behavioral: true,
      include_technical: true,
      include_situational: true,
      language: 'en'
    },
    is_system: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    id: '1234567890123456790',
    name: 'Data Science Template',
    description: 'Template for data science and ML engineering positions',
    position_types: ['Data Scientist', 'ML Engineer', 'Data Analyst'],
    industries: ['Technology', 'Finance', 'Research'],
    default_settings: {
      question_count: 30,
      difficulty_level: 'hard',
      focus_areas: ['digital_literacy', 'industry_skill', 'innovation', 'learning_potential'],
      include_behavioral: true,
      include_technical: true,
      include_situational: false,
      language: 'en'
    },
    is_system: true,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  }
];

const mockQuestionnaires: Questionnaire[] = [
  {
    id: '2234567890123456789',
    title: 'Senior Frontend Developer Assessment',
    description: 'Comprehensive assessment for senior frontend developer role',
    position_type: 'Senior Frontend Developer',
    industry: 'Technology',
    difficulty_level: 'hard',
    estimated_duration: 60,
    question_count: 25,
    questions: [],
    is_active: true,
    is_ai_generated: true,
    generation_metadata: {
      ai_model_used: 'gpt-4',
      generation_id: 'gen-789012',
      quality_score: 0.89,
      created_from_template: '1234567890123456789'
    },
    created_by: '9876543210987654321',
    created_at: '2024-01-02T10:00:00Z',
    updated_at: '2024-01-02T10:30:00Z'
  }
];

// Progress state management
const generationProgress = new Map<string, GenerationProgress>();

export const aiQuestionnaireHandlers = [
  // Generate questionnaire
  rest.post('/api/v1/ai/ai-questionnaire/generate', async (req, res, ctx) => {
    const request = await req.json() as QuestionnaireGenerateRequest;

    // Validate required fields
    if (!request.position_type || !request.industry || !request.company_size) {
      return res(
        ctx.status(422),
        ctx.json({
          error_code: 'VALIDATION_ERROR',
          detail: 'Missing required fields',
          details: {
            validation_errors: [
              ...(request.position_type ? [] : [{ field: 'position_type', message: 'Position type is required' }]),
              ...(request.industry ? [] : [{ field: 'industry', message: 'Industry is required' }]),
              ...(request.company_size ? [] : [{ field: 'company_size', message: 'Company size is required' }])
            ]
          }
        })
      );
    }

    if (request.focus_areas.length === 0) {
      return res(
        ctx.status(422),
        ctx.json({
          error_code: 'VALIDATION_ERROR',
          detail: 'At least one focus area is required',
          details: {
            validation_errors: [
              { field: 'focus_areas', message: 'At least one focus area is required' }
            ]
          }
        })
      );
    }

    // Simulate AI service failure
    if (request.position_type.toLowerCase().includes('fail')) {
      return res(
        ctx.status(503),
        ctx.json({
          error_code: 'AI_GENERATION_FAILED',
          detail: 'AI service is temporarily unavailable. Please try again later.'
        })
      );
    }

    const generationId = `gen-${Date.now()}`;

    // Initialize progress
    generationProgress.set(generationId, {
      generation_id: generationId,
      status: GenerationStatus.PENDING,
      progress_percentage: 0,
      current_step: 'Initializing generation process...',
      estimated_remaining_time: request.question_count * 3, // 3 seconds per question
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    });

    // Generate questions based on request
    const questions = Array.from({ length: Math.min(request.question_count, 50) }, (_, index) => ({
      id: `${Date.now()}${index}`,
      question_text: generateQuestionText(request.position_type, request.focus_areas, index),
      question_type: getQuestionType(index, request),
      category: getQuestionCategory(request.focus_areas, index),
      difficulty: request.difficulty_level as DifficultyLevel,
      estimated_time: Math.floor(Math.random() * 8) + 3, // 3-10 minutes
      evaluation_criteria: generateEvaluationCriteria(request.focus_areas),
      ai_rationale: `This question evaluates ${request.focus_areas.join(', ')} capabilities for ${request.position_type} role.`
    }));

    const response: QuestionnaireGenerateResponse = {
      questionnaire_id: `${Date.now()}`,
      title: `${request.position_type} Assessment`,
      description: `AI-generated assessment for ${request.position_type} position in ${request.industry} industry`,
      questions,
      estimated_duration: questions.reduce((sum, q) => sum + q.estimated_time, 0),
      generation_metadata: {
        generation_time: Math.floor(Math.random() * 60) + 30, // 30-90 seconds
        ai_model_used: 'gpt-4',
        generation_id: generationId,
        quality_score: Math.random() * 0.3 + 0.7 // 0.7-1.0
      },
      created_at: new Date().toISOString()
    };

    return res(ctx.json(response));
  }),

  // Get generation progress
  rest.get('/api/v1/ai/ai-questionnaire/progress/:generationId', (req, res, ctx) => {
    const { generationId } = req.params;

    if (!generationProgress.has(generationId as string)) {
      return res(
        ctx.status(404),
        ctx.json({
          error_code: 'GENERATION_NOT_FOUND',
          detail: 'Generation ID not found'
        })
      );
    }

    let progress = generationProgress.get(generationId as string)!;

    // Simulate progress advancement
    if (progress.status !== GenerationStatus.COMPLETED && progress.status !== GenerationStatus.FAILED) {
      const stages = [
        { status: GenerationStatus.PENDING, step: 'Initializing generation process...', progress: 0 },
        { status: GenerationStatus.ANALYZING_REQUIREMENTS, step: 'Analyzing position requirements...', progress: 15 },
        { status: GenerationStatus.GENERATING_QUESTIONS, step: 'Generating technical questions...', progress: 40 },
        { status: GenerationStatus.GENERATING_QUESTIONS, step: 'Generating behavioral questions...', progress: 65 },
        { status: GenerationStatus.REVIEWING_QUALITY, step: 'Reviewing question quality...', progress: 85 },
        { status: GenerationStatus.FINALIZING, step: 'Finalizing questionnaire...', progress: 95 },
        { status: GenerationStatus.COMPLETED, step: 'Generation completed successfully', progress: 100 }
      ];

      const currentIndex = stages.findIndex(s => s.status === progress.status && s.progress === progress.progress_percentage);
      const nextIndex = Math.min(currentIndex + 1, stages.length - 1);

      if (nextIndex > currentIndex) {
        const nextStage = stages[nextIndex];
        progress = {
          ...progress,
          status: nextStage.status,
          progress_percentage: nextStage.progress,
          current_step: nextStage.step,
          estimated_remaining_time: Math.max(0, progress.estimated_remaining_time - 15),
          updated_at: new Date().toISOString()
        };
        generationProgress.set(generationId as string, progress);
      }
    }

    return res(ctx.json(progress));
  }),

  // Cancel generation
  rest.post('/api/v1/ai/ai-questionnaire/cancel/:generationId', (req, res, ctx) => {
    const { generationId } = req.params;

    if (!generationProgress.has(generationId as string)) {
      return res(
        ctx.status(404),
        ctx.json({
          error_code: 'GENERATION_NOT_FOUND',
          detail: 'Generation ID not found'
        })
      );
    }

    const progress = generationProgress.get(generationId as string)!;

    if (progress.status === GenerationStatus.COMPLETED) {
      return res(ctx.json({
        success: false,
        message: 'Generation already completed'
      }));
    }

    // Update progress to cancelled
    generationProgress.set(generationId as string, {
      ...progress,
      status: GenerationStatus.FAILED,
      current_step: 'Generation cancelled by user',
      error_message: 'Generation cancelled by user request',
      updated_at: new Date().toISOString()
    });

    return res(ctx.json({
      success: true,
      message: 'Generation cancelled successfully'
    }));
  }),

  // Get templates
  rest.get('/api/v1/ai/ai-questionnaire/templates/', (req, res, ctx) => {
    const url = new URL(req.url);
    const positionType = url.searchParams.get('position_type');
    const industry = url.searchParams.get('industry');
    const skip = parseInt(url.searchParams.get('skip') || '0');
    const limit = parseInt(url.searchParams.get('limit') || '20');

    let filteredTemplates = [...mockTemplates];

    if (positionType) {
      filteredTemplates = filteredTemplates.filter(t => 
        t.position_types.some(pt => pt.toLowerCase().includes(positionType.toLowerCase()))
      );
    }

    if (industry) {
      filteredTemplates = filteredTemplates.filter(t => 
        t.industries.some(ind => ind.toLowerCase().includes(industry.toLowerCase()))
      );
    }

    const pagedTemplates = filteredTemplates.slice(skip, skip + limit);

    const response: TemplateListResponse = {
      items: pagedTemplates,
      total: filteredTemplates.length,
      skip,
      limit
    };

    return res(ctx.json(response));
  }),

  // Get specific template
  rest.get('/api/v1/ai/ai-questionnaire/templates/:templateId', (req, res, ctx) => {
    const { templateId } = req.params;
    const template = mockTemplates.find(t => t.id === templateId);

    if (!template) {
      return res(
        ctx.status(404),
        ctx.json({
          error_code: 'TEMPLATE_NOT_FOUND',
          detail: 'Template not found'
        })
      );
    }

    return res(ctx.json(template));
  }),

  // Create template
  rest.post('/api/v1/ai/ai-questionnaire/templates/', async (req, res, ctx) => {
    const templateData = await req.json();

    if (!templateData.name || !templateData.description) {
      return res(
        ctx.status(422),
        ctx.json({
          error_code: 'VALIDATION_ERROR',
          detail: 'Name and description are required'
        })
      );
    }

    const newTemplate: QuestionnaireTemplate = {
      id: `${Date.now()}`,
      ...templateData,
      is_system: false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    mockTemplates.push(newTemplate);

    return res(ctx.status(201), ctx.json(newTemplate));
  }),

  // Get questionnaires
  rest.get('/api/v1/ai/ai-questionnaire/', (req, res, ctx) => {
    const url = new URL(req.url);
    const search = url.searchParams.get('search');
    const positionType = url.searchParams.get('position_type');
    const isAiGenerated = url.searchParams.get('is_ai_generated');
    const skip = parseInt(url.searchParams.get('skip') || '0');
    const limit = parseInt(url.searchParams.get('limit') || '20');

    let filteredQuestionnaires = [...mockQuestionnaires];

    if (search) {
      filteredQuestionnaires = filteredQuestionnaires.filter(q =>
        q.title.toLowerCase().includes(search.toLowerCase()) ||
        q.description.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (positionType) {
      filteredQuestionnaires = filteredQuestionnaires.filter(q =>
        q.position_type.toLowerCase().includes(positionType.toLowerCase())
      );
    }

    if (isAiGenerated !== null) {
      const aiGenerated = isAiGenerated === 'true';
      filteredQuestionnaires = filteredQuestionnaires.filter(q => q.is_ai_generated === aiGenerated);
    }

    const pagedQuestionnaires = filteredQuestionnaires.slice(skip, skip + limit);

    const response: QuestionnaireListResponse = {
      items: pagedQuestionnaires,
      total: filteredQuestionnaires.length,
      skip,
      limit
    };

    return res(ctx.json(response));
  }),

  // Save questionnaire
  rest.post('/api/v1/ai/ai-questionnaire/', async (req, res, ctx) => {
    const questionnaireData = await req.json();

    if (!questionnaireData.title || !questionnaireData.position_type) {
      return res(
        ctx.status(422),
        ctx.json({
          error_code: 'VALIDATION_ERROR',
          detail: 'Title and position type are required'
        })
      );
    }

    const newQuestionnaire: Questionnaire = {
      id: `${Date.now()}`,
      ...questionnaireData,
      question_count: questionnaireData.questions?.length || 0,
      is_active: true,
      is_ai_generated: true,
      created_by: 'current_user_id',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    mockQuestionnaires.push(newQuestionnaire);

    return res(ctx.status(201), ctx.json(newQuestionnaire));
  }),

  // Toggle questionnaire status
  rest.patch('/api/v1/ai/ai-questionnaire/:questionnaireId/status', async (req, res, ctx) => {
    const { questionnaireId } = req.params;
    const { is_active } = await req.json();

    const questionnaire = mockQuestionnaires.find(q => q.id === questionnaireId);

    if (!questionnaire) {
      return res(
        ctx.status(404),
        ctx.json({
          error_code: 'QUESTIONNAIRE_NOT_FOUND',
          detail: 'Questionnaire not found'
        })
      );
    }

    questionnaire.is_active = is_active;
    questionnaire.updated_at = new Date().toISOString();

    return res(ctx.json({
      success: true,
      message: `Questionnaire ${is_active ? 'activated' : 'deactivated'} successfully`
    }));
  }),

  // Get generation stats
  rest.get('/api/v1/ai/ai-questionnaire/stats', (req, res, ctx) => {
    const stats = {
      total_generated: 247,
      successful_generations: 235,
      average_generation_time: 67,
      most_popular_position_types: [
        { position_type: 'Software Engineer', count: 89 },
        { position_type: 'Data Scientist', count: 45 },
        { position_type: 'Product Manager', count: 32 }
      ],
      most_popular_industries: [
        { industry: 'Technology', count: 156 },
        { industry: 'Finance', count: 67 },
        { industry: 'Healthcare', count: 24 }
      ],
      quality_score_distribution: [
        { score_range: '0.9-1.0', count: 98 },
        { score_range: '0.8-0.9', count: 87 },
        { score_range: '0.7-0.8', count: 50 },
        { score_range: '0.6-0.7', count: 12 }
      ]
    };

    return res(ctx.json(stats));
  }),

  // Preview questionnaire
  rest.post('/api/v1/ai/ai-questionnaire/preview', async (req, res, ctx) => {
    const request = await req.json() as QuestionnaireGenerateRequest;

    const previewQuestions = Array.from({ length: Math.min(3, request.question_count) }, (_, index) => ({
      question_text: generateQuestionText(request.position_type, request.focus_areas, index),
      category: getQuestionCategory(request.focus_areas, index),
      difficulty: request.difficulty_level,
      estimated_time: Math.floor(Math.random() * 5) + 3
    }));

    const preview = {
      preview_questions: previewQuestions,
      estimated_total_duration: request.question_count * 4.5,
      quality_preview: {
        coverage_score: Math.random() * 0.3 + 0.7,
        difficulty_distribution: {
          easy: 0.2,
          medium: 0.5,
          hard: 0.3
        },
        category_distribution: request.focus_areas.reduce((acc, area) => {
          acc[area] = Math.random() * 0.4 + 0.1;
          return acc;
        }, {} as Record<string, number>)
      }
    };

    return res(ctx.json(preview));
  })
];

// Helper functions
function generateQuestionText(positionType: string, focusAreas: string[], index: number): string {
  const questionTemplates = {
    digital_literacy: [
      'Describe your experience with digital tools and technologies relevant to this role.',
      'How do you stay current with digital trends and emerging technologies?',
      'What digital platforms or software have you used extensively in your work?'
    ],
    position_skill: [
      `What specific skills do you bring to a ${positionType} role?`,
      `Describe a challenging project you completed as a ${positionType}.`,
      `How would you approach learning new technologies required for this ${positionType} position?`
    ],
    industry_skill: [
      'What industry-specific knowledge do you possess that makes you suitable for this role?',
      'How do you stay updated with industry trends and best practices?',
      'Describe your experience with industry-standard tools and methodologies.'
    ],
    innovation: [
      'Describe a time when you introduced an innovative solution to a problem.',
      'How do you approach creative problem-solving in your work?',
      'What methods do you use to generate new ideas and approaches?'
    ],
    learning_potential: [
      'How do you approach learning new skills or technologies?',
      'Describe a time when you had to quickly master something new.',
      'What strategies do you use for continuous learning and development?'
    ]
  };

  const relevantArea = focusAreas[index % focusAreas.length];
  const templates = questionTemplates[relevantArea] || questionTemplates.position_skill;
  return templates[index % templates.length];
}

function getQuestionType(index: number, request: QuestionnaireGenerateRequest): QuestionType {
  const types = [QuestionType.TEXT_LONG, QuestionType.TEXT_SHORT, QuestionType.SCENARIO];
  
  if (request.include_technical && index % 4 === 0) {
    return QuestionType.CODE;
  }
  
  return types[index % types.length];
}

function getQuestionCategory(focusAreas: string[], index: number): QuestionCategory {
  const categoryMapping = {
    digital_literacy: QuestionCategory.DIGITAL_LITERACY,
    industry_skill: QuestionCategory.INDUSTRY_SKILL,
    position_skill: QuestionCategory.POSITION_SKILL,
    innovation: QuestionCategory.INNOVATION,
    learning_potential: QuestionCategory.LEARNING_POTENTIAL
  };

  const focusArea = focusAreas[index % focusAreas.length];
  return categoryMapping[focusArea] || QuestionCategory.TECHNICAL;
}

function generateEvaluationCriteria(focusAreas: string[]): string[] {
  const criteriaMap = {
    digital_literacy: ['Technical proficiency', 'Tool usage', 'Digital adaptation'],
    position_skill: ['Role-specific expertise', 'Practical experience', 'Problem-solving approach'],
    industry_skill: ['Industry knowledge', 'Best practices awareness', 'Domain expertise'],
    innovation: ['Creative thinking', 'Solution originality', 'Innovation impact'],
    learning_potential: ['Learning agility', 'Knowledge retention', 'Skill development']
  };

  const criteria = [];
  for (const area of focusAreas) {
    criteria.push(...(criteriaMap[area] || ['General competency']));
  }

  return [...new Set(criteria)].slice(0, 4); // Return unique criteria, max 4
}