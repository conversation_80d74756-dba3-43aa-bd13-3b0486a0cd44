/**
 * Component tests for AIQuestionnaireForm
 * Tests form validation, user interactions, and API integration
 */
import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { AIQuestionnaireForm } from '@/components/ai-questionnaire/AIQuestionnaireForm';
import {
  QuestionnaireGenerateResponse,
  GenerationProgress,
  GenerationStatus,
  QuestionType,
  QuestionCategory,
  DifficultyLevel
} from '@/types/questionnaire';
import aiQuestionnaireService from '@/services/aiQuestionnaireService';

// Mock the service
jest.mock('@/services/aiQuestionnaireService');
const mockAiQuestionnaireService = aiQuestionnaireService as jest.Mocked<typeof aiQuestionnaireService>;

// Mock framer-motion to avoid animation issues in tests
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock data
const mockGenerateResponse: QuestionnaireGenerateResponse = {
  questionnaire_id: '1234567890123456789',
  title: 'Software Engineer Assessment',
  description: 'AI-generated questionnaire for software engineer position',
  questions: [
    {
      id: '1234567890123456790',
      question_text: 'What is your experience with cloud computing?',
      question_type: QuestionType.TEXT_LONG,
      category: QuestionCategory.TECHNICAL,
      difficulty: DifficultyLevel.MEDIUM,
      estimated_time: 5,
      evaluation_criteria: ['Technical depth', 'Practical experience'],
      ai_rationale: 'This question evaluates cloud computing knowledge'
    }
  ],
  estimated_duration: 40,
  generation_metadata: {
    generation_time: 30,
    ai_model_used: 'gpt-4',
    generation_id: 'gen-123456',
    quality_score: 0.85
  },
  created_at: '2024-01-01T00:00:00Z'
};

const mockProgressStates: GenerationProgress[] = [
  {
    generation_id: 'gen-123456',
    status: GenerationStatus.PENDING,
    progress_percentage: 0,
    current_step: 'Initializing...',
    estimated_remaining_time: 60,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z'
  },
  {
    generation_id: 'gen-123456',
    status: GenerationStatus.ANALYZING_REQUIREMENTS,
    progress_percentage: 25,
    current_step: 'Analyzing requirements...',
    estimated_remaining_time: 45,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:30Z'
  },
  {
    generation_id: 'gen-123456',
    status: GenerationStatus.GENERATING_QUESTIONS,
    progress_percentage: 50,
    current_step: 'Generating questions...',
    estimated_remaining_time: 30,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:01:00Z'
  },
  {
    generation_id: 'gen-123456',
    status: GenerationStatus.COMPLETED,
    progress_percentage: 100,
    current_step: 'Generation completed',
    estimated_remaining_time: 0,
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:02:00Z'
  }
];

describe('AIQuestionnaireForm', () => {
  const mockOnGenerate = jest.fn();
  const mockOnProgress = jest.fn();
  const mockOnError = jest.fn();

  const defaultProps = {
    onGenerate: mockOnGenerate,
    onProgress: mockOnProgress,
    onError: mockOnError
  };

  beforeEach(() => {
    jest.clearAllMocks();
    jest.clearAllTimers();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  describe('Form Rendering', () => {
    it('should render all required form fields', () => {
      render(<AIQuestionnaireForm {...defaultProps} />);

      // Basic Configuration
      expect(screen.getByLabelText(/position type/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/industry/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/company size/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/language/i)).toBeInTheDocument();

      // Question Configuration
      expect(screen.getByText(/question count/i)).toBeInTheDocument();
      expect(screen.getByText(/difficulty level/i)).toBeInTheDocument();
      expect(screen.getByText(/focus areas/i)).toBeInTheDocument();

      // Question Types
      expect(screen.getByText(/technical questions/i)).toBeInTheDocument();
      expect(screen.getByText(/behavioral questions/i)).toBeInTheDocument();
      expect(screen.getByText(/situational questions/i)).toBeInTheDocument();

      // Submit button
      expect(screen.getByRole('button', { name: /generate/i })).toBeInTheDocument();
    });

    it('should render with default values', () => {
      render(<AIQuestionnaireForm {...defaultProps} />);

      // Check default values
      expect(screen.getByDisplayValue('')).toBeInTheDocument(); // position_type starts empty
      expect(screen.getByText('20')).toBeInTheDocument(); // default question count
      
      // Check default checkboxes
      const technicalCheckbox = screen.getByRole('checkbox', { name: /technical questions/i });
      const behavioralCheckbox = screen.getByRole('checkbox', { name: /behavioral questions/i });
      expect(technicalCheckbox).toBeChecked();
      expect(behavioralCheckbox).toBeChecked();
    });

    it('should show estimated duration based on question count', () => {
      render(<AIQuestionnaireForm {...defaultProps} />);

      // Default estimation should be visible
      expect(screen.getByText(/estimated duration/i)).toBeInTheDocument();
      expect(screen.getByText(/minutes/)).toBeInTheDocument();
    });
  });

  describe('Form Validation', () => {
    it('should show validation errors for required fields', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      render(<AIQuestionnaireForm {...defaultProps} />);

      const submitButton = screen.getByRole('button', { name: /generate/i });
      
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/position type is required/i)).toBeInTheDocument();
        expect(screen.getByText(/company size is required/i)).toBeInTheDocument();
        expect(screen.getByText(/industry is required/i)).toBeInTheDocument();
      });
    });

    it('should validate focus areas requirement', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      render(<AIQuestionnaireForm {...defaultProps} />);

      // Uncheck all focus areas
      const digitalLiteracyCheckbox = screen.getByRole('checkbox', { name: /digital literacy/i });
      const positionSkillCheckbox = screen.getByRole('checkbox', { name: /position skills/i });
      
      await user.click(digitalLiteracyCheckbox);
      await user.click(positionSkillCheckbox);

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/at least one focus area is required/i)).toBeInTheDocument();
      });
    });

    it('should validate question count limits', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      render(<AIQuestionnaireForm {...defaultProps} />);

      // The slider has min/max constraints, so we test the validation schema indirectly
      // by checking that the form prevents submission with extreme values
      const slider = screen.getByRole('slider');
      
      // Set to minimum - 1 (should not be possible with slider, but we test validation)
      fireEvent.change(slider, { target: { value: '4' } });
      
      const submitButton = screen.getByRole('button', { name: /generate/i });
      expect(submitButton).toBeDisabled(); // Form should be invalid
    });
  });

  describe('User Interactions', () => {
    it('should handle form field changes correctly', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      render(<AIQuestionnaireForm {...defaultProps} />);

      // Fill in basic fields
      const positionInput = screen.getByLabelText(/position type/i);
      await user.type(positionInput, 'Software Engineer');

      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      await waitFor(() => {
        expect(positionInput).toHaveValue('Software Engineer');
      });
    });

    it('should handle question count slider changes', async () => {
      render(<AIQuestionnaireForm {...defaultProps} />);

      const slider = screen.getByRole('slider');
      fireEvent.change(slider, { target: { value: '30' } });

      await waitFor(() => {
        expect(screen.getByText('30')).toBeInTheDocument();
      });
    });

    it('should handle focus area checkboxes', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      render(<AIQuestionnaireForm {...defaultProps} />);

      const innovationCheckbox = screen.getByRole('checkbox', { name: /innovation/i });
      await user.click(innovationCheckbox);

      expect(innovationCheckbox).toBeChecked();
    });

    it('should handle difficulty level selection', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      render(<AIQuestionnaireForm {...defaultProps} />);

      const hardRadio = screen.getByRole('radio', { name: /hard - advanced/i });
      await user.click(hardRadio);

      expect(hardRadio).toBeChecked();
    });

    it('should update estimated duration when parameters change', async () => {
      render(<AIQuestionnaireForm {...defaultProps} />);

      // Change question count
      const slider = screen.getByRole('slider');
      fireEvent.change(slider, { target: { value: '40' } });

      await waitFor(() => {
        // Duration should update based on question count
        expect(screen.getByText(/estimated duration/i)).toBeInTheDocument();
      });
    });
  });

  describe('Form Submission and API Integration', () => {
    it('should successfully submit form with valid data', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      mockAiQuestionnaireService.generateQuestionnaire.mockResolvedValue(mockGenerateResponse);
      
      render(<AIQuestionnaireForm {...defaultProps} />);

      // Fill in required fields
      await user.type(screen.getByLabelText(/position type/i), 'Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockAiQuestionnaireService.generateQuestionnaire).toHaveBeenCalledWith(
          expect.objectContaining({
            position_type: 'Software Engineer',
            industry: 'Technology',
            company_size: 'medium',
            question_count: 20,
            difficulty_level: 'medium'
          })
        );
        expect(mockOnGenerate).toHaveBeenCalledWith(mockGenerateResponse);
      });
    });

    it('should handle API errors gracefully', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      const apiError = {
        error_code: 'AI_GENERATION_FAILED',
        detail: 'AI service temporarily unavailable'
      };
      mockAiQuestionnaireService.generateQuestionnaire.mockRejectedValue(apiError);

      render(<AIQuestionnaireForm {...defaultProps} />);

      // Fill in required fields and submit
      await user.type(screen.getByLabelText(/position type/i), 'Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith('AI service temporarily unavailable');
      });
    });

    it('should disable form during generation', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      mockAiQuestionnaireService.generateQuestionnaire.mockResolvedValue(mockGenerateResponse);

      render(<AIQuestionnaireForm {...defaultProps} />);

      // Fill in required fields
      await user.type(screen.getByLabelText(/position type/i), 'Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      // Button should be disabled during generation
      expect(submitButton).toBeDisabled();
      expect(screen.getByText(/generating/i)).toBeInTheDocument();
    });
  });

  describe('Progress Monitoring', () => {
    it('should poll for progress updates during generation', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      
      mockAiQuestionnaireService.generateQuestionnaire.mockResolvedValue(mockGenerateResponse);
      mockAiQuestionnaireService.getGenerationProgress
        .mockResolvedValueOnce(mockProgressStates[0])
        .mockResolvedValueOnce(mockProgressStates[1])
        .mockResolvedValueOnce(mockProgressStates[2])
        .mockResolvedValue(mockProgressStates[3]);

      render(<AIQuestionnaireForm {...defaultProps} />);

      // Fill and submit form
      await user.type(screen.getByLabelText(/position type/i), 'Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      // Fast-forward through polling intervals
      act(() => {
        jest.advanceTimersByTime(2000); // First poll
      });

      act(() => {
        jest.advanceTimersByTime(2000); // Second poll
      });

      act(() => {
        jest.advanceTimersByTime(2000); // Third poll
      });

      await waitFor(() => {
        expect(mockAiQuestionnaireService.getGenerationProgress).toHaveBeenCalledWith('gen-123456');
        expect(mockOnProgress).toHaveBeenCalledWith(mockProgressStates[3]);
      });
    });

    it('should display progress information', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      
      mockAiQuestionnaireService.generateQuestionnaire.mockResolvedValue(mockGenerateResponse);
      mockAiQuestionnaireService.getGenerationProgress.mockResolvedValue(mockProgressStates[2]);

      render(<AIQuestionnaireForm {...defaultProps} />);

      // Submit form
      await user.type(screen.getByLabelText(/position type/i), 'Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      act(() => {
        jest.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        expect(screen.getByText('Generating questions...')).toBeInTheDocument();
        expect(screen.getByText('50%')).toBeInTheDocument();
      });
    });

    it('should handle progress polling errors', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      
      mockAiQuestionnaireService.generateQuestionnaire.mockResolvedValue(mockGenerateResponse);
      mockAiQuestionnaireService.getGenerationProgress.mockRejectedValue(
        new Error('Failed to fetch progress')
      );

      render(<AIQuestionnaireForm {...defaultProps} />);

      // Submit form
      await user.type(screen.getByLabelText(/position type/i), 'Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      act(() => {
        jest.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith('Failed to check generation progress');
      });
    });

    it('should handle generation failure', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      
      const failedProgress: GenerationProgress = {
        generation_id: 'gen-123456',
        status: GenerationStatus.FAILED,
        progress_percentage: 30,
        current_step: 'Generation failed',
        estimated_remaining_time: 0,
        error_message: 'AI model timeout',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:01:00Z'
      };

      mockAiQuestionnaireService.generateQuestionnaire.mockResolvedValue(mockGenerateResponse);
      mockAiQuestionnaireService.getGenerationProgress.mockResolvedValue(failedProgress);

      render(<AIQuestionnaireForm {...defaultProps} />);

      // Submit form
      await user.type(screen.getByLabelText(/position type/i), 'Software Engineer');
      
      const industrySelect = screen.getByLabelText(/industry/i);
      await user.click(industrySelect);
      await user.click(screen.getByText('Technology'));

      const companySizeSelect = screen.getByLabelText(/company size/i);
      await user.click(companySizeSelect);
      await user.click(screen.getByText(/medium/i));

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      act(() => {
        jest.advanceTimersByTime(2000);
      });

      await waitFor(() => {
        expect(mockOnError).toHaveBeenCalledWith('AI model timeout');
      });
    });
  });

  describe('Accessibility', () => {
    it('should have proper form labels and ARIA attributes', () => {
      render(<AIQuestionnaireForm {...defaultProps} />);

      // Check that all inputs have labels
      expect(screen.getByLabelText(/position type/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/industry/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/company size/i)).toBeInTheDocument();
      
      // Check button accessibility
      const submitButton = screen.getByRole('button', { name: /generate/i });
      expect(submitButton).toBeInTheDocument();
    });

    it('should provide appropriate error messages', async () => {
      const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
      render(<AIQuestionnaireForm {...defaultProps} />);

      const submitButton = screen.getByRole('button', { name: /generate/i });
      await user.click(submitButton);

      await waitFor(() => {
        // Error messages should be associated with their inputs
        expect(screen.getByText(/position type is required/i)).toBeInTheDocument();
        expect(screen.getByText(/company size is required/i)).toBeInTheDocument();
        expect(screen.getByText(/industry is required/i)).toBeInTheDocument();
      });
    });
  });
});