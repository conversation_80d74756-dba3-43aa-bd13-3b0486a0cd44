/**
 * Component tests for EvaluationReport
 * Tests report display, data visualization, and user interactions
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { EvaluationReport } from '@/components/evaluation/EvaluationReport';
import {
  EvaluationReport as IEvaluationReport,
  EvaluationDimension,
  EvaluationScores,
  QuestionResponse
} from '@/types/evaluation';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
}));

// Mock Recharts components
jest.mock('recharts', () => ({
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
  RadarChart: ({ children }: any) => <div data-testid="radar-chart">{children}</div>,
  PolarGrid: () => <div data-testid="polar-grid" />,
  PolarAngleAxis: () => <div data-testid="polar-angle-axis" />,
  PolarRadiusAxis: () => <div data-testid="polar-radius-axis" />,
  Radar: () => <div data-testid="radar" />,
  Legend: () => <div data-testid="legend" />,
  Tooltip: () => <div data-testid="tooltip" />,
  PieChart: ({ children }: any) => <div data-testid="pie-chart">{children}</div>,
  Pie: () => <div data-testid="pie" />,
  Cell: () => <div data-testid="cell" />,
  BarChart: ({ children }: any) => <div data-testid="bar-chart">{children}</div>,
  Bar: () => <div data-testid="bar" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
}));

// Mock data
const mockQuestionResponses: QuestionResponse[] = [
  {
    id: '1234567890123456790',
    question_id: '1234567890123456791',
    question_text: 'What is your experience with cloud computing?',
    question_type: 'text_long',
    response_text: 'I have 5 years of experience with AWS, Azure, and GCP.',
    response_data: null,
    scores: {
      digital_literacy: 85,
      industry_skill: 70,
      position_skill: 90,
      innovation: 75,
      learning_potential: 80
    },
    evaluation_notes: ['Strong technical background', 'Good practical experience'],
    time_spent: 300,
    created_at: '2024-01-01T12:00:00Z'
  },
  {
    id: '1234567890123456792',
    question_id: '1234567890123456793',
    question_text: 'How do you handle technical challenges?',
    question_type: 'text_short',
    response_text: 'I approach challenges systematically and seek help when needed.',
    response_data: null,
    scores: {
      digital_literacy: 70,
      industry_skill: 80,
      position_skill: 85,
      innovation: 90,
      learning_potential: 85
    },
    evaluation_notes: ['Good problem-solving approach'],
    time_spent: 180,
    created_at: '2024-01-01T12:05:00Z'
  }
];

const mockEvaluationScores: EvaluationScores = {
  digital_literacy: {
    score: 78,
    max_score: 100,
    percentage: 78,
    weight: 0.20,
    weighted_score: 15.6
  },
  industry_skill: {
    score: 75,
    max_score: 100,
    percentage: 75,
    weight: 0.25,
    weighted_score: 18.75
  },
  position_skill: {
    score: 87,
    max_score: 100,
    percentage: 87,
    weight: 0.30,
    weighted_score: 26.1
  },
  innovation: {
    score: 82,
    max_score: 100,
    percentage: 82,
    weight: 0.15,
    weighted_score: 12.3
  },
  learning_potential: {
    score: 83,
    max_score: 100,
    percentage: 83,
    weight: 0.10,
    weighted_score: 8.3
  }
};

const mockEvaluationReport: IEvaluationReport = {
  id: '1234567890123456789',
  candidate_id: '1234567890123456788',
  candidate_name: 'John Doe',
  questionnaire_id: '1234567890123456787',
  questionnaire_title: 'Software Engineer Assessment',
  position_type: 'Software Engineer',
  industry: 'Technology',
  
  // Overall scores
  dci_score: 81.05,
  jfs_score: 84.5,
  overall_score: 82.8,
  percentile_rank: 85,
  
  // Dimensional scores
  scores: mockEvaluationScores,
  
  // Question responses
  question_responses: mockQuestionResponses,
  
  // Performance metrics
  total_questions: 20,
  completed_questions: 18,
  completion_rate: 0.90,
  total_time_spent: 2400,
  average_time_per_question: 133,
  
  // Analysis results
  strengths: [
    'Strong position-specific skills',
    'Good innovation mindset',
    'Solid learning potential'
  ],
  improvement_areas: [
    'Digital literacy could be enhanced',
    'Industry knowledge needs development'
  ],
  recommendations: [
    'Consider additional training in cloud technologies',
    'Recommend for mid-level position with mentoring support'
  ],
  
  // Risk assessment
  risk_level: 'low',
  risk_factors: [],
  confidence_score: 0.87,
  
  // Metadata
  evaluation_method: 'ai_assisted',
  evaluator_id: '1234567890123456786',
  ai_model_used: 'gpt-4',
  created_at: '2024-01-01T12:00:00Z',
  updated_at: '2024-01-01T12:30:00Z'
};

describe('EvaluationReport', () => {
  const mockOnExport = jest.fn();

  const defaultProps = {
    report: mockEvaluationReport,
    onExport: mockOnExport
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Report Header and Basic Information', () => {
    it('should render candidate and questionnaire information', () => {
      render(<EvaluationReport {...defaultProps} />);

      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('Software Engineer Assessment')).toBeInTheDocument();
      expect(screen.getByText('Software Engineer')).toBeInTheDocument();
      expect(screen.getByText('Technology')).toBeInTheDocument();
    });

    it('should display overall scores prominently', () => {
      render(<EvaluationReport {...defaultProps} />);

      expect(screen.getByText('82.8')).toBeInTheDocument(); // Overall score
      expect(screen.getByText('81.05')).toBeInTheDocument(); // DCI score
      expect(screen.getByText('84.5')).toBeInTheDocument(); // JFS score
    });

    it('should show percentile rank', () => {
      render(<EvaluationReport {...defaultProps} />);

      expect(screen.getByText(/85/)).toBeInTheDocument(); // Percentile rank
      expect(screen.getByText(/percentile/i)).toBeInTheDocument();
    });

    it('should display completion metrics', () => {
      render(<EvaluationReport {...defaultProps} />);

      expect(screen.getByText('18/20')).toBeInTheDocument(); // Completed questions
      expect(screen.getByText('90%')).toBeInTheDocument(); // Completion rate
    });
  });

  describe('Dimensional Scores Display', () => {
    it('should render all five evaluation dimensions', () => {
      render(<EvaluationReport {...defaultProps} />);

      // Check for dimension names
      expect(screen.getByText(/digital literacy/i)).toBeInTheDocument();
      expect(screen.getByText(/industry skill/i)).toBeInTheDocument();
      expect(screen.getByText(/position skill/i)).toBeInTheDocument();
      expect(screen.getByText(/innovation/i)).toBeInTheDocument();
      expect(screen.getByText(/learning potential/i)).toBeInTheDocument();
    });

    it('should display dimension scores and percentages', () => {
      render(<EvaluationReport {...defaultProps} />);

      // Check for specific scores
      expect(screen.getByText('78')).toBeInTheDocument(); // Digital literacy
      expect(screen.getByText('75')).toBeInTheDocument(); // Industry skill
      expect(screen.getByText('87')).toBeInTheDocument(); // Position skill
      expect(screen.getByText('82')).toBeInTheDocument(); // Innovation
      expect(screen.getByText('83')).toBeInTheDocument(); // Learning potential
    });

    it('should show weighted contributions', () => {
      render(<EvaluationReport {...defaultProps} />);

      // Check for weight information
      expect(screen.getByText('20%')).toBeInTheDocument(); // Digital literacy weight
      expect(screen.getByText('25%')).toBeInTheDocument(); // Industry skill weight
      expect(screen.getByText('30%')).toBeInTheDocument(); // Position skill weight
      expect(screen.getByText('15%')).toBeInTheDocument(); // Innovation weight
      expect(screen.getByText('10%')).toBeInTheDocument(); // Learning potential weight
    });
  });

  describe('Charts and Visualizations', () => {
    it('should render radar chart for dimensional scores', () => {
      render(<EvaluationReport {...defaultProps} />);

      expect(screen.getByTestId('radar-chart')).toBeInTheDocument();
      expect(screen.getByTestId('responsive-container')).toBeInTheDocument();
    });

    it('should render charts in tabs interface', () => {
      render(<EvaluationReport {...defaultProps} />);

      // Look for tab interface
      expect(screen.getByRole('tablist')).toBeInTheDocument();
    });

    it('should handle chart interactions', async () => {
      const user = userEvent.setup();
      render(<EvaluationReport {...defaultProps} />);

      // Test tab switching
      const tabs = screen.getAllByRole('tab');
      if (tabs.length > 1) {
        await user.click(tabs[1]);
        // Should switch to different visualization
      }
    });
  });

  describe('Strengths and Recommendations', () => {
    it('should display candidate strengths', () => {
      render(<EvaluationReport {...defaultProps} />);

      expect(screen.getByText('Strong position-specific skills')).toBeInTheDocument();
      expect(screen.getByText('Good innovation mindset')).toBeInTheDocument();
      expect(screen.getByText('Solid learning potential')).toBeInTheDocument();
    });

    it('should display improvement areas', () => {
      render(<EvaluationReport {...defaultProps} />);

      expect(screen.getByText('Digital literacy could be enhanced')).toBeInTheDocument();
      expect(screen.getByText('Industry knowledge needs development')).toBeInTheDocument();
    });

    it('should display recommendations', () => {
      render(<EvaluationReport {...defaultProps} />);

      expect(screen.getByText(/additional training in cloud technologies/)).toBeInTheDocument();
      expect(screen.getByText(/mid-level position with mentoring support/)).toBeInTheDocument();
    });
  });

  describe('Question Response Analysis', () => {
    it('should display question responses when expanded', async () => {
      const user = userEvent.setup();
      render(<EvaluationReport {...defaultProps} />);

      // Look for expandable sections or detailed view
      const detailButton = screen.getByText(/question responses/i) || screen.getByText(/details/i);
      if (detailButton) {
        await user.click(detailButton);

        await waitFor(() => {
          expect(screen.getByText('What is your experience with cloud computing?')).toBeInTheDocument();
          expect(screen.getByText(/5 years of experience with AWS/)).toBeInTheDocument();
        });
      }
    });

    it('should show individual question scores', async () => {
      const user = userEvent.setup();
      render(<EvaluationReport {...defaultProps} />);

      // If there's a detailed view of responses
      const expandButton = screen.queryByRole('button', { name: /expand/i }) || 
                          screen.queryByRole('button', { name: /details/i });
      
      if (expandButton) {
        await user.click(expandButton);
        
        // Check for question-specific scores
        await waitFor(() => {
          expect(screen.getByText('85')).toBeInTheDocument(); // Digital literacy for first question
          expect(screen.getByText('90')).toBeInTheDocument(); // Position skill for first question
        });
      }
    });

    it('should display response evaluation notes', async () => {
      const user = userEvent.setup();
      render(<EvaluationReport {...defaultProps} />);

      // Look for evaluation notes
      const detailsSection = screen.queryByText(/evaluation notes/i);
      if (detailsSection) {
        expect(screen.getByText('Strong technical background')).toBeInTheDocument();
        expect(screen.getByText('Good practical experience')).toBeInTheDocument();
      }
    });
  });

  describe('Risk Assessment Display', () => {
    it('should show risk level indicator', () => {
      render(<EvaluationReport {...defaultProps} />);

      expect(screen.getByText(/low/i)).toBeInTheDocument(); // Risk level
    });

    it('should display confidence score', () => {
      render(<EvaluationReport {...defaultProps} />);

      expect(screen.getByText('87%')).toBeInTheDocument(); // Confidence score
    });

    it('should handle high risk scenarios', () => {
      const highRiskReport = {
        ...mockEvaluationReport,
        risk_level: 'high',
        risk_factors: ['Incomplete responses', 'Low technical scores'],
        confidence_score: 0.65
      };

      render(<EvaluationReport report={highRiskReport} />);

      expect(screen.getByText(/high/i)).toBeInTheDocument();
      expect(screen.getByText('65%')).toBeInTheDocument();
    });
  });

  describe('Performance Metrics', () => {
    it('should display time-related metrics', () => {
      render(<EvaluationReport {...defaultProps} />);

      // Look for time spent information
      expect(screen.getByText(/40/)).toBeInTheDocument(); // Total time in minutes (2400 seconds)
      expect(screen.getByText(/2.2/)).toBeInTheDocument(); // Average time per question in minutes
    });

    it('should show completion statistics', () => {
      render(<EvaluationReport {...defaultProps} />);

      expect(screen.getByText('18/20')).toBeInTheDocument(); // Questions completed
      expect(screen.getByText('90%')).toBeInTheDocument(); // Completion rate
    });
  });

  describe('Export and Actions', () => {
    it('should render export button when callback provided', () => {
      render(<EvaluationReport {...defaultProps} />);

      const exportButton = screen.getByRole('button', { name: /export/i });
      expect(exportButton).toBeInTheDocument();
    });

    it('should call export callback when clicked', async () => {
      const user = userEvent.setup();
      render(<EvaluationReport {...defaultProps} />);

      const exportButton = screen.getByRole('button', { name: /export/i });
      await user.click(exportButton);

      expect(mockOnExport).toHaveBeenCalledTimes(1);
    });

    it('should not render export button when callback not provided', () => {
      render(<EvaluationReport report={mockEvaluationReport} />);

      const exportButton = screen.queryByRole('button', { name: /export/i });
      expect(exportButton).not.toBeInTheDocument();
    });

    it('should render other action buttons', () => {
      render(<EvaluationReport {...defaultProps} />);

      // Look for share, print, or other action buttons
      const actionButtons = screen.getAllByRole('button');
      expect(actionButtons.length).toBeGreaterThan(1); // Should have multiple action buttons
    });
  });

  describe('Comparison Mode', () => {
    it('should render in comparison mode when enabled', () => {
      render(<EvaluationReport {...defaultProps} showComparison={true} />);

      // Should render normally but potentially with comparison indicators
      expect(screen.getByText('John Doe')).toBeInTheDocument();
    });

    it('should handle comparison-specific styling', () => {
      const { container } = render(
        <EvaluationReport {...defaultProps} showComparison={true} className="comparison-mode" />
      );

      expect(container.firstChild).toHaveClass('comparison-mode');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle missing optional data gracefully', () => {
      const incompleteReport = {
        ...mockEvaluationReport,
        question_responses: [],
        strengths: [],
        improvement_areas: [],
        recommendations: []
      };

      render(<EvaluationReport report={incompleteReport} />);

      // Should still render basic information
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('82.8')).toBeInTheDocument();
    });

    it('should handle zero scores correctly', () => {
      const zeroScoreReport = {
        ...mockEvaluationReport,
        scores: {
          ...mockEvaluationScores,
          digital_literacy: {
            ...mockEvaluationScores.digital_literacy,
            score: 0,
            percentage: 0
          }
        }
      };

      render(<EvaluationReport report={zeroScoreReport} />);

      expect(screen.getByText('0')).toBeInTheDocument();
    });

    it('should handle very high scores correctly', () => {
      const perfectScoreReport = {
        ...mockEvaluationReport,
        overall_score: 100,
        dci_score: 100,
        jfs_score: 100,
        percentile_rank: 99
      };

      render(<EvaluationReport report={perfectScoreReport} />);

      expect(screen.getByText('100')).toBeInTheDocument();
      expect(screen.getByText('99')).toBeInTheDocument(); // Percentile
    });

    it('should handle incomplete questionnaire responses', () => {
      const incompleteReport = {
        ...mockEvaluationReport,
        completed_questions: 5,
        total_questions: 20,
        completion_rate: 0.25
      };

      render(<EvaluationReport report={incompleteReport} />);

      expect(screen.getByText('5/20')).toBeInTheDocument();
      expect(screen.getByText('25%')).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('should have proper heading structure', () => {
      render(<EvaluationReport {...defaultProps} />);

      const headings = screen.getAllByRole('heading');
      expect(headings.length).toBeGreaterThan(0);
    });

    it('should have accessible labels for scores', () => {
      render(<EvaluationReport {...defaultProps} />);

      // Progress bars and scores should have accessible labels
      const progressBars = screen.getAllByRole('progressbar');
      progressBars.forEach(bar => {
        expect(bar).toHaveAttribute('aria-label');
      });
    });

    it('should support keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<EvaluationReport {...defaultProps} />);

      // Tab navigation should work for interactive elements
      const buttons = screen.getAllByRole('button');
      if (buttons.length > 0) {
        buttons[0].focus();
        expect(buttons[0]).toHaveFocus();

        await user.tab();
        if (buttons.length > 1) {
          expect(buttons[1]).toHaveFocus();
        }
      }
    });
  });
});