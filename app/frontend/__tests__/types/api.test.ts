/**
 * Unit tests for API types and utilities
 * Tests SnowflakeID conversion, validation, and utility functions
 */
import {
  toSnowflakeID,
  isValidSnowflakeID,
  formatDateToISO,
  parseDateFromISO,
  isApiError
} from '@/types/api';

describe('API Types and Utilities', () => {
  describe('toSnowflakeID', () => {
    it('should convert number to string', () => {
      const result = toSnowflakeID(1234567890123456789);
      expect(result).toBe('1234567890123456789');
      expect(typeof result).toBe('string');
    });

    it('should keep string as string', () => {
      const result = toSnowflakeID('1234567890123456789');
      expect(result).toBe('1234567890123456789');
      expect(typeof result).toBe('string');
    });

    it('should handle string numbers correctly', () => {
      const result = toSnowflakeID('9876543210987654321');
      expect(result).toBe('9876543210987654321');
    });

    it('should return undefined for null input', () => {
      const result = toSnowflakeID(null);
      expect(result).toBeUndefined();
    });

    it('should return undefined for undefined input', () => {
      const result = toSnowflakeID(undefined);
      expect(result).toBeUndefined();
    });

    it('should handle zero correctly', () => {
      const result = toSnowflakeID(0);
      expect(result).toBe('0');
    });

    it('should handle negative numbers (edge case)', () => {
      const result = toSnowflakeID(-123);
      expect(result).toBe('-123');
    });

    it('should handle very large numbers', () => {
      const largeNumber = Number.MAX_SAFE_INTEGER;
      const result = toSnowflakeID(largeNumber);
      expect(result).toBe(largeNumber.toString());
    });

    it('should handle numbers larger than MAX_SAFE_INTEGER', () => {
      // This tests the boundary case where JavaScript number precision might be lost
      const veryLargeNumber = 9223372036854775807; // Max 64-bit signed integer
      const result = toSnowflakeID(veryLargeNumber);
      expect(result).toBe('9223372036854775807');
    });
  });

  describe('isValidSnowflakeID', () => {
    it('should validate correct Snowflake ID format', () => {
      const validIds = [
        '1234567890123456789',
        '9876543210987654321',
        '1',
        '123',
        '999999999999999999'
      ];

      validIds.forEach(id => {
        expect(isValidSnowflakeID(id)).toBe(true);
      });
    });

    it('should reject invalid Snowflake ID formats', () => {
      const invalidIds = [
        '', // empty string
        '0', // zero is not valid for Snowflake IDs
        '-123', // negative numbers
        'abc123', // contains letters
        '123abc', // contains letters
        '12.34', // contains decimal
        '12 34', // contains spaces
        '12,34', // contains comma
        '12e3', // scientific notation
        'NaN', // not a number string
        'null', // null string
        'undefined' // undefined string
      ];

      invalidIds.forEach(id => {
        expect(isValidSnowflakeID(id)).toBe(false);
      });
    });

    it('should handle very large valid IDs', () => {
      // Test with realistic Twitter-style Snowflake IDs
      const realSnowflakeIds = [
        '1420070400000000000', // Approximately Twitter's epoch
        '1577836800000000000', // 2020-01-01 in Snowflake format
        '9223372036854775807'  // Max 64-bit signed integer
      ];

      realSnowflakeIds.forEach(id => {
        expect(isValidSnowflakeID(id)).toBe(true);
      });
    });

    it('should reject edge cases', () => {
      const edgeCases = [
        '00123', // leading zeros might be problematic
        '+123', // explicit positive sign
        '12\n34', // newline character
        '12\t34', // tab character
        ' 123 ', // leading/trailing whitespace
        '1.23e5', // scientific notation
        'Infinity', // infinity
        '-Infinity' // negative infinity
      ];

      edgeCases.forEach(id => {
        expect(isValidSnowflakeID(id)).toBe(false);
      });
    });
  });

  describe('formatDateToISO', () => {
    it('should format date to ISO string', () => {
      const date = new Date('2024-01-01T12:00:00Z');
      const result = formatDateToISO(date);
      expect(result).toBe('2024-01-01T12:00:00.000Z');
    });

    it('should handle different dates correctly', () => {
      const testCases = [
        new Date('2023-12-31T23:59:59Z'),
        new Date('2024-06-15T08:30:00Z'),
        new Date('2024-02-29T00:00:00Z'), // leap year
      ];

      testCases.forEach(date => {
        const result = formatDateToISO(date);
        expect(result).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/);
        expect(new Date(result)).toEqual(date);
      });
    });

    it('should handle date with milliseconds', () => {
      const date = new Date('2024-01-01T12:00:00.123Z');
      const result = formatDateToISO(date);
      expect(result).toBe('2024-01-01T12:00:00.123Z');
    });

    it('should handle current date', () => {
      const now = new Date();
      const result = formatDateToISO(now);
      expect(result).toBe(now.toISOString());
    });
  });

  describe('parseDateFromISO', () => {
    it('should parse valid ISO string to date', () => {
      const isoString = '2024-01-01T12:00:00.000Z';
      const result = parseDateFromISO(isoString);
      expect(result).toEqual(new Date('2024-01-01T12:00:00.000Z'));
    });

    it('should parse ISO string without milliseconds', () => {
      const isoString = '2024-01-01T12:00:00Z';
      const result = parseDateFromISO(isoString);
      expect(result).toEqual(new Date('2024-01-01T12:00:00Z'));
    });

    it('should handle different ISO formats', () => {
      const testCases = [
        '2024-01-01T12:00:00Z',
        '2024-01-01T12:00:00.000Z',
        '2024-01-01T12:00:00.123Z',
        '2023-12-31T23:59:59.999Z'
      ];

      testCases.forEach(isoString => {
        const result = parseDateFromISO(isoString);
        expect(result).toBeInstanceOf(Date);
        expect(result.toISOString()).toBe(isoString);
      });
    });

    it('should round-trip with formatDateToISO', () => {
      const originalDate = new Date('2024-01-01T12:00:00.456Z');
      const isoString = formatDateToISO(originalDate);
      const parsedDate = parseDateFromISO(isoString);
      expect(parsedDate).toEqual(originalDate);
    });

    it('should handle invalid ISO strings gracefully', () => {
      const invalidStrings = [
        'invalid-date',
        '2024-13-01T12:00:00Z', // invalid month
        '2024-01-32T12:00:00Z', // invalid day
        '2024-01-01T25:00:00Z', // invalid hour
        ''
      ];

      invalidStrings.forEach(invalidString => {
        const result = parseDateFromISO(invalidString);
        expect(result.toString()).toBe('Invalid Date');
      });
    });
  });

  describe('isApiError', () => {
    it('should identify valid API error objects', () => {
      const validErrors = [
        {
          error_code: 'AUTH_TOKEN_EXPIRED',
          detail: 'Token has expired'
        },
        {
          error_code: 'VALIDATION_ERROR',
          detail: 'Invalid input data',
          details: { field: 'email' }
        },
        {
          error_code: 'NOT_FOUND',
          detail: 'Resource not found'
        }
      ];

      validErrors.forEach(error => {
        expect(isApiError(error)).toBe(true);
      });
    });

    it('should reject invalid error objects', () => {
      const invalidErrors = [
        null,
        undefined,
        {},
        { error_code: 'TEST' }, // missing detail
        { detail: 'Test error' }, // missing error_code
        { error_code: 123, detail: 'Test' }, // wrong type for error_code
        { error_code: 'TEST', detail: 123 }, // wrong type for detail
        'string error',
        123,
        [],
        { message: 'Regular error object' }
      ];

      invalidErrors.forEach(error => {
        expect(isApiError(error)).toBe(false);
      });
    });

    it('should handle objects with additional properties', () => {
      const errorWithExtras = {
        error_code: 'CUSTOM_ERROR',
        detail: 'Custom error message',
        timestamp: '2024-01-01T12:00:00Z',
        trace_id: 'trace-123',
        additional_info: { field: 'value' }
      };

      expect(isApiError(errorWithExtras)).toBe(true);
    });

    it('should be type-safe with TypeScript', () => {
      const unknownError: unknown = {
        error_code: 'TEST_ERROR',
        detail: 'Test error message'
      };

      if (isApiError(unknownError)) {
        // TypeScript should now know that unknownError has error_code and detail
        expect(unknownError.error_code).toBe('TEST_ERROR');
        expect(unknownError.detail).toBe('Test error message');
      }
    });
  });

  describe('Edge Cases and Integration', () => {
    it('should handle SnowflakeID conversion in typical API response', () => {
      const mockApiResponse = {
        id: 1234567890123456789,
        name: 'Test User',
        created_at: '2024-01-01T12:00:00Z'
      };

      // Simulate how we'd process this in real code
      const processedId = toSnowflakeID(mockApiResponse.id);
      expect(processedId).toBe('1234567890123456789');
      expect(isValidSnowflakeID(processedId!)).toBe(true);
    });

    it('should handle date processing in API context', () => {
      const mockTimestamp = '2024-01-01T12:00:00.123Z';
      const parsed = parseDateFromISO(mockTimestamp);
      const formatted = formatDateToISO(parsed);
      
      expect(formatted).toBe(mockTimestamp);
    });

    it('should handle error detection in API error handling', () => {
      const mockApiError = {
        error_code: 'CANDIDATE_NOT_FOUND',
        detail: 'Candidate with ID 123456789 not found',
        details: {
          candidate_id: '123456789',
          searched_at: '2024-01-01T12:00:00Z'
        }
      };

      expect(isApiError(mockApiError)).toBe(true);
      
      // Simulate error handling
      if (isApiError(mockApiError)) {
        expect(mockApiError.error_code).toBe('CANDIDATE_NOT_FOUND');
        expect(mockApiError.detail).toContain('123456789');
      }
    });

    it('should handle SnowflakeID arrays', () => {
      const idArray = [
        1234567890123456789,
        '2345678901234567890',
        3456789012345678901
      ];

      const processedIds = idArray
        .map(id => toSnowflakeID(id))
        .filter(id => id !== undefined);

      expect(processedIds).toHaveLength(3);
      expect(processedIds.every(id => isValidSnowflakeID(id!))).toBe(true);
    });

    it('should handle mixed type scenarios', () => {
      const mixedData = {
        string_id: '1234567890123456789',
        number_id: 9876543210987654321,
        null_id: null,
        undefined_id: undefined,
        zero_id: 0
      };

      const processed = {
        string_id: toSnowflakeID(mixedData.string_id),
        number_id: toSnowflakeID(mixedData.number_id),
        null_id: toSnowflakeID(mixedData.null_id),
        undefined_id: toSnowflakeID(mixedData.undefined_id),
        zero_id: toSnowflakeID(mixedData.zero_id)
      };

      expect(processed.string_id).toBe('1234567890123456789');
      expect(processed.number_id).toBe('9876543210987654321');
      expect(processed.null_id).toBeUndefined();
      expect(processed.undefined_id).toBeUndefined();
      expect(processed.zero_id).toBe('0');

      // Validate only non-undefined IDs (zero is not a valid Snowflake ID)
      expect(isValidSnowflakeID(processed.string_id!)).toBe(true);
      expect(isValidSnowflakeID(processed.number_id!)).toBe(true);
      expect(isValidSnowflakeID(processed.zero_id!)).toBe(false); // Zero is not valid
    });
  });
});