/**
 * Unit tests for AI Questionnaire Service
 * Tests critical API integration, error handling, and business logic
 */
import { aiQuestionnaireService } from '@/services/aiQuestionnaireService';
import {
  QuestionnaireGenerateRequest,
  QuestionnaireGenerateResponse,
  GenerationProgress,
  GenerationStatus,
  QuestionnaireTemplate,
  TemplateListResponse,
  Questionnaire,
  QuestionnaireListResponse,
  QuestionType,
  QuestionCategory,
  DifficultyLevel
} from '@/types/questionnaire';
import { SnowflakeID } from '@/types/api';

// Mock the API client
jest.mock('@/lib/api/client', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
  },
}));

import { apiClient } from '@/lib/api/client';

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

// Mock data
const mockGenerateRequest: QuestionnaireGenerateRequest = {
  position_type: 'Software Engineer',
  company_size: 'medium',
  industry: 'Technology',
  question_count: 20,
  difficulty_level: 'medium',
  focus_areas: ['digital_literacy', 'position_skill'],
  include_behavioral: true,
  include_technical: true,
  include_situational: false,
  language: 'en',
  additional_requirements: 'Focus on cloud technologies'
};

const mockGenerateResponse: QuestionnaireGenerateResponse = {
  questionnaire_id: '1234567890123456789',
  title: 'Software Engineer Assessment',
  description: 'AI-generated questionnaire for software engineer position',
  questions: [
    {
      id: '1234567890123456790',
      question_text: 'What is your experience with cloud computing?',
      question_type: QuestionType.TEXT_LONG,
      category: QuestionCategory.TECHNICAL,
      difficulty: DifficultyLevel.MEDIUM,
      estimated_time: 5,
      evaluation_criteria: ['Technical depth', 'Practical experience'],
      ai_rationale: 'This question evaluates cloud computing knowledge'
    }
  ],
  estimated_duration: 40,
  generation_metadata: {
    generation_time: 30,
    ai_model_used: 'gpt-4',
    generation_id: 'gen-123456',
    quality_score: 0.85
  },
  created_at: '2024-01-01T00:00:00Z'
};

const mockGenerationProgress: GenerationProgress = {
  generation_id: 'gen-123456',
  status: GenerationStatus.GENERATING_QUESTIONS,
  progress_percentage: 50,
  current_step: 'Generating questions for position skills...',
  estimated_remaining_time: 30,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:01:00Z'
};

const mockTemplate: QuestionnaireTemplate = {
  id: '1234567890123456791',
  name: 'Software Engineer Template',
  description: 'Standard template for software engineering positions',
  position_types: ['Software Engineer', 'Full Stack Developer'],
  industries: ['Technology', 'Finance'],
  default_settings: {
    question_count: 25,
    difficulty_level: 'medium',
    focus_areas: ['digital_literacy', 'position_skill', 'innovation']
  },
  is_system: true,
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

const mockTemplateList: TemplateListResponse = {
  items: [mockTemplate],
  total: 1,
  skip: 0,
  limit: 20
};

const mockQuestionnaire: Questionnaire = {
  id: '1234567890123456792',
  title: 'Software Engineer Assessment',
  description: 'Comprehensive assessment for software engineer role',
  position_type: 'Software Engineer',
  industry: 'Technology',
  difficulty_level: 'medium',
  estimated_duration: 45,
  question_count: 20,
  questions: [],
  is_active: true,
  is_ai_generated: true,
  generation_metadata: {
    ai_model_used: 'gpt-4',
    generation_id: 'gen-123456',
    quality_score: 0.85
  },
  created_by: '1234567890123456793',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z'
};

describe('AIQuestionnaireService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Generation Operations', () => {
    describe('generateQuestionnaire', () => {
      it('should successfully generate questionnaire', async () => {
        mockApiClient.post.mockResolvedValue(mockGenerateResponse);

        const result = await aiQuestionnaireService.generateQuestionnaire(mockGenerateRequest);

        expect(mockApiClient.post).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/generate',
          mockGenerateRequest
        );
        expect(result).toEqual(mockGenerateResponse);
      });

      it('should handle generation errors with proper error propagation', async () => {
        const errorResponse = {
          error_code: 'AI_GENERATION_FAILED',
          detail: 'AI service temporarily unavailable'
        };
        mockApiClient.post.mockRejectedValue(errorResponse);

        await expect(aiQuestionnaireService.generateQuestionnaire(mockGenerateRequest))
          .rejects.toEqual(errorResponse);
      });

      it('should handle validation errors for invalid parameters', async () => {
        const validationError = {
          error_code: 'VALIDATION_ERROR',
          detail: 'Invalid question count',
          isValidationError: true,
          statusCode: 422
        };
        mockApiClient.post.mockRejectedValue(validationError);

        const invalidRequest = { ...mockGenerateRequest, question_count: 0 };

        await expect(aiQuestionnaireService.generateQuestionnaire(invalidRequest))
          .rejects.toEqual(validationError);
      });
    });

    describe('getGenerationProgress', () => {
      it('should fetch generation progress by ID', async () => {
        mockApiClient.get.mockResolvedValue(mockGenerationProgress);

        const result = await aiQuestionnaireService.getGenerationProgress('gen-123456');

        expect(mockApiClient.get).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/progress/gen-123456'
        );
        expect(result).toEqual(mockGenerationProgress);
      });

      it('should handle progress polling for completed generation', async () => {
        const completedProgress = {
          ...mockGenerationProgress,
          status: GenerationStatus.COMPLETED,
          progress_percentage: 100,
          current_step: 'Generation completed'
        };
        mockApiClient.get.mockResolvedValue(completedProgress);

        const result = await aiQuestionnaireService.getGenerationProgress('gen-123456');

        expect(result.status).toBe(GenerationStatus.COMPLETED);
        expect(result.progress_percentage).toBe(100);
      });

      it('should handle progress polling for failed generation', async () => {
        const failedProgress = {
          ...mockGenerationProgress,
          status: GenerationStatus.FAILED,
          error_message: 'AI model timeout'
        };
        mockApiClient.get.mockResolvedValue(failedProgress);

        const result = await aiQuestionnaireService.getGenerationProgress('gen-123456');

        expect(result.status).toBe(GenerationStatus.FAILED);
        expect(result.error_message).toBe('AI model timeout');
      });

      it('should handle not found error for invalid generation ID', async () => {
        const notFoundError = {
          error_code: 'GENERATION_NOT_FOUND',
          detail: 'Generation ID not found'
        };
        mockApiClient.get.mockRejectedValue(notFoundError);

        await expect(aiQuestionnaireService.getGenerationProgress('invalid-id'))
          .rejects.toEqual(notFoundError);
      });
    });

    describe('cancelGeneration', () => {
      it('should successfully cancel ongoing generation', async () => {
        const cancelResponse = { success: true, message: 'Generation cancelled successfully' };
        mockApiClient.post.mockResolvedValue(cancelResponse);

        const result = await aiQuestionnaireService.cancelGeneration('gen-123456');

        expect(mockApiClient.post).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/cancel/gen-123456'
        );
        expect(result).toEqual(cancelResponse);
      });

      it('should handle cancellation of already completed generation', async () => {
        const alreadyCompletedResponse = { 
          success: false, 
          message: 'Generation already completed' 
        };
        mockApiClient.post.mockResolvedValue(alreadyCompletedResponse);

        const result = await aiQuestionnaireService.cancelGeneration('gen-completed');

        expect(result.success).toBe(false);
        expect(result.message).toBe('Generation already completed');
      });
    });
  });

  describe('Template Operations', () => {
    describe('getTemplates', () => {
      it('should fetch templates with default parameters', async () => {
        mockApiClient.get.mockResolvedValue(mockTemplateList);

        const result = await aiQuestionnaireService.getTemplates();

        expect(mockApiClient.get).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/templates/',
          { params: undefined }
        );
        expect(result).toEqual(mockTemplateList);
      });

      it('should fetch templates with search filters', async () => {
        const params = {
          position_type: 'Software Engineer',
          industry: 'Technology',
          skip: 0,
          limit: 10
        };
        mockApiClient.get.mockResolvedValue(mockTemplateList);

        const result = await aiQuestionnaireService.getTemplates(params);

        expect(mockApiClient.get).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/templates/',
          { params }
        );
        expect(result).toEqual(mockTemplateList);
      });
    });

    describe('getTemplate', () => {
      it('should fetch template by ID', async () => {
        mockApiClient.get.mockResolvedValue(mockTemplate);

        const result = await aiQuestionnaireService.getTemplate('1234567890123456791');

        expect(mockApiClient.get).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/templates/1234567890123456791'
        );
        expect(result).toEqual(mockTemplate);
      });

      it('should handle SnowflakeID conversion correctly', async () => {
        mockApiClient.get.mockResolvedValue(mockTemplate);

        // Test with numeric SnowflakeID (should convert to string)
        await aiQuestionnaireService.getTemplate('1234567890123456791');

        expect(mockApiClient.get).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/templates/1234567890123456791'
        );
      });
    });

    describe('createTemplate', () => {
      it('should create new template', async () => {
        const newTemplate = {
          name: 'Frontend Developer Template',
          description: 'Template for frontend developer positions',
          position_types: ['Frontend Developer', 'React Developer'],
          industries: ['Technology'],
          default_settings: {
            question_count: 20,
            difficulty_level: 'medium',
            focus_areas: ['digital_literacy', 'position_skill']
          }
        };
        const createdTemplate = { ...mockTemplate, ...newTemplate };
        mockApiClient.post.mockResolvedValue(createdTemplate);

        const result = await aiQuestionnaireService.createTemplate(newTemplate);

        expect(mockApiClient.post).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/templates/',
          newTemplate
        );
        expect(result).toEqual(createdTemplate);
      });
    });

    describe('updateTemplate', () => {
      it('should update existing template', async () => {
        const updates = {
          name: 'Updated Template Name',
          default_settings: {
            question_count: 30
          }
        };
        const updatedTemplate = { ...mockTemplate, ...updates };
        mockApiClient.put.mockResolvedValue(updatedTemplate);

        const result = await aiQuestionnaireService.updateTemplate('1234567890123456791', updates);

        expect(mockApiClient.put).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/templates/1234567890123456791',
          updates
        );
        expect(result).toEqual(updatedTemplate);
      });
    });
  });

  describe('Questionnaire CRUD Operations', () => {
    describe('getQuestionnaires', () => {
      it('should fetch questionnaires list', async () => {
        const mockList: QuestionnaireListResponse = {
          items: [mockQuestionnaire],
          total: 1,
          skip: 0,
          limit: 20
        };
        mockApiClient.get.mockResolvedValue(mockList);

        const result = await aiQuestionnaireService.getQuestionnaires();

        expect(mockApiClient.get).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/',
          { params: undefined }
        );
        expect(result).toEqual(mockList);
      });

      it('should handle search parameters', async () => {
        const searchParams = {
          search: 'Software Engineer',
          position_type: 'Software Engineer',
          is_ai_generated: true,
          skip: 10,
          limit: 5
        };
        const mockList: QuestionnaireListResponse = {
          items: [mockQuestionnaire],
          total: 1,
          skip: 10,
          limit: 5
        };
        mockApiClient.get.mockResolvedValue(mockList);

        const result = await aiQuestionnaireService.getQuestionnaires(searchParams);

        expect(mockApiClient.get).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/',
          { params: searchParams }
        );
      });
    });

    describe('saveQuestionnaire', () => {
      it('should save generated questionnaire', async () => {
        const questionnaireData = {
          title: 'Software Engineer Assessment',
          description: 'AI-generated assessment',
          position_type: 'Software Engineer',
          industry: 'Technology',
          difficulty_level: 'medium',
          questions: [
            {
              question_text: 'Describe your experience with React',
              question_type: 'text_long',
              category: 'technical',
              difficulty: 'medium',
              estimated_time: 5,
              evaluation_criteria: ['Technical depth'],
              scoring_weights: {
                digital_literacy: 0.2,
                industry_skill: 0.0,
                position_skill: 0.6,
                innovation: 0.1,
                learning_potential: 0.1
              }
            }
          ]
        };

        mockApiClient.post.mockResolvedValue(mockQuestionnaire);

        const result = await aiQuestionnaireService.saveQuestionnaire(questionnaireData);

        expect(mockApiClient.post).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/',
          questionnaireData
        );
        expect(result).toEqual(mockQuestionnaire);
      });
    });

    describe('toggleQuestionnaireStatus', () => {
      it('should activate questionnaire', async () => {
        const toggleResponse = { success: true, message: 'Questionnaire activated' };
        mockApiClient.patch.mockResolvedValue(toggleResponse);

        const result = await aiQuestionnaireService.toggleQuestionnaireStatus('123456789', true);

        expect(mockApiClient.patch).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/123456789/status',
          { is_active: true }
        );
        expect(result).toEqual(toggleResponse);
      });

      it('should deactivate questionnaire', async () => {
        const toggleResponse = { success: true, message: 'Questionnaire deactivated' };
        mockApiClient.patch.mockResolvedValue(toggleResponse);

        const result = await aiQuestionnaireService.toggleQuestionnaireStatus('123456789', false);

        expect(mockApiClient.patch).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/123456789/status',
          { is_active: false }
        );
        expect(result).toEqual(toggleResponse);
      });
    });
  });

  describe('Advanced Operations', () => {
    describe('getGenerationStats', () => {
      it('should fetch generation statistics', async () => {
        const mockStats = {
          total_generated: 150,
          successful_generations: 142,
          average_generation_time: 45,
          most_popular_position_types: [
            { position_type: 'Software Engineer', count: 50 },
            { position_type: 'Data Scientist', count: 30 }
          ],
          most_popular_industries: [
            { industry: 'Technology', count: 80 },
            { industry: 'Finance', count: 25 }
          ],
          quality_score_distribution: [
            { score_range: '0.8-1.0', count: 120 },
            { score_range: '0.6-0.8', count: 22 }
          ]
        };

        mockApiClient.get.mockResolvedValue(mockStats);

        const result = await aiQuestionnaireService.getGenerationStats();

        expect(mockApiClient.get).toHaveBeenCalledWith('/ai/ai-questionnaire/stats');
        expect(result).toEqual(mockStats);
      });
    });

    describe('previewQuestionnaire', () => {
      it('should generate questionnaire preview', async () => {
        const mockPreview = {
          preview_questions: [
            {
              question_text: 'What is your experience with JavaScript?',
              category: 'technical',
              difficulty: 'medium',
              estimated_time: 3
            }
          ],
          estimated_total_duration: 45,
          quality_preview: {
            coverage_score: 0.85,
            difficulty_distribution: { easy: 0.2, medium: 0.6, hard: 0.2 },
            category_distribution: { technical: 0.7, behavioral: 0.3 }
          }
        };

        mockApiClient.post.mockResolvedValue(mockPreview);

        const result = await aiQuestionnaireService.previewQuestionnaire(mockGenerateRequest);

        expect(mockApiClient.post).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/preview',
          mockGenerateRequest
        );
        expect(result).toEqual(mockPreview);
      });
    });

    describe('assessQuestionnaireQuality', () => {
      it('should assess questionnaire quality', async () => {
        const mockAssessment = {
          overall_quality_score: 0.87,
          detailed_assessment: {
            question_clarity: 0.9,
            difficulty_balance: 0.8,
            category_coverage: 0.85,
            duration_accuracy: 0.95
          },
          recommendations: [
            'Consider adding more behavioral questions',
            'Balance difficulty levels better'
          ],
          improvement_suggestions: [
            {
              question_index: 2,
              suggestion: 'Make question more specific',
              impact: 'medium' as const
            }
          ]
        };

        mockApiClient.get.mockResolvedValue(mockAssessment);

        const result = await aiQuestionnaireService.assessQuestionnaireQuality('123456789');

        expect(mockApiClient.get).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/123456789/quality-assessment'
        );
        expect(result).toEqual(mockAssessment);
      });
    });

    describe('regenerateQuestions', () => {
      it('should regenerate specific questions', async () => {
        const regenerateRequest = {
          question_indices: [1, 3, 5],
          new_requirements: {
            difficulty_level: 'hard',
            focus_areas: ['innovation']
          }
        };

        mockApiClient.post.mockResolvedValue(mockGenerateResponse);

        const result = await aiQuestionnaireService.regenerateQuestions('123456789', regenerateRequest);

        expect(mockApiClient.post).toHaveBeenCalledWith(
          '/ai/ai-questionnaire/123456789/regenerate',
          regenerateRequest
        );
        expect(result).toEqual(mockGenerateResponse);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle network errors gracefully', async () => {
      const networkError = {
        error_code: 'NETWORK_ERROR',
        detail: 'Network error occurred. Please try again.',
        isNetworkError: true
      };
      mockApiClient.post.mockRejectedValue(networkError);

      await expect(aiQuestionnaireService.generateQuestionnaire(mockGenerateRequest))
        .rejects.toEqual(networkError);
    });

    it('should handle authentication errors', async () => {
      const authError = {
        error_code: 'AUTH_TOKEN_EXPIRED',
        detail: 'Authentication token has expired'
      };
      mockApiClient.get.mockRejectedValue(authError);

      await expect(aiQuestionnaireService.getGenerationProgress('gen-123'))
        .rejects.toEqual(authError);
    });

    it('should handle server errors with proper error codes', async () => {
      const serverError = {
        error_code: 'INTERNAL_SERVER_ERROR',
        detail: 'An internal server error occurred'
      };
      mockApiClient.post.mockRejectedValue(serverError);

      await expect(aiQuestionnaireService.generateQuestionnaire(mockGenerateRequest))
        .rejects.toEqual(serverError);
    });
  });
});