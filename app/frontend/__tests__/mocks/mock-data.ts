/**
 * Mock data for tests
 */
import { Candidate, CandidateStatus, DataPermission } from '@/types'

export const mockCandidate: Candidate = {
  id: '1',
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '13800138000',
  current_position: 'Senior Developer',
  current_company: 'Tech Corp',
  years_of_experience: 5,
  expected_salary: 25000,
  skills: ['React', 'TypeScript', 'Node.js'],
  status: CandidateStatus.NEW,
  data_permission: DataPermission.PRIVATE,
  source: 'website',
  tags: ['javascript', 'frontend'],
  created_by: 'user-1',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
  shared_with: [],
  resume_url: null,
  notes: 'Strong technical background',
}

export const mockCandidates: Candidate[] = [
  mockCandidate,
  {
    id: '2',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '13900139000',
    current_position: 'Product Manager',
    current_company: 'StartupXYZ',
    years_of_experience: 3,
    expected_salary: 20000,
    skills: ['Product Management', 'Agile', 'Analytics'],
    status: CandidateStatus.SCREENING,
    data_permission: DataPermission.TEAM,
    source: 'referral',
    tags: ['management', 'product'],
    created_by: 'user-2',
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
    shared_with: ['user-3'],
    resume_url: 'https://example.com/resume.pdf',
    notes: 'Great communication skills',
  },
  {
    id: '3',
    name: 'Bob Johnson',
    email: '<EMAIL>',
    phone: '13700137000',
    current_position: 'DevOps Engineer',
    current_company: 'CloudCorp',
    years_of_experience: 7,
    expected_salary: 30000,
    skills: ['AWS', 'Docker', 'Kubernetes', 'Terraform'],
    status: CandidateStatus.INTERVIEW,
    data_permission: DataPermission.PUBLIC,
    source: 'linkedin',
    tags: ['devops', 'cloud'],
    created_by: 'user-1',
    created_at: '2024-01-03T00:00:00Z',
    updated_at: '2024-01-03T00:00:00Z',
    shared_with: [],
    resume_url: 'https://example.com/bob-resume.pdf',
    notes: 'Expert in cloud infrastructure',
  },
]

export const mockCandidateListResponse = {
  items: mockCandidates,
  total: mockCandidates.length,
  skip: 0,
  limit: 20,
}

export const mockCandidateCreate = {
  name: 'New Candidate',
  email: '<EMAIL>',
  phone: '13600136000',
  current_position: 'Software Engineer',
  current_company: 'NewCorp',
  years_of_experience: 2,
  expected_salary: 18000,
  skills: ['JavaScript', 'React'],
  status: CandidateStatus.NEW,
  data_permission: DataPermission.PRIVATE,
  source: 'website',
  tags: ['frontend'],
  notes: 'Promising candidate',
}

export const mockCandidateUpdate = {
  name: 'Updated Candidate',
  current_position: 'Lead Developer',
  years_of_experience: 6,
  status: CandidateStatus.SCREENING,
}

export const mockUser = {
  id: 'user-1',
  email: '<EMAIL>',
  username: 'admin',
  full_name: 'Admin User',
  role: 'admin',
  permissions: ['CANDIDATES_READ', 'CANDIDATES_WRITE', 'CANDIDATES_DELETE', 'CANDIDATES_EXPORT'],
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-01-01T00:00:00Z',
}

export const mockAuthTokens = {
  access_token: 'mock-access-token',
  refresh_token: 'mock-refresh-token',
  token_type: 'bearer',
  expires_in: 3600,
}

export const mockLoginResponse = {
  ...mockAuthTokens,
  user: mockUser,
}

export const mockCandidateStats = {
  total: 3,
  by_status: {
    [CandidateStatus.NEW]: 1,
    [CandidateStatus.SCREENING]: 1,
    [CandidateStatus.INTERVIEW]: 1,
    [CandidateStatus.OFFER]: 0,
    [CandidateStatus.HIRED]: 0,
    [CandidateStatus.REJECTED]: 0,
    [CandidateStatus.WITHDRAWN]: 0,
  },
  by_permission: {
    [DataPermission.PRIVATE]: 1,
    [DataPermission.TEAM]: 1,
    [DataPermission.PUBLIC]: 1,
    [DataPermission.SHARED]: 0,
  },
  recent_count: 2,
}

export const mockResumeUploadResponse = {
  resume_url: 'https://example.com/uploaded-resume.pdf',
  message: 'Resume uploaded successfully',
}

export const mockBulkExportResponse = {
  download_url: 'https://example.com/export-12345.xlsx',
  message: 'Export file generated successfully',
}

export const mockBulkImportResponse = {
  message: 'Import task started',
  task_id: 'import-12345',
}

export const mockDuplicateCheckResponse = {
  exists: false,
  candidate: null,
}

export const mockShareResponse = {
  message: 'Candidate shared successfully',
}