/**
 * Mock assessment data for comprehensive five-dimensional talent evaluation system
 * Based on Sprint 4 requirements for TalentForge Pro
 */

import { 
  AssessmentResponse, 
  DimensionScore, 
  CandidateComparison,
  AssessmentSchedule,
  TeamAssessmentSummary,
  AssessmentTrends,
  IndustryBenchmarks
} from '@/types/recruitment';

// Five dimensions with their weights:
// 数字素养 (Digital Literacy) - 20%
// 行业技能 (Industry Skills) - 25% 
// 岗位技能 (Position Skills) - 30%
// 创新能力 (Innovation) - 15%
// 学习潜力 (Learning Potential) - 10%

// Mock dimension scores for different candidate profiles
export const mockDimensionScores = {
  digitalLiteracy: {
    excellent: {
      dimension: "digital_literacy",
      score: 0.92,
      weight: 0.20,
      confidence: 0.89,
      evidence: [
        "Proficient in advanced data visualization tools (Tableau, Power BI)",
        "Expert-level cloud platform knowledge (AWS, Azure, GCP)",
        "Strong cybersecurity awareness and implementation",
        "Advanced automation and workflow optimization skills",
        "Digital transformation project leadership experience"
      ],
      improvement_suggestions: [
        "Continue staying updated with emerging AI/ML tools",
        "Consider obtaining advanced cloud architecture certifications"
      ],
      industry_benchmark: 0.75,
      percentile_ranking: 92,
      historical_trend: 0.05
    } as DimensionScore,
    
    good: {
      dimension: "digital_literacy",
      score: 0.78,
      weight: 0.20,
      confidence: 0.85,
      evidence: [
        "Solid understanding of modern development tools and platforms",
        "Competent in cloud services and deployment strategies",
        "Good data analysis and reporting capabilities",
        "Effective use of collaboration and productivity tools"
      ],
      improvement_suggestions: [
        "Enhance knowledge of advanced analytics tools",
        "Develop stronger automation scripting skills",
        "Expand understanding of enterprise security frameworks"
      ],
      industry_benchmark: 0.75,
      percentile_ranking: 78,
      historical_trend: 0.02
    } as DimensionScore,

    average: {
      dimension: "digital_literacy",
      score: 0.65,
      weight: 0.20,
      confidence: 0.82,
      evidence: [
        "Basic proficiency in common software and tools",
        "Understanding of fundamental digital concepts",
        "Can adapt to new technologies with guidance"
      ],
      improvement_suggestions: [
        "Develop stronger technical troubleshooting skills",
        "Gain experience with cloud-based solutions",
        "Improve data literacy and analytical thinking",
        "Enhance digital communication and presentation skills"
      ],
      industry_benchmark: 0.75,
      percentile_ranking: 45,
      historical_trend: -0.02
    } as DimensionScore
  },

  industrySkills: {
    excellent: {
      dimension: "industry_skills",
      score: 0.88,
      weight: 0.25,
      confidence: 0.91,
      evidence: [
        "Deep expertise in industry-specific methodologies and frameworks",
        "Proven track record in complex industry projects",
        "Recognition as subject matter expert by peers",
        "Contribution to industry standards and best practices",
        "Mentoring junior professionals in specialized skills"
      ],
      improvement_suggestions: [
        "Explore adjacent industry applications",
        "Consider industry thought leadership opportunities"
      ],
      industry_benchmark: 0.72,
      percentile_ranking: 88,
      historical_trend: 0.03
    } as DimensionScore,

    good: {
      dimension: "industry_skills",
      score: 0.74,
      weight: 0.25,
      confidence: 0.87,
      evidence: [
        "Strong foundation in core industry practices",
        "Successful completion of multiple relevant projects",
        "Understanding of industry regulations and compliance",
        "Effective collaboration with industry stakeholders"
      ],
      improvement_suggestions: [
        "Deepen expertise in emerging industry trends",
        "Gain experience in cross-functional industry initiatives",
        "Develop stronger industry network and relationships"
      ],
      industry_benchmark: 0.72,
      percentile_ranking: 74,
      historical_trend: 0.01
    } as DimensionScore,

    average: {
      dimension: "industry_skills",
      score: 0.58,
      weight: 0.25,
      confidence: 0.79,
      evidence: [
        "Basic understanding of industry fundamentals",
        "Limited hands-on experience in specialized areas",
        "Awareness of key industry challenges and trends"
      ],
      improvement_suggestions: [
        "Gain more hands-on experience through projects",
        "Pursue industry-specific training and certifications",
        "Build relationships with industry professionals",
        "Stay current with industry publications and research"
      ],
      industry_benchmark: 0.72,
      percentile_ranking: 38,
      historical_trend: -0.01
    } as DimensionScore
  },

  positionSkills: {
    excellent: {
      dimension: "position_skills",
      score: 0.94,
      weight: 0.30,
      confidence: 0.93,
      evidence: [
        "Exceptional proficiency in all core position requirements",
        "Demonstrates advanced techniques and innovative approaches",
        "Consistently delivers high-quality results under pressure",
        "Trains and mentors others in position-specific skills",
        "Recognized for outstanding performance and expertise"
      ],
      improvement_suggestions: [
        "Consider leadership roles within the domain",
        "Explore cutting-edge tools and methodologies"
      ],
      industry_benchmark: 0.68,
      percentile_ranking: 94,
      historical_trend: 0.04
    } as DimensionScore,

    good: {
      dimension: "position_skills",
      score: 0.81,
      weight: 0.30,
      confidence: 0.88,
      evidence: [
        "Strong competency in most position requirements",
        "Proven ability to handle complex tasks independently",
        "Good problem-solving skills in domain-specific challenges",
        "Effective collaboration within functional teams"
      ],
      improvement_suggestions: [
        "Master advanced features of key tools and platforms",
        "Develop expertise in specialized areas of the role",
        "Gain exposure to strategic aspects of the position"
      ],
      industry_benchmark: 0.68,
      percentile_ranking: 81,
      historical_trend: 0.02
    } as DimensionScore,

    average: {
      dimension: "position_skills",
      score: 0.62,
      weight: 0.30,
      confidence: 0.84,
      evidence: [
        "Meets basic requirements for the position",
        "Can complete routine tasks with minimal supervision",
        "Shows willingness to learn and improve"
      ],
      improvement_suggestions: [
        "Focus on mastering core competencies first",
        "Seek mentoring and guidance from senior colleagues",
        "Practice with real-world scenarios and case studies",
        "Invest time in relevant skill development programs"
      ],
      industry_benchmark: 0.68,
      percentile_ranking: 42,
      historical_trend: 0.01
    } as DimensionScore
  },

  innovation: {
    excellent: {
      dimension: "innovation",
      score: 0.89,
      weight: 0.15,
      confidence: 0.86,
      evidence: [
        "Consistently generates creative solutions to complex problems",
        "Successfully led innovation initiatives with measurable impact",
        "Demonstrates forward-thinking and strategic vision",
        "Encourages innovative thinking in team environments",
        "Has patents, publications, or recognized innovative contributions"
      ],
      improvement_suggestions: [
        "Expand cross-industry knowledge for broader innovation potential",
        "Consider formal innovation management training"
      ],
      industry_benchmark: 0.63,
      percentile_ranking: 89,
      historical_trend: 0.06
    } as DimensionScore,

    good: {
      dimension: "innovation",
      score: 0.71,
      weight: 0.15,
      confidence: 0.83,
      evidence: [
        "Shows creativity in problem-solving approaches",
        "Suggests process improvements and optimizations",
        "Adapts well to new situations and challenges",
        "Contributes ideas in brainstorming and planning sessions"
      ],
      improvement_suggestions: [
        "Develop systematic innovation methodologies",
        "Seek opportunities to lead innovation projects",
        "Build broader knowledge base across disciplines"
      ],
      industry_benchmark: 0.63,
      percentile_ranking: 71,
      historical_trend: 0.03
    } as DimensionScore,

    average: {
      dimension: "innovation",
      score: 0.54,
      weight: 0.15,
      confidence: 0.78,
      evidence: [
        "Occasionally provides creative input",
        "Can implement innovative solutions designed by others",
        "Shows some flexibility in thinking and approach"
      ],
      improvement_suggestions: [
        "Practice creative thinking techniques and methodologies",
        "Expose yourself to diverse perspectives and industries",
        "Take on projects that require creative problem-solving",
        "Collaborate with innovative team members to learn approaches"
      ],
      industry_benchmark: 0.63,
      percentile_ranking: 35,
      historical_trend: 0.01
    } as DimensionScore
  },

  learningPotential: {
    excellent: {
      dimension: "learning_potential",
      score: 0.91,
      weight: 0.10,
      confidence: 0.88,
      evidence: [
        "Rapid mastery of new concepts and technologies",
        "Actively seeks out learning opportunities and challenges",
        "Demonstrates strong meta-learning and self-reflection skills",
        "Effectively transfers knowledge across different domains",
        "Consistently stays ahead of industry trends and developments"
      ],
      improvement_suggestions: [
        "Consider formal teaching or mentoring roles",
        "Explore advanced learning methodologies and techniques"
      ],
      industry_benchmark: 0.69,
      percentile_ranking: 91,
      historical_trend: 0.04
    } as DimensionScore,

    good: {
      dimension: "learning_potential",
      score: 0.77,
      weight: 0.10,
      confidence: 0.85,
      evidence: [
        "Good ability to acquire new skills and knowledge",
        "Responds well to feedback and coaching",
        "Shows curiosity and interest in professional development",
        "Can adapt learning style to different situations"
      ],
      improvement_suggestions: [
        "Develop more systematic approach to skill acquisition",
        "Seek diverse learning experiences and challenges",
        "Practice self-directed learning and goal setting"
      ],
      industry_benchmark: 0.69,
      percentile_ranking: 77,
      historical_trend: 0.02
    } as DimensionScore,

    average: {
      dimension: "learning_potential",
      score: 0.61,
      weight: 0.10,
      confidence: 0.81,
      evidence: [
        "Can learn new skills with proper support and guidance",
        "Shows some motivation for professional growth",
        "Follows established learning paths and curricula"
      ],
      improvement_suggestions: [
        "Develop stronger self-learning habits and discipline",
        "Seek feedback more actively and apply it consistently",
        "Set specific learning goals and track progress",
        "Explore different learning methods to find what works best"
      ],
      industry_benchmark: 0.69,
      percentile_ranking: 45,
      historical_trend: 0.00
    } as DimensionScore
  }
};

// Mock industry benchmarks
export const mockIndustryBenchmarks: IndustryBenchmarks = {
  digital_literacy: {
    industry_average: 0.75,
    top_10_percent: 0.92,
    median: 0.74,
    bottom_10_percent: 0.45,
    sample_size: 15420
  },
  industry_skills: {
    industry_average: 0.72,
    top_10_percent: 0.89,
    median: 0.71,
    bottom_10_percent: 0.42,
    sample_size: 18650
  },
  position_skills: {
    industry_average: 0.68,
    top_10_percent: 0.94,
    median: 0.67,
    bottom_10_percent: 0.38,
    sample_size: 12350
  },
  innovation: {
    industry_average: 0.63,
    top_10_percent: 0.88,
    median: 0.61,
    bottom_10_percent: 0.32,
    sample_size: 9850
  },
  learning_potential: {
    industry_average: 0.69,
    top_10_percent: 0.91,
    median: 0.68,
    bottom_10_percent: 0.41,
    sample_size: 11200
  }
};

// Generate a DCI score based on weighted dimensions
const calculateDCIScore = (dimensions: {
  digital_literacy: DimensionScore;
  industry_skills: DimensionScore;
  position_skills: DimensionScore;
  innovation: DimensionScore;
  learning_potential: DimensionScore;
}): number => {
  return (
    dimensions.digital_literacy.score * dimensions.digital_literacy.weight +
    dimensions.industry_skills.score * dimensions.industry_skills.weight +
    dimensions.position_skills.score * dimensions.position_skills.weight +
    dimensions.innovation.score * dimensions.innovation.weight +
    dimensions.learning_potential.score * dimensions.learning_potential.weight
  ) * 100;
};

// Generate grade based on DCI score
const getDCIGrade = (score: number): 'S' | 'A' | 'B' | 'C' | 'D' => {
  if (score >= 90) return 'S';
  if (score >= 80) return 'A';
  if (score >= 70) return 'B';
  if (score >= 60) return 'C';
  return 'D';
};

// Generate percentile based on score
const getPercentile = (score: number): number => {
  if (score >= 90) return Math.floor(Math.random() * 10) + 90;
  if (score >= 80) return Math.floor(Math.random() * 20) + 70;
  if (score >= 70) return Math.floor(Math.random() * 20) + 50;
  if (score >= 60) return Math.floor(Math.random() * 20) + 30;
  return Math.floor(Math.random() * 30) + 10;
};

// Mock assessment profiles for different candidate types
export const mockAssessmentProfiles = {
  seniorDeveloper: {
    digital_literacy: mockDimensionScores.digitalLiteracy.excellent,
    industry_skills: mockDimensionScores.industrySkills.good,
    position_skills: mockDimensionScores.positionSkills.excellent,
    innovation: mockDimensionScores.innovation.good,
    learning_potential: mockDimensionScores.learningPotential.excellent
  },
  
  productManager: {
    digital_literacy: mockDimensionScores.digitalLiteracy.good,
    industry_skills: mockDimensionScores.industrySkills.excellent,
    position_skills: mockDimensionScores.positionSkills.good,
    innovation: mockDimensionScores.innovation.excellent,
    learning_potential: mockDimensionScores.learningPotential.good
  },
  
  devopsEngineer: {
    digital_literacy: mockDimensionScores.digitalLiteracy.excellent,
    industry_skills: mockDimensionScores.industrySkills.excellent,
    position_skills: mockDimensionScores.positionSkills.good,
    innovation: mockDimensionScores.innovation.good,
    learning_potential: mockDimensionScores.learningPotential.good
  },
  
  juniorDeveloper: {
    digital_literacy: mockDimensionScores.digitalLiteracy.good,
    industry_skills: mockDimensionScores.industrySkills.average,
    position_skills: mockDimensionScores.positionSkills.average,
    innovation: mockDimensionScores.innovation.average,
    learning_potential: mockDimensionScores.learningPotential.excellent
  },

  dataScientist: {
    digital_literacy: mockDimensionScores.digitalLiteracy.excellent,
    industry_skills: mockDimensionScores.industrySkills.excellent,
    position_skills: mockDimensionScores.positionSkills.excellent,
    innovation: mockDimensionScores.innovation.excellent,
    learning_potential: mockDimensionScores.learningPotential.good
  }
};

// Generate full assessment response
export const generateMockAssessment = (
  candidateId: string,
  profile: keyof typeof mockAssessmentProfiles = 'seniorDeveloper'
): AssessmentResponse => {
  const dimensions = mockAssessmentProfiles[profile];
  const dciScore = calculateDCIScore(dimensions);
  const grade = getDCIGrade(dciScore);
  const percentile = getPercentile(dciScore);

  const strengthsMap = {
    seniorDeveloper: [
      "Exceptional technical expertise with cutting-edge technologies",
      "Strong problem-solving abilities in complex scenarios", 
      "Excellent code quality and architecture design skills",
      "Proven leadership in technical decision-making",
      "Outstanding ability to learn and adapt to new technologies"
    ],
    productManager: [
      "Deep understanding of market dynamics and user needs",
      "Excellent stakeholder management and communication skills",
      "Strong analytical thinking and data-driven decision making",
      "Proven track record in successful product launches",
      "Outstanding ability to balance technical and business priorities"
    ],
    devopsEngineer: [
      "Expert-level cloud infrastructure and automation skills",
      "Strong understanding of security and compliance requirements",
      "Excellent troubleshooting and incident response capabilities",
      "Proven experience with large-scale system optimization",
      "Outstanding collaboration with development teams"
    ],
    juniorDeveloper: [
      "Strong foundational programming skills and best practices",
      "Excellent learning attitude and growth mindset",
      "Good understanding of modern development tools and workflows",
      "Effective collaboration and team communication skills",
      "Demonstrated potential for rapid skill development"
    ],
    dataScientist: [
      "Expert-level statistical analysis and machine learning skills",
      "Strong business acumen and problem-solving approach",
      "Excellent data visualization and storytelling abilities",
      "Proven experience with advanced analytics tools and platforms",
      "Outstanding ability to translate complex data into insights"
    ]
  };

  const improvementMap = {
    seniorDeveloper: [
      "Expand knowledge in emerging AI/ML technologies",
      "Develop stronger project management and planning skills",
      "Enhance public speaking and presentation abilities"
    ],
    productManager: [
      "Deepen technical understanding of implementation challenges",
      "Strengthen quantitative analysis and modeling skills",
      "Expand knowledge of emerging market trends and technologies"
    ],
    devopsEngineer: [
      "Develop stronger programming and scripting skills",
      "Enhance understanding of business requirements and priorities",
      "Expand knowledge of new cloud services and platforms"
    ],
    juniorDeveloper: [
      "Gain more experience with complex system architecture",
      "Develop stronger debugging and troubleshooting skills",
      "Expand knowledge of industry best practices and patterns",
      "Build more confidence in technical decision-making"
    ],
    dataScientist: [
      "Strengthen software engineering and production deployment skills",
      "Develop better understanding of business strategy and operations",
      "Enhance communication with non-technical stakeholders"
    ]
  };

  const recommendationsMap = {
    seniorDeveloper: [
      "Consider pursuing technical leadership or architecture roles",
      "Explore opportunities in emerging technology areas like AI/ML",
      "Develop mentoring skills to guide junior team members",
      "Engage in industry conferences and thought leadership activities"
    ],
    productManager: [
      "Pursue advanced product management certifications",
      "Develop stronger technical background through engineering collaboration",
      "Explore opportunities in strategic product planning roles",
      "Build expertise in emerging market segments"
    ],
    devopsEngineer: [
      "Consider cloud architecture certification paths",
      "Explore opportunities in platform engineering and SRE roles",
      "Develop expertise in emerging containerization and orchestration tools",
      "Build stronger programming skills for infrastructure automation"
    ],
    juniorDeveloper: [
      "Focus on mastering fundamental programming concepts first",
      "Seek mentorship from senior developers",
      "Practice with increasingly complex projects and challenges",
      "Build strong foundation in testing and code quality practices"
    ],
    dataScientist: [
      "Explore opportunities in AI/ML engineering roles",
      "Develop stronger software development and MLOps skills",
      "Consider specialization in specific industry verticals",
      "Build expertise in real-time analytics and edge computing"
    ]
  };

  return {
    success: true,
    candidate_id: candidateId,
    assessment_id: `assess_${Date.now()}_${candidateId}`,
    dimensions,
    dci_score: dciScore,
    percentile,
    grade,
    strengths: strengthsMap[profile],
    improvement_areas: improvementMap[profile],
    recommendations: recommendationsMap[profile],
    radar_chart_data: {
      digital_literacy: dimensions.digital_literacy.score,
      industry_skills: dimensions.industry_skills.score,
      position_skills: dimensions.position_skills.score,
      innovation: dimensions.innovation.score,
      learning_potential: dimensions.learning_potential.score
    },
    generated_at: new Date().toISOString(),
    industry_benchmarks: mockIndustryBenchmarks,
    validation_confidence: 0.87,
    assessment_duration_hours: 2.5
  };
};

// Mock assessment data for different candidates
export const mockAssessments: Record<string, AssessmentResponse> = {
  '1': generateMockAssessment('1', 'seniorDeveloper'),
  '2': generateMockAssessment('2', 'productManager'),  
  '3': generateMockAssessment('3', 'devopsEngineer'),
  '4': generateMockAssessment('4', 'juniorDeveloper'),
  '5': generateMockAssessment('5', 'dataScientist')
};

// Mock candidate comparison data
export const mockCandidateComparisons: CandidateComparison[] = [
  {
    candidate_id: '1',
    candidate_name: 'John Doe',
    basic_info: {
      education: 'M.S. Computer Science',
      experience_years: 5,
      current_salary: 120000,
      expected_salary: 140000
    },
    scores: {
      dci_score: mockAssessments['1'].dci_score,
      jfs_score: 85.2,
      overall_score: 87.6
    },
    dimensions: mockAssessments['1'].radar_chart_data,
    strengths: mockAssessments['1'].strengths.slice(0, 3),
    weaknesses: mockAssessments['1'].improvement_areas.slice(0, 2),
    risk_factors: ['Limited experience in team leadership', 'May require higher compensation']
  },
  {
    candidate_id: '2',
    candidate_name: 'Jane Smith',
    basic_info: {
      education: 'MBA + B.S. Engineering',
      experience_years: 3,
      current_salary: 95000,
      expected_salary: 115000
    },
    scores: {
      dci_score: mockAssessments['2'].dci_score,
      jfs_score: 82.1,
      overall_score: 84.3
    },
    dimensions: mockAssessments['2'].radar_chart_data,
    strengths: mockAssessments['2'].strengths.slice(0, 3),
    weaknesses: mockAssessments['2'].improvement_areas.slice(0, 2),
    risk_factors: ['Limited technical depth', 'Relatively new to the industry']
  }
];

// Mock assessment schedules
export const mockAssessmentSchedules: AssessmentSchedule[] = [
  {
    id: 'sched_1',
    candidate_id: '1',
    candidate_name: 'John Doe',
    scheduled_at: '2024-02-15T14:00:00Z',
    assessment_type: 'comprehensive',
    status: 'scheduled',
    estimated_duration_hours: 3,
    assessor_id: 'assessor_1',
    assessor_name: 'Dr. Sarah Wilson',
    notes: 'Focus on technical architecture and leadership potential',
    position_id: 'pos_1',
    position_title: 'Senior Software Architect'
  },
  {
    id: 'sched_2', 
    candidate_id: '2',
    candidate_name: 'Jane Smith',
    scheduled_at: '2024-02-16T10:00:00Z',
    assessment_type: 'comprehensive',
    status: 'in_progress',
    estimated_duration_hours: 2.5,
    assessor_id: 'assessor_2',
    assessor_name: 'Mark Johnson',
    notes: 'Evaluate product sense and stakeholder management skills',
    position_id: 'pos_2',
    position_title: 'Senior Product Manager'
  },
  {
    id: 'sched_3',
    candidate_id: '3',
    candidate_name: 'Bob Johnson',
    scheduled_at: '2024-02-14T09:00:00Z',
    assessment_type: 'comprehensive',
    status: 'completed',
    estimated_duration_hours: 3.5,
    assessor_id: 'assessor_1',
    assessor_name: 'Dr. Sarah Wilson',
    notes: 'Comprehensive technical evaluation completed',
    position_id: 'pos_3',
    position_title: 'DevOps Lead'
  }
];

// Mock assessment trends data
export const mockAssessmentTrends: AssessmentTrends = {
  period: 'monthly',
  data_points: [
    {
      date: '2024-01-01',
      avg_dci_score: 76.2,
      assessment_count: 45,
      grade_distribution: { 'S': 2, 'A': 12, 'B': 18, 'C': 10, 'D': 3 }
    },
    {
      date: '2024-02-01',
      avg_dci_score: 78.1,
      assessment_count: 52,
      grade_distribution: { 'S': 3, 'A': 15, 'B': 22, 'C': 9, 'D': 3 }
    },
    {
      date: '2024-03-01',
      avg_dci_score: 79.5,
      assessment_count: 48,
      grade_distribution: { 'S': 4, 'A': 16, 'B': 19, 'C': 7, 'D': 2 }
    }
  ],
  trends: {
    dci_trend: 'improving',
    quality_trend: 'improving', 
    volume_trend: 'stable'
  }
};

// Mock team assessment summary
export const mockTeamAssessmentSummary: TeamAssessmentSummary = {
  team_name: 'Engineering Team Alpha',
  total_members: 8,
  avg_dci_score: 82.3,
  team_strengths: [
    'Exceptional technical expertise across the team',
    'Strong collaborative culture and knowledge sharing',
    'High learning velocity and adaptation to new technologies',
    'Excellent problem-solving and innovation mindset'
  ],
  team_gaps: [
    'Limited experience in enterprise-scale architecture',
    'Need stronger project management and planning skills',
    'Could benefit from improved stakeholder communication'
  ],
  dimension_averages: {
    digital_literacy: 0.85,
    industry_skills: 0.78,
    position_skills: 0.82,
    innovation: 0.76,
    learning_potential: 0.88
  },
  recommended_training: [
    'Enterprise Architecture Certification Program',
    'Advanced Project Management Workshop',
    'Technical Leadership Development',
    'Stakeholder Communication and Presentation Skills'
  ],
  risk_areas: [
    'Potential skill gaps in legacy system maintenance',
    'Limited backup expertise in specialized areas',
    'Team dependency on key technical leads'
  ]
};

// Export default mock data for quick testing
export const defaultMockAssessment = mockAssessments['1'];
export { mockAssessments as assessmentData };