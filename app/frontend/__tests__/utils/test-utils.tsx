/**
 * Test utilities for React Testing Library
 */
import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { Provider } from 'react-redux'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { configureStore, EnhancedStore } from '@reduxjs/toolkit'
import authSlice from '@/store/authSlice'
import candidateSlice from '@/store/candidateSlice'
import { I18nProvider } from '@/app/i18n/client'

// Initial state for tests
const initialAuthState = {
  user: {
    id: 'user-1',
    email: '<EMAIL>',
    username: 'admin',
    full_name: 'Admin User',
    role: 'admin',
    permissions: ['CANDIDATES_READ', 'CANDIDATES_WRITE', 'CANDIDATES_DELETE', 'CANDIDATES_EXPORT'],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  tokens: {
    access_token: 'mock-access-token',
    refresh_token: 'mock-refresh-token',
    token_type: 'bearer',
    expires_in: 3600,
  },
  isAuthenticated: true,
  loading: false,
  error: null,
}

const initialCandidateState = {
  candidates: [],
  currentCandidate: null,
  pagination: {
    page: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  },
  filters: {
    search: '',
    status: undefined,
    skills: [],
    min_experience: undefined,
    max_experience: undefined,
    min_salary: undefined,
    max_salary: undefined,
    data_permission: undefined,
    source: undefined,
    tags: [],
    created_after: undefined,
    created_before: undefined,
    order_by: 'created_at',
    order_desc: true,
  },
  bulkOperations: {
    selectedIds: [],
    operation: null,
    progress: 0,
  },
  loading: {
    list: false,
    detail: false,
    create: false,
    update: false,
    delete: false,
    uploadResume: false,
    share: false,
    bulkImport: false,
    bulkExport: false,
  },
  errors: {
    list: null,
    detail: null,
    create: null,
    update: null,
    delete: null,
    uploadResume: null,
    share: null,
    bulkImport: null,
    bulkExport: null,
  },
  lastFetch: null,
  cacheValid: false,
}

// Deep merge helper
function deepMerge(target: any, source: any): any {
  const output = { ...target }
  
  if (isObject(target) && isObject(source)) {
    Object.keys(source).forEach(key => {
      if (isObject(source[key])) {
        if (!(key in target)) {
          output[key] = source[key]
        } else {
          output[key] = deepMerge(target[key], source[key])
        }
      } else {
        // Arrays and primitives should be replaced, not merged
        output[key] = source[key]
      }
    })
  }
  
  return output
}

function isObject(item: any): boolean {
  return item && typeof item === 'object' && !Array.isArray(item)
}

// Create a test store
export function createTestStore(preloadedState?: any): EnhancedStore {
  const mergedState = {
    auth: deepMerge(initialAuthState, preloadedState?.auth || {}),
    candidates: deepMerge(initialCandidateState, preloadedState?.candidates || {}),
  }
  
  return configureStore({
    reducer: {
      auth: authSlice,
      candidates: candidateSlice,
    },
    preloadedState: mergedState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: {
          ignoredActions: ['persist/PERSIST'],
        },
      }),
  })
}

// Custom render function
interface ExtendedRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  preloadedState?: any
  store?: EnhancedStore
}

export function renderWithProviders(
  ui: ReactElement,
  {
    preloadedState = {},
    store = createTestStore(preloadedState),
    ...renderOptions
  }: ExtendedRenderOptions = {}
) {
  // Create a new QueryClient for each test
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
      mutations: {
        retry: false,
      },
    },
  })

  function Wrapper({ children }: { children: React.ReactNode }) {
    return (
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <I18nProvider>
            {children}
          </I18nProvider>
        </QueryClientProvider>
      </Provider>
    )
  }

  // Return an object with the store and all of RTL's query functions
  return { store, ...render(ui, { wrapper: Wrapper, ...renderOptions }) }
}

// Mock implementations for common scenarios
export const mockPermissions = {
  admin: ['CANDIDATES_READ', 'CANDIDATES_WRITE', 'CANDIDATES_DELETE', 'CANDIDATES_EXPORT'],
  hr: ['CANDIDATES_READ', 'CANDIDATES_WRITE'],
  recruiter: ['CANDIDATES_READ'],
  viewer: [],
}

// Create test user with specific permissions
export function createTestUser(role: keyof typeof mockPermissions = 'admin') {
  return {
    id: `user-${role}`,
    email: `${role}@talentforge.pro`,
    username: role,
    full_name: `${role.charAt(0).toUpperCase() + role.slice(1)} User`,
    role,
    permissions: mockPermissions[role],
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  }
}

// Create auth state with specific user
export function createAuthState(role: keyof typeof mockPermissions = 'admin') {
  return {
    user: createTestUser(role),
    tokens: {
      access_token: 'mock-access-token',
      refresh_token: 'mock-refresh-token',
      token_type: 'bearer',
      expires_in: 3600,
    },
    isAuthenticated: true,
    loading: false,
    error: null,
  }
}

// Wait for loading to complete
export async function waitForLoadingToFinish() {
  await new Promise(resolve => setTimeout(resolve, 0))
}

// Mock file for testing file uploads
export function createMockFile(
  name: string = 'test-resume.pdf',
  type: string = 'application/pdf',
  size: number = 1024
): File {
  const file = new File(['test content'], name, { type })
  Object.defineProperty(file, 'size', { value: size })
  return file
}

// Helper to create FormData with file
export function createFormDataWithFile(data: any, file: File): FormData {
  const formData = new FormData()
  formData.append('data', JSON.stringify(data))
  formData.append('resume', file)
  return formData
}

// Helper to simulate user interactions
export const userInteractions = {
  fillForm: async (user: any, fields: Record<string, string>) => {
    for (const [field, value] of Object.entries(fields)) {
      const input = document.querySelector(`[name="${field}"]`) as HTMLInputElement
      if (input) {
        await user.clear(input)
        await user.type(input, value)
      }
    }
  },
  selectOption: async (user: any, selectName: string, optionValue: string) => {
    const select = document.querySelector(`[name="${selectName}"]`)
    if (select) {
      await user.click(select)
      const option = document.querySelector(`[value="${optionValue}"]`)
      if (option) {
        await user.click(option)
      }
    }
  },
}

// re-export everything
export * from '@testing-library/react'