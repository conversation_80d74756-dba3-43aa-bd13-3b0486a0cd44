'use client';

import React, { useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import {
  ArrowLeft,
  Bot,
  Save,
  Eye,
  Download,
  Share2,
  MoreHorizontal,
  AlertCircle,
  CheckCircle,
  Sparkles
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useTranslations } from '@/app/i18n/client';
import { cn } from '@/lib/utils';

import { AIQuestionnaireForm } from '@/components/ai-questionnaire/AIQuestionnaireForm';
import { GenerationProgress } from '@/components/ai-questionnaire/GenerationProgress';
import { TemplateSelector } from '@/components/ai-questionnaire/TemplateSelector';

import {
  QuestionnaireGenerateResponse,
  GenerationProgress as IGenerationProgress,
  GenerationStatus,
  QuestionnaireTemplate,
  Questionnaire,
  GeneratedQuestion
} from '@/types/questionnaire';
import aiQuestionnaireService from '@/services/aiQuestionnaireService';

const pageVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
};

const contentVariants = {
  initial: { opacity: 0, x: 20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: -20 }
};

export default function AIQuestionnaireGeneratePage() {
  const t = useTranslations();
  const router = useRouter();

  const [currentTab, setCurrentTab] = useState<'form' | 'templates' | 'progress' | 'result'>('form');
  const [generationResponse, setGenerationResponse] = useState<QuestionnaireGenerateResponse | null>(null);
  const [currentProgress, setCurrentProgress] = useState<IGenerationProgress | null>(null);
  const [savedQuestionnaire, setSavedQuestionnaire] = useState<Questionnaire | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<QuestionnaireTemplate | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  // Handle generation start
  const handleGenerate = useCallback((response: QuestionnaireGenerateResponse) => {
    setGenerationResponse(response);
    setCurrentTab('progress');
    setError(null);
  }, []);

  // Handle progress updates
  const handleProgress = useCallback((progress: IGenerationProgress) => {
    setCurrentProgress(progress);
    
    if (progress.status === GenerationStatus.COMPLETED) {
      setCurrentTab('result');
      toast.success(t('aiQuestionnaire.messages.generationCompleted'));
    } else if (progress.status === GenerationStatus.FAILED) {
      toast.error(t('aiQuestionnaire.messages.generationFailed'));
    }
  }, [t]);

  // Handle errors
  const handleError = useCallback((errorMessage: string) => {
    setError(errorMessage);
    toast.error(errorMessage);
  }, []);

  // Handle template selection
  const handleTemplateSelect = useCallback((template: QuestionnaireTemplate) => {
    setSelectedTemplate(template);
  }, []);

  // Handle create from template
  const handleCreateFromTemplate = useCallback((template: QuestionnaireTemplate) => {
    setSelectedTemplate(template);
    setCurrentTab('form');
    toast.success(t('aiQuestionnaire.messages.templateSelected', { name: template.name }));
  }, [t]);

  // Cancel generation
  const handleCancelGeneration = useCallback(async () => {
    if (currentProgress && currentProgress.generation_id) {
      try {
        await aiQuestionnaireService.cancelGeneration(currentProgress.generation_id);
        toast.success(t('aiQuestionnaire.messages.generationCancelled'));
        setCurrentTab('form');
        setCurrentProgress(null);
        setGenerationResponse(null);
      } catch (error) {
        toast.error(t('aiQuestionnaire.messages.cancelFailed'));
      }
    }
  }, [currentProgress, t]);

  // Retry generation
  const handleRetryGeneration = useCallback(() => {
    setCurrentTab('form');
    setCurrentProgress(null);
    setGenerationResponse(null);
    setError(null);
  }, []);

  // Save questionnaire
  const handleSaveQuestionnaire = useCallback(async (title?: string, description?: string) => {
    if (!generationResponse) return;

    try {
      setIsSaving(true);

      const questionnaireData = {
        title: title || generationResponse.title,
        description: description || generationResponse.description,
        position_type: selectedTemplate?.position_types[0] || 'General',
        industry: selectedTemplate?.industries[0] || 'General',
        difficulty_level: 'medium',
        questions: generationResponse.questions.map((q: GeneratedQuestion, index: number) => ({
          question_text: q.question_text,
          question_type: q.question_type,
          category: q.category,
          difficulty: q.difficulty,
          estimated_time: q.estimated_time,
          options: q.options,
          evaluation_criteria: q.evaluation_criteria,
          scoring_weights: {
            digital_literacy: 0.2,
            industry_skill: 0.25,
            position_skill: 0.3,
            innovation: 0.15,
            learning_potential: 0.1
          }
        })),
        generation_metadata: generationResponse.generation_metadata
      };

      const saved = await aiQuestionnaireService.saveQuestionnaire(questionnaireData);
      setSavedQuestionnaire(saved);
      setShowSaveDialog(false);
      toast.success(t('aiQuestionnaire.messages.questionnaireSaved'));
    } catch (error: any) {
      toast.error(error?.detail || t('aiQuestionnaire.messages.saveFailed'));
    } finally {
      setIsSaving(false);
    }
  }, [generationResponse, selectedTemplate, t]);

  // Preview questionnaire
  const handlePreview = useCallback(() => {
    if (savedQuestionnaire) {
      // Navigate to questionnaire preview page
      router.push(`/questionnaires/${savedQuestionnaire.id}/preview`);
    }
  }, [savedQuestionnaire, router]);

  // Share questionnaire
  const handleShare = useCallback(async () => {
    if (savedQuestionnaire) {
      try {
        const shareUrl = `${window.location.origin}/questionnaires/${savedQuestionnaire.id}`;
        await navigator.clipboard.writeText(shareUrl);
        toast.success(t('aiQuestionnaire.messages.linkCopied'));
      } catch (error) {
        toast.error(t('aiQuestionnaire.messages.copyFailed'));
      }
    }
  }, [savedQuestionnaire, t]);

  // Export questionnaire
  const handleExport = useCallback(() => {
    if (generationResponse) {
      const dataStr = JSON.stringify(generationResponse, null, 2);
      const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
      
      const exportFileDefaultName = `questionnaire_${generationResponse.questionnaire_id}.json`;
      
      const linkElement = document.createElement('a');
      linkElement.setAttribute('href', dataUri);
      linkElement.setAttribute('download', exportFileDefaultName);
      linkElement.click();
    }
  }, [generationResponse]);

  return (
    <motion.div
      variants={pageVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className="container max-w-7xl mx-auto p-6 space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            {t('common.back')}
          </Button>

          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <Bot className="w-6 h-6 text-blue-600" />
              {t('aiQuestionnaire.generateTitle')}
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {t('aiQuestionnaire.generateSubtitle')}
            </p>
          </div>
        </div>

        {/* Actions */}
        {generationResponse && (
          <div className="flex items-center gap-2">
            {savedQuestionnaire && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePreview}
                >
                  <Eye className="w-4 h-4 mr-2" />
                  {t('common.preview')}
                </Button>

                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" size="sm">
                      <MoreHorizontal className="w-4 h-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={handleShare}>
                      <Share2 className="w-4 h-4 mr-2" />
                      {t('common.share')}
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleExport}>
                      <Download className="w-4 h-4 mr-2" />
                      {t('common.export')}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </>
            )}

            {!savedQuestionnaire && currentTab === 'result' && (
              <Button
                onClick={() => setShowSaveDialog(true)}
                className="min-w-[120px]"
              >
                <Save className="w-4 h-4 mr-2" />
                {t('common.save')}
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="w-4 h-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
        
        {/* Primary Content */}
        <div className="xl:col-span-3">
          <Tabs value={currentTab} onValueChange={(value) => setCurrentTab(value as any)}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="form" className="flex items-center gap-2">
                <Bot className="w-4 h-4" />
                {t('aiQuestionnaire.tabs.configure')}
              </TabsTrigger>
              <TabsTrigger value="templates" className="flex items-center gap-2">
                <Sparkles className="w-4 h-4" />
                {t('aiQuestionnaire.tabs.templates')}
              </TabsTrigger>
              <TabsTrigger value="progress" disabled={!currentProgress} className="flex items-center gap-2">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  currentProgress ? "bg-blue-500" : "bg-gray-300"
                )} />
                {t('aiQuestionnaire.tabs.progress')}
              </TabsTrigger>
              <TabsTrigger value="result" disabled={!generationResponse} className="flex items-center gap-2">
                <CheckCircle className={cn(
                  "w-4 h-4",
                  generationResponse ? "text-green-500" : "text-gray-400"
                )} />
                {t('aiQuestionnaire.tabs.result')}
              </TabsTrigger>
            </TabsList>

            <div className="mt-6">
              <AnimatePresence mode="wait">
                <TabsContent value="form" className="mt-0">
                  <motion.div
                    key="form"
                    variants={contentVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                  >
                    <AIQuestionnaireForm
                      onGenerate={handleGenerate}
                      onProgress={handleProgress}
                      onError={handleError}
                    />
                  </motion.div>
                </TabsContent>

                <TabsContent value="templates" className="mt-0">
                  <motion.div
                    key="templates"
                    variants={contentVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                  >
                    <TemplateSelector
                      onSelect={handleTemplateSelect}
                      onCreateFromTemplate={handleCreateFromTemplate}
                    />
                  </motion.div>
                </TabsContent>

                <TabsContent value="progress" className="mt-0">
                  <motion.div
                    key="progress"
                    variants={contentVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                  >
                    {currentProgress && (
                      <GenerationProgress
                        progress={currentProgress}
                        onCancel={handleCancelGeneration}
                        onRetry={handleRetryGeneration}
                      />
                    )}
                  </motion.div>
                </TabsContent>

                <TabsContent value="result" className="mt-0">
                  <motion.div
                    key="result"
                    variants={contentVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                  >
                    {generationResponse && (
                      <Card>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <CheckCircle className="w-5 h-5 text-green-500" />
                            {t('aiQuestionnaire.result.title')}
                          </CardTitle>
                          <CardDescription>
                            {t('aiQuestionnaire.result.description')}
                          </CardDescription>
                        </CardHeader>

                        <CardContent className="space-y-6">
                          {/* Questionnaire Info */}
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <Card>
                              <CardContent className="pt-6">
                                <div className="text-center">
                                  <div className="text-2xl font-bold text-blue-600">
                                    {generationResponse.questions.length}
                                  </div>
                                  <div className="text-sm text-gray-600">
                                    {t('aiQuestionnaire.result.questionsGenerated')}
                                  </div>
                                </div>
                              </CardContent>
                            </Card>

                            <Card>
                              <CardContent className="pt-6">
                                <div className="text-center">
                                  <div className="text-2xl font-bold text-green-600">
                                    {generationResponse.estimated_duration}
                                  </div>
                                  <div className="text-sm text-gray-600">
                                    {t('aiQuestionnaire.result.estimatedMinutes')}
                                  </div>
                                </div>
                              </CardContent>
                            </Card>

                            <Card>
                              <CardContent className="pt-6">
                                <div className="text-center">
                                  <div className="text-2xl font-bold text-purple-600">
                                    {Math.round(generationResponse.generation_metadata.quality_score * 100)}%
                                  </div>
                                  <div className="text-sm text-gray-600">
                                    {t('aiQuestionnaire.result.qualityScore')}
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          </div>

                          {/* Questions Preview */}
                          <div>
                            <h3 className="font-semibold mb-4">{t('aiQuestionnaire.result.questionsPreview')}</h3>
                            <div className="space-y-3 max-h-96 overflow-y-auto">
                              {generationResponse.questions.slice(0, 5).map((question, index) => (
                                <Card key={question.id} className="p-4">
                                  <div className="flex items-start gap-3">
                                    <Badge variant="outline" className="text-xs">
                                      {index + 1}
                                    </Badge>
                                    <div className="flex-1">
                                      <p className="font-medium">{question.question_text}</p>
                                      <div className="flex gap-2 mt-2">
                                        <Badge variant="secondary" className="text-xs">
                                          {question.category}
                                        </Badge>
                                        <Badge variant="outline" className="text-xs">
                                          {question.difficulty}
                                        </Badge>
                                      </div>
                                    </div>
                                  </div>
                                </Card>
                              ))}
                              
                              {generationResponse.questions.length > 5 && (
                                <div className="text-center text-sm text-gray-500">
                                  {t('aiQuestionnaire.result.andMoreQuestions', { 
                                    count: generationResponse.questions.length - 5 
                                  })}
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Actions */}
                          {!savedQuestionnaire && (
                            <div className="flex justify-center">
                              <Button
                                onClick={() => setShowSaveDialog(true)}
                                size="lg"
                                className="min-w-[200px]"
                              >
                                <Save className="w-4 h-4 mr-2" />
                                {t('aiQuestionnaire.result.saveQuestionnaire')}
                              </Button>
                            </div>
                          )}

                          {savedQuestionnaire && (
                            <Alert>
                              <CheckCircle className="w-4 h-4" />
                              <AlertDescription>
                                {t('aiQuestionnaire.result.questionnaireSaved')}
                              </AlertDescription>
                            </Alert>
                          )}
                        </CardContent>
                      </Card>
                    )}
                  </motion.div>
                </TabsContent>
              </AnimatePresence>
            </div>
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="xl:col-span-1">
          <div className="space-y-4 sticky top-6">
            {/* Selected Template Info */}
            {selectedTemplate && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">{t('aiQuestionnaire.sidebar.selectedTemplate')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="font-medium text-sm">{selectedTemplate.name}</p>
                    <p className="text-xs text-gray-600">{selectedTemplate.description}</p>
                    <div className="flex flex-wrap gap-1">
                      {selectedTemplate.position_types.slice(0, 2).map(type => (
                        <Badge key={type} variant="outline" className="text-xs">
                          {type}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Generation Status */}
            {currentProgress && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-sm">{t('aiQuestionnaire.sidebar.generationStatus')}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">{t('aiQuestionnaire.sidebar.status')}</span>
                      <Badge variant={
                        currentProgress.status === GenerationStatus.COMPLETED ? 'success' as any :
                        currentProgress.status === GenerationStatus.FAILED ? 'destructive' :
                        'default'
                      }>
                        {currentProgress.status}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">{t('aiQuestionnaire.sidebar.progress')}</span>
                      <span className="text-sm font-medium">{currentProgress.progress_percentage}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Help Card */}
            <Card>
              <CardHeader>
                <CardTitle className="text-sm">{t('aiQuestionnaire.sidebar.helpTitle')}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2 text-xs text-gray-600">
                  <p>{t('aiQuestionnaire.sidebar.helpText1')}</p>
                  <p>{t('aiQuestionnaire.sidebar.helpText2')}</p>
                  <p>{t('aiQuestionnaire.sidebar.helpText3')}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      {/* Save Dialog */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{t('aiQuestionnaire.saveDialog.title')}</DialogTitle>
            <DialogDescription>
              {t('aiQuestionnaire.saveDialog.description')}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="flex justify-end gap-2">
              <Button
                variant="outline"
                onClick={() => setShowSaveDialog(false)}
                disabled={isSaving}
              >
                {t('common.cancel')}
              </Button>
              <Button
                onClick={() => handleSaveQuestionnaire()}
                disabled={isSaving}
              >
                {isSaving ? (
                  <>
                    <div className="w-4 h-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    {t('common.saving')}
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    {t('common.save')}
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </motion.div>
  );
}