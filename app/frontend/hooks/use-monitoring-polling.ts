import { useState, useEffect, useCallback, useRef } from 'react';
import { monitoringService } from '@/services/monitoring';
import { SystemHealthResponse } from '@/types/monitoring';

interface PollingConfig {
  enabled: boolean;
  interval: number; // milliseconds
  retryOnError: boolean;
  maxRetries: number;
}

interface PollingState {
  data: SystemHealthResponse | null;
  loading: boolean;
  error: string | null;
  lastUpdate: string | null;
  isPolling: boolean;
  retryCount: number;
}

const DEFAULT_CONFIG: PollingConfig = {
  enabled: true,
  interval: 30000, // 30 seconds
  retryOnError: true,
  maxRetries: 3
};

export const useMonitoringPolling = (config: Partial<PollingConfig> = {}) => {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  const [state, setState] = useState<PollingState>({
    data: null,
    loading: true,
    error: null,
    lastUpdate: null,
    isPolling: false,
    retryCount: 0
  });
  
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isUnmountedRef = useRef(false);
  
  const fetchData = useCallback(async () => {
    if (isUnmountedRef.current) return;
    
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const data = await monitoringService.getSystemHealth();
      
      if (isUnmountedRef.current) return;
      
      setState(prev => ({
        ...prev,
        data,
        loading: false,
        error: null,
        lastUpdate: new Date().toISOString(),
        retryCount: 0
      }));
      
    } catch (error: any) {
      if (isUnmountedRef.current) return;
      
      const errorCode = error.message || 'MONITORING_POLLING_ERROR';
      
      setState(prev => {
        const newRetryCount = prev.retryCount + 1;
        
        // If we've exceeded max retries, stop polling
        if (newRetryCount >= finalConfig.maxRetries) {
          return {
            ...prev,
            loading: false,
            error: errorCode,
            retryCount: newRetryCount,
            isPolling: false
          };
        }
        
        return {
          ...prev,
          loading: false,
          error: errorCode,
          retryCount: newRetryCount
        };
      });
    }
  }, [finalConfig.maxRetries]);
  
  const startPolling = useCallback(() => {
    if (!finalConfig.enabled || isUnmountedRef.current) return;
    
    setState(prev => ({ ...prev, isPolling: true, error: null, retryCount: 0 }));
    
    // Initial fetch
    fetchData();
    
    // Set up polling interval
    intervalRef.current = setInterval(fetchData, finalConfig.interval);
  }, [finalConfig.enabled, finalConfig.interval, fetchData]);
  
  const stopPolling = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
    setState(prev => ({ ...prev, isPolling: false }));
  }, []);
  
  const restartPolling = useCallback(() => {
    stopPolling();
    setTimeout(startPolling, 1000); // 1 second delay before restart
  }, [startPolling, stopPolling]);
  
  // Auto-start polling on mount
  useEffect(() => {
    startPolling();
    
    return () => {
      isUnmountedRef.current = true;
      stopPolling();
    };
  }, [startPolling, stopPolling]);
  
  return {
    ...state,
    startPolling,
    stopPolling,
    restartPolling,
    refreshNow: fetchData
  };
};