'use client';

import React, { useState, useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Users,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Award,
  Target,
  Filter,
  ArrowUpDown,
  Eye,
  User,
  Calendar,
  ChevronDown,
  MoreHorizontal
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { useTranslations } from '@/app/i18n/client';
import { cn } from '@/lib/utils';

import {
  EvaluationComparison,
  CandidateEvaluationSummary,
  EvaluationScores
} from '@/types/evaluation';

interface ComparisonViewProps {
  comparison: EvaluationComparison;
  onCandidateSelect?: (candidateId: string) => void;
  onExport?: () => void;
  className?: string;
}

type SortField = 'name' | 'overall_score' | 'dci_score' | 'jfs_score' | 'assessment_date';
type SortOrder = 'asc' | 'desc';

const cardVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
};

const rowVariants = {
  initial: { opacity: 0, x: -20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 20 }
};

// Score color mapping
const getScoreColor = (score: number) => {
  if (score >= 90) return 'text-green-600 bg-green-100';
  if (score >= 80) return 'text-blue-600 bg-blue-100'; 
  if (score >= 70) return 'text-yellow-600 bg-yellow-100';
  if (score >= 60) return 'text-orange-600 bg-orange-100';
  return 'text-red-600 bg-red-100';
};

const getPerformanceLevel = (score: number) => {
  if (score >= 90) return { label: '优秀', color: 'text-green-600' };
  if (score >= 80) return { label: '良好', color: 'text-blue-600' };
  if (score >= 70) return { label: '一般', color: 'text-yellow-600' };
  if (score >= 60) return { label: '有待提升', color: 'text-orange-600' };
  return { label: '需要改进', color: 'text-red-600' };
};

export const ComparisonView: React.FC<ComparisonViewProps> = ({
  comparison,
  onCandidateSelect,
  onExport,
  className
}) => {
  const t = useTranslations();
  const [sortField, setSortField] = useState<SortField>('overall_score');
  const [sortOrder, setSortOrder] = useState<SortOrder>('desc');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedCandidates, setSelectedCandidates] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'table' | 'grid'>('table');
  const [selectedCandidate, setSelectedCandidate] = useState<CandidateEvaluationSummary | null>(null);

  // Filter and sort candidates
  const filteredAndSortedCandidates = useMemo(() => {
    let filtered = comparison.candidates;

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(candidate => candidate.status === statusFilter);
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortField) {
        case 'name':
          aValue = a.candidate_name;
          bValue = b.candidate_name;
          break;
        case 'overall_score':
          aValue = a.overall_score;
          bValue = b.overall_score;
          break;
        case 'dci_score':
          aValue = a.dci_score;
          bValue = b.dci_score;
          break;
        case 'jfs_score':
          aValue = a.jfs_score;
          bValue = b.jfs_score;
          break;
        case 'assessment_date':
          aValue = new Date(a.assessment_date).getTime();
          bValue = new Date(b.assessment_date).getTime();
          break;
        default:
          aValue = a.overall_score;
          bValue = b.overall_score;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        const comparison = aValue.localeCompare(bValue);
        return sortOrder === 'asc' ? comparison : -comparison;
      }

      const comparison = (aValue as number) - (bValue as number);
      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return sorted;
  }, [comparison.candidates, sortField, sortOrder, statusFilter]);

  // Calculate statistics
  const statistics = useMemo(() => {
    const candidates = filteredAndSortedCandidates;
    const totalCandidates = candidates.length;
    
    if (totalCandidates === 0) {
      return {
        totalCandidates: 0,
        avgOverallScore: 0,
        avgDciScore: 0,
        avgJfsScore: 0,
        topPerformer: null,
        scoreDistribution: { excellent: 0, good: 0, average: 0, below_average: 0, poor: 0 }
      };
    }

    const avgOverallScore = candidates.reduce((sum, c) => sum + c.overall_score, 0) / totalCandidates;
    const avgDciScore = candidates.reduce((sum, c) => sum + c.dci_score, 0) / totalCandidates;
    const avgJfsScore = candidates.reduce((sum, c) => sum + c.jfs_score, 0) / totalCandidates;

    const topPerformer = candidates.reduce((top, current) => 
      current.overall_score > top.overall_score ? current : top
    );

    const scoreDistribution = candidates.reduce(
      (dist, candidate) => {
        const level = getPerformanceLevel(candidate.overall_score);
        if (level.label === '优秀') dist.excellent++;
        else if (level.label === '良好') dist.good++;
        else if (level.label === '一般') dist.average++;
        else if (level.label === '有待提升') dist.below_average++;
        else dist.poor++;
        return dist;
      },
      { excellent: 0, good: 0, average: 0, below_average: 0, poor: 0 }
    );

    return {
      totalCandidates,
      avgOverallScore: Math.round(avgOverallScore),
      avgDciScore: Math.round(avgDciScore),
      avgJfsScore: Math.round(avgJfsScore),
      topPerformer,
      scoreDistribution
    };
  }, [filteredAndSortedCandidates]);

  const handleSort = (field: SortField) => {
    if (field === sortField) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortOrder('desc');
    }
  };

  const handleCandidateView = (candidate: CandidateEvaluationSummary) => {
    setSelectedCandidate(candidate);
    onCandidateSelect?.(candidate.candidate_id);
  };

  const renderDimensionBars = (scores: EvaluationScores) => (
    <div className="space-y-1">
      {Object.entries(scores).map(([key, value]) => (
        <div key={key} className="flex items-center gap-2">
          <div className="w-16 text-xs text-gray-500 text-right">
            {t(`evaluation.dimensions.${key}.short`)}
          </div>
          <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div 
              className="h-full bg-blue-500 transition-all duration-300"
              style={{ width: `${value}%` }}
            />
          </div>
          <div className="w-8 text-xs font-medium text-right">{value}</div>
        </div>
      ))}
    </div>
  );

  return (
    <motion.div
      variants={cardVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className={cn("space-y-6", className)}
    >
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-6 h-6 text-blue-600" />
                {t('evaluation.comparison.title')}
              </CardTitle>
              <CardDescription className="mt-2">
                {comparison.comparison_metadata.position_title} - {comparison.comparison_metadata.questionnaire_title}
              </CardDescription>
            </div>
            
            <div className="flex items-center gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => setViewMode(viewMode === 'table' ? 'grid' : 'table')}
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                {viewMode === 'table' ? t('common.gridView') : t('common.tableView')}
              </Button>
              
              {onExport && (
                <Button variant="outline" size="sm" onClick={onExport}>
                  <ArrowUpDown className="w-4 h-4 mr-2" />
                  {t('common.export')}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {statistics.totalCandidates}
              </div>
              <div className="text-sm text-gray-600">
                {t('evaluation.comparison.totalCandidates')}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className={cn(
                "text-xl font-bold px-2 py-1 rounded",
                getScoreColor(statistics.avgOverallScore)
              )}>
                {statistics.avgOverallScore}
              </div>
              <div className="text-sm text-gray-600 mt-1">
                {t('evaluation.comparison.avgOverallScore')}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-xl font-bold text-green-600">
                {statistics.avgDciScore}
              </div>
              <div className="text-sm text-gray-600 mt-1">
                {t('evaluation.comparison.avgDciScore')}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-xl font-bold text-purple-600">
                {statistics.avgJfsScore}
              </div>
              <div className="text-sm text-gray-600 mt-1">
                {t('evaluation.comparison.avgJfsScore')}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <option value="all">{t('common.allStatuses')}</option>
                <option value="completed">{t('evaluation.status.completed')}</option>
                <option value="in_progress">{t('evaluation.status.inProgress')}</option>
                <option value="not_started">{t('evaluation.status.notStarted')}</option>
              </Select>
            </div>

            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500">{t('common.sortBy')}:</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSort('overall_score')}
                className="flex items-center gap-1"
              >
                {t('evaluation.overallScore')}
                {sortField === 'overall_score' && (
                  <ArrowUpDown className={cn(
                    "w-3 h-3 transition-transform",
                    sortOrder === 'desc' && "rotate-180"
                  )} />
                )}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSort('name')}
                className="flex items-center gap-1"
              >
                {t('common.name')}
                {sortField === 'name' && (
                  <ArrowUpDown className={cn(
                    "w-3 h-3 transition-transform",
                    sortOrder === 'desc' && "rotate-180"
                  )} />
                )}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleSort('assessment_date')}
                className="flex items-center gap-1"
              >
                {t('common.date')}
                {sortField === 'assessment_date' && (
                  <ArrowUpDown className={cn(
                    "w-3 h-3 transition-transform",
                    sortOrder === 'desc' && "rotate-180"
                  )} />
                )}
              </Button>
            </div>

            <div className="ml-auto text-sm text-gray-500">
              {filteredAndSortedCandidates.length} {t('evaluation.comparison.candidatesShowing')}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Candidates List/Grid */}
      <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as 'table' | 'grid')}>
        <TabsContent value="table">
          <Card>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[250px]">
                      <Button
                        variant="ghost"
                        className="font-semibold"
                        onClick={() => handleSort('name')}
                      >
                        {t('evaluation.candidate')}
                        {sortField === 'name' && (
                          <ArrowUpDown className={cn(
                            "ml-1 w-3 h-3 transition-transform",
                            sortOrder === 'desc' && "rotate-180"
                          )} />
                        )}
                      </Button>
                    </TableHead>
                    <TableHead className="text-center">
                      <Button
                        variant="ghost"
                        className="font-semibold"
                        onClick={() => handleSort('overall_score')}
                      >
                        {t('evaluation.overallScore')}
                        {sortField === 'overall_score' && (
                          <ArrowUpDown className={cn(
                            "ml-1 w-3 h-3 transition-transform",
                            sortOrder === 'desc' && "rotate-180"
                          )} />
                        )}
                      </Button>
                    </TableHead>
                    <TableHead className="text-center">
                      <Button
                        variant="ghost"
                        className="font-semibold"
                        onClick={() => handleSort('dci_score')}
                      >
                        DCI
                        {sortField === 'dci_score' && (
                          <ArrowUpDown className={cn(
                            "ml-1 w-3 h-3 transition-transform",
                            sortOrder === 'desc' && "rotate-180"
                          )} />
                        )}
                      </Button>
                    </TableHead>
                    <TableHead className="text-center">
                      <Button
                        variant="ghost"
                        className="font-semibold"
                        onClick={() => handleSort('jfs_score')}
                      >
                        JFS
                        {sortField === 'jfs_score' && (
                          <ArrowUpDown className={cn(
                            "ml-1 w-3 h-3 transition-transform",
                            sortOrder === 'desc' && "rotate-180"
                          )} />
                        )}
                      </Button>
                    </TableHead>
                    <TableHead>{t('evaluation.fiveDimensions')}</TableHead>
                    <TableHead className="text-center">{t('common.status')}</TableHead>
                    <TableHead className="text-center">{t('common.date')}</TableHead>
                    <TableHead className="w-[100px]">{t('common.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <AnimatePresence>
                    {filteredAndSortedCandidates.map((candidate, index) => (
                      <motion.tr
                        key={candidate.candidate_id}
                        variants={rowVariants}
                        initial="initial"
                        animate="animate"
                        exit="exit"
                        transition={{ delay: index * 0.05 }}
                        className="group hover:bg-gray-50 dark:hover:bg-gray-900"
                      >
                        <TableCell>
                          <div className="flex items-center gap-3">
                            <div className="flex-shrink-0">
                              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                <User className="w-4 h-4 text-blue-600" />
                              </div>
                            </div>
                            <div>
                              <div className="font-medium">{candidate.candidate_name}</div>
                              <div className="text-sm text-gray-500">
                                {t('evaluation.percentile', { rank: candidate.percentile_rank })}
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        
                        <TableCell className="text-center">
                          <div className={cn(
                            "inline-flex items-center px-2 py-1 rounded-full text-sm font-medium",
                            getScoreColor(candidate.overall_score)
                          )}>
                            {candidate.overall_score}
                          </div>
                        </TableCell>
                        
                        <TableCell className="text-center">
                          <span className="font-medium">{candidate.dci_score}</span>
                        </TableCell>
                        
                        <TableCell className="text-center">
                          <span className="font-medium">{candidate.jfs_score}</span>
                        </TableCell>
                        
                        <TableCell>
                          <div className="w-40">
                            {renderDimensionBars(candidate.evaluation_scores)}
                          </div>
                        </TableCell>
                        
                        <TableCell className="text-center">
                          <Badge variant={
                            candidate.status === 'completed' ? 'success' as any :
                            candidate.status === 'in_progress' ? 'default' :
                            'secondary'
                          }>
                            {t(`evaluation.status.${candidate.status}`)}
                          </Badge>
                        </TableCell>
                        
                        <TableCell className="text-center">
                          <div className="flex items-center justify-center gap-1">
                            <Calendar className="w-3 h-3 text-gray-400" />
                            <span className="text-sm">
                              {new Date(candidate.assessment_date).toLocaleDateString()}
                            </span>
                          </div>
                        </TableCell>
                        
                        <TableCell>
                          <div className="flex items-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleCandidateView(candidate)}
                            >
                              <Eye className="w-4 h-4" />
                            </Button>
                            
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm">
                                  <MoreHorizontal className="w-4 h-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem onClick={() => handleCandidateView(candidate)}>
                                  <Eye className="w-4 h-4 mr-2" />
                                  {t('common.viewDetails')}
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem>
                                  <Award className="w-4 h-4 mr-2" />
                                  {t('evaluation.comparison.addToShortlist')}
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </TableCell>
                      </motion.tr>
                    ))}
                  </AnimatePresence>
                </TableBody>
              </Table>
              
              {filteredAndSortedCandidates.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>{t('evaluation.comparison.noCandidatesFound')}</p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="grid">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <AnimatePresence>
              {filteredAndSortedCandidates.map((candidate, index) => (
                <motion.div
                  key={candidate.candidate_id}
                  variants={cardVariants}
                  initial="initial"
                  animate="animate"
                  exit="exit"
                  transition={{ delay: index * 0.1 }}
                >
                  <Card className="hover:shadow-lg transition-shadow cursor-pointer" onClick={() => handleCandidateView(candidate)}>
                    <CardHeader>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="w-5 h-5 text-blue-600" />
                          </div>
                          <div>
                            <CardTitle className="text-lg">{candidate.candidate_name}</CardTitle>
                            <CardDescription>
                              {t('evaluation.percentile', { rank: candidate.percentile_rank })}
                            </CardDescription>
                          </div>
                        </div>
                        
                        <Badge variant={
                          candidate.status === 'completed' ? 'success' as any :
                          candidate.status === 'in_progress' ? 'default' :
                          'secondary'
                        }>
                          {t(`evaluation.status.${candidate.status}`)}
                        </Badge>
                      </div>
                    </CardHeader>
                    
                    <CardContent>
                      {/* Scores */}
                      <div className="grid grid-cols-3 gap-4 mb-4">
                        <div className="text-center">
                          <div className={cn(
                            "text-xl font-bold px-2 py-1 rounded mb-1",
                            getScoreColor(candidate.overall_score)
                          )}>
                            {candidate.overall_score}
                          </div>
                          <div className="text-xs text-gray-500">{t('evaluation.overall')}</div>
                        </div>
                        
                        <div className="text-center">
                          <div className="text-lg font-semibold text-green-600 mb-1">
                            {candidate.dci_score}
                          </div>
                          <div className="text-xs text-gray-500">DCI</div>
                        </div>
                        
                        <div className="text-center">
                          <div className="text-lg font-semibold text-purple-600 mb-1">
                            {candidate.jfs_score}
                          </div>
                          <div className="text-xs text-gray-500">JFS</div>
                        </div>
                      </div>
                      
                      {/* Dimensions */}
                      <div className="space-y-2">
                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          {t('evaluation.fiveDimensions')}
                        </h4>
                        {renderDimensionBars(candidate.evaluation_scores)}
                      </div>
                      
                      {/* Date */}
                      <div className="flex items-center justify-between mt-4 pt-4 border-t">
                        <div className="flex items-center gap-1 text-sm text-gray-500">
                          <Calendar className="w-3 h-3" />
                          {new Date(candidate.assessment_date).toLocaleDateString()}
                        </div>
                        
                        <Button variant="ghost" size="sm">
                          <Eye className="w-4 h-4 mr-1" />
                          {t('common.view')}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
            
            {filteredAndSortedCandidates.length === 0 && (
              <div className="col-span-full text-center py-8 text-gray-500">
                <Users className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>{t('evaluation.comparison.noCandidatesFound')}</p>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      {/* Benchmarks Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="w-5 h-5" />
            {t('evaluation.comparison.benchmarks')}
          </CardTitle>
          <CardDescription>
            {t('evaluation.comparison.benchmarksDescription')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">{t('evaluation.comparison.industryBenchmark')}</h4>
              {renderDimensionBars(comparison.comparison_metadata.industry_benchmarks)}
            </div>
            
            <div>
              <h4 className="font-medium mb-3">{t('evaluation.comparison.positionBenchmark')}</h4>
              {renderDimensionBars(comparison.comparison_metadata.position_benchmarks)}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Candidate Detail Dialog */}
      <Dialog open={selectedCandidate !== null} onOpenChange={() => setSelectedCandidate(null)}>
        <DialogContent className="max-w-2xl">
          {selectedCandidate && (
            <>
              <DialogHeader>
                <DialogTitle>{selectedCandidate.candidate_name}</DialogTitle>
                <DialogDescription>
                  {t('evaluation.comparison.candidateDetails')}
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-4">
                {/* Quick Stats */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-4 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{selectedCandidate.overall_score}</div>
                    <div className="text-sm text-blue-600">{t('evaluation.overallScore')}</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">{selectedCandidate.dci_score}</div>
                    <div className="text-sm text-green-600">DCI Score</div>
                  </div>
                  <div className="text-center p-4 bg-purple-50 rounded-lg">
                    <div className="text-2xl font-bold text-purple-600">{selectedCandidate.jfs_score}</div>
                    <div className="text-sm text-purple-600">JFS Score</div>
                  </div>
                </div>
                
                {/* Five Dimensions */}
                <div>
                  <h4 className="font-medium mb-3">{t('evaluation.fiveDimensions')}</h4>
                  {renderDimensionBars(selectedCandidate.evaluation_scores)}
                </div>
                
                {/* Actions */}
                <div className="flex justify-end gap-2 pt-4">
                  <Button variant="outline" onClick={() => setSelectedCandidate(null)}>
                    {t('common.close')}
                  </Button>
                  <Button onClick={() => onCandidateSelect?.(selectedCandidate.candidate_id)}>
                    <Eye className="w-4 h-4 mr-2" />
                    {t('evaluation.viewFullReport')}
                  </Button>
                </div>
              </div>
            </>
          )}
        </DialogContent>
      </Dialog>
    </motion.div>
  );
};