'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import {
  User,
  Calendar,
  Clock,
  Target,
  TrendingUp,
  TrendingDown,
  Award,
  AlertTriangle,
  Lightbulb,
  BarChart3,
  PieChart,
  Download,
  Share2,
  Print,
  Eye,
  ChevronDown,
  ChevronRight
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { useTranslations } from '@/app/i18n/client';
import { cn } from '@/lib/utils';

import { 
  EvaluationReport as IEvaluationReport,
  EvaluationDimension,
  QuestionResponse,
  EvaluationScores
} from '@/types/evaluation';

// Import radar chart component (placeholder)
// import { RadarChart } from '@/components/charts/RadarChart';

interface EvaluationReportProps {
  report: IEvaluationReport;
  showComparison?: boolean;
  className?: string;
  onExport?: () => void;
  onShare?: () => void;
  onPrint?: () => void;
}

const cardVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
};

const scoreVariants = {
  initial: { scale: 0 },
  animate: { scale: 1 },
  transition: { type: "spring", stiffness: 200 }
};

// Score color mapping
const getScoreColor = (score: number) => {
  if (score >= 90) return 'text-green-600 bg-green-100';
  if (score >= 80) return 'text-blue-600 bg-blue-100';
  if (score >= 70) return 'text-yellow-600 bg-yellow-100';
  if (score >= 60) return 'text-orange-600 bg-orange-100';
  return 'text-red-600 bg-red-100';
};

const getPerformanceLevel = (score: number) => {
  if (score >= 90) return 'excellent';
  if (score >= 80) return 'good';
  if (score >= 70) return 'average';
  if (score >= 60) return 'below_average';
  return 'poor';
};

const performanceLevelLabels = {
  excellent: { label: '优秀', color: 'text-green-600', bgColor: 'bg-green-100' },
  good: { label: '良好', color: 'text-blue-600', bgColor: 'bg-blue-100' },
  average: { label: '一般', color: 'text-yellow-600', bgColor: 'bg-yellow-100' },
  below_average: { label: '有待提升', color: 'text-orange-600', bgColor: 'bg-orange-100' },
  poor: { label: '需要改进', color: 'text-red-600', bgColor: 'bg-red-100' }
};

export const EvaluationReport: React.FC<EvaluationReportProps> = ({
  report,
  showComparison = false,
  className,
  onExport,
  onShare,
  onPrint
}) => {
  const t = useTranslations();
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({
    dimensions: true,
    insights: true,
    questions: false
  });

  const toggleSection = (section: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [section]: !prev[section]
    }));
  };

  // Calculate radar chart data
  const radarData = {
    labels: [
      t('evaluation.dimensions.digitalLiteracy'),
      t('evaluation.dimensions.industrySkill'), 
      t('evaluation.dimensions.positionSkill'),
      t('evaluation.dimensions.innovation'),
      t('evaluation.dimensions.learningPotential')
    ],
    datasets: [
      {
        label: report.candidate_name,
        data: [
          report.evaluation_scores.digital_literacy,
          report.evaluation_scores.industry_skill,
          report.evaluation_scores.position_skill,
          report.evaluation_scores.innovation,
          report.evaluation_scores.learning_potential
        ],
        borderColor: 'rgb(59, 130, 246)',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        pointBackgroundColor: 'rgb(59, 130, 246)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(59, 130, 246)',
      },
      ...(showComparison ? [{
        label: t('evaluation.industryAverage'),
        data: [
          report.industry_average.digital_literacy,
          report.industry_average.industry_skill,
          report.industry_average.position_skill,
          report.industry_average.innovation,
          report.industry_average.learning_potential
        ],
        borderColor: 'rgb(156, 163, 175)',
        backgroundColor: 'rgba(156, 163, 175, 0.1)',
        pointBackgroundColor: 'rgb(156, 163, 175)',
        pointBorderColor: '#fff',
        pointHoverBackgroundColor: '#fff',
        pointHoverBorderColor: 'rgb(156, 163, 175)',
      }] : [])
    ]
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className={cn("space-y-6", className)}
    >
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 rounded-lg">
                <BarChart3 className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <CardTitle className="text-xl">
                  {t('evaluation.report.title')}
                </CardTitle>
                <CardDescription className="flex items-center gap-4 mt-2">
                  <span className="flex items-center gap-1">
                    <User className="w-4 h-4" />
                    {report.candidate_name}
                  </span>
                  <span className="flex items-center gap-1">
                    <Calendar className="w-4 h-4" />
                    {new Date(report.assessment_date).toLocaleDateString()}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {report.time_spent_minutes} {t('common.minutes')}
                  </span>
                </CardDescription>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center gap-2">
              {onExport && (
                <Button variant="outline" size="sm" onClick={onExport}>
                  <Download className="w-4 h-4 mr-2" />
                  {t('common.export')}
                </Button>
              )}
              {onShare && (
                <Button variant="outline" size="sm" onClick={onShare}>
                  <Share2 className="w-4 h-4 mr-2" />
                  {t('common.share')}
                </Button>
              )}
              {onPrint && (
                <Button variant="outline" size="sm" onClick={onPrint}>
                  <Print className="w-4 h-4 mr-2" />
                  {t('common.print')}
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Overview Scores */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <motion.div variants={scoreVariants} className="col-span-1">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className={cn(
                  "text-3xl font-bold mb-2 px-3 py-1 rounded-lg inline-block",
                  getScoreColor(report.overall_score)
                )}>
                  {report.overall_score}
                </div>
                <div className="text-sm font-medium text-gray-600">
                  {t('evaluation.overallScore')}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {t('evaluation.percentile', { rank: report.percentile_rank })}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={scoreVariants} className="col-span-1">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className={cn(
                  "text-2xl font-bold mb-2 px-3 py-1 rounded-lg inline-block",
                  getScoreColor(report.dci_score)
                )}>
                  {report.dci_score}
                </div>
                <div className="text-sm font-medium text-gray-600">
                  {t('evaluation.dciScore')}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {t('evaluation.digitalCompetency')}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={scoreVariants} className="col-span-1">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className={cn(
                  "text-2xl font-bold mb-2 px-3 py-1 rounded-lg inline-block",
                  getScoreColor(report.jfs_score)
                )}>
                  {report.jfs_score}
                </div>
                <div className="text-sm font-medium text-gray-600">
                  {t('evaluation.jfsScore')}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {t('evaluation.jobFitnessScore')}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <motion.div variants={scoreVariants} className="col-span-1">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <Badge
                  variant="outline"
                  className={cn(
                    "text-lg px-4 py-2",
                    performanceLevelLabels[report.performance_summary.overall_level].color,
                    performanceLevelLabels[report.performance_summary.overall_level].bgColor
                  )}
                >
                  {performanceLevelLabels[report.performance_summary.overall_level].label}
                </Badge>
                <div className="text-sm font-medium text-gray-600 mt-2">
                  {t('evaluation.performanceLevel')}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  {report.answered_questions}/{report.total_questions} {t('evaluation.questionsAnswered')}
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">{t('evaluation.tabs.overview')}</TabsTrigger>
          <TabsTrigger value="dimensions">{t('evaluation.tabs.dimensions')}</TabsTrigger>
          <TabsTrigger value="insights">{t('evaluation.tabs.insights')}</TabsTrigger>
          <TabsTrigger value="questions">{t('evaluation.tabs.questions')}</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Radar Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="w-5 h-5" />
                {t('evaluation.competencyProfile')}
              </CardTitle>
              <CardDescription>
                {t('evaluation.fiveDimensionAnalysis')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center">
                {/* Placeholder for radar chart */}
                <div className="text-center text-gray-500">
                  <PieChart className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <p>{t('evaluation.radarChartPlaceholder')}</p>
                  <p className="text-sm mt-2">Chart.js integration needed</p>
                </div>
                {/* 
                <RadarChart 
                  data={radarData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                      r: {
                        beginAtZero: true,
                        max: 100
                      }
                    }
                  }}
                />
                */}
              </div>
            </CardContent>
          </Card>

          {/* Performance Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="w-5 h-5" />
                {t('evaluation.performanceSummary')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Strengths */}
              <div>
                <h4 className="font-semibold text-green-600 flex items-center gap-2 mb-2">
                  <TrendingUp className="w-4 h-4" />
                  {t('evaluation.keyStrengths')}
                </h4>
                <div className="space-y-2">
                  {report.performance_summary.key_strengths.map((strength, index) => (
                    <Alert key={index}>
                      <Award className="w-4 h-4" />
                      <AlertDescription>{strength}</AlertDescription>
                    </Alert>
                  ))}
                </div>
              </div>

              {/* Weaknesses */}
              <div>
                <h4 className="font-semibold text-red-600 flex items-center gap-2 mb-2">
                  <TrendingDown className="w-4 h-4" />
                  {t('evaluation.keyWeaknesses')}
                </h4>
                <div className="space-y-2">
                  {report.performance_summary.key_weaknesses.map((weakness, index) => (
                    <Alert key={index} variant="destructive">
                      <AlertTriangle className="w-4 h-4" />
                      <AlertDescription>{weakness}</AlertDescription>
                    </Alert>
                  ))}
                </div>
              </div>

              {/* Recommendations */}
              <div>
                <h4 className="font-semibold text-blue-600 flex items-center gap-2 mb-2">
                  <Lightbulb className="w-4 h-4" />
                  {t('evaluation.recommendations')}
                </h4>
                <div className="space-y-2">
                  {report.performance_summary.recommendations.map((recommendation, index) => (
                    <Alert key={index}>
                      <Lightbulb className="w-4 h-4" />
                      <AlertDescription>{recommendation}</AlertDescription>
                    </Alert>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dimensions" className="space-y-6">
          {/* Five-dimensional Breakdown */}
          <div className="space-y-4">
            {report.evaluation_dimensions.map((dimension, index) => (
              <motion.div
                key={dimension.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card>
                  <Collapsible
                    open={expandedSections[`dimension_${index}`]}
                    onOpenChange={() => toggleSection(`dimension_${index}`)}
                  >
                    <CollapsibleTrigger asChild>
                      <CardHeader className="cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-900">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4">
                            <div className={cn(
                              "text-2xl font-bold px-3 py-2 rounded-lg",
                              getScoreColor(dimension.score)
                            )}>
                              {dimension.score}
                            </div>
                            <div>
                              <CardTitle className="text-lg">{dimension.name}</CardTitle>
                              <CardDescription>
                                权重: {Math.round(dimension.weight * 100)}% | 
                                加权得分: {dimension.weighted_score.toFixed(1)}
                              </CardDescription>
                            </div>
                          </div>
                          <ChevronRight className={cn(
                            "w-5 h-5 transition-transform duration-200",
                            expandedSections[`dimension_${index}`] && "rotate-90"
                          )} />
                        </div>
                        
                        <div className="mt-4">
                          <Progress value={dimension.percentage} className="h-2" />
                          <div className="flex justify-between text-sm text-gray-500 mt-1">
                            <span>{dimension.percentage.toFixed(1)}%</span>
                            <span>{dimension.score}/{dimension.max_score}</span>
                          </div>
                        </div>
                      </CardHeader>
                    </CollapsibleTrigger>
                    
                    <CollapsibleContent>
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {/* Sub-dimensions */}
                          <div>
                            <h5 className="font-medium mb-3">{t('evaluation.subDimensions')}</h5>
                            <div className="space-y-2">
                              {dimension.sub_dimensions.map((subDim, subIndex) => (
                                <div key={subIndex} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                                  <span className="text-sm">{subDim.name}</span>
                                  <div className="flex items-center gap-2">
                                    <Badge variant="outline" className={cn(
                                      performanceLevelLabels[subDim.performance_level].color
                                    )}>
                                      {subDim.score}/{subDim.max_score}
                                    </Badge>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>

                          {/* Strengths & Recommendations */}
                          <div className="space-y-4">
                            {dimension.strengths.length > 0 && (
                              <div>
                                <h5 className="font-medium text-green-600 mb-2">{t('evaluation.strengths')}</h5>
                                <ul className="text-sm space-y-1 text-gray-600">
                                  {dimension.strengths.map((strength, idx) => (
                                    <li key={idx} className="flex items-start gap-2">
                                      <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0" />
                                      {strength}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}

                            {dimension.recommendations.length > 0 && (
                              <div>
                                <h5 className="font-medium text-blue-600 mb-2">{t('evaluation.improvements')}</h5>
                                <ul className="text-sm space-y-1 text-gray-600">
                                  {dimension.recommendations.map((rec, idx) => (
                                    <li key={idx} className="flex items-start gap-2">
                                      <div className="w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 flex-shrink-0" />
                                      {rec}
                                    </li>
                                  ))}
                                </ul>
                              </div>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </CollapsibleContent>
                  </Collapsible>
                </Card>
              </motion.div>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-6">
          {/* AI Insights */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="w-5 h-5 text-yellow-600" />
                {t('evaluation.aiInsights')}
              </CardTitle>
              <CardDescription>
                {t('evaluation.aiInsightsDescription')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Automated Summary */}
              <div>
                <h4 className="font-medium mb-2">{t('evaluation.automatedSummary')}</h4>
                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                  {report.ai_insights.automated_summary}
                </p>
              </div>

              <Separator />

              {/* Key Metrics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardContent className="pt-6 text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {Math.round(report.ai_insights.predicted_job_success)}%
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {t('evaluation.predictedJobSuccess')}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6 text-center">
                    <Badge variant={
                      report.ai_insights.growth_potential === 'high' ? 'success' as any :
                      report.ai_insights.growth_potential === 'medium' ? 'default' :
                      'secondary'
                    } className="text-lg px-4 py-2">
                      {t(`evaluation.growthPotential.${report.ai_insights.growth_potential}`)}
                    </Badge>
                    <div className="text-sm text-gray-600 mt-2">
                      {t('evaluation.growthPotential.label')}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6 text-center">
                    <div className={cn(
                      "text-2xl font-bold",
                      report.ai_insights.cultural_fit_score >= 80 ? "text-green-600" :
                      report.ai_insights.cultural_fit_score >= 60 ? "text-yellow-600" :
                      "text-red-600"
                    )}>
                      {Math.round(report.ai_insights.cultural_fit_score)}%
                    </div>
                    <div className="text-sm text-gray-600 mt-1">
                      {t('evaluation.culturalFitScore')}
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Risk Factors */}
              {report.ai_insights.risk_factors.length > 0 && (
                <div>
                  <h4 className="font-medium text-red-600 mb-2">
                    {t('evaluation.riskFactors')}
                  </h4>
                  <div className="space-y-2">
                    {report.ai_insights.risk_factors.map((risk, index) => (
                      <Alert key={index} variant="destructive">
                        <AlertTriangle className="w-4 h-4" />
                        <AlertDescription>{risk}</AlertDescription>
                      </Alert>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="questions" className="space-y-4">
          {/* Question-level Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="w-5 h-5" />
                {t('evaluation.questionAnalysis')}
              </CardTitle>
              <CardDescription>
                {t('evaluation.detailedQuestionBreakdown')}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {report.question_responses.map((response, index) => (
                  <motion.div
                    key={response.question_id}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="border rounded-lg p-4"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <Badge variant="outline" className="text-xs">
                            Q{index + 1}
                          </Badge>
                          <Badge variant="secondary" className="text-xs">
                            {response.question_category}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {response.question_difficulty}
                          </Badge>
                        </div>
                        <p className="font-medium text-sm">{response.question_text}</p>
                      </div>
                      <div className={cn(
                        "px-2 py-1 rounded text-sm font-medium",
                        getScoreColor(Math.round((response.score_earned / response.max_score) * 100))
                      )}>
                        {response.score_earned}/{response.max_score}
                      </div>
                    </div>

                    {/* Answer */}
                    <div className="bg-gray-50 dark:bg-gray-900 rounded p-3 mb-2">
                      <div className="text-sm">
                        <strong>{t('evaluation.candidateAnswer')}:</strong>
                        <div className="mt-1 text-gray-700 dark:text-gray-300">
                          {Array.isArray(response.candidate_answer) 
                            ? response.candidate_answer.join(', ')
                            : response.candidate_answer
                          }
                        </div>
                      </div>
                    </div>

                    {/* AI Analysis */}
                    {response.ai_analysis && (
                      <div className="border-t pt-3">
                        <h5 className="text-sm font-medium mb-2">{t('evaluation.aiAnalysis')}</h5>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mb-2">
                          <div className="text-xs">
                            <span className="text-gray-500">{t('evaluation.answerQuality')}:</span>
                            <Badge variant="outline" className="ml-1">
                              {t(`evaluation.quality.${response.ai_analysis.answer_quality}`)}
                            </Badge>
                          </div>
                          <div className="text-xs">
                            <span className="text-gray-500">{t('evaluation.reasoningScore')}:</span>
                            <span className="ml-1 font-medium">{response.ai_analysis.reasoning_score}/10</span>
                          </div>
                          <div className="text-xs">
                            <span className="text-gray-500">{t('evaluation.creativity')}:</span>
                            <span className="ml-1 font-medium">{response.ai_analysis.creativity_score}/10</span>
                          </div>
                          <div className="text-xs">
                            <span className="text-gray-500">{t('evaluation.clarity')}:</span>
                            <span className="ml-1 font-medium">{response.ai_analysis.communication_clarity}/10</span>
                          </div>
                        </div>
                        
                        {response.ai_analysis.suggestions.length > 0 && (
                          <div className="text-xs text-gray-600">
                            <strong>{t('evaluation.suggestions')}:</strong>
                            <ul className="mt-1 space-y-1">
                              {response.ai_analysis.suggestions.map((suggestion, idx) => (
                                <li key={idx} className="flex items-start gap-1">
                                  <div className="w-1 h-1 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                                  {suggestion}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                    )}
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </motion.div>
  );
};