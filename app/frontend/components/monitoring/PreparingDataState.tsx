import React from 'react';
import { motion } from 'framer-motion';
import { RefreshCw, Clock, Database, Activity } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useTranslations } from '@/app/i18n/client';

interface PreparingDataStateProps {
  className?: string;
  estimatedWaitTime?: number; // seconds
  showProgress?: boolean;
}

export const PreparingDataState: React.FC<PreparingDataStateProps> = ({
  className = "",
  estimatedWaitTime = 60,
  showProgress = true
}) => {
  const t = useTranslations();
  const [elapsedTime, setElapsedTime] = React.useState(0);
  const [progress, setProgress] = React.useState(0);
  
  React.useEffect(() => {
    const interval = setInterval(() => {
      setElapsedTime(prev => {
        const newTime = prev + 1;
        // Update progress based on elapsed time vs estimated time
        if (showProgress && estimatedWaitTime > 0) {
          const newProgress = Math.min((newTime / estimatedWaitTime) * 100, 95);
          setProgress(newProgress);
        }
        return newTime;
      });
    }, 1000);
    
    return () => clearInterval(interval);
  }, [estimatedWaitTime, showProgress]);
  
  return (
    <Card className={`border-2 border-dashed border-blue-300 bg-blue-50/50 dark:bg-blue-950/20 ${className}`}>
      <CardHeader className="text-center">
        <div className="flex justify-center mb-4">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            className="p-3 rounded-full bg-blue-100 dark:bg-blue-900"
          >
            <RefreshCw className="h-8 w-8 text-blue-600 dark:text-blue-400" />
          </motion.div>
        </div>
        
        <CardTitle className="text-xl text-blue-800 dark:text-blue-200">
          {t('monitoring.preparing.title', { defaultValue: 'Preparing Monitoring Data' })}
        </CardTitle>
        
        <div className="text-blue-600 dark:text-blue-400 space-y-2">
          <p className="text-sm">
            {t('monitoring.preparing.description', {
              defaultValue: 'Our system is performing comprehensive health checks across all services.'
            })}
          </p>
          
          {showProgress && (
            <div className="space-y-2">
              <Progress value={progress} className="w-full h-2" />
              <div className="flex justify-between text-xs text-blue-500">
                <span>{Math.round(progress)}% {t('monitoring.preparing.complete', { defaultValue: 'Complete' })}</span>
                <span>{elapsedTime}s {t('monitoring.preparing.elapsed', { defaultValue: 'elapsed' })}</span>
              </div>
            </div>
          )}
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
          <div className="space-y-2">
            <Database className="h-6 w-6 mx-auto text-blue-500" />
            <div className="text-sm">
              <div className="font-medium text-blue-800 dark:text-blue-200">
                {t('monitoring.preparing.checking.databases', { defaultValue: 'Databases' })}
              </div>
              <div className="text-blue-600 dark:text-blue-400 text-xs">
                PostgreSQL, Redis, MinIO
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Activity className="h-6 w-6 mx-auto text-blue-500" />
            <div className="text-sm">
              <div className="font-medium text-blue-800 dark:text-blue-200">
                {t('monitoring.preparing.checking.services', { defaultValue: 'Services' })}
              </div>
              <div className="text-blue-600 dark:text-blue-400 text-xs">
                APIs, Background Tasks
              </div>
            </div>
          </div>
          
          <div className="space-y-2">
            <Clock className="h-6 w-6 mx-auto text-blue-500" />
            <div className="text-sm">
              <div className="font-medium text-blue-800 dark:text-blue-200">
                {t('monitoring.preparing.estimatedTime', { defaultValue: 'Estimated Time' })}
              </div>
              <div className="text-blue-600 dark:text-blue-400 text-xs">
                ~{Math.max(0, estimatedWaitTime - elapsedTime)}s {t('monitoring.preparing.remaining', { defaultValue: 'remaining' })}
              </div>
            </div>
          </div>
        </div>
        
        <div className="mt-6 p-4 bg-blue-100/50 dark:bg-blue-900/30 rounded-lg">
          <div className="text-xs text-blue-600 dark:text-blue-400 text-center space-y-1">
            <p>
              {t('monitoring.preparing.cacheInfo', {
                defaultValue: 'Data is cached for 5 minutes after completion for optimal performance.'
              })}
            </p>
            <p>
              {t('monitoring.preparing.autoRefresh', {
                defaultValue: 'This page will automatically update when data becomes available.'
              })}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};