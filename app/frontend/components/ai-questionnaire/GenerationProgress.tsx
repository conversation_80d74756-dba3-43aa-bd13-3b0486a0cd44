'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Bot, 
  Target, 
  FileText, 
  ShieldCheck,
  Sparkles
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useTranslations } from '@/app/i18n/client';
import { cn } from '@/lib/utils';

import { GenerationProgress as IGenerationProgress, GenerationStatus } from '@/types/questionnaire';

interface GenerationProgressProps {
  progress: IGenerationProgress;
  onCancel?: () => void;
  onRetry?: () => void;
  className?: string;
}

const stepIcons = {
  [GenerationStatus.PENDING]: Clock,
  [GenerationStatus.ANALYZING_REQUIREMENTS]: Target,
  [GenerationStatus.GENERATING_QUESTIONS]: Bot,
  [GenerationStatus.REVIEWING_QUALITY]: ShieldCheck,
  [GenerationStatus.FINALIZING]: Sparkles,
  [GenerationStatus.COMPLETED]: CheckCircle,
  [GenerationStatus.FAILED]: XCircle
};

const stepColors = {
  [GenerationStatus.PENDING]: 'text-gray-500',
  [GenerationStatus.ANALYZING_REQUIREMENTS]: 'text-blue-500',
  [GenerationStatus.GENERATING_QUESTIONS]: 'text-purple-500',
  [GenerationStatus.REVIEWING_QUALITY]: 'text-green-500',
  [GenerationStatus.FINALIZING]: 'text-yellow-500',
  [GenerationStatus.COMPLETED]: 'text-green-600',
  [GenerationStatus.FAILED]: 'text-red-500'
};

const stepBgColors = {
  [GenerationStatus.PENDING]: 'bg-gray-100 dark:bg-gray-800',
  [GenerationStatus.ANALYZING_REQUIREMENTS]: 'bg-blue-100 dark:bg-blue-900/50',
  [GenerationStatus.GENERATING_QUESTIONS]: 'bg-purple-100 dark:bg-purple-900/50',
  [GenerationStatus.REVIEWING_QUALITY]: 'bg-green-100 dark:bg-green-900/50',
  [GenerationStatus.FINALIZING]: 'bg-yellow-100 dark:bg-yellow-900/50',
  [GenerationStatus.COMPLETED]: 'bg-green-100 dark:bg-green-900/50',
  [GenerationStatus.FAILED]: 'bg-red-100 dark:bg-red-900/50'
};

const progressVariants = {
  initial: { opacity: 0, scale: 0.95 },
  animate: { opacity: 1, scale: 1 },
  exit: { opacity: 0, scale: 0.95 }
};

const pulseVariants = {
  initial: { scale: 1 },
  animate: {
    scale: [1, 1.05, 1],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
};

export const GenerationProgress: React.FC<GenerationProgressProps> = ({
  progress,
  onCancel,
  onRetry,
  className
}) => {
  const t = useTranslations();

  const getStatusBadge = () => {
    const variants = {
      [GenerationStatus.PENDING]: 'secondary',
      [GenerationStatus.ANALYZING_REQUIREMENTS]: 'default',
      [GenerationStatus.GENERATING_QUESTIONS]: 'default',
      [GenerationStatus.REVIEWING_QUALITY]: 'default',
      [GenerationStatus.FINALIZING]: 'default',
      [GenerationStatus.COMPLETED]: 'success' as any,
      [GenerationStatus.FAILED]: 'destructive'
    };

    return (
      <Badge variant={variants[progress.status] || 'secondary'}>
        {t(`aiQuestionnaire.status.${progress.status}`)}
      </Badge>
    );
  };

  const getProgressColor = () => {
    if (progress.status === GenerationStatus.FAILED) return 'bg-red-500';
    if (progress.status === GenerationStatus.COMPLETED) return 'bg-green-500';
    return 'bg-blue-500';
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const isInProgress = [
    GenerationStatus.PENDING,
    GenerationStatus.ANALYZING_REQUIREMENTS,
    GenerationStatus.GENERATING_QUESTIONS,
    GenerationStatus.REVIEWING_QUALITY,
    GenerationStatus.FINALIZING
  ].includes(progress.status);

  const IconComponent = stepIcons[progress.status];

  return (
    <AnimatePresence mode="wait">
      <motion.div
        variants={progressVariants}
        initial="initial"
        animate="animate"
        exit="exit"
        className={cn("w-full", className)}
      >
        <Card className={cn(
          "transition-all duration-300",
          stepBgColors[progress.status]
        )}>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-3">
                <motion.div
                  variants={isInProgress ? pulseVariants : {}}
                  initial="initial"
                  animate={isInProgress ? "animate" : "initial"}
                  className={cn(
                    "p-2 rounded-lg",
                    stepBgColors[progress.status]
                  )}
                >
                  <IconComponent className={cn("w-5 h-5", stepColors[progress.status])} />
                </motion.div>
                {t('aiQuestionnaire.generationProgress')}
              </CardTitle>
              {getStatusBadge()}
            </div>
            <CardDescription>
              {t('aiQuestionnaire.generationId')}: {progress.generation_id}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Current Step */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="font-medium text-sm">
                  {progress.current_step}
                </span>
                <span className="text-sm text-gray-500">
                  {progress.progress_percentage}%
                </span>
              </div>
              
              <div className="relative">
                <Progress 
                  value={progress.progress_percentage} 
                  className="h-3"
                />
                <motion.div
                  className={cn(
                    "absolute top-0 left-0 h-3 rounded-full transition-all duration-500",
                    getProgressColor()
                  )}
                  initial={{ width: 0 }}
                  animate={{ width: `${progress.progress_percentage}%` }}
                  style={{ width: `${progress.progress_percentage}%` }}
                />
              </div>
            </div>

            {/* Time Information */}
            {progress.estimated_remaining_time > 0 && isInProgress && (
              <div className="flex items-center justify-between text-sm">
                <span className="text-gray-600">
                  {t('aiQuestionnaire.estimatedRemaining')}:
                </span>
                <span className="font-medium">
                  {formatTime(progress.estimated_remaining_time)}
                </span>
              </div>
            )}

            {/* Timestamps */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">{t('common.started')}:</span>
                <div className="font-medium">
                  {new Date(progress.created_at).toLocaleTimeString()}
                </div>
              </div>
              <div>
                <span className="text-gray-600">{t('common.lastUpdated')}:</span>
                <div className="font-medium">
                  {new Date(progress.updated_at).toLocaleTimeString()}
                </div>
              </div>
            </div>

            {/* Error Message */}
            {progress.status === GenerationStatus.FAILED && progress.error_message && (
              <Alert variant="destructive">
                <AlertTriangle className="w-4 h-4" />
                <AlertDescription>
                  {progress.error_message}
                </AlertDescription>
              </Alert>
            )}

            {/* Success Message */}
            {progress.status === GenerationStatus.COMPLETED && (
              <Alert>
                <CheckCircle className="w-4 h-4" />
                <AlertDescription>
                  {t('aiQuestionnaire.generationCompleted')}
                </AlertDescription>
              </Alert>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end gap-2">
              {isInProgress && onCancel && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onCancel}
                >
                  {t('common.cancel')}
                </Button>
              )}

              {progress.status === GenerationStatus.FAILED && onRetry && (
                <Button
                  variant="default"
                  size="sm"
                  onClick={onRetry}
                >
                  {t('common.retry')}
                </Button>
              )}
            </div>

            {/* Progress Steps Visualization */}
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {t('aiQuestionnaire.generationSteps')}
              </h4>
              <div className="flex items-center justify-between">
                {[
                  GenerationStatus.ANALYZING_REQUIREMENTS,
                  GenerationStatus.GENERATING_QUESTIONS,
                  GenerationStatus.REVIEWING_QUALITY,
                  GenerationStatus.FINALIZING
                ].map((step, index) => {
                  const StepIcon = stepIcons[step];
                  const isCompleted = Object.values(GenerationStatus).indexOf(progress.status) > Object.values(GenerationStatus).indexOf(step);
                  const isCurrent = progress.status === step;
                  const isPending = Object.values(GenerationStatus).indexOf(progress.status) < Object.values(GenerationStatus).indexOf(step);
                  
                  return (
                    <div
                      key={step}
                      className="flex flex-col items-center gap-2"
                    >
                      <motion.div
                        className={cn(
                          "w-8 h-8 rounded-full flex items-center justify-center border-2 transition-all duration-300",
                          isCompleted && "bg-green-100 border-green-500 dark:bg-green-900/50 dark:border-green-400",
                          isCurrent && "bg-blue-100 border-blue-500 dark:bg-blue-900/50 dark:border-blue-400",
                          isPending && "bg-gray-100 border-gray-300 dark:bg-gray-800 dark:border-gray-600"
                        )}
                        animate={isCurrent ? { scale: [1, 1.1, 1] } : { scale: 1 }}
                        transition={{ duration: 2, repeat: isCurrent ? Infinity : 0 }}
                      >
                        <StepIcon className={cn(
                          "w-4 h-4",
                          isCompleted && "text-green-600 dark:text-green-400",
                          isCurrent && "text-blue-600 dark:text-blue-400",
                          isPending && "text-gray-400 dark:text-gray-500"
                        )} />
                      </motion.div>
                      
                      <span className={cn(
                        "text-xs text-center max-w-16",
                        isCompleted && "text-green-600 dark:text-green-400 font-medium",
                        isCurrent && "text-blue-600 dark:text-blue-400 font-medium",
                        isPending && "text-gray-400 dark:text-gray-500"
                      )}>
                        {t(`aiQuestionnaire.step.${step}`)}
                      </span>
                      
                      {index < 3 && (
                        <div className={cn(
                          "h-0.5 w-12 mt-4 transition-colors duration-300",
                          isCompleted && "bg-green-500",
                          !isCompleted && "bg-gray-300 dark:bg-gray-600"
                        )} />
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </AnimatePresence>
  );
};