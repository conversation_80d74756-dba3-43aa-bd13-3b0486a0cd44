'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Template, 
  Search, 
  Filter, 
  Star, 
  Clock, 
  Users, 
  Building, 
  ChevronRight,
  Plus,
  BookOpen,
  Sparkles
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select } from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslations } from '@/app/i18n/client';
import { cn } from '@/lib/utils';

import { 
  QuestionnaireTemplate,
  TemplateListResponse,
  QuestionnaireGenerateRequest
} from '@/types/questionnaire';
import aiQuestionnaireService from '@/services/aiQuestionnaireService';

interface TemplateSelectorProps {
  onSelect: (template: QuestionnaireTemplate) => void;
  onCreateFromTemplate: (template: QuestionnaireTemplate) => void;
  selectedPositionType?: string;
  selectedIndustry?: string;
  className?: string;
}

const cardVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
};

const itemVariants = {
  initial: { opacity: 0, x: -20 },
  animate: { opacity: 1, x: 0 },
  exit: { opacity: 0, x: 20 }
};

export const TemplateSelector: React.FC<TemplateSelectorProps> = ({
  onSelect,
  onCreateFromTemplate,
  selectedPositionType,
  selectedIndustry,
  className
}) => {
  const t = useTranslations();
  
  const [templates, setTemplates] = useState<QuestionnaireTemplate[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterIndustry, setFilterIndustry] = useState<string>('');
  const [filterPositionType, setFilterPositionType] = useState<string>('');
  const [showSystemTemplates, setShowSystemTemplates] = useState(true);
  const [selectedTemplate, setSelectedTemplate] = useState<QuestionnaireTemplate | null>(null);

  // Load templates on component mount
  useEffect(() => {
    loadTemplates();
  }, []);

  // Auto-filter based on props
  useEffect(() => {
    if (selectedIndustry && !filterIndustry) {
      setFilterIndustry(selectedIndustry);
    }
    if (selectedPositionType && !filterPositionType) {
      setFilterPositionType(selectedPositionType);
    }
  }, [selectedIndustry, selectedPositionType, filterIndustry, filterPositionType]);

  const loadTemplates = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await aiQuestionnaireService.getTemplates({
        industry: filterIndustry || undefined,
        position_type: filterPositionType || undefined,
        limit: 50
      });
      
      setTemplates(response.items);
    } catch (err: any) {
      setError(err?.detail || err?.message || 'Failed to load templates');
    } finally {
      setLoading(false);
    }
  };

  // Reload templates when filters change
  useEffect(() => {
    loadTemplates();
  }, [filterIndustry, filterPositionType]);

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = !searchTerm || 
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesSystemFilter = showSystemTemplates || !template.is_system;
    
    return matchesSearch && matchesSystemFilter;
  });

  const getTemplateIcon = (template: QuestionnaireTemplate) => {
    if (template.is_system) {
      return <Sparkles className="w-5 h-5 text-blue-500" />;
    }
    return <BookOpen className="w-5 h-5 text-green-500" />;
  };

  const getRecommendationScore = (template: QuestionnaireTemplate) => {
    let score = 0;
    
    // Match position type
    if (selectedPositionType && template.position_types.some(type => 
      type.toLowerCase().includes(selectedPositionType.toLowerCase())
    )) {
      score += 40;
    }
    
    // Match industry
    if (selectedIndustry && template.industries.some(industry => 
      industry.toLowerCase().includes(selectedIndustry.toLowerCase())
    )) {
      score += 40;
    }
    
    // System templates get slight boost
    if (template.is_system) {
      score += 10;
    }
    
    // Popular templates (mock data - would come from backend)
    score += Math.floor(Math.random() * 10);
    
    return Math.min(score, 100);
  };

  const recommendedTemplates = filteredTemplates
    .map(template => ({
      ...template,
      recommendationScore: getRecommendationScore(template)
    }))
    .sort((a, b) => b.recommendationScore - a.recommendationScore);

  const handleTemplateSelect = (template: QuestionnaireTemplate) => {
    setSelectedTemplate(template);
    onSelect(template);
  };

  const handleCreateFromTemplate = () => {
    if (selectedTemplate) {
      onCreateFromTemplate(selectedTemplate);
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Template className="w-5 h-5" />
            {t('aiQuestionnaire.templates.title')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="space-y-2">
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-1/2" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-600">
            <Template className="w-5 h-5" />
            {t('aiQuestionnaire.templates.loadError')}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600 mb-4">{error}</p>
          <Button onClick={loadTemplates} variant="outline">
            {t('common.retry')}
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <motion.div
      variants={cardVariants}
      initial="initial"
      animate="animate"
      exit="exit"
      className={className}
    >
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Template className="w-5 h-5 text-blue-600" />
            {t('aiQuestionnaire.templates.title')}
          </CardTitle>
          <CardDescription>
            {t('aiQuestionnaire.templates.description')}
          </CardDescription>
        </CardHeader>

        <CardContent>
          {/* Search and Filters */}
          <div className="space-y-4 mb-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder={t('aiQuestionnaire.templates.searchPlaceholder')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
              <Select
                value={filterIndustry}
                onValueChange={setFilterIndustry}
                placeholder={t('aiQuestionnaire.templates.filterIndustry')}
              >
                <option value="">{t('common.all')}</option>
                <option value="Technology">Technology</option>
                <option value="Healthcare">Healthcare</option>
                <option value="Finance">Finance</option>
                <option value="Education">Education</option>
                <option value="Manufacturing">Manufacturing</option>
              </Select>

              <Select
                value={filterPositionType}
                onValueChange={setFilterPositionType}
                placeholder={t('aiQuestionnaire.templates.filterPosition')}
              >
                <option value="">{t('common.all')}</option>
                <option value="Software Engineer">Software Engineer</option>
                <option value="Product Manager">Product Manager</option>
                <option value="Data Scientist">Data Scientist</option>
                <option value="Designer">Designer</option>
                <option value="Marketing Manager">Marketing Manager</option>
              </Select>

              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={showSystemTemplates}
                  onChange={(e) => setShowSystemTemplates(e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm">{t('aiQuestionnaire.templates.showSystem')}</span>
              </label>
            </div>
          </div>

          {/* Template List */}
          <ScrollArea className="h-96">
            <div className="space-y-3">
              <AnimatePresence>
                {recommendedTemplates.length === 0 ? (
                  <motion.div
                    variants={itemVariants}
                    initial="initial"
                    animate="animate"
                    exit="exit"
                    className="text-center py-8 text-gray-500"
                  >
                    <Template className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                    <p>{t('aiQuestionnaire.templates.noTemplatesFound')}</p>
                  </motion.div>
                ) : (
                  recommendedTemplates.map((template, index) => (
                    <motion.div
                      key={template.id}
                      variants={itemVariants}
                      initial="initial"
                      animate="animate"
                      exit="exit"
                      transition={{ delay: index * 0.05 }}
                      className={cn(
                        "p-4 rounded-lg border cursor-pointer transition-all duration-200 hover:shadow-md",
                        selectedTemplate?.id === template.id 
                          ? "border-blue-500 bg-blue-50 dark:bg-blue-950/50"
                          : "border-gray-200 hover:border-gray-300"
                      )}
                      onClick={() => handleTemplateSelect(template)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            {getTemplateIcon(template)}
                            <h3 className="font-semibold">{template.name}</h3>
                            {template.recommendationScore >= 70 && (
                              <Badge variant="secondary" className="text-xs">
                                <Star className="w-3 h-3 mr-1" />
                                {t('aiQuestionnaire.templates.recommended')}
                              </Badge>
                            )}
                          </div>

                          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                            {template.description}
                          </p>

                          <div className="flex flex-wrap gap-2 mb-3">
                            {/* Position Types */}
                            {template.position_types.slice(0, 2).map(type => (
                              <Badge key={type} variant="outline" className="text-xs">
                                <Users className="w-3 h-3 mr-1" />
                                {type}
                              </Badge>
                            ))}
                            {template.position_types.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{template.position_types.length - 2}
                              </Badge>
                            )}

                            {/* Industries */}
                            {template.industries.slice(0, 2).map(industry => (
                              <Badge key={industry} variant="outline" className="text-xs">
                                <Building className="w-3 h-3 mr-1" />
                                {industry}
                              </Badge>
                            ))}
                            {template.industries.length > 2 && (
                              <Badge variant="outline" className="text-xs">
                                +{template.industries.length - 2}
                              </Badge>
                            )}
                          </div>

                          <div className="flex items-center justify-between text-xs text-gray-500">
                            <div className="flex items-center gap-4">
                              <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {template.default_settings.question_count || 20} questions
                              </span>
                              <span className="flex items-center gap-1">
                                <Template className="w-3 h-3" />
                                {template.is_system ? 'System' : 'Custom'}
                              </span>
                            </div>
                            
                            {template.recommendationScore > 0 && (
                              <span className="text-blue-600">
                                {template.recommendationScore}% match
                              </span>
                            )}
                          </div>
                        </div>

                        <ChevronRight className={cn(
                          "w-5 h-5 text-gray-400 transition-transform duration-200",
                          selectedTemplate?.id === template.id && "rotate-90"
                        )} />
                      </div>

                      {/* Template Details (Expanded) */}
                      <AnimatePresence>
                        {selectedTemplate?.id === template.id && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            transition={{ duration: 0.2 }}
                            className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
                          >
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div>
                                <strong>{t('aiQuestionnaire.templates.defaultSettings')}:</strong>
                                <ul className="mt-1 space-y-1 text-gray-600 dark:text-gray-400">
                                  <li>Questions: {template.default_settings.question_count || 20}</li>
                                  <li>Difficulty: {template.default_settings.difficulty_level || 'Medium'}</li>
                                  <li>Language: {template.default_settings.language || 'English'}</li>
                                </ul>
                              </div>
                              
                              <div>
                                <strong>{t('aiQuestionnaire.templates.focusAreas')}:</strong>
                                <div className="mt-1 flex flex-wrap gap-1">
                                  {template.default_settings.focus_areas?.map(area => (
                                    <Badge key={area} variant="secondary" className="text-xs">
                                      {area}
                                    </Badge>
                                  )) || <span className="text-gray-400">Not specified</span>}
                                </div>
                              </div>
                            </div>

                            <div className="flex justify-end gap-2 mt-4">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  // Open template preview dialog
                                }}
                              >
                                {t('common.preview')}
                              </Button>
                              
                              <Button
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleCreateFromTemplate();
                                }}
                              >
                                <Plus className="w-4 h-4 mr-1" />
                                {t('aiQuestionnaire.templates.useTemplate')}
                              </Button>
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  ))
                )}
              </AnimatePresence>
            </div>
          </ScrollArea>

          {/* Action Buttons */}
          {recommendedTemplates.length > 0 && (
            <div className="flex justify-between items-center mt-4 pt-4 border-t">
              <span className="text-sm text-gray-500">
                {recommendedTemplates.length} {t('aiQuestionnaire.templates.templatesFound')}
              </span>
              
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={loadTemplates}
                >
                  {t('common.refresh')}
                </Button>
                
                {selectedTemplate && (
                  <Button
                    size="sm"
                    onClick={handleCreateFromTemplate}
                  >
                    {t('aiQuestionnaire.templates.generateFromTemplate')}
                  </Button>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};