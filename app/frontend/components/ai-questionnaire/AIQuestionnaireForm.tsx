'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { 
  Bot, 
  Settings, 
  Target, 
  Users, 
  Clock, 
  Brain,
  CheckCircle,
  XCircle,
  AlertCircle,
  Lightbulb
} from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useTranslations } from '@/app/i18n/client';
import { cn } from '@/lib/utils';

import { 
  QuestionnaireGenerateRequest,
  QuestionnaireGenerateResponse,
  GenerationProgress,
  GenerationStatus 
} from '@/types/questionnaire';
import aiQuestionnaireService from '@/services/aiQuestionnaireService';

// Form validation schema
const questionnaireSchema = z.object({
  position_type: z.string().min(1, 'Position type is required'),
  company_size: z.string().min(1, 'Company size is required'),
  industry: z.string().min(1, 'Industry is required'),
  question_count: z.number().min(5, 'At least 5 questions required').max(50, 'Maximum 50 questions allowed'),
  difficulty_level: z.string().min(1, 'Difficulty level is required'),
  focus_areas: z.array(z.string()).min(1, 'At least one focus area is required'),
  include_behavioral: z.boolean(),
  include_technical: z.boolean(),
  include_situational: z.boolean(),
  language: z.string().min(1, 'Language is required'),
  additional_requirements: z.string().optional()
});

type FormData = z.infer<typeof questionnaireSchema>;

interface AIQuestionnaireFormProps {
  onGenerate: (response: QuestionnaireGenerateResponse) => void;
  onProgress: (progress: GenerationProgress) => void;
  onError: (error: string) => void;
}

const cardVariants = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  exit: { opacity: 0, y: -20 }
};

const focusAreaOptions = [
  { id: 'digital_literacy', label: 'Digital Literacy', icon: '💻' },
  { id: 'industry_skill', label: 'Industry Skills', icon: '🏭' },
  { id: 'position_skill', label: 'Position Skills', icon: '🎯' },
  { id: 'innovation', label: 'Innovation', icon: '💡' },
  { id: 'learning_potential', label: 'Learning Potential', icon: '🧠' }
];

const companySizeOptions = [
  { value: 'startup', label: 'Startup (1-10 employees)' },
  { value: 'small', label: 'Small (11-50 employees)' },
  { value: 'medium', label: 'Medium (51-200 employees)' },
  { value: 'large', label: 'Large (201-1000 employees)' },
  { value: 'enterprise', label: 'Enterprise (1000+ employees)' }
];

const difficultyOptions = [
  { value: 'easy', label: 'Easy - Entry Level', description: 'Basic knowledge required' },
  { value: 'medium', label: 'Medium - Intermediate', description: 'Some experience required' },
  { value: 'hard', label: 'Hard - Advanced', description: 'Extensive experience required' },
  { value: 'expert', label: 'Expert - Senior Level', description: 'Deep expertise required' }
];

const industryOptions = [
  'Technology', 'Healthcare', 'Finance', 'Education', 'Manufacturing',
  'Retail', 'Consulting', 'Media', 'Real Estate', 'Transportation',
  'Energy', 'Government', 'Non-profit', 'Other'
];

export const AIQuestionnaireForm: React.FC<AIQuestionnaireFormProps> = ({
  onGenerate,
  onProgress,
  onError
}) => {
  const t = useTranslations();
  const [isGenerating, setIsGenerating] = useState(false);
  const [currentProgress, setCurrentProgress] = useState<GenerationProgress | null>(null);
  const [estimatedDuration, setEstimatedDuration] = useState(0);

  const { 
    register, 
    control, 
    handleSubmit, 
    watch,
    setValue,
    formState: { errors, isValid } 
  } = useForm<FormData>({
    resolver: zodResolver(questionnaireSchema),
    defaultValues: {
      position_type: '',
      company_size: '',
      industry: '',
      question_count: 20,
      difficulty_level: 'medium',
      focus_areas: ['digital_literacy', 'position_skill'],
      include_behavioral: true,
      include_technical: true,
      include_situational: true,
      language: 'en',
      additional_requirements: ''
    },
    mode: 'onChange'
  });

  const watchQuestionCount = watch('question_count');
  const watchFocusAreas = watch('focus_areas');

  // Calculate estimated duration based on question count and complexity
  useEffect(() => {
    const baseTimePerQuestion = 2; // 2 minutes per question
    const complexityMultiplier = watchFocusAreas.length * 0.1 + 1;
    const estimated = Math.round(watchQuestionCount * baseTimePerQuestion * complexityMultiplier);
    setEstimatedDuration(estimated);
  }, [watchQuestionCount, watchFocusAreas]);

  // Poll for generation progress
  useEffect(() => {
    let intervalId: NodeJS.Timeout;

    if (isGenerating && currentProgress && currentProgress.status !== GenerationStatus.COMPLETED && currentProgress.status !== GenerationStatus.FAILED) {
      intervalId = setInterval(async () => {
        try {
          const progress = await aiQuestionnaireService.getGenerationProgress(currentProgress.generation_id);
          setCurrentProgress(progress);
          onProgress(progress);

          if (progress.status === GenerationStatus.COMPLETED) {
            setIsGenerating(false);
          } else if (progress.status === GenerationStatus.FAILED) {
            setIsGenerating(false);
            onError(progress.error_message || 'Generation failed');
          }
        } catch (error) {
          console.error('Failed to fetch progress:', error);
          setIsGenerating(false);
          onError('Failed to check generation progress');
        }
      }, 2000); // Poll every 2 seconds
    }

    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [isGenerating, currentProgress, onProgress, onError]);

  const onSubmit = async (data: FormData) => {
    try {
      setIsGenerating(true);
      
      const request: QuestionnaireGenerateRequest = {
        ...data,
        question_count: Number(data.question_count)
      };

      const response = await aiQuestionnaireService.generateQuestionnaire(request);
      
      // Set initial progress
      setCurrentProgress({
        generation_id: response.generation_metadata.generation_id,
        status: GenerationStatus.PENDING,
        progress_percentage: 0,
        current_step: 'Initializing...',
        estimated_remaining_time: estimatedDuration * 60, // Convert to seconds
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

      onGenerate(response);
    } catch (error: any) {
      setIsGenerating(false);
      const errorMessage = error?.detail || error?.message || 'Failed to generate questionnaire';
      onError(errorMessage);
    }
  };

  const handleFocusAreaToggle = (areaId: string, checked: boolean) => {
    const currentAreas = watchFocusAreas;
    if (checked) {
      setValue('focus_areas', [...currentAreas, areaId], { shouldValidate: true });
    } else {
      setValue('focus_areas', currentAreas.filter(area => area !== areaId), { shouldValidate: true });
    }
  };

  const getProgressIcon = (status: GenerationStatus) => {
    switch (status) {
      case GenerationStatus.COMPLETED:
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case GenerationStatus.FAILED:
        return <XCircle className="w-4 h-4 text-red-500" />;
      case GenerationStatus.PENDING:
      case GenerationStatus.ANALYZING_REQUIREMENTS:
      case GenerationStatus.GENERATING_QUESTIONS:
      case GenerationStatus.REVIEWING_QUALITY:
      case GenerationStatus.FINALIZING:
        return <Clock className="w-4 h-4 text-blue-500 animate-spin" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      <motion.div
        variants={cardVariants}
        initial="initial"
        animate="animate"
        exit="exit"
      >
        <Card>
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50">
            <CardTitle className="flex items-center gap-2">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <Bot className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              {t('aiQuestionnaire.generateTitle')}
            </CardTitle>
            <CardDescription>
              {t('aiQuestionnaire.generateDescription')}
            </CardDescription>
          </CardHeader>

          <CardContent className="pt-6">
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              
              {/* Basic Configuration */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Settings className="w-5 h-5 text-gray-600" />
                  <h3 className="font-semibold">{t('aiQuestionnaire.basicConfig')}</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="position_type">{t('aiQuestionnaire.positionType')} *</Label>
                    <Input
                      id="position_type"
                      {...register('position_type')}
                      placeholder={t('aiQuestionnaire.positionTypePlaceholder')}
                      className={cn(errors.position_type && 'border-red-500')}
                    />
                    {errors.position_type && (
                      <span className="text-sm text-red-500">{errors.position_type.message}</span>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="industry">{t('aiQuestionnaire.industry')} *</Label>
                    <Controller
                      name="industry"
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          placeholder={t('aiQuestionnaire.selectIndustry')}
                        >
                          {industryOptions.map(industry => (
                            <option key={industry} value={industry}>
                              {industry}
                            </option>
                          ))}
                        </Select>
                      )}
                    />
                    {errors.industry && (
                      <span className="text-sm text-red-500">{errors.industry.message}</span>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="company_size">{t('aiQuestionnaire.companySize')} *</Label>
                    <Controller
                      name="company_size"
                      control={control}
                      render={({ field }) => (
                        <Select
                          {...field}
                          placeholder={t('aiQuestionnaire.selectCompanySize')}
                        >
                          {companySizeOptions.map(option => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </Select>
                      )}
                    />
                    {errors.company_size && (
                      <span className="text-sm text-red-500">{errors.company_size.message}</span>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="language">{t('aiQuestionnaire.language')} *</Label>
                    <Controller
                      name="language"
                      control={control}
                      render={({ field }) => (
                        <Select {...field}>
                          <option value="en">English</option>
                          <option value="zh">中文</option>
                        </Select>
                      )}
                    />
                  </div>
                </div>
              </div>

              <Separator />

              {/* Question Configuration */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Target className="w-5 h-5 text-gray-600" />
                  <h3 className="font-semibold">{t('aiQuestionnaire.questionConfig')}</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="question_count">
                        {t('aiQuestionnaire.questionCount')}: {watchQuestionCount}
                      </Label>
                      <Controller
                        name="question_count"
                        control={control}
                        render={({ field: { onChange, value } }) => (
                          <Slider
                            value={[value]}
                            onValueChange={(values) => onChange(values[0])}
                            min={5}
                            max={50}
                            step={1}
                            className="w-full"
                          />
                        )}
                      />
                      <div className="flex justify-between text-sm text-gray-500">
                        <span>5</span>
                        <span>50</span>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>{t('aiQuestionnaire.difficultyLevel')} *</Label>
                      <Controller
                        name="difficulty_level"
                        control={control}
                        render={({ field }) => (
                          <div className="space-y-2">
                            {difficultyOptions.map(option => (
                              <label
                                key={option.value}
                                className={cn(
                                  "flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors",
                                  field.value === option.value
                                    ? "border-blue-500 bg-blue-50 dark:bg-blue-950/50"
                                    : "border-gray-200 hover:border-gray-300"
                                )}
                              >
                                <input
                                  type="radio"
                                  {...field}
                                  value={option.value}
                                  className="text-blue-600"
                                />
                                <div>
                                  <div className="font-medium">{option.label}</div>
                                  <div className="text-sm text-gray-500">{option.description}</div>
                                </div>
                              </label>
                            ))}
                          </div>
                        )}
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label>{t('aiQuestionnaire.focusAreas')} *</Label>
                      <div className="grid grid-cols-1 gap-2">
                        {focusAreaOptions.map(area => (
                          <label
                            key={area.id}
                            className="flex items-center space-x-3 p-3 rounded-lg border cursor-pointer transition-colors hover:border-gray-300"
                          >
                            <Checkbox
                              checked={watchFocusAreas.includes(area.id)}
                              onCheckedChange={(checked) => 
                                handleFocusAreaToggle(area.id, checked as boolean)
                              }
                            />
                            <span className="text-lg">{area.icon}</span>
                            <span className="font-medium">{area.label}</span>
                          </label>
                        ))}
                      </div>
                      {errors.focus_areas && (
                        <span className="text-sm text-red-500">{errors.focus_areas.message}</span>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Question Types */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Brain className="w-5 h-5 text-gray-600" />
                  <h3 className="font-semibold">{t('aiQuestionnaire.questionTypes')}</h3>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <label className="flex items-center space-x-3 p-4 rounded-lg border cursor-pointer transition-colors hover:border-gray-300">
                    <Controller
                      name="include_technical"
                      control={control}
                      render={({ field: { value, onChange } }) => (
                        <Checkbox
                          checked={value}
                          onCheckedChange={onChange}
                        />
                      )}
                    />
                    <div>
                      <div className="font-medium">{t('aiQuestionnaire.technicalQuestions')}</div>
                      <div className="text-sm text-gray-500">{t('aiQuestionnaire.technicalQuestionsDesc')}</div>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 p-4 rounded-lg border cursor-pointer transition-colors hover:border-gray-300">
                    <Controller
                      name="include_behavioral"
                      control={control}
                      render={({ field: { value, onChange } }) => (
                        <Checkbox
                          checked={value}
                          onCheckedChange={onChange}
                        />
                      )}
                    />
                    <div>
                      <div className="font-medium">{t('aiQuestionnaire.behavioralQuestions')}</div>
                      <div className="text-sm text-gray-500">{t('aiQuestionnaire.behavioralQuestionsDesc')}</div>
                    </div>
                  </label>

                  <label className="flex items-center space-x-3 p-4 rounded-lg border cursor-pointer transition-colors hover:border-gray-300">
                    <Controller
                      name="include_situational"
                      control={control}
                      render={({ field: { value, onChange } }) => (
                        <Checkbox
                          checked={value}
                          onCheckedChange={onChange}
                        />
                      )}
                    />
                    <div>
                      <div className="font-medium">{t('aiQuestionnaire.situationalQuestions')}</div>
                      <div className="text-sm text-gray-500">{t('aiQuestionnaire.situationalQuestionsDesc')}</div>
                    </div>
                  </label>
                </div>
              </div>

              <Separator />

              {/* Additional Requirements */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 mb-4">
                  <Lightbulb className="w-5 h-5 text-gray-600" />
                  <h3 className="font-semibold">{t('aiQuestionnaire.additionalRequirements')}</h3>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="additional_requirements">{t('aiQuestionnaire.specificRequirements')}</Label>
                  <Textarea
                    id="additional_requirements"
                    {...register('additional_requirements')}
                    placeholder={t('aiQuestionnaire.specificRequirementsPlaceholder')}
                    rows={3}
                    className="resize-none"
                  />
                </div>
              </div>

              {/* Estimated Duration */}
              <Alert>
                <Clock className="w-4 h-4" />
                <AlertDescription>
                  {t('aiQuestionnaire.estimatedDuration')}: <strong>{estimatedDuration} minutes</strong>
                </AlertDescription>
              </Alert>

              {/* Generation Progress */}
              {currentProgress && (
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {getProgressIcon(currentProgress.status)}
                        <span className="font-medium">{currentProgress.current_step}</span>
                      </div>
                      <Badge variant="outline">
                        {currentProgress.progress_percentage}%
                      </Badge>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${currentProgress.progress_percentage}%` }}
                      />
                    </div>
                    {currentProgress.estimated_remaining_time > 0 && (
                      <div className="text-sm text-gray-500 mt-2">
                        {t('aiQuestionnaire.estimatedRemaining')}: {Math.ceil(currentProgress.estimated_remaining_time / 60)} minutes
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Submit Button */}
              <div className="flex justify-end">
                <Button
                  type="submit"
                  size="lg"
                  disabled={!isValid || isGenerating}
                  className="min-w-[200px]"
                >
                  {isGenerating ? (
                    <>
                      <Clock className="w-4 h-4 mr-2 animate-spin" />
                      {t('aiQuestionnaire.generating')}
                    </>
                  ) : (
                    <>
                      <Bot className="w-4 h-4 mr-2" />
                      {t('aiQuestionnaire.generateButton')}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
};