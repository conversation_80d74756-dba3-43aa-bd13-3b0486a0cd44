/**
 * Questionnaire types for AI-generated assessment questionnaires
 * Based on the technical specification requirements
 */

import { SnowflakeID } from './api';

// Questionnaire generation request types
export interface QuestionnaireGenerateRequest {
  position_type: string;
  company_size: string;
  industry: string;
  question_count: number;
  difficulty_level: string;
  focus_areas: string[];
  include_behavioral: boolean;
  include_technical: boolean;
  include_situational: boolean;
  language: string;
  additional_requirements?: string;
}

export interface QuestionnaireGenerateResponse {
  questionnaire_id: SnowflakeID;
  title: string;
  description: string;
  questions: GeneratedQuestion[];
  estimated_duration: number;
  generation_metadata: {
    generation_time: number;
    ai_model_used: string;
    generation_id: string;
    quality_score: number;
  };
  created_at: string;
}

// Question types
export interface GeneratedQuestion {
  id: SnowflakeID;
  question_text: string;
  question_type: QuestionType;
  category: QuestionCategory;
  difficulty: DifficultyLevel;
  estimated_time: number;
  options?: QuestionOption[];
  evaluation_criteria: string[];
  ai_rationale: string;
}

export interface QuestionOption {
  id: string;
  text: string;
  is_correct?: boolean;
  score?: number;
}

// Enums
export enum QuestionType {
  MULTIPLE_CHOICE = 'multiple_choice',
  SINGLE_CHOICE = 'single_choice',
  TEXT_SHORT = 'text_short',
  TEXT_LONG = 'text_long',
  CODE = 'code',
  SCENARIO = 'scenario',
  RATING_SCALE = 'rating_scale'
}

export enum QuestionCategory {
  DIGITAL_LITERACY = 'digital_literacy',
  INDUSTRY_SKILL = 'industry_skill', 
  POSITION_SKILL = 'position_skill',
  INNOVATION = 'innovation',
  LEARNING_POTENTIAL = 'learning_potential',
  BEHAVIORAL = 'behavioral',
  TECHNICAL = 'technical',
  SITUATIONAL = 'situational'
}

export enum DifficultyLevel {
  EASY = 'easy',
  MEDIUM = 'medium',
  HARD = 'hard',
  EXPERT = 'expert'
}

// Template types
export interface QuestionnaireTemplate {
  id: SnowflakeID;
  name: string;
  description: string;
  position_types: string[];
  industries: string[];
  default_settings: Partial<QuestionnaireGenerateRequest>;
  is_system: boolean;
  created_at: string;
  updated_at: string;
}

export interface TemplateListResponse {
  items: QuestionnaireTemplate[];
  total: number;
  skip: number;
  limit: number;
}

// Generation progress types
export interface GenerationProgress {
  generation_id: string;
  status: GenerationStatus;
  progress_percentage: number;
  current_step: string;
  estimated_remaining_time: number;
  error_message?: string;
  created_at: string;
  updated_at: string;
}

export enum GenerationStatus {
  PENDING = 'pending',
  ANALYZING_REQUIREMENTS = 'analyzing_requirements',
  GENERATING_QUESTIONS = 'generating_questions',
  REVIEWING_QUALITY = 'reviewing_quality',
  FINALIZING = 'finalizing',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

// Questionnaire entity (completed questionnaire)
export interface Questionnaire {
  id: SnowflakeID;
  title: string;
  description: string;
  position_type: string;
  industry: string;
  difficulty_level: string;
  estimated_duration: number;
  question_count: number;
  questions: Question[];
  is_active: boolean;
  is_ai_generated: boolean;
  generation_metadata?: {
    ai_model_used: string;
    generation_id: string;
    quality_score: number;
    created_from_template?: SnowflakeID;
  };
  created_by: SnowflakeID;
  created_at: string;
  updated_at: string;
}

export interface Question {
  id: SnowflakeID;
  questionnaire_id: SnowflakeID;
  question_text: string;
  question_type: QuestionType;
  category: QuestionCategory;
  difficulty: DifficultyLevel;
  order_index: number;
  estimated_time: number;
  options?: QuestionOption[];
  evaluation_criteria: string[];
  scoring_weights: {
    digital_literacy: number;
    industry_skill: number;
    position_skill: number;
    innovation: number;
    learning_potential: number;
  };
  created_at: string;
  updated_at: string;
}

// List response types
export interface QuestionnaireListResponse {
  items: Questionnaire[];
  total: number;
  skip: number;
  limit: number;
}

// Search and filter types
export interface QuestionnaireSearchParams {
  search?: string;
  position_type?: string;
  industry?: string;
  difficulty_level?: string;
  is_ai_generated?: boolean;
  created_after?: string;
  created_before?: string;
  skip?: number;
  limit?: number;
}