/**
 * Evaluation and Assessment types
 * Based on the technical specification for evaluation report display
 */

import { SnowflakeID } from './api';

// Five-dimensional evaluation scores
export interface EvaluationScores {
  digital_literacy: number;      // 数字素养 (20%)
  industry_skill: number;        // 行业技能 (25%) 
  position_skill: number;        // 岗位技能 (30%)
  innovation: number;           // 创新能力 (15%)
  learning_potential: number;   // 学习潜力 (10%)
}

// Evaluation dimensions with detailed breakdown
export interface EvaluationDimension {
  name: string;
  score: number;
  weight: number;
  weighted_score: number;
  max_score: number;
  percentage: number;
  sub_dimensions: SubDimension[];
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
}

export interface SubDimension {
  name: string;
  score: number;
  max_score: number;
  questions_count: number;
  performance_level: 'excellent' | 'good' | 'average' | 'below_average' | 'poor';
}

// Complete evaluation report
export interface EvaluationReport {
  id: SnowflakeID;
  candidate_id: SnowflakeID;
  candidate_name: string;
  questionnaire_id: SnowflakeID;
  questionnaire_title: string;
  position_title: string;
  
  // Overall scores
  dci_score: number;              // Digital Competency Index (0-100)
  jfs_score: number;              // Job Fitness Score (0-100)
  overall_score: number;          // Weighted average (0-100)
  percentile_rank: number;        // Percentile among all candidates (0-100)
  
  // Five-dimensional scores
  evaluation_scores: EvaluationScores;
  evaluation_dimensions: EvaluationDimension[];
  
  // Assessment metadata
  total_questions: number;
  answered_questions: number;
  completion_rate: number;
  time_spent_minutes: number;
  assessment_date: string;
  
  // Performance analysis
  performance_summary: {
    overall_level: 'excellent' | 'good' | 'average' | 'below_average' | 'poor';
    key_strengths: string[];
    key_weaknesses: string[];
    improvement_areas: string[];
    recommendations: string[];
  };
  
  // Comparison data
  industry_average: EvaluationScores;
  position_average: EvaluationScores;
  
  // Question-level details
  question_responses: QuestionResponse[];
  
  // AI insights
  ai_insights: {
    automated_summary: string;
    predicted_job_success: number;
    risk_factors: string[];
    growth_potential: 'high' | 'medium' | 'low';
    cultural_fit_score: number;
  };
  
  // Metadata
  generated_at: string;
  generated_by: SnowflakeID;
  report_version: string;
}

// Individual question response
export interface QuestionResponse {
  question_id: SnowflakeID;
  question_text: string;
  question_category: string;
  question_difficulty: string;
  candidate_answer: string | string[] | number;
  correct_answer?: string | string[] | number;
  is_correct?: boolean;
  score_earned: number;
  max_score: number;
  time_spent_seconds: number;
  ai_analysis?: {
    answer_quality: 'excellent' | 'good' | 'fair' | 'poor';
    reasoning_score: number;
    creativity_score: number;
    technical_accuracy: number;
    communication_clarity: number;
    suggestions: string[];
  };
}

// Comparison view data
export interface EvaluationComparison {
  candidates: CandidateEvaluationSummary[];
  comparison_metadata: {
    position_title: string;
    questionnaire_title: string;
    comparison_date: string;
    industry_benchmarks: EvaluationScores;
    position_benchmarks: EvaluationScores;
  };
}

export interface CandidateEvaluationSummary {
  candidate_id: SnowflakeID;
  candidate_name: string;
  overall_score: number;
  dci_score: number;
  jfs_score: number;
  evaluation_scores: EvaluationScores;
  percentile_rank: number;
  assessment_date: string;
  status: 'completed' | 'in_progress' | 'not_started';
}

// Report customization options
export interface ReportOptions {
  include_question_details: boolean;
  include_ai_insights: boolean;
  include_comparison_data: boolean;
  include_recommendations: boolean;
  format: 'detailed' | 'summary' | 'executive';
  language: 'en' | 'zh';
}

// Report generation request
export interface GenerateReportRequest {
  candidate_id: SnowflakeID;
  questionnaire_id?: SnowflakeID;
  include_benchmarks: boolean;
  options: ReportOptions;
}

// Radar chart data format
export interface RadarChartData {
  labels: string[];
  datasets: RadarDataset[];
}

export interface RadarDataset {
  label: string;
  data: number[];
  borderColor: string;
  backgroundColor: string;
  pointBackgroundColor: string;
  pointBorderColor: string;
  pointHoverBackgroundColor: string;
  pointHoverBorderColor: string;
}

// Performance trends
export interface PerformanceTrend {
  date: string;
  overall_score: number;
  evaluation_scores: EvaluationScores;
}

// Assessment history
export interface AssessmentHistory {
  assessments: AssessmentRecord[];
  performance_trends: PerformanceTrend[];
  improvement_metrics: {
    score_improvement: number;
    strongest_growth_area: string;
    consistency_score: number;
  };
}

export interface AssessmentRecord {
  id: SnowflakeID;
  questionnaire_title: string;
  position_title: string;
  overall_score: number;
  dci_score: number;
  jfs_score: number;
  assessment_date: string;
  time_spent_minutes: number;
  status: 'completed' | 'incomplete' | 'expired';
}