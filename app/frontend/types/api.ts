/**
 * API-specific types and utilities
 * Provides SnowflakeID type that handles the discrepancy between 
 * backend Snowflake IDs (64-bit integers) and frontend handling (strings)
 */

// SnowflakeID: Backend uses BigInteger, but JSON serializes to string
// Accept both int and string input, always output as string for consistency
export type SnowflakeID = string;

// Utility function to ensure SnowflakeID is always string
export const toSnowflakeID = (id: number | string | undefined | null): SnowflakeID | undefined => {
  if (id === undefined || id === null) return undefined;
  return String(id);
};

// Utility function to validate SnowflakeID format
export const isValidSnowflakeID = (id: string): boolean => {
  // Basic validation: should be a string representing a positive integer
  return /^\d+$/.test(id) && parseInt(id) > 0;
};

// Enhanced date utilities for proper ISO string handling
export const formatDateToISO = (date: Date): string => {
  return date.toISOString();
};

export const parseDateFromISO = (isoString: string): Date => {
  return new Date(isoString);
};

// Type guard for API error responses
export const isApiError = (error: any): error is { error_code: string; detail: string } => {
  return error && typeof error.error_code === 'string' && typeof error.detail === 'string';
};

// Generic API response wrapper
export interface ApiResponse<T> {
  data?: T;
  error?: {
    error_code: string;
    detail: string;
    details?: any;
  };
}

// List response format used throughout the API
export interface ApiListResponse<T> {
  items: T[];
  total: number;
  skip: number;
  limit: number;
}

// Common request parameters for list endpoints
export interface ApiListParams {
  skip?: number;
  limit?: number;
  search?: string;
}