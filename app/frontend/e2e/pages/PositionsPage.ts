import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../utils/base';
import { TestPosition } from '../utils/test-data';

/**
 * Positions Page Object - Job positions management interface
 */
export class PositionsPage extends BasePage {
  // Page header
  readonly pageTitle: Locator;
  readonly addPositionBtn: Locator;
  readonly importJobDescBtn: Locator;
  readonly exportBtn: Locator;

  // Search and filters
  readonly searchInput: Locator;
  readonly departmentFilter: Locator;
  readonly experienceLevelFilter: Locator;
  readonly employmentTypeFilter: Locator;
  readonly locationFilter: Locator;
  readonly salaryRangeFilter: Locator;
  readonly clearFiltersBtn: Locator;

  // Positions table/list
  readonly positionsList: Locator;
  readonly positionRows: Locator;
  readonly emptyState: Locator;
  readonly loadingSpinner: Locator;

  // Position cards (if using card layout)
  readonly positionCards: Locator;

  // Pagination
  readonly pagination: Locator;
  readonly prevPageBtn: Locator;
  readonly nextPageBtn: Locator;
  readonly pageSizeSelect: Locator;

  // Bulk actions
  readonly selectAllCheckbox: Locator;
  readonly bulkActionsMenu: Locator;
  readonly bulkDeleteBtn: Locator;
  readonly bulkExportBtn: Locator;
  readonly bulkStatusBtn: Locator;

  // Modal dialogs
  readonly positionModal: Locator;
  readonly deleteConfirmModal: Locator;
  readonly jobDescriptionModal: Locator;

  // Job description upload
  readonly jobDescUploadArea: Locator;
  readonly fileInput: Locator;
  readonly uploadBtn: Locator;

  constructor(page: Page) {
    super(page, '/dashboard/positions');
    
    // Header elements
    this.pageTitle = page.locator('h1, [data-testid="page-title"]');
    this.addPositionBtn = page.locator('[data-testid="add-position-btn"]');
    this.importJobDescBtn = page.locator('[data-testid="import-job-desc-btn"]');
    this.exportBtn = page.locator('[data-testid="export-btn"]');

    // Search and filters
    this.searchInput = page.locator('[data-testid="search-input"], input[placeholder*="search" i]');
    this.departmentFilter = page.locator('[data-testid="department-filter"]');
    this.experienceLevelFilter = page.locator('[data-testid="experience-level-filter"]');
    this.employmentTypeFilter = page.locator('[data-testid="employment-type-filter"]');
    this.locationFilter = page.locator('[data-testid="location-filter"]');
    this.salaryRangeFilter = page.locator('[data-testid="salary-range-filter"]');
    this.clearFiltersBtn = page.locator('[data-testid="clear-filters-btn"]');

    // Table/list
    this.positionsList = page.locator('[data-testid="positions-list"], .positions-table, table');
    this.positionRows = page.locator('[data-testid="position-row"], tbody tr');
    this.positionCards = page.locator('[data-testid="position-card"]');
    this.emptyState = page.locator('[data-testid="empty-state"]');
    this.loadingSpinner = page.locator('[data-testid="loading-positions"]');

    // Pagination
    this.pagination = page.locator('[data-testid="pagination"]');
    this.prevPageBtn = page.locator('[data-testid="prev-page-btn"]');
    this.nextPageBtn = page.locator('[data-testid="next-page-btn"]');
    this.pageSizeSelect = page.locator('[data-testid="page-size-select"]');

    // Bulk actions
    this.selectAllCheckbox = page.locator('[data-testid="select-all-checkbox"]');
    this.bulkActionsMenu = page.locator('[data-testid="bulk-actions-menu"]');
    this.bulkDeleteBtn = page.locator('[data-testid="bulk-delete-btn"]');
    this.bulkExportBtn = page.locator('[data-testid="bulk-export-btn"]');
    this.bulkStatusBtn = page.locator('[data-testid="bulk-status-btn"]');

    // Modals
    this.positionModal = page.locator('[data-testid="position-modal"]');
    this.deleteConfirmModal = page.locator('[data-testid="delete-confirm-modal"]');
    this.jobDescriptionModal = page.locator('[data-testid="job-description-modal"]');

    // Job description upload
    this.jobDescUploadArea = page.locator('[data-testid="job-desc-upload-area"]');
    this.fileInput = page.locator('input[type="file"]');
    this.uploadBtn = page.locator('[data-testid="upload-btn"]');
  }

  /**
   * Navigate to positions page and verify it loads
   */
  async navigate(): Promise<void> {
    await this.goto();
    await this.verifyPositionsPageLoaded();
  }

  /**
   * Verify positions page is properly loaded
   */
  async verifyPositionsPageLoaded(): Promise<void> {
    await this.waitForElement(this.pageTitle);
    await expect(this.pageTitle).toContainText(/positions|jobs/i);
    await this.waitForLoadingComplete();
  }

  /**
   * Search for positions
   */
  async searchPositions(query: string): Promise<void> {
    await this.fillInput(this.searchInput, query);
    await this.page.keyboard.press('Enter');
    await this.waitForSearchResults();
  }

  /**
   * Wait for search results to load
   */
  async waitForSearchResults(): Promise<void> {
    await this.waitForElementHidden(this.loadingSpinner);
    await this.page.waitForTimeout(500); // Allow for debounced search
  }

  /**
   * Apply department filter
   */
  async filterByDepartment(department: string): Promise<void> {
    await this.clickElement(this.departmentFilter);
    const departmentOption = this.page.locator(`[data-testid="dept-${department}"], [value="${department}"]`);
    await this.clickElement(departmentOption);
    await this.page.keyboard.press('Escape');
    await this.waitForSearchResults();
  }

  /**
   * Apply experience level filter
   */
  async filterByExperienceLevel(level: 'entry' | 'mid' | 'senior' | 'lead'): Promise<void> {
    await this.clickElement(this.experienceLevelFilter);
    const levelOption = this.page.locator(`[data-testid="level-${level}"], [value="${level}"]`);
    await this.clickElement(levelOption);
    await this.page.keyboard.press('Escape');
    await this.waitForSearchResults();
  }

  /**
   * Apply employment type filter
   */
  async filterByEmploymentType(type: 'full_time' | 'part_time' | 'contract'): Promise<void> {
    await this.clickElement(this.employmentTypeFilter);
    const typeOption = this.page.locator(`[data-testid="type-${type}"], [value="${type}"]`);
    await this.clickElement(typeOption);
    await this.page.keyboard.press('Escape');
    await this.waitForSearchResults();
  }

  /**
   * Apply salary range filter
   */
  async filterBySalaryRange(minSalary: number, maxSalary?: number): Promise<void> {
    await this.clickElement(this.salaryRangeFilter);
    
    const minInput = this.page.locator('[data-testid="min-salary-input"]');
    await this.fillInput(minInput, minSalary.toString());
    
    if (maxSalary) {
      const maxInput = this.page.locator('[data-testid="max-salary-input"]');
      await this.fillInput(maxInput, maxSalary.toString());
    }
    
    await this.page.keyboard.press('Escape');
    await this.waitForSearchResults();
  }

  /**
   * Clear all active filters
   */
  async clearFilters(): Promise<void> {
    await this.clickElement(this.clearFiltersBtn);
    await this.waitForSearchResults();
  }

  /**
   * Get number of positions in current list
   */
  async getPositionCount(): Promise<number> {
    await this.waitForSearchResults();
    return await this.positionRows.count();
  }

  /**
   * Click on a specific position
   */
  async clickPosition(positionTitle: string): Promise<void> {
    const positionRow = this.positionRows.filter({ hasText: positionTitle }).first();
    await this.clickElement(positionRow);
    await this.page.waitForURL(/.*\/positions\/\d+/);
  }

  /**
   * Open add position modal
   */
  async openAddPositionModal(): Promise<void> {
    await this.clickElement(this.addPositionBtn);
    await this.waitForElement(this.positionModal);
  }

  /**
   * Fill position form in modal
   */
  async fillPositionForm(position: TestPosition): Promise<void> {
    await this.waitForElement(this.positionModal);
    
    // Basic information
    await this.fillInput(this.positionModal.locator('[data-testid="title-input"]'), position.title);
    await this.fillInput(this.positionModal.locator('[data-testid="department-input"]'), position.department);
    await this.fillInput(this.positionModal.locator('[data-testid="location-input"]'), position.location);
    
    // Employment details
    const employmentTypeSelect = this.positionModal.locator('[data-testid="employment-type-select"]');
    await this.selectOption(employmentTypeSelect, position.employment_type);
    
    const experienceLevelSelect = this.positionModal.locator('[data-testid="experience-level-select"]');
    await this.selectOption(experienceLevelSelect, position.experience_level);
    
    // Salary range
    if (position.salary_min) {
      await this.fillInput(
        this.positionModal.locator('[data-testid="salary-min-input"]'),
        position.salary_min.toString()
      );
    }
    
    if (position.salary_max) {
      await this.fillInput(
        this.positionModal.locator('[data-testid="salary-max-input"]'),
        position.salary_max.toString()
      );
    }
    
    // Description
    await this.fillInput(
      this.positionModal.locator('[data-testid="description-textarea"]'),
      position.description
    );
    
    // Requirements
    if (position.requirements.length > 0) {
      const requirementsInput = this.positionModal.locator('[data-testid="requirements-textarea"]');
      await this.fillInput(requirementsInput, position.requirements.join('\n• '));
    }
    
    // Benefits
    if (position.benefits && position.benefits.length > 0) {
      const benefitsInput = this.positionModal.locator('[data-testid="benefits-textarea"]');
      await this.fillInput(benefitsInput, position.benefits.join('\n• '));
    }
    
    // Remote work allowed
    if (position.remote_work_allowed) {
      const remoteWorkCheckbox = this.positionModal.locator('[data-testid="remote-work-checkbox"]');
      await this.clickElement(remoteWorkCheckbox);
    }
  }

  /**
   * Submit position form
   */
  async submitPositionForm(): Promise<void> {
    const submitBtn = this.positionModal.locator('[data-testid="submit-btn"]');
    await this.clickElement(submitBtn);
    
    // Wait for either success message or form validation
    await Promise.race([
      this.waitForToast('Position created successfully'),
      this.page.waitForSelector('[data-testid="form-errors"]', { timeout: 5000 }).catch(() => {})
    ]);
  }

  /**
   * Create a new position end-to-end
   */
  async createPosition(position: TestPosition): Promise<void> {
    await this.openAddPositionModal();
    await this.fillPositionForm(position);
    await this.submitPositionForm();
    await this.waitForElementHidden(this.positionModal);
    await this.waitForSearchResults(); // Refresh list
  }

  /**
   * Upload job description for OCR parsing
   */
  async uploadJobDescription(filePath: string): Promise<void> {
    await this.clickElement(this.importJobDescBtn);
    await this.waitForElement(this.jobDescriptionModal);
    
    // Upload file
    const fileInput = this.jobDescriptionModal.locator('input[type="file"]');
    await this.uploadFile(fileInput, filePath);
    
    // Wait for OCR processing
    await this.page.waitForSelector('[data-testid="ocr-processing"]', { timeout: 5000 });
    await this.waitForElementHidden(this.page.locator('[data-testid="ocr-processing"]'), 30000);
    
    // Verify parsed data appears
    const parsedData = this.jobDescriptionModal.locator('[data-testid="parsed-job-data"]');
    await this.waitForElement(parsedData);
    
    // Submit parsed job description
    const confirmBtn = this.jobDescriptionModal.locator('[data-testid="confirm-import-btn"]');
    await this.clickElement(confirmBtn);
    
    await this.waitForToast('Job description imported successfully');
    await this.waitForElementHidden(this.jobDescriptionModal);
  }

  /**
   * Delete a position
   */
  async deletePosition(positionTitle: string): Promise<void> {
    const positionRow = this.positionRows.filter({ hasText: positionTitle }).first();
    const deleteBtn = positionRow.locator('[data-testid="delete-position-btn"]');
    await this.clickElement(deleteBtn);
    
    // Confirm deletion
    await this.waitForElement(this.deleteConfirmModal);
    const confirmBtn = this.deleteConfirmModal.locator('[data-testid="confirm-delete-btn"]');
    await this.clickElement(confirmBtn);
    
    // Wait for deletion to complete
    await this.waitForToast('Position deleted successfully');
    await this.waitForElementHidden(this.deleteConfirmModal);
    await this.waitForSearchResults();
  }

  /**
   * Navigate to position matching page
   */
  async goToMatching(positionTitle: string): Promise<void> {
    const positionRow = this.positionRows.filter({ hasText: positionTitle }).first();
    const matchBtn = positionRow.locator('[data-testid="match-candidates-btn"]');
    await this.clickElement(matchBtn);
    await this.page.waitForURL(/.*\/positions\/\d+\/match/);
  }

  /**
   * Change position status
   */
  async changePositionStatus(positionTitle: string, status: 'active' | 'paused' | 'closed'): Promise<void> {
    const positionRow = this.positionRows.filter({ hasText: positionTitle }).first();
    const statusBtn = positionRow.locator('[data-testid="status-dropdown"]');
    await this.clickElement(statusBtn);
    
    const statusOption = this.page.locator(`[data-testid="status-${status}"]`);
    await this.clickElement(statusOption);
    
    await this.waitForToast(`Position status updated to ${status}`);
  }

  /**
   * Select positions for bulk actions
   */
  async selectPositions(positionTitles: string[]): Promise<void> {
    for (const title of positionTitles) {
      const positionRow = this.positionRows.filter({ hasText: title }).first();
      const checkbox = positionRow.locator('input[type="checkbox"]');
      await this.clickElement(checkbox);
    }
  }

  /**
   * Perform bulk status change
   */
  async bulkChangeStatus(positionTitles: string[], status: 'active' | 'paused' | 'closed'): Promise<void> {
    await this.selectPositions(positionTitles);
    await this.clickElement(this.bulkActionsMenu);
    await this.clickElement(this.bulkStatusBtn);
    
    const statusOption = this.page.locator(`[data-testid="bulk-status-${status}"]`);
    await this.clickElement(statusOption);
    
    await this.waitForToast(`${positionTitles.length} positions updated to ${status}`);
    await this.waitForSearchResults();
  }

  /**
   * Verify position appears in list
   */
  async verifyPositionExists(positionTitle: string): Promise<void> {
    const positionRow = this.positionRows.filter({ hasText: positionTitle }).first();
    await expect(positionRow).toBeVisible();
  }

  /**
   * Verify position does not appear in list
   */
  async verifyPositionNotExists(positionTitle: string): Promise<void> {
    const positionRow = this.positionRows.filter({ hasText: positionTitle }).first();
    await expect(positionRow).not.toBeVisible();
  }

  /**
   * Get position status
   */
  async getPositionStatus(positionTitle: string): Promise<string> {
    const positionRow = this.positionRows.filter({ hasText: positionTitle }).first();
    const statusBadge = positionRow.locator('[data-testid="status-badge"]');
    return await this.getElementText(statusBadge);
  }

  /**
   * View position details
   */
  async viewPositionDetails(positionTitle: string): Promise<void> {
    const positionRow = this.positionRows.filter({ hasText: positionTitle }).first();
    const viewBtn = positionRow.locator('[data-testid="view-position-btn"]');
    await this.clickElement(viewBtn);
    await this.page.waitForURL(/.*\/positions\/\d+/);
  }

  /**
   * Export positions
   */
  async exportPositions(format: 'csv' | 'excel' = 'csv'): Promise<void> {
    await this.clickElement(this.exportBtn);
    
    const formatBtn = this.page.locator(`[data-testid="export-${format}-btn"]`);
    await this.clickElement(formatBtn);
    
    // Wait for download to start
    const downloadPromise = this.page.waitForEvent('download');
    await downloadPromise;
    
    await this.waitForToast('Export completed successfully');
  }
}