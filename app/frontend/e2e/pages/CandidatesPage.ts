import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../utils/base';
import { TestCandidate } from '../utils/test-data';

/**
 * Candidates Page Object - Candidate management interface
 */
export class CandidatesPage extends BasePage {
  // Page header
  readonly pageTitle: Locator;
  readonly addCandidateBtn: Locator;
  readonly importCandidatesBtn: Locator;
  readonly exportBtn: Locator;

  // Search and filters
  readonly searchInput: Locator;
  readonly skillsFilter: Locator;
  readonly experienceFilter: Locator;
  readonly locationFilter: Locator;
  readonly clearFiltersBtn: Locator;

  // Candidates table/list
  readonly candidatesList: Locator;
  readonly candidateRows: Locator;
  readonly emptyState: Locator;
  readonly loadingSpinner: Locator;

  // Pagination
  readonly pagination: Locator;
  readonly prevPageBtn: Locator;
  readonly nextPageBtn: Locator;
  readonly pageSizeSelect: Locator;

  // Bulk actions
  readonly selectAllCheckbox: Locator;
  readonly bulkActionsMenu: Locator;
  readonly bulkDeleteBtn: Locator;
  readonly bulkExportBtn: Locator;

  // Modal dialogs
  readonly candidateModal: Locator;
  readonly deleteConfirmModal: Locator;
  readonly resumeUploadModal: Locator;

  constructor(page: Page) {
    super(page, '/dashboard/candidates');
    
    // Header elements
    this.pageTitle = page.locator('h1, [data-testid="page-title"]');
    this.addCandidateBtn = page.locator('[data-testid="add-candidate-btn"]');
    this.importCandidatesBtn = page.locator('[data-testid="import-candidates-btn"]');
    this.exportBtn = page.locator('[data-testid="export-btn"]');

    // Search and filters
    this.searchInput = page.locator('[data-testid="search-input"], input[placeholder*="search" i]');
    this.skillsFilter = page.locator('[data-testid="skills-filter"]');
    this.experienceFilter = page.locator('[data-testid="experience-filter"]');
    this.locationFilter = page.locator('[data-testid="location-filter"]');
    this.clearFiltersBtn = page.locator('[data-testid="clear-filters-btn"]');

    // Table/list
    this.candidatesList = page.locator('[data-testid="candidates-list"], .candidates-table, table');
    this.candidateRows = page.locator('[data-testid="candidate-row"], tbody tr');
    this.emptyState = page.locator('[data-testid="empty-state"]');
    this.loadingSpinner = page.locator('[data-testid="loading-candidates"]');

    // Pagination
    this.pagination = page.locator('[data-testid="pagination"]');
    this.prevPageBtn = page.locator('[data-testid="prev-page-btn"]');
    this.nextPageBtn = page.locator('[data-testid="next-page-btn"]');
    this.pageSizeSelect = page.locator('[data-testid="page-size-select"]');

    // Bulk actions
    this.selectAllCheckbox = page.locator('[data-testid="select-all-checkbox"]');
    this.bulkActionsMenu = page.locator('[data-testid="bulk-actions-menu"]');
    this.bulkDeleteBtn = page.locator('[data-testid="bulk-delete-btn"]');
    this.bulkExportBtn = page.locator('[data-testid="bulk-export-btn"]');

    // Modals
    this.candidateModal = page.locator('[data-testid="candidate-modal"]');
    this.deleteConfirmModal = page.locator('[data-testid="delete-confirm-modal"]');
    this.resumeUploadModal = page.locator('[data-testid="resume-upload-modal"]');
  }

  /**
   * Navigate to candidates page and verify it loads
   */
  async navigate(): Promise<void> {
    await this.goto();
    await this.verifyCandidatesPageLoaded();
  }

  /**
   * Verify candidates page is properly loaded
   */
  async verifyCandidatesPageLoaded(): Promise<void> {
    await this.waitForElement(this.pageTitle);
    await expect(this.pageTitle).toContainText(/candidates/i);
    await this.waitForLoadingComplete();
  }

  /**
   * Search for candidates
   */
  async searchCandidates(query: string): Promise<void> {
    await this.fillInput(this.searchInput, query);
    await this.page.keyboard.press('Enter');
    await this.waitForSearchResults();
  }

  /**
   * Wait for search results to load
   */
  async waitForSearchResults(): Promise<void> {
    await this.waitForElementHidden(this.loadingSpinner);
    await this.page.waitForTimeout(500); // Allow for debounced search
  }

  /**
   * Apply skills filter
   */
  async filterBySkills(skills: string[]): Promise<void> {
    await this.clickElement(this.skillsFilter);
    
    for (const skill of skills) {
      const skillOption = this.page.locator(`[data-testid="skill-${skill}"], [value="${skill}"]`);
      await this.clickElement(skillOption);
    }
    
    // Close filter dropdown
    await this.page.keyboard.press('Escape');
    await this.waitForSearchResults();
  }

  /**
   * Apply experience filter
   */
  async filterByExperience(minYears: number, maxYears?: number): Promise<void> {
    await this.clickElement(this.experienceFilter);
    
    const minInput = this.page.locator('[data-testid="min-experience-input"]');
    await this.fillInput(minInput, minYears.toString());
    
    if (maxYears) {
      const maxInput = this.page.locator('[data-testid="max-experience-input"]');
      await this.fillInput(maxInput, maxYears.toString());
    }
    
    await this.page.keyboard.press('Escape');
    await this.waitForSearchResults();
  }

  /**
   * Clear all active filters
   */
  async clearFilters(): Promise<void> {
    await this.clickElement(this.clearFiltersBtn);
    await this.waitForSearchResults();
  }

  /**
   * Get number of candidates in current list
   */
  async getCandidateCount(): Promise<number> {
    await this.waitForSearchResults();
    return await this.candidateRows.count();
  }

  /**
   * Click on a specific candidate row
   */
  async clickCandidate(candidateEmail: string): Promise<void> {
    const candidateRow = this.candidateRows.filter({ hasText: candidateEmail }).first();
    await this.clickElement(candidateRow);
    await this.page.waitForURL(/.*\/candidates\/\d+/);
  }

  /**
   * Open add candidate modal
   */
  async openAddCandidateModal(): Promise<void> {
    await this.clickElement(this.addCandidateBtn);
    await this.waitForElement(this.candidateModal);
  }

  /**
   * Fill candidate form in modal
   */
  async fillCandidateForm(candidate: TestCandidate): Promise<void> {
    await this.waitForElement(this.candidateModal);
    
    // Basic information
    await this.fillInput(this.candidateModal.locator('[data-testid="email-input"]'), candidate.email);
    await this.fillInput(this.candidateModal.locator('[data-testid="full-name-input"]'), candidate.full_name);
    
    if (candidate.phone_number) {
      await this.fillInput(this.candidateModal.locator('[data-testid="phone-input"]'), candidate.phone_number);
    }
    
    if (candidate.location) {
      await this.fillInput(this.candidateModal.locator('[data-testid="location-input"]'), candidate.location);
    }

    // Skills
    if (candidate.skills.length > 0) {
      const skillsInput = this.candidateModal.locator('[data-testid="skills-input"]');
      await this.fillInput(skillsInput, candidate.skills.join(', '));
    }

    // Experience
    if (candidate.experience_years) {
      await this.fillInput(
        this.candidateModal.locator('[data-testid="experience-input"]'),
        candidate.experience_years.toString()
      );
    }

    // Education level
    if (candidate.education_level) {
      const educationSelect = this.candidateModal.locator('[data-testid="education-select"]');
      await this.selectOption(educationSelect, candidate.education_level);
    }

    // Current position
    if (candidate.current_position) {
      await this.fillInput(
        this.candidateModal.locator('[data-testid="current-position-input"]'),
        candidate.current_position
      );
    }

    // Salary expectations
    if (candidate.expected_salary_min) {
      await this.fillInput(
        this.candidateModal.locator('[data-testid="salary-min-input"]'),
        candidate.expected_salary_min.toString()
      );
    }
    
    if (candidate.expected_salary_max) {
      await this.fillInput(
        this.candidateModal.locator('[data-testid="salary-max-input"]'),
        candidate.expected_salary_max.toString()
      );
    }
  }

  /**
   * Submit candidate form
   */
  async submitCandidateForm(): Promise<void> {
    const submitBtn = this.candidateModal.locator('[data-testid="submit-btn"]');
    await this.clickElement(submitBtn);
    
    // Wait for either success message or form validation
    await Promise.race([
      this.waitForToast('Candidate created successfully'),
      this.page.waitForSelector('[data-testid="form-errors"]', { timeout: 5000 }).catch(() => {})
    ]);
  }

  /**
   * Create a new candidate end-to-end
   */
  async createCandidate(candidate: TestCandidate): Promise<void> {
    await this.openAddCandidateModal();
    await this.fillCandidateForm(candidate);
    await this.submitCandidateForm();
    await this.waitForElementHidden(this.candidateModal);
    await this.waitForSearchResults(); // Refresh list
  }

  /**
   * Upload resume for candidate
   */
  async uploadResume(candidateEmail: string, resumeFilePath: string): Promise<void> {
    // Find candidate row and click resume upload
    const candidateRow = this.candidateRows.filter({ hasText: candidateEmail }).first();
    const uploadBtn = candidateRow.locator('[data-testid="upload-resume-btn"]');
    await this.clickElement(uploadBtn);
    
    // Wait for upload modal
    await this.waitForElement(this.resumeUploadModal);
    
    // Upload file
    const fileInput = this.resumeUploadModal.locator('input[type="file"]');
    await this.uploadFile(fileInput, resumeFilePath);
    
    // Submit upload
    const submitUploadBtn = this.resumeUploadModal.locator('[data-testid="submit-upload-btn"]');
    await this.clickElement(submitUploadBtn);
    
    // Wait for upload completion
    await this.waitForToast('Resume uploaded successfully');
    await this.waitForElementHidden(this.resumeUploadModal);
  }

  /**
   * Delete a candidate
   */
  async deleteCandidate(candidateEmail: string): Promise<void> {
    const candidateRow = this.candidateRows.filter({ hasText: candidateEmail }).first();
    const deleteBtn = candidateRow.locator('[data-testid="delete-candidate-btn"]');
    await this.clickElement(deleteBtn);
    
    // Confirm deletion
    await this.waitForElement(this.deleteConfirmModal);
    const confirmBtn = this.deleteConfirmModal.locator('[data-testid="confirm-delete-btn"]');
    await this.clickElement(confirmBtn);
    
    // Wait for deletion to complete
    await this.waitForToast('Candidate deleted successfully');
    await this.waitForElementHidden(this.deleteConfirmModal);
    await this.waitForSearchResults();
  }

  /**
   * Select candidates for bulk actions
   */
  async selectCandidates(candidateEmails: string[]): Promise<void> {
    for (const email of candidateEmails) {
      const candidateRow = this.candidateRows.filter({ hasText: email }).first();
      const checkbox = candidateRow.locator('input[type="checkbox"]');
      await this.clickElement(checkbox);
    }
  }

  /**
   * Perform bulk delete
   */
  async bulkDeleteCandidates(candidateEmails: string[]): Promise<void> {
    await this.selectCandidates(candidateEmails);
    await this.clickElement(this.bulkActionsMenu);
    await this.clickElement(this.bulkDeleteBtn);
    
    // Confirm bulk deletion
    await this.waitForElement(this.deleteConfirmModal);
    const confirmBtn = this.deleteConfirmModal.locator('[data-testid="confirm-delete-btn"]');
    await this.clickElement(confirmBtn);
    
    await this.waitForToast('Candidates deleted successfully');
    await this.waitForElementHidden(this.deleteConfirmModal);
    await this.waitForSearchResults();
  }

  /**
   * Change page size
   */
  async changePageSize(size: number): Promise<void> {
    await this.selectOption(this.pageSizeSelect, size.toString());
    await this.waitForSearchResults();
  }

  /**
   * Navigate to next page
   */
  async goToNextPage(): Promise<void> {
    await this.clickElement(this.nextPageBtn);
    await this.waitForSearchResults();
  }

  /**
   * Navigate to previous page
   */
  async goToPreviousPage(): Promise<void> {
    await this.clickElement(this.prevPageBtn);
    await this.waitForSearchResults();
  }

  /**
   * Verify candidate appears in list
   */
  async verifyCandidateExists(candidateEmail: string): Promise<void> {
    const candidateRow = this.candidateRows.filter({ hasText: candidateEmail }).first();
    await expect(candidateRow).toBeVisible();
  }

  /**
   * Verify candidate does not appear in list
   */
  async verifyCandidateNotExists(candidateEmail: string): Promise<void> {
    const candidateRow = this.candidateRows.filter({ hasText: candidateEmail }).first();
    await expect(candidateRow).not.toBeVisible();
  }

  /**
   * Export candidates
   */
  async exportCandidates(format: 'csv' | 'excel' = 'csv'): Promise<void> {
    await this.clickElement(this.exportBtn);
    
    const formatBtn = this.page.locator(`[data-testid="export-${format}-btn"]`);
    await this.clickElement(formatBtn);
    
    // Wait for download to start
    const downloadPromise = this.page.waitForEvent('download');
    await downloadPromise;
    
    await this.waitForToast('Export completed successfully');
  }
}