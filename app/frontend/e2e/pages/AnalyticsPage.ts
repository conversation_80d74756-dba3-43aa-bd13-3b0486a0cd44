import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../utils/base';

/**
 * Analytics Page Object - Analytics dashboard and reporting interface
 */
export class AnalyticsPage extends BasePage {
  // Page header
  readonly pageTitle: Locator;
  readonly timeRangeSelector: Locator;
  readonly refreshBtn: Locator;
  readonly exportBtn: Locator;

  // Overview section
  readonly overviewSection: Locator;
  readonly totalCandidatesMetric: Locator;
  readonly totalPositionsMetric: Locator;
  readonly activeMatchesMetric: Locator;
  readonly successRateMetric: Locator;
  readonly avgTimeToHireMetric: Locator;

  // Charts section
  readonly chartsSection: Locator;
  readonly trendsChart: Locator;
  readonly performanceChart: Locator;
  readonly departmentChart: Locator;
  readonly conversionFunnelChart: Locator;

  // Filters and controls
  readonly departmentFilter: Locator;
  readonly positionTypeFilter: Locator;
  readonly metricSelector: Locator;
  readonly chartTypeSelector: Locator;

  // Data tables
  readonly departmentPerformanceTable: Locator;
  readonly topSkillsTable: Locator;
  readonly conversionDataTable: Locator;

  // Real-time features
  readonly realTimeIndicator: Locator;
  readonly realTimeToggle: Locator;
  readonly lastUpdatedTimestamp: Locator;

  // Export and sharing
  readonly shareBtn: Locator;
  readonly scheduleReportBtn: Locator;
  readonly downloadDashboardBtn: Locator;

  constructor(page: Page) {
    super(page, '/dashboard/analytics');
    
    // Header elements
    this.pageTitle = page.locator('h1, [data-testid="page-title"]');
    this.timeRangeSelector = page.locator('[data-testid="time-range-selector"]');
    this.refreshBtn = page.locator('[data-testid="refresh-btn"]');
    this.exportBtn = page.locator('[data-testid="export-btn"]');

    // Overview metrics
    this.overviewSection = page.locator('[data-testid="overview-section"]');
    this.totalCandidatesMetric = page.locator('[data-testid="total-candidates-metric"]');
    this.totalPositionsMetric = page.locator('[data-testid="total-positions-metric"]');
    this.activeMatchesMetric = page.locator('[data-testid="active-matches-metric"]');
    this.successRateMetric = page.locator('[data-testid="success-rate-metric"]');
    this.avgTimeToHireMetric = page.locator('[data-testid="avg-time-to-hire-metric"]');

    // Charts
    this.chartsSection = page.locator('[data-testid="charts-section"]');
    this.trendsChart = page.locator('[data-testid="trends-chart"]');
    this.performanceChart = page.locator('[data-testid="performance-chart"]');
    this.departmentChart = page.locator('[data-testid="department-chart"]');
    this.conversionFunnelChart = page.locator('[data-testid="conversion-funnel-chart"]');

    // Filters and controls
    this.departmentFilter = page.locator('[data-testid="department-filter"]');
    this.positionTypeFilter = page.locator('[data-testid="position-type-filter"]');
    this.metricSelector = page.locator('[data-testid="metric-selector"]');
    this.chartTypeSelector = page.locator('[data-testid="chart-type-selector"]');

    // Data tables
    this.departmentPerformanceTable = page.locator('[data-testid="department-performance-table"]');
    this.topSkillsTable = page.locator('[data-testid="top-skills-table"]');
    this.conversionDataTable = page.locator('[data-testid="conversion-data-table"]');

    // Real-time features
    this.realTimeIndicator = page.locator('[data-testid="real-time-indicator"]');
    this.realTimeToggle = page.locator('[data-testid="real-time-toggle"]');
    this.lastUpdatedTimestamp = page.locator('[data-testid="last-updated"]');

    // Export and sharing
    this.shareBtn = page.locator('[data-testid="share-btn"]');
    this.scheduleReportBtn = page.locator('[data-testid="schedule-report-btn"]');
    this.downloadDashboardBtn = page.locator('[data-testid="download-dashboard-btn"]');
  }

  /**
   * Navigate to analytics page and verify it loads
   */
  async navigate(): Promise<void> {
    await this.goto();
    await this.verifyAnalyticsPageLoaded();
  }

  /**
   * Verify analytics page is properly loaded
   */
  async verifyAnalyticsPageLoaded(): Promise<void> {
    await this.waitForElement(this.pageTitle);
    await expect(this.pageTitle).toContainText(/analytics|dashboard/i);
    await this.waitForChartsToLoad();
  }

  /**
   * Wait for all charts to load
   */
  async waitForChartsToLoad(): Promise<void> {
    await this.waitForElement(this.chartsSection);
    
    // Wait for chart libraries to initialize
    await this.page.waitForFunction(() => {
      const charts = document.querySelectorAll('[data-testid*="chart"] svg, .recharts-surface, canvas');
      return charts.length > 0;
    }, { timeout: 15000 });

    await this.waitForLoadingComplete();
  }

  /**
   * Change time range for analytics
   */
  async selectTimeRange(range: 'last7days' | 'last30days' | 'last3months' | 'last6months' | 'lastyear'): Promise<void> {
    await this.clickElement(this.timeRangeSelector);
    const rangeOption = this.page.locator(`[data-testid="range-${range}"]`);
    await this.clickElement(rangeOption);
    
    // Wait for data to refresh
    await this.waitForChartsToLoad();
  }

  /**
   * Get overview metric value
   */
  async getMetricValue(metric: 'candidates' | 'positions' | 'matches' | 'success_rate' | 'time_to_hire'): Promise<string> {
    let metricLocator: Locator;
    
    switch (metric) {
      case 'candidates':
        metricLocator = this.totalCandidatesMetric;
        break;
      case 'positions':
        metricLocator = this.totalPositionsMetric;
        break;
      case 'matches':
        metricLocator = this.activeMatchesMetric;
        break;
      case 'success_rate':
        metricLocator = this.successRateMetric;
        break;
      case 'time_to_hire':
        metricLocator = this.avgTimeToHireMetric;
        break;
    }
    
    await this.waitForElement(metricLocator);
    const valueElement = metricLocator.locator('[data-testid="metric-value"], .metric-value');
    return await this.getElementText(valueElement);
  }

  /**
   * Verify all overview metrics are visible and have values
   */
  async verifyOverviewMetrics(): Promise<void> {
    const metrics = [
      this.totalCandidatesMetric,
      this.totalPositionsMetric,
      this.activeMatchesMetric,
      this.successRateMetric,
      this.avgTimeToHireMetric
    ];

    for (const metric of metrics) {
      await this.waitForElement(metric);
      const value = await this.getElementText(metric.locator('[data-testid="metric-value"], .metric-value'));
      expect(value).not.toBe('');
      expect(value).not.toBe('0'); // Assuming test data exists
    }
  }

  /**
   * Apply department filter
   */
  async filterByDepartment(department: string): Promise<void> {
    await this.clickElement(this.departmentFilter);
    const departmentOption = this.page.locator(`[data-testid="dept-${department}"]`);
    await this.clickElement(departmentOption);
    await this.waitForChartsToLoad();
  }

  /**
   * Apply position type filter
   */
  async filterByPositionType(type: string): Promise<void> {
    await this.clickElement(this.positionTypeFilter);
    const typeOption = this.page.locator(`[data-testid="type-${type}"]`);
    await this.clickElement(typeOption);
    await this.waitForChartsToLoad();
  }

  /**
   * Change chart visualization type
   */
  async changeChartType(chartTestId: string, type: 'line' | 'bar' | 'pie' | 'area'): Promise<void> {
    const chart = this.page.locator(`[data-testid="${chartTestId}"]`);
    const chartControls = chart.locator('[data-testid="chart-controls"]');
    await this.clickElement(chartControls);
    
    const typeOption = this.page.locator(`[data-testid="chart-type-${type}"]`);
    await this.clickElement(typeOption);
    
    // Wait for chart to re-render
    await this.page.waitForTimeout(1000);
  }

  /**
   * Verify trends chart shows data
   */
  async verifyTrendsChart(): Promise<void> {
    await this.waitForElement(this.trendsChart);
    
    // Check that chart has data points or bars
    const chartData = this.trendsChart.locator('.recharts-line-curve, .recharts-bar, .recharts-area-curve');
    await expect(chartData.first()).toBeVisible();
  }

  /**
   * Verify performance chart shows data
   */
  async verifyPerformanceChart(): Promise<void> {
    await this.waitForElement(this.performanceChart);
    
    // Check that chart has data elements
    const chartData = this.performanceChart.locator('path, rect, circle');
    await expect(chartData.first()).toBeVisible();
  }

  /**
   * Verify department performance table has data
   */
  async verifyDepartmentPerformanceTable(): Promise<void> {
    await this.waitForElement(this.departmentPerformanceTable);
    
    const rows = this.departmentPerformanceTable.locator('tbody tr');
    await expect(rows).not.toHaveCount(0);
  }

  /**
   * Get department performance data
   */
  async getDepartmentPerformance(department: string): Promise<{ candidates: string; hired: string; success_rate: string }> {
    const row = this.departmentPerformanceTable.locator(`tr:has-text("${department}")`);
    await this.waitForElement(row);
    
    const cells = row.locator('td');
    
    return {
      candidates: await cells.nth(1).textContent() || '0',
      hired: await cells.nth(2).textContent() || '0',
      success_rate: await cells.nth(3).textContent() || '0%'
    };
  }

  /**
   * Enable real-time updates
   */
  async enableRealTimeUpdates(): Promise<void> {
    if (!(await this.realTimeToggle.isChecked())) {
      await this.clickElement(this.realTimeToggle);
    }
    
    // Verify real-time indicator is active
    await this.waitForElement(this.realTimeIndicator);
    await expect(this.realTimeIndicator).toHaveClass(/active|enabled/);
  }

  /**
   * Disable real-time updates
   */
  async disableRealTimeUpdates(): Promise<void> {
    if (await this.realTimeToggle.isChecked()) {
      await this.clickElement(this.realTimeToggle);
    }
  }

  /**
   * Verify last updated timestamp is recent
   */
  async verifyLastUpdatedTimestamp(): Promise<void> {
    const timestamp = await this.getElementText(this.lastUpdatedTimestamp);
    expect(timestamp).toMatch(/\d+:\d+|\d+ (minutes?|hours?) ago|just now/i);
  }

  /**
   * Refresh analytics data
   */
  async refreshData(): Promise<void> {
    await this.clickElement(this.refreshBtn);
    await this.waitForChartsToLoad();
    await this.waitForToast('Analytics data refreshed');
  }

  /**
   * Export analytics data
   */
  async exportAnalytics(format: 'csv' | 'excel' | 'pdf'): Promise<void> {
    await this.clickElement(this.exportBtn);
    const formatBtn = this.page.locator(`[data-testid="export-${format}-btn"]`);
    await this.clickElement(formatBtn);
    
    // Wait for download to start
    const downloadPromise = this.page.waitForEvent('download');
    await downloadPromise;
    
    await this.waitForToast('Analytics export completed');
  }

  /**
   * Share analytics dashboard
   */
  async shareDashboard(emails: string[]): Promise<void> {
    await this.clickElement(this.shareBtn);
    
    const shareModal = this.page.locator('[data-testid="share-modal"]');
    await this.waitForElement(shareModal);
    
    const emailInput = shareModal.locator('[data-testid="email-input"]');
    await this.fillInput(emailInput, emails.join(', '));
    
    const sendBtn = shareModal.locator('[data-testid="send-btn"]');
    await this.clickElement(sendBtn);
    
    await this.waitForToast('Dashboard shared successfully');
    await this.waitForElementHidden(shareModal);
  }

  /**
   * Schedule automated report
   */
  async scheduleReport(frequency: 'daily' | 'weekly' | 'monthly', recipients: string[]): Promise<void> {
    await this.clickElement(this.scheduleReportBtn);
    
    const scheduleModal = this.page.locator('[data-testid="schedule-modal"]');
    await this.waitForElement(scheduleModal);
    
    // Set frequency
    const frequencySelect = scheduleModal.locator('[data-testid="frequency-select"]');
    await this.selectOption(frequencySelect, frequency);
    
    // Set recipients
    const recipientsInput = scheduleModal.locator('[data-testid="recipients-input"]');
    await this.fillInput(recipientsInput, recipients.join(', '));
    
    // Schedule the report
    const scheduleBtn = scheduleModal.locator('[data-testid="schedule-btn"]');
    await this.clickElement(scheduleBtn);
    
    await this.waitForToast('Report scheduled successfully');
    await this.waitForElementHidden(scheduleModal);
  }

  /**
   * Download dashboard as image
   */
  async downloadDashboard(format: 'png' | 'pdf' = 'png'): Promise<void> {
    await this.clickElement(this.downloadDashboardBtn);
    
    const formatBtn = this.page.locator(`[data-testid="download-${format}-btn"]`);
    await this.clickElement(formatBtn);
    
    // Wait for download
    const downloadPromise = this.page.waitForEvent('download');
    await downloadPromise;
  }

  /**
   * Compare data between two time periods
   */
  async comparePeriods(period1: string, period2: string): Promise<void> {
    const compareBtn = this.page.locator('[data-testid="compare-periods-btn"]');
    await this.clickElement(compareBtn);
    
    const compareModal = this.page.locator('[data-testid="compare-modal"]');
    await this.waitForElement(compareModal);
    
    const period1Select = compareModal.locator('[data-testid="period1-select"]');
    await this.selectOption(period1Select, period1);
    
    const period2Select = compareModal.locator('[data-testid="period2-select"]');
    await this.selectOption(period2Select, period2);
    
    const compareSubmitBtn = compareModal.locator('[data-testid="compare-submit-btn"]');
    await this.clickElement(compareSubmitBtn);
    
    // Wait for comparison data to load
    await this.waitForChartsToLoad();
    await this.waitForElementHidden(compareModal);
  }

  /**
   * Drill down into specific metric
   */
  async drillDownMetric(metricName: string): Promise<void> {
    const metric = this.page.locator(`[data-testid="${metricName}-metric"]`);
    await this.clickElement(metric);
    
    // Wait for drill-down view to load
    const drillDownView = this.page.locator('[data-testid="drill-down-view"]');
    await this.waitForElement(drillDownView);
  }

  /**
   * Take screenshot of full analytics dashboard
   */
  async takeAnalyticsScreenshot(name: string = 'analytics-dashboard'): Promise<void> {
    await this.waitForChartsToLoad();
    await this.takeScreenshot(name);
  }

  /**
   * Verify analytics page responsiveness
   */
  async verifyResponsiveLayout(): Promise<void> {
    // Test desktop layout
    await this.page.setViewportSize({ width: 1920, height: 1080 });
    await this.waitForChartsToLoad();
    
    // Test tablet layout
    await this.page.setViewportSize({ width: 768, height: 1024 });
    await this.page.waitForTimeout(1000); // Allow charts to reflow
    
    // Test mobile layout
    await this.page.setViewportSize({ width: 375, height: 667 });
    await this.page.waitForTimeout(1000);
    
    // Restore desktop layout
    await this.page.setViewportSize({ width: 1920, height: 1080 });
  }
}