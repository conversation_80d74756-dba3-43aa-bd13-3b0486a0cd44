import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from '../utils/base';

/**
 * Dashboard Page Object - Main admin dashboard
 */
export class DashboardPage extends BasePage {
  // Navigation elements
  readonly sidebar: Locator;
  readonly userMenu: Locator;
  readonly logoutButton: Locator;

  // Main content areas
  readonly statsCards: Locator;
  readonly candidatesCard: Locator;
  readonly positionsCard: Locator;
  readonly matchesCard: Locator;
  readonly recentActivity: Locator;

  // Quick action buttons
  readonly addCandidateBtn: Locator;
  readonly addPositionBtn: Locator;
  readonly generateReportBtn: Locator;

  // Charts and visualizations
  readonly chartContainer: Locator;
  readonly performanceChart: Locator;
  readonly trendsChart: Locator;

  constructor(page: Page) {
    super(page, '/dashboard');
    
    // Navigation
    this.sidebar = page.locator('[data-testid="sidebar"], .sidebar, nav[role="navigation"]');
    this.userMenu = page.locator('[data-testid="user-menu"]');
    this.logoutButton = page.locator('[data-testid="logout-button"]');

    // Main content
    this.statsCards = page.locator('[data-testid="stats-cards"], .stats-grid');
    this.candidatesCard = page.locator('[data-testid="candidates-stats"]');
    this.positionsCard = page.locator('[data-testid="positions-stats"]');
    this.matchesCard = page.locator('[data-testid="matches-stats"]');
    this.recentActivity = page.locator('[data-testid="recent-activity"]');

    // Quick actions
    this.addCandidateBtn = page.locator('[data-testid="add-candidate-btn"]');
    this.addPositionBtn = page.locator('[data-testid="add-position-btn"]');
    this.generateReportBtn = page.locator('[data-testid="generate-report-btn"]');

    // Charts
    this.chartContainer = page.locator('[data-testid="chart-container"], .chart-container');
    this.performanceChart = page.locator('[data-testid="performance-chart"]');
    this.trendsChart = page.locator('[data-testid="trends-chart"]');
  }

  /**
   * Navigate to dashboard and verify it loads
   */
  async navigate(): Promise<void> {
    await this.goto();
    await this.verifyDashboardLoaded();
  }

  /**
   * Verify dashboard is properly loaded
   */
  async verifyDashboardLoaded(): Promise<void> {
    await this.waitForElement(this.statsCards);
    await this.waitForLoadingComplete();
    await expect(this.page.locator('h1, [data-testid="page-title"]')).toContainText(/dashboard|overview/i);
  }

  /**
   * Get stats card values
   */
  async getStatsCardValue(cardName: 'candidates' | 'positions' | 'matches'): Promise<string> {
    let card: Locator;
    switch (cardName) {
      case 'candidates':
        card = this.candidatesCard;
        break;
      case 'positions':
        card = this.positionsCard;
        break;
      case 'matches':
        card = this.matchesCard;
        break;
    }
    
    await this.waitForElement(card);
    const valueElement = card.locator('[data-testid="stat-value"], .stat-value, .count');
    return await valueElement.textContent() || '0';
  }

  /**
   * Navigate to candidates page via quick action
   */
  async goToCandidates(): Promise<void> {
    await this.clickElement(this.addCandidateBtn.or(this.candidatesCard));
    await this.page.waitForURL(/.*\/candidates/);
  }

  /**
   * Navigate to positions page via quick action
   */
  async goToPositions(): Promise<void> {
    await this.clickElement(this.addPositionBtn.or(this.positionsCard));
    await this.page.waitForURL(/.*\/positions/);
  }

  /**
   * Navigate to reports page
   */
  async goToReports(): Promise<void> {
    await this.clickElement(this.generateReportBtn);
    await this.page.waitForURL(/.*\/reports/);
  }

  /**
   * Navigate using sidebar menu
   */
  async navigateToPage(pageName: string): Promise<void> {
    const menuItem = this.sidebar.locator(`[data-testid="${pageName}-menu"], a[href*="${pageName}"]`);
    await this.clickElement(menuItem);
    await this.page.waitForURL(new RegExp(`.*/${pageName}`));
  }

  /**
   * Verify recent activity shows items
   */
  async verifyRecentActivity(): Promise<void> {
    await this.waitForElement(this.recentActivity);
    const activityItems = this.recentActivity.locator('.activity-item, [data-testid="activity-item"]');
    await expect(activityItems).not.toHaveCount(0);
  }

  /**
   * Wait for charts to load
   */
  async waitForChartsToLoad(): Promise<void> {
    await this.waitForElement(this.chartContainer);
    
    // Wait for chart libraries to initialize
    await this.page.waitForFunction(() => {
      return document.querySelector('[data-testid="chart-container"] svg') !== null ||
             document.querySelector('.recharts-surface') !== null;
    }, { timeout: 15000 });
  }

  /**
   * Verify performance metrics are displayed
   */
  async verifyPerformanceMetrics(): Promise<void> {
    await this.waitForChartsToLoad();
    
    // Check that performance chart has data
    const chartSvg = this.performanceChart.locator('svg, canvas');
    await this.waitForElement(chartSvg);
  }

  /**
   * Take screenshot of dashboard for visual testing
   */
  async takeFullScreenshot(name: string = 'dashboard'): Promise<void> {
    await this.waitForChartsToLoad();
    await this.takeScreenshot(name);
  }

  /**
   * Logout from dashboard
   */
  async logout(): Promise<void> {
    await this.clickElement(this.userMenu);
    await this.clickElement(this.logoutButton);
    await this.page.waitForURL(/.*\/login/);
  }

  /**
   * Verify user is logged in as admin
   */
  async verifyAdminUser(): Promise<void> {
    await this.clickElement(this.userMenu);
    const userInfo = this.page.locator('[data-testid="user-info"], .user-info');
    await expect(userInfo).toContainText('<EMAIL>');
  }

  /**
   * Search using global search if available
   */
  async globalSearch(query: string): Promise<void> {
    const searchInput = this.page.locator('[data-testid="global-search"], input[placeholder*="search" i]');
    await this.fillInput(searchInput, query);
    await this.page.keyboard.press('Enter');
  }

  /**
   * Check system notifications
   */
  async checkNotifications(): Promise<number> {
    const notificationBadge = this.page.locator('[data-testid="notification-badge"]');
    
    if (await notificationBadge.isVisible()) {
      const count = await notificationBadge.textContent();
      return parseInt(count || '0', 10);
    }
    
    return 0;
  }

  /**
   * Verify dashboard responsiveness by checking layout at different sizes
   */
  async verifyResponsiveLayout(): Promise<void> {
    // Test desktop layout
    await this.page.setViewportSize({ width: 1920, height: 1080 });
    await this.waitForElement(this.sidebar);
    
    // Test tablet layout
    await this.page.setViewportSize({ width: 768, height: 1024 });
    await this.page.waitForTimeout(500); // Allow layout to adjust
    
    // Test mobile layout
    await this.page.setViewportSize({ width: 375, height: 667 });
    await this.page.waitForTimeout(500);
    
    // Restore desktop layout
    await this.page.setViewportSize({ width: 1920, height: 1080 });
  }
}