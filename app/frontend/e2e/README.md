# TalentForge Pro E2E Testing Suite

Comprehensive End-to-End testing suite for TalentForge Pro admin functionality using Playwright.

## 🚀 Quick Start

### Prerequisites
- Docker services running (`make up`)
- Application accessible at `http://localhost:8088`
- Node.js and pnpm installed

### Running Tests

```bash
# Install Playwright browsers
pnpm playwright install

# Run all E2E tests
pnpm test:e2e

# Run tests with UI
pnpm test:e2e:ui

# Run tests in headed mode (see browser)
pnpm test:e2e:headed

# Run specific test file
pnpm playwright test auth/admin-authentication.spec.ts

# Run tests with specific tag
pnpm playwright test --grep "@smoke"
```

## 📋 Test Structure

### Test Categories

#### 🔐 Authentication Tests
- **File**: `tests/auth/admin-authentication.spec.ts`
- **Coverage**: Dev token auth, session management, protected routes
- **Key Tests**: Token validation, route protection, logout flow

#### 👥 Candidate Management Tests  
- **File**: `tests/candidates/candidate-management.spec.ts`
- **Coverage**: CRUD operations, search, filtering, bulk actions
- **Key Tests**: Create/delete candidates, search functionality, pagination

#### 💼 Position Management Tests
- **File**: `tests/positions/position-management.spec.ts` 
- **Coverage**: Job posting management, status changes, matching
- **Key Tests**: Create positions, OCR upload, status management

#### 📊 Analytics Dashboard Tests
- **File**: `tests/analytics/analytics-dashboard.spec.ts`
- **Coverage**: Dashboard metrics, charts, filters, real-time updates
- **Key Tests**: Chart rendering, data filtering, export functionality

#### 🎨 Visual Regression Tests
- **File**: `tests/admin/visual-regression.spec.ts`
- **Coverage**: UI consistency, responsive design, theme variations
- **Key Tests**: Page snapshots, component-level screenshots

#### ⚡ Performance Monitoring Tests
- **File**: `tests/admin/performance-monitoring.spec.ts`
- **Coverage**: Load times, memory usage, Core Web Vitals
- **Key Tests**: Page performance, API response times, resource sizes

#### 🔄 Integration Tests
- **File**: `tests/admin/dashboard-integration.spec.ts`
- **Coverage**: Cross-page workflows, data consistency, error handling
- **Key Tests**: End-to-end user journeys, concurrent operations

## 🏗️ Architecture

### Page Object Model
```
e2e/
├── pages/                  # Page Object classes
│   ├── DashboardPage.ts   # Dashboard functionality
│   ├── CandidatesPage.ts  # Candidate management
│   ├── PositionsPage.ts   # Position management
│   └── AnalyticsPage.ts   # Analytics dashboard
├── utils/                  # Utilities and helpers
│   ├── base.ts            # Base page class
│   ├── auth.ts            # Authentication utilities
│   └── test-data.ts       # Test data factories
├── fixtures/              # Test fixtures
│   └── admin.fixture.ts   # Admin user fixtures
└── tests/                 # Test specifications
    ├── auth/              # Authentication tests
    ├── candidates/        # Candidate management tests
    ├── positions/         # Position management tests
    ├── analytics/         # Analytics tests
    └── admin/             # Integration and QA tests
```

### Key Components

#### 🔧 Base Page Class (`utils/base.ts`)
- Common functionality for all page objects
- Element interaction helpers
- Loading state management
- Screenshot utilities

#### 🛡️ Authentication Utils (`utils/auth.ts`)
- Development token authentication
- Session management
- Admin user verification

#### 🏭 Test Data Factory (`utils/test-data.ts`)
- Generates test candidates, positions, job descriptions
- Configurable test data with realistic values
- Bulk data generation for performance testing

#### 🧪 Admin Fixtures (`fixtures/admin.fixture.ts`)
- Pre-authenticated page objects
- Consistent test setup across suites
- Dependency injection for page objects

## 🎯 Configuration

### Playwright Config (`playwright.config.ts`)
```typescript
{
  baseURL: 'http://localhost:8088',        // Docker environment
  browser: 'chrome-desktop',               // Chrome only as requested
  authentication: 'dev_bypass_token',     // Development token auth
  viewport: { width: 1920, height: 1080 }, // Full HD testing
  screenshots: 'only-on-failure',         // Visual debugging
  videos: 'retain-on-failure',           // Failure analysis
  parallelization: false,                 // Sequential for admin flows
}
```

### Environment Variables
```bash
# Override default base URL
PLAYWRIGHT_BASE_URL=http://localhost:8088

# Enable debug mode
DEBUG=pw:api

# Set test timeout
PLAYWRIGHT_TIMEOUT=30000
```

## 🔍 Test Features

### 🔐 Authentication Strategy
- **Development Token**: `dev_bypass_token_2025_talentforge`
- **Admin User**: `<EMAIL>`
- **Session Management**: Automatic token refresh
- **SSR/CSR Compatibility**: Cookie + localStorage storage

### 📊 Data Management
- **Test Data Factory**: Generates realistic test data
- **Data Isolation**: Each test creates its own data
- **Cleanup**: Automatic cleanup after test completion
- **Mock Responses**: API mocking for edge cases

### 🎨 Visual Testing
- **Page Snapshots**: Full page visual regression
- **Component Screenshots**: Isolated component testing
- **Responsive Testing**: Multiple viewport sizes
- **Theme Variations**: Light/dark theme testing
- **Animation Handling**: Disabled for consistent screenshots

### ⚡ Performance Monitoring
- **Core Web Vitals**: LCP, FID, CLS measurement
- **Memory Usage**: JavaScript heap monitoring
- **API Response Times**: Backend performance tracking
- **Resource Sizes**: Bundle size monitoring
- **Concurrent User Simulation**: Load testing

## 📈 Reporting

### HTML Reports
- **Location**: `test-results/html-report/index.html`
- **Features**: Interactive test results, screenshots, videos
- **Filtering**: By test status, duration, browser

### JUnit XML
- **Location**: `test-results/results.xml`
- **Usage**: CI/CD integration, test reporting tools

### JSON Results
- **Location**: `test-results/results.json`
- **Usage**: Custom reporting, metrics extraction

### Screenshots & Videos
- **Location**: `test-results/screenshots/`, `test-results/videos/`
- **Triggers**: Test failures, explicit screenshots
- **Format**: PNG screenshots, WebM videos

## 🚀 CI/CD Integration

### GitHub Actions Example
```yaml
- name: Run E2E Tests
  run: |
    make up
    pnpm install
    pnpm playwright install --with-deps
    pnpm test:e2e:ci
  
- name: Upload Test Results
  uses: actions/upload-artifact@v3
  if: always()
  with:
    name: playwright-report
    path: test-results/
```

### Docker Integration
```bash
# Run tests in Docker environment
docker compose -f app/docker-compose.yml exec frontend pnpm test:e2e
```

## 🛠️ Development

### Adding New Tests
1. Create test file in appropriate directory
2. Use admin fixture for authenticated tests
3. Follow Page Object Model pattern
4. Include visual regression if UI changes
5. Add performance tests for new features

### Debugging Tests
```bash
# Run with debug mode
DEBUG=pw:api pnpm playwright test

# Run in headed mode
pnpm test:e2e:headed

# Run specific test with UI
pnpm playwright test --ui auth/admin-authentication.spec.ts

# Generate test code
pnpm playwright codegen localhost:8088
```

### Best Practices
- Use data-testid attributes for stable selectors
- Include both positive and negative test cases
- Test error states and edge cases
- Keep tests independent and idempotent
- Use meaningful test descriptions
- Clean up test data after completion

## 📊 Coverage Matrix

### Admin Functionality Coverage

| Feature | Authentication | CRUD | Search/Filter | Bulk Ops | Export | Performance | Visual |
|---------|----------------|------|---------------|----------|--------|-------------|---------|
| Dashboard | ✅ | N/A | ✅ | N/A | ✅ | ✅ | ✅ |
| Candidates | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Positions | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Analytics | ✅ | N/A | ✅ | N/A | ✅ | ✅ | ✅ |
| Reports | ✅ | ✅ | ✅ | N/A | ✅ | ✅ | ✅ |

### Test Scenarios
- **Positive Flows**: Happy path user journeys
- **Negative Flows**: Error handling, validation
- **Edge Cases**: Boundary conditions, empty states
- **Performance**: Load times, memory usage
- **Accessibility**: Keyboard navigation, ARIA labels
- **Responsive**: Mobile, tablet, desktop layouts
- **Cross-browser**: Chrome focus (as requested)

## 🎯 Success Metrics

### Test Quality KPIs
- **Test Coverage**: >95% of admin features
- **Test Reliability**: <5% flaky test rate  
- **Performance**: <30s total execution time
- **Maintenance**: <2h/month test maintenance

### Application Quality Gates
- **Page Load**: <3s on standard hardware
- **API Response**: <2s for CRUD operations  
- **Memory Usage**: <100MB peak during testing
- **Visual Consistency**: 0 unintended UI regressions

---

🚀 **Ready to test!** Run `pnpm test:e2e` to execute the full admin E2E testing suite.