import { adminTest as test, expect } from '../../fixtures/admin.fixture';
import { TestDataFactory } from '../../utils/test-data';

test.describe('Candidate Management', () => {
  test.beforeEach(async () => {
    // Reset test data factory counters
    TestDataFactory.reset();
  });

  test('should display candidates list page', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    // Verify page loads with proper elements
    await expect(candidatesPage.pageTitle).toBeVisible();
    await expect(candidatesPage.searchInput).toBeVisible();
    await expect(candidatesPage.addCandidateBtn).toBeVisible();
  });

  test('should create a new candidate', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    const testCandidate = TestDataFactory.createCandidate();
    await candidatesPage.createCandidate(testCandidate);
    
    // Verify candidate appears in list
    await candidatesPage.verifyCandidateExists(testCandidate.email);
  });

  test('should search for candidates', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    // Create a candidate first
    const testCandidate = TestDataFactory.createCandidate();
    await candidatesPage.createCandidate(testCandidate);
    
    // Search for the candidate
    await candidatesPage.searchCandidates(testCandidate.email);
    
    // Should find the candidate
    await candidatesPage.verifyCandidateExists(testCandidate.email);
    
    // Search for non-existent candidate
    await candidatesPage.searchCandidates('<EMAIL>');
    
    // Should show empty state or no results
    const candidateCount = await candidatesPage.getCandidateCount();
    expect(candidateCount).toBe(0);
  });

  test('should filter candidates by skills', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    // Create candidates with different skills
    const jsCandidate = TestDataFactory.createCandidate({
      skills: ['JavaScript', 'React', 'Node.js']
    });
    const pythonCandidate = TestDataFactory.createCandidate({
      skills: ['Python', 'Django', 'PostgreSQL']
    });
    
    await candidatesPage.createCandidate(jsCandidate);
    await candidatesPage.createCandidate(pythonCandidate);
    
    // Filter by JavaScript skill
    await candidatesPage.filterBySkills(['JavaScript']);
    
    // Should show JS candidate but not Python candidate
    await candidatesPage.verifyCandidateExists(jsCandidate.email);
    await candidatesPage.verifyCandidateNotExists(pythonCandidate.email);
  });

  test('should filter candidates by experience level', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    // Create candidates with different experience levels
    const juniorCandidate = TestDataFactory.createCandidate({
      experience_years: 2
    });
    const seniorCandidate = TestDataFactory.createCandidate({
      experience_years: 8
    });
    
    await candidatesPage.createCandidate(juniorCandidate);
    await candidatesPage.createCandidate(seniorCandidate);
    
    // Filter by experience (5+ years)
    await candidatesPage.filterByExperience(5);
    
    // Should show senior candidate but not junior
    await candidatesPage.verifyCandidateExists(seniorCandidate.email);
    await candidatesPage.verifyCandidateNotExists(juniorCandidate.email);
  });

  test('should clear filters and show all candidates', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    // Create test candidates
    const candidate1 = TestDataFactory.createCandidate();
    const candidate2 = TestDataFactory.createCandidate();
    
    await candidatesPage.createCandidate(candidate1);
    await candidatesPage.createCandidate(candidate2);
    
    // Apply a filter
    await candidatesPage.filterByExperience(10); // High experience filter
    
    // Clear filters
    await candidatesPage.clearFilters();
    
    // Should show both candidates again
    await candidatesPage.verifyCandidateExists(candidate1.email);
    await candidatesPage.verifyCandidateExists(candidate2.email);
  });

  test('should delete a candidate', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    const testCandidate = TestDataFactory.createCandidate();
    await candidatesPage.createCandidate(testCandidate);
    
    // Delete the candidate
    await candidatesPage.deleteCandidate(testCandidate.email);
    
    // Verify candidate no longer appears in list
    await candidatesPage.verifyCandidateNotExists(testCandidate.email);
  });

  test('should perform bulk delete operations', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    // Create multiple candidates
    const candidates = TestDataFactory.createCandidates(3);
    
    for (const candidate of candidates) {
      await candidatesPage.createCandidate(candidate);
    }
    
    // Select and bulk delete
    const candidateEmails = candidates.map(c => c.email);
    await candidatesPage.bulkDeleteCandidates(candidateEmails);
    
    // Verify all candidates are deleted
    for (const email of candidateEmails) {
      await candidatesPage.verifyCandidateNotExists(email);
    }
  });

  test('should navigate between pages with pagination', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    // Set small page size to test pagination
    await candidatesPage.changePageSize(5);
    
    // Create enough candidates to span multiple pages
    const candidates = TestDataFactory.createCandidates(12);
    
    for (const candidate of candidates) {
      await candidatesPage.createCandidate(candidate);
    }
    
    // Should show 5 candidates on first page
    let candidateCount = await candidatesPage.getCandidateCount();
    expect(candidateCount).toBe(5);
    
    // Go to next page
    await candidatesPage.goToNextPage();
    
    // Should show next 5 candidates
    candidateCount = await candidatesPage.getCandidateCount();
    expect(candidateCount).toBe(5);
    
    // Go back to first page
    await candidatesPage.goToPreviousPage();
    candidateCount = await candidatesPage.getCandidateCount();
    expect(candidateCount).toBe(5);
  });

  test('should export candidates data', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    // Create some test candidates
    const candidates = TestDataFactory.createCandidates(3);
    
    for (const candidate of candidates) {
      await candidatesPage.createCandidate(candidate);
    }
    
    // Export candidates
    await candidatesPage.exportCandidates('csv');
    
    // Note: In a real test, you might verify the downloaded file contents
    // This test just verifies the export functionality works without errors
  });

  test('should validate candidate form fields', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    await candidatesPage.openAddCandidateModal();
    
    // Try to submit empty form
    await candidatesPage.submitCandidateForm();
    
    // Should show validation errors
    const formErrors = candidatesPage.candidateModal.locator('[data-testid="form-errors"]');
    await expect(formErrors).toBeVisible();
  });

  test('should handle candidate creation with minimum required fields', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    const minimalCandidate = TestDataFactory.createCandidate({
      phone_number: undefined,
      location: undefined,
      experience_years: undefined,
      education_level: undefined,
      current_position: undefined,
      expected_salary_min: undefined,
      expected_salary_max: undefined
    });
    
    await candidatesPage.createCandidate(minimalCandidate);
    await candidatesPage.verifyCandidateExists(minimalCandidate.email);
  });

  test('should display candidate details when clicked', async ({ candidatesPage, page }) => {
    await candidatesPage.navigate();
    
    const testCandidate = TestDataFactory.createCandidate();
    await candidatesPage.createCandidate(testCandidate);
    
    // Click on candidate to view details
    await candidatesPage.clickCandidate(testCandidate.email);
    
    // Should navigate to candidate detail page
    await expect(page).toHaveURL(/.*\/candidates\/\d+/);
  });
});