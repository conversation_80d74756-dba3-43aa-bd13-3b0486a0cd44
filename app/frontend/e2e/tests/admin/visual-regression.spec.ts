import { adminTest as test, expect } from '../../fixtures/admin.fixture';

test.describe('Visual Regression Testing', () => {
  test('should match dashboard visual snapshot', async ({ dashboardPage, page }) => {
    await dashboardPage.navigate();
    await dashboardPage.waitForChartsToLoad();
    
    // Hide dynamic elements that change frequently
    await page.addStyleTag({
      content: `
        [data-testid="last-updated"],
        [data-testid="real-time-indicator"],
        .timestamp,
        .last-updated {
          visibility: hidden !important;
        }
      `
    });
    
    // Take full page screenshot
    await expect(page).toHaveScreenshot('dashboard-full-page.png', {
      fullPage: true,
      animations: 'disabled'
    });
  });

  test('should match candidates page visual snapshot', async ({ candidatesPage, page }) => {
    await candidatesPage.navigate();
    
    // Hide dynamic elements
    await page.addStyleTag({
      content: `
        [data-testid="last-updated"],
        .timestamp {
          visibility: hidden !important;
        }
      `
    });
    
    await expect(page).toHaveScreenshot('candidates-page.png', {
      fullPage: true,
      animations: 'disabled'
    });
  });

  test('should match positions page visual snapshot', async ({ positionsPage, page }) => {
    await positionsPage.navigate();
    
    // Hide dynamic elements
    await page.addStyleTag({
      content: `
        [data-testid="last-updated"],
        .timestamp {
          visibility: hidden !important;
        }
      `
    });
    
    await expect(page).toHaveScreenshot('positions-page.png', {
      fullPage: true,
      animations: 'disabled'
    });
  });

  test('should match analytics page visual snapshot', async ({ analyticsPage, page }) => {
    await analyticsPage.navigate();
    await analyticsPage.waitForChartsToLoad();
    
    // Hide dynamic elements and animations
    await page.addStyleTag({
      content: `
        [data-testid="last-updated"],
        [data-testid="real-time-indicator"],
        .timestamp,
        .recharts-tooltip,
        .chart-animation,
        *[class*="animate"] {
          visibility: hidden !important;
        }
        
        .recharts-surface,
        .recharts-wrapper {
          animation: none !important;
          transition: none !important;
        }
      `
    });
    
    // Wait for charts to stabilize
    await page.waitForTimeout(2000);
    
    await expect(page).toHaveScreenshot('analytics-page.png', {
      fullPage: true,
      animations: 'disabled'
    });
  });

  test('should match modal dialogs visual snapshots', async ({ candidatesPage, page }) => {
    await candidatesPage.navigate();
    await candidatesPage.openAddCandidateModal();
    
    // Screenshot of modal
    await expect(candidatesPage.candidateModal).toHaveScreenshot('add-candidate-modal.png', {
      animations: 'disabled'
    });
  });

  test('should match responsive layout snapshots', async ({ dashboardPage, page }) => {
    await dashboardPage.navigate();
    
    // Desktop view
    await page.setViewportSize({ width: 1920, height: 1080 });
    await expect(page).toHaveScreenshot('dashboard-desktop.png', {
      fullPage: true,
      animations: 'disabled'
    });
    
    // Tablet view
    await page.setViewportSize({ width: 768, height: 1024 });
    await page.waitForTimeout(500);
    await expect(page).toHaveScreenshot('dashboard-tablet.png', {
      fullPage: true,
      animations: 'disabled'
    });
    
    // Mobile view
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    await expect(page).toHaveScreenshot('dashboard-mobile.png', {
      fullPage: true,
      animations: 'disabled'
    });
  });

  test('should match component-level visual snapshots', async ({ dashboardPage, page }) => {
    await dashboardPage.navigate();
    
    // Stats cards component
    await expect(dashboardPage.statsCards).toHaveScreenshot('stats-cards.png', {
      animations: 'disabled'
    });
    
    // Recent activity component (if visible)
    if (await dashboardPage.recentActivity.isVisible()) {
      await expect(dashboardPage.recentActivity).toHaveScreenshot('recent-activity.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match chart visual snapshots', async ({ analyticsPage, page }) => {
    await analyticsPage.navigate();
    await analyticsPage.waitForChartsToLoad();
    
    // Disable chart animations and tooltips
    await page.addStyleTag({
      content: `
        .recharts-tooltip,
        .recharts-legend,
        .chart-tooltip {
          display: none !important;
        }
        
        * {
          animation-duration: 0s !important;
          transition-duration: 0s !important;
        }
      `
    });
    
    // Wait for charts to stabilize
    await page.waitForTimeout(1000);
    
    // Individual chart snapshots
    await expect(analyticsPage.trendsChart).toHaveScreenshot('trends-chart.png');
    await expect(analyticsPage.performanceChart).toHaveScreenshot('performance-chart.png');
  });

  test('should handle visual regression with different themes', async ({ dashboardPage, page }) => {
    // Test light theme (default)
    await dashboardPage.navigate();
    await expect(page).toHaveScreenshot('dashboard-light-theme.png', {
      animations: 'disabled'
    });
    
    // Switch to dark theme if available
    const themeToggle = page.locator('[data-testid="theme-toggle"], [data-theme-toggle]');
    if (await themeToggle.isVisible()) {
      await themeToggle.click();
      await page.waitForTimeout(500);
      
      await expect(page).toHaveScreenshot('dashboard-dark-theme.png', {
        animations: 'disabled'
      });
    }
  });

  test('should match error state visual snapshots', async ({ page }) => {
    // Mock API error to test error states
    await page.route('/api/v1/analytics/overview', (route) => {
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'Server error' })
      });
    });
    
    await page.goto('/dashboard/analytics');
    
    // Wait for error state to appear
    const errorState = page.locator('[data-testid="error-state"], .error-message');
    await expect(errorState).toBeVisible();
    
    await expect(page).toHaveScreenshot('analytics-error-state.png', {
      animations: 'disabled'
    });
  });

  test('should match loading state visual snapshots', async ({ page }) => {
    // Slow down network to capture loading states
    await page.route('/api/v1/analytics/overview', async (route) => {
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay
      route.continue();
    });
    
    const gotoPromise = page.goto('/dashboard/analytics');
    
    // Take screenshot of loading state
    await page.waitForSelector('[data-testid="loading"], .loading-spinner', { timeout: 1000 });
    await expect(page).toHaveScreenshot('analytics-loading-state.png', {
      animations: 'disabled'
    });
    
    // Wait for navigation to complete
    await gotoPromise;
  });

  test('should match empty state visual snapshots', async ({ candidatesPage, page }) => {
    // Mock empty candidates response
    await page.route('/api/v1/candidates*', (route) => {
      route.fulfill({
        status: 200,
        body: JSON.stringify({
          items: [],
          total: 0,
          skip: 0,
          limit: 10
        })
      });
    });
    
    await candidatesPage.navigate();
    
    // Should show empty state
    await expect(candidatesPage.emptyState).toBeVisible();
    await expect(page).toHaveScreenshot('candidates-empty-state.png', {
      animations: 'disabled'
    });
  });
});