import { adminTest as test, expect } from '../../fixtures/admin.fixture';

test.describe('Performance Monitoring', () => {
  test('should meet page load performance thresholds', async ({ page, dashboardPage }) => {
    // Start performance monitoring
    await page.goto(dashboardPage.url, { waitUntil: 'networkidle' });
    
    // Get performance metrics
    const performanceMetrics = await page.evaluate(() => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      return {
        domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
        loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
        firstPaint: performance.getEntriesByName('first-paint')[0]?.startTime || 0,
        firstContentfulPaint: performance.getEntriesByName('first-contentful-paint')[0]?.startTime || 0,
        largestContentfulPaint: 0, // Would need LCP observer
        totalLoadTime: navigation.loadEventEnd - navigation.navigationStart
      };
    });
    
    console.log('Performance Metrics:', performanceMetrics);
    
    // Assert performance thresholds
    expect(performanceMetrics.domContentLoaded).toBeLessThan(2000); // < 2 seconds
    expect(performanceMetrics.firstContentfulPaint).toBeLessThan(3000); // < 3 seconds
    expect(performanceMetrics.totalLoadTime).toBeLessThan(5000); // < 5 seconds
  });

  test('should measure API response times', async ({ page, candidatesPage }) => {
    const apiTimings: Record<string, number> = {};
    
    // Monitor API requests
    page.on('response', (response) => {
      if (response.url().includes('/api/v1/')) {
        const timing = response.timing();
        const totalTime = timing.responseEnd - timing.requestStart;
        const endpoint = new URL(response.url()).pathname;
        apiTimings[endpoint] = totalTime;
      }
    });
    
    await candidatesPage.navigate();
    
    // Wait for API calls to complete
    await page.waitForTimeout(2000);
    
    console.log('API Response Times:', apiTimings);
    
    // Assert API response time thresholds
    Object.entries(apiTimings).forEach(([endpoint, time]) => {
      expect(time).toBeLessThan(2000); // < 2 seconds per API call
      console.log(`${endpoint}: ${time}ms`);
    });
  });

  test('should monitor memory usage during navigation', async ({ page, dashboardPage, candidatesPage, positionsPage, analyticsPage }) => {
    const memoryUsages: number[] = [];
    
    const getMemoryUsage = async () => {
      const metrics = await page.evaluate(() => {
        return (performance as any).memory ? {
          used: (performance as any).memory.usedJSHeapSize,
          total: (performance as any).memory.totalJSHeapSize,
          limit: (performance as any).memory.jsHeapSizeLimit
        } : null;
      });
      return metrics;
    };
    
    // Baseline memory usage
    await dashboardPage.navigate();
    let initialMemory = await getMemoryUsage();
    if (initialMemory) {
      memoryUsages.push(initialMemory.used);
      console.log('Initial memory usage:', initialMemory.used / 1024 / 1024, 'MB');
    }
    
    // Navigate through different pages and measure memory
    const pages = [candidatesPage, positionsPage, analyticsPage];
    
    for (const pageObj of pages) {
      await pageObj.navigate();
      await page.waitForTimeout(1000); // Allow page to settle
      
      const memory = await getMemoryUsage();
      if (memory) {
        memoryUsages.push(memory.used);
        console.log(`Memory usage after ${pageObj.constructor.name}:`, memory.used / 1024 / 1024, 'MB');
        
        // Check for memory leaks (memory should not grow excessively)
        const growth = memory.used - (initialMemory?.used || 0);
        expect(growth).toBeLessThan(50 * 1024 * 1024); // < 50MB growth
      }
    }
  });

  test('should measure chart rendering performance', async ({ page, analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Measure chart rendering time
    const chartPerformance = await page.evaluate(() => {
      return new Promise((resolve) => {
        const startTime = performance.now();
        
        // Wait for charts to be rendered
        const checkCharts = () => {
          const charts = document.querySelectorAll('.recharts-surface, canvas, svg[class*="chart"]');
          if (charts.length > 0) {
            const endTime = performance.now();
            resolve(endTime - startTime);
          } else {
            setTimeout(checkCharts, 100);
          }
        };
        
        checkCharts();
      });
    });
    
    console.log('Chart rendering time:', chartPerformance, 'ms');
    expect(chartPerformance).toBeLessThan(3000); // < 3 seconds for chart rendering
  });

  test('should monitor network resource sizes', async ({ page, dashboardPage }) => {
    const resourceSizes: Record<string, number> = {};
    let totalSize = 0;
    
    page.on('response', async (response) => {
      try {
        const url = new URL(response.url());
        const size = parseInt(response.headers()['content-length'] || '0', 10);
        
        if (size > 0) {
          const resourceType = url.pathname.split('.').pop() || 'unknown';
          resourceSizes[resourceType] = (resourceSizes[resourceType] || 0) + size;
          totalSize += size;
        }
      } catch (error) {
        // Ignore errors in resource size calculation
      }
    });
    
    await dashboardPage.navigate();
    
    console.log('Resource sizes by type:', resourceSizes);
    console.log('Total size:', totalSize / 1024 / 1024, 'MB');
    
    // Assert resource size thresholds
    expect(totalSize).toBeLessThan(10 * 1024 * 1024); // < 10MB total
    
    // Check specific resource types
    if (resourceSizes.js) {
      expect(resourceSizes.js).toBeLessThan(5 * 1024 * 1024); // < 5MB JS
    }
    if (resourceSizes.css) {
      expect(resourceSizes.css).toBeLessThan(1 * 1024 * 1024); // < 1MB CSS
    }
  });

  test('should measure search performance', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    
    // Measure search response time
    const searchStartTime = Date.now();
    
    await candidatesPage.searchCandidates('test');
    
    const searchEndTime = Date.now();
    const searchTime = searchEndTime - searchStartTime;
    
    console.log('Search response time:', searchTime, 'ms');
    expect(searchTime).toBeLessThan(1000); // < 1 second for search
  });

  test('should measure form submission performance', async ({ candidatesPage }) => {
    await candidatesPage.navigate();
    await candidatesPage.openAddCandidateModal();
    
    // Measure form submission time
    const submissionStartTime = Date.now();
    
    // Fill minimal form data
    await candidatesPage.fillCandidateForm({
      email: '<EMAIL>',
      full_name: 'Performance Test User',
      skills: ['Testing']
    });
    
    await candidatesPage.submitCandidateForm();
    
    const submissionEndTime = Date.now();
    const submissionTime = submissionEndTime - submissionStartTime;
    
    console.log('Form submission time:', submissionTime, 'ms');
    expect(submissionTime).toBeLessThan(3000); // < 3 seconds for form submission
  });

  test('should monitor Core Web Vitals', async ({ page, dashboardPage }) => {
    await dashboardPage.navigate();
    
    // Get Core Web Vitals using the web-vitals library approach
    const webVitals = await page.evaluate(() => {
      return new Promise((resolve) => {
        const vitals: Record<string, number> = {};
        
        // Largest Contentful Paint
        new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          vitals.LCP = lastEntry.startTime;
        }).observe({ entryTypes: ['largest-contentful-paint'] });
        
        // First Input Delay (approximate)
        new PerformanceObserver((list) => {
          const firstInput = list.getEntries()[0];
          if (firstInput) {
            vitals.FID = firstInput.processingStart - firstInput.startTime;
          }
        }).observe({ entryTypes: ['first-input'] });
        
        // Cumulative Layout Shift
        let cumulativeScore = 0;
        new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (!(entry as any).hadRecentInput) {
              cumulativeScore += (entry as any).value;
            }
          }
          vitals.CLS = cumulativeScore;
        }).observe({ entryTypes: ['layout-shift'] });
        
        // Return vitals after a reasonable wait time
        setTimeout(() => resolve(vitals), 5000);
      });
    });
    
    console.log('Core Web Vitals:', webVitals);
    
    // Assert Core Web Vitals thresholds (Google's "Good" thresholds)
    if (webVitals.LCP) {
      expect(webVitals.LCP).toBeLessThan(2500); // < 2.5 seconds
    }
    if (webVitals.FID) {
      expect(webVitals.FID).toBeLessThan(100); // < 100ms
    }
    if (webVitals.CLS) {
      expect(webVitals.CLS).toBeLessThan(0.1); // < 0.1
    }
  });

  test('should handle concurrent user simulation', async ({ browser }) => {
    const contexts = await Promise.all([
      browser.newContext(),
      browser.newContext(),
      browser.newContext()
    ]);
    
    const pages = await Promise.all(contexts.map(context => context.newPage()));
    
    // Simulate concurrent users
    const startTime = Date.now();
    
    await Promise.all(pages.map(async (page, index) => {
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
      
      // Different user actions
      if (index === 0) {
        await page.goto('/dashboard/candidates');
      } else if (index === 1) {
        await page.goto('/dashboard/positions');
      } else {
        await page.goto('/dashboard/analytics');
      }
      
      await page.waitForLoadState('networkidle');
    }));
    
    const endTime = Date.now();
    const concurrentLoadTime = endTime - startTime;
    
    console.log('Concurrent user simulation time:', concurrentLoadTime, 'ms');
    expect(concurrentLoadTime).toBeLessThan(10000); // < 10 seconds for 3 concurrent users
    
    // Clean up
    await Promise.all(contexts.map(context => context.close()));
  });
});