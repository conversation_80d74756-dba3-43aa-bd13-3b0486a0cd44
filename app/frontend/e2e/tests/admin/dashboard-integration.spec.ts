import { adminTest as test, expect } from '../../fixtures/admin.fixture';
import { TestDataFactory } from '../../utils/test-data';

test.describe('Dashboard Integration Tests', () => {
  test('should complete full admin user journey', async ({ 
    dashboardPage, 
    candidatesPage, 
    positionsPage, 
    analyticsPage,
    page 
  }) => {
    // Start at dashboard
    await dashboardPage.navigate();
    await dashboardPage.verifyDashboardLoaded();
    
    // Navigate to candidates
    await dashboardPage.goToCandidates();
    await expect(page).toHaveURL(/.*\/candidates/);
    
    // Create a candidate
    const testCandidate = TestDataFactory.createCandidate();
    await candidatesPage.createCandidate(testCandidate);
    await candidatesPage.verifyCandidateExists(testCandidate.email);
    
    // Navigate to positions
    await dashboardPage.navigateToPage('positions');
    await expect(page).toHaveURL(/.*\/positions/);
    
    // Create a position
    const testPosition = TestDataFactory.createPosition();
    await positionsPage.createPosition(testPosition);
    await positionsPage.verifyPositionExists(testPosition.title);
    
    // Navigate to analytics
    await dashboardPage.navigateToPage('analytics');
    await expect(page).toHaveURL(/.*\/analytics/);
    
    // Verify analytics reflects the new data
    await analyticsPage.verifyOverviewMetrics();
    
    // Return to dashboard
    await dashboardPage.navigate();
    await dashboardPage.verifyDashboardLoaded();
    
    // Verify stats are updated (candidates count should be > 0)
    const candidatesCount = await dashboardPage.getStatsCardValue('candidates');
    expect(parseInt(candidatesCount) || 0).toBeGreaterThan(0);
  });

  test('should handle navigation between all admin sections', async ({ dashboardPage, page }) => {
    await dashboardPage.navigate();
    
    const adminSections = [
      'candidates',
      'positions', 
      'analytics',
      'reports'
    ];
    
    for (const section of adminSections) {
      await dashboardPage.navigateToPage(section);
      await expect(page).toHaveURL(new RegExp(`.*/${section}`));
      
      // Verify page loads without errors
      await page.waitForLoadState('networkidle');
      const mainContent = page.locator('main, [role="main"], .main-content');
      await expect(mainContent).toBeVisible();
      
      // Return to dashboard
      await page.goto('/dashboard');
    }
  });

  test('should maintain consistent header and navigation across pages', async ({ 
    dashboardPage, 
    candidatesPage, 
    positionsPage, 
    page 
  }) => {
    const pages = [dashboardPage, candidatesPage, positionsPage];
    
    for (const pageObj of pages) {
      await pageObj.navigate();
      
      // Verify sidebar is consistent
      await expect(dashboardPage.sidebar).toBeVisible();
      
      // Verify user menu is present
      await expect(dashboardPage.userMenu).toBeVisible();
      
      // Verify admin user info
      await dashboardPage.verifyAdminUser();
    }
  });

  test('should handle global search functionality', async ({ dashboardPage }) => {
    await dashboardPage.navigate();
    
    // Test global search if available
    try {
      await dashboardPage.globalSearch('test search');
      // If no error, search functionality exists and works
    } catch (error) {
      // Global search might not be implemented, which is acceptable
      console.log('Global search not available or not implemented');
    }
  });

  test('should display and update real-time notifications', async ({ dashboardPage, page }) => {
    await dashboardPage.navigate();
    
    // Check for notifications
    const notificationCount = await dashboardPage.checkNotifications();
    expect(notificationCount).toBeGreaterThanOrEqual(0);
    
    // If notifications exist, they should be clickable
    if (notificationCount > 0) {
      const notificationBell = page.locator('[data-testid="notification-bell"]');
      if (await notificationBell.isVisible()) {
        await notificationBell.click();
        
        // Notifications panel should open
        const notificationsPanel = page.locator('[data-testid="notifications-panel"]');
        await expect(notificationsPanel).toBeVisible();
      }
    }
  });

  test('should handle responsive dashboard layout', async ({ dashboardPage }) => {
    await dashboardPage.navigate();
    await dashboardPage.verifyResponsiveLayout();
    
    // Verify dashboard functionality works on all screen sizes
    await dashboardPage.verifyDashboardLoaded();
  });

  test('should maintain session across dashboard sections', async ({ 
    authUtils, 
    dashboardPage, 
    candidatesPage, 
    page 
  }) => {
    await dashboardPage.navigate();
    
    // Navigate to different sections
    await candidatesPage.navigate();
    
    // Verify still authenticated
    await authUtils.verifyAdminAuth();
    
    // Reload page
    await page.reload();
    
    // Should still be authenticated and on candidates page
    await expect(page).toHaveURL(/.*\/candidates/);
    await authUtils.verifyAdminAuth();
  });

  test('should handle concurrent operations across dashboard', async ({ 
    candidatesPage, 
    positionsPage,
    browser 
  }) => {
    // Create additional context for concurrent operations
    const context2 = await browser.newContext();
    const page2 = await context2.newPage();
    
    // Setup authentication for second context
    await page2.goto('/dashboard');
    await page2.evaluate(() => {
      localStorage.setItem('access_token', 'dev_bypass_token_2025_talentforge');
    });
    
    // Concurrent operations
    const operations = [
      // First context: Create candidate
      (async () => {
        await candidatesPage.navigate();
        const candidate = TestDataFactory.createCandidate();
        await candidatesPage.createCandidate(candidate);
        return candidate.email;
      })(),
      
      // Second context: Create position  
      (async () => {
        const positionsPage2 = new positionsPage.constructor(page2);
        await positionsPage2.navigate();
        const position = TestDataFactory.createPosition();
        await positionsPage2.createPosition(position);
        return position.title;
      })()
    ];
    
    const [candidateEmail, positionTitle] = await Promise.all(operations);
    
    // Verify both operations succeeded
    await candidatesPage.verifyCandidateExists(candidateEmail);
    await positionsPage.navigate();
    await positionsPage.verifyPositionExists(positionTitle);
    
    await context2.close();
  });

  test('should handle dashboard data refresh', async ({ dashboardPage, analyticsPage }) => {
    await dashboardPage.navigate();
    
    // Get initial stats
    const initialCandidates = await dashboardPage.getStatsCardValue('candidates');
    
    // Navigate to analytics and refresh
    await analyticsPage.navigate();
    await analyticsPage.refreshData();
    
    // Return to dashboard
    await dashboardPage.navigate();
    
    // Stats should still be consistent
    const refreshedCandidates = await dashboardPage.getStatsCardValue('candidates');
    expect(refreshedCandidates).toBe(initialCandidates);
  });

  test('should handle error states gracefully across dashboard', async ({ page, dashboardPage }) => {
    // Mock API errors
    await page.route('/api/v1/candidates*', (route) => {
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'Server error' })
      });
    });
    
    await dashboardPage.navigate();
    
    // Dashboard should still load with error handling
    await dashboardPage.verifyDashboardLoaded();
    
    // Navigate to candidates - should handle error gracefully
    await page.goto('/dashboard/candidates');
    
    // Should show error state instead of crashing
    const errorMessage = page.locator('[data-testid="error-message"], .error-state');
    await expect(errorMessage).toBeVisible();
  });

  test('should maintain consistent branding and styling', async ({ 
    dashboardPage, 
    candidatesPage, 
    positionsPage,
    page 
  }) => {
    const pages = [dashboardPage, candidatesPage, positionsPage];
    
    for (const pageObj of pages) {
      await pageObj.navigate();
      
      // Check for consistent logo/branding
      const logo = page.locator('[data-testid="logo"], .logo, [alt*="logo" i]');
      if (await logo.isVisible()) {
        await expect(logo).toBeVisible();
      }
      
      // Check for consistent color scheme
      const primaryColors = await page.evaluate(() => {
        const computedStyle = getComputedStyle(document.body);
        return {
          backgroundColor: computedStyle.backgroundColor,
          color: computedStyle.color
        };
      });
      
      // Colors should be defined (not transparent/default)
      expect(primaryColors.backgroundColor).not.toBe('rgba(0, 0, 0, 0)');
    }
  });

  test('should complete end-to-end admin workflow', async ({ 
    dashboardPage, 
    candidatesPage, 
    positionsPage, 
    analyticsPage,
    page 
  }) => {
    // 1. Start at dashboard overview
    await dashboardPage.navigate();
    const initialStats = {
      candidates: await dashboardPage.getStatsCardValue('candidates'),
      positions: await dashboardPage.getStatsCardValue('positions')
    };
    
    // 2. Add a new candidate
    await candidatesPage.navigate();
    const candidate = TestDataFactory.createCandidate();
    await candidatesPage.createCandidate(candidate);
    
    // 3. Add a new position
    await positionsPage.navigate();
    const position = TestDataFactory.createPosition();
    await positionsPage.createPosition(position);
    
    // 4. View analytics to see updated metrics
    await analyticsPage.navigate();
    await analyticsPage.verifyOverviewMetrics();
    
    // 5. Return to dashboard and verify updates
    await dashboardPage.navigate();
    const finalStats = {
      candidates: await dashboardPage.getStatsCardValue('candidates'),
      positions: await dashboardPage.getStatsCardValue('positions')
    };
    
    // Stats should reflect the additions
    expect(parseInt(finalStats.candidates) || 0).toBeGreaterThanOrEqual(
      parseInt(initialStats.candidates) || 0
    );
    expect(parseInt(finalStats.positions) || 0).toBeGreaterThanOrEqual(
      parseInt(initialStats.positions) || 0
    );
    
    // 6. Clean up by deleting created items
    await candidatesPage.navigate();
    await candidatesPage.deleteCandidate(candidate.email);
    
    await positionsPage.navigate();
    await positionsPage.deletePosition(position.title);
  });
});