import { adminTest as test, expect } from '../../fixtures/admin.fixture';
import { AuthUtils } from '../../utils/auth';

test.describe('Admin Authentication', () => {
  test.describe.configure({ mode: 'serial' }); // Run auth tests sequentially
  
  test('should authenticate admin user with development token', async ({ page, authUtils }) => {
    // Authentication is handled by fixture, verify it worked
    await authUtils.verifyAdminAuth();
    
    // Navigate to dashboard and verify access
    await page.goto('/dashboard');
    await expect(page.locator('h1, [data-testid="page-title"]')).toContainText(/dashboard|overview/i);
  });

  test('should maintain authentication across page reloads', async ({ page, authUtils }) => {
    await page.goto('/dashboard');
    await page.reload();
    
    // Should still be authenticated
    await authUtils.verifyAdminAuth();
    await expect(page).toHaveURL(/.*\/dashboard/);
  });

  test('should access protected admin routes', async ({ page, authUtils }) => {
    const protectedRoutes = [
      '/dashboard',
      '/dashboard/candidates',
      '/dashboard/positions',
      '/dashboard/analytics',
      '/dashboard/reports'
    ];

    for (const route of protectedRoutes) {
      await page.goto(route);
      
      // Should not redirect to login
      await expect(page).not.toHaveURL(/.*\/login/);
      
      // Should load the page content
      await page.waitForLoadState('networkidle');
      const pageContent = page.locator('main, [role="main"], .main-content');
      await expect(pageContent).toBeVisible();
    }
  });

  test('should display correct admin user info', async ({ page, dashboardPage }) => {
    await dashboardPage.navigate();
    await dashboardPage.verifyAdminUser();
  });

  test('should successfully logout admin user', async ({ page, authUtils, dashboardPage }) => {
    await dashboardPage.navigate();
    await dashboardPage.logout();
    
    // Should redirect to login page
    await expect(page).toHaveURL(/.*\/login/);
    
    // Should not be authenticated anymore
    const isLoggedIn = await authUtils.isLoggedIn();
    expect(isLoggedIn).toBe(false);
  });

  test('should handle API authentication for admin requests', async ({ page, authUtils }) => {
    // Test that API requests include proper authentication
    const [response] = await Promise.all([
      page.waitForResponse('/api/v1/auth/me'),
      page.goto('/dashboard')
    ]);

    expect(response.status()).toBe(200);
    
    const userData = await response.json();
    expect(userData.email).toBe(AuthUtils.ADMIN_CREDENTIALS.email);
  });

  test('should handle authentication token refresh', async ({ page, authUtils }) => {
    // Navigate to dashboard
    await page.goto('/dashboard');
    
    // Wait longer than typical token refresh interval
    await page.waitForTimeout(2000);
    
    // Should still be authenticated
    await authUtils.verifyAdminAuth();
  });

  test('should prevent access to login page when authenticated', async ({ page }) => {
    await page.goto('/login');
    
    // Should redirect away from login since user is authenticated
    await page.waitForURL(/dashboard/, { timeout: 10000 });
    await expect(page).toHaveURL(/.*\/dashboard/);
  });

  test('should handle concurrent authentication requests', async ({ page, authUtils }) => {
    // Make multiple concurrent requests to protected endpoints
    const requests = [
      page.request.get('/api/v1/candidates?limit=5'),
      page.request.get('/api/v1/positions?limit=5'),
      page.request.get('/api/v1/auth/me'),
      page.request.get('/api/v1/analytics/overview')
    ];

    const responses = await Promise.all(requests);
    
    // All requests should succeed with proper authentication
    for (const response of responses) {
      expect(response.status()).toBe(200);
    }
  });
});