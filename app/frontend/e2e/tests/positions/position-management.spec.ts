import { adminTest as test, expect } from '../../fixtures/admin.fixture';
import { TestDataFactory } from '../../utils/test-data';

test.describe('Position Management', () => {
  test.beforeEach(async () => {
    // Reset test data factory counters
    TestDataFactory.reset();
  });

  test('should display positions list page', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    // Verify page loads with proper elements
    await expect(positionsPage.pageTitle).toBeVisible();
    await expect(positionsPage.searchInput).toBeVisible();
    await expect(positionsPage.addPositionBtn).toBeVisible();
  });

  test('should create a new position', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    const testPosition = TestDataFactory.createPosition();
    await positionsPage.createPosition(testPosition);
    
    // Verify position appears in list
    await positionsPage.verifyPositionExists(testPosition.title);
  });

  test('should search for positions', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    // Create a position first
    const testPosition = TestDataFactory.createPosition();
    await positionsPage.createPosition(testPosition);
    
    // Search for the position
    await positionsPage.searchPositions(testPosition.title);
    
    // Should find the position
    await positionsPage.verifyPositionExists(testPosition.title);
    
    // Search for non-existent position
    await positionsPage.searchPositions('Non-existent Position');
    
    // Should show no results
    const positionCount = await positionsPage.getPositionCount();
    expect(positionCount).toBe(0);
  });

  test('should filter positions by department', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    // Create positions in different departments
    const engineeringPos = TestDataFactory.createPosition({
      title: 'Software Engineer',
      department: 'Engineering'
    });
    const marketingPos = TestDataFactory.createPosition({
      title: 'Marketing Manager',
      department: 'Marketing'
    });
    
    await positionsPage.createPosition(engineeringPos);
    await positionsPage.createPosition(marketingPos);
    
    // Filter by Engineering department
    await positionsPage.filterByDepartment('Engineering');
    
    // Should show engineering position but not marketing
    await positionsPage.verifyPositionExists(engineeringPos.title);
    await positionsPage.verifyPositionNotExists(marketingPos.title);
  });

  test('should filter positions by experience level', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    // Create positions with different experience levels
    const entryPosition = TestDataFactory.createPosition({
      title: 'Junior Developer',
      experience_level: 'entry'
    });
    const seniorPosition = TestDataFactory.createPosition({
      title: 'Senior Engineer',
      experience_level: 'senior'
    });
    
    await positionsPage.createPosition(entryPosition);
    await positionsPage.createPosition(seniorPosition);
    
    // Filter by senior level
    await positionsPage.filterByExperienceLevel('senior');
    
    // Should show senior position but not entry
    await positionsPage.verifyPositionExists(seniorPosition.title);
    await positionsPage.verifyPositionNotExists(entryPosition.title);
  });

  test('should filter positions by employment type', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    // Create positions with different employment types
    const fullTimePos = TestDataFactory.createPosition({
      title: 'Full-time Developer',
      employment_type: 'full_time'
    });
    const contractPos = TestDataFactory.createPosition({
      title: 'Contract Developer',
      employment_type: 'contract'
    });
    
    await positionsPage.createPosition(fullTimePos);
    await positionsPage.createPosition(contractPos);
    
    // Filter by contract type
    await positionsPage.filterByEmploymentType('contract');
    
    // Should show contract position but not full-time
    await positionsPage.verifyPositionExists(contractPos.title);
    await positionsPage.verifyPositionNotExists(fullTimePos.title);
  });

  test('should filter positions by salary range', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    // Create positions with different salary ranges
    const juniorPos = TestDataFactory.createPosition({
      title: 'Junior Position',
      salary_min: 50000,
      salary_max: 70000
    });
    const seniorPos = TestDataFactory.createPosition({
      title: 'Senior Position',
      salary_min: 120000,
      salary_max: 160000
    });
    
    await positionsPage.createPosition(juniorPos);
    await positionsPage.createPosition(seniorPos);
    
    // Filter by high salary range (100k+)
    await positionsPage.filterBySalaryRange(100000);
    
    // Should show senior position but not junior
    await positionsPage.verifyPositionExists(seniorPos.title);
    await positionsPage.verifyPositionNotExists(juniorPos.title);
  });

  test('should clear all filters', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    // Create test positions
    const position1 = TestDataFactory.createPosition();
    const position2 = TestDataFactory.createPosition();
    
    await positionsPage.createPosition(position1);
    await positionsPage.createPosition(position2);
    
    // Apply filters
    await positionsPage.filterBySalaryRange(200000); // High salary filter
    
    // Clear filters
    await positionsPage.clearFilters();
    
    // Should show both positions again
    await positionsPage.verifyPositionExists(position1.title);
    await positionsPage.verifyPositionExists(position2.title);
  });

  test('should delete a position', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    const testPosition = TestDataFactory.createPosition();
    await positionsPage.createPosition(testPosition);
    
    // Delete the position
    await positionsPage.deletePosition(testPosition.title);
    
    // Verify position no longer appears in list
    await positionsPage.verifyPositionNotExists(testPosition.title);
  });

  test('should change position status', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    const testPosition = TestDataFactory.createPosition();
    await positionsPage.createPosition(testPosition);
    
    // Change status to paused
    await positionsPage.changePositionStatus(testPosition.title, 'paused');
    
    // Verify status changed
    const status = await positionsPage.getPositionStatus(testPosition.title);
    expect(status.toLowerCase()).toContain('paused');
  });

  test('should perform bulk status changes', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    // Create multiple positions
    const positions = TestDataFactory.createPositions(3);
    
    for (const position of positions) {
      await positionsPage.createPosition(position);
    }
    
    // Bulk change status to paused
    const positionTitles = positions.map(p => p.title);
    await positionsPage.bulkChangeStatus(positionTitles, 'paused');
    
    // Verify all positions are paused
    for (const title of positionTitles) {
      const status = await positionsPage.getPositionStatus(title);
      expect(status.toLowerCase()).toContain('paused');
    }
  });

  test('should navigate to candidate matching', async ({ positionsPage, page }) => {
    await positionsPage.navigate();
    
    const testPosition = TestDataFactory.createPosition();
    await positionsPage.createPosition(testPosition);
    
    // Navigate to matching page
    await positionsPage.goToMatching(testPosition.title);
    
    // Should navigate to matching page
    await expect(page).toHaveURL(/.*\/positions\/\d+\/match/);
  });

  test('should view position details', async ({ positionsPage, page }) => {
    await positionsPage.navigate();
    
    const testPosition = TestDataFactory.createPosition();
    await positionsPage.createPosition(testPosition);
    
    // View position details
    await positionsPage.viewPositionDetails(testPosition.title);
    
    // Should navigate to position detail page
    await expect(page).toHaveURL(/.*\/positions\/\d+/);
  });

  test('should export positions data', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    // Create some test positions
    const positions = TestDataFactory.createPositions(3);
    
    for (const position of positions) {
      await positionsPage.createPosition(position);
    }
    
    // Export positions
    await positionsPage.exportPositions('csv');
    
    // Note: In a real test, you might verify the downloaded file contents
    // This test just verifies the export functionality works without errors
  });

  test('should validate position form fields', async ({ positionsPage }) => {
    await positionsPage.navigate();
    await positionsPage.openAddPositionModal();
    
    // Try to submit empty form
    await positionsPage.submitPositionForm();
    
    // Should show validation errors
    const formErrors = positionsPage.positionModal.locator('[data-testid="form-errors"]');
    await expect(formErrors).toBeVisible();
  });

  test('should create position with all fields', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    const fullPosition = TestDataFactory.createPosition({
      title: 'Full Stack Developer',
      department: 'Engineering',
      location: 'San Francisco, CA',
      employment_type: 'full_time',
      experience_level: 'senior',
      salary_min: 120000,
      salary_max: 160000,
      description: 'We are looking for a talented Full Stack Developer...',
      requirements: [
        '5+ years of full-stack development',
        'React and Node.js expertise',
        'Database design experience'
      ],
      benefits: [
        'Health insurance',
        'Flexible hours',
        'Remote work'
      ],
      remote_work_allowed: true
    });
    
    await positionsPage.createPosition(fullPosition);
    await positionsPage.verifyPositionExists(fullPosition.title);
  });

  test('should handle position creation with minimum required fields', async ({ positionsPage }) => {
    await positionsPage.navigate();
    
    const minimalPosition = TestDataFactory.createPosition({
      salary_min: undefined,
      salary_max: undefined,
      benefits: undefined
    });
    
    await positionsPage.createPosition(minimalPosition);
    await positionsPage.verifyPositionExists(minimalPosition.title);
  });
});