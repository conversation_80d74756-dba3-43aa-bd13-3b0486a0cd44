import { adminTest as test, expect } from '../../fixtures/admin.fixture';

test.describe('Analytics Dashboard', () => {
  test('should display analytics dashboard with all components', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Verify page loads with proper elements
    await expect(analyticsPage.pageTitle).toBeVisible();
    await expect(analyticsPage.overviewSection).toBeVisible();
    await expect(analyticsPage.chartsSection).toBeVisible();
  });

  test('should display overview metrics with valid data', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    await analyticsPage.verifyOverviewMetrics();
    
    // Verify specific metrics have reasonable values
    const candidatesCount = await analyticsPage.getMetricValue('candidates');
    const positionsCount = await analyticsPage.getMetricValue('positions');
    const matchesCount = await analyticsPage.getMetricValue('matches');
    const successRate = await analyticsPage.getMetricValue('success_rate');
    
    // Basic validation that metrics exist and are not empty
    expect(candidatesCount).not.toBe('');
    expect(positionsCount).not.toBe('');
    expect(matchesCount).not.toBe('');
    expect(successRate).not.toBe('');
    
    // Success rate should be a percentage
    expect(successRate).toMatch(/\d+%|\d+\.\d+%/);
  });

  test('should display and interact with charts', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Verify charts are visible and have data
    await analyticsPage.verifyTrendsChart();
    await analyticsPage.verifyPerformanceChart();
    
    // Test chart interaction - change chart type
    await analyticsPage.changeChartType('trends-chart', 'bar');
    
    // Chart should re-render (wait for animation)
    await analyticsPage.page.waitForTimeout(1000);
  });

  test('should change time range and update data', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Get initial metrics
    const initialCandidates = await analyticsPage.getMetricValue('candidates');
    
    // Change time range
    await analyticsPage.selectTimeRange('last7days');
    
    // Data should update (might be the same or different)
    const newCandidates = await analyticsPage.getMetricValue('candidates');
    expect(newCandidates).not.toBe('');
    
    // Change to longer time range
    await analyticsPage.selectTimeRange('last6months');
    
    const longerRangeCandidates = await analyticsPage.getMetricValue('candidates');
    expect(longerRangeCandidates).not.toBe('');
  });

  test('should filter analytics by department', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Apply department filter
    await analyticsPage.filterByDepartment('Engineering');
    
    // Charts should update with filtered data
    await analyticsPage.verifyTrendsChart();
    await analyticsPage.verifyPerformanceChart();
  });

  test('should filter analytics by position type', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Apply position type filter
    await analyticsPage.filterByPositionType('full_time');
    
    // Charts should update with filtered data
    await analyticsPage.verifyTrendsChart();
    await analyticsPage.verifyPerformanceChart();
  });

  test('should display department performance table with data', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    await analyticsPage.verifyDepartmentPerformanceTable();
    
    // Get performance data for a specific department
    try {
      const engineeringPerf = await analyticsPage.getDepartmentPerformance('Engineering');
      expect(engineeringPerf.candidates).not.toBe('');
      expect(engineeringPerf.hired).not.toBe('');
      expect(engineeringPerf.success_rate).toMatch(/\d+%/);
    } catch (error) {
      // Department might not exist in test data, which is acceptable
      console.log('Engineering department not found in test data');
    }
  });

  test('should refresh analytics data', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Get timestamp before refresh
    const initialTimestamp = await analyticsPage.getElementText(analyticsPage.lastUpdatedTimestamp).catch(() => '');
    
    // Refresh data
    await analyticsPage.refreshData();
    
    // Verify data refreshed (timestamp should update or success message shown)
    await analyticsPage.verifyLastUpdatedTimestamp();
  });

  test('should export analytics data', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Export as CSV
    await analyticsPage.exportAnalytics('csv');
    
    // Export as Excel
    await analyticsPage.exportAnalytics('excel');
    
    // Export as PDF
    await analyticsPage.exportAnalytics('pdf');
  });

  test('should enable and disable real-time updates', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Enable real-time updates
    await analyticsPage.enableRealTimeUpdates();
    
    // Verify real-time indicator is active
    await expect(analyticsPage.realTimeIndicator).toHaveClass(/active|enabled/);
    
    // Disable real-time updates
    await analyticsPage.disableRealTimeUpdates();
  });

  test('should share analytics dashboard', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    const recipients = ['<EMAIL>', '<EMAIL>'];
    await analyticsPage.shareDashboard(recipients);
  });

  test('should schedule automated reports', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    const recipients = ['<EMAIL>'];
    await analyticsPage.scheduleReport('weekly', recipients);
  });

  test('should download dashboard as image', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Download as PNG
    await analyticsPage.downloadDashboard('png');
    
    // Download as PDF
    await analyticsPage.downloadDashboard('pdf');
  });

  test('should compare different time periods', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    await analyticsPage.comparePeriods('last30days', 'previous30days');
    
    // Verify comparison view loads
    const comparisonView = analyticsPage.page.locator('[data-testid="comparison-view"]');
    await expect(comparisonView).toBeVisible();
  });

  test('should drill down into metrics', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Drill down into candidates metric
    await analyticsPage.drillDownMetric('total-candidates');
    
    // Should show detailed view
    const drillDownView = analyticsPage.page.locator('[data-testid="drill-down-view"]');
    await expect(drillDownView).toBeVisible();
  });

  test('should handle responsive layout', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    await analyticsPage.verifyResponsiveLayout();
    
    // Verify charts still render properly on mobile
    await analyticsPage.verifyTrendsChart();
  });

  test('should handle analytics with no data gracefully', async ({ analyticsPage, page }) => {
    // Mock empty analytics response
    await analyticsPage.mockApiResponse('/api/v1/analytics/overview', {
      total_candidates: 0,
      total_positions: 0,
      active_matches: 0,
      success_rate: 0,
      avg_time_to_hire: 0
    });
    
    await analyticsPage.navigate();
    
    // Should still display the dashboard without errors
    await expect(analyticsPage.overviewSection).toBeVisible();
    
    // Should show zero values
    const candidatesCount = await analyticsPage.getMetricValue('candidates');
    expect(candidatesCount).toBe('0');
  });

  test('should handle API errors gracefully', async ({ analyticsPage, page }) => {
    // Mock API error
    await page.route('/api/v1/analytics/overview', (route) => {
      route.fulfill({
        status: 500,
        body: JSON.stringify({ error: 'Internal server error' })
      });
    });
    
    await analyticsPage.navigate();
    
    // Should display error message or fallback content
    const errorMessage = page.locator('[data-testid="error-message"], .error-message');
    await expect(errorMessage).toBeVisible();
  });

  test('should take full analytics dashboard screenshot', async ({ analyticsPage }) => {
    await analyticsPage.navigate();
    
    // Wait for all content to load
    await analyticsPage.verifyOverviewMetrics();
    await analyticsPage.verifyTrendsChart();
    
    // Take screenshot for visual regression testing
    await analyticsPage.takeAnalyticsScreenshot('full-analytics-dashboard');
  });
});