import { Page, BrowserContext } from '@playwright/test';

/**
 * Authentication utilities for E2E tests
 */
export class AuthUtils {
  private page: Page;
  private context: BrowserContext;

  constructor(page: Page) {
    this.page = page;
    this.context = page.context();
  }

  /**
   * Development token for bypassing authentication
   */
  static readonly DEV_TOKEN = 'dev_bypass_token_2025_talentforge';

  /**
   * Admin user credentials for development
   */
  static readonly ADMIN_CREDENTIALS = {
    email: '<EMAIL>',
    password: 'test123'
  };

  /**
   * Set authentication token in storage and headers
   */
  async setAuthToken(token: string = AuthUtils.DEV_TOKEN): Promise<void> {
    // Set token in localStorage
    await this.page.addInitScript((token) => {
      localStorage.setItem('access_token', token);
    }, token);

    // Set token in cookies for SSR
    await this.context.addCookies([{
      name: 'access_token',
      value: token,
      domain: 'localhost',
      path: '/',
      expires: Math.floor(Date.now() / 1000) + 3600, // 1 hour
      httpOnly: false,
      secure: false,
      sameSite: 'Lax'
    }]);

    // Set Authorization header for API requests
    await this.context.setExtraHTTPHeaders({
      'Authorization': `Bearer ${token}`
    });
  }

  /**
   * Clear authentication data
   */
  async clearAuth(): Promise<void> {
    await this.page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
    
    // Clear cookies
    await this.context.clearCookies();
    
    // Clear headers
    await this.context.setExtraHTTPHeaders({});
  }

  /**
   * Login using the development token (recommended for tests)
   */
  async loginWithDevToken(): Promise<void> {
    await this.setAuthToken();
    
    // Verify token is working by checking a protected endpoint
    const response = await this.page.request.get('/api/v1/auth/me');
    if (!response.ok()) {
      throw new Error(`Auth token validation failed: ${response.status()}`);
    }
  }

  /**
   * Login using email/password form (for testing login flow)
   */
  async loginWithCredentials(email: string, password: string): Promise<void> {
    await this.page.goto('/login');
    await this.page.waitForLoadState('networkidle');

    // Fill login form
    await this.page.fill('[data-testid="email-input"]', email);
    await this.page.fill('[data-testid="password-input"]', password);
    
    // Submit login form
    await this.page.click('[data-testid="login-submit"]');
    
    // Wait for successful login redirect
    await this.page.waitForURL('/dashboard', { timeout: 30000 });
  }

  /**
   * Verify user is authenticated and has admin role
   */
  async verifyAdminAuth(): Promise<void> {
    // Check auth state from API
    const response = await this.page.request.get('/api/v1/auth/me');
    if (!response.ok()) {
      throw new Error(`Authentication check failed: ${response.status()}`);
    }

    const user = await response.json();
    if (!user || user.email !== AuthUtils.ADMIN_CREDENTIALS.email) {
      throw new Error('Not logged in as admin user');
    }
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    // Try to logout through UI if on dashboard
    if (this.page.url().includes('/dashboard')) {
      try {
        await this.page.click('[data-testid="user-menu"]');
        await this.page.click('[data-testid="logout-button"]');
        await this.page.waitForURL('/login', { timeout: 10000 });
      } catch {
        // If UI logout fails, clear auth manually
        await this.clearAuth();
        await this.page.goto('/login');
      }
    } else {
      // Clear auth and redirect to login
      await this.clearAuth();
      await this.page.goto('/login');
    }
  }

  /**
   * Check if user is currently logged in
   */
  async isLoggedIn(): Promise<boolean> {
    try {
      const response = await this.page.request.get('/api/v1/auth/me');
      return response.ok();
    } catch {
      return false;
    }
  }

  /**
   * Wait for authentication state to be ready
   */
  async waitForAuthReady(): Promise<void> {
    // Wait for auth context to be initialized
    await this.page.waitForFunction(() => {
      return window.localStorage.getItem('access_token') !== null;
    }, { timeout: 10000 });

    // Verify with API
    await this.verifyAdminAuth();
  }

  /**
   * Setup authentication for test suite
   */
  async setupAuth(): Promise<void> {
    await this.loginWithDevToken();
    await this.waitForAuthReady();
  }
}

/**
 * Global auth setup for test fixtures
 */
export async function setupGlobalAuth(page: Page): Promise<AuthUtils> {
  const authUtils = new AuthUtils(page);
  await authUtils.setupAuth();
  return authUtils;
}