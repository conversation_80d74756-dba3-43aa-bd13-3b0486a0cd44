/**
 * Test data factories and fixtures for E2E tests
 */

export interface TestCandidate {
  email: string;
  full_name: string;
  phone_number?: string;
  location?: string;
  skills: string[];
  experience_years?: number;
  education_level?: 'high_school' | 'bachelors' | 'masters' | 'phd';
  current_position?: string;
  expected_salary_min?: number;
  expected_salary_max?: number;
}

export interface TestPosition {
  title: string;
  department: string;
  location: string;
  employment_type: 'full_time' | 'part_time' | 'contract';
  experience_level: 'entry' | 'mid' | 'senior' | 'lead';
  salary_min?: number;
  salary_max?: number;
  description: string;
  requirements: string[];
  benefits?: string[];
  remote_work_allowed: boolean;
}

export interface TestJobDescription {
  title: string;
  company: string;
  location: string;
  description: string;
  requirements: string[];
  skills: string[];
  salary_range?: string;
}

/**
 * Test data factory for creating candidates
 */
export class TestDataFactory {
  private static candidateCounter = 0;
  private static positionCounter = 0;

  /**
   * Generate a test candidate with optional overrides
   */
  static createCandidate(overrides?: Partial<TestCandidate>): TestCandidate {
    this.candidateCounter++;
    
    const defaults: TestCandidate = {
      email: `test.candidate${this.candidateCounter}@example.com`,
      full_name: `Test Candidate ${this.candidateCounter}`,
      phone_number: `******-0${String(this.candidateCounter).padStart(3, '0')}`,
      location: 'San Francisco, CA',
      skills: ['JavaScript', 'Python', 'React', 'Node.js'],
      experience_years: 3,
      education_level: 'bachelors',
      current_position: 'Software Developer',
      expected_salary_min: 80000,
      expected_salary_max: 120000
    };

    return { ...defaults, ...overrides };
  }

  /**
   * Generate a test position with optional overrides
   */
  static createPosition(overrides?: Partial<TestPosition>): TestPosition {
    this.positionCounter++;
    
    const defaults: TestPosition = {
      title: `Senior Developer ${this.positionCounter}`,
      department: 'Engineering',
      location: 'San Francisco, CA',
      employment_type: 'full_time',
      experience_level: 'senior',
      salary_min: 100000,
      salary_max: 150000,
      description: `We are looking for a talented Senior Developer ${this.positionCounter} to join our growing team...`,
      requirements: [
        '5+ years of software development experience',
        'Strong knowledge of JavaScript and React',
        'Experience with cloud platforms (AWS, GCP, Azure)',
        'Excellent communication skills'
      ],
      benefits: [
        'Health, dental, and vision insurance',
        'Flexible working hours',
        'Remote work options',
        '401(k) with company matching'
      ],
      remote_work_allowed: true
    };

    return { ...defaults, ...overrides };
  }

  /**
   * Generate a test job description for OCR/parsing tests
   */
  static createJobDescription(overrides?: Partial<TestJobDescription>): TestJobDescription {
    const defaults: TestJobDescription = {
      title: 'Full Stack Developer',
      company: 'TechCorp Inc.',
      location: 'Remote / San Francisco, CA',
      description: 'Join our innovative team as a Full Stack Developer and help build cutting-edge web applications...',
      requirements: [
        'Bachelor\'s degree in Computer Science or related field',
        '3+ years of full-stack development experience',
        'Proficiency in React, Node.js, and PostgreSQL',
        'Experience with cloud deployment and CI/CD'
      ],
      skills: ['React', 'Node.js', 'PostgreSQL', 'AWS', 'Docker', 'TypeScript'],
      salary_range: '$90,000 - $130,000'
    };

    return { ...defaults, ...overrides };
  }

  /**
   * Generate multiple candidates for bulk testing
   */
  static createCandidates(count: number, baseOverrides?: Partial<TestCandidate>): TestCandidate[] {
    return Array.from({ length: count }, (_, index) => 
      this.createCandidate({
        ...baseOverrides,
        email: `bulk.candidate${this.candidateCounter + index}@example.com`,
        full_name: `Bulk Candidate ${this.candidateCounter + index}`
      })
    );
  }

  /**
   * Generate multiple positions for bulk testing
   */
  static createPositions(count: number, baseOverrides?: Partial<TestPosition>): TestPosition[] {
    return Array.from({ length: count }, (_, index) => 
      this.createPosition({
        ...baseOverrides,
        title: `Position ${this.positionCounter + index}`,
      })
    );
  }

  /**
   * Reset counters for clean test state
   */
  static reset(): void {
    this.candidateCounter = 0;
    this.positionCounter = 0;
  }
}

/**
 * Pre-defined test data sets for common scenarios
 */
export const TEST_DATA = {
  candidates: {
    junior: TestDataFactory.createCandidate({
      experience_years: 1,
      education_level: 'bachelors',
      skills: ['JavaScript', 'HTML', 'CSS', 'React'],
      expected_salary_min: 50000,
      expected_salary_max: 70000
    }),
    senior: TestDataFactory.createCandidate({
      experience_years: 8,
      education_level: 'masters',
      skills: ['JavaScript', 'Python', 'React', 'Node.js', 'AWS', 'Docker', 'Kubernetes'],
      current_position: 'Senior Software Engineer',
      expected_salary_min: 120000,
      expected_salary_max: 160000
    }),
    lead: TestDataFactory.createCandidate({
      experience_years: 12,
      education_level: 'masters',
      skills: ['Leadership', 'Architecture', 'JavaScript', 'Python', 'AWS', 'Team Management'],
      current_position: 'Engineering Lead',
      expected_salary_min: 150000,
      expected_salary_max: 200000
    })
  },
  positions: {
    entry: TestDataFactory.createPosition({
      title: 'Junior Developer',
      experience_level: 'entry',
      salary_min: 60000,
      salary_max: 80000,
      requirements: [
        '1-2 years of development experience',
        'Knowledge of JavaScript and web technologies',
        'Eagerness to learn and grow'
      ]
    }),
    senior: TestDataFactory.createPosition({
      title: 'Senior Software Engineer',
      experience_level: 'senior',
      salary_min: 120000,
      salary_max: 160000
    }),
    lead: TestDataFactory.createPosition({
      title: 'Engineering Team Lead',
      experience_level: 'lead',
      salary_min: 150000,
      salary_max: 200000,
      requirements: [
        '8+ years of software development experience',
        'Proven leadership and mentoring experience',
        'Strong architectural and design skills',
        'Experience with team management'
      ]
    })
  }
};

/**
 * File upload test data
 */
export const TEST_FILES = {
  resume: {
    valid_pdf: 'test-data/resume-sample.pdf',
    valid_docx: 'test-data/resume-sample.docx',
    invalid_format: 'test-data/invalid-file.txt',
    large_file: 'test-data/large-resume.pdf'
  },
  jobDescription: {
    valid_pdf: 'test-data/job-description-sample.pdf',
    valid_text: 'test-data/job-description-sample.txt'
  }
};

/**
 * API response mocks for testing
 */
export const MOCK_RESPONSES = {
  candidates: {
    list: {
      items: [TEST_DATA.candidates.junior, TEST_DATA.candidates.senior],
      total: 2,
      skip: 0,
      limit: 10
    },
    single: TEST_DATA.candidates.senior
  },
  positions: {
    list: {
      items: [TEST_DATA.positions.entry, TEST_DATA.positions.senior],
      total: 2,
      skip: 0,
      limit: 10
    },
    single: TEST_DATA.positions.senior
  },
  analytics: {
    overview: {
      total_candidates: 150,
      total_positions: 25,
      active_matches: 45,
      success_rate: 0.78,
      avg_time_to_hire: 14
    }
  },
  reports: {
    statistics: {
      total_reports: 23,
      generated_this_month: 8,
      most_popular_type: 'candidate_summary'
    }
  }
};