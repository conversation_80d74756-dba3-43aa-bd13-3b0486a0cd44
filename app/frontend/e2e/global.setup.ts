import { chromium, FullConfig } from '@playwright/test';
import { AuthUtils } from './utils/auth';

/**
 * Global setup for E2E tests
 * Runs once before all tests to initialize the testing environment
 */
async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting TalentForge Pro E2E Test Suite Setup...');

  // Create a browser instance for setup tasks
  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // 1. Verify application is accessible
    console.log('✅ Checking application accessibility...');
    const response = await page.goto(config.projects[0].use?.baseURL || 'http://localhost:8088');
    
    if (!response || !response.ok()) {
      throw new Error(`Application not accessible: ${response?.status()}`);
    }

    // 2. Initialize authentication
    console.log('✅ Setting up authentication...');
    const authUtils = new AuthUtils(page);
    await authUtils.loginWithDevToken();
    await authUtils.verifyAdminAuth();
    
    // 3. Check API health
    console.log('✅ Checking API health...');
    const healthResponse = await page.request.get('/api/v1/health');
    if (!healthResponse.ok()) {
      console.warn('⚠️  API health check failed, but continuing with tests');
    }

    // 4. Prepare test data if needed
    console.log('✅ Preparing test environment...');
    
    // Create test results directories
    await page.evaluate(() => {
      // Any client-side setup can go here
      console.log('Client-side setup completed');
    });

    console.log('✅ Global setup completed successfully!');

  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  } finally {
    await browser.close();
  }
}

export default globalSetup;