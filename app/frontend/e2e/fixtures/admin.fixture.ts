import { test as base } from '@playwright/test';
import { AuthUtils } from '../utils/auth';
import { DashboardPage } from '../pages/DashboardPage';
import { CandidatesPage } from '../pages/CandidatesPage';
import { PositionsPage } from '../pages/PositionsPage';
import { AnalyticsPage } from '../pages/AnalyticsPage';

/**
 * Test fixtures for admin functionality
 * Provides authenticated page objects for admin user tests
 */
type AdminFixtures = {
  authUtils: AuthUtils;
  dashboardPage: DashboardPage;
  candidatesPage: CandidatesPage;
  positionsPage: PositionsPage;
  analyticsPage: AnalyticsPage;
};

/**
 * Extended test with admin fixtures
 */
export const adminTest = base.extend<AdminFixtures>({
  /**
   * Authentication utilities fixture
   */
  authUtils: async ({ page }, use) => {
    const authUtils = new AuthUtils(page);
    await authUtils.setupAuth();
    await use(authUtils);
  },

  /**
   * Dashboard page fixture - authenticated
   */
  dashboardPage: async ({ page, authUtils }, use) => {
    const dashboardPage = new DashboardPage(page);
    await use(dashboardPage);
  },

  /**
   * Candidates page fixture - authenticated
   */
  candidatesPage: async ({ page, authUtils }, use) => {
    const candidatesPage = new CandidatesPage(page);
    await use(candidatesPage);
  },

  /**
   * Positions page fixture - authenticated
   */
  positionsPage: async ({ page, authUtils }, use) => {
    const positionsPage = new PositionsPage(page);
    await use(positionsPage);
  },

  /**
   * Analytics page fixture - authenticated
   */
  analyticsPage: async ({ page, authUtils }, use) => {
    const analyticsPage = new AnalyticsPage(page);
    await use(analyticsPage);
  },
});

/**
 * Expect function from base test
 */
export { expect } from '@playwright/test';