import { chromium, FullConfig } from '@playwright/test';

/**
 * Global teardown for E2E tests
 * Runs once after all tests to clean up the testing environment
 */
async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting TalentForge Pro E2E Test Suite Teardown...');

  const browser = await chromium.launch();
  const page = await browser.newPage();

  try {
    // 1. Clean up test data if needed
    console.log('✅ Cleaning up test data...');
    
    // You could add cleanup API calls here if needed
    // await page.request.delete('/api/v1/test-data/cleanup');

    // 2. Generate test summary
    console.log('✅ Generating test summary...');
    
    // 3. Clear any persistent state
    await page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });

    console.log('✅ Global teardown completed successfully!');

  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error as teardown failures shouldn't fail the test suite
  } finally {
    await browser.close();
  }
}

export default globalTeardown;