#!/bin/bash

# TalentForge Pro E2E Test Runner
# Comprehensive E2E testing suite execution script

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BASE_URL="${PLAYWRIGHT_BASE_URL:-http://localhost:8088}"
TIMEOUT="${PLAYWRIGHT_TIMEOUT:-30000}"
RETRIES="${PLAYWRIGHT_RETRIES:-1}"
WORKERS="${PLAYWRIGHT_WORKERS:-2}"

# Test categories
CATEGORIES=("auth" "candidates" "positions" "analytics" "admin")

print_banner() {
    echo -e "${BLUE}"
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║                TalentForge Pro E2E Test Suite                ║"
    echo "║                     Chrome-Only Testing                     ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

check_prerequisites() {
    echo -e "${YELLOW}🔍 Checking prerequisites...${NC}"
    
    # Check if Docker services are running
    if ! curl -s "$BASE_URL" > /dev/null; then
        echo -e "${RED}❌ Application not accessible at $BASE_URL${NC}"
        echo -e "${YELLOW}Please run 'make up' to start Docker services${NC}"
        exit 1
    fi
    
    # Check if Playwright is installed
    if ! npx playwright --version > /dev/null 2>&1; then
        echo -e "${RED}❌ Playwright not installed${NC}"
        echo -e "${YELLOW}Installing Playwright...${NC}"
        pnpm playwright install --with-deps
    fi
    
    # Check API health
    if ! curl -s "$BASE_URL/api/v1/health" > /dev/null; then
        echo -e "${YELLOW}⚠️  API health check failed, but continuing...${NC}"
    fi
    
    echo -e "${GREEN}✅ Prerequisites check passed${NC}"
}

setup_test_environment() {
    echo -e "${YELLOW}🛠️  Setting up test environment...${NC}"
    
    # Create test results directories
    mkdir -p test-results/{screenshots,videos,html-report}
    
    # Set environment variables
    export PLAYWRIGHT_BASE_URL="$BASE_URL"
    export PLAYWRIGHT_TIMEOUT="$TIMEOUT"
    
    echo -e "${GREEN}✅ Test environment ready${NC}"
}

run_test_category() {
    local category=$1
    local description=$2
    
    echo -e "${BLUE}🧪 Running $description tests...${NC}"
    
    if npx playwright test "tests/$category" \
        --reporter=list \
        --retries="$RETRIES" \
        --workers="$WORKERS"; then
        echo -e "${GREEN}✅ $description tests passed${NC}"
        return 0
    else
        echo -e "${RED}❌ $description tests failed${NC}"
        return 1
    fi
}

run_all_tests() {
    echo -e "${BLUE}🚀 Running complete E2E test suite...${NC}"
    
    local failed_categories=()
    local start_time=$(date +%s)
    
    # Authentication Tests
    if ! run_test_category "auth" "Authentication"; then
        failed_categories+=("Authentication")
    fi
    
    # Candidate Management Tests
    if ! run_test_category "candidates" "Candidate Management"; then
        failed_categories+=("Candidate Management")
    fi
    
    # Position Management Tests  
    if ! run_test_category "positions" "Position Management"; then
        failed_categories+=("Position Management")
    fi
    
    # Analytics Dashboard Tests
    if ! run_test_category "analytics" "Analytics Dashboard"; then
        failed_categories+=("Analytics Dashboard")
    fi
    
    # Admin Integration & QA Tests
    if ! run_test_category "admin" "Admin Integration & QA"; then
        failed_categories+=("Admin Integration & QA")
    fi
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo -e "${BLUE}📊 Test Results Summary${NC}"
    echo "─────────────────────────"
    echo -e "Total Duration: ${duration}s"
    echo -e "Failed Categories: ${#failed_categories[@]}"
    
    if [ ${#failed_categories[@]} -eq 0 ]; then
        echo -e "${GREEN}🎉 All test categories passed!${NC}"
        return 0
    else
        echo -e "${RED}❌ Failed categories: ${failed_categories[*]}${NC}"
        return 1
    fi
}

run_smoke_tests() {
    echo -e "${YELLOW}💨 Running smoke tests (critical paths only)...${NC}"
    
    npx playwright test \
        --grep="@smoke|should display|should authenticate|should create|should navigate" \
        --reporter=list \
        --retries=0 \
        --workers=1
}

run_visual_tests() {
    echo -e "${YELLOW}🎨 Running visual regression tests...${NC}"
    
    npx playwright test "tests/admin/visual-regression.spec.ts" \
        --reporter=list \
        --retries=0 \
        --workers=1
}

run_performance_tests() {
    echo -e "${YELLOW}⚡ Running performance tests...${NC}"
    
    npx playwright test "tests/admin/performance-monitoring.spec.ts" \
        --reporter=list \
        --retries=0 \
        --workers=1
}

generate_report() {
    echo -e "${YELLOW}📋 Generating test report...${NC}"
    
    # Generate HTML report
    npx playwright show-report &
    
    echo -e "${GREEN}✅ Test report available at: test-results/html-report/index.html${NC}"
}

cleanup() {
    echo -e "${YELLOW}🧹 Cleaning up...${NC}"
    
    # Kill any remaining processes
    pkill -f "playwright" 2>/dev/null || true
    
    echo -e "${GREEN}✅ Cleanup completed${NC}"
}

show_help() {
    echo "TalentForge Pro E2E Test Runner"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  all               Run complete E2E test suite (default)"
    echo "  smoke             Run smoke tests only"
    echo "  visual            Run visual regression tests"  
    echo "  performance       Run performance tests"
    echo "  auth              Run authentication tests"
    echo "  candidates        Run candidate management tests"
    echo "  positions         Run position management tests"
    echo "  analytics         Run analytics dashboard tests"
    echo "  admin             Run admin integration tests"
    echo "  report            Generate and show HTML report"
    echo "  help              Show this help message"
    echo ""
    echo "Environment Variables:"
    echo "  PLAYWRIGHT_BASE_URL    Application base URL (default: http://localhost:8088)"
    echo "  PLAYWRIGHT_TIMEOUT     Test timeout in ms (default: 30000)"
    echo "  PLAYWRIGHT_RETRIES     Number of retries (default: 1)"
    echo "  PLAYWRIGHT_WORKERS     Number of parallel workers (default: 2)"
    echo ""
    echo "Examples:"
    echo "  $0                     # Run all tests"
    echo "  $0 smoke               # Run smoke tests"
    echo "  $0 auth                # Run authentication tests only"
    echo "  PLAYWRIGHT_WORKERS=1 $0 visual  # Run visual tests with single worker"
}

main() {
    local command="${1:-all}"
    
    print_banner
    
    case "$command" in
        "all")
            check_prerequisites
            setup_test_environment
            if run_all_tests; then
                generate_report
                exit 0
            else
                generate_report
                exit 1
            fi
            ;;
        "smoke")
            check_prerequisites
            setup_test_environment
            run_smoke_tests
            ;;
        "visual")
            check_prerequisites
            setup_test_environment
            run_visual_tests
            ;;
        "performance")
            check_prerequisites
            setup_test_environment
            run_performance_tests
            ;;
        "auth")
            check_prerequisites
            setup_test_environment
            run_test_category "auth" "Authentication"
            ;;
        "candidates")
            check_prerequisites
            setup_test_environment
            run_test_category "candidates" "Candidate Management"
            ;;
        "positions")
            check_prerequisites
            setup_test_environment
            run_test_category "positions" "Position Management"
            ;;
        "analytics")
            check_prerequisites
            setup_test_environment
            run_test_category "analytics" "Analytics Dashboard"
            ;;
        "admin")
            check_prerequisites
            setup_test_environment
            run_test_category "admin" "Admin Integration & QA"
            ;;
        "report")
            generate_report
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}❌ Unknown command: $command${NC}"
            show_help
            exit 1
            ;;
    esac
}

# Trap cleanup on script exit
trap cleanup EXIT

# Run main function with all arguments
main "$@"