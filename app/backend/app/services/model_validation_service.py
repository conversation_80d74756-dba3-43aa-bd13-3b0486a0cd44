"""
Comprehensive Database Model Relationship Validation Service

This service validates all SQLAlchemy model relationships to prevent
mapping errors and ensure data integrity across the application.

Created: August 27, 2025
Purpose: Prevent SQLAlchemy model mapping issues after service consolidation
"""

import logging
from typing import Dict, List, Any, Optional
from sqlalchemy import inspect
from sqlalchemy.orm import relationship
from sqlalchemy.exc import InvalidRequestError, ArgumentError
from app.core.database import AsyncSessionLocal, engine
from app.models import *  # Import all models

logger = logging.getLogger(__name__)


class ModelValidationService:
    """Comprehensive model relationship validation service"""
    
    def __init__(self):
        """Initialize model validation service"""
        self.session = None
        self.validation_results = {
            "passed": [],
            "failed": [],
            "warnings": [],
            "critical_errors": []
        }
    
    async def validate_all_models(self) -> Dict[str, Any]:
        """
        Validate all model relationships and return comprehensive results
        
        Returns:
            Dict containing validation results with passed/failed/warnings
        """
        logger.info("🔍 Starting comprehensive model relationship validation...")
        
        try:
            # Initialize database session
            self.session = AsyncSessionLocal()
            
            # Get all model classes
            models = self._discover_models()
            logger.info(f"📋 Found {len(models)} model classes to validate")
            
            # Validate each model
            for model_class in models:
                try:
                    self._validate_model_relationships(model_class)
                except Exception as e:
                    error_msg = f"Critical error validating {model_class.__name__}: {str(e)}"
                    self.validation_results["critical_errors"].append(error_msg)
                    logger.error(f"🚨 {error_msg}")
            
            # Validate cross-model consistency
            self._validate_cross_model_consistency(models)
            
            # Generate summary
            return self._generate_validation_report()
            
        except Exception as e:
            logger.error(f"❌ Model validation failed: {str(e)}")
            return {
                "status": "CRITICAL_ERROR",
                "error": str(e),
                "results": self.validation_results
            }
        finally:
            if self.session:
                await self.session.close()
    
    def _discover_models(self) -> List[Any]:
        """Discover all SQLAlchemy model classes"""
        try:
            from app.models.base import BaseModel
            from app.core.database import Base
            
            # Get all subclasses of Base that are actual models
            models = []
            
            # Use SQLAlchemy's registry to get all mapped classes
            mapper_registry = Base.registry
            for mapper in mapper_registry.mappers:
                model_class = mapper.class_
                if hasattr(model_class, '__tablename__'):
                    models.append(model_class)
                    
            return models
            
        except Exception as e:
            logger.error(f"Failed to discover models: {e}")
            return []
    
    def _validate_model_relationships(self, model_class: Any) -> None:
        """Validate relationships for a specific model class"""
        model_name = model_class.__name__
        logger.debug(f"🔍 Validating model: {model_name}")
        
        try:
            # Get SQLAlchemy mapper for the model
            mapper = inspect(model_class)
            
            # Validate each relationship
            for relationship_name, relationship_prop in mapper.relationships.items():
                try:
                    self._validate_single_relationship(
                        model_class, model_name, relationship_name, relationship_prop
                    )
                except Exception as e:
                    error_msg = f"Relationship error in {model_name}.{relationship_name}: {str(e)}"
                    self.validation_results["failed"].append(error_msg)
                    logger.warning(f"⚠️ {error_msg}")
            
            # Test model instantiation
            try:
                # Try to create model instance without triggering database operations
                model_instance = model_class()
                success_msg = f"✅ Model {model_name}: Structure validation passed"
                self.validation_results["passed"].append(success_msg)
                logger.debug(success_msg)
                
            except Exception as e:
                error_msg = f"Model instantiation failed for {model_name}: {str(e)}"
                self.validation_results["failed"].append(error_msg)
                logger.warning(f"⚠️ {error_msg}")
                
        except Exception as e:
            error_msg = f"Mapper inspection failed for {model_name}: {str(e)}"
            self.validation_results["critical_errors"].append(error_msg)
            logger.error(f"🚨 {error_msg}")
    
    def _validate_single_relationship(
        self, 
        model_class: Any, 
        model_name: str, 
        relationship_name: str, 
        relationship_prop: Any
    ) -> None:
        """Validate a single relationship property"""
        
        # Get relationship configuration
        related_class = relationship_prop.entity.class_
        related_class_name = related_class.__name__
        back_populates = relationship_prop.back_populates
        
        logger.debug(f"  🔗 {model_name}.{relationship_name} -> {related_class_name}")
        
        # Check if back_populates is configured correctly
        if back_populates:
            related_mapper = inspect(related_class)
            if back_populates not in related_mapper.relationships:
                error_msg = f"Missing back_populates: {related_class_name}.{back_populates} for {model_name}.{relationship_name}"
                raise InvalidRequestError(error_msg)
            
            # Verify reciprocal relationship
            reciprocal_rel = related_mapper.relationships[back_populates]
            if reciprocal_rel.entity.class_ != model_class:
                error_msg = f"Reciprocal relationship mismatch: {related_class_name}.{back_populates} points to {reciprocal_rel.entity.class_.__name__} instead of {model_name}"
                raise InvalidRequestError(error_msg)
        
        # Check foreign key configuration
        if relationship_prop.direction.name == 'MANYTOONE':
            # For many-to-one relationships, check foreign key exists
            foreign_keys = relationship_prop.local_columns
            if not foreign_keys:
                warning_msg = f"No foreign key columns found for {model_name}.{relationship_name}"
                self.validation_results["warnings"].append(warning_msg)
                logger.debug(f"⚠️ {warning_msg}")
        
        success_msg = f"✅ Relationship {model_name}.{relationship_name} -> {related_class_name}: Valid"
        logger.debug(f"    {success_msg}")
    
    def _validate_cross_model_consistency(self, models: List[Any]) -> None:
        """Validate consistency across all models"""
        logger.info("🔍 Validating cross-model consistency...")
        
        # Check for circular imports or dependency issues
        try:
            # Try to access all model classes
            model_names = []
            for model_class in models:
                model_names.append(model_class.__name__)
                # Try to access the mapper to trigger any import errors
                inspect(model_class)
            
            success_msg = f"✅ Cross-model consistency check passed for {len(model_names)} models"
            self.validation_results["passed"].append(success_msg)
            logger.info(success_msg)
            
        except Exception as e:
            error_msg = f"Cross-model consistency check failed: {str(e)}"
            self.validation_results["critical_errors"].append(error_msg)
            logger.error(f"🚨 {error_msg}")
    
    def _generate_validation_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        total_checks = (
            len(self.validation_results["passed"]) + 
            len(self.validation_results["failed"]) + 
            len(self.validation_results["warnings"]) + 
            len(self.validation_results["critical_errors"])
        )
        
        # Determine overall status
        if self.validation_results["critical_errors"]:
            status = "CRITICAL_FAILED"
            status_emoji = "🚨"
        elif self.validation_results["failed"]:
            status = "FAILED"
            status_emoji = "❌"
        elif self.validation_results["warnings"]:
            status = "PASSED_WITH_WARNINGS"
            status_emoji = "⚠️"
        else:
            status = "PASSED"
            status_emoji = "✅"
        
        report = {
            "status": status,
            "status_emoji": status_emoji,
            "timestamp": logger.handlers[0].formatter.formatTime(logger.handlers[0], logger.makeRecord(
                logger.name, logging.INFO, "", 0, "", (), None
            )) if logger.handlers else "unknown",
            "summary": {
                "total_checks": total_checks,
                "passed": len(self.validation_results["passed"]),
                "failed": len(self.validation_results["failed"]),
                "warnings": len(self.validation_results["warnings"]),
                "critical_errors": len(self.validation_results["critical_errors"]),
                "success_rate": (len(self.validation_results["passed"]) / total_checks * 100) if total_checks > 0 else 0
            },
            "details": self.validation_results,
            "recommendations": self._generate_recommendations()
        }
        
        # Log summary
        logger.info(f"\n{'='*60}")
        logger.info(f"📊 MODEL VALIDATION REPORT")
        logger.info(f"{'='*60}")
        logger.info(f"Status: {status_emoji} {status}")
        logger.info(f"Total Checks: {total_checks}")
        logger.info(f"✅ Passed: {len(self.validation_results['passed'])}")
        logger.info(f"❌ Failed: {len(self.validation_results['failed'])}")
        logger.info(f"⚠️ Warnings: {len(self.validation_results['warnings'])}")
        logger.info(f"🚨 Critical: {len(self.validation_results['critical_errors'])}")
        logger.info(f"📈 Success Rate: {report['summary']['success_rate']:.1f}%")
        logger.info(f"{'='*60}")
        
        if self.validation_results["critical_errors"]:
            logger.error("🚨 CRITICAL ERRORS FOUND:")
            for error in self.validation_results["critical_errors"]:
                logger.error(f"  • {error}")
        
        if self.validation_results["failed"]:
            logger.warning("❌ FAILURES FOUND:")
            for failure in self.validation_results["failed"]:
                logger.warning(f"  • {failure}")
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        if self.validation_results["critical_errors"]:
            recommendations.append("🚨 CRITICAL: Fix model mapping errors immediately - authentication will fail")
            recommendations.append("🔧 Run database migration: `make db-migrate`")
            recommendations.append("🔍 Check import statements and model definitions")
        
        if self.validation_results["failed"]:
            recommendations.append("⚠️ Fix relationship mapping issues to prevent runtime errors")
            recommendations.append("🔗 Verify back_populates relationships are bidirectional")
            recommendations.append("🗃️ Check foreign key column definitions")
        
        if self.validation_results["warnings"]:
            recommendations.append("📝 Review warnings for potential optimization opportunities")
            recommendations.append("🏗️ Consider adding explicit foreign key constraints")
        
        if not any(self.validation_results.values()):
            recommendations.append("🎉 All model relationships are properly configured!")
            recommendations.append("✅ Regular validation recommended after model changes")
        
        return recommendations
    
    async def health_check(self) -> Dict[str, Any]:
        """Quick health check for model validation service"""
        try:
            # Run a minimal validation test
            results = await self.validate_all_models()
            
            return {
                "service": "model_validation",
                "status": "healthy" if results["status"] == "PASSED" else "degraded",
                "last_validation": results["timestamp"],
                "quick_stats": results["summary"]
            }
            
        except Exception as e:
            return {
                "service": "model_validation",
                "status": "unhealthy",
                "error": str(e)
            }


# Global service instance
model_validation_service = ModelValidationService()


async def validate_models_on_startup() -> bool:
    """
    Startup validation function for use in FastAPI lifespan
    
    Returns:
        bool: True if validation passed, False if critical errors found
    """
    try:
        logger.info("🚀 Running model validation on application startup...")
        results = await model_validation_service.validate_all_models()
        
        if results["status"] == "CRITICAL_FAILED":
            logger.error("🚨 CRITICAL MODEL VALIDATION FAILED - Application startup blocked")
            logger.error("🔧 Fix model relationships before proceeding")
            return False
        elif results["status"] == "FAILED":
            logger.warning("⚠️ Model validation failed - Application may have issues")
            return False
        else:
            logger.info("✅ Model validation passed - Application ready")
            return True
            
    except Exception as e:
        logger.error(f"❌ Model validation error during startup: {e}")
        return False