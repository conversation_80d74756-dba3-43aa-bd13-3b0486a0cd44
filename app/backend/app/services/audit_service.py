"""
Audit Service for Event-Driven Activity Logging
"""
import json
import logging
from typing import Any, Dict, List, Optional
from datetime import datetime, timezone

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, desc, func
from fastapi import Request

from app.core.database import get_db
from app.models.audit_log import AuditLog
from app.models.user import User
from app.schemas.dashboard import DashboardActivity, ActivityType
from app.core.redis import RedisClient
from app.worker import celery_app

logger = logging.getLogger(__name__)


class AuditService:
    """
    Enhanced audit logging service with hybrid architecture:
    - Event-driven: Celery background tasks for heavy processing
    - Decorator pattern: Easy integration with existing endpoints
    - Redis queue: Minimal request latency impact
    """
    
    @staticmethod
    async def log_activity(
        operation: str,
        resource_type: str,
        resource_id: Optional[int] = None,
        user_id: Optional[int] = None,
        user_email: Optional[str] = None,
        status: str = "success",
        status_code: int = 200,
        details: Optional[Dict[str, Any]] = None,
        changes: Optional[Dict[str, Any]] = None,
        request: Optional[Request] = None,
        error_message: Optional[str] = None
    ) -> Optional[AuditLog]:
        """
        Log audit activity with enhanced context capture
        
        Args:
            operation: Operation identifier (e.g., "dashboard.stats.view")
            resource_type: Type of resource affected
            resource_id: ID of affected resource
            user_id: User performing the operation
            user_email: User email (for deletion-safe logging)
            status: Operation status (success, failure, error)
            status_code: HTTP status code
            details: Additional operation-specific context
            changes: Before/after values for updates
            request: FastAPI request object for context
            error_message: Error details if operation failed
        """
        try:
            # Extract request context if available
            http_method = None
            endpoint = None
            ip_address = None
            user_agent = None
            
            if request:
                http_method = request.method
                endpoint = str(request.url.path)
                ip_address = request.client.host if request.client else None
                user_agent = request.headers.get("user-agent")
            
            # Queue audit log creation for background processing
            audit_data = {
                "operation": operation,
                "resource_type": resource_type,
                "resource_id": resource_id,
                "user_id": user_id,
                "user_email": user_email,
                "http_method": http_method,
                "endpoint": endpoint,
                "ip_address": ip_address,
                "user_agent": user_agent,
                "status": status,
                "status_code": status_code,
                "details": details or {},
                "changes": changes,
                "error_message": error_message,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Queue for async processing via Celery
            try:
                create_audit_log_task.delay(audit_data)
                logger.debug(f"Queued audit log for operation: {operation}")
            except Exception as queue_error:
                # Fallback to direct database write if queue fails
                logger.warning(f"Queue failed, writing audit log directly: {queue_error}")
                audit_log = await AuditService._create_audit_log_direct(audit_data)
                return audit_log
            
        except Exception as e:
            logger.error(f"Failed to log audit activity: {str(e)}")
            return None
    
    @staticmethod
    async def _create_audit_log_direct(audit_data: Dict[str, Any]) -> Optional[AuditLog]:
        """Create audit log directly in database (fallback method)"""
        try:
            async for db in get_db():
                audit_log = AuditLog(**audit_data)
                db.add(audit_log)
                await db.commit()
                await db.refresh(audit_log)
                return audit_log
        except Exception as e:
            logger.error(f"Failed to create audit log directly: {str(e)}")
            return None
    
    @staticmethod
    async def get_dashboard_activities(
        db: AsyncSession,
        limit: int = 20,
        skip: int = 0,
        activity_type: Optional[ActivityType] = None,
        user_id: Optional[int] = None
    ) -> List[DashboardActivity]:
        """
        Get dashboard activities from audit logs
        
        Maps audit log operations to dashboard activities with enhanced context
        """
        try:
            # Build base query
            query = select(AuditLog).filter(
                and_(
                    AuditLog.status == "success",
                    AuditLog.operation.in_([
                        "candidate.create",
                        "candidate.register", 
                        "assessment.complete",
                        "assessment.generate",
                        "position.create",
                        "position.post",
                        "resume.upload",
                        "resume.parse",
                        "evaluation.generate",
                        "questionnaire.create"
                    ])
                )
            )
            
            # Filter by activity type if specified
            if activity_type:
                operation_map = {
                    ActivityType.CANDIDATE_ADDED: ["candidate.create", "candidate.register"],
                    ActivityType.ASSESSMENT_COMPLETED: ["assessment.complete", "assessment.generate"],
                    ActivityType.POSITION_CREATED: ["position.create", "position.post"],
                    ActivityType.RESUME_UPLOADED: ["resume.upload", "resume.parse"]
                }
                
                if activity_type in operation_map:
                    query = query.filter(AuditLog.operation.in_(operation_map[activity_type]))
            
            # Filter by user if specified
            if user_id:
                query = query.filter(AuditLog.user_id == user_id)
            
            # Order by timestamp and apply pagination
            query = query.order_by(desc(AuditLog.timestamp)).offset(skip).limit(limit)
            
            result = await db.execute(query)
            audit_logs = result.scalars().all()
            
            # Transform audit logs to dashboard activities
            activities = []
            for log in audit_logs:
                activity = await AuditService._transform_audit_log_to_activity(log)
                if activity:
                    activities.append(activity)
            
            return activities
            
        except Exception as e:
            logger.error(f"Failed to get dashboard activities: {str(e)}")
            return []
    
    @staticmethod
    async def _transform_audit_log_to_activity(log: AuditLog) -> Optional[DashboardActivity]:
        """Transform audit log entry to dashboard activity"""
        try:
            # Map operations to activity types and generate human-readable content
            operation_mapping = {
                "candidate.create": ("candidate_added", "新候选人注册", "candidate"),
                "candidate.register": ("candidate_added", "新候选人注册", "candidate"),
                "assessment.complete": ("assessment_completed", "评估完成", "assessment"),
                "assessment.generate": ("assessment_completed", "评估生成", "assessment"),
                "position.create": ("position_created", "职位创建", "position"),
                "position.post": ("position_created", "职位发布", "position"),
                "resume.upload": ("resume_uploaded", "简历上传", "resume"),
                "resume.parse": ("resume_uploaded", "简历解析", "resume"),
                "evaluation.generate": ("assessment_completed", "智能评估生成", "evaluation"),
                "questionnaire.create": ("assessment_completed", "问卷创建", "questionnaire")
            }
            
            if log.operation not in operation_mapping:
                return None
            
            activity_type, title_template, entity_type = operation_mapping[log.operation]
            
            # Generate description with context
            description = AuditService._generate_activity_description(log, title_template)
            
            # Create globally unique activity ID
            activity_id = f"{activity_type}_{log.resource_id or log.id}_{int(log.timestamp.timestamp())}"
            
            return DashboardActivity(
                id=activity_id,
                type=activity_type,
                title=title_template,
                description=description,
                timestamp=log.timestamp.isoformat(),
                user_id=str(log.user_id) if log.user_id else "system",
                user_name=log.user_email or "系统用户",
                metadata={
                    "audit_id": str(log.id),
                    "operation": log.operation,
                    "resource_type": log.resource_type,
                    "resource_id": log.resource_id,
                    "status_code": log.status_code,
                    "details": log.details or {}
                }
            )
            
        except Exception as e:
            logger.error(f"Failed to transform audit log to activity: {str(e)}")
            return None
    
    @staticmethod
    def _generate_activity_description(log: AuditLog, title_template: str) -> str:
        """Generate human-readable activity description"""
        try:
            user_name = log.user_email or "系统"
            
            # Extract meaningful details from log
            details = log.details or {}
            
            # Generate context-specific descriptions
            if log.operation in ["candidate.create", "candidate.register"]:
                candidate_name = details.get("candidate_name", "新候选人")
                return f"{candidate_name} 加入人才库"
            
            elif log.operation in ["assessment.complete", "assessment.generate"]:
                candidate_name = details.get("candidate_name", "候选人")
                assessment_type = details.get("assessment_type", "能力评估")
                return f"{candidate_name} 完成{assessment_type}"
            
            elif log.operation in ["position.create", "position.post"]:
                position_title = details.get("position_title", details.get("title", "新职位"))
                return f"发布职位: {position_title}"
            
            elif log.operation in ["resume.upload", "resume.parse"]:
                candidate_name = details.get("candidate_name", "候选人")
                return f"{candidate_name} 上传简历"
            
            else:
                return f"{user_name} 执行了 {title_template}"
                
        except Exception as e:
            logger.error(f"Failed to generate activity description: {str(e)}")
            return title_template


@celery_app.task
def create_audit_log_task(audit_data: Dict[str, Any]):
    """Celery task for background audit log creation"""
    import asyncio
    
    async def _create_log():
        try:
            async for db in get_db():
                # Parse timestamp back to datetime
                if isinstance(audit_data.get("timestamp"), str):
                    audit_data["timestamp"] = datetime.fromisoformat(
                        audit_data["timestamp"].replace("Z", "+00:00")
                    )
                
                audit_log = AuditLog(**audit_data)
                db.add(audit_log)
                await db.commit()
                logger.debug(f"Created audit log for operation: {audit_data['operation']}")
        except Exception as e:
            logger.error(f"Failed to create audit log in background: {str(e)}")
    
    # Run the async function
    asyncio.run(_create_log())