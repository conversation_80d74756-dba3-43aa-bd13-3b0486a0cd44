"""
Backward compatibility shim for role service.
This file maintains compatibility after consolidation into permission_service.py
"""
from app.services.permission_service import permission_service

# Export the same service instance for backward compatibility
role_service = permission_service

# Export all role-related methods as module-level functions for compatibility
create_role = permission_service.create_role
get_role = permission_service.get_role
update_role = permission_service.update_role
delete_role = permission_service.delete_role
list_roles = permission_service.list_roles
assign_role = permission_service.assign_role_to_user
bulk_assign_role = permission_service.bulk_assign_role
get_default_role = permission_service.get_default_role
get_assignable_roles = permission_service.get_assignable_roles

__all__ = [
    'role_service',
    'create_role',
    'get_role', 
    'update_role',
    'delete_role',
    'list_roles',
    'assign_role',
    'bulk_assign_role',
    'get_default_role',
    'get_assignable_roles'
]