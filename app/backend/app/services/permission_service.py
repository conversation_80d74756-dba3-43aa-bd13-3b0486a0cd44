"""
Unified Permission and Role Management Service

This consolidated service provides:
- Permission management (CRUD operations, bulk operations)
- Role management (CRUD operations, role assignments)
- User permission checking and validation
- Role-based access control (RBAC) functionality
- System initialization for default roles and permissions

Consolidated from:
- permission.py (permission management)
- role.py (role management and RBAC)
"""
from typing import Any, Dict, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.exceptions import ConflictError, NotFoundError, PermissionError, ValidationError
from app.crud.permission import permission_crud
from app.crud.role import role_crud
from app.crud.user import user as user_crud
from app.models.permission import Permission
from app.models.role import Role
from app.models.user import User
from app.schemas.permission import (
    PermissionCreate,
    PermissionFilter,
    PermissionListResponse,
    PermissionResponse,
    PermissionUpdate,
)
from app.schemas.role import (
    BulkRoleAssignment,
    RoleAssignment,
    RoleCreate,
    RoleFilter,
    RoleListResponse,
    RoleResponse,
    RoleUpdate,
)
from app.schemas.user import UserResponse


class PermissionService:
    """Comprehensive service for permission and role management"""

    # =============================================
    # PERMISSION MANAGEMENT METHODS
    # =============================================

    async def get_permission(
        self,
        db: AsyncSession,
        permission_id: int
    ) -> PermissionResponse:
        """
        Get a permission by ID
        
        Args:
            db: Database session
            permission_id: Permission ID
            
        Returns:
            Permission response object
            
        Raises:
            NotFoundError: If permission not found
        """
        permission = await permission_crud.get(db, id=permission_id)
        if not permission:
            raise NotFoundError(f"Permission with id {permission_id} not found")

        return PermissionResponse.from_orm_with_str_id(permission)

    async def get_permission_by_code(
        self,
        db: AsyncSession,
        code: str
    ) -> PermissionResponse:
        """
        Get a permission by code
        
        Args:
            db: Database session
            code: Permission code
            
        Returns:
            Permission response object
            
        Raises:
            NotFoundError: If permission not found
        """
        permission = await permission_crud.get_by_code(db, code=code)
        if not permission:
            raise NotFoundError(f"Permission with code {code} not found")

        return PermissionResponse.from_orm_with_str_id(permission)

    async def list_permissions(
        self,
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        filters: PermissionFilter | None = None
    ) -> PermissionListResponse:
        """
        List permissions with filtering and pagination
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Optional filter parameters
            
        Returns:
            Paginated list of permissions
        """
        permissions = await permission_crud.get_multi_filtered(
            db,
            skip=skip,
            limit=limit,
            filters=filters
        )

        total = await permission_crud.count_filtered(db, filters=filters)

        items = [
            PermissionResponse.from_orm_with_str_id(permission)
            for permission in permissions
        ]

        return PermissionListResponse(
            items=items,
            total=total,
            skip=skip,
            limit=limit
        )

    async def create_permission(
        self,
        db: AsyncSession,
        permission_in: PermissionCreate
    ) -> PermissionResponse:
        """
        Create a new permission
        
        Args:
            db: Database session
            permission_in: Permission creation data
            
        Returns:
            Created permission
            
        Raises:
            ConflictError: If permission code already exists
        """
        # Check if permission code already exists
        existing = await permission_crud.get_by_code(db, code=permission_in.code)
        if existing:
            raise ConflictError(f"Permission with code {permission_in.code} already exists")

        permission = await permission_crud.create(db, obj_in=permission_in)
        return PermissionResponse.from_orm_with_str_id(permission)

    async def update_permission(
        self,
        db: AsyncSession,
        permission_id: int,
        permission_in: PermissionUpdate
    ) -> PermissionResponse:
        """
        Update a permission
        
        Args:
            db: Database session
            permission_id: Permission ID
            permission_in: Permission update data
            
        Returns:
            Updated permission
            
        Raises:
            NotFoundError: If permission not found
            PermissionError: If attempting to modify a system permission
            ConflictError: If new code already exists
        """
        permission = await permission_crud.get(db, id=permission_id)
        if not permission:
            raise NotFoundError(f"Permission with id {permission_id} not found")

        # Check if it's a system permission
        if permission.is_system:
            raise PermissionError("Cannot modify system permissions")

        # Check if new code conflicts with existing permission
        if permission_in.code and permission_in.code != permission.code:
            existing = await permission_crud.get_by_code(db, code=permission_in.code)
            if existing:
                raise ConflictError(f"Permission with code {permission_in.code} already exists")

        permission = await permission_crud.update(db, db_obj=permission, obj_in=permission_in)
        return PermissionResponse.from_orm_with_str_id(permission)

    async def delete_permission(
        self,
        db: AsyncSession,
        permission_id: int
    ) -> bool:
        """
        Delete a permission
        
        Args:
            db: Database session
            permission_id: Permission ID
            
        Returns:
            True if deleted successfully
            
        Raises:
            NotFoundError: If permission not found
            PermissionError: If attempting to delete a system permission
        """
        permission = await permission_crud.get(db, id=permission_id)
        if not permission:
            raise NotFoundError(f"Permission with id {permission_id} not found")

        # Check if it's a system permission
        if permission.is_system:
            raise PermissionError("Cannot delete system permissions")

        await permission_crud.remove(db, id=permission_id)
        return True

    async def bulk_create_permissions(
        self,
        db: AsyncSession,
        permissions: list[PermissionCreate]
    ) -> list[PermissionResponse]:
        """
        Create multiple permissions at once
        
        Args:
            db: Database session
            permissions: List of permissions to create
            
        Returns:
            List of created permissions
        """
        created = await permission_crud.bulk_create(db, permissions=permissions)
        return [
            PermissionResponse.from_orm_with_str_id(permission)
            for permission in created
        ]

    async def get_permissions_by_module(
        self,
        db: AsyncSession,
        module: str
    ) -> list[PermissionResponse]:
        """
        Get all permissions for a specific module
        
        Args:
            db: Database session
            module: Module name
            
        Returns:
            List of permissions for the module
        """
        permissions = await permission_crud.get_by_module(db, module=module)
        return [
            PermissionResponse.from_orm_with_str_id(permission)
            for permission in permissions
        ]

    async def get_active_permissions(
        self,
        db: AsyncSession
    ) -> list[PermissionResponse]:
        """
        Get all active permissions
        
        Args:
            db: Database session
            
        Returns:
            List of active permissions
        """
        permissions = await permission_crud.get_active_permissions(db)
        return [
            PermissionResponse.from_orm_with_str_id(permission)
            for permission in permissions
        ]

    async def group_permissions_by_module(
        self,
        db: AsyncSession
    ) -> dict[str, list[PermissionResponse]]:
        """
        Get permissions grouped by module
        
        Args:
            db: Database session
            
        Returns:
            Dictionary with module names as keys and permission lists as values
        """
        all_permissions = await permission_crud.get_active_permissions(db)

        grouped: dict[str, list[PermissionResponse]] = {}
        for permission in all_permissions:
            if permission.module not in grouped:
                grouped[permission.module] = []
            grouped[permission.module].append(
                PermissionResponse.from_orm_with_str_id(permission)
            )

        # Sort permissions within each module
        for module in grouped:
            grouped[module].sort(key=lambda p: (p.resource, p.action))

        return grouped

    async def check_permission_exists(
        self,
        db: AsyncSession,
        code: str
    ) -> bool:
        """
        Check if a permission exists by code
        
        Args:
            db: Database session
            code: Permission code
            
        Returns:
            True if permission exists
        """
        permission = await permission_crud.get_by_code(db, code=code)
        return permission is not None

    # =============================================
    # ROLE MANAGEMENT METHODS
    # =============================================

    async def get_role(
        self,
        db: AsyncSession,
        role_id: int
    ) -> RoleResponse:
        """
        Get a role by ID with permissions
        
        Args:
            db: Database session
            role_id: Role ID
            
        Returns:
            Role response object with permissions
            
        Raises:
            NotFoundError: If role not found
        """
        role = await role_crud.get_with_permissions(db, id=role_id)
        if not role:
            raise NotFoundError(f"Role with id {role_id} not found")

        # Get user count for this role
        user_count = await role_crud.get_user_count(db, role_id=role_id)

        return RoleResponse.from_orm_with_str_id(role, user_count)

    async def get_role_by_code(
        self,
        db: AsyncSession,
        code: str
    ) -> RoleResponse:
        """
        Get a role by code
        
        Args:
            db: Database session
            code: Role code
            
        Returns:
            Role response object
            
        Raises:
            NotFoundError: If role not found
        """
        role = await role_crud.get_by_code(db, code=code)
        if not role:
            raise NotFoundError(f"Role with code {code} not found")

        user_count = await role_crud.get_user_count(db, role_id=role.id)
        return RoleResponse.from_orm_with_str_id(role, user_count)

    async def list_roles(
        self,
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        filters: RoleFilter | None = None
    ) -> RoleListResponse:
        """
        List roles with filtering and pagination
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Optional filter parameters
            
        Returns:
            Paginated list of roles
        """
        roles = await role_crud.get_multi_filtered(
            db,
            skip=skip,
            limit=limit,
            filters=filters
        )

        total = await role_crud.count_filtered(db, filters=filters)

        # Get user counts for all roles
        items = []
        for role in roles:
            user_count = await role_crud.get_user_count(db, role_id=role.id)
            items.append(RoleResponse.from_orm_with_str_id(role, user_count))

        return RoleListResponse(
            items=items,
            total=total,
            skip=skip,
            limit=limit
        )

    async def create_role(
        self,
        db: AsyncSession,
        role_in: RoleCreate
    ) -> RoleResponse:
        """
        Create a new role with permissions
        
        Args:
            db: Database session
            role_in: Role creation data
            
        Returns:
            Created role
            
        Raises:
            ConflictError: If role code already exists
            ValidationError: If permission IDs are invalid
        """
        # Check if role code already exists
        existing = await role_crud.get_by_code(db, code=role_in.code)
        if existing:
            raise ConflictError(f"Role with code {role_in.code} already exists")

        # Validate permission IDs if provided
        if role_in.permission_ids:
            # Get ALL permissions to validate against (not just limited by count)
            permissions = await permission_crud.get_active_permissions(db)
            valid_ids = {p.id for p in permissions}
            invalid_ids = set(role_in.permission_ids) - valid_ids
            if invalid_ids:
                raise ValidationError("permission_ids", f"Invalid IDs: {invalid_ids}")

        role = await role_crud.create_with_permissions(db, obj_in=role_in)
        return RoleResponse.from_orm_with_str_id(role, 0)

    async def update_role(
        self,
        db: AsyncSession,
        role_id: int,
        role_in: RoleUpdate
    ) -> RoleResponse:
        """
        Update a role
        
        Args:
            db: Database session
            role_id: Role ID
            role_in: Role update data
            
        Returns:
            Updated role
            
        Raises:
            NotFoundError: If role not found
            PermissionError: If attempting to modify a system role
            ConflictError: If new code already exists
            ValidationError: If permission IDs are invalid
        """
        role = await role_crud.get_with_permissions(db, id=role_id)
        if not role:
            raise NotFoundError(f"Role with id {role_id} not found")

        # Check if it's a system role
        if role.is_system and not role_in.is_system:
            raise PermissionError("Cannot modify system roles")

        # Check if new code conflicts with existing role
        if role_in.code and role_in.code != role.code:
            existing = await role_crud.get_by_code(db, code=role_in.code)
            if existing:
                raise ConflictError(f"Role with code {role_in.code} already exists")

        # Validate permission IDs if provided
        if role_in.permission_ids is not None:
            if role_in.permission_ids:
                # Get ALL permissions to validate against (not just limited by count)
                permissions = await permission_crud.get_active_permissions(db)
                valid_ids = {p.id for p in permissions}
                invalid_ids = set(role_in.permission_ids) - valid_ids
                if invalid_ids:
                    raise ValidationError("permission_ids", f"Invalid IDs: {invalid_ids}")

        role = await role_crud.update_with_permissions(db, db_obj=role, obj_in=role_in)
        user_count = await role_crud.get_user_count(db, role_id=role_id)
        return RoleResponse.from_orm_with_str_id(role, user_count)

    async def delete_role(
        self,
        db: AsyncSession,
        role_id: int
    ) -> bool:
        """
        Delete a role
        
        Args:
            db: Database session
            role_id: Role ID
            
        Returns:
            True if deleted successfully
            
        Raises:
            NotFoundError: If role not found
            PermissionError: If attempting to delete a system role or role with users
        """
        role = await role_crud.get(db, id=role_id)
        if not role:
            raise NotFoundError(f"Role with id {role_id} not found")

        # Check if it's a system role
        if role.is_system:
            raise PermissionError("Cannot delete system roles")

        # Check if role has users
        user_count = await role_crud.get_user_count(db, role_id=role_id)
        if user_count > 0:
            raise PermissionError(f"Cannot delete role with {user_count} assigned users")

        await role_crud.remove(db, id=role_id)
        return True

    # =============================================
    # ROLE ASSIGNMENT METHODS
    # =============================================

    async def assign_role_to_user(
        self,
        db: AsyncSession,
        assignment: RoleAssignment
    ) -> UserResponse:
        """
        Assign a role to a user
        
        Args:
            db: Database session
            assignment: Role assignment data
            
        Returns:
            Updated user with new role
            
        Raises:
            NotFoundError: If user or role not found
            PermissionError: If role cannot be assigned
        """
        # Check if user exists
        user = await user_crud.get(db, id=assignment.user_id)
        if not user:
            raise NotFoundError(f"User with id {assignment.user_id} not found")

        # Check if role exists and can be assigned
        role = await role_crud.get_with_permissions(db, id=assignment.role_id)
        if not role:
            raise NotFoundError(f"Role with id {assignment.role_id} not found")

        if not role.can_be_assigned:
            raise PermissionError(f"Role {role.name} cannot be assigned to users")

        if not role.is_active:
            raise PermissionError(f"Role {role.name} is not active")

        # Assign role
        updated_user = await role_crud.assign_role_to_user(
            db,
            user_id=assignment.user_id,
            role_id=assignment.role_id
        )

        return UserResponse.model_validate(updated_user)

    async def bulk_assign_role(
        self,
        db: AsyncSession,
        bulk_assignment: BulkRoleAssignment
    ) -> list[UserResponse]:
        """
        Assign a role to multiple users
        
        Args:
            db: Database session
            bulk_assignment: Bulk role assignment data
            
        Returns:
            List of updated users
            
        Raises:
            NotFoundError: If role not found
            PermissionError: If role cannot be assigned
            ValidationError: If any user IDs are invalid
        """
        # Check if role exists and can be assigned
        role = await role_crud.get_with_permissions(db, id=bulk_assignment.role_id)
        if not role:
            raise NotFoundError(f"Role with id {bulk_assignment.role_id} not found")

        if not role.can_be_assigned:
            raise PermissionError(f"Role {role.name} cannot be assigned to users")

        if not role.is_active:
            raise PermissionError(f"Role {role.name} is not active")

        # Validate all user IDs exist
        users = []
        invalid_ids = []
        for user_id in bulk_assignment.user_ids:
            user = await user_crud.get(db, id=user_id)
            if user:
                users.append(user)
            else:
                invalid_ids.append(user_id)

        if invalid_ids:
            raise ValidationError("user_ids", f"Invalid IDs: {invalid_ids}")

        # Assign role to all users
        updated_users = []
        for user_id in bulk_assignment.user_ids:
            updated_user = await role_crud.assign_role_to_user(
                db,
                user_id=user_id,
                role_id=bulk_assignment.role_id
            )
            updated_users.append(UserResponse.model_validate(updated_user))

        return updated_users

    async def get_default_role(
        self,
        db: AsyncSession
    ) -> RoleResponse | None:
        """
        Get the default role for new users
        
        Args:
            db: Database session
            
        Returns:
            Default role if exists, None otherwise
        """
        role = await role_crud.get_default_role(db)
        if role:
            user_count = await role_crud.get_user_count(db, role_id=role.id)
            return RoleResponse.from_orm_with_str_id(role, user_count)
        return None

    async def get_assignable_roles(
        self,
        db: AsyncSession
    ) -> list[RoleResponse]:
        """
        Get all roles that can be assigned to users
        
        Args:
            db: Database session
            
        Returns:
            List of assignable roles
        """
        roles = await role_crud.get_assignable_roles(db)
        items = []
        for role in roles:
            user_count = await role_crud.get_user_count(db, role_id=role.id)
            items.append(RoleResponse.from_orm_with_str_id(role, user_count))
        return items

    # =============================================
    # PERMISSION CHECKING METHODS
    # =============================================

    async def get_user_permissions(
        self,
        db: AsyncSession,
        user_id: int
    ) -> list[str]:
        """
        Get all permission codes for a user based on their role
        
        Args:
            db: Database session
            user_id: User ID
            
        Returns:
            List of permission codes
        """
        user = await user_crud.get(db, id=user_id)
        if not user or not user.role_id:
            return []

        role = await role_crud.get_with_permissions(db, id=user.role_id)
        if not role or not role.is_active:
            return []

        # If super admin, return all permission codes
        if role.is_super_admin:
            all_permissions = await permission_crud.get_active_permissions(db)
            return [p.code for p in all_permissions]

        # Return role's permission codes
        return [p.code for p in role.permissions if p.is_active]

    async def check_user_permission(
        self,
        db: AsyncSession,
        user_id: int,
        permission_code: str
    ) -> bool:
        """
        Check if a user has a specific permission
        
        Args:
            db: Database session
            user_id: User ID
            permission_code: Permission code to check
            
        Returns:
            True if user has the permission
        """
        # Check if user is superuser first
        user = await user_crud.get(db, id=user_id)
        if user and user.is_superuser:
            return True
            
        # Otherwise check role permissions
        user_permissions = await self.get_user_permissions(db, user_id)
        return permission_code in user_permissions

    # =============================================
    # SYSTEM INITIALIZATION METHODS
    # =============================================

    async def initialize_default_permissions(
        self,
        db: AsyncSession
    ) -> list[PermissionResponse]:
        """
        Initialize default system permissions
        
        Args:
            db: Database session
            
        Returns:
            List of created permissions
        """
        default_permissions = [
            # User management permissions
            PermissionCreate(
                code="users.view",
                name="View Users",
                module="users",
                resource="users",
                action="view",
                access_level="all",
                description="View user list and details",
                is_system=True
            ),
            PermissionCreate(
                code="users.create",
                name="Create Users",
                module="users",
                resource="users",
                action="create",
                access_level="all",
                description="Create new users",
                is_system=True
            ),
            PermissionCreate(
                code="users.update",
                name="Update Users",
                module="users",
                resource="users",
                action="update",
                access_level="all",
                description="Update user information",
                is_system=True
            ),
            PermissionCreate(
                code="users.delete",
                name="Delete Users",
                module="users",
                resource="users",
                action="delete",
                access_level="all",
                description="Delete users",
                is_system=True
            ),

            # Role management permissions
            PermissionCreate(
                code="roles.view",
                name="View Roles",
                module="roles",
                resource="roles",
                action="view",
                access_level="all",
                description="View role list and details",
                is_system=True
            ),
            PermissionCreate(
                code="roles.create",
                name="Create Roles",
                module="roles",
                resource="roles",
                action="create",
                access_level="all",
                description="Create new roles",
                is_system=True
            ),
            PermissionCreate(
                code="roles.update",
                name="Update Roles",
                module="roles",
                resource="roles",
                action="update",
                access_level="all",
                description="Update role information",
                is_system=True
            ),
            PermissionCreate(
                code="roles.delete",
                name="Delete Roles",
                module="roles",
                resource="roles",
                action="delete",
                access_level="all",
                description="Delete roles",
                is_system=True
            ),

            # Permission management permissions
            PermissionCreate(
                code="permissions.view",
                name="View Permissions",
                module="permissions",
                resource="permissions",
                action="view",
                access_level="all",
                description="View permission list and details",
                is_system=True
            ),
            PermissionCreate(
                code="permissions.manage",
                name="Manage Permissions",
                module="permissions",
                resource="permissions",
                action="manage",
                access_level="all",
                description="Create, update, and delete permissions",
                is_system=True
            ),

            # Candidate management permissions
            PermissionCreate(
                code="candidates.view",
                name="View Candidates",
                module="candidates",
                resource="candidates",
                action="view",
                access_level="all",
                description="View candidate list and details",
                is_system=True
            ),
            PermissionCreate(
                code="candidates.create",
                name="Create Candidates",
                module="candidates",
                resource="candidates",
                action="create",
                access_level="all",
                description="Create new candidates",
                is_system=True
            ),
            PermissionCreate(
                code="candidates.update",
                name="Update Candidates",
                module="candidates",
                resource="candidates",
                action="update",
                access_level="all",
                description="Update candidate information",
                is_system=True
            ),
            PermissionCreate(
                code="candidates.delete",
                name="Delete Candidates",
                module="candidates",
                resource="candidates",
                action="delete",
                access_level="all",
                description="Delete candidates",
                is_system=True
            ),

            # Position management permissions
            PermissionCreate(
                code="positions.view",
                name="View Positions",
                module="positions",
                resource="positions",
                action="view",
                access_level="all",
                description="View position list and details",
                is_system=True
            ),
            PermissionCreate(
                code="positions.create",
                name="Create Positions",
                module="positions",
                resource="positions",
                action="create",
                access_level="all",
                description="Create new positions",
                is_system=True
            ),
            PermissionCreate(
                code="positions.update",
                name="Update Positions",
                module="positions",
                resource="positions",
                action="update",
                access_level="all",
                description="Update position information",
                is_system=True
            ),
            PermissionCreate(
                code="positions.delete",
                name="Delete Positions",
                module="positions",
                resource="positions",
                action="delete",
                access_level="all",
                description="Delete positions",
                is_system=True
            ),

            # System monitoring permissions
            PermissionCreate(
                code="monitoring.view",
                name="View System Monitoring",
                module="monitoring",
                resource="monitoring",
                action="view",
                access_level="all",
                description="View system health and metrics",
                is_system=True
            ),

            # Settings permissions
            PermissionCreate(
                code="settings.view",
                name="View Settings",
                module="settings",
                resource="settings",
                action="view",
                access_level="all",
                description="View system settings",
                is_system=True
            ),
            PermissionCreate(
                code="settings.manage",
                name="Manage Settings",
                module="settings",
                resource="settings",
                action="manage",
                access_level="all",
                description="Modify system settings",
                is_system=True
            ),
        ]

        created_permissions = await self.bulk_create_permissions(db, default_permissions)
        return created_permissions

    async def initialize_default_roles(
        self,
        db: AsyncSession
    ) -> list[RoleResponse]:
        """
        Initialize default system roles
        
        Args:
            db: Database session
            
        Returns:
            List of created roles
        """
        # Get all system permissions first
        all_permissions = await permission_crud.get_active_permissions(db)
        permission_map = {p.code: p.id for p in all_permissions}

        default_roles = [
            # Super Admin role with all permissions
            RoleCreate(
                code="super_admin",
                name="Super Administrator",
                description="Full system access with all permissions",
                level=100,
                is_active=True,
                is_system=True,
                is_super_admin=True,
                is_default=False,
                can_be_assigned=False,
                permission_ids=list(permission_map.values())  # All permissions
            ),

            # Admin role with most permissions
            RoleCreate(
                code="admin",
                name="Administrator",
                description="Administrative access with most permissions",
                level=90,
                is_active=True,
                is_system=True,
                is_super_admin=False,
                is_default=False,
                can_be_assigned=True,
                permission_ids=[
                    pid for pid in [
                        permission_map.get("users.view"),
                        permission_map.get("users.create"),
                        permission_map.get("users.update"),
                        permission_map.get("roles.view"),
                        permission_map.get("permissions.view"),
                        permission_map.get("candidates.view"),
                        permission_map.get("candidates.create"),
                        permission_map.get("candidates.update"),
                        permission_map.get("candidates.delete"),
                        permission_map.get("positions.view"),
                        permission_map.get("positions.create"),
                        permission_map.get("positions.update"),
                        permission_map.get("positions.delete"),
                        permission_map.get("monitoring.view"),
                        permission_map.get("settings.view"),
                    ] if pid is not None
                ]
            ),

            # Manager role
            RoleCreate(
                code="manager",
                name="Manager",
                description="Management access for teams and resources",
                level=70,
                is_active=True,
                is_system=True,
                is_super_admin=False,
                is_default=False,
                can_be_assigned=True,
                permission_ids=[
                    pid for pid in [
                        permission_map.get("users.view"),
                        permission_map.get("candidates.view"),
                        permission_map.get("candidates.create"),
                        permission_map.get("candidates.update"),
                        permission_map.get("positions.view"),
                        permission_map.get("positions.create"),
                        permission_map.get("positions.update"),
                        permission_map.get("monitoring.view"),
                    ] if pid is not None
                ]
            ),

            # HR role
            RoleCreate(
                code="hr",
                name="HR Specialist",
                description="Human resources management access",
                level=60,
                is_active=True,
                is_system=True,
                is_super_admin=False,
                is_default=False,
                can_be_assigned=True,
                permission_ids=[
                    pid for pid in [
                        permission_map.get("candidates.view"),
                        permission_map.get("candidates.create"),
                        permission_map.get("candidates.update"),
                        permission_map.get("positions.view"),
                        permission_map.get("positions.create"),
                        permission_map.get("positions.update"),
                    ] if pid is not None
                ]
            ),

            # Recruiter role
            RoleCreate(
                code="recruiter",
                name="Recruiter",
                description="Recruitment and candidate management access",
                level=50,
                is_active=True,
                is_system=True,
                is_super_admin=False,
                is_default=False,
                can_be_assigned=True,
                permission_ids=[
                    pid for pid in [
                        permission_map.get("candidates.view"),
                        permission_map.get("candidates.create"),
                        permission_map.get("candidates.update"),
                        permission_map.get("positions.view"),
                    ] if pid is not None
                ]
            ),

            # User role (default)
            RoleCreate(
                code="user",
                name="User",
                description="Basic user access",
                level=10,
                is_active=True,
                is_system=True,
                is_super_admin=False,
                is_default=True,  # Default role for new users
                can_be_assigned=True,
                permission_ids=[
                    pid for pid in [
                        permission_map.get("candidates.view"),
                        permission_map.get("positions.view"),
                    ] if pid is not None
                ]
            ),
        ]

        created_roles = []
        for role_data in default_roles:
            # Check if role already exists
            existing = await role_crud.get_by_code(db, code=role_data.code)
            if not existing:
                role = await self.create_role(db, role_data)
                created_roles.append(role)

        return created_roles

    async def initialize_system(
        self,
        db: AsyncSession
    ) -> Dict[str, Any]:
        """
        Initialize the complete permission and role system
        
        Args:
            db: Database session
            
        Returns:
            Summary of initialization results
        """
        # Initialize permissions first
        created_permissions = await self.initialize_default_permissions(db)
        
        # Then initialize roles (which depend on permissions)
        created_roles = await self.initialize_default_roles(db)
        
        return {
            "permissions_created": len(created_permissions),
            "roles_created": len(created_roles),
            "permission_codes": [p.code for p in created_permissions],
            "role_codes": [r.code for r in created_roles]
        }


# Create a singleton instance
permission_service = PermissionService()