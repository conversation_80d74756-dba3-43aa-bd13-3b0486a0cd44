"""
PaddleOCR Integration Service

This service provides OCR capabilities for TalentForge Pro with:
- GPU-first processing with CPU fallback
- File type detection and text-extractable PDF handling
- Confidence scoring and quality validation
- Resource-efficient model loading with singleton pattern
- Comprehensive error handling and timeout management
"""

import asyncio
import logging
import os
import tempfile
import time
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List
import base64

# Image processing - Handle import errors gracefully for testing
try:
    from PIL import Image
    import cv2
    import numpy as np
    PIL_AVAILABLE = True
    CV2_AVAILABLE = True
    NUMPY_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    CV2_AVAILABLE = False
    NUMPY_AVAILABLE = False
    # Mock objects for testing
    Image = None
    cv2 = None
    np = None

# PDF processing
try:
    import PyPDF2
    PYPDF2_AVAILABLE = True
except ImportError:
    PYPDF2_AVAILABLE = False
    PyPDF2 = None

import io

from app.core.config import settings
from app.core.exceptions import service_error

logger = logging.getLogger(__name__)

# Log warnings for missing dependencies
if not PIL_AVAILABLE or not CV2_AVAILABLE or not NUMPY_AVAILABLE:
    logger.warning("Image processing libraries not available (PIL, cv2, numpy)")
if not PYPDF2_AVAILABLE:
    logger.warning("PyPDF2 not available")


class OCRError(Exception):
    """Base OCR exception"""
    pass


class OCRTimeoutError(OCRError):
    """OCR processing timeout"""
    pass


class OCRResourceError(OCRError):
    """Insufficient resources for OCR"""
    pass


class OCRQualityError(OCRError):
    """OCR quality below threshold"""
    pass


class OCRService:
    """PaddleOCR integration service with GPU/CPU fallback"""
    
    _instance = None
    _ocr_engine = None
    _gpu_available = None
    _model_loaded = False
    
    def __new__(cls):
        """Singleton pattern for model reuse"""
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        """Initialize OCR service configuration"""
        if hasattr(self, '_initialized'):
            return
            
        self.enabled = settings.OCR_ENABLED
        self.use_gpu = settings.OCR_USE_GPU
        self.confidence_threshold = settings.OCR_CONFIDENCE_THRESHOLD
        self.max_processing_time = settings.OCR_MAX_PROCESSING_TIME
        self.supported_languages = settings.OCR_SUPPORTED_LANGUAGES.split(',')
        self.cpu_thread_num = settings.OCR_CPU_THREAD_NUM
        self.max_image_size = settings.OCR_MAX_IMAGE_SIZE
        self.timeout_seconds = settings.OCR_TIMEOUT_SECONDS
        self.last_used_time = None
        
        # OCR metrics
        self.metrics = {
            "total_requests": 0,
            "successful_extractions": 0,
            "gpu_usage": 0,
            "cpu_fallbacks": 0,
            "failures": 0,
            "avg_processing_time": 0.0,
            "avg_confidence": 0.0
        }
        
        self._initialized = True
        logger.info("OCR service initialized with GPU support: %s", self.use_gpu)
    
    def _detect_gpu_availability(self) -> bool:
        """Detect GPU availability for PaddleOCR"""
        if self._gpu_available is not None:
            return self._gpu_available
            
        try:
            import paddle
            self._gpu_available = paddle.is_compiled_with_cuda() and paddle.device.cuda.device_count() > 0
            logger.info(f"GPU detection: CUDA compiled={paddle.is_compiled_with_cuda()}, "
                       f"GPU count={paddle.device.cuda.device_count() if paddle.is_compiled_with_cuda() else 0}")
        except Exception as e:
            logger.warning(f"Failed to detect GPU availability: {e}")
            self._gpu_available = False
        
        return self._gpu_available
    
    def _initialize_engine(self) -> None:
        """Initialize PaddleOCR engine with GPU/CPU detection"""
        if self._ocr_engine is not None:
            return
            
        if not self.enabled:
            logger.info("OCR is disabled, skipping engine initialization")
            return
            
        try:
            from paddleocr import PaddleOCR
            
            # Determine GPU usage based on availability and settings
            gpu_available = self._detect_gpu_availability()
            use_gpu = self.use_gpu and gpu_available
            
            logger.info(f"Initializing PaddleOCR with GPU: {use_gpu}")
            
            # Initialize PaddleOCR with minimal parameters for compatibility
            self._ocr_engine = PaddleOCR(lang=','.join(self.supported_languages))
            
            self._model_loaded = True
            self.use_gpu = use_gpu  # Update actual GPU usage
            self.last_used_time = time.time()
            
            logger.info(f"PaddleOCR initialized successfully (GPU: {use_gpu})")
            
        except ImportError as e:
            logger.error(f"PaddleOCR not available: {e}")
            raise OCRError(f"PaddleOCR dependencies not installed: {e}")
        except Exception as e:
            logger.error(f"Failed to initialize OCR engine: {e}")
            raise OCRError(f"OCR engine initialization failed: {e}")
    
    def is_loaded(self) -> bool:
        """Check if OCR model is loaded"""
        return self._model_loaded and self._ocr_engine is not None
    
    def test_connection(self) -> bool:
        """Test OCR engine with a simple operation"""
        try:
            if not self.enabled:
                return False
                
            self._initialize_engine()
            
            # Create a simple test image
            test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255
            cv2.putText(test_image, 'TEST', (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            
            # Run OCR on test image
            result = self._ocr_engine.ocr(test_image, cls=True)
            return result is not None
            
        except Exception as e:
            logger.warning(f"OCR connection test failed: {e}")
            return False
    
    def _should_use_ocr(self, file_path: str, file_type: str) -> bool:
        """Determine if OCR is needed for this file"""
        if not self.enabled:
            return False
            
        file_type_lower = file_type.lower()
        
        if file_type_lower == 'pdf':
            # Check if PDF is text-extractable
            return not self._is_pdf_text_extractable(file_path)
        elif file_type_lower in ['png', 'jpg', 'jpeg', 'bmp', 'tiff', 'webp']:
            # Images always need OCR
            return True
        else:
            # Other formats don't need OCR
            return False
    
    def _is_pdf_text_extractable(self, file_path: str) -> bool:
        """Check if PDF contains extractable text"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                # Check first few pages for text
                pages_to_check = min(3, len(pdf_reader.pages))
                total_text_length = 0
                
                for i in range(pages_to_check):
                    page = pdf_reader.pages[i]
                    text = page.extract_text()
                    total_text_length += len(text.strip())
                
                # If we have substantial text (>100 chars), consider it extractable
                return total_text_length > 100
                
        except Exception as e:
            logger.warning(f"Failed to check PDF text extraction: {e}")
            return False  # Assume scanned if we can't check
    
    def _extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF using traditional method"""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_parts = []
                
                for page in pdf_reader.pages:
                    text = page.extract_text()
                    if text.strip():
                        text_parts.append(text)
                
                return '\n\n'.join(text_parts)
                
        except Exception as e:
            logger.error(f"Failed to extract text from PDF: {e}")
            return ""
    
    def _validate_file(self, file_path: str) -> bool:
        """Validate file for OCR processing"""
        try:
            path = Path(file_path)
            
            # Check file exists
            if not path.exists():
                logger.error(f"File does not exist: {file_path}")
                return False
            
            # Check file size
            file_size = path.stat().st_size
            if file_size > self.max_image_size:
                logger.error(f"File too large: {file_size} bytes > {self.max_image_size}")
                return False
            
            # Check if it's a valid image or PDF
            file_suffix = path.suffix.lower()
            if file_suffix == '.pdf':
                return True
            elif file_suffix in ['.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp']:
                # Try to open image
                try:
                    with Image.open(file_path) as img:
                        img.verify()
                    return True
                except Exception as e:
                    logger.error(f"Invalid image file: {e}")
                    return False
            else:
                logger.error(f"Unsupported file type: {file_suffix}")
                return False
                
        except Exception as e:
            logger.error(f"File validation failed: {e}")
            return False
    
    def _optimize_image_for_ocr(self, image_path: str) -> str:
        """Preprocess image for better OCR accuracy"""
        try:
            # Load image
            img = cv2.imread(image_path)
            if img is None:
                logger.warning("Failed to load image, using original")
                return image_path
            
            height, width = img.shape[:2]
            
            # Resize if too large
            if max(height, width) > 2048:
                scale = 2048 / max(height, width)
                new_width = int(width * scale)
                new_height = int(height * scale)
                img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_AREA)
                logger.debug(f"Resized image from {width}x{height} to {new_width}x{new_height}")
            
            # Convert to grayscale
            gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            
            # Apply noise reduction
            enhanced = cv2.medianBlur(gray, 3)
            
            # Enhance contrast
            enhanced = cv2.convertScaleAbs(enhanced, alpha=1.2, beta=10)
            
            # Save optimized image
            optimized_path = f"{image_path}_optimized.jpg"
            cv2.imwrite(optimized_path, enhanced)
            
            return optimized_path
            
        except Exception as e:
            logger.warning(f"Image optimization failed: {e}")
            return image_path  # Return original on failure
    
    def _parse_ocr_results(self, ocr_result: List) -> Tuple[str, float]:
        """Parse OCR results and calculate confidence score"""
        if not ocr_result or not ocr_result[0]:
            return "", 0.0
        
        text_parts = []
        confidence_scores = []
        
        for line in ocr_result[0]:
            if len(line) >= 2:
                # line[0] contains bounding box coordinates
                # line[1] contains (text, confidence)
                text_info = line[1]
                if isinstance(text_info, tuple) and len(text_info) >= 2:
                    text, confidence = text_info[0], text_info[1]
                    text_parts.append(text)
                    confidence_scores.append(float(confidence))
        
        # Combine text
        extracted_text = '\n'.join(text_parts)
        
        # Calculate average confidence
        avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
        
        return extracted_text, avg_confidence
    
    async def _process_with_ocr(self, file_path: str) -> Dict[str, Any]:
        """OCR processing pipeline with error handling"""
        start_time = time.time()
        result = {
            "text": "",
            "confidence": 0.0,
            "success": False,
            "method": "unknown",
            "processing_time": 0.0,
            "error": None
        }
        
        try:
            # Initialize engine if needed
            self._initialize_engine()
            
            if not self._ocr_engine:
                raise OCRError("OCR engine not initialized")
            
            # Validate file
            if not self._validate_file(file_path):
                raise OCRError("File validation failed")
            
            # Optimize image if it's an image file
            processing_path = file_path
            cleanup_optimized = False
            
            if file_path.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.webp')):
                processing_path = self._optimize_image_for_ocr(file_path)
                cleanup_optimized = processing_path != file_path
            
            try:
                # Run OCR with timeout
                ocr_task = asyncio.to_thread(self._ocr_engine.ocr, processing_path, cls=True)
                ocr_result = await asyncio.wait_for(ocr_task, timeout=self.timeout_seconds)
                
                # Parse results
                extracted_text, avg_confidence = self._parse_ocr_results(ocr_result)
                
                # Update metrics
                self.metrics["total_requests"] += 1
                if self.use_gpu:
                    self.metrics["gpu_usage"] += 1
                else:
                    self.metrics["cpu_fallbacks"] += 1
                
                # Validate quality
                if avg_confidence >= self.confidence_threshold:
                    result.update({
                        "text": extracted_text,
                        "confidence": avg_confidence,
                        "success": True,
                        "method": "ocr_gpu" if self.use_gpu else "ocr_cpu"
                    })
                    self.metrics["successful_extractions"] += 1
                else:
                    raise OCRQualityError(f"Low confidence: {avg_confidence:.3f}")
            
            finally:
                # Cleanup optimized image
                if cleanup_optimized and os.path.exists(processing_path):
                    try:
                        os.remove(processing_path)
                    except Exception as e:
                        logger.warning(f"Failed to cleanup optimized image: {e}")
                    
        except asyncio.TimeoutError:
            logger.error(f"OCR processing timeout after {self.timeout_seconds}s")
            result["error"] = f"Processing timeout ({self.timeout_seconds}s)"
            self.metrics["failures"] += 1
        except Exception as e:
            logger.error(f"OCR processing failed: {e}")
            result["error"] = str(e)
            self.metrics["failures"] += 1
            
            # CPU fallback if GPU failed
            if self.use_gpu and "CUDA" in str(e).upper():
                logger.info("GPU OCR failed, retrying with CPU...")
                return await self._process_with_cpu_fallback(file_path)
        
        finally:
            processing_time = time.time() - start_time
            result["processing_time"] = processing_time
            self.last_used_time = time.time()
            
            # Update average processing time
            if self.metrics["total_requests"] > 0:
                self.metrics["avg_processing_time"] = (
                    self.metrics["avg_processing_time"] * (self.metrics["total_requests"] - 1) + processing_time
                ) / self.metrics["total_requests"]
                
            # Update average confidence
            if result["success"] and self.metrics["successful_extractions"] > 0:
                self.metrics["avg_confidence"] = (
                    self.metrics["avg_confidence"] * (self.metrics["successful_extractions"] - 1) + result["confidence"]
                ) / self.metrics["successful_extractions"]
        
        return result
    
    async def _process_with_cpu_fallback(self, file_path: str) -> Dict[str, Any]:
        """Fallback to CPU processing when GPU fails"""
        original_gpu_setting = self.use_gpu
        
        try:
            # Force CPU mode
            self.use_gpu = False
            self._ocr_engine = None  # Force re-initialization
            
            # Re-initialize with CPU
            self._initialize_engine()
            
            # Process with CPU
            result = await self._process_with_ocr(file_path)
            if result["success"]:
                result["method"] = "ocr_cpu_fallback"
            
            return result
            
        finally:
            # Restore original setting
            self.use_gpu = original_gpu_setting
    
    def _fallback_text_extraction(self, file_path: str) -> str:
        """Fallback to regular text extraction for PDFs"""
        if file_path.lower().endswith('.pdf'):
            return self._extract_text_from_pdf(file_path)
        else:
            logger.warning(f"No fallback text extraction available for {file_path}")
            return ""
    
    async def extract_text(self, file_path: str, file_type: str) -> Dict[str, Any]:
        """
        Extract text from image/PDF file
        
        Args:
            file_path: Path to the file
            file_type: File type (pdf, png, jpg, etc.)
        
        Returns:
            Dict with keys: text, confidence, success, method, processing_time
        """
        logger.info(f"Starting OCR text extraction for {file_path} (type: {file_type})")
        
        # Check if OCR is needed
        if not self._should_use_ocr(file_path, file_type):
            # Use traditional text extraction
            fallback_text = self._fallback_text_extraction(file_path)
            return {
                "text": fallback_text,
                "confidence": 1.0 if fallback_text else 0.0,
                "success": bool(fallback_text),
                "method": "fallback_extraction",
                "processing_time": 0.0
            }
        
        # Perform OCR processing
        try:
            result = await self._process_with_ocr(file_path)
            
            # If OCR failed, try fallback for PDFs
            if not result["success"] and file_type.lower() == 'pdf':
                logger.info("OCR failed for PDF, trying fallback text extraction")
                fallback_text = self._fallback_text_extraction(file_path)
                if fallback_text:
                    result.update({
                        "text": fallback_text,
                        "confidence": 0.8,  # Lower confidence for fallback
                        "success": True,
                        "method": "fallback_after_ocr_failure"
                    })
            
            logger.info(f"OCR extraction completed: success={result['success']}, "
                       f"method={result['method']}, confidence={result.get('confidence', 0):.3f}")
            
            return result
            
        except Exception as e:
            logger.error(f"OCR extraction failed completely: {e}")
            return {
                "text": "",
                "confidence": 0.0,
                "success": False,
                "method": "error",
                "processing_time": 0.0,
                "error": str(e)
            }
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get OCR service metrics"""
        return {
            **self.metrics,
            "enabled": self.enabled,
            "gpu_available": self._detect_gpu_availability(),
            "gpu_enabled": self.use_gpu,
            "model_loaded": self.is_loaded(),
            "last_used": self.last_used_time,
            "success_rate": (
                self.metrics["successful_extractions"] / self.metrics["total_requests"] * 100
                if self.metrics["total_requests"] > 0 else 0
            )
        }


# Global OCR service instance
ocr_service = OCRService()