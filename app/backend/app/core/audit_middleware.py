"""
Audit Middleware for Request-Level Logging
"""
import time
import logging
from typing import Callable, Optional

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.services.audit_service import AuditService
from app.core.security import decode_token
from app.crud import user as user_crud
from app.core.database import get_db

logger = logging.getLogger(__name__)


class AuditMiddleware(BaseHTTPMiddleware):
    """
    Middleware for automatic request-level audit logging
    
    Captures all HTTP requests with timing, user context, and response status
    Focus on high-value endpoints while avoiding noise from health checks
    """
    
    # Endpoints that should always be audited
    AUDIT_PATHS = {
        "/api/v1/dashboard",
        "/api/v1/candidates",
        "/api/v1/positions", 
        "/api/v1/assessment",
        "/api/v1/matching",
        "/api/v1/admin",
        "/api/v1/questionnaires"
    }
    
    # Endpoints to exclude from auditing (reduce noise)
    EXCLUDE_PATHS = {
        "/health",
        "/docs",
        "/openapi.json",
        "/favicon.ico"
    }
    
    # Critical operations that always get logged regardless of path
    CRITICAL_OPERATIONS = {
        "POST": ["create", "upload", "generate", "match"],
        "PUT": ["update", "modify"], 
        "DELETE": ["delete", "remove"],
        "PATCH": ["patch", "modify"]
    }
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and response with audit logging"""
        
        # Skip audit for excluded paths
        if self._should_exclude_path(request.url.path):
            return await call_next(request)
        
        # Capture request start time
        start_time = time.time()
        
        # Extract user context if available
        current_user = None
        try:
            # Attempt to extract user from JWT token
            current_user = await self._extract_user_from_request(request)
        except Exception:
            # User extraction failed - could be anonymous access
            pass
        
        # Determine if this request should be audited
        should_audit = self._should_audit_request(request)
        
        # Variables for audit logging
        response = None
        error_occurred = False
        error_message = None
        
        try:
            # Execute the request
            response = await call_next(request)
            
        except Exception as e:
            error_occurred = True
            error_message = str(e)
            logger.error(f"Request failed: {request.method} {request.url.path} - {error_message}")
            raise
            
        finally:
            # Log audit information if needed
            if should_audit:
                await self._log_request_audit(
                    request=request,
                    response=response,
                    current_user=current_user,
                    start_time=start_time,
                    error_occurred=error_occurred,
                    error_message=error_message
                )
        
        return response
    
    def _should_exclude_path(self, path: str) -> bool:
        """Check if path should be excluded from all processing"""
        return any(excluded in path for excluded in self.EXCLUDE_PATHS)
    
    def _should_audit_request(self, request: Request) -> bool:
        """Determine if request should be audited"""
        path = request.url.path
        method = request.method
        
        # Always audit paths in AUDIT_PATHS
        if any(audit_path in path for audit_path in self.AUDIT_PATHS):
            return True
        
        # Always audit critical operations
        if method in self.CRITICAL_OPERATIONS:
            critical_ops = self.CRITICAL_OPERATIONS[method]
            if any(op in path.lower() for op in critical_ops):
                return True
        
        # Audit authentication endpoints
        if "/auth" in path and method in ["POST", "PUT", "DELETE"]:
            return True
        
        return False
    
    async def _log_request_audit(
        self,
        request: Request,
        response: Optional[Response],
        current_user,
        start_time: float,
        error_occurred: bool = False,
        error_message: Optional[str] = None
    ):
        """Log audit information for the request"""
        try:
            # Calculate request duration
            duration_ms = round((time.time() - start_time) * 1000, 2)
            
            # Determine operation and resource type from path
            operation, resource_type = self._extract_operation_info(request)
            
            # Extract status information
            status = "error" if error_occurred else "success"
            status_code = 500 if error_occurred else (response.status_code if response else 200)
            
            # Build audit details
            details = {
                "duration_ms": duration_ms,
                "query_params": dict(request.query_params) if request.query_params else {},
                "path_params": request.path_params if request.path_params else {},
                "request_size": request.headers.get("content-length", 0)
            }
            
            # Add response info if available
            if response:
                details.update({
                    "response_size": response.headers.get("content-length", 0),
                    "response_headers": {
                        k: v for k, v in response.headers.items() 
                        if k.lower() in ["content-type", "content-length"]
                    }
                })
            
            # Log the audit entry
            await AuditService.log_activity(
                operation=operation,
                resource_type=resource_type,
                user_id=current_user.id if current_user else None,
                user_email=current_user.email if current_user else None,
                status=status,
                status_code=status_code,
                details=details,
                error_message=error_message,
                request=request
            )
            
            # Log performance warning for slow requests
            if duration_ms > 1000:  # > 1 second
                logger.warning(
                    f"Slow request: {request.method} {request.url.path} "
                    f"took {duration_ms}ms (user: {current_user.email if current_user else 'anonymous'})"
                )
                
        except Exception as e:
            # Don't fail the request if audit logging fails
            logger.error(f"Failed to log request audit: {str(e)}")
    
    def _extract_operation_info(self, request: Request) -> tuple[str, str]:
        """Extract operation and resource type from request path"""
        path = request.url.path.lower()
        method = request.method.lower()
        
        # Parse path components
        path_parts = [part for part in path.split("/") if part and part != "api" and part != "v1"]
        
        if not path_parts:
            return f"root.{method}", "system"
        
        resource_type = path_parts[0] if path_parts else "unknown"
        
        # Build operation name
        if len(path_parts) >= 2:
            operation = f"{resource_type}.{path_parts[1]}.{method}"
        else:
            operation = f"{resource_type}.{method}"
        
        return operation, resource_type
    
    async def _extract_user_from_request(self, request: Request):
        """Extract user from JWT token in request headers"""
        try:
            auth_header = request.headers.get("authorization")
            if not auth_header or not auth_header.startswith("Bearer "):
                return None
            
            token = auth_header.split(" ")[1]
            if not token:
                return None
            
            # Decode token
            payload = decode_token(token)
            user_id = int(payload.get("sub")) if payload.get("sub") else None
            
            if not user_id:
                return None
            
            # Get user from database
            async for db in get_db():
                user = await user_crud.get(db, id=user_id)
                return user if user and user.is_active else None
                
        except Exception as e:
            logger.debug(f"Failed to extract user from request: {e}")
            return None