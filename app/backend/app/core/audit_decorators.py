"""
Audit Decorators for Easy Integration
"""
import functools
import logging
from typing import Any, Callable, Dict, Optional

from fastapi import Request
from app.services.audit_service import AuditService
from app.models.user import User

logger = logging.getLogger(__name__)


def audit_log(
    operation: str,
    resource_type: str,
    extract_resource_id: Optional[Callable[[Any], int]] = None,
    capture_changes: bool = False,
    success_only: bool = True
):
    """
    Decorator for automatic audit logging of API endpoints
    
    Args:
        operation: Operation identifier (e.g., "dashboard.stats.view")
        resource_type: Type of resource being accessed
        extract_resource_id: Function to extract resource ID from response
        capture_changes: Whether to capture before/after changes
        success_only: Only log successful operations (2xx status codes)
    
    Usage:
        @router.get("/dashboard/stats")
        @audit_log("dashboard.stats.view", "dashboard")
        async def get_dashboard_stats(...):
            return stats
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract request and current user from function parameters
            request = None
            current_user = None
            
            # Look for Request and User objects in kwargs
            for key, value in kwargs.items():
                if isinstance(value, Request):
                    request = value
                elif isinstance(value, User):
                    current_user = value
            
            # Also check args for request/user (common in dependency injection)
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                elif isinstance(arg, User):
                    current_user = arg
            
            start_status = "success"
            status_code = 200
            error_message = None
            details = {}
            
            try:
                # Execute the original function
                result = await func(*args, **kwargs)
                
                # Extract resource ID if extractor provided
                resource_id = None
                if extract_resource_id and result:
                    try:
                        resource_id = extract_resource_id(result)
                    except Exception as e:
                        logger.debug(f"Failed to extract resource ID: {e}")
                
                # Add function context to details
                details = {
                    "function_name": func.__name__,
                    "endpoint_path": request.url.path if request else None,
                    "parameters": {k: str(v)[:100] for k, v in kwargs.items() 
                                if not isinstance(v, (Request, User))}
                }
                
                # Log successful operation
                await AuditService.log_activity(
                    operation=operation,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    user_id=current_user.id if current_user else None,
                    user_email=current_user.email if current_user else None,
                    status=start_status,
                    status_code=status_code,
                    details=details,
                    request=request
                )
                
                return result
                
            except Exception as e:
                # Handle operation failure
                start_status = "error"
                status_code = getattr(e, 'status_code', 500)
                error_message = str(e)
                
                # Log failed operation unless success_only is True
                if not success_only:
                    details["error_type"] = type(e).__name__
                    
                    await AuditService.log_activity(
                        operation=operation,
                        resource_type=resource_type,
                        user_id=current_user.id if current_user else None,
                        user_email=current_user.email if current_user else None,
                        status=start_status,
                        status_code=status_code,
                        details=details,
                        error_message=error_message,
                        request=request
                    )
                
                # Re-raise the exception
                raise
        
        return wrapper
    return decorator


def audit_resource_access(resource_type: str):
    """
    Simplified decorator for resource access logging
    
    Args:
        resource_type: Type of resource being accessed
    
    Usage:
        @audit_resource_access("dashboard")
        async def get_dashboard_data(...):
            return data
    """
    def decorator(func: Callable) -> Callable:
        operation = f"{resource_type}.{func.__name__.replace('get_', '').replace('_', '.')}"
        return audit_log(operation, resource_type)(func)
    return decorator


def audit_crud_operation(operation_type: str, resource_type: str):
    """
    Decorator for CRUD operation logging
    
    Args:
        operation_type: CRUD operation (create, read, update, delete)
        resource_type: Type of resource
    
    Usage:
        @audit_crud_operation("create", "candidate")
        async def create_candidate(...):
            return candidate
    """
    def decorator(func: Callable) -> Callable:
        operation = f"{resource_type}.{operation_type}"
        
        # Resource ID extractor for different CRUD operations
        def extract_id(result):
            if hasattr(result, 'id'):
                return result.id
            elif isinstance(result, dict) and 'id' in result:
                return result['id']
            return None
        
        return audit_log(
            operation=operation,
            resource_type=resource_type,
            extract_resource_id=extract_id,
            capture_changes=(operation_type in ['create', 'update', 'delete']),
            success_only=False
        )(func)
    
    return decorator


def audit_dashboard_access(specific_operation: Optional[str] = None):
    """
    Specialized decorator for dashboard endpoint auditing
    
    Args:
        specific_operation: Specific dashboard operation (stats, trends, activities)
    
    Usage:
        @audit_dashboard_access("stats")
        async def get_dashboard_stats(...):
            return stats
    """
    def decorator(func: Callable) -> Callable:
        base_operation = "dashboard"
        if specific_operation:
            operation = f"{base_operation}.{specific_operation}.view"
        else:
            operation = f"{base_operation}.{func.__name__.replace('get_dashboard_', '').replace('_', '.')}"
        
        return audit_log(
            operation=operation,
            resource_type="dashboard",
            success_only=True  # Only log successful dashboard access
        )(func)
    
    return decorator