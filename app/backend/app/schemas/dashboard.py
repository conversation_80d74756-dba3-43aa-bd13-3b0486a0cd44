"""
Dashboard schemas for comprehensive dashboard functionality
Aligned with frontend requirements and providing 100% real data
"""
from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum
from app.core.id_types import SnowflakeID


class ActivityType(str, Enum):
    """Dashboard activity types"""
    CANDIDATE_ADDED = "candidate_added"
    ASSESSMENT_COMPLETED = "assessment_completed"
    MATCHING_COMPLETED = "matching_completed"
    POSITION_POSTED = "position_posted"
    BATCH_TASK_COMPLETED = "batch_task_completed"


class DashboardStats(BaseModel):
    """Dashboard statistics - aligned with frontend DashboardStats component"""
    # Core metrics
    new_candidates_today: int = Field(..., description="New candidates registered today")
    total_candidates: int = Field(..., description="Total active candidates")
    pending_matches: int = Field(..., description="Pending match tasks")
    avg_dci_score: float = Field(..., description="Average DCI score across all assessments")
    avg_jfs_score: float = Field(0.0, description="Average JFS score across all assessments")
    weekly_assessments: int = Field(..., description="Assessments completed this week")
    active_positions: int = Field(..., description="Active job positions")
    high_potential_candidates: int = Field(..., description="Candidates with DCI > 80")
    completion_rate: float = Field(..., description="Assessment completion rate percentage")
    
    # Growth metrics for frontend monthly growth calculations
    new_candidates_change: float = Field(0.0, description="Percentage change in new candidates (month over month)")
    avg_dci_score_change: float = Field(0.0, description="Percentage change in average DCI score")
    avg_jfs_score_change: float = Field(0.0, description="Percentage change in average JFS score")
    weekly_assessments_change: float = Field(0.0, description="Percentage change in weekly assessments")
    
    last_updated: datetime = Field(default_factory=datetime.now, description="When stats were last updated")
    
    class Config:
        json_schema_extra = {
            "example": {
                "new_candidates_today": 15,
                "total_candidates": 1234,
                "pending_matches": 45,
                "avg_dci_score": 85.2,
                "avg_jfs_score": 78.5,
                "weekly_assessments": 892,
                "active_positions": 67,
                "high_potential_candidates": 234,
                "completion_rate": 87.3,
                "new_candidates_change": 12.3,
                "avg_dci_score_change": 2.1,
                "avg_jfs_score_change": 8.3,
                "weekly_assessments_change": 15.2,
                "last_updated": "2025-08-29T14:30:00Z"
            }
        }


class TrendDataPoint(BaseModel):
    """Single data point in trend analysis"""
    date: str = Field(..., description="Date in YYYY-MM-DD format")
    dci_average: float = Field(0.0, description="Average DCI score for this date")
    jfs_average: float = Field(0.0, description="Average JFS score for this date")
    candidate_count: int = Field(0, description="Number of candidates registered on this date")
    assessment_count: int = Field(0, description="Number of assessments completed on this date")


class DashboardTrends(BaseModel):
    """Dashboard trends over time - aligned with frontend TrendAnalysisCharts"""
    candidate_trends: List[Dict[str, Any]] = Field(default_factory=list, description="Daily candidate registration trends")
    assessment_trends: List[Dict[str, Any]] = Field(default_factory=list, description="Daily assessment completion trends")
    dci_trends: List[Dict[str, Any]] = Field(default_factory=list, description="DCI score trends over time")
    jfs_trends: List[Dict[str, Any]] = Field(default_factory=list, description="JFS score trends over time")
    matching_trends: List[Dict[str, Any]] = Field(default_factory=list, description="Matching activity trends")
    period_days: int = Field(..., description="Number of days covered")
    generated_at: datetime = Field(default_factory=datetime.now, description="When trends were generated")


class SkillDistribution(BaseModel):
    """Skill distribution data - aligned with frontend SkillDistributionChart"""
    skill_category: str = Field(..., description="Skill category name")
    average_score: float = Field(..., description="Average assessment score for this skill")
    candidate_count: int = Field(..., description="Number of candidates with this skill")
    percentage: float = Field(..., description="Percentage of total candidates with this skill")


class SkillDistributionResponse(BaseModel):
    """Response for skill distribution endpoint"""
    items: List[SkillDistribution]
    total_candidates: int = Field(..., description="Total candidates across all skills")
    overall_average_score: float = Field(..., description="Overall average score across all skills")
    generated_at: datetime = Field(default_factory=datetime.now)


class ComparisonData(BaseModel):
    """Comparison analysis data - aligned with frontend ComparisonAnalysis"""
    category: str = Field(..., description="Assessment category name")
    company_score: float = Field(..., description="Company average score")
    industry_average: float = Field(..., description="Industry benchmark average")
    percentile_rank: float = Field(..., description="Company's percentile rank (0-100)")
    trend: str = Field(..., description="Trend direction: up, down, stable")


class ComparisonAnalysisResponse(BaseModel):
    """Response for comparison analysis endpoint"""
    items: List[ComparisonData]
    overall_performance: Dict[str, float] = Field(..., description="Overall performance summary")
    benchmark_type: str = Field("industry", description="Type of benchmark: industry, team, position")
    generated_at: datetime = Field(default_factory=datetime.now)


class AssessmentAnalytics(BaseModel):
    """Detailed assessment analytics"""
    total_assessments: int
    completion_rate: float
    average_time_to_complete: float  # in minutes
    score_distribution: Dict[str, int]  # score ranges and counts
    monthly_completion_trend: List[Dict[str, Any]]
    skill_performance: List[Dict[str, Any]]


class DashboardActivity(BaseModel):
    """Dashboard activity item - aligned with frontend expectations"""
    id: str = Field(..., description="Activity ID")
    type: ActivityType = Field(..., description="Activity type")
    title: str = Field(..., description="Activity title")
    description: Optional[str] = Field(None, description="Activity description")
    timestamp: datetime = Field(..., description="When activity occurred")
    entity_id: str = Field(..., description="Related entity ID")
    entity_type: str = Field(..., description="Type of related entity")
    user_id: Optional[SnowflakeID] = Field(None, description="User who performed the activity")
    user_name: Optional[str] = Field(None, description="Name of user who performed the activity")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional activity metadata")


class DashboardActivitiesResponse(BaseModel):
    """Response for dashboard activities endpoint - aligned with frontend expectations"""
    items: List[DashboardActivity]
    total: int
    has_more: bool
    skip: int = 0
    limit: int = 20