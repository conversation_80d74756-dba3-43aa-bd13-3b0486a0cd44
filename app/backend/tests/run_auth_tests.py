#!/usr/bin/env python3
"""
Authentication Test Runner

Focused test runner for authentication system verification.
Runs only the auth-related tests without expanding scope.
"""
import asyncio
import sys
import time
from pathlib import Path
from typing import Dict, Any, List
import pytest

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))


class AuthTestRunner:
    """Authentication test runner with reporting"""
    
    def __init__(self):
        self.test_results = {}
        self.start_time = time.perf_counter()
        
    def run_smoke_tests(self) -> Dict[str, Any]:
        """Run quick smoke tests for basic functionality"""
        print("🔥 Running Authentication Smoke Tests...")
        
        test_file = Path(__file__).parent / "test_auth_smoke.py"
        exit_code = pytest.main([
            str(test_file),
            "-v",
            "--tb=short",
            "--no-header",
            "-q"
        ])
        
        return {
            "name": "Smoke Tests",
            "passed": exit_code == 0,
            "exit_code": exit_code
        }
    
    def run_verification_tests(self) -> Dict[str, Any]:
        """Run comprehensive verification tests"""
        print("🔍 Running Authentication Verification Tests...")
        
        test_file = Path(__file__).parent / "test_auth_verification.py"
        exit_code = pytest.main([
            str(test_file),
            "-v",
            "--tb=short",
            "--no-header",
            "-x"  # Stop on first failure for faster feedback
        ])
        
        return {
            "name": "Verification Tests", 
            "passed": exit_code == 0,
            "exit_code": exit_code
        }
    
    def run_all_auth_tests(self) -> Dict[str, Any]:
        """Run all authentication-related tests"""
        print("🧪 Running All Authentication Tests...")
        
        # Run both test files
        test_files = [
            Path(__file__).parent / "test_auth_smoke.py",
            Path(__file__).parent / "test_auth_verification.py"
        ]
        
        exit_code = pytest.main([
            str(test_files[0]),
            str(test_files[1]),
            "-v",
            "--tb=short",
            "--no-header"
        ])
        
        return {
            "name": "All Auth Tests",
            "passed": exit_code == 0,
            "exit_code": exit_code
        }
    
    def run_with_coverage(self) -> Dict[str, Any]:
        """Run tests with coverage report"""
        print("📊 Running Authentication Tests with Coverage...")
        
        try:
            exit_code = pytest.main([
                str(Path(__file__).parent / "test_auth_smoke.py"),
                str(Path(__file__).parent / "test_auth_verification.py"),
                "--cov=app.api.v1.auth",
                "--cov=app.api.deps", 
                "--cov=app.core.security",
                "--cov-report=term-missing",
                "--cov-report=html:htmlcov/auth",
                "-v"
            ])
            
            return {
                "name": "Tests with Coverage",
                "passed": exit_code == 0,
                "exit_code": exit_code
            }
        except ImportError:
            print("⚠️ pytest-cov not installed, running without coverage")
            return self.run_all_auth_tests()
    
    def generate_report(self, results: List[Dict[str, Any]]) -> str:
        """Generate test execution report"""
        end_time = time.perf_counter()
        duration = end_time - self.start_time
        
        passed_count = sum(1 for r in results if r["passed"])
        total_count = len(results)
        success_rate = (passed_count / total_count * 100) if total_count > 0 else 0
        
        report_lines = [
            "=" * 70,
            "AUTHENTICATION SYSTEM TEST REPORT", 
            "=" * 70,
            f"Duration: {duration:.2f} seconds",
            f"Test Suites: {passed_count}/{total_count} passed ({success_rate:.1f}%)",
            "",
            "TEST SUITE RESULTS:",
            "-" * 50
        ]
        
        for result in results:
            status = "✅ PASSED" if result["passed"] else "❌ FAILED"
            report_lines.append(f"{status} {result['name']} (exit code: {result['exit_code']})")
        
        report_lines.extend([
            "",
            "=" * 70
        ])
        
        if success_rate == 100:
            report_lines.append("🎉 ALL AUTHENTICATION TESTS PASSED")
        elif success_rate >= 50:
            report_lines.append("⚠️ SOME AUTHENTICATION TESTS FAILED - REVIEW REQUIRED")
        else:
            report_lines.append("🚨 CRITICAL: MULTIPLE AUTHENTICATION TESTS FAILED")
        
        report_lines.append("=" * 70)
        
        return "\n".join(report_lines)


def main():
    """Main test execution function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run authentication tests")
    parser.add_argument(
        "--mode", 
        choices=["smoke", "verification", "all", "coverage"],
        default="all",
        help="Test mode to run"
    )
    parser.add_argument(
        "--fast", 
        action="store_true",
        help="Run only smoke tests for fast feedback"
    )
    
    args = parser.parse_args()
    
    if args.fast:
        args.mode = "smoke"
    
    runner = AuthTestRunner()
    results = []
    
    print(f"🚀 TalentForge Pro - Authentication Test Runner")
    print(f"Mode: {args.mode}")
    print("-" * 70)
    
    try:
        if args.mode == "smoke":
            results.append(runner.run_smoke_tests())
        elif args.mode == "verification":
            results.append(runner.run_verification_tests())
        elif args.mode == "coverage":
            results.append(runner.run_with_coverage())
        else:  # "all"
            results.append(runner.run_smoke_tests())
            if results[-1]["passed"]:  # Only run verification if smoke tests pass
                results.append(runner.run_verification_tests())
            else:
                print("❌ Smoke tests failed, skipping verification tests")
        
        # Generate and display report
        report = runner.generate_report(results)
        print("\n" + report)
        
        # Save report to file
        report_file = Path(__file__).parent / "auth_test_report.txt"
        report_file.write_text(report)
        print(f"\n📄 Report saved to: {report_file}")
        
        # Exit with appropriate code
        overall_success = all(r["passed"] for r in results)
        sys.exit(0 if overall_success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️ Test execution interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()