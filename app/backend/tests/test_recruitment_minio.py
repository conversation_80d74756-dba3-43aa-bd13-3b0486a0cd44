"""
Functional tests for MinIO upload functionality in Recruitment Service
Tests the _upload_export_to_storage method implementation
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timezone, timedelta
from io import BytesIO
from fastapi import HTTPException

from app.services.recruitment_service import RecruitmentService


class TestRecruitmentServiceMinIO:
    """Test MinIO upload functionality in recruitment service"""
    
    @pytest.fixture
    def recruitment_service(self, db_session):
        """Create recruitment service instance"""
        return RecruitmentService(db_session)
    
    @pytest.fixture
    def sample_export_data(self):
        """Sample export data for testing"""
        return {
            "file_content": b"Sample CSV content\nheader1,header2\nvalue1,value2",
            "file_name": "recruitment_export_12345.csv",
            "content_type": "text/csv",
            "export_id": "test-export-123"
        }
    
    @pytest.mark.asyncio
    async def test_upload_export_to_storage_success(self, recruitment_service, sample_export_data):
        """Test successful file upload to MinIO storage"""
        # Mock StorageService
        mock_storage_service = Mock()
        mock_storage_service.upload_file = AsyncMock()
        mock_storage_service.generate_presigned_url = Mock(
            return_value="https://minio.example.com/bucket/exports/test-export-123/recruitment_export_12345.csv?X-Amz-Expires=86400"
        )
        
        with patch('app.services.recruitment_service.StorageService', return_value=mock_storage_service):
            result = await recruitment_service._upload_export_to_storage(
                file_content=sample_export_data["file_content"],
                file_name=sample_export_data["file_name"],
                content_type=sample_export_data["content_type"],
                export_id=sample_export_data["export_id"]
            )
            
            # Verify StorageService methods were called correctly
            mock_storage_service.upload_file.assert_called_once()
            upload_call_args = mock_storage_service.upload_file.call_args
            
            # Check upload parameters
            assert upload_call_args.kwargs["object_key"] == f"exports/{sample_export_data['export_id']}/{sample_export_data['file_name']}"
            assert upload_call_args.kwargs["content_type"] == sample_export_data["content_type"]
            
            # Check file_data is BytesIO
            file_data = upload_call_args.kwargs["file_data"]
            assert isinstance(file_data, BytesIO)
            file_data.seek(0)
            assert file_data.read() == sample_export_data["file_content"]
            
            # Check metadata
            metadata = upload_call_args.kwargs["metadata"]
            assert metadata["export_id"] == sample_export_data["export_id"]
            assert metadata["file_type"] == "recruitment_export"
            assert "created_at" in metadata
            
            # Verify presigned URL generation
            mock_storage_service.generate_presigned_url.assert_called_once_with(
                object_name=f"exports/{sample_export_data['export_id']}/{sample_export_data['file_name']}",
                expires=timedelta(hours=24)
            )
            
            # Check return value
            assert result == "https://minio.example.com/bucket/exports/test-export-123/recruitment_export_12345.csv?X-Amz-Expires=86400"
    
    @pytest.mark.asyncio
    async def test_upload_export_to_storage_upload_failure(self, recruitment_service, sample_export_data):
        """Test handling of MinIO upload failure"""
        # Mock StorageService with upload failure
        mock_storage_service = Mock()
        mock_storage_service.upload_file = AsyncMock(
            side_effect=Exception("MinIO connection timeout")
        )
        
        with patch('app.services.recruitment_service.StorageService', return_value=mock_storage_service):
            with pytest.raises(HTTPException) as exc_info:
                await recruitment_service._upload_export_to_storage(
                    file_content=sample_export_data["file_content"],
                    file_name=sample_export_data["file_name"],
                    content_type=sample_export_data["content_type"],
                    export_id=sample_export_data["export_id"]
                )
            
            # Verify exception details
            assert exc_info.value.status_code == 500
            assert "FILE_UPLOAD_FAILED" in exc_info.value.detail
            assert "MinIO connection timeout" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_upload_export_to_storage_presigned_url_failure(self, recruitment_service, sample_export_data):
        """Test handling of presigned URL generation failure"""
        # Mock StorageService with presigned URL failure
        mock_storage_service = Mock()
        mock_storage_service.upload_file = AsyncMock()
        mock_storage_service.generate_presigned_url = Mock(
            side_effect=Exception("Failed to generate presigned URL")
        )
        
        with patch('app.services.recruitment_service.StorageService', return_value=mock_storage_service):
            with pytest.raises(HTTPException) as exc_info:
                await recruitment_service._upload_export_to_storage(
                    file_content=sample_export_data["file_content"],
                    file_name=sample_export_data["file_name"],
                    content_type=sample_export_data["content_type"],
                    export_id=sample_export_data["export_id"]
                )
            
            # Upload should have succeeded
            mock_storage_service.upload_file.assert_called_once()
            
            # Verify exception details
            assert exc_info.value.status_code == 500
            assert "FILE_UPLOAD_FAILED" in exc_info.value.detail
            assert "Failed to generate presigned URL" in exc_info.value.detail
    
    @pytest.mark.asyncio
    async def test_upload_export_different_file_types(self, recruitment_service):
        """Test upload with different file types and content"""
        test_cases = [
            {
                "content": b"Excel binary content here",
                "filename": "export_test.xlsx", 
                "content_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                "export_id": "excel-export-456"
            },
            {
                "content": b"%PDF-1.4 PDF content here",
                "filename": "export_report.pdf",
                "content_type": "application/pdf", 
                "export_id": "pdf-export-789"
            }
        ]
        
        mock_storage_service = Mock()
        mock_storage_service.upload_file = AsyncMock()
        mock_storage_service.generate_presigned_url = Mock(
            return_value="https://minio.example.com/test-url"
        )
        
        with patch('app.services.recruitment_service.StorageService', return_value=mock_storage_service):
            for test_case in test_cases:
                result = await recruitment_service._upload_export_to_storage(
                    file_content=test_case["content"],
                    file_name=test_case["filename"],
                    content_type=test_case["content_type"],
                    export_id=test_case["export_id"]
                )
                
                # Verify correct object key format
                expected_object_key = f"exports/{test_case['export_id']}/{test_case['filename']}"
                
                upload_call = mock_storage_service.upload_file.call_args_list[-1]  # Get last call
                assert upload_call.kwargs["object_key"] == expected_object_key
                assert upload_call.kwargs["content_type"] == test_case["content_type"]
                
                # Verify file content
                file_data = upload_call.kwargs["file_data"]
                file_data.seek(0)
                assert file_data.read() == test_case["content"]
                
                assert result == "https://minio.example.com/test-url"
    
    @pytest.mark.asyncio
    async def test_upload_export_metadata_validation(self, recruitment_service, sample_export_data):
        """Test that metadata is correctly formatted"""
        mock_storage_service = Mock()
        mock_storage_service.upload_file = AsyncMock()
        mock_storage_service.generate_presigned_url = Mock(
            return_value="https://minio.example.com/test-url"
        )
        
        with patch('app.services.recruitment_service.StorageService', return_value=mock_storage_service):
            await recruitment_service._upload_export_to_storage(
                file_content=sample_export_data["file_content"],
                file_name=sample_export_data["file_name"],
                content_type=sample_export_data["content_type"],
                export_id=sample_export_data["export_id"]
            )
            
            # Get the metadata from the upload call
            upload_call_args = mock_storage_service.upload_file.call_args
            metadata = upload_call_args.kwargs["metadata"]
            
            # Validate metadata structure and content
            assert metadata["export_id"] == sample_export_data["export_id"]
            assert metadata["file_type"] == "recruitment_export"
            
            # Validate created_at is proper ISO format
            created_at = metadata["created_at"]
            assert isinstance(created_at, str)
            # Should be parseable as datetime
            parsed_datetime = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
            assert isinstance(parsed_datetime, datetime)
            
            # Should be recent (within last minute)
            time_diff = datetime.now(timezone.utc) - parsed_datetime
            assert time_diff.total_seconds() < 60
    
    @pytest.mark.asyncio
    async def test_integration_export_dashboard_data_with_minio(self, recruitment_service, sample_export_data):
        """Integration test: export_dashboard_data method using MinIO upload"""
        # Mock database queries to return sample data
        mock_stats = Mock()
        mock_stats.dict.return_value = {"new_candidates": 5, "pending_matches": 3}
        
        mock_trends = Mock()
        mock_trends.dict.return_value = {"period": "30d", "metrics": {}}
        
        # Mock the service methods
        with patch.object(recruitment_service, 'get_dashboard_stats', return_value=mock_stats):
            with patch.object(recruitment_service, 'get_trends', return_value=mock_trends):
                with patch.object(recruitment_service, 'get_recruitment_summary', return_value={}):
                    # Mock the MinIO upload
                    with patch.object(recruitment_service, '_upload_export_to_storage', return_value="https://test.url") as mock_upload:
                        
                        result = await recruitment_service.export_dashboard_data(
                            format="csv",
                            metrics=["stats", "trends"],
                            date_range="month",
                            include_charts=False,
                            user_id=1
                        )
                        
                        # Verify export response
                        assert result.format == "csv"
                        assert result.file_url == "https://test.url"
                        assert isinstance(result.expires_at, datetime)
                        assert result.size_bytes > 0
                        
                        # Verify upload was called with correct parameters
                        mock_upload.assert_called_once()
                        upload_args = mock_upload.call_args
                        
                        assert upload_args.kwargs["content_type"] == "text/csv"
                        assert upload_args.kwargs["file_name"].endswith(".csv")
                        assert len(upload_args.kwargs["file_content"]) > 0
    
    @pytest.mark.asyncio
    async def test_upload_export_error_logging(self, recruitment_service, sample_export_data):
        """Test that errors are properly logged"""
        mock_storage_service = Mock()
        mock_storage_service.upload_file = AsyncMock(
            side_effect=Exception("Storage service unavailable")
        )
        
        with patch('app.services.recruitment_service.StorageService', return_value=mock_storage_service):
            # Mock logger to capture error logs
            with patch('app.services.recruitment_service.logger') as mock_logger:
                with pytest.raises(HTTPException):
                    await recruitment_service._upload_export_to_storage(
                        file_content=sample_export_data["file_content"],
                        file_name=sample_export_data["file_name"],
                        content_type=sample_export_data["content_type"],
                        export_id=sample_export_data["export_id"]
                    )
                
                # Verify error was logged
                mock_logger.error.assert_called_once()
                log_call_args = mock_logger.error.call_args[0]
                
                assert sample_export_data["file_name"] in log_call_args[0]
                assert "Storage service unavailable" in log_call_args[0]