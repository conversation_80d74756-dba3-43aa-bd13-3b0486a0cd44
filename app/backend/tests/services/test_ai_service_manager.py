"""
Unit tests for AI Service Manager
Tests the unified AI service functionality
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timezone
import json
from typing import Dict, Any

from app.services.ai_service_manager import ai_service_manager
from app.core.config import settings


class TestAIServiceManager:
    """Test suite for AIServiceManager"""

    @pytest.fixture
    def mock_settings(self):
        """Mock settings"""
        with patch('app.services.ai_service_manager.settings') as mock:
            mock.DEEPSEEK_API_KEY = "test_api_key"
            mock.DEEPSEEK_API_URL = "https://api.deepseek.com/v1"
            mock.DEEPSEEK_MODEL = "deepseek-r1"
            mock.MOONSHOT_API_KEY = "test_moonshot_key"
            mock.OPENROUTER_API_KEY = "test_openrouter_key"
            mock.QWEN_API_KEY = "test_qwen_key"
            yield mock

    @pytest.fixture
    def sample_text_response(self):
        """Sample text generation response"""
        return "This is a sample response from the AI model"

    @pytest.fixture
    def sample_embedding_response(self):
        """Sample embedding response"""
        return [0.1, 0.2, 0.3, 0.4, 0.5] * 200  # 1000 dimensions

    @pytest.mark.asyncio
    async def test_generate_text_success(self, mock_settings, sample_text_response):
        """Test successful text generation"""
        # Mock the HTTP request
        with patch.object(ai_service_manager, '_call_llm_api', new_callable=AsyncMock) as mock_call:
            mock_call.return_value = sample_text_response
            
            # Call the method
            result = await ai_service_manager.generate_text(
                "Test prompt",
                temperature=0.7,
                max_tokens=1000
            )
            
            # Assertions
            assert result == sample_text_response
            mock_call.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_embedding_success(self, mock_settings, sample_embedding_response):
        """Test successful embedding generation"""
        # Mock the HTTP request
        with patch.object(ai_service_manager, '_call_embedding_api', new_callable=AsyncMock) as mock_call:
            mock_call.return_value = sample_embedding_response
            
            # Call the method
            result = await ai_service_manager.generate_embedding("Test text")
            
            # Assertions
            assert result == sample_embedding_response
            assert len(result) == 1000  # Check embedding dimension

    @pytest.mark.asyncio
    async def test_provider_switching(self, mock_settings):
        """Test switching between providers"""
        # Get initial provider
        initial_provider = ai_service_manager.current_llm_provider
        
        # Switch to a different provider
        new_provider = "moonshot" if initial_provider != "moonshot" else "deepseek"
        ai_service_manager.set_llm_provider(new_provider)
        
        # Check the switch was successful
        assert ai_service_manager.current_llm_provider == new_provider
        
        # Switch back
        ai_service_manager.set_llm_provider(initial_provider)
        assert ai_service_manager.current_llm_provider == initial_provider

    @pytest.mark.asyncio
    async def test_health_check(self, mock_settings):
        """Test health check functionality"""
        # Mock the health check
        with patch.object(ai_service_manager, 'check_all_providers_health', new_callable=AsyncMock) as mock_health:
            mock_health.return_value = {
                "status": "healthy",
                "current_llm_provider": "deepseek",
                "providers": {
                    "deepseek": {"status": "healthy", "latency": 100},
                    "moonshot": {"status": "healthy", "latency": 150},
                    "openrouter": {"status": "unhealthy", "error": "API key invalid"},
                    "qwen": {"status": "healthy", "latency": 120}
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Call health check
            result = await ai_service_manager.check_all_providers_health()
            
            # Assertions
            assert result["status"] == "healthy"
            assert "providers" in result
            assert len(result["providers"]) == 4
            assert result["providers"]["deepseek"]["status"] == "healthy"
            assert result["providers"]["openrouter"]["status"] == "unhealthy"

    @pytest.mark.asyncio
    async def test_retry_on_failure(self, mock_settings):
        """Test retry mechanism on API failure"""
        # Mock the API call to fail first, then succeed
        with patch.object(ai_service_manager, '_call_llm_api', new_callable=AsyncMock) as mock_call:
            mock_call.side_effect = [
                Exception("API error"),
                "Success response"
            ]
            
            # The manager should retry and eventually succeed
            result = await ai_service_manager.generate_text(
                "Test prompt",
                max_retries=2
            )
            
            # Should have succeeded on second try
            assert result == "Success response"
            assert mock_call.call_count == 2

    @pytest.mark.asyncio
    async def test_provider_fallback(self, mock_settings):
        """Test fallback to another provider on failure"""
        # Mock the first provider to fail
        with patch.object(ai_service_manager, '_call_llm_api', new_callable=AsyncMock) as mock_call:
            # First call fails, second call (with fallback provider) succeeds
            mock_call.side_effect = [
                Exception("Provider unavailable"),
                "Fallback response"
            ]
            
            # Set up provider list
            ai_service_manager.llm_providers = ["deepseek", "moonshot"]
            
            # Should fallback to next provider
            result = await ai_service_manager.generate_text(
                "Test prompt",
                fallback=True
            )
            
            # Should have succeeded with fallback
            assert result == "Fallback response"
            assert mock_call.call_count == 2

    @pytest.mark.asyncio
    async def test_get_provider_config(self, mock_settings):
        """Test getting provider configuration"""
        # Get config for a provider
        config = ai_service_manager.get_provider_config("deepseek")
        
        # Should have required fields
        assert config is not None
        assert "api_key" in config or "api_url" in config
        assert "llm_model" in config or "model" in config

    @pytest.mark.asyncio
    async def test_rate_limiting(self, mock_settings):
        """Test rate limiting functionality"""
        # This would test rate limiting if implemented
        # For now, just verify the method exists
        assert hasattr(ai_service_manager, 'generate_text')
        assert hasattr(ai_service_manager, 'generate_embedding')

    @pytest.mark.asyncio
    async def test_structured_output(self, mock_settings):
        """Test generation with structured output (JSON mode)"""
        # Mock the API to return JSON
        json_response = json.dumps({
            "answer": "Test answer",
            "confidence": 0.95
        })
        
        with patch.object(ai_service_manager, '_call_llm_api', new_callable=AsyncMock) as mock_call:
            mock_call.return_value = json_response
            
            # Call with JSON mode
            result = await ai_service_manager.generate_text(
                "Generate JSON",
                response_format="json",
                temperature=0.1
            )
            
            # Should return valid JSON
            parsed = json.loads(result)
            assert "answer" in parsed
            assert parsed["confidence"] == 0.95