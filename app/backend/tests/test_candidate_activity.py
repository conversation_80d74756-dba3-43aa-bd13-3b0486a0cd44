"""
Functional tests for candidate activity tracking functionality
Tests the last_active logic in candidate CRUD operations
"""
import pytest
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, AsyncMock, patch
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession

from app.crud.candidate import candidate as candidate_crud
from app.models.candidate import Candidate


class TestCandidateActivity:
    """Test candidate activity tracking functionality"""
    
    @pytest.mark.asyncio
    async def test_update_last_active_success(self, db_session: AsyncSession, test_candidate):
        """Test successful last_active_at timestamp update"""
        # Get initial timestamp 
        initial_last_active = test_candidate.last_active_at
        
        # Update last active
        result = await candidate_crud.update_last_active(db_session, test_candidate.id)
        
        # Verify update was successful
        assert result is True
        
        # Refresh and verify the timestamp was updated
        await db_session.refresh(test_candidate)
        assert test_candidate.last_active_at is not None
        assert test_candidate.last_active_at != initial_last_active
        
        # Verify timestamp is recent (within last 5 seconds)
        time_diff = datetime.now(timezone.utc) - test_candidate.last_active_at
        assert time_diff.total_seconds() < 5
    
    @pytest.mark.asyncio
    async def test_update_last_active_nonexistent_candidate(self, db_session: AsyncSession):
        """Test update_last_active with non-existent candidate ID"""
        # Try to update non-existent candidate
        result = await candidate_crud.update_last_active(db_session, 999999)
        
        # Should return False as no rows were affected
        assert result is False
    
    @pytest.mark.asyncio
    async def test_update_last_active_database_error(self, db_session: AsyncSession, test_candidate):
        """Test update_last_active handles database errors gracefully"""
        # Mock database session to raise exception
        mock_session = AsyncMock()
        mock_session.execute = AsyncMock(side_effect=Exception("Database connection error"))
        mock_session.commit = AsyncMock()
        mock_session.rollback = AsyncMock()
        
        result = await candidate_crud.update_last_active(mock_session, test_candidate.id)
        
        # Should return False and rollback transaction
        assert result is False
        mock_session.rollback.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_active_candidates_count_default_days(self, db_session: AsyncSession, test_user):
        """Test get_active_candidates_count with default 30 days"""
        # Create test candidates with different activity patterns
        now = datetime.now(timezone.utc)
        
        # Recently active candidate
        active_candidate = Candidate(
            name="Active User",
            email="<EMAIL>", 
            created_by=test_user.id,
            last_active_at=now - timedelta(days=15),
            created_at=now - timedelta(days=45),
            is_deleted=False
        )
        db_session.add(active_candidate)
        
        # Recently created candidate (no last_active_at)
        new_candidate = Candidate(
            name="New User",
            email="<EMAIL>",
            created_by=test_user.id,
            last_active_at=None,
            created_at=now - timedelta(days=10),
            is_deleted=False
        )
        db_session.add(new_candidate)
        
        # Old inactive candidate
        old_candidate = Candidate(
            name="Old User",
            email="<EMAIL>",
            created_by=test_user.id,
            last_active_at=now - timedelta(days=60),
            created_at=now - timedelta(days=90),
            is_deleted=False
        )
        db_session.add(old_candidate)
        
        # Deleted candidate (should not count)
        deleted_candidate = Candidate(
            name="Deleted User", 
            email="<EMAIL>",
            created_by=test_user.id,
            last_active_at=now - timedelta(days=5),
            created_at=now - timedelta(days=20),
            is_deleted=True
        )
        db_session.add(deleted_candidate)
        
        await db_session.commit()
        
        # Get active count
        active_count = await candidate_crud.get_active_candidates_count(db_session)
        
        # Should count: active_candidate (recent activity) + new_candidate (recent creation)
        # Should not count: old_candidate (old activity) + deleted_candidate (deleted)
        assert active_count == 2
    
    @pytest.mark.asyncio
    async def test_get_active_candidates_count_custom_days(self, db_session: AsyncSession, test_user):
        """Test get_active_candidates_count with custom day range"""
        now = datetime.now(timezone.utc)
        
        # Create candidates with different activity times
        candidates_data = [
            # Within 7 days - should count
            {"days_ago": 5, "should_count_7d": True, "should_count_60d": True},
            # Within 60 days but not 7 - should only count for 60d
            {"days_ago": 30, "should_count_7d": False, "should_count_60d": True},
            # Beyond 60 days - should not count
            {"days_ago": 90, "should_count_7d": False, "should_count_60d": False}
        ]
        
        for i, data in enumerate(candidates_data):
            candidate = Candidate(
                name=f"Test User {i}",
                email=f"test{i}@test.com",
                created_by=test_user.id,
                last_active_at=now - timedelta(days=data["days_ago"]),
                created_at=now - timedelta(days=data["days_ago"] + 10),
                is_deleted=False
            )
            db_session.add(candidate)
        
        await db_session.commit()
        
        # Test 7 days
        count_7d = await candidate_crud.get_active_candidates_count(db_session, days=7)
        expected_7d = sum(1 for d in candidates_data if d["should_count_7d"])
        assert count_7d == expected_7d
        
        # Test 60 days
        count_60d = await candidate_crud.get_active_candidates_count(db_session, days=60)
        expected_60d = sum(1 for d in candidates_data if d["should_count_60d"])
        assert count_60d == expected_60d
    
    @pytest.mark.asyncio
    async def test_get_active_candidates_count_or_logic(self, db_session: AsyncSession, test_user):
        """Test the OR logic: recent activity OR recent creation (if no activity)"""
        now = datetime.now(timezone.utc)
        
        # Case 1: Has recent last_active_at (should count)
        recent_active = Candidate(
            name="Recent Active",
            email="<EMAIL>",
            created_by=test_user.id,
            last_active_at=now - timedelta(days=10),
            created_at=now - timedelta(days=100),  # Old creation
            is_deleted=False
        )
        db_session.add(recent_active)
        
        # Case 2: No last_active_at but recent creation (should count)
        recent_created = Candidate(
            name="Recent Created",
            email="<EMAIL>", 
            created_by=test_user.id,
            last_active_at=None,
            created_at=now - timedelta(days=10),
            is_deleted=False
        )
        db_session.add(recent_created)
        
        # Case 3: Old last_active_at and old creation (should not count)
        old_inactive = Candidate(
            name="Old Inactive",
            email="<EMAIL>",
            created_by=test_user.id,
            last_active_at=now - timedelta(days=60),
            created_at=now - timedelta(days=100),
            is_deleted=False
        )
        db_session.add(old_inactive)
        
        # Case 4: No last_active_at and old creation (should not count)
        old_never_active = Candidate(
            name="Old Never Active",
            email="<EMAIL>",
            created_by=test_user.id, 
            last_active_at=None,
            created_at=now - timedelta(days=60),
            is_deleted=False
        )
        db_session.add(old_never_active)
        
        await db_session.commit()
        
        # Should count cases 1 and 2 only
        active_count = await candidate_crud.get_active_candidates_count(db_session, days=30)
        assert active_count == 2
    
    @pytest.mark.asyncio
    async def test_get_active_candidates_count_database_error(self, db_session: AsyncSession):
        """Test get_active_candidates_count handles database errors"""
        # Mock database session to raise exception
        mock_session = AsyncMock()
        mock_session.execute = AsyncMock(side_effect=Exception("Database query failed"))
        
        result = await candidate_crud.get_active_candidates_count(mock_session)
        
        # Should return 0 on error
        assert result == 0
    
    @pytest.mark.asyncio 
    async def test_integration_candidate_stats_service_uses_active_logic(self, db_session: AsyncSession, test_user):
        """Integration test: candidate_stats service uses the improved active logic"""
        from app.services.candidate_stats import CandidateStatsService
        
        now = datetime.now(timezone.utc)
        
        # Create test data
        active_candidate = Candidate(
            name="Active Candidate",
            email="<EMAIL>",
            created_by=test_user.id,
            last_active_at=now - timedelta(days=15),
            created_at=now - timedelta(days=45),
            is_deleted=False
        )
        db_session.add(active_candidate)
        
        inactive_candidate = Candidate(
            name="Inactive Candidate", 
            email="<EMAIL>",
            created_by=test_user.id,
            last_active_at=now - timedelta(days=45),
            created_at=now - timedelta(days=60),
            is_deleted=False
        )
        db_session.add(inactive_candidate)
        
        await db_session.commit()
        
        # Create stats service
        stats_service = CandidateStatsService(db_session)
        
        # Mock the CRUD call to verify it's being used
        with patch.object(candidate_crud, 'get_active_candidates_count', 
                         wraps=candidate_crud.get_active_candidates_count) as mock_crud:
            
            # Get overview stats
            stats = await stats_service.get_overview_stats(test_user)
            
            # Verify the CRUD method was called
            mock_crud.assert_called_once_with(db_session, days=30)
            
            # Verify the result uses the improved logic
            assert "active_candidates" in stats
            assert stats["active_candidates"] == 1  # Only the active candidate
    
    @pytest.mark.asyncio
    async def test_update_last_active_timestamp_precision(self, db_session: AsyncSession, test_candidate):
        """Test that last_active_at timestamps are properly timezone-aware"""
        # Update last active
        await candidate_crud.update_last_active(db_session, test_candidate.id)
        
        # Refresh candidate
        await db_session.refresh(test_candidate)
        
        # Verify timestamp is timezone-aware UTC
        assert test_candidate.last_active_at.tzinfo is not None
        assert test_candidate.last_active_at.tzinfo == timezone.utc
        
        # Verify it's recent
        time_diff = datetime.now(timezone.utc) - test_candidate.last_active_at
        assert time_diff.total_seconds() < 2
    
    @pytest.mark.asyncio
    async def test_get_active_candidates_count_edge_cases(self, db_session: AsyncSession, test_user):
        """Test edge cases for active candidates count"""
        now = datetime.now(timezone.utc)
        
        # Edge case: exactly at the boundary (30 days ago)
        boundary_candidate = Candidate(
            name="Boundary Candidate",
            email="<EMAIL>",
            created_by=test_user.id,
            last_active_at=now - timedelta(days=30, seconds=1),  # Just over 30 days
            created_at=now - timedelta(days=50),
            is_deleted=False
        )
        db_session.add(boundary_candidate)
        
        # Edge case: exactly at boundary but recently created
        boundary_created = Candidate(
            name="Boundary Created",
            email="<EMAIL>", 
            created_by=test_user.id,
            last_active_at=None,
            created_at=now - timedelta(days=30, seconds=1),  # Just over 30 days
            is_deleted=False
        )
        db_session.add(boundary_created)
        
        await db_session.commit()
        
        # Both should not count (just over the 30-day limit)
        active_count = await candidate_crud.get_active_candidates_count(db_session, days=30)
        assert active_count == 0
        
        # But should count with 31 days
        active_count_31 = await candidate_crud.get_active_candidates_count(db_session, days=31)
        assert active_count_31 == 2
    
    @pytest.mark.asyncio
    async def test_concurrent_last_active_updates(self, db_session: AsyncSession, test_candidate):
        """Test concurrent updates to last_active_at"""
        # Simulate concurrent updates
        import asyncio
        
        async def update_last_active():
            return await candidate_crud.update_last_active(db_session, test_candidate.id)
        
        # Run multiple concurrent updates
        results = await asyncio.gather(
            update_last_active(),
            update_last_active(),
            update_last_active(),
            return_exceptions=True
        )
        
        # At least one should succeed
        successful_updates = [r for r in results if r is True]
        assert len(successful_updates) >= 1
        
        # Verify final state is valid
        await db_session.refresh(test_candidate)
        assert test_candidate.last_active_at is not None
        assert test_candidate.last_active_at.tzinfo == timezone.utc