"""
Comprehensive AI Provider Testing Suite
Tests all 6 AI providers with real API calls for models endpoint and basic conversation functionality
"""
import asyncio
import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timezone

import pytest

# Try to import ollama, make it optional for testing
try:
    import ollama
    OLLAMA_AVAILABLE = True
except ImportError:
    OLLAMA_AVAILABLE = False
    ollama = None

from app.services.ai_service_manager import ai_service_manager, AIProvider
from app.core.ai_config import ai_settings

from .base_provider_test import BaseProviderTest, TestResult, TestStatus, ProviderTestConfig
from .test_config import PROVIDER_EXPECTATIONS, PROVIDER_TIMEOUTS, TEST_SCENARIOS

logger = logging.getLogger(__name__)


class ComprehensiveProviderValidator(BaseProviderTest):
    """
    Comprehensive validator for all AI providers using real API calls
    """
    
    def __init__(self, config: Optional[ProviderTestConfig] = None):
        super().__init__(config)
        self.ai_manager = ai_service_manager
        self.all_providers = ["zhipu", "deepseek", "moonshot", "openrouter", "qwen", "ollama"]
    
    async def test_models_endpoint(self, provider: str) -> TestResult:
        """
        Test models endpoint for specific provider
        
        Args:
            provider: Provider name
            
        Returns:
            TestResult: Models endpoint test results
        """
        try:
            # Check if provider is configured
            if not ai_settings.is_provider_available(provider):
                return TestResult(
                    status=TestStatus.NOT_CONFIGURED,
                    provider=provider,
                    test_type="models",
                    error=f"Provider {provider} is not configured (missing API key or configuration)"
                )
            
            # Get client and config
            client, config = self.ai_manager.get_llm_client(provider)
            
            # Execute test with timing
            if provider == "ollama":
                result, response_time = await self.measure_execution_time(
                    self._test_ollama_models
                )
            else:
                result, response_time = await self.measure_execution_time(
                    self._test_openai_compatible_models, client
                )
            
            # Validate response format
            if not self.validate_response_format(result, provider, "models"):
                return TestResult(
                    status=TestStatus.FAILED,
                    provider=provider,
                    test_type="models",
                    response_time_ms=response_time,
                    error="Invalid response format from models endpoint",
                    data={"raw_response": str(result)[:200]}
                )
            
            # Extract response data
            response_data = self.extract_response_data(result, "models", provider)
            
            return TestResult(
                status=TestStatus.SUCCESS,
                provider=provider,
                test_type="models",
                response_time_ms=response_time,
                data=response_data
            )
            
        except Exception as e:
            self.logger.error(f"Models endpoint test failed for {provider}: {e}")
            return TestResult(
                status=TestStatus.FAILED,
                provider=provider,
                test_type="models",
                error=self.create_diagnostic_message(e, provider, "models"),
                error_category=self.categorize_error(e)
            )
    
    async def test_conversation(self, provider: str) -> TestResult:
        """
        Test basic conversation functionality
        
        Args:
            provider: Provider name
            
        Returns:
            TestResult: Conversation test results
        """
        try:
            # Check if provider is configured
            if not ai_settings.is_provider_available(provider):
                return TestResult(
                    status=TestStatus.NOT_CONFIGURED,
                    provider=provider,
                    test_type="conversation",
                    error=f"Provider {provider} is not configured"
                )
            
            # Get client and config
            client, config = self.ai_manager.get_llm_client(provider)
            
            # Prepare conversation request
            messages = [
                {"role": "user", "content": self.config.conversation_test_prompt}
            ]
            
            # Execute test with timing
            if provider == "ollama":
                result, response_time = await self.measure_execution_time(
                    self._test_ollama_conversation, messages, config
                )
            else:
                result, response_time = await self.measure_execution_time(
                    self._test_openai_compatible_conversation, client, messages, config
                )
            
            # Validate response format
            if not self.validate_response_format(result, provider, "conversation"):
                return TestResult(
                    status=TestStatus.FAILED,
                    provider=provider,
                    test_type="conversation",
                    response_time_ms=response_time,
                    error="Invalid response format from conversation endpoint",
                    data={"raw_response": str(result)[:200]}
                )
            
            # Extract response data
            response_data = self.extract_response_data(result, "conversation", provider)
            
            return TestResult(
                status=TestStatus.SUCCESS,
                provider=provider,
                test_type="conversation", 
                response_time_ms=response_time,
                data=response_data
            )
            
        except Exception as e:
            self.logger.error(f"Conversation test failed for {provider}: {e}")
            return TestResult(
                status=TestStatus.FAILED,
                provider=provider,
                test_type="conversation",
                error=self.create_diagnostic_message(e, provider, "conversation"),
                error_category=self.categorize_error(e)
            )
    
    async def _test_ollama_models(self) -> Any:
        """Test Ollama models endpoint"""
        if not OLLAMA_AVAILABLE:
            raise Exception("Ollama not available - install with: pip install ollama")
        
        # Use asyncio.to_thread for synchronous Ollama client
        return await asyncio.to_thread(ollama.list)
    
    async def _test_openai_compatible_models(self, client) -> Any:
        """Test OpenAI-compatible models endpoint"""
        return await client.models.list()
    
    async def _test_ollama_conversation(self, messages: List[Dict], config: Dict) -> Any:
        """Test Ollama conversation"""
        if not OLLAMA_AVAILABLE:
            raise Exception("Ollama not available - install with: pip install ollama")
        
        # Convert messages to Ollama format
        prompt = messages[0]["content"]
        
        response = await asyncio.to_thread(
            ollama.chat,
            model=config.get("llm_model", "qwen2.5:14b"),
            messages=messages,
            options={
                "temperature": config.get("temperature", 0.7),
                "num_predict": min(config.get("max_tokens", 4000), 4000)
            }
        )
        
        # Convert to OpenAI-like format for consistent validation
        class OllamaResponse:
            def __init__(self, response_data):
                self.choices = [OllamaChoice(response_data)]
        
        class OllamaChoice:
            def __init__(self, response_data):
                self.message = OllamaMessage(response_data)
        
        class OllamaMessage:
            def __init__(self, response_data):
                self.content = response_data.get("message", {}).get("content", "")
        
        return OllamaResponse(response)
    
    async def _test_openai_compatible_conversation(self, client, messages: List[Dict], config: Dict) -> Any:
        """Test OpenAI-compatible conversation"""
        return await client.chat.completions.create(
            model=config.get("llm_model", "gpt-3.5-turbo"),
            messages=messages,
            max_tokens=min(config.get("max_tokens", 4000), 4000),
            temperature=config.get("temperature", 0.7)
        )
    
    async def test_all_providers_models_endpoint(self) -> Dict[str, TestResult]:
        """
        Test models endpoint for all providers
        
        Returns:
            Dict: Results by provider name
        """
        results = {}
        
        self.logger.info("Testing models endpoint for all providers...")
        
        # Test all providers concurrently
        tasks = [
            self.test_models_endpoint(provider) 
            for provider in self.all_providers
        ]
        
        test_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for provider, result in zip(self.all_providers, test_results):
            if isinstance(result, Exception):
                results[provider] = TestResult(
                    status=TestStatus.ERROR,
                    provider=provider,
                    test_type="models",
                    error=str(result),
                    error_category=self.categorize_error(result)
                )
            else:
                results[provider] = result
        
        return results
    
    async def test_all_providers_conversation(self) -> Dict[str, TestResult]:
        """
        Test conversation for all providers
        
        Returns:
            Dict: Results by provider name
        """
        results = {}
        
        self.logger.info("Testing conversation for all providers...")
        
        # Test all providers concurrently
        tasks = [
            self.test_conversation(provider)
            for provider in self.all_providers
        ]
        
        test_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for provider, result in zip(self.all_providers, test_results):
            if isinstance(result, Exception):
                results[provider] = TestResult(
                    status=TestStatus.ERROR,
                    provider=provider,
                    test_type="conversation",
                    error=str(result),
                    error_category=self.categorize_error(result)
                )
            else:
                results[provider] = result
        
        return results
    
    async def test_provider_fallback_chain(self) -> Dict[str, Any]:
        """
        Test provider fallback chain validation
        
        Returns:
            Dict: Fallback chain test results
        """
        self.logger.info("Testing provider fallback chain...")
        
        try:
            # Get configured fallback chains
            llm_chain = ai_settings.get_llm_fallback_chain()
            embedding_chain = ai_settings.get_embedding_fallback_chain()
            
            # Test each provider in fallback chain
            llm_results = {}
            for provider in llm_chain:
                health = await self.ai_manager.check_provider_health(provider)
                llm_results[provider] = {
                    "status": health.get("status"),
                    "configured": ai_settings.is_provider_available(provider),
                    "response_time_ms": health.get("response_time_ms")
                }
            
            embedding_results = {}
            for provider in embedding_chain:
                health = await self.ai_manager.check_provider_health(provider)
                embedding_results[provider] = {
                    "status": health.get("status"),
                    "configured": ai_settings.is_provider_available(provider),
                    "response_time_ms": health.get("response_time_ms")
                }
            
            return {
                "llm_fallback_chain": {
                    "chain": llm_chain,
                    "results": llm_results,
                    "healthy_count": sum(1 for r in llm_results.values() if r["status"] == "healthy")
                },
                "embedding_fallback_chain": {
                    "chain": embedding_chain,
                    "results": embedding_results,
                    "healthy_count": sum(1 for r in embedding_results.values() if r["status"] == "healthy")
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Fallback chain test failed: {e}")
            return {
                "status": "error",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    async def test_provider_specific_configurations(self) -> Dict[str, Any]:
        """
        Test provider-specific configuration validation
        
        Returns:
            Dict: Configuration validation results
        """
        self.logger.info("Testing provider-specific configurations...")
        
        results = {}
        
        for provider in self.all_providers:
            try:
                expectations = PROVIDER_EXPECTATIONS.get(provider, {})
                config = self.ai_manager.get_provider_config(provider)
                
                if config is None:
                    results[provider] = {
                        "status": "not_configured",
                        "error": "Provider configuration not found"
                    }
                    continue
                
                # Validate configuration against expectations
                validation_result = {
                    "status": "validated",
                    "config_present": True,
                    "expected_models": expectations.get("expected_models", []),
                    "configured_llm_model": config.get("llm_model"),
                    "configured_embedding_model": config.get("embedding_model"),
                    "api_base_url": getattr(ai_settings, f"{provider.upper()}_API_BASE", None),
                    "has_api_key": bool(getattr(ai_settings, f"{provider.upper()}_API_KEY", None)),
                    "timeout_config": PROVIDER_TIMEOUTS.get(provider, 30),
                    "special_requirements": expectations.get("special_requirements", "None")
                }
                
                # Additional validations
                if expectations.get("models_endpoint_required") and not validation_result["configured_llm_model"]:
                    validation_result["warnings"] = validation_result.get("warnings", [])
                    validation_result["warnings"].append("LLM model not configured but required")
                
                results[provider] = validation_result
                
            except Exception as e:
                results[provider] = {
                    "status": "error",
                    "error": str(e)
                }
        
        return {
            "provider_configurations": results,
            "total_providers": len(self.all_providers),
            "configured_providers": sum(1 for r in results.values() if r.get("status") != "not_configured"),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    
    async def run_comprehensive_test_suite(
        self, 
        providers: Optional[List[str]] = None,
        test_types: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Run comprehensive test suite for selected providers and test types
        
        Args:
            providers: List of provider names to test, None for all
            test_types: List of test types to run, None for all
            
        Returns:
            Dict: Complete test suite results
        """
        start_time = datetime.now(timezone.utc)
        
        # Default to all providers and test types
        providers = providers or self.all_providers
        test_types = test_types or ["models", "conversation", "fallback", "configuration"]
        
        self.logger.info(f"Running comprehensive test suite for providers: {providers}")
        self.logger.info(f"Test types: {test_types}")
        
        results = {
            "suite_info": {
                "start_time": start_time.isoformat(),
                "providers_tested": providers,
                "test_types": test_types,
                "total_providers": len(providers)
            },
            "test_results": {}
        }
        
        # Run models endpoint tests
        if "models" in test_types:
            models_results = {}
            for provider in providers:
                models_results[provider] = await self.test_models_endpoint(provider)
            results["test_results"]["models"] = models_results
        
        # Run conversation tests
        if "conversation" in test_types:
            conversation_results = {}
            for provider in providers:
                conversation_results[provider] = await self.test_conversation(provider)
            results["test_results"]["conversation"] = conversation_results
        
        # Run fallback chain tests
        if "fallback" in test_types:
            results["test_results"]["fallback_chain"] = await self.test_provider_fallback_chain()
        
        # Run configuration validation tests
        if "configuration" in test_types:
            results["test_results"]["configuration"] = await self.test_provider_specific_configurations()
        
        # Calculate summary statistics
        end_time = datetime.now(timezone.utc)
        execution_time = (end_time - start_time).total_seconds()
        
        # Count successful/failed tests
        success_count = 0
        total_count = 0
        failed_providers = []
        
        for test_type in ["models", "conversation"]:
            if test_type in results["test_results"]:
                for provider, result in results["test_results"][test_type].items():
                    total_count += 1
                    if result.status == TestStatus.SUCCESS:
                        success_count += 1
                    elif result.status in [TestStatus.FAILED, TestStatus.ERROR]:
                        failed_providers.append(f"{provider}:{test_type}")
        
        results["summary"] = {
            "end_time": end_time.isoformat(),
            "execution_time_seconds": round(execution_time, 2),
            "total_tests": total_count,
            "successful_tests": success_count,
            "failed_tests": total_count - success_count,
            "success_rate": round(success_count / total_count if total_count > 0 else 0, 2),
            "failed_providers": failed_providers,
            "overall_status": "success" if success_count == total_count else "partial_failure" if success_count > 0 else "failure"
        }
        
        return results


# Pytest fixtures and test functions
@pytest.fixture
def provider_validator():
    """Create provider validator instance"""
    config = ProviderTestConfig(
        timeout=30,
        max_retries=2,
        conversation_test_prompt="你好，你是谁？",
        expected_response_min_length=10
    )
    return ComprehensiveProviderValidator(config)


@pytest.mark.asyncio
async def test_all_providers_models_endpoint(provider_validator):
    """Test models endpoint for all providers"""
    results = await provider_validator.test_all_providers_models_endpoint()
    
    # Log results for debugging
    for provider, result in results.items():
        print(f"\n{provider.upper()} Models Test:")
        print(f"  Status: {result.status.value}")
        if result.data:
            print(f"  Models Count: {result.data.get('models_count', 'N/A')}")
            print(f"  Sample Models: {result.data.get('sample_models', [])}")
        if result.error:
            print(f"  Error: {result.error}")
        print(f"  Response Time: {result.response_time_ms}ms")
    
    # Assert at least one provider is working
    successful_providers = [p for p, r in results.items() if r.status == TestStatus.SUCCESS]
    assert len(successful_providers) > 0, f"No providers passed models test. Results: {results}"


@pytest.mark.asyncio  
async def test_all_providers_conversation(provider_validator):
    """Test conversation for all providers"""
    results = await provider_validator.test_all_providers_conversation()
    
    # Log results for debugging
    for provider, result in results.items():
        print(f"\n{provider.upper()} Conversation Test:")
        print(f"  Status: {result.status.value}")
        if result.data:
            response = result.data.get('response', '')
            print(f"  Response: {response[:100]}..." if len(response) > 100 else f"  Response: {response}")
            print(f"  Response Length: {result.data.get('response_length', 0)}")
            print(f"  Valid Response: {result.data.get('valid_response', False)}")
        if result.error:
            print(f"  Error: {result.error}")
        print(f"  Response Time: {result.response_time_ms}ms")
    
    # Assert at least one provider is working
    successful_providers = [p for p, r in results.items() if r.status == TestStatus.SUCCESS]
    assert len(successful_providers) > 0, f"No providers passed conversation test. Results: {results}"


@pytest.mark.asyncio
async def test_provider_fallback_chain(provider_validator):
    """Test provider fallback chain functionality"""
    results = await provider_validator.test_provider_fallback_chain()
    
    print("\nFallback Chain Test Results:")
    print(f"LLM Chain: {results['llm_fallback_chain']['chain']}")
    print(f"LLM Healthy Providers: {results['llm_fallback_chain']['healthy_count']}")
    print(f"Embedding Chain: {results['embedding_fallback_chain']['chain']}")
    print(f"Embedding Healthy Providers: {results['embedding_fallback_chain']['healthy_count']}")
    
    # Assert fallback chains are configured
    assert len(results['llm_fallback_chain']['chain']) > 0, "LLM fallback chain is empty"
    assert len(results['embedding_fallback_chain']['chain']) > 0, "Embedding fallback chain is empty"


@pytest.mark.asyncio
async def test_provider_specific_configurations(provider_validator):
    """Test provider-specific configuration validation"""
    results = await provider_validator.test_provider_specific_configurations()
    
    print("\nConfiguration Validation Results:")
    print(f"Total Providers: {results['total_providers']}")
    print(f"Configured Providers: {results['configured_providers']}")
    
    for provider, config in results['provider_configurations'].items():
        print(f"\n{provider.upper()}:")
        print(f"  Status: {config.get('status')}")
        print(f"  Has API Key: {config.get('has_api_key', False)}")
        print(f"  LLM Model: {config.get('configured_llm_model')}")
        
    # Assert at least one provider is configured
    assert results['configured_providers'] > 0, "No providers are configured"


@pytest.mark.asyncio
async def test_comprehensive_suite(provider_validator):
    """Test complete comprehensive suite"""
    results = await provider_validator.run_comprehensive_test_suite()
    
    print("\nComprehensive Test Suite Results:")
    print(f"Overall Status: {results['summary']['overall_status']}")
    print(f"Success Rate: {results['summary']['success_rate']*100:.1f}%")
    print(f"Execution Time: {results['summary']['execution_time_seconds']}s")
    print(f"Failed Providers: {results['summary']['failed_providers']}")
    
    # Assert reasonable success rate
    assert results['summary']['success_rate'] > 0.5, f"Success rate too low: {results['summary']['success_rate']}"