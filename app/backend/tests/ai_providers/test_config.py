"""
Test configuration for AI provider testing
"""
from typing import Dict, List, Optional

# Test timeout and retry settings
PROVIDER_TEST_CONFIG = {
    "timeout": 30,
    "max_retries": 2,
    "retry_delay": 1.0,
    "conversation_test_prompt": "你好，你是谁？",
    "expected_response_min_length": 10,
    "models_endpoint_required": True
}

# Provider-specific test expectations
PROVIDER_EXPECTATIONS = {
    "zhipu": {
        "models_endpoint": True,
        "conversation_support": True,
        "expected_models": ["glm-4", "glm-3-turbo", "embedding-3"],
        "response_format": "openai_compatible",
        "api_endpoint": "/models",
        "auth_method": "bearer_token",
        "special_requirements": "JWT token authentication"
    },
    "deepseek": {
        "models_endpoint": True,
        "conversation_support": True,
        "expected_models": ["deepseek-chat", "deepseek-coder"],
        "response_format": "openai_compatible",
        "api_endpoint": "/models",
        "auth_method": "api_key",
        "special_requirements": "OpenAI-compatible API"
    },
    "moonshot": {
        "models_endpoint": True,
        "conversation_support": True,
        "expected_models": ["moonshot-v1-8k", "moonshot-v1-32k", "moonshot-v1-128k"],
        "response_format": "openai_compatible",
        "api_endpoint": "/models",
        "auth_method": "api_key",
        "special_requirements": "OpenAI-compatible API"
    },
    "openrouter": {
        "models_endpoint": True,
        "conversation_support": True,
        "expected_models": ["anthropic/claude-3-sonnet", "openai/gpt-4"],
        "response_format": "openai_compatible", 
        "api_endpoint": "/models",
        "auth_method": "api_key",
        "special_requirements": "Proxy for multiple providers"
    },
    "qwen": {
        "models_endpoint": True,
        "conversation_support": True,
        "expected_models": ["qwen-turbo", "qwen-plus", "text-embedding-v3"],
        "response_format": "openai_compatible",
        "api_endpoint": "/models",
        "auth_method": "api_key", 
        "special_requirements": "Alibaba Cloud compatible mode"
    },
    "ollama": {
        "models_endpoint": True,
        "conversation_support": True,
        "expected_models": ["qwen2.5:14b", "bge-m3:latest"],
        "response_format": "ollama_native",
        "api_endpoint": "/api/tags",
        "auth_method": "none",
        "special_requirements": "Local HTTP API, different endpoints"
    }
}

# Error message patterns for categorization
ERROR_PATTERNS = {
    "config": [
        "api key", "missing", "not configured", "invalid configuration",
        "environment variable", "settings", "credentials"
    ],
    "network": [
        "connection refused", "network", "dns", "unreachable",
        "connect", "timeout", "network error"
    ],
    "auth": [
        "unauthorized", "forbidden", "authentication", "invalid key",
        "401", "403", "api key invalid", "token"
    ],
    "api": [
        "400", "422", "429", "500", "502", "503",
        "rate limit", "bad request", "server error", "quota"
    ],
    "response": [
        "json", "parse", "decode", "format", "schema",
        "unexpected response", "malformed"
    ]
}

# Provider-specific timeout configurations
PROVIDER_TIMEOUTS = {
    "zhipu": 35,      # Slightly higher for JWT auth
    "deepseek": 30,
    "moonshot": 30,
    "openrouter": 45,  # Higher due to proxy overhead
    "qwen": 30,
    "ollama": 20       # Local, should be faster
}

# Test scenarios for comprehensive validation
TEST_SCENARIOS = {
    "basic_conversation": {
        "prompt": "你好，你是谁？",
        "expected_min_length": 10,
        "expected_languages": ["zh", "en"],
        "timeout": 30
    },
    "english_conversation": {
        "prompt": "Hello, who are you?",
        "expected_min_length": 10,
        "expected_languages": ["en"],
        "timeout": 30
    },
    "coding_question": {
        "prompt": "Write a simple Python function to add two numbers",
        "expected_min_length": 50,
        "expected_keywords": ["def", "return", "+"],
        "timeout": 45
    }
}

# Health check thresholds
HEALTH_THRESHOLDS = {
    "response_time_ms": {
        "excellent": 1000,
        "good": 3000,
        "acceptable": 10000,
        "poor": 30000
    },
    "success_rate": {
        "excellent": 0.95,
        "good": 0.90,
        "acceptable": 0.80,
        "poor": 0.70
    }
}

# Expected model patterns by provider
MODEL_PATTERNS = {
    "zhipu": [r"glm-\d+", r"embedding-\d+", r"chatglm.*"],
    "deepseek": [r"deepseek-.*", r".*-chat", r".*-coder"],  
    "moonshot": [r"moonshot-v1-.*", r".*-8k", r".*-32k", r".*-128k"],
    "openrouter": [r".*/.*", r"anthropic/.*", r"openai/.*", r"meta-llama/.*"],
    "qwen": [r"qwen-.*", r"qwen\d+.*", r"text-embedding-.*"],
    "ollama": [r".*:.*", r".*:latest", r".*:\d+b"]
}