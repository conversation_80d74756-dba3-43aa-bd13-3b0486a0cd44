"""
Base test class for AI provider testing with common patterns and utilities
"""
import asyncio
import logging
import time
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone

import httpx

# Try to import tenacity, fall back to simple retry if not available
try:
    from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
    TENACITY_AVAILABLE = True
except ImportError:
    TENACITY_AVAILABLE = False
    # Simple fallback decorators
    def retry(*args, **kwargs):
        def decorator(func):
            return func
        return decorator
    
    def stop_after_attempt(attempts):
        pass
    
    def wait_exponential(multiplier=1, min=1, max=10):
        pass
    
    def retry_if_exception_type(exception_types):
        pass

logger = logging.getLogger(__name__)


class TestStatus(Enum):
    """Test result status"""
    SUCCESS = "success"
    FAILED = "failed"
    SKIPPED = "skipped"
    ERROR = "error"
    TIMEOUT = "timeout"
    NOT_CONFIGURED = "not_configured"


class ErrorCategory(Enum):
    """Error categorization for diagnostics"""
    CONFIG_ERROR = "configuration"
    NETWORK_ERROR = "network"
    AUTH_ERROR = "authentication"
    API_ERROR = "api"
    RESPONSE_ERROR = "response"
    TIMEOUT_ERROR = "timeout"
    UNKNOWN_ERROR = "unknown"


@dataclass
class TestResult:
    """Test result container"""
    status: TestStatus
    provider: str
    test_type: str
    response_time_ms: Optional[float] = None
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    error_category: Optional[ErrorCategory] = None
    timestamp: Optional[str] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now(timezone.utc).isoformat()


@dataclass
class ProviderTestConfig:
    """Configuration for provider testing"""
    timeout: int = 30
    max_retries: int = 2
    retry_delay: float = 1.0
    conversation_test_prompt: str = "你好，你是谁？"
    expected_response_min_length: int = 10
    models_endpoint_required: bool = True
    
    # Provider-specific expectations
    expected_models: Optional[List[str]] = None
    response_format: str = "openai_compatible"
    custom_endpoints: Optional[Dict[str, str]] = None


class BaseProviderTest(ABC):
    """
    Base class for AI provider testing with common patterns and utilities
    """
    
    def __init__(self, config: Optional[ProviderTestConfig] = None):
        self.config = config or ProviderTestConfig()
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def categorize_error(self, error: Exception) -> ErrorCategory:
        """
        Categorize error for better diagnostics
        
        Args:
            error: The exception that occurred
            
        Returns:
            ErrorCategory: The categorized error type
        """
        error_str = str(error).lower()
        error_type = type(error).__name__.lower()
        
        # Configuration errors
        if any(keyword in error_str for keyword in [
            "api key", "missing", "not configured", "invalid configuration",
            "environment variable", "settings"
        ]):
            return ErrorCategory.CONFIG_ERROR
        
        # Network errors
        if any(keyword in error_type for keyword in [
            "connect", "timeout", "network", "connection"
        ]) or any(keyword in error_str for keyword in [
            "connection refused", "network", "dns", "unreachable"
        ]):
            return ErrorCategory.NETWORK_ERROR
        
        # Authentication errors
        if any(keyword in error_str for keyword in [
            "unauthorized", "forbidden", "authentication", "invalid key",
            "401", "403", "api key"
        ]):
            return ErrorCategory.AUTH_ERROR
        
        # API errors  
        if any(keyword in error_str for keyword in [
            "400", "422", "429", "500", "502", "503",
            "rate limit", "bad request", "server error"
        ]):
            return ErrorCategory.API_ERROR
        
        # Response format errors
        if any(keyword in error_str for keyword in [
            "json", "parse", "decode", "format", "schema"
        ]):
            return ErrorCategory.RESPONSE_ERROR
        
        # Timeout errors
        if "timeout" in error_str or "timeout" in error_type:
            return ErrorCategory.TIMEOUT_ERROR
        
        return ErrorCategory.UNKNOWN_ERROR
    
    def create_diagnostic_message(self, error: Exception, provider: str, test_type: str) -> str:
        """
        Create actionable diagnostic message based on error category
        
        Args:
            error: The exception that occurred
            provider: Provider name
            test_type: Type of test that failed
            
        Returns:
            str: Diagnostic message with suggested actions
        """
        category = self.categorize_error(error)
        base_error = str(error)
        
        diagnostics = {
            ErrorCategory.CONFIG_ERROR: f"""
Configuration Error for {provider}:
- Error: {base_error}
- Check environment variables for {provider.upper()}_API_KEY
- Verify API base URL configuration
- Ensure provider is enabled in settings
            """.strip(),
            
            ErrorCategory.NETWORK_ERROR: f"""
Network Error for {provider}:
- Error: {base_error}
- Check internet connectivity
- Verify API base URL is reachable
- Check firewall/proxy settings
- For Ollama: Ensure local service is running on configured port
            """.strip(),
            
            ErrorCategory.AUTH_ERROR: f"""
Authentication Error for {provider}:
- Error: {base_error}
- Verify API key is valid and not expired
- Check API key permissions/quotas
- Ensure correct authentication format
            """.strip(),
            
            ErrorCategory.API_ERROR: f"""
API Error for {provider}:
- Error: {base_error}
- Check API service status
- Verify request format matches provider requirements
- Check rate limits and quotas
- Review provider-specific documentation
            """.strip(),
            
            ErrorCategory.RESPONSE_ERROR: f"""
Response Format Error for {provider}:
- Error: {base_error}
- Provider returned unexpected response format
- Check API version compatibility
- Verify model availability
            """.strip(),
            
            ErrorCategory.TIMEOUT_ERROR: f"""
Timeout Error for {provider}:
- Error: {base_error}
- Increase timeout configuration if needed
- Check network latency to provider
- Verify provider service health
            """.strip(),
        }
        
        return diagnostics.get(category, f"Unknown Error for {provider}: {base_error}")
    
    async def _execute_with_retry(self, test_func, *args, **kwargs) -> Any:
        """
        Execute test function with retry logic for transient failures
        
        Args:
            test_func: Function to execute
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            Any: Test function result
        """
        if TENACITY_AVAILABLE:
            # Use tenacity for sophisticated retry logic
            @retry(
                stop=stop_after_attempt(3),
                wait=wait_exponential(multiplier=1, min=1, max=10),
                retry=retry_if_exception_type((httpx.ConnectTimeout, httpx.ReadTimeout, httpx.NetworkError))
            )
            async def _retry_func():
                return await test_func(*args, **kwargs)
            
            return await _retry_func()
        else:
            # Simple retry implementation
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    return await test_func(*args, **kwargs)
                except (httpx.ConnectTimeout, httpx.ReadTimeout, httpx.NetworkError) as e:
                    if attempt == max_attempts - 1:  # Last attempt
                        raise e
                    # Simple backoff
                    await asyncio.sleep(1 * (attempt + 1))
                except Exception as e:
                    # Don't retry for other exceptions
                    raise e
    
    async def measure_execution_time(self, test_func, *args, **kwargs) -> tuple[Any, float]:
        """
        Measure execution time of test function
        
        Args:
            test_func: Function to execute and measure
            *args: Positional arguments
            **kwargs: Keyword arguments
            
        Returns:
            tuple: (result, execution_time_ms)
        """
        start_time = time.perf_counter()
        try:
            result = await asyncio.wait_for(
                test_func(*args, **kwargs),
                timeout=self.config.timeout
            )
            end_time = time.perf_counter()
            return result, (end_time - start_time) * 1000
        except asyncio.TimeoutError:
            end_time = time.perf_counter()
            raise TimeoutError(f"Test timed out after {self.config.timeout}s")
    
    def validate_response_format(self, response: Any, provider: str, test_type: str) -> bool:
        """
        Validate response format based on provider and test type
        
        Args:
            response: API response to validate
            provider: Provider name
            test_type: Type of test
            
        Returns:
            bool: True if response format is valid
        """
        if test_type == "models":
            # Validate models endpoint response
            if provider == "ollama":
                # Ollama returns different format
                return isinstance(response, (list, dict)) and len(response) > 0
            else:
                # OpenAI-compatible format
                return (
                    hasattr(response, 'data') and 
                    isinstance(response.data, list) and 
                    len(response.data) > 0
                )
        
        elif test_type == "conversation":
            # Validate conversation response
            if hasattr(response, 'choices') and len(response.choices) > 0:
                content = response.choices[0].message.content
                return (
                    content is not None and 
                    len(content) >= self.config.expected_response_min_length and
                    any(c.isalpha() for c in content)
                )
            
        return False
    
    def extract_response_data(self, response: Any, test_type: str, provider: str) -> Dict[str, Any]:
        """
        Extract relevant data from response for reporting
        
        Args:
            response: API response
            test_type: Type of test
            provider: Provider name
            
        Returns:
            Dict: Extracted response data
        """
        if test_type == "models":
            if provider == "ollama":
                # Ollama format
                if isinstance(response, dict) and 'models' in response:
                    models = response['models']
                    return {
                        "models_count": len(models),
                        "sample_models": [m.get('name', 'unknown') for m in models[:3]]
                    }
                elif isinstance(response, list):
                    return {
                        "models_count": len(response),
                        "sample_models": [str(m)[:50] for m in response[:3]]
                    }
            else:
                # OpenAI-compatible format
                if hasattr(response, 'data'):
                    return {
                        "models_count": len(response.data),
                        "sample_models": [m.id for m in response.data[:3]]
                    }
        
        elif test_type == "conversation":
            if hasattr(response, 'choices') and len(response.choices) > 0:
                content = response.choices[0].message.content
                return {
                    "response": content,
                    "response_length": len(content) if content else 0,
                    "valid_response": self.validate_response_format(response, provider, test_type)
                }
        
        return {"raw_response": str(response)[:200]}
    
    @abstractmethod
    async def test_models_endpoint(self, provider: str) -> TestResult:
        """
        Test models endpoint for specific provider
        
        Args:
            provider: Provider name
            
        Returns:
            TestResult: Test results
        """
        pass
    
    @abstractmethod  
    async def test_conversation(self, provider: str) -> TestResult:
        """
        Test basic conversation functionality
        
        Args:
            provider: Provider name
            
        Returns:
            TestResult: Test results
        """
        pass
    
    async def run_provider_tests(self, provider: str) -> Dict[str, TestResult]:
        """
        Run all tests for a specific provider
        
        Args:
            provider: Provider name
            
        Returns:
            Dict: Test results by test type
        """
        results = {}
        
        self.logger.info(f"Starting tests for provider: {provider}")
        
        # Test models endpoint
        try:
            results["models"] = await self.test_models_endpoint(provider)
        except Exception as e:
            self.logger.error(f"Models test failed for {provider}: {e}")
            results["models"] = TestResult(
                status=TestStatus.ERROR,
                provider=provider,
                test_type="models",
                error=str(e),
                error_category=self.categorize_error(e)
            )
        
        # Test conversation (only if models test succeeded)
        if results["models"].status == TestStatus.SUCCESS:
            try:
                results["conversation"] = await self.test_conversation(provider)
            except Exception as e:
                self.logger.error(f"Conversation test failed for {provider}: {e}")
                results["conversation"] = TestResult(
                    status=TestStatus.ERROR,
                    provider=provider,
                    test_type="conversation", 
                    error=str(e),
                    error_category=self.categorize_error(e)
                )
        else:
            # Skip conversation test if models failed
            results["conversation"] = TestResult(
                status=TestStatus.SKIPPED,
                provider=provider,
                test_type="conversation",
                error="Skipped due to models endpoint failure"
            )
        
        return results