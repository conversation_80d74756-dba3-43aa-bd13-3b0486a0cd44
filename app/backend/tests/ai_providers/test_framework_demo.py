"""
Demonstration test for AI Provider Testing Framework
Shows how to use the framework with mock data
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch

from tests.ai_providers.base_provider_test import TestStatus, ProviderTestConfig
from tests.ai_providers.test_comprehensive_provider_validation import ComprehensiveProviderValidator
from tests.fixtures.provider_test_fixtures import (
    MockProviderFactory, ResponseValidator, TestDataGenerator
)


@pytest.fixture
def mock_validator():
    """Create validator with test configuration"""
    config = ProviderTestConfig(
        timeout=10,  # Shorter timeout for tests
        max_retries=1,
        conversation_test_prompt="Hello, test prompt",
        expected_response_min_length=5
    )
    return ComprehensiveProviderValidator(config)


@pytest.fixture 
def response_validator():
    """Get response validator instance"""
    return ResponseValidator()


@pytest.mark.asyncio
async def test_mock_successful_models_response():
    """Test mock successful models response validation"""
    validator = ResponseValidator()
    
    # Test OpenAI-compatible provider
    deepseek_response = MockProviderFactory.create_successful_models_response("deepseek", 3)
    result = validator.validate_models_response(deepseek_response, "deepseek")
    
    assert result["valid"] is True
    assert result["format_correct"] is True
    assert result["models_count"] == 3
    assert len(result["sample_models"]) == 3
    
    # Test Ollama provider
    ollama_response = MockProviderFactory.create_successful_models_response("ollama", 2)
    result = validator.validate_models_response(ollama_response, "ollama")
    
    assert result["valid"] is True
    assert result["format_correct"] is True
    assert result["models_count"] == 2
    assert len(result["sample_models"]) == 2


@pytest.mark.asyncio
async def test_mock_successful_conversation_response():
    """Test mock successful conversation response validation"""
    validator = ResponseValidator()
    
    # Test Chinese prompt
    chinese_prompt = "你好，你是谁？"
    response = MockProviderFactory.create_successful_conversation_response("deepseek", chinese_prompt)
    result = validator.validate_conversation_response(response, "deepseek", chinese_prompt)
    
    assert result["valid"] is True
    assert result["format_correct"] is True
    assert result["has_content"] is True
    assert result["content_length"] > 10
    assert result["response_quality"] in ["good", "excellent"]
    
    # Test English prompt
    english_prompt = "Hello, who are you?"
    response = MockProviderFactory.create_successful_conversation_response("moonshot", english_prompt)
    result = validator.validate_conversation_response(response, "moonshot", english_prompt)
    
    assert result["valid"] is True
    assert result["has_content"] is True


@pytest.mark.asyncio
async def test_error_categorization():
    """Test error categorization functionality"""
    validator = ResponseValidator()
    
    # Test different error types
    test_cases = [
        ("Invalid API key provided", "auth"),
        ("Connection refused", "network"),
        ("Missing environment variable", "config"),
        ("Rate limit exceeded", "api"),
        ("Invalid JSON response", "response"),
        ("Unknown error occurred", "unknown")
    ]
    
    for error_message, expected_category in test_cases:
        category = validator.categorize_error_by_message(error_message)
        assert category == expected_category, f"Expected {expected_category}, got {category} for: {error_message}"


@pytest.mark.asyncio
async def test_test_data_generator():
    """Test test data generator functionality"""
    generator = TestDataGenerator()
    
    # Test prompt generation
    prompts = generator.generate_test_prompts()
    assert len(prompts) > 0
    assert all("name" in p and "prompt" in p for p in prompts)
    
    # Test config generation
    configs = generator.generate_provider_configs()
    assert "deepseek" in configs
    assert "ollama" in configs
    assert all("timeout" in c for c in configs.values())


@pytest.mark.asyncio 
async def test_framework_integration_with_mocks(mock_validator):
    """Test framework integration using mocks"""
    
    # Mock AI service manager
    with patch.object(mock_validator, 'ai_manager') as mock_manager:
        # Setup mock for successful provider
        mock_client = AsyncMock()
        mock_config = {"llm_model": "test-model", "temperature": 0.7}
        mock_manager.get_llm_client.return_value = (mock_client, mock_config)
        
        # Mock successful models call
        mock_models_response = MockProviderFactory.create_successful_models_response("deepseek", 5)
        mock_client.models.list.return_value = mock_models_response
        
        # Mock successful conversation call
        mock_conversation_response = MockProviderFactory.create_successful_conversation_response(
            "deepseek", "Hello, test prompt"
        )
        mock_client.chat.completions.create.return_value = mock_conversation_response
        
        # Mock ai_settings
        with patch('tests.ai_providers.test_comprehensive_provider_validation.ai_settings') as mock_settings:
            mock_settings.is_provider_available.return_value = True
            
            # Test models endpoint
            result = await mock_validator.test_models_endpoint("deepseek")
            assert result.status == TestStatus.SUCCESS
            assert result.provider == "deepseek"
            assert result.test_type == "models"
            assert result.data is not None
            assert result.data["models_count"] == 5
            
            # Test conversation endpoint
            result = await mock_validator.test_conversation("deepseek")
            assert result.status == TestStatus.SUCCESS
            assert result.provider == "deepseek"
            assert result.test_type == "conversation"
            assert result.data is not None
            assert result.data["response_length"] > 0


@pytest.mark.asyncio
async def test_framework_error_handling(mock_validator):
    """Test framework error handling"""
    
    # Test with unconfigured provider
    with patch.object(mock_validator, 'ai_manager') as mock_manager:
        with patch('tests.ai_providers.test_comprehensive_provider_validation.ai_settings') as mock_settings:
            mock_settings.is_provider_available.return_value = False
            
            result = await mock_validator.test_models_endpoint("unconfigured_provider")
            assert result.status == TestStatus.NOT_CONFIGURED
            assert "not configured" in result.error.lower()
    
    # Test with API error
    with patch.object(mock_validator, 'ai_manager') as mock_manager:
        mock_client = AsyncMock()
        mock_config = {"llm_model": "test-model"}
        mock_manager.get_llm_client.return_value = (mock_client, mock_config)
        
        # Mock API error
        mock_client.models.list.side_effect = Exception("API rate limit exceeded")
        
        with patch('tests.ai_providers.test_comprehensive_provider_validation.ai_settings') as mock_settings:
            mock_settings.is_provider_available.return_value = True
            
            result = await mock_validator.test_models_endpoint("deepseek")
            assert result.status == TestStatus.FAILED
            assert "rate limit" in result.error.lower()


@pytest.mark.asyncio
async def test_comprehensive_suite_mock(mock_validator):
    """Test comprehensive suite with mocked providers"""
    
    with patch.object(mock_validator, 'ai_manager') as mock_manager:
        with patch('tests.ai_providers.test_comprehensive_provider_validation.ai_settings') as mock_settings:
            
            # Mock one successful and one failed provider
            def mock_is_available(provider, task_type=None):
                return provider == "deepseek"  # Only deepseek is available
            
            mock_settings.is_provider_available.side_effect = mock_is_available
            mock_settings.get_llm_fallback_chain.return_value = ["deepseek", "moonshot"]
            mock_settings.get_embedding_fallback_chain.return_value = ["ollama", "deepseek"]
            
            def mock_get_client(provider):
                if provider == "deepseek":
                    mock_client = AsyncMock()
                    mock_client.models.list.return_value = MockProviderFactory.create_successful_models_response("deepseek", 3)
                    mock_client.chat.completions.create.return_value = MockProviderFactory.create_successful_conversation_response("deepseek", "test")
                    return mock_client, {"llm_model": "deepseek-chat"}
                else:
                    raise Exception(f"Provider {provider} not configured")
            
            mock_manager.get_llm_client.side_effect = mock_get_client
            
            # Run comprehensive suite with limited providers
            results = await mock_validator.run_comprehensive_test_suite(
                providers=["deepseek", "moonshot"],
                test_types=["models", "conversation"]
            )
            
            # Verify results structure
            assert "suite_info" in results
            assert "test_results" in results
            assert "summary" in results
            
            # Check that deepseek succeeded and moonshot failed/not configured
            if "models" in results["test_results"]:
                assert results["test_results"]["models"]["deepseek"].status == TestStatus.SUCCESS
                # moonshot should fail due to configuration
                assert results["test_results"]["models"]["moonshot"].status in [TestStatus.FAILED, TestStatus.NOT_CONFIGURED]
            
            # Verify summary calculations
            summary = results["summary"]
            assert "total_tests" in summary
            assert "successful_tests" in summary
            assert "success_rate" in summary
            assert summary["success_rate"] >= 0.0


def test_provider_expectations_loaded():
    """Test that provider expectations are properly loaded"""
    from tests.ai_providers.test_config import PROVIDER_EXPECTATIONS
    
    expected_providers = ["zhipu", "deepseek", "moonshot", "openrouter", "qwen", "ollama"]
    
    for provider in expected_providers:
        assert provider in PROVIDER_EXPECTATIONS
        
        config = PROVIDER_EXPECTATIONS[provider]
        assert "models_endpoint" in config
        assert "conversation_support" in config
        assert "expected_models" in config
        assert "response_format" in config


if __name__ == "__main__":
    # Run a simple demo
    import asyncio
    
    async def run_demo():
        """Run a simple demonstration"""
        print("🧪 AI Provider Testing Framework Demo")
        print("="*50)
        
        # Test response validator
        validator = ResponseValidator()
        
        # Test successful models response
        response = MockProviderFactory.create_successful_models_response("deepseek", 3)
        result = validator.validate_models_response(response, "deepseek")
        print(f"✅ Models validation: {result['valid']} ({result['models_count']} models)")
        
        # Test successful conversation response
        response = MockProviderFactory.create_successful_conversation_response("deepseek", "你好，你是谁？")
        result = validator.validate_conversation_response(response, "deepseek", "你好，你是谁？")
        print(f"✅ Conversation validation: {result['valid']} (quality: {result['response_quality']})")
        
        print("\n🎯 Framework demo completed successfully!")
    
    asyncio.run(run_demo())