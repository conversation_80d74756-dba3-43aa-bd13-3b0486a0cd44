"""
API endpoint tests for OCR-enhanced resume parsing

This module tests:
- API endpoints with enable_ocr parameter
- OCR parameter validation and propagation
- Response format with OCR metadata
- Error handling in API layer
- Backward compatibility with existing clients
"""

import base64
import json
import pytest
from httpx import AsyncClient
from unittest.mock import patch, AsyncMock

from app.core.config import settings
from app.models.user import User
from app.services.resume_parser import resume_parser
from app.services.ocr_service import ocr_service


class TestResumeParsingAPIWithOCR:
    """Test resume parsing API endpoints with OCR integration"""
    
    @pytest.mark.asyncio
    async def test_parse_resume_endpoint_ocr_enabled(self, client: AsyncClient, test_user: User, auth_headers: dict):
        """Test resume parsing endpoint with OCR enabled"""
        # Create test file content
        test_content = b"test resume content"
        encoded_content = base64.b64encode(test_content).decode()
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = {
                "candidate_id": 123,
                "action": "created",
                "parsing_result": {
                    "confidence_score": 0.87,
                    "parser_version": "v2.0",
                    "parsing_errors": [],
                    "fields_extracted": 8,
                    "ocr_enabled": True,
                    "text_extraction_method": "pdf_ocr_enhanced"
                },
                "candidate_data": {
                    "name": "John Doe",
                    "email": "<EMAIL>",
                    "skills_count": 5
                },
                "embeddings": {
                    "embeddings_generated": True,
                    "provider": "openai",
                    "dimension": 1536
                }
            }
            
            response = await client.post(
                f"{settings.API_V1_STR}/candidates/parse-resume",
                headers=auth_headers,
                json={
                    "file_content": encoded_content,
                    "filename": "test_resume.pdf",
                    "generate_embeddings": True,
                    "enable_ocr": True
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify OCR metadata is included
            assert data["parsing_result"]["ocr_enabled"] == True
            assert data["parsing_result"]["text_extraction_method"] == "pdf_ocr_enhanced"
            assert data["parsing_result"]["confidence_score"] == 0.87
            
            # Verify the service was called with OCR enabled
            mock_parse.assert_called_once()
            call_args = mock_parse.call_args
            assert call_args.kwargs["enable_ocr"] == True
    
    @pytest.mark.asyncio
    async def test_parse_resume_endpoint_ocr_disabled(self, client: AsyncClient, test_user: User, auth_headers: dict):
        """Test resume parsing endpoint with OCR disabled"""
        test_content = b"test resume content"
        encoded_content = base64.b64encode(test_content).decode()
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = {
                "candidate_id": 124,
                "action": "created",
                "parsing_result": {
                    "confidence_score": 0.92,
                    "parser_version": "v2.0",
                    "parsing_errors": [],
                    "fields_extracted": 7,
                    "ocr_enabled": False,
                    "text_extraction_method": "pdf_text_only"
                },
                "candidate_data": {
                    "name": "Jane Smith",
                    "email": "<EMAIL>",
                    "skills_count": 4
                }
            }
            
            response = await client.post(
                f"{settings.API_V1_STR}/candidates/parse-resume",
                headers=auth_headers,
                json={
                    "file_content": encoded_content,
                    "filename": "text_resume.pdf",
                    "generate_embeddings": False,
                    "enable_ocr": False
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify OCR metadata shows disabled
            assert data["parsing_result"]["ocr_enabled"] == False
            assert data["parsing_result"]["text_extraction_method"] == "pdf_text_only"
            
            # Verify the service was called with OCR disabled
            mock_parse.assert_called_once()
            call_args = mock_parse.call_args
            assert call_args.kwargs["enable_ocr"] == False
    
    @pytest.mark.asyncio
    async def test_parse_resume_endpoint_default_ocr_enabled(self, client: AsyncClient, test_user: User, auth_headers: dict):
        """Test that OCR is enabled by default when not specified"""
        test_content = b"test resume content"
        encoded_content = base64.b64encode(test_content).decode()
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = {
                "candidate_id": 125,
                "action": "created",
                "parsing_result": {
                    "confidence_score": 0.85,
                    "ocr_enabled": True,
                    "text_extraction_method": "image_ocr"
                },
                "candidate_data": {"name": "Default OCR Test"}
            }
            
            response = await client.post(
                f"{settings.API_V1_STR}/candidates/parse-resume",
                headers=auth_headers,
                json={
                    "file_content": encoded_content,
                    "filename": "resume_image.png"
                    # Note: enable_ocr not specified, should default to True
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify OCR is enabled by default
            assert data["parsing_result"]["ocr_enabled"] == True
            
            # Verify the service was called with OCR enabled by default
            mock_parse.assert_called_once()
            call_args = mock_parse.call_args
            assert call_args.kwargs["enable_ocr"] == True
    
    @pytest.mark.asyncio
    async def test_batch_parse_resume_ocr_parameter(self, client: AsyncClient, test_user: User, auth_headers: dict):
        """Test batch resume parsing with OCR parameter"""
        resume_files = [
            {
                "file_content": base64.b64encode(b"resume 1 content").decode(),
                "filename": "resume1.pdf"
            },
            {
                "file_content": base64.b64encode(b"resume 2 content").decode(),
                "filename": "resume2.png"
            }
        ]
        
        with patch.object(resume_parser, 'batch_parse_resumes') as mock_batch_parse:
            mock_batch_parse.return_value = [
                {
                    "candidate_id": 126,
                    "success": True,
                    "parsing_result": {"ocr_enabled": True}
                },
                {
                    "candidate_id": 127,
                    "success": True,
                    "parsing_result": {"ocr_enabled": True}
                }
            ]
            
            response = await client.post(
                f"{settings.API_V1_STR}/candidates/batch-parse-resumes",
                headers=auth_headers,
                json={
                    "resume_files": resume_files,
                    "generate_embeddings": True,
                    "enable_ocr": True,
                    "max_concurrent": 2
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify batch parsing was called with OCR enabled
            mock_batch_parse.assert_called_once()
            call_args = mock_batch_parse.call_args
            assert call_args.kwargs["enable_ocr"] == True
            
            # Verify response includes OCR metadata
            assert len(data["results"]) == 2
            for result in data["results"]:
                assert result["parsing_result"]["ocr_enabled"] == True


class TestOCRParameterValidation:
    """Test validation of OCR-related parameters"""
    
    @pytest.mark.asyncio
    async def test_invalid_ocr_parameter_type(self, client: AsyncClient, test_user: User, auth_headers: dict):
        """Test validation of invalid OCR parameter types"""
        test_content = base64.b64encode(b"test content").decode()
        
        response = await client.post(
            f"{settings.API_V1_STR}/candidates/parse-resume",
            headers=auth_headers,
            json={
                "file_content": test_content,
                "filename": "test.pdf",
                "enable_ocr": "invalid"  # Should be boolean
            }
        )
        
        assert response.status_code == 422  # Validation error
        data = response.json()
        assert "enable_ocr" in str(data["detail"]).lower()
    
    @pytest.mark.asyncio
    async def test_ocr_parameter_with_unsupported_format(self, client: AsyncClient, test_user: User, auth_headers: dict):
        """Test OCR parameter with unsupported file format"""
        test_content = base64.b64encode(b"test content").decode()
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.side_effect = ValueError("Unsupported file type: xyz")
            
            response = await client.post(
                f"{settings.API_V1_STR}/candidates/parse-resume",
                headers=auth_headers,
                json={
                    "file_content": test_content,
                    "filename": "test.xyz",
                    "enable_ocr": True
                }
            )
            
            assert response.status_code == 400
            data = response.json()
            assert "unsupported" in data["detail"].lower()


class TestOCRErrorHandlingInAPI:
    """Test OCR error handling at API level"""
    
    @pytest.mark.asyncio
    async def test_ocr_service_failure_handling(self, client: AsyncClient, test_user: User, auth_headers: dict):
        """Test API handling of OCR service failures"""
        test_content = base64.b64encode(b"test content").decode()
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = {
                "candidate_id": 128,
                "action": "created",
                "parsing_result": {
                    "confidence_score": 0.65,
                    "ocr_enabled": True,
                    "text_extraction_method": "pdf_ocr_fallback",
                    "parsing_errors": ["OCR processing failed, used fallback extraction"]
                },
                "candidate_data": {"name": "Fallback Test"}
            }
            
            response = await client.post(
                f"{settings.API_V1_STR}/candidates/parse-resume",
                headers=auth_headers,
                json={
                    "file_content": test_content,
                    "filename": "problematic.pdf",
                    "enable_ocr": True
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify graceful degradation
            assert data["parsing_result"]["text_extraction_method"] == "pdf_ocr_fallback"
            assert len(data["parsing_result"]["parsing_errors"]) > 0
            assert "fallback" in data["parsing_result"]["parsing_errors"][0].lower()
    
    @pytest.mark.asyncio
    async def test_ocr_timeout_handling(self, client: AsyncClient, test_user: User, auth_headers: dict):
        """Test API handling of OCR timeouts"""
        test_content = base64.b64encode(b"large file content").decode()
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            # Simulate timeout scenario
            mock_parse.return_value = {
                "candidate_id": 129,
                "action": "created",
                "parsing_result": {
                    "confidence_score": 0.70,
                    "ocr_enabled": True,
                    "text_extraction_method": "pdf_text_only",
                    "parsing_errors": ["OCR processing timeout, used traditional extraction"]
                },
                "candidate_data": {"name": "Timeout Test"}
            }
            
            response = await client.post(
                f"{settings.API_V1_STR}/candidates/parse-resume",
                headers=auth_headers,
                json={
                    "file_content": test_content,
                    "filename": "large_resume.pdf",
                    "enable_ocr": True
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify timeout was handled gracefully
            assert "timeout" in data["parsing_result"]["parsing_errors"][0].lower()
            assert data["candidate_data"]["name"] == "Timeout Test"


class TestOCRBackwardCompatibility:
    """Test backward compatibility with existing API clients"""
    
    @pytest.mark.asyncio
    async def test_existing_api_calls_work_without_ocr_param(self, client: AsyncClient, test_user: User, auth_headers: dict):
        """Test that existing API calls work without OCR parameter"""
        test_content = base64.b64encode(b"legacy test content").decode()
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = {
                "candidate_id": 130,
                "action": "created",
                "parsing_result": {
                    "confidence_score": 0.88,
                    "ocr_enabled": True,  # Should default to enabled
                    "text_extraction_method": "pdf_text_extraction"
                },
                "candidate_data": {"name": "Legacy Test"}
            }
            
            # Legacy API call format (no enable_ocr parameter)
            response = await client.post(
                f"{settings.API_V1_STR}/candidates/parse-resume",
                headers=auth_headers,
                json={
                    "file_content": test_content,
                    "filename": "legacy_resume.pdf",
                    "generate_embeddings": True
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify OCR is enabled by default for backward compatibility
            assert data["parsing_result"]["ocr_enabled"] == True
            
            # Verify service was called with OCR enabled
            mock_parse.assert_called_once()
            call_args = mock_parse.call_args
            assert call_args.kwargs["enable_ocr"] == True
    
    @pytest.mark.asyncio
    async def test_legacy_response_format_compatibility(self, client: AsyncClient, test_user: User, auth_headers: dict):
        """Test that response format is compatible with legacy expectations"""
        test_content = base64.b64encode(b"test content").decode()
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = {
                "candidate_id": 131,
                "action": "created",
                "parsing_result": {
                    "confidence_score": 0.82,
                    "parser_version": "v2.0",
                    "parsing_errors": [],
                    "fields_extracted": 6,
                    # New OCR fields
                    "ocr_enabled": True,
                    "text_extraction_method": "image_ocr"
                },
                "candidate_data": {
                    "name": "Compatibility Test",
                    "email": "<EMAIL>"
                },
                "embeddings": {
                    "embeddings_generated": True
                }
            }
            
            response = await client.post(
                f"{settings.API_V1_STR}/candidates/parse-resume",
                headers=auth_headers,
                json={
                    "file_content": test_content,
                    "filename": "compat_test.png"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify legacy fields are still present
            assert "candidate_id" in data
            assert "action" in data
            assert "parsing_result" in data
            assert "candidate_data" in data
            
            # Verify legacy parsing_result fields
            assert "confidence_score" in data["parsing_result"]
            assert "parser_version" in data["parsing_result"]
            assert "parsing_errors" in data["parsing_result"]
            
            # Verify new OCR fields are added
            assert "ocr_enabled" in data["parsing_result"]
            assert "text_extraction_method" in data["parsing_result"]


class TestOCRHealthEndpoint:
    """Test OCR-specific health check endpoint"""
    
    @pytest.mark.asyncio
    async def test_ocr_health_check_endpoint(self, client: AsyncClient, auth_headers: dict):
        """Test OCR health check endpoint"""
        with patch.object(ocr_service, 'test_connection') as mock_test, \
             patch.object(ocr_service, 'is_loaded') as mock_loaded, \
             patch.object(ocr_service, '_detect_gpu_availability') as mock_gpu:
            
            mock_test.return_value = True
            mock_loaded.return_value = True
            mock_gpu.return_value = True
            
            response = await client.get(
                f"{settings.API_V1_STR}/health/ocr",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["status"] == "healthy"
            assert data["gpu_available"] == True
            assert data["model_loaded"] == True
    
    @pytest.mark.asyncio
    async def test_ocr_health_check_degraded(self, client: AsyncClient, auth_headers: dict):
        """Test OCR health check when service is degraded"""
        with patch.object(ocr_service, 'test_connection') as mock_test, \
             patch.object(ocr_service, 'is_loaded') as mock_loaded, \
             patch.object(ocr_service, '_detect_gpu_availability') as mock_gpu:
            
            mock_test.return_value = False  # OCR test failed
            mock_loaded.return_value = True
            mock_gpu.return_value = False  # No GPU available
            
            response = await client.get(
                f"{settings.API_V1_STR}/health/ocr",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["status"] == "degraded"
            assert data["gpu_available"] == False
    
    @pytest.mark.asyncio
    async def test_ocr_health_check_unhealthy(self, client: AsyncClient, auth_headers: dict):
        """Test OCR health check when service is unhealthy"""
        with patch.object(ocr_service, 'test_connection', side_effect=Exception("OCR service down")):
            
            response = await client.get(
                f"{settings.API_V1_STR}/health/ocr",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["status"] == "unhealthy"
            assert "OCR service down" in data["error"]


class TestOCRMetricsEndpoint:
    """Test OCR metrics reporting endpoint"""
    
    @pytest.mark.asyncio
    async def test_ocr_metrics_endpoint(self, client: AsyncClient, auth_headers: dict):
        """Test OCR metrics endpoint"""
        mock_metrics = {
            "total_requests": 150,
            "successful_extractions": 142,
            "gpu_usage": 135,
            "cpu_fallbacks": 7,
            "failures": 8,
            "avg_processing_time": 1.8,
            "avg_confidence": 0.87,
            "enabled": True,
            "gpu_available": True,
            "gpu_enabled": True,
            "model_loaded": True,
            "success_rate": 94.67
        }
        
        with patch.object(ocr_service, 'get_metrics', return_value=mock_metrics):
            
            response = await client.get(
                f"{settings.API_V1_STR}/monitoring/ocr-metrics",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            
            assert data["total_requests"] == 150
            assert data["successful_extractions"] == 142
            assert data["success_rate"] == 94.67
            assert data["avg_processing_time"] == 1.8
            assert data["gpu_usage"] == 135
            assert data["cpu_fallbacks"] == 7


# Test fixtures for API testing
@pytest.fixture
async def auth_headers(client: AsyncClient, test_user: User):
    """Create authentication headers for API tests"""
    # Login to get access token
    response = await client.post(
        f"{settings.API_V1_STR}/auth/login",
        data={
            "username": test_user.email,
            "password": "Test123!@#"
        }
    )
    
    assert response.status_code == 200
    token_data = response.json()
    access_token = token_data["access_token"]
    
    return {"Authorization": f"Bearer {access_token}"}


@pytest.fixture
def sample_resume_content():
    """Sample resume content for API testing"""
    return {
        "pdf": base64.b64encode(b"Sample PDF resume content").decode(),
        "png": base64.b64encode(b"Sample PNG image content").decode(),
        "docx": base64.b64encode(b"Sample DOCX content").decode(),
        "txt": base64.b64encode(b"Sample text resume content").decode()
    }


# Test markers
pytestmark = [
    pytest.mark.asyncio,
    pytest.mark.integration,
    pytest.mark.api,
    pytest.mark.ocr
]