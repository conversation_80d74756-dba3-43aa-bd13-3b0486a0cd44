"""
Authentication System Verification Tests

Focused integration tests that validate the existing authentication flow
works correctly after database migration fix. Tests only what's implemented
without expanding requirements.
"""
import pytest
import time
from datetime import datetime, timezone, timedelta
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core import security
from app.core.exceptions import unauthorized
from app.crud import user as user_crud
from app.models.user import User
from app.core.enums import UserRole


class TestAuthenticationFlow:
    """Test core authentication flow functionality"""

    @pytest.mark.asyncio
    async def test_login_with_valid_credentials(self, client: AsyncClient, test_user: User, test_password: str):
        """Test successful login with valid credentials"""
        response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": test_password
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        
        # Verify token can be decoded
        payload = security.decode_token(data["access_token"])
        assert payload.get("type") == "access"
        assert int(payload.get("sub")) == test_user.id

    @pytest.mark.asyncio
    async def test_login_with_invalid_credentials(self, client: AsyncClient, test_user: User):
        """Test login failure with invalid password"""
        response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": "wrong_password"
            }
        )
        
        assert response.status_code == 401
        data = response.json()
        assert data.get("error_code") == "AUTH_LOGIN_INVALID_CREDENTIALS"

    @pytest.mark.asyncio
    async def test_login_with_inactive_user(self, client: AsyncClient, db_session: AsyncSession, test_password: str):
        """Test login failure with inactive user"""
        # Create inactive user
        inactive_user = User(
            email="<EMAIL>",
            username="inactive",
            hashed_password=security.get_password_hash(test_password),
            full_name="Inactive User",
            role=UserRole.USER,
            is_active=False
        )
        db_session.add(inactive_user)
        await db_session.commit()
        await db_session.refresh(inactive_user)
        
        response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": inactive_user.email,
                "password": test_password
            }
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data.get("error_code") == "AUTH_LOGIN_INACTIVE_USER"

    @pytest.mark.asyncio
    async def test_get_current_user_with_valid_token(self, client: AsyncClient, auth_headers: dict):
        """Test retrieving current user with valid token"""
        response = await client.get(
            f"{settings.API_V1_STR}/auth/me",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "email" in data
        assert "username" in data
        assert "role" in data
        assert data["is_active"] is True

    @pytest.mark.asyncio
    async def test_get_current_user_with_invalid_token(self, client: AsyncClient):
        """Test getting current user with invalid token"""
        response = await client.get(
            f"{settings.API_V1_STR}/auth/me",
            headers={"Authorization": "Bearer invalid_token"}
        )
        
        assert response.status_code == 401
        data = response.json()
        assert data.get("error_code") == "AUTH_TOKEN_VALIDATION_FAILED"

    @pytest.mark.asyncio
    async def test_development_bypass_token(self, client: AsyncClient, db_session: AsyncSession):
        """Test development bypass token functionality (if configured)"""
        # Only test if development environment and bypass token configured
        if settings.ENVIRONMENT not in ("development", "dev") or not settings.DEV_BYPASS_TOKEN:
            pytest.skip("Development bypass token not configured")
        
        # Ensure admin user exists
        admin_user = await user_crud.get_by_email(db_session, email="<EMAIL>")
        if not admin_user:
            admin_user = User(
                email="<EMAIL>",
                username="admin",
                hashed_password=security.get_password_hash("test123"),
                full_name="Admin User",
                role=UserRole.SUPER_ADMIN,
                is_active=True,
                is_superuser=True
            )
            db_session.add(admin_user)
            await db_session.commit()
        
        response = await client.get(
            f"{settings.API_V1_STR}/auth/me",
            headers={"Authorization": f"Bearer {settings.DEV_BYPASS_TOKEN}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == "<EMAIL>"


class TestTokenRefresh:
    """Test JWT token refresh functionality"""

    @pytest.mark.asyncio
    async def test_refresh_token_with_authorization_header(self, client: AsyncClient, test_user: User, test_password: str):
        """Test token refresh using Authorization header (preferred method)"""
        # First login to get tokens
        login_response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": test_password
            }
        )
        assert login_response.status_code == 200
        tokens = login_response.json()
        
        # Use refresh token in Authorization header
        response = await client.post(
            f"{settings.API_V1_STR}/auth/refresh",
            headers={"Authorization": f"Bearer {tokens['refresh_token']}"}
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "refresh_token" in data
        assert data["token_type"] == "bearer"
        assert data["method_used"] == "header"
        assert data.get("deprecation_warning") is None

    @pytest.mark.asyncio
    async def test_refresh_token_with_invalid_token(self, client: AsyncClient):
        """Test refresh token with invalid token"""
        response = await client.post(
            f"{settings.API_V1_STR}/auth/refresh",
            headers={"Authorization": "Bearer invalid_refresh_token"}
        )
        
        assert response.status_code == 401
        data = response.json()
        assert data.get("error_code") == "AUTH_REFRESH_TOKEN_INVALID"

    @pytest.mark.asyncio
    async def test_refresh_token_with_access_token_instead_of_refresh(self, client: AsyncClient, test_user: User, test_password: str):
        """Test refresh endpoint rejects access token instead of refresh token"""
        # Login to get tokens
        login_response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": test_password
            }
        )
        tokens = login_response.json()
        
        # Try to use access token for refresh
        response = await client.post(
            f"{settings.API_V1_STR}/auth/refresh",
            headers={"Authorization": f"Bearer {tokens['access_token']}"}
        )
        
        assert response.status_code == 401
        data = response.json()
        assert data.get("error_code") == "AUTH_TOKEN_INVALID_TYPE"


class TestAuthenticationEdgeCases:
    """Test authentication edge cases and error conditions"""

    @pytest.mark.asyncio
    async def test_login_with_missing_credentials(self, client: AsyncClient):
        """Test login with missing username or password"""
        # Missing username
        response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={"password": "somepassword"}
        )
        assert response.status_code == 422  # Validation error

        # Missing password
        response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={"username": "<EMAIL>"}
        )
        assert response.status_code == 422  # Validation error

    @pytest.mark.asyncio
    async def test_auth_with_nonexistent_user(self, client: AsyncClient):
        """Test authentication with non-existent user"""
        response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": "<EMAIL>",
                "password": "password123"
            }
        )
        
        assert response.status_code == 401
        data = response.json()
        assert data.get("error_code") == "AUTH_LOGIN_INVALID_CREDENTIALS"

    @pytest.mark.asyncio
    async def test_auth_without_authorization_header(self, client: AsyncClient):
        """Test accessing protected endpoint without Authorization header"""
        response = await client.get(f"{settings.API_V1_STR}/auth/me")
        
        assert response.status_code == 401
        # Should get OAuth2 scheme error since no token provided

    @pytest.mark.asyncio
    async def test_auth_with_malformed_authorization_header(self, client: AsyncClient):
        """Test authentication with malformed Authorization header"""
        # Missing "Bearer" prefix
        response = await client.get(
            f"{settings.API_V1_STR}/auth/me",
            headers={"Authorization": "some_token"}
        )
        assert response.status_code == 401

        # Wrong prefix
        response = await client.get(
            f"{settings.API_V1_STR}/auth/me",
            headers={"Authorization": "Basic some_token"}
        )
        assert response.status_code == 401


class TestTokenSecurity:
    """Test JWT token security aspects"""

    @pytest.mark.asyncio
    async def test_token_contains_expected_claims(self, test_user: User):
        """Test that generated tokens contain expected claims"""
        # Create access token
        access_token = security.create_access_token(test_user.id)
        payload = security.decode_token(access_token)
        
        assert payload.get("type") == "access"
        assert int(payload.get("sub")) == test_user.id
        assert "exp" in payload
        assert "iat" in payload

        # Create refresh token
        refresh_token = security.create_refresh_token(test_user.id)
        refresh_payload = security.decode_token(refresh_token)
        
        assert refresh_payload.get("type") == "refresh"
        assert int(refresh_payload.get("sub")) == test_user.id

    @pytest.mark.asyncio
    async def test_token_expiration_format(self, test_user: User):
        """Test that token expiration is properly set"""
        token = security.create_access_token(test_user.id)
        payload = security.decode_token(token)
        
        # Check expiration is set and is in the future
        exp_timestamp = payload.get("exp")
        assert exp_timestamp is not None
        assert exp_timestamp > datetime.now(timezone.utc).timestamp()

    @pytest.mark.asyncio
    async def test_different_users_get_different_tokens(self, db_session: AsyncSession):
        """Test that different users get different tokens"""
        # Create two different users
        user1 = User(
            email="<EMAIL>",
            username="user1",
            hashed_password=security.get_password_hash("password"),
            full_name="User 1",
            role=UserRole.USER,
            is_active=True
        )
        user2 = User(
            email="<EMAIL>",
            username="user2",
            hashed_password=security.get_password_hash("password"),
            full_name="User 2",
            role=UserRole.USER,
            is_active=True
        )
        
        db_session.add_all([user1, user2])
        await db_session.commit()
        await db_session.refresh(user1)
        await db_session.refresh(user2)
        
        token1 = security.create_access_token(user1.id)
        token2 = security.create_access_token(user2.id)
        
        assert token1 != token2
        
        payload1 = security.decode_token(token1)
        payload2 = security.decode_token(token2)
        
        assert int(payload1.get("sub")) != int(payload2.get("sub"))


class TestAuthenticationRegressionPrevention:
    """Regression tests for specific issues that were fixed"""

    @pytest.mark.asyncio
    async def test_enum_conflicts_resolved(self, db_session: AsyncSession):
        """Test that enum conflicts don't break authentication (regression test)"""
        # This tests the specific issue that was fixed with database migration
        # Should be able to create users with enum roles without conflicts
        user = User(
            email="<EMAIL>",
            username="enumtest",
            hashed_password=security.get_password_hash("password"),
            full_name="Enum Test User",
            role=UserRole.HR_SPECIALIST,  # This should work without enum conflicts
            is_active=True
        )
        
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        
        # Verify the user was created successfully with the correct role
        assert user.role == UserRole.HR_SPECIALIST
        assert user.id is not None

    @pytest.mark.asyncio
    async def test_user_crud_operations_work(self, db_session: AsyncSession):
        """Test that user CRUD operations work correctly after migration fix"""
        # Test creating user
        user_data = {
            "email": "<EMAIL>",
            "username": "crudtest",
            "password": "testpassword",
            "full_name": "CRUD Test User",
            "role": UserRole.USER
        }
        
        user = await user_crud.create(db_session, obj_in=user_data)
        assert user.email == user_data["email"]
        assert user.role == UserRole.USER
        
        # Test authenticating user
        auth_user = await user_crud.authenticate(
            db_session, 
            username=user_data["email"], 
            password=user_data["password"]
        )
        assert auth_user is not None
        assert auth_user.id == user.id

    @pytest.mark.asyncio
    async def test_database_migration_state_stable(self, db_session: AsyncSession):
        """Test that database is in expected state after migration"""
        # Simple test to verify critical tables exist and are accessible
        # This ensures the migration fix was successful
        
        # Test that we can query the users table
        users = await user_crud.search(db_session, limit=1)
        assert isinstance(users, list)  # Should return list, even if empty
        
        # Test that we can create a user (tests enum types work)
        user = User(
            email="<EMAIL>",
            username="migrationtest",
            hashed_password=security.get_password_hash("password"),
            full_name="Migration Test",
            role=UserRole.ADMIN,
            is_active=True
        )
        
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        
        assert user.id is not None
        assert user.role == UserRole.ADMIN


class TestAuthenticationPerformance:
    """Basic performance tests for authentication operations"""

    @pytest.mark.asyncio
    async def test_login_performance(self, client: AsyncClient, test_user: User, test_password: str):
        """Test that login completes within reasonable time"""
        start_time = time.perf_counter()
        
        response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": test_password
            }
        )
        
        end_time = time.perf_counter()
        login_time = end_time - start_time
        
        assert response.status_code == 200
        assert login_time < 2.0  # Should complete within 2 seconds

    @pytest.mark.asyncio
    async def test_token_verification_performance(self, client: AsyncClient, auth_headers: dict):
        """Test that token verification is fast"""
        start_time = time.perf_counter()
        
        response = await client.get(
            f"{settings.API_V1_STR}/auth/me",
            headers=auth_headers
        )
        
        end_time = time.perf_counter()
        verification_time = end_time - start_time
        
        assert response.status_code == 200
        assert verification_time < 1.0  # Should complete within 1 second

    @pytest.mark.asyncio
    async def test_multiple_concurrent_authentications(self, client: AsyncClient, test_user: User, test_password: str):
        """Test that multiple concurrent authentications work properly"""
        import asyncio
        
        async def login_attempt():
            response = await client.post(
                f"{settings.API_V1_STR}/auth/login",
                data={
                    "username": test_user.email,
                    "password": test_password
                }
            )
            return response.status_code == 200
        
        # Run 3 concurrent login attempts
        results = await asyncio.gather(
            login_attempt(),
            login_attempt(),
            login_attempt(),
            return_exceptions=True
        )
        
        # All should succeed
        for result in results:
            if isinstance(result, Exception):
                pytest.fail(f"Concurrent login failed: {result}")
            assert result is True