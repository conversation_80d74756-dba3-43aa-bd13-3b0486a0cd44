# AI Service Manager Comprehensive Test Suite

## Overview

This test suite provides comprehensive coverage for the enhanced AI Service Manager, testing all core functionality, error handling, caching, health monitoring, and integration scenarios.

## Test Structure

### Test Files

1. **`test_ai_service_manager.py`** - Core functionality and unit tests
2. **`test_ai_service_manager_caching.py`** - Caching system tests  
3. **`test_ai_service_manager_errors.py`** - Error handling and fallback tests
4. **`test_ai_service_manager_health.py`** - Health monitoring tests
5. **`test_ai_service_manager_integration.py`** - Integration and end-to-end tests

### Test Categories

#### 🔧 Core Functionality Tests
- **Singleton Pattern**: Ensures proper singleton implementation
- **Provider Management**: Provider properties, thread-safe switching
- **Service Methods**: Text generation, embedding generation, document reranking
- **Configuration Integration**: AI settings integration and validation

#### 🗄️ Caching System Tests
- **Cache Key Generation**: Deterministic, parameter-sensitive keys
- **Redis Operations**: Set/get operations with error handling
- **TTL Management**: Different TTL values for different operations
- **Service Integration**: Caching behavior in service methods
- **Error Resilience**: System continues when caching fails

#### 🛡️ Error Handling Tests
- **Custom Exceptions**: AIServiceError, ProviderError, AllProvidersFailedError
- **Fallback Chains**: Multi-provider fallback logic
- **Retry Mechanisms**: Exponential backoff for transient failures
- **Provider-Specific Errors**: API key issues, rate limits, timeouts
- **Error Recovery**: Complex recovery scenarios and logging

#### 💚 Health Monitoring Tests
- **Single Provider Checks**: Individual provider health validation
- **Comprehensive Checks**: All-provider health assessment
- **Service Method Health**: Health checks for core service methods
- **Performance Measurement**: Response time tracking
- **Status Reporting**: Consistent health report formatting

#### 🔄 Integration Tests
- **End-to-End Workflows**: Complete service workflows with mocking
- **Redis Integration**: Real cache operations and performance
- **Concurrent Operations**: Thread safety under load
- **Stress Testing**: High concurrency and sustained load scenarios
- **Performance Monitoring**: Cache benefits and operation metrics

## Test Coverage Areas

### Core Service Methods
- ✅ **generate_text()**: Text generation with all parameters
- ✅ **generate_embedding()**: Vector generation with caching
- ✅ **rerank_documents()**: Document ranking with fallback

### Provider Management
- ✅ **Provider Properties**: Thread-safe getter/setter operations
- ✅ **Runtime Switching**: Dynamic provider changes
- ✅ **Fallback Chains**: Multi-provider fallback logic
- ✅ **Health Monitoring**: Individual and comprehensive health checks

### Error Handling & Resilience
- ✅ **Retry Logic**: Exponential backoff with max retries
- ✅ **Fallback Mechanisms**: Provider chains and error recovery
- ✅ **Custom Exceptions**: Proper exception hierarchy and handling
- ✅ **Error Logging**: Comprehensive operation metrics

### Caching Implementation
- ✅ **Redis Integration**: Cache operations with TTL management
- ✅ **Key Generation**: Deterministic, collision-resistant keys
- ✅ **Error Resilience**: Service continues when cache fails
- ✅ **Performance Benefits**: Validation of caching effectiveness

### Health Monitoring
- ✅ **Provider Health**: Individual provider status checks
- ✅ **Service Health**: Method-level health validation
- ✅ **Performance Tracking**: Response time measurement
- ✅ **Status Reporting**: Structured health information

### Thread Safety & Concurrency
- ✅ **Concurrent Requests**: Multiple simultaneous operations
- ✅ **Provider Switching**: Thread-safe property changes
- ✅ **Resource Management**: Proper resource cleanup
- ✅ **Performance Under Load**: Stress testing scenarios

## Running the Tests

### Quick Start
```bash
# Run all tests
python tests/run_ai_service_manager_tests.py

# Run specific categories
python tests/run_ai_service_manager_tests.py specific --unit
python tests/run_ai_service_manager_tests.py specific --integration
python tests/run_ai_service_manager_tests.py specific --caching

# Run performance benchmarks
python tests/run_ai_service_manager_tests.py performance

# Skip slow tests
python tests/run_ai_service_manager_tests.py specific --fast

# Skip Redis-dependent tests
python tests/run_ai_service_manager_tests.py specific --no-redis
```

### Using pytest directly
```bash
# All AI service manager tests
pytest tests/unit/test_ai_service_manager*.py tests/integration/test_ai_service_manager*.py -v

# Specific test file
pytest tests/unit/test_ai_service_manager_caching.py -v

# With coverage
pytest tests/unit/test_ai_service_manager.py --cov=app.services.ai_service_manager --cov-report=html

# Skip slow tests
pytest -m "not slow" tests/unit/test_ai_service_manager*.py

# Skip Redis tests  
pytest -m "not redis_required" tests/unit/test_ai_service_manager*.py
```

## Test Configuration

### Markers
- `@pytest.mark.integration` - Integration tests
- `@pytest.mark.slow` - Stress/performance tests
- `@pytest.mark.redis_required` - Tests requiring Redis

### Dependencies
- **pytest** - Test framework
- **pytest-asyncio** - Async test support
- **pytest-cov** - Coverage reporting
- **redis** - Redis client for caching tests
- **httpx** - HTTP client mocking

### Environment Setup
```python
# Required environment variables for full testing
REDIS_URL=redis://localhost:6379/0
AI_SETTINGS_MODULE=app.core.ai_config
```

## Test Scenarios

### 1. Basic Functionality Tests
- Service initialization and singleton behavior
- Provider management and configuration
- Core service method operations
- Parameter validation and handling

### 2. Caching Functionality Tests  
- Cache key generation and consistency
- Redis operations (set/get/expire)
- Cache hit/miss behavior
- TTL management for different operations
- Error resilience when cache fails

### 3. Error Handling Tests
- Provider fallback chain execution
- Retry mechanisms with exponential backoff  
- Custom exception handling
- Provider-specific error scenarios
- Complex error recovery workflows

### 4. Health Monitoring Tests
- Individual provider health checks
- Comprehensive system health assessment
- Service method health validation
- Performance measurement and reporting
- Health status caching and formatting

### 5. Integration Tests
- End-to-end service workflows
- Real Redis caching integration
- Concurrent operation handling
- Performance validation
- Stress testing scenarios

### 6. Performance Tests
- Cache performance benefits
- Concurrent operation efficiency
- Memory usage under load
- Response time measurement
- Sustained load testing

## Success Criteria

### ✅ Functional Requirements
- All core service methods work correctly
- Provider fallback chains execute properly  
- Caching provides performance benefits
- Health monitoring reports accurate status
- Error handling is robust and resilient

### ✅ Performance Requirements
- Service methods complete within reasonable time
- Caching improves performance by >50%
- Concurrent operations scale properly
- Memory usage remains stable under load
- Health checks complete within timeout limits

### ✅ Quality Requirements
- >95% test coverage for critical functionality
- All error scenarios properly handled
- Thread-safe operations under concurrency
- Comprehensive logging and monitoring
- Consistent API behavior across providers

## Troubleshooting

### Common Issues

1. **Redis Connection Errors**
   ```bash
   # Start Redis locally
   redis-server
   
   # Or skip Redis tests
   pytest -m "not redis_required"
   ```

2. **Async Test Issues**
   ```bash
   # Ensure pytest-asyncio is installed
   pip install pytest-asyncio
   
   # Use auto async mode
   pytest --asyncio-mode=auto
   ```

3. **Mock Configuration Issues**
   - Verify all AI provider clients are properly mocked
   - Check that Redis client mocking is consistent
   - Ensure environment variables are set correctly

4. **Performance Test Failures**
   - Performance tests may be sensitive to system load
   - Consider running on dedicated test environment
   - Adjust timing expectations for slower systems

### Debug Mode
```bash
# Run with debug output
pytest tests/unit/test_ai_service_manager.py -v -s --tb=long

# Run single test with debug
pytest tests/unit/test_ai_service_manager.py::TestCoreServiceMethods::test_generate_text_success -v -s
```

## Coverage Goals

- **Overall Coverage**: >90%
- **Core Service Methods**: 100%
- **Error Handling**: >95%
- **Caching Logic**: >95%
- **Health Monitoring**: >90%
- **Provider Management**: >95%

## Contributing

When adding new functionality to the AI Service Manager:

1. **Add corresponding tests** in appropriate test files
2. **Update test coverage** to maintain >90% coverage
3. **Add integration tests** for end-to-end scenarios
4. **Update this documentation** with new test scenarios
5. **Run full test suite** to ensure no regressions

## Test Performance Benchmarks

### Expected Performance
- **Unit Tests**: Complete in <30 seconds
- **Integration Tests**: Complete in <2 minutes  
- **Full Suite**: Complete in <3 minutes
- **Coverage Report**: Generated in <10 seconds

### Performance Monitoring
The test suite includes performance monitoring to ensure:
- Service methods meet response time requirements
- Caching provides measurable performance benefits
- Concurrent operations scale properly
- Memory usage remains stable under load