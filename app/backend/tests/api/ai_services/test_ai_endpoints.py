"""
API endpoint tests for AI services
"""
import pytest
import pytest_asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
import json

from app.main import app
from tests.fixtures.ai_mocks import (
    MockAIServiceManager,
    create_mock_ai_service_manager,
    MOCK_RESUME_EXTRACTION,
    MOCK_QUESTIONNAIRE,
    MOCK_EVALUATION
)


class TestAIQuestionnaireEndpoint:
    """Test /api/v1/ai-questionnaire endpoints"""
    
    @pytest.fixture
    async def client(self):
        """Create test client"""
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers"""
        return {"Authorization": "Bearer test_token"}
    
    @pytest.mark.asyncio
    @patch('app.api.v1.endpoints.ai_questionnaire.ai_service_manager')
    @patch('app.api.deps.get_current_user')
    async def test_generate_questionnaire_endpoint(
        self, 
        mock_get_user,
        mock_ai_manager,
        client
    ):
        """Test POST /api/v1/ai-questionnaire/generate"""
        # Mock authentication
        mock_get_user.return_value = {"id": 1, "email": "<EMAIL>"}
        
        # Mock AI service
        mock_ai_manager.generate_questionnaire = AsyncMock(
            return_value=MOCK_QUESTIONNAIRE
        )
        
        # Test request
        request_data = {
            "position_type": "Software Engineer",
            "dimensions": ["Technical Skills", "Problem Solving"],
            "question_count": 10,
            "industry": "Technology"
        }
        
        response = await client.post(
            "/api/v1/ai-questionnaire/generate",
            json=request_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Assertions would depend on actual endpoint implementation
        # For now, we test the mock structure
        assert mock_ai_manager.generate_questionnaire.called
    
    @pytest.mark.asyncio
    @patch('app.api.v1.endpoints.ai_questionnaire.ai_service_manager')
    @patch('app.api.deps.get_current_user')
    async def test_evaluate_responses_endpoint(
        self,
        mock_get_user,
        mock_ai_manager,
        client
    ):
        """Test POST /api/v1/ai-questionnaire/evaluate"""
        # Mock authentication
        mock_get_user.return_value = {"id": 1, "email": "<EMAIL>"}
        
        # Mock AI service
        mock_ai_manager.evaluate_responses = AsyncMock(
            return_value=MOCK_EVALUATION
        )
        
        # Test request
        request_data = {
            "questionnaire_id": 1,
            "responses": {
                "q1": "My answer to question 1",
                "q2": "Python"
            }
        }
        
        response = await client.post(
            "/api/v1/ai-questionnaire/evaluate",
            json=request_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Verify AI service was called
        assert mock_ai_manager.evaluate_responses.called


class TestEmbeddingEndpoint:
    """Test /api/v1/embedding endpoints"""
    
    @pytest.fixture
    async def client(self):
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.mark.asyncio
    @patch('app.services.vector_service.ai_service_manager')
    @patch('app.api.deps.get_current_user')
    async def test_generate_embedding_endpoint(
        self,
        mock_get_user,
        mock_ai_manager,
        client
    ):
        """Test POST /api/v1/embedding/generate"""
        # Mock authentication
        mock_get_user.return_value = {"id": 1, "email": "<EMAIL>"}
        
        # Mock embedding generation
        mock_embedding = [0.1] * 1024
        mock_ai_manager.create_embedding = AsyncMock(
            return_value=mock_embedding
        )
        
        # Test request
        request_data = {
            "text": "Generate embedding for this text",
            "model": "bge-m3"
        }
        
        response = await client.post(
            "/api/v1/embedding/generate",
            json=request_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Verify embedding service was called
        assert mock_ai_manager.create_embedding.called
    
    @pytest.mark.asyncio
    @patch('app.services.vector_service.ai_service_manager')
    async def test_embedding_fallback_on_failure(self, mock_ai_manager):
        """Test embedding endpoint fallback mechanism"""
        mock_ai = create_mock_ai_service_manager()
        
        # Make primary fail
        mock_ai.set_provider_fail_rate("deepseek", 1.0)
        
        # Generate embedding should fall back
        embedding = await mock_ai.create_embedding(
            "Test text",
            use_fallback=True
        )
        
        assert embedding is not None
        assert len(embedding) == 1024
        assert len(mock_ai.call_history) == 2


class TestAssessmentEndpoint:
    """Test /api/v1/assessment endpoints"""
    
    @pytest.fixture
    async def client(self):
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.mark.asyncio
    @patch('app.services.assessment_service_enhanced.ai_service_manager')
    @patch('app.api.deps.get_current_user')
    async def test_create_assessment_endpoint(
        self,
        mock_get_user,
        mock_ai_manager,
        client
    ):
        """Test POST /api/v1/assessment/create"""
        # Mock authentication
        mock_get_user.return_value = {"id": 1, "email": "<EMAIL>"}
        
        # Mock assessment generation
        mock_ai_manager.create_completion = AsyncMock(
            return_value={
                "assessment_id": 1,
                "questions": ["Q1", "Q2"],
                "scoring_rubric": {}
            }
        )
        
        # Test request
        request_data = {
            "candidate_id": 1,
            "position_id": 1,
            "assessment_type": "technical"
        }
        
        response = await client.post(
            "/api/v1/assessment/create",
            json=request_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Verify assessment service was called
        assert mock_ai_manager.create_completion.called


class TestMatchingEndpoint:
    """Test /api/v1/matching endpoints"""
    
    @pytest.fixture
    async def client(self):
        async with AsyncClient(app=app, base_url="http://test") as client:
            yield client
    
    @pytest.mark.asyncio
    @patch('app.services.matching_service.ai_service_manager')
    @patch('app.api.deps.get_current_user')
    async def test_match_candidates_endpoint(
        self,
        mock_get_user,
        mock_ai_manager,
        client
    ):
        """Test POST /api/v1/matching/candidates"""
        # Mock authentication
        mock_get_user.return_value = {"id": 1, "email": "<EMAIL>"}
        
        # Mock matching service
        mock_ai_manager.create_embedding = AsyncMock(
            return_value=[0.1] * 1024
        )
        
        # Test request
        request_data = {
            "position_id": 1,
            "requirements": "Python, FastAPI, PostgreSQL",
            "limit": 10
        }
        
        response = await client.post(
            "/api/v1/matching/candidates",
            json=request_data,
            headers={"Authorization": "Bearer test_token"}
        )
        
        # Verify matching service was called
        assert mock_ai_manager.create_embedding.called


class TestEndpointFallbackBehavior:
    """Test fallback behavior across all AI endpoints"""
    
    @pytest.mark.asyncio
    async def test_all_endpoints_support_fallback(self):
        """Test that all AI endpoints properly handle provider fallback"""
        mock_ai = create_mock_ai_service_manager()
        
        # Make primary provider fail 50% of the time
        mock_ai.set_provider_fail_rate("deepseek", 0.5)
        
        # Test multiple endpoints
        endpoints_to_test = [
            ("generate_questionnaire", {"position_type": "Engineer"}),
            ("evaluate_responses", {"questionnaire_title": "Test"}),
            ("create_embedding", {"text": "Test"}),
            ("create_completion", {"messages": [{"role": "user", "content": "Test"}]})
        ]
        
        for method_name, kwargs in endpoints_to_test:
            mock_ai.reset_all()
            mock_ai.set_provider_fail_rate("deepseek", 0.5)
            
            # Get method
            if hasattr(mock_ai, method_name):
                method = getattr(mock_ai, method_name)
                
                # Try multiple times
                successes = 0
                fallback_uses = 0
                
                for _ in range(10):
                    try:
                        result = await method(**kwargs, use_fallback=True)
                        successes += 1
                        
                        # Check if fallback was used
                        history = mock_ai.get_call_history()
                        if any("fallback" in call.get("method", "") for call in history):
                            fallback_uses += 1
                        
                        mock_ai.call_history = []  # Reset for next iteration
                    except Exception:
                        pass
                
                # All should eventually succeed with fallback
                assert successes == 10
                # With 50% failure rate, expect some fallback usage
                assert fallback_uses > 2
    
    @pytest.mark.asyncio
    async def test_endpoint_error_handling(self):
        """Test proper error handling when all providers fail"""
        mock_ai = create_mock_ai_service_manager()
        
        # Make all providers fail
        for provider in mock_ai.providers:
            mock_ai.set_provider_fail_rate(provider, 1.0)
        
        # Should raise exception when all providers fail
        with pytest.raises(Exception) as exc_info:
            await mock_ai.create_completion(
                [{"role": "user", "content": "Test"}],
                use_fallback=True
            )
        
        assert "provider failed" in str(exc_info.value).lower()
    
    @pytest.mark.asyncio
    async def test_endpoint_response_consistency(self):
        """Test that responses are consistent across provider switches"""
        mock_ai = create_mock_ai_service_manager()
        
        # Test with different providers
        providers = ["deepseek", "moonshot", "openai"]
        
        for provider in providers:
            mock_ai.current_provider = provider
            mock_ai.reset_all()
            
            # Generate questionnaire
            result = await mock_ai.generate_questionnaire(
                position_type="Engineer",
                dimensions=["Technical"],
                question_count=5,
                industry="Tech"
            )
            
            # All providers should return similar structure
            assert "title" in result
            assert "questions" in result
            assert isinstance(result["questions"], list)