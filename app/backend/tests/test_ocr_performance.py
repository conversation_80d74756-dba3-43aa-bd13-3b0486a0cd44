"""
Performance tests for OCR integration

This module tests:
- OCR processing time benchmarks
- Memory usage validation
- GPU vs CPU performance comparison
- Batch processing throughput
- Large file handling
- Concurrent processing limits
"""

import asyncio
import psutil
import time
from typing import List, Dict, Any
from unittest.mock import patch, Mock

import pytest
import numpy as np

from app.services.ocr_service import OCRService, ocr_service
from app.services.resume_parser import <PERSON><PERSON><PERSON><PERSON><PERSON>, resume_parser
from tests.fixtures.ocr_fixtures import OCRTestDataGenerator, MockOCRResponses


class TestOCRPerformanceBenchmarks:
    """Performance benchmarks for OCR processing"""
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_image_ocr_processing_time(self, performance_benchmarks):
        """Test OCR processing time for different image sizes"""
        service = OCRService()
        generator = OCRTestDataGenerator()
        
        test_cases = [
            {"size": (400, 600), "name": "small_image", "expected_time": 2.0},
            {"size": (800, 1200), "name": "medium_image", "expected_time": 4.0},
            {"size": (1200, 1800), "name": "large_image", "expected_time": 8.0}
        ]
        
        results = []
        
        for case in test_cases:
            width, height = case["size"]
            image_content = generator.create_resume_image(width, height, "detailed")
            
            with patch.object(service, '_process_with_ocr') as mock_process:
                # Simulate realistic processing times
                async def mock_ocr_process(*args, **kwargs):
                    await asyncio.sleep(0.1)  # Simulate processing
                    return MockOCRResponses.success_response(
                        processing_time=case["expected_time"] * 0.8  # Slightly better than expected
                    )
                
                mock_process.side_effect = mock_ocr_process
                
                start_time = time.time()
                result = await service.extract_text("mock_path", "png")
                end_time = time.time()
                
                actual_time = end_time - start_time
                reported_time = result.get('processing_time', 0)
                
                results.append({
                    "name": case["name"],
                    "size": case["size"],
                    "actual_time": actual_time,
                    "reported_time": reported_time,
                    "expected_max": case["expected_time"],
                    "within_limit": actual_time <= case["expected_time"]
                })
        
        # Verify all tests completed within acceptable time limits
        for result in results:
            assert result["within_limit"], f"{result['name']} took {result['actual_time']:.2f}s, expected <{result['expected_max']}s"
        
        # Generate performance report
        avg_time = sum(r["actual_time"] for r in results) / len(results)
        max_time = max(r["actual_time"] for r in results)
        
        assert avg_time < 5.0, f"Average processing time {avg_time:.2f}s exceeds 5s limit"
        assert max_time < 10.0, f"Maximum processing time {max_time:.2f}s exceeds 10s limit"
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_pdf_ocr_processing_time(self, performance_benchmarks):
        """Test OCR processing time for PDF files"""
        service = OCRService()
        generator = OCRTestDataGenerator()
        
        test_cases = [
            {"name": "simple_pdf", "content": "Simple PDF content", "max_time": 5.0},
            {"name": "complex_pdf", "content": "Complex multi-page PDF", "max_time": 15.0}
        ]
        
        results = []
        
        for case in test_cases:
            pdf_content = generator.create_pdf_with_text(case["content"])
            
            with patch.object(service, '_process_with_ocr') as mock_process:
                mock_process.return_value = MockOCRResponses.success_response(
                    text=f"Extracted: {case['content']}",
                    processing_time=case["max_time"] * 0.7
                )
                
                start_time = time.time()
                result = await service.extract_text("mock_pdf_path", "pdf")
                end_time = time.time()
                
                actual_time = end_time - start_time
                
                results.append({
                    "name": case["name"],
                    "actual_time": actual_time,
                    "max_time": case["max_time"],
                    "success": result.get("success", False),
                    "within_limit": actual_time <= case["max_time"]
                })
        
        # Verify performance benchmarks
        for result in results:
            assert result["within_limit"], f"PDF {result['name']} processing took {result['actual_time']:.2f}s, expected <{result['max_time']}s"
            assert result["success"], f"PDF {result['name']} processing failed"


class TestOCRMemoryUsage:
    """Test OCR memory usage and resource management"""
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_memory_usage_during_ocr(self):
        """Test memory usage during OCR processing"""
        service = OCRService()
        generator = OCRTestDataGenerator()
        
        # Get baseline memory usage
        process = psutil.Process()
        baseline_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create large image for testing
        large_image = generator.create_resume_image(1600, 2400, "detailed")
        
        with patch.object(service, '_process_with_ocr') as mock_process:
            async def memory_intensive_ocr(*args, **kwargs):
                # Simulate memory usage during OCR
                temp_data = np.ones((1000, 1000, 3), dtype=np.uint8)  # ~3MB
                await asyncio.sleep(0.2)
                return MockOCRResponses.success_response(
                    text="Memory test content",
                    processing_time=1.5
                )
            
            mock_process.side_effect = memory_intensive_ocr
            
            # Monitor memory during processing
            peak_memory = baseline_memory
            
            async def memory_monitor():
                nonlocal peak_memory
                for _ in range(20):  # Monitor for 2 seconds
                    current_memory = process.memory_info().rss / 1024 / 1024
                    peak_memory = max(peak_memory, current_memory)
                    await asyncio.sleep(0.1)
            
            # Start monitoring and processing concurrently
            monitor_task = asyncio.create_task(memory_monitor())
            result = await service.extract_text("large_image_path", "png")
            await monitor_task
            
            memory_increase = peak_memory - baseline_memory
            
            # Assertions
            assert result["success"], "OCR processing should succeed"
            assert memory_increase < 500, f"Memory usage increased by {memory_increase:.1f}MB, should be <500MB"
            
            # Final memory check (should return to near baseline)
            final_memory = process.memory_info().rss / 1024 / 1024
            memory_leak = final_memory - baseline_memory
            assert memory_leak < 50, f"Potential memory leak: {memory_leak:.1f}MB difference from baseline"
    
    @pytest.mark.performance
    def test_file_size_limits(self):
        """Test handling of files that exceed size limits"""
        service = OCRService()
        
        # Test file size validation
        max_size = service.max_image_size
        
        # Create oversized file content
        oversized_content = b'x' * (max_size + 1000)
        
        import tempfile
        with tempfile.NamedTemporaryFile() as temp_file:
            temp_file.write(oversized_content)
            temp_file.flush()
            
            # Should fail validation due to size
            is_valid = service._validate_file(temp_file.name)
            assert not is_valid, "Oversized file should fail validation"
        
        # Test acceptable size
        acceptable_content = b'x' * (max_size // 2)
        with tempfile.NamedTemporaryFile(suffix='.png') as temp_file:
            temp_file.write(acceptable_content)
            temp_file.flush()
            
            # Note: This will still fail due to invalid image format,
            # but size validation should pass
            try:
                service._validate_file(temp_file.name)
            except:
                pass  # Expected to fail on image format, not size


class TestConcurrentOCRProcessing:
    """Test concurrent OCR processing performance"""
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_concurrent_ocr_requests(self):
        """Test handling of multiple concurrent OCR requests"""
        service = OCRService()
        generator = OCRTestDataGenerator()
        
        # Create multiple test images
        test_images = [
            generator.create_resume_image(600, 800, "simple") for _ in range(5)
        ]
        
        with patch.object(service, '_process_with_ocr') as mock_process:
            async def concurrent_ocr_process(*args, **kwargs):
                # Simulate processing delay
                await asyncio.sleep(0.3)
                return MockOCRResponses.success_response(
                    text=f"Concurrent OCR result",
                    processing_time=0.8
                )
            
            mock_process.side_effect = concurrent_ocr_process
            
            # Process all images concurrently
            start_time = time.time()
            
            tasks = [
                service.extract_text(f"image_{i}", "png")
                for i in range(len(test_images))
            ]
            
            results = await asyncio.gather(*tasks)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # Verify concurrent processing
            assert len(results) == 5, "Should process all 5 images"
            assert all(r["success"] for r in results), "All OCR operations should succeed"
            
            # Concurrent processing should be faster than sequential
            sequential_time_estimate = len(test_images) * 0.8  # 0.8s per image
            assert total_time < sequential_time_estimate, f"Concurrent processing took {total_time:.2f}s, expected <{sequential_time_estimate}s"
            
            # But not too fast (should have actual processing time)
            min_expected_time = 0.3  # At least one processing cycle
            assert total_time >= min_expected_time, f"Processing time {total_time:.2f}s seems too fast"
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_resource_contention_handling(self):
        """Test handling of resource contention during high load"""
        service = OCRService()
        
        # Simulate high load scenario
        high_load_requests = 10
        
        with patch.object(service, '_process_with_ocr') as mock_process:
            call_count = 0
            
            async def resource_contention_ocr(*args, **kwargs):
                nonlocal call_count
                call_count += 1
                
                # Simulate resource contention - some requests take longer
                if call_count <= 3:
                    delay = 0.1  # Fast processing
                    method = "ocr_gpu"
                elif call_count <= 7:
                    delay = 0.3  # Slower due to contention
                    method = "ocr_gpu"
                else:
                    delay = 0.5  # CPU fallback
                    method = "ocr_cpu_fallback"
                
                await asyncio.sleep(delay)
                return MockOCRResponses.success_response(
                    method=method,
                    processing_time=delay * 2
                )
            
            mock_process.side_effect = resource_contention_ocr
            
            # Submit requests with staggered timing
            tasks = []
            for i in range(high_load_requests):
                task = asyncio.create_task(service.extract_text(f"load_test_{i}", "png"))
                tasks.append(task)
                await asyncio.sleep(0.05)  # Stagger requests
            
            # Wait for all to complete
            start_time = time.time()
            results = await asyncio.gather(*tasks)
            end_time = time.time()
            
            total_time = end_time - start_time
            
            # Verify all requests completed
            assert len(results) == high_load_requests
            assert all(r["success"] for r in results)
            
            # Check method distribution (GPU + fallback)
            methods = [r.get("method", "") for r in results]
            gpu_count = sum(1 for m in methods if "gpu" in m)
            cpu_count = sum(1 for m in methods if "cpu" in m)
            
            assert gpu_count > 0, "Should have some GPU processing"
            assert cpu_count > 0, "Should have CPU fallback under load"
            
            # Total time should be reasonable
            assert total_time < 10.0, f"High load processing took {total_time:.2f}s, should be <10s"


class TestBatchProcessingPerformance:
    """Test batch processing performance with OCR"""
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_batch_resume_parsing_throughput(self):
        """Test throughput of batch resume parsing with OCR"""
        parser = ResumeParser()
        generator = OCRTestDataGenerator()
        
        # Create batch of resume files
        batch_size = 10
        resume_files = []
        
        for i in range(batch_size):
            content_type = ["simple", "detailed", "technical"][i % 3]
            resume_content = generator.create_resume_image(600, 800, content_type)
            resume_files.append((resume_content, f"resume_{i}.png"))
        
        with patch.object(parser, '_extract_text') as mock_extract, \
             patch.object(parser, '_extract_structured_data') as mock_structure:
            
            # Mock text extraction with realistic timing
            async def batch_text_extract(content, file_type, enable_ocr=True):
                await asyncio.sleep(0.2)  # Simulate OCR processing
                return f"Extracted text from {file_type} file with OCR: {enable_ocr}"
            
            mock_extract.side_effect = batch_text_extract
            
            # Mock structure extraction
            from app.schemas.matching import ParsedResumeData
            mock_structure.return_value = ParsedResumeData(
                basic_info={"name": "Batch Test User"},
                work_experience=[{"position": "Test Role"}],
                skills={"technical": ["Python", "Testing"]}
            )
            
            # Time the batch processing
            start_time = time.time()
            
            results = await parser.batch_parse_resumes(
                db=Mock(),  # Mock database session
                resume_files=resume_files,
                user_id=1,
                generate_embeddings=False,  # Skip embeddings for performance test
                max_concurrent=3,
                enable_ocr=True
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Calculate throughput
            throughput = batch_size / processing_time  # files per second
            
            # Assertions
            assert len(results) == batch_size, f"Should process all {batch_size} files"
            assert throughput > 1.0, f"Throughput {throughput:.2f} files/sec should be >1.0"
            assert processing_time < 30.0, f"Batch processing took {processing_time:.2f}s, should be <30s"
            
            # Verify concurrent processing was used
            assert mock_extract.call_count == batch_size
            
            # Check for any failed processing
            failed_count = sum(1 for r in results if not r.get("success", True))
            assert failed_count == 0, f"{failed_count} files failed processing"
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_mixed_file_type_batch_performance(self):
        """Test batch processing performance with mixed file types"""
        parser = ResumeParser()
        generator = OCRTestDataGenerator()
        
        # Create mixed batch
        mixed_files = [
            (generator.create_resume_image(600, 800, "simple"), "resume1.png"),
            (generator.create_pdf_with_text(), "resume2.pdf"),
            (generator.create_docx_resume(), "resume3.docx"),
            (generator.create_resume_image(600, 800, "detailed"), "resume4.jpg"),
            (generator.create_scanned_pdf_image(), "resume5.pdf")
        ]
        
        with patch.object(parser, '_extract_text') as mock_extract:
            
            async def mixed_type_extract(content, file_type, enable_ocr=True):
                # Different processing times for different types
                processing_times = {
                    'png': 0.15,
                    'jpg': 0.15,
                    'pdf': 0.25,
                    'docx': 0.1
                }
                
                await asyncio.sleep(processing_times.get(file_type.lower(), 0.2))
                
                if file_type.lower() in ['png', 'jpg'] and enable_ocr:
                    return f"OCR extracted text from {file_type}"
                elif file_type.lower() == 'pdf':
                    return f"PDF text (OCR: {enable_ocr})"
                else:
                    return f"Regular extraction from {file_type}"
            
            mock_extract.side_effect = mixed_type_extract
            
            with patch.object(parser, '_extract_structured_data') as mock_structure:
                from app.schemas.matching import ParsedResumeData
                mock_structure.return_value = ParsedResumeData(
                    basic_info={"name": "Mixed Batch Test"}
                )
                
                start_time = time.time()
                
                results = await parser.batch_parse_resumes(
                    db=Mock(),
                    resume_files=mixed_files,
                    user_id=1,
                    generate_embeddings=False,
                    max_concurrent=2,
                    enable_ocr=True
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                # Verify processing
                assert len(results) == len(mixed_files)
                assert processing_time < 10.0, f"Mixed batch took {processing_time:.2f}s, should be <10s"
                
                # Verify OCR was used appropriately
                ocr_calls = [call for call in mock_extract.call_args_list 
                           if call[1].get('enable_ocr', False)]
                assert len(ocr_calls) == len(mixed_files), "OCR should be attempted for all files"


class TestPerformanceRegression:
    """Test for performance regressions in OCR processing"""
    
    @pytest.mark.performance
    @pytest.mark.asyncio
    async def test_performance_regression_baseline(self):
        """Establish performance baseline for regression testing"""
        service = OCRService()
        generator = OCRTestDataGenerator()
        
        # Standard test case
        test_image = generator.create_resume_image(600, 800, "detailed")
        
        baseline_expectations = {
            'max_processing_time': 3.0,  # seconds
            'min_confidence': 0.8,
            'max_memory_increase': 100,  # MB
            'min_success_rate': 0.95
        }
        
        # Run multiple iterations to get stable metrics
        iterations = 5
        results = []
        
        process = psutil.Process()
        baseline_memory = process.memory_info().rss / 1024 / 1024
        
        with patch.object(service, '_process_with_ocr') as mock_process:
            mock_process.return_value = MockOCRResponses.success_response(
                text="Detailed resume content for baseline testing",
                confidence=0.87,
                processing_time=1.8
            )
            
            for i in range(iterations):
                start_time = time.time()
                result = await service.extract_text("baseline_test", "png")
                end_time = time.time()
                
                current_memory = process.memory_info().rss / 1024 / 1024
                
                results.append({
                    'iteration': i + 1,
                    'processing_time': end_time - start_time,
                    'success': result.get('success', False),
                    'confidence': result.get('confidence', 0),
                    'memory_usage': current_memory - baseline_memory
                })
        
        # Calculate metrics
        avg_time = sum(r['processing_time'] for r in results) / len(results)
        avg_confidence = sum(r['confidence'] for r in results) / len(results)
        max_memory = max(r['memory_usage'] for r in results)
        success_rate = sum(1 for r in results if r['success']) / len(results)
        
        # Regression checks
        assert avg_time <= baseline_expectations['max_processing_time'], \
            f"Average processing time {avg_time:.2f}s exceeds baseline {baseline_expectations['max_processing_time']}s"
        
        assert avg_confidence >= baseline_expectations['min_confidence'], \
            f"Average confidence {avg_confidence:.2f} below baseline {baseline_expectations['min_confidence']}"
        
        assert max_memory <= baseline_expectations['max_memory_increase'], \
            f"Memory usage {max_memory:.1f}MB exceeds baseline {baseline_expectations['max_memory_increase']}MB"
        
        assert success_rate >= baseline_expectations['min_success_rate'], \
            f"Success rate {success_rate:.2f} below baseline {baseline_expectations['min_success_rate']}"
        
        # Store baseline metrics for future comparisons
        baseline_metrics = {
            'timestamp': time.time(),
            'avg_processing_time': avg_time,
            'avg_confidence': avg_confidence,
            'max_memory_usage': max_memory,
            'success_rate': success_rate,
            'iterations': iterations
        }
        
        # In a real implementation, you'd store this in a database or file
        # for comparison in future test runs


# Test markers and configuration
pytestmark = [
    pytest.mark.performance,
    pytest.mark.asyncio
]

# Performance test configuration
PERFORMANCE_CONFIG = {
    'timeout': 120,  # 2 minutes for performance tests
    'memory_limit': 1024,  # 1GB memory limit
    'concurrent_limit': 10,  # Max concurrent operations
    'benchmark_iterations': 5  # Iterations for stable metrics
}