"""
Test fixtures for OCR integration testing

This module provides:
- Sample resume files for different formats
- Mock OCR responses for various scenarios
- Test data generation utilities
- Performance benchmarking utilities
"""

import base64
import io
import tempfile
import os
from pathlib import Path
from typing import Dict, Any, List, Tuple

import pytest
from unittest.mock import Mock
from app.services.ocr_service import OCRService

# Mock imports for missing dependencies
try:
    import numpy as np
    import cv2
except ImportError:
    np = Mock()
    cv2 = Mock()

try:
    from PIL import Image, ImageDraw, ImageFont
except ImportError:
    Image = Mock()
    ImageDraw = Mock()
    ImageFont = Mock()

try:
    import PyPDF2
except ImportError:
    PyPDF2 = Mock()

try:
    from docx import Document
except ImportError:
    Document = Mock()


class OCRTestDataGenerator:
    """Generate test data for OCR testing"""
    
    @staticmethod
    def create_resume_image(
        width: int = 600,
        height: int = 800,
        content_type: str = "detailed"
    ) -> bytes:
        """Create a resume image with realistic text content"""
        
        # Create white background
        img = np.ones((height, width, 3), dtype=np.uint8) * 255
        
        if content_type == "simple":
            # Simple resume with basic information
            texts = [
                ("JOHN DOE", 50, 60, 1.2, 3),
                ("Software Engineer", 50, 100, 0.8, 2),
                ("<EMAIL>", 50, 130, 0.6, 2),
                ("(*************", 50, 160, 0.6, 2),
                ("Python, JavaScript, React", 50, 220, 0.7, 2),
                ("5 years experience", 50, 250, 0.6, 2)
            ]
        elif content_type == "detailed":
            # Detailed resume with sections
            texts = [
                ("SARAH WILSON", 50, 50, 1.5, 3),
                ("Senior Product Manager", 50, 90, 1.0, 2),
                ("<EMAIL>", 50, 120, 0.6, 2),
                ("Phone: (*************", 50, 145, 0.6, 2),
                ("LinkedIn: linkedin.com/in/sarah-wilson", 50, 170, 0.5, 1),
                ("", 50, 195, 0.5, 1),  # spacer
                ("EXPERIENCE", 50, 220, 0.9, 3),
                ("Senior Product Manager - Tech Corp (2020-2024)", 50, 250, 0.7, 2),
                ("* Led product strategy for mobile application", 70, 280, 0.6, 2),
                ("* Managed cross-functional team of 12 people", 70, 305, 0.6, 2),
                ("* Increased user engagement by 45%", 70, 330, 0.6, 2),
                ("", 50, 355, 0.5, 1),  # spacer
                ("Product Manager - StartupCo (2018-2020)", 50, 380, 0.7, 2),
                ("* Launched 3 major product features", 70, 410, 0.6, 2),
                ("* Conducted user research and A/B testing", 70, 435, 0.6, 2),
                ("", 50, 460, 0.5, 1),  # spacer
                ("EDUCATION", 50, 485, 0.9, 3),
                ("MBA - Stanford University (2018)", 50, 515, 0.7, 2),
                ("BS Computer Science - UC Berkeley (2016)", 50, 545, 0.7, 2),
                ("", 50, 570, 0.5, 1),  # spacer
                ("SKILLS", 50, 595, 0.9, 3),
                ("Product Strategy, Agile, Scrum, Analytics", 50, 625, 0.7, 2),
                ("SQL, Python, Tableau, Figma", 50, 655, 0.7, 2),
                ("Team Leadership, Stakeholder Management", 50, 685, 0.7, 2)
            ]
        elif content_type == "technical":
            # Technical resume for software engineer
            texts = [
                ("ALEX CHEN", 50, 50, 1.4, 3),
                ("Senior Software Engineer", 50, 90, 1.0, 2),
                ("<EMAIL> | GitHub: alexchen", 50, 120, 0.6, 2),
                ("", 50, 145, 0.5, 1),
                ("TECHNICAL SKILLS", 50, 170, 0.9, 3),
                ("Languages: Python, JavaScript, Go, Java", 50, 200, 0.7, 2),
                ("Frameworks: React, Django, FastAPI, Node.js", 50, 225, 0.7, 2),
                ("Databases: PostgreSQL, MongoDB, Redis", 50, 250, 0.7, 2),
                ("Tools: Docker, Kubernetes, AWS, Git", 50, 275, 0.7, 2),
                ("", 50, 300, 0.5, 1),
                ("EXPERIENCE", 50, 325, 0.9, 3),
                ("Senior Engineer - TechGiant (2021-2024)", 50, 355, 0.7, 2),
                ("* Built microservices handling 1M+ requests/day", 70, 385, 0.6, 2),
                ("* Implemented CI/CD pipelines reducing deploy time 80%", 70, 410, 0.6, 2),
                ("* Mentored 5 junior developers", 70, 435, 0.6, 2),
                ("", 50, 460, 0.5, 1),
                ("Software Engineer - Startup (2019-2021)", 50, 485, 0.7, 2),
                ("* Developed full-stack web applications", 70, 515, 0.6, 2),
                ("* Optimized database queries improving performance 3x", 70, 540, 0.6, 2),
                ("", 50, 565, 0.5, 1),
                ("PROJECTS", 50, 590, 0.9, 3),
                ("E-commerce Platform - React, Node.js, MongoDB", 50, 620, 0.7, 2),
                ("Machine Learning Pipeline - Python, TensorFlow", 50, 645, 0.7, 2),
                ("", 50, 670, 0.5, 1),
                ("EDUCATION", 50, 695, 0.9, 3),
                ("MS Computer Science - MIT (2019)", 50, 725, 0.7, 2)
            ]
        else:
            # Minimal content
            texts = [
                ("RESUME", 250, 100, 2.0, 4),
                ("Name: Test Person", 50, 200, 1.0, 2),
                ("Title: Test Position", 50, 250, 1.0, 2)
            ]
        
        # Add text to image
        for text_info in texts:
            if len(text_info) == 5:
                text, x, y, scale, thickness = text_info
                if text:  # Skip empty spacers
                    cv2.putText(img, text, (x, y), cv2.FONT_HERSHEY_SIMPLEX, scale, (0, 0, 0), thickness)
        
        # Encode as PNG
        success, encoded_img = cv2.imencode('.png', img)
        if not success:
            raise ValueError("Failed to encode image")
        
        return encoded_img.tobytes()
    
    @staticmethod
    def create_scanned_pdf_image(
        width: int = 600,
        height: int = 800,
        quality: str = "high"
    ) -> bytes:
        """Create an image that simulates a scanned PDF document"""
        
        # Create base image
        img = np.ones((height, width, 3), dtype=np.uint8) * 255
        
        # Add document-style content
        texts = [
            ("RESUME", 250, 80, 1.8, 3),
            ("", 50, 110, 0.5, 1),
            ("Personal Information", 50, 140, 1.0, 2),
            ("Name: Michael Rodriguez", 80, 170, 0.7, 2),
            ("Email: <EMAIL>", 80, 195, 0.7, 2),
            ("Phone: (*************", 80, 220, 0.7, 2),
            ("Address: 123 Main St, City, State", 80, 245, 0.7, 2),
            ("", 50, 275, 0.5, 1),
            ("Professional Experience", 50, 305, 1.0, 2),
            ("Marketing Director - Global Corp (2020-2024)", 80, 335, 0.7, 2),
            ("- Developed marketing strategies for 15+ products", 100, 365, 0.6, 2),
            ("- Managed $2M annual marketing budget", 100, 390, 0.6, 2),
            ("- Led team of 8 marketing professionals", 100, 415, 0.6, 2),
            ("", 80, 445, 0.5, 1),
            ("Marketing Manager - StartupCorp (2018-2020)", 80, 475, 0.7, 2),
            ("- Launched go-to-market strategy for new product", 100, 505, 0.6, 2),
            ("- Increased brand awareness by 60%", 100, 530, 0.6, 2),
            ("", 50, 560, 0.5, 1),
            ("Education", 50, 590, 1.0, 2),
            ("MBA Marketing - Business School (2018)", 80, 620, 0.7, 2),
            ("BA Communications - University (2016)", 80, 645, 0.7, 2),
            ("", 50, 675, 0.5, 1),
            ("Skills", 50, 705, 1.0, 2),
            ("Digital Marketing, SEO, SEM, Analytics", 80, 735, 0.7, 2),
            ("Team Leadership, Project Management", 80, 760, 0.7, 2)
        ]
        
        # Add text with appropriate quality degradation
        for text, x, y, scale, thickness in texts:
            if text:
                cv2.putText(img, text, (x, y), cv2.FONT_HERSHEY_SIMPLEX, scale, (0, 0, 0), thickness)
        
        # Simulate scanning artifacts
        if quality == "low":
            # Add noise and blur
            noise = np.random.normal(0, 10, img.shape).astype(np.uint8)
            img = cv2.add(img, noise)
            img = cv2.GaussianBlur(img, (3, 3), 0)
        elif quality == "medium":
            # Light noise
            noise = np.random.normal(0, 5, img.shape).astype(np.uint8)
            img = cv2.add(img, noise)
        # High quality - no degradation
        
        # Encode as PNG
        success, encoded_img = cv2.imencode('.png', img)
        if not success:
            raise ValueError("Failed to encode scanned image")
        
        return encoded_img.tobytes()
    
    @staticmethod
    def create_pdf_with_text(content: str = None) -> bytes:
        """Create a PDF with extractable text"""
        
        if content is None:
            content = """RESUME
            
John Smith
Software Developer
<EMAIL>
(*************

EXPERIENCE

Senior Developer - Tech Company (2022-2024)
• Developed web applications using Python and JavaScript
• Implemented microservices architecture
• Mentored junior developers

Developer - Previous Company (2020-2022)
• Built REST APIs using Django
• Worked with PostgreSQL databases
• Participated in agile development

EDUCATION

Bachelor of Computer Science - University (2020)

SKILLS

Python, JavaScript, React, Django, PostgreSQL, Docker, Git"""
        
        # Create PDF in memory
        pdf_buffer = io.BytesIO()
        
        # Note: This is a simplified PDF creation
        # In practice, you'd use a library like reportlab for text-containing PDFs
        pdf_writer = PyPDF2.PdfWriter()
        
        # Create a blank page (this won't have the text content)
        # For testing purposes, we'll create a minimal PDF structure
        pdf_writer.add_blank_page(width=612, height=792)
        
        pdf_writer.write(pdf_buffer)
        return pdf_buffer.getvalue()
    
    @staticmethod
    def create_scanned_pdf() -> bytes:
        """Create a PDF that simulates a scanned document (no extractable text)"""
        return OCRTestDataGenerator.create_pdf_with_text("")  # Empty text content
    
    @staticmethod
    def create_docx_resume() -> bytes:
        """Create a DOCX resume file"""
        
        doc = Document()
        
        # Title
        title = doc.add_heading('RESUME', 0)
        title.alignment = 1  # Center alignment
        
        # Personal Info
        doc.add_heading('Personal Information', level=1)
        personal_info = [
            'Name: Jennifer Lee',
            'Email: <EMAIL>',
            'Phone: (*************',
            'LinkedIn: linkedin.com/in/jennifer-lee'
        ]
        for info in personal_info:
            doc.add_paragraph(info)
        
        # Experience
        doc.add_heading('Professional Experience', level=1)
        
        job1 = doc.add_heading('Data Scientist - Analytics Corp (2021-2024)', level=2)
        job1_duties = [
            'Developed machine learning models for predictive analytics',
            'Analyzed large datasets using Python and SQL',
            'Created data visualizations and dashboards',
            'Collaborated with cross-functional teams'
        ]
        for duty in job1_duties:
            p = doc.add_paragraph(duty)
            p.style = 'List Bullet'
        
        job2 = doc.add_heading('Junior Data Analyst - DataCorp (2019-2021)', level=2)
        job2_duties = [
            'Performed statistical analysis on business data',
            'Created reports for management team',
            'Maintained data quality and integrity'
        ]
        for duty in job2_duties:
            p = doc.add_paragraph(duty)
            p.style = 'List Bullet'
        
        # Education
        doc.add_heading('Education', level=1)
        doc.add_paragraph('Master of Data Science - Tech University (2019)')
        doc.add_paragraph('Bachelor of Mathematics - State University (2017)')
        
        # Skills
        doc.add_heading('Technical Skills', level=1)
        skills = [
            'Programming: Python, R, SQL, JavaScript',
            'Machine Learning: Scikit-learn, TensorFlow, PyTorch',
            'Data Visualization: Tableau, Power BI, Matplotlib',
            'Databases: PostgreSQL, MongoDB, BigQuery',
            'Cloud: AWS, Google Cloud Platform'
        ]
        for skill in skills:
            doc.add_paragraph(skill, style='List Bullet')
        
        # Save to bytes
        doc_buffer = io.BytesIO()
        doc.save(doc_buffer)
        return doc_buffer.getvalue()
    
    @staticmethod
    def create_corrupted_file(file_type: str = "pdf") -> bytes:
        """Create a corrupted file for error testing"""
        if file_type == "pdf":
            return b"%%PDF-1.4\nCorrupted PDF content that cannot be parsed"
        elif file_type == "image":
            return b"\x89PNG\r\n\x1a\nCorrupted PNG header with invalid data"
        elif file_type == "docx":
            return b"PK\x03\x04Corrupted DOCX file"
        else:
            return b"Corrupted file content"


class MockOCRResponses:
    """Mock OCR service responses for testing"""
    
    @staticmethod
    def success_response(
        text: str = "Mock OCR extracted text",
        confidence: float = 0.85,
        method: str = "ocr_gpu",
        processing_time: float = 1.5
    ) -> Dict[str, Any]:
        """Generate a successful OCR response"""
        return {
            'success': True,
            'text': text,
            'confidence': confidence,
            'method': method,
            'processing_time': processing_time
        }
    
    @staticmethod
    def failure_response(
        error: str = "OCR processing failed",
        processing_time: float = 0.5
    ) -> Dict[str, Any]:
        """Generate a failed OCR response"""
        return {
            'success': False,
            'text': "",
            'confidence': 0.0,
            'method': 'error',
            'processing_time': processing_time,
            'error': error
        }
    
    @staticmethod
    def low_confidence_response(
        text: str = "Unclear text",
        confidence: float = 0.2,
        processing_time: float = 2.0
    ) -> Dict[str, Any]:
        """Generate a low confidence OCR response"""
        return {
            'success': False,
            'text': text,
            'confidence': confidence,
            'method': 'ocr_gpu',
            'processing_time': processing_time,
            'error': f'Low confidence: {confidence:.3f}'
        }
    
    @staticmethod
    def timeout_response(timeout_seconds: int = 60) -> Dict[str, Any]:
        """Generate a timeout OCR response"""
        return {
            'success': False,
            'text': "",
            'confidence': 0.0,
            'method': 'error',
            'processing_time': float(timeout_seconds),
            'error': f'Processing timeout ({timeout_seconds}s)'
        }
    
    @staticmethod
    def gpu_fallback_response(
        text: str = "CPU fallback text",
        confidence: float = 0.78
    ) -> Dict[str, Any]:
        """Generate a GPU to CPU fallback response"""
        return {
            'success': True,
            'text': text,
            'confidence': confidence,
            'method': 'ocr_cpu_fallback',
            'processing_time': 3.2
        }


# Test fixtures
@pytest.fixture
def sample_resume_files():
    """Generate sample resume files for testing"""
    generator = OCRTestDataGenerator()
    
    return {
        'simple_image': generator.create_resume_image(content_type="simple"),
        'detailed_image': generator.create_resume_image(content_type="detailed"),
        'technical_image': generator.create_resume_image(content_type="technical"),
        'scanned_pdf_image': generator.create_scanned_pdf_image(quality="high"),
        'low_quality_scan': generator.create_scanned_pdf_image(quality="low"),
        'text_pdf': generator.create_pdf_with_text(),
        'scanned_pdf': generator.create_scanned_pdf(),
        'docx_resume': generator.create_docx_resume(),
        'corrupted_pdf': generator.create_corrupted_file("pdf"),
        'corrupted_image': generator.create_corrupted_file("image")
    }


@pytest.fixture
def temp_test_files(sample_resume_files):
    """Create temporary files for testing"""
    temp_files = {}
    
    for name, content in sample_resume_files.items():
        # Determine file extension
        if 'image' in name or 'scan' in name:
            ext = '.png'
        elif 'pdf' in name:
            ext = '.pdf'
        elif 'docx' in name:
            ext = '.docx'
        else:
            ext = '.bin'
        
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix=ext, delete=False)
        temp_file.write(content)
        temp_file.close()
        
        temp_files[name] = temp_file.name
    
    yield temp_files
    
    # Cleanup
    for file_path in temp_files.values():
        try:
            os.unlink(file_path)
        except FileNotFoundError:
            pass


@pytest.fixture
def mock_ocr_responses():
    """Generate mock OCR responses for testing"""
    return {
        'success': MockOCRResponses.success_response(),
        'failure': MockOCRResponses.failure_response(),
        'low_confidence': MockOCRResponses.low_confidence_response(),
        'timeout': MockOCRResponses.timeout_response(),
        'gpu_fallback': MockOCRResponses.gpu_fallback_response(),
        'detailed_success': MockOCRResponses.success_response(
            text="""SARAH WILSON
Senior Product Manager
<EMAIL>
Phone: (*************

EXPERIENCE
Senior Product Manager - Tech Corp (2020-2024)
* Led product strategy for mobile application
* Managed cross-functional team of 12 people
* Increased user engagement by 45%

SKILLS
Product Strategy, Agile, Scrum, Analytics
SQL, Python, Tableau, Figma
Team Leadership, Stakeholder Management""",
            confidence=0.92,
            processing_time=2.1
        ),
        'technical_success': MockOCRResponses.success_response(
            text="""ALEX CHEN
Senior Software Engineer
<EMAIL>

TECHNICAL SKILLS
Languages: Python, JavaScript, Go, Java
Frameworks: React, Django, FastAPI, Node.js
Databases: PostgreSQL, MongoDB, Redis
Tools: Docker, Kubernetes, AWS, Git

EXPERIENCE
Senior Engineer - TechGiant (2021-2024)
* Built microservices handling 1M+ requests/day
* Implemented CI/CD pipelines reducing deploy time 80%
* Mentored 5 junior developers""",
            confidence=0.89,
            processing_time=1.8
        )
    }


@pytest.fixture
def performance_benchmarks():
    """Performance benchmarks for OCR testing"""
    return {
        'acceptable_processing_time': {
            'image': 5.0,      # seconds
            'pdf': 10.0,       # seconds
            'large_file': 30.0  # seconds
        },
        'min_confidence_thresholds': {
            'high_quality': 0.9,
            'medium_quality': 0.7,
            'low_quality': 0.5
        },
        'memory_limits': {
            'max_image_size': 10 * 1024 * 1024,  # 10MB
            'max_pdf_size': 50 * 1024 * 1024     # 50MB
        },
        'success_rates': {
            'target_success_rate': 0.95,
            'min_acceptable_rate': 0.85
        }
    }


@pytest.fixture(scope="session")
def test_data_directory():
    """Create a temporary directory for test data"""
    temp_dir = tempfile.mkdtemp(prefix="ocr_test_data_")
    yield temp_dir
    
    # Cleanup
    import shutil
    try:
        shutil.rmtree(temp_dir)
    except OSError:
        pass


class OCRTestUtils:
    """Utility functions for OCR testing"""
    
    @staticmethod
    def validate_ocr_response(response: Dict[str, Any]) -> bool:
        """Validate OCR response structure"""
        required_fields = ['success', 'text', 'confidence', 'method', 'processing_time']
        
        for field in required_fields:
            if field not in response:
                return False
        
        # Type validation
        if not isinstance(response['success'], bool):
            return False
        if not isinstance(response['text'], str):
            return False
        if not isinstance(response['confidence'], (int, float)):
            return False
        if not isinstance(response['method'], str):
            return False
        if not isinstance(response['processing_time'], (int, float)):
            return False
        
        # Range validation
        if not (0.0 <= response['confidence'] <= 1.0):
            return False
        if response['processing_time'] < 0:
            return False
        
        return True
    
    @staticmethod
    def calculate_text_similarity(text1: str, text2: str) -> float:
        """Calculate simple text similarity for validation"""
        if not text1 or not text2:
            return 0.0
        
        words1 = set(text1.lower().split())
        words2 = set(text2.lower().split())
        
        if not words1 or not words2:
            return 0.0
        
        intersection = len(words1.intersection(words2))
        union = len(words1.union(words2))
        
        return intersection / union if union > 0 else 0.0
    
    @staticmethod
    def generate_performance_report(
        results: List[Dict[str, Any]],
        benchmarks: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate a performance report from test results"""
        if not results:
            return {"error": "No results provided"}
        
        successful_results = [r for r in results if r.get('success', False)]
        success_rate = len(successful_results) / len(results)
        
        if successful_results:
            avg_processing_time = sum(r.get('processing_time', 0) for r in successful_results) / len(successful_results)
            avg_confidence = sum(r.get('confidence', 0) for r in successful_results) / len(successful_results)
        else:
            avg_processing_time = 0
            avg_confidence = 0
        
        report = {
            'total_tests': len(results),
            'successful_tests': len(successful_results),
            'success_rate': success_rate,
            'avg_processing_time': avg_processing_time,
            'avg_confidence': avg_confidence,
            'benchmarks_met': {
                'success_rate': success_rate >= benchmarks.get('success_rates', {}).get('min_acceptable_rate', 0.85),
                'processing_time': avg_processing_time <= benchmarks.get('acceptable_processing_time', {}).get('image', 5.0)
            }
        }
        
        return report
    
    @staticmethod
    def create_base64_test_data(file_content: bytes) -> str:
        """Convert file content to base64 for API testing"""
        return base64.b64encode(file_content).decode('utf-8')


@pytest.fixture
def ocr_test_utils():
    """Provide OCR test utilities"""
    return OCRTestUtils()