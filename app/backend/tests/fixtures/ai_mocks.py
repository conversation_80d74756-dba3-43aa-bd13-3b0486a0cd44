"""
Mock AI Provider Infrastructure for Testing
"""
import json
import random
from typing import Dict, Any, List, Optional
from unittest.mock import AsyncMock, MagicMock
import asyncio


class MockAIProvider:
    """Base mock AI provider for testing"""
    
    def __init__(self, provider_name: str, fail_rate: float = 0.0):
        self.provider_name = provider_name
        self.fail_rate = fail_rate
        self.call_count = 0
        self.last_request = None
        self.is_healthy = True
        
    async def create_completion(self, messages: List[Dict], **kwargs) -> Dict[str, Any]:
        """Mock completion creation"""
        self.call_count += 1
        self.last_request = {"messages": messages, "kwargs": kwargs}
        
        # Simulate failure based on fail_rate
        if random.random() < self.fail_rate:
            raise Exception(f"Mock {self.provider_name} provider failed")
        
        # Return mock response based on provider
        if kwargs.get("response_format") == "json":
            return self._get_json_response(messages)
        else:
            return self._get_text_response(messages)
    
    async def create_embedding(self, text: str, **kwargs) -> List[float]:
        """Mock embedding creation"""
        self.call_count += 1
        self.last_request = {"text": text, "kwargs": kwargs}
        
        # Simulate failure
        if random.random() < self.fail_rate:
            raise Exception(f"Mock {self.provider_name} embedding failed")
        
        # Return mock embedding (1024 dimensions for BGE-M3)
        return [random.random() for _ in range(1024)]
    
    def _get_json_response(self, messages: List[Dict]) -> Dict:
        """Generate mock JSON response"""
        user_content = messages[-1].get("content", "") if messages else ""
        
        # Mock responses based on content patterns
        if "questionnaire" in user_content.lower():
            return {
                "title": "Test Questionnaire",
                "description": "Mock questionnaire for testing",
                "questions": [
                    {
                        "id": "q1",
                        "content": "Test question 1",
                        "type": "single_choice",
                        "options": ["A", "B", "C", "D"]
                    }
                ]
            }
        elif "evaluate" in user_content.lower():
            return {
                "score": 85,
                "feedback": "Mock evaluation feedback",
                "strengths": ["Good understanding"],
                "improvements": ["Needs more detail"]
            }
        elif "extract" in user_content.lower() or "resume" in user_content.lower():
            return {
                "name": "John Doe",
                "email": "<EMAIL>",
                "phone": "+**********",
                "skills": ["Python", "FastAPI", "PostgreSQL"],
                "years_of_experience": 5
            }
        else:
            return {"response": "Mock JSON response", "provider": self.provider_name}
    
    def _get_text_response(self, messages: List[Dict]) -> str:
        """Generate mock text response"""
        return f"Mock response from {self.provider_name}: Processed {len(messages)} messages"
    
    async def health_check(self) -> bool:
        """Mock health check"""
        return self.is_healthy
    
    def set_healthy(self, is_healthy: bool):
        """Set provider health status"""
        self.is_healthy = is_healthy
    
    def reset(self):
        """Reset mock state"""
        self.call_count = 0
        self.last_request = None
        self.is_healthy = True


class MockAIServiceManager:
    """Mock AIServiceManager for testing"""
    
    def __init__(self):
        self.providers = {
            "deepseek": MockAIProvider("deepseek"),
            "moonshot": MockAIProvider("moonshot"),
            "openrouter": MockAIProvider("openrouter"),
            "qwen": MockAIProvider("qwen"),
            "openai": MockAIProvider("openai")
        }
        self.current_provider = "deepseek"
        self.fallback_provider = "moonshot"
        self.use_fallback = True
        self.call_history = []
        
    async def create_completion(
        self,
        messages: List[Dict],
        temperature: float = 0.7,
        max_tokens: int = 2000,
        response_format: Optional[str] = None,
        use_fallback: bool = True,
        **kwargs
    ) -> Any:
        """Mock completion with fallback support"""
        self.call_history.append({
            "method": "create_completion",
            "provider": self.current_provider,
            "messages": messages
        })
        
        # Try primary provider
        try:
            provider = self.providers[self.current_provider]
            return await provider.create_completion(
                messages,
                temperature=temperature,
                max_tokens=max_tokens,
                response_format=response_format,
                **kwargs
            )
        except Exception as e:
            if use_fallback and self.fallback_provider:
                # Try fallback provider
                self.call_history.append({
                    "method": "create_completion_fallback",
                    "provider": self.fallback_provider,
                    "reason": str(e)
                })
                fallback = self.providers[self.fallback_provider]
                return await fallback.create_completion(
                    messages,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    response_format=response_format,
                    **kwargs
                )
            raise
    
    async def create_embedding(
        self,
        text: str,
        model: str = "bge-m3",
        use_fallback: bool = True,
        **kwargs
    ) -> List[float]:
        """Mock embedding creation with fallback"""
        self.call_history.append({
            "method": "create_embedding",
            "provider": self.current_provider,
            "text": text[:100]  # Log first 100 chars
        })
        
        # Try primary provider
        try:
            provider = self.providers[self.current_provider]
            return await provider.create_embedding(text, model=model, **kwargs)
        except Exception as e:
            if use_fallback and self.fallback_provider:
                # Try fallback provider
                self.call_history.append({
                    "method": "create_embedding_fallback",
                    "provider": self.fallback_provider,
                    "reason": str(e)
                })
                fallback = self.providers[self.fallback_provider]
                return await fallback.create_embedding(text, model=model, **kwargs)
            raise
    
    async def evaluate_responses(self, **kwargs) -> Dict[str, Any]:
        """Mock response evaluation"""
        messages = [
            {"role": "system", "content": "You are an evaluation expert"},
            {"role": "user", "content": json.dumps(kwargs)}
        ]
        return await self.create_completion(messages, response_format="json")
    
    async def generate_questionnaire(self, **kwargs) -> Dict[str, Any]:
        """Mock questionnaire generation"""
        messages = [
            {"role": "system", "content": "You are a questionnaire generator"},
            {"role": "user", "content": json.dumps(kwargs)}
        ]
        return await self.create_completion(messages, response_format="json")
    
    async def evaluate_text_response(self, question: str, response: str, **kwargs) -> Dict:
        """Mock text response evaluation"""
        messages = [
            {"role": "system", "content": "Evaluate the response"},
            {"role": "user", "content": f"Question: {question}\\nResponse: {response}"}
        ]
        return await self.create_completion(messages, response_format="json")
    
    async def generate_feedback(self, **kwargs) -> str:
        """Mock feedback generation"""
        messages = [
            {"role": "system", "content": "Generate feedback"},
            {"role": "user", "content": json.dumps(kwargs)}
        ]
        return await self.create_completion(messages)
    
    def is_available(self) -> bool:
        """Check if any provider is available"""
        return any(p.is_healthy for p in self.providers.values())
    
    def get_current_provider_info(self) -> Dict[str, str]:
        """Get current provider information"""
        return {
            "name": self.current_provider,
            "type": "mock",
            "status": "healthy" if self.providers[self.current_provider].is_healthy else "unhealthy"
        }
    
    def set_provider_health(self, provider: str, is_healthy: bool):
        """Set provider health status for testing"""
        if provider in self.providers:
            self.providers[provider].set_healthy(is_healthy)
    
    def set_provider_fail_rate(self, provider: str, fail_rate: float):
        """Set provider failure rate for testing"""
        if provider in self.providers:
            self.providers[provider].fail_rate = fail_rate
    
    def reset_all(self):
        """Reset all mock state"""
        for provider in self.providers.values():
            provider.reset()
        self.call_history = []
    
    def get_call_history(self) -> List[Dict]:
        """Get call history for verification"""
        return self.call_history


def create_mock_ai_service_manager() -> MockAIServiceManager:
    """Factory function to create mock AI service manager"""
    return MockAIServiceManager()


def create_failing_provider(provider_name: str, fail_rate: float = 1.0) -> MockAIProvider:
    """Create a provider that fails at specified rate"""
    return MockAIProvider(provider_name, fail_rate=fail_rate)


def create_slow_provider(provider_name: str, delay: float = 2.0) -> MockAIProvider:
    """Create a provider with simulated latency"""
    class SlowMockProvider(MockAIProvider):
        async def create_completion(self, messages: List[Dict], **kwargs):
            await asyncio.sleep(delay)
            return await super().create_completion(messages, **kwargs)
        
        async def create_embedding(self, text: str, **kwargs):
            await asyncio.sleep(delay)
            return await super().create_embedding(text, **kwargs)
    
    return SlowMockProvider(provider_name)


# Mock responses for specific test scenarios
MOCK_RESUME_EXTRACTION = {
    "name": "Jane Smith",
    "email": "<EMAIL>",
    "phone": "******-0123",
    "current_position": "Senior Software Engineer",
    "current_company": "Tech Corp",
    "years_of_experience": 8,
    "skills": ["Python", "FastAPI", "PostgreSQL", "Docker", "Kubernetes"],
    "education": [
        {
            "school": "MIT",
            "degree": "Master",
            "major": "Computer Science",
            "duration": "2014-2016"
        }
    ],
    "work_experience": [
        {
            "company": "Tech Corp",
            "position": "Senior Software Engineer",
            "duration": "2018-Present",
            "description": "Leading backend development team"
        }
    ]
}

MOCK_QUESTIONNAIRE = {
    "title": "Software Engineering Assessment",
    "description": "Comprehensive assessment for software engineering position",
    "position_type": "Software Engineer",
    "industry": "Technology",
    "dimensions": ["Technical Skills", "Problem Solving", "Communication"],
    "questions": [
        {
            "id": "q1",
            "content": "Describe your experience with microservices architecture",
            "type": "text",
            "dimension": "Technical Skills",
            "required": True,
            "scoring": {"max_score": 10, "scoring_type": "ai_evaluated"}
        },
        {
            "id": "q2",
            "content": "What is your preferred programming language?",
            "type": "single_choice",
            "dimension": "Technical Skills",
            "required": True,
            "options": ["Python", "Java", "JavaScript", "Go", "Other"],
            "scoring": {"max_score": 5, "scoring_type": "direct"}
        }
    ]
}

MOCK_EVALUATION = {
    "score": 88,
    "feedback": "Strong technical background with excellent problem-solving skills",
    "strengths": [
        "Deep understanding of microservices",
        "Good communication skills",
        "Strong Python expertise"
    ],
    "improvements": [
        "Could benefit from more cloud platform experience",
        "Consider expanding knowledge in data structures"
    ],
    "confidence_score": 0.92
}