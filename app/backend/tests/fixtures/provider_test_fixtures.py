"""
Shared test data, response validators, and utility functions for AI provider testing
"""
import re
from dataclasses import dataclass
from typing import Dict, Any, List, Optional, Pattern
from unittest.mock import Mock, AsyncMock

from tests.ai_providers.test_config import PROVIDER_EXPECTATIONS, MODEL_PATTERNS, ERROR_PATTERNS


@dataclass
class MockResponse:
    """Mock response for testing"""
    status_code: int = 200
    data: Optional[List[Dict[str, Any]]] = None
    choices: Optional[List[Any]] = None
    
    def json(self):
        return {"data": self.data} if self.data else {}


@dataclass
class MockChoice:
    """Mock choice for conversation response"""
    message: Any
    
    def __init__(self, content: str):
        self.message = MockMessage(content)


@dataclass
class MockMessage:
    """Mock message for conversation response"""
    content: str


class ResponseValidator:
    """
    Utility class for standardizing response validation across providers
    """
    
    def __init__(self):
        self.model_patterns = MODEL_PATTERNS
        self.expectations = PROVIDER_EXPECTATIONS
    
    def validate_models_response(self, response: Any, provider: str) -> Dict[str, Any]:
        """
        Validate models endpoint response format
        
        Args:
            response: API response from models endpoint
            provider: Provider name
            
        Returns:
            Dict: Validation results
        """
        result = {
            "valid": False,
            "format_correct": False,
            "models_count": 0,
            "sample_models": [],
            "expected_patterns_found": 0,
            "issues": []
        }
        
        try:
            if provider == "ollama":
                # Ollama returns different format
                if isinstance(response, dict) and 'models' in response:
                    models = response['models']
                    result["format_correct"] = True
                    result["models_count"] = len(models)
                    result["sample_models"] = [m.get('name', 'unknown') for m in models[:5]]
                elif isinstance(response, list):
                    result["format_correct"] = True
                    result["models_count"] = len(response)
                    result["sample_models"] = [str(m)[:50] for m in response[:5]]
                else:
                    result["issues"].append("Expected dict with 'models' key or list, got: " + str(type(response)))
            else:
                # OpenAI-compatible format
                if hasattr(response, 'data') and isinstance(response.data, list):
                    result["format_correct"] = True
                    result["models_count"] = len(response.data)
                    result["sample_models"] = [getattr(m, 'id', str(m)) for m in response.data[:5]]
                else:
                    result["issues"].append("Expected response with .data list attribute")
            
            # Check expected model patterns
            if result["sample_models"]:
                patterns = self.model_patterns.get(provider, [])
                for model in result["sample_models"]:
                    for pattern in patterns:
                        if re.search(pattern, model, re.IGNORECASE):
                            result["expected_patterns_found"] += 1
                            break
            
            # Overall validation
            result["valid"] = (
                result["format_correct"] and 
                result["models_count"] > 0 and
                len(result["issues"]) == 0
            )
            
        except Exception as e:
            result["issues"].append(f"Validation error: {str(e)}")
        
        return result
    
    def validate_conversation_response(self, response: Any, provider: str, prompt: str) -> Dict[str, Any]:
        """
        Validate conversation response format and content
        
        Args:
            response: API response from conversation endpoint
            provider: Provider name
            prompt: Original prompt sent
            
        Returns:
            Dict: Validation results
        """
        result = {
            "valid": False,
            "format_correct": False,
            "has_content": False,
            "content_length": 0,
            "content_preview": "",
            "response_quality": "unknown",
            "issues": []
        }
        
        try:
            content = None
            
            # Extract content based on response format
            if hasattr(response, 'choices') and len(response.choices) > 0:
                choice = response.choices[0]
                if hasattr(choice, 'message') and hasattr(choice.message, 'content'):
                    content = choice.message.content
                    result["format_correct"] = True
                else:
                    result["issues"].append("Response choice missing message.content")
            elif provider == "ollama" and isinstance(response, dict):
                # Ollama native format
                content = response.get("message", {}).get("content", "")
                result["format_correct"] = True
            else:
                result["issues"].append("Unexpected response format: " + str(type(response)))
            
            # Validate content
            if content is not None:
                result["has_content"] = len(content.strip()) > 0
                result["content_length"] = len(content)
                result["content_preview"] = content[:100]
                
                # Assess response quality
                if len(content.strip()) < 5:
                    result["response_quality"] = "too_short"
                elif len(content.strip()) < 10:
                    result["response_quality"] = "short"
                elif any(char.isalpha() for char in content):
                    result["response_quality"] = "good"
                    
                    # Check for relevant response to Chinese greeting
                    if "你好" in prompt and any(indicator in content.lower() for indicator in ["你好", "hello", "我是", "助手", "ai", "人工智能"]):
                        result["response_quality"] = "excellent"
                else:
                    result["response_quality"] = "poor"
            
            # Overall validation
            result["valid"] = (
                result["format_correct"] and
                result["has_content"] and
                result["content_length"] >= 10 and
                len(result["issues"]) == 0
            )
            
        except Exception as e:
            result["issues"].append(f"Validation error: {str(e)}")
        
        return result
    
    def categorize_error_by_message(self, error_message: str) -> str:
        """
        Categorize error based on message content
        
        Args:
            error_message: Error message to categorize
            
        Returns:
            str: Error category
        """
        error_lower = error_message.lower()
        
        for category, patterns in ERROR_PATTERNS.items():
            for pattern in patterns:
                if pattern in error_lower:
                    return category
        
        return "unknown"
    
    def get_provider_expectations(self, provider: str) -> Dict[str, Any]:
        """
        Get expectations for specific provider
        
        Args:
            provider: Provider name
            
        Returns:
            Dict: Provider expectations
        """
        return self.expectations.get(provider, {})


class MockProviderFactory:
    """
    Factory for creating mock providers for offline testing
    """
    
    @staticmethod
    def create_successful_models_response(provider: str, model_count: int = 3) -> Any:
        """Create successful models response"""
        expectations = PROVIDER_EXPECTATIONS.get(provider, {})
        expected_models = expectations.get("expected_models", [f"model-{i}" for i in range(model_count)])
        
        if provider == "ollama":
            return {
                "models": [
                    {"name": model, "size": 1000000, "digest": "abc123"}
                    for model in expected_models[:model_count]
                ]
            }
        else:
            # OpenAI-compatible format
            mock_models = []
            for model in expected_models[:model_count]:
                mock_model = Mock()
                mock_model.id = model
                mock_model.object = "model"
                mock_model.created = **********
                mock_models.append(mock_model)
            
            response = Mock()
            response.data = mock_models
            return response
    
    @staticmethod
    def create_successful_conversation_response(provider: str, prompt: str) -> Any:
        """Create successful conversation response"""
        # Generate appropriate response based on prompt
        if "你好" in prompt:
            content = "你好！我是一个AI助手，很高兴为您服务。有什么我可以帮助您的吗？"
        elif "hello" in prompt.lower():
            content = "Hello! I'm an AI assistant. How can I help you today?"
        else:
            content = "I'm an AI assistant. How can I help you?"
        
        if provider == "ollama":
            return {
                "message": {
                    "role": "assistant",
                    "content": content
                },
                "done": True
            }
        else:
            # OpenAI-compatible format
            choice = Mock()
            choice.message = Mock()
            choice.message.content = content
            choice.message.role = "assistant"
            choice.finish_reason = "stop"
            
            response = Mock()
            response.choices = [choice]
            response.usage = Mock()
            response.usage.total_tokens = 50
            
            return response
    
    @staticmethod
    def create_error_response(error_type: str, provider: str) -> Exception:
        """Create error response for testing"""
        error_messages = {
            "auth": f"Authentication failed for {provider}: invalid API key",
            "network": f"Network error connecting to {provider}: connection refused",
            "config": f"Configuration error for {provider}: missing API key",
            "api": f"API error from {provider}: rate limit exceeded (429)",
            "response": f"Response parsing error from {provider}: invalid JSON format"
        }
        
        message = error_messages.get(error_type, f"Unknown error from {provider}")
        
        if error_type == "network":
            import httpx
            return httpx.ConnectError(message)
        elif error_type == "auth":
            return PermissionError(message)
        else:
            return Exception(message)


class TestDataGenerator:
    """
    Generator for test data and scenarios
    """
    
    @staticmethod
    def generate_test_prompts() -> List[Dict[str, Any]]:
        """Generate test prompts for conversation testing"""
        return [
            {
                "name": "chinese_greeting",
                "prompt": "你好，你是谁？",
                "expected_keywords": ["你好", "我是", "助手", "AI"],
                "min_length": 10,
                "timeout": 30
            },
            {
                "name": "english_greeting", 
                "prompt": "Hello, who are you?",
                "expected_keywords": ["hello", "i am", "assistant", "ai"],
                "min_length": 10,
                "timeout": 30
            },
            {
                "name": "simple_question",
                "prompt": "What is 2+2?", 
                "expected_keywords": ["4", "four", "answer"],
                "min_length": 5,
                "timeout": 20
            }
        ]
    
    @staticmethod
    def generate_provider_configs() -> Dict[str, Dict[str, Any]]:
        """Generate provider configurations for testing"""
        configs = {}
        
        for provider, expectations in PROVIDER_EXPECTATIONS.items():
            configs[provider] = {
                "timeout": 30,
                "max_retries": 2,
                "expected_models": expectations.get("expected_models", []),
                "conversation_support": expectations.get("conversation_support", True),
                "models_endpoint": expectations.get("models_endpoint", True),
                "special_requirements": expectations.get("special_requirements", "None")
            }
        
        return configs


# Global instances for easy access
response_validator = ResponseValidator()
mock_factory = MockProviderFactory()
test_data_generator = TestDataGenerator()