"""
Authentication CI/CD Tests

Minimal tests specifically designed for continuous integration/deployment.
Fast execution, clear pass/fail, essential functionality only.
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core import security
from app.models.user import User
from app.core.enums import UserRole


@pytest.mark.smoke
class TestAuthCI:
    """CI/CD-focused authentication tests"""

    @pytest.mark.asyncio
    async def test_auth_system_imports_successfully(self):
        """Test all auth-related imports work (import errors break everything)"""
        # These imports should not fail in CI
        from app.api.v1.auth import router
        from app.api.deps import get_current_user
        from app.core.security import create_access_token, verify_password
        from app.crud.user import user as user_crud
        assert router is not None
        assert get_current_user is not None

    @pytest.mark.asyncio
    async def test_jwt_token_basic_functionality(self):
        """Test JWT tokens can be created and decoded"""
        user_id = 12345
        token = security.create_access_token(user_id)
        payload = security.decode_token(token)
        
        assert int(payload.get("sub")) == user_id
        assert payload.get("type") == "access"

    @pytest.mark.asyncio
    async def test_password_hashing_works(self):
        """Test password hashing and verification"""
        password = "test_password_123"
        hashed = security.get_password_hash(password)
        
        assert security.verify_password(password, hashed)
        assert not security.verify_password("wrong_password", hashed)

    @pytest.mark.asyncio
    async def test_user_model_with_enum_creates_successfully(self, db_session: AsyncSession):
        """Test user creation with enum (regression test for enum conflicts)"""
        user = User(
            email="<EMAIL>",
            username="ci_test",
            hashed_password=security.get_password_hash("password"),
            full_name="CI Test User",
            role=UserRole.USER,
            is_active=True
        )
        
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        
        assert user.id is not None
        assert user.role == UserRole.USER

    @pytest.mark.asyncio
    async def test_auth_endpoints_respond(self, client: AsyncClient):
        """Test auth endpoints are reachable and respond appropriately"""
        # Login endpoint should exist (will fail auth but not 404)
        response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={"username": "test", "password": "test"}
        )
        assert response.status_code in [401, 422]  # Not 404 or 500

        # Me endpoint should require auth
        response = await client.get(f"{settings.API_V1_STR}/auth/me")
        assert response.status_code == 401

    @pytest.mark.asyncio
    async def test_basic_auth_flow_end_to_end(self, client: AsyncClient, test_user: User, test_password: str):
        """Test complete auth flow works (critical path test)"""
        # Login
        response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": test_password
            }
        )
        assert response.status_code == 200
        tokens = response.json()
        
        # Use token
        response = await client.get(
            f"{settings.API_V1_STR}/auth/me",
            headers={"Authorization": f"Bearer {tokens['access_token']}"}
        )
        assert response.status_code == 200


@pytest.mark.integration  
class TestAuthIntegrationCI:
    """Integration tests for CI/CD"""

    @pytest.mark.asyncio
    async def test_database_migration_state_valid(self, db_session: AsyncSession):
        """Test database is in expected state (post-migration validation)"""
        from sqlalchemy import text
        
        # Basic connectivity
        result = await db_session.execute(text("SELECT 1"))
        assert result.scalar() == 1
        
        # Users table accessible
        result = await db_session.execute(text("SELECT COUNT(*) FROM users"))
        count = result.scalar()
        assert count is not None

        # UserRole enum exists and usable
        result = await db_session.execute(text("""
            SELECT COUNT(*) FROM pg_type WHERE typname = 'userrole'
        """))
        assert result.scalar() > 0

    @pytest.mark.asyncio
    async def test_environment_configuration_valid(self):
        """Test required configuration is present"""
        assert settings.SECRET_KEY is not None
        assert len(settings.SECRET_KEY) >= 32
        assert settings.ALGORITHM in ["HS256", "RS256", "ES256"]
        assert settings.ACCESS_TOKEN_EXPIRE_MINUTES > 0

    @pytest.mark.asyncio 
    async def test_development_bypass_token_if_configured(self, client: AsyncClient, db_session: AsyncSession):
        """Test development bypass token works if configured"""
        # Only test if dev environment and bypass token configured
        if settings.ENVIRONMENT not in ("development", "dev") or not settings.DEV_BYPASS_TOKEN:
            pytest.skip("Development bypass not configured")
        
        # Should be able to use dev bypass token
        response = await client.get(
            f"{settings.API_V1_STR}/auth/me",
            headers={"Authorization": f"Bearer {settings.DEV_BYPASS_TOKEN}"}
        )
        
        # Should either work (200) or fail gracefully (401), not crash (500)
        assert response.status_code in [200, 401]


@pytest.mark.regression
class TestAuthRegressionCI:
    """Regression tests for CI/CD to prevent issues from returning"""

    @pytest.mark.asyncio
    async def test_enum_conflicts_resolved(self, db_session: AsyncSession):
        """Regression: Enum conflicts don't break user operations"""
        # This specific issue was fixed - ensure it doesn't return
        from sqlalchemy import text
        
        # Should not have duplicate enum types
        result = await db_session.execute(text("""
            SELECT typname, COUNT(*) as count
            FROM pg_type 
            WHERE typtype = 'e' 
            GROUP BY typname
            HAVING COUNT(*) > 1
        """))
        
        conflicts = result.fetchall()
        assert len(conflicts) == 0, f"Found enum conflicts: {conflicts}"

    @pytest.mark.asyncio
    async def test_user_crud_operations_stable(self, db_session: AsyncSession):
        """Regression: User CRUD operations work reliably"""
        from app.crud.user import user as user_crud
        
        # Should be able to create user without issues
        user_data = {
            "email": "<EMAIL>",
            "username": "regression",
            "password": "password123",
            "full_name": "Regression Test",
            "role": UserRole.USER
        }
        
        user = await user_crud.create(db_session, obj_in=user_data)
        assert user.email == user_data["email"]
        
        # Should be able to authenticate
        auth_user = await user_crud.authenticate(
            db_session,
            username=user_data["email"],
            password=user_data["password"]
        )
        assert auth_user is not None