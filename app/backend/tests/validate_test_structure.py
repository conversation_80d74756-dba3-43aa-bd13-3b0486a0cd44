#!/usr/bin/env python3
"""
Validate AI Service Manager Test Structure
Simple validation that doesn't require full dependencies
"""
import sys
import ast
import os
from pathlib import Path

def validate_test_file_structure(file_path):
    """Validate a test file's structure"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Parse the AST to analyze the file
        tree = ast.parse(content)
        
        classes = []
        functions = []
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes.append(node.name)
            elif isinstance(node, ast.FunctionDef) and node.name.startswith('test_'):
                functions.append(node.name)
            elif isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                imports.append(f"{node.module}.{node.names[0].name}" if node.names else node.module)
        
        return {
            'file': file_path.name,
            'classes': len(classes),
            'test_functions': len(functions),
            'imports': len(set(imports)),
            'valid': True,
            'details': {
                'class_names': classes[:5],  # First 5 classes
                'test_functions': functions[:10]  # First 10 test functions
            }
        }
    
    except Exception as e:
        return {
            'file': file_path.name,
            'valid': False,
            'error': str(e)
        }

def main():
    """Main validation function"""
    print("🔍 Validating AI Service Manager Test Structure")
    print("=" * 60)
    
    # Find test files
    test_dir = Path(__file__).parent
    test_files = [
        test_dir / "unit" / "test_ai_service_manager.py",
        test_dir / "unit" / "test_ai_service_manager_caching.py", 
        test_dir / "unit" / "test_ai_service_manager_errors.py",
        test_dir / "unit" / "test_ai_service_manager_health.py",
        test_dir / "integration" / "test_ai_service_manager_integration.py"
    ]
    
    total_classes = 0
    total_functions = 0
    valid_files = 0
    
    for test_file in test_files:
        if test_file.exists():
            result = validate_test_file_structure(test_file)
            
            if result['valid']:
                print(f"\n✅ {result['file']}")
                print(f"   📋 Classes: {result['classes']}")
                print(f"   🧪 Test Functions: {result['test_functions']}")
                print(f"   📦 Imports: {result['imports']}")
                
                if result['details']['class_names']:
                    print(f"   🏷️  Sample Classes: {', '.join(result['details']['class_names'])}")
                
                total_classes += result['classes']
                total_functions += result['test_functions']
                valid_files += 1
            else:
                print(f"\n❌ {result['file']}")
                print(f"   Error: {result['error']}")
        else:
            print(f"\n⚠️  {test_file.name} - File not found")
    
    print(f"\n📊 Test Suite Summary:")
    print(f"   Valid Files: {valid_files}/{len(test_files)}")
    print(f"   Total Test Classes: {total_classes}")
    print(f"   Total Test Functions: {total_functions}")
    
    # Validate test runner
    test_runner = test_dir / "run_ai_service_manager_tests.py"
    if test_runner.exists():
        print(f"   ✅ Test Runner: {test_runner.name}")
    else:
        print(f"   ❌ Test Runner: Not found")
    
    # Check if tests would be discoverable by pytest
    print(f"\n🔍 Pytest Discovery Validation:")
    
    patterns = [
        "test_*.py",
        "*_test.py", 
        "Test*",
        "test*"
    ]
    
    discovered = 0
    for pattern in patterns:
        if pattern == "test_*.py":
            # Count files matching test_*.py pattern
            matching = [f for f in test_files if f.name.startswith('test_') and f.exists()]
            discovered += len(matching)
            print(f"   📁 {pattern}: {len(matching)} files")
    
    if total_functions > 0:
        print(f"\n✅ Test suite structure is valid!")
        print(f"   🎯 Ready for execution with {total_functions} test functions")
    else:
        print(f"\n❌ No test functions found!")
    
    return 0 if valid_files == len(test_files) and total_functions > 0 else 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)