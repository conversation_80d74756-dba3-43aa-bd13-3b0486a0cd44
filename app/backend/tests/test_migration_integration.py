"""
Integration tests for the complete database migration workflow
Tests the full migration process from reset to validation
"""
import asyncio
import pytest
import subprocess
import os
import tempfile
from pathlib import Path
from unittest.mock import patch, MagicMock
from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine

from app.core.database import async_engine
from app.core.config import settings


class TestFullMigrationWorkflow:
    """Test the complete migration workflow integration"""

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_migration_validation_workflow(self):
        """Test that we can run the validation workflow end-to-end"""
        from pathlib import Path
        import sys
        
        # Add scripts to path
        scripts_path = Path(__file__).parent.parent.parent / "scripts" / "database"
        sys.path.insert(0, str(scripts_path))
        
        try:
            import validate_migration
            
            # Run the main validation function
            result = await validate_migration.main()
            
            # Should return 0 for success, 1 for failure
            assert result in [0, 1], f"Validation should return 0 or 1, got {result}"
            
            # If it returns 0, the migration is valid
            if result == 0:
                print("✅ Migration validation passed")
            else:
                print("❌ Migration validation failed (this may be expected in test env)")
                
        except ImportError as e:
            pytest.skip(f"Could not import validation script: {e}")
        finally:
            # Clean up path
            if str(scripts_path) in sys.path:
                sys.path.remove(str(scripts_path))

    @pytest.mark.integration
    def test_shell_script_executable(self):
        """Test that the full reset shell script is executable and has valid structure"""
        script_path = Path(__file__).parent.parent.parent / "scripts" / "database" / "full_reset.sh"
        
        # Check file exists
        assert script_path.exists(), "full_reset.sh script not found"
        
        # Check basic shell script syntax
        content = script_path.read_text()
        
        # Basic syntax validation
        assert content.startswith('#!/bin/bash'), "Should have bash shebang"
        assert 'set -e' in content, "Should have error exit mode"
        
        # Environment safety check
        assert 'ENVIRONMENT' in content, "Should check environment variable"
        assert 'development' in content.lower(), "Should restrict to development"
        
        # Step-by-step execution
        steps = ['Step 1:', 'Step 2:', 'Step 3:', 'Step 4:', 'Step 5:', 'Step 6:', 'Step 7:']
        for step in steps:
            assert step in content, f"Missing execution step: {step}"

    @pytest.mark.integration
    def test_migration_file_structure(self):
        """Test that the migration file has correct structure and content"""
        migration_path = Path(__file__).parent.parent / "alembic" / "versions" / "20250828_000000_initial_reset_migration.py"
        
        assert migration_path.exists(), "Migration file does not exist"
        
        content = migration_path.read_text()
        
        # Check Alembic metadata
        assert "revision = '20250828000000'" in content
        assert "down_revision = None" in content
        assert "branch_labels = None" in content
        
        # Check required functions
        assert "def upgrade() -> None:" in content
        assert "def downgrade() -> None:" in content
        
        # Check extension creation
        extensions = ['vector', 'pg_trgm', 'uuid-ossp']
        for ext in extensions:
            assert f'CREATE EXTENSION IF NOT EXISTS {ext}' in content or f'CREATE EXTENSION IF NOT EXISTS "{ext}"' in content
        
        # Check enum creation
        critical_enums = ['userrole', 'datapermission', 'candidatestatus', 'positionstatus']
        for enum_name in critical_enums:
            assert enum_name in content.lower(), f"Missing enum: {enum_name}"
        
        # Check table creation
        critical_tables = ['users', 'candidates', 'positions', 'questionnaires']
        for table in critical_tables:
            assert f"create_table('{table}'" in content, f"Missing table creation: {table}"

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_database_state_consistency(self):
        """Test that database state is consistent after migration"""
        async with async_engine.begin() as conn:
            # Test 1: All expected tables exist
            expected_tables = [
                'users', 'user_preferences', 'positions', 'candidates',
                'questionnaires', 'questionnaire_sections', 'questions',
                'questionnaire_responses', 'answers', 'monitoring_snapshots',
                'job_vectors', 'resume_vectors', 'resume_files', 'candidate_assessments',
                'alembic_version'
            ]
            
            for table in expected_tables:
                result = await conn.execute(text(
                    f"SELECT COUNT(*) FROM information_schema.tables WHERE table_name = '{table}'"
                ))
                count = result.scalar()
                assert count == 1, f"Table {table} not found or duplicated"
            
            # Test 2: All expected enums exist and have no conflicts
            expected_enums = [
                'userrole', 'datapermission', 'candidatestatus', 'positionstatus',
                'questiontype', 'servicestatus'
            ]
            
            for enum_name in expected_enums:
                result = await conn.execute(text(
                    f"SELECT COUNT(*) FROM pg_type WHERE typname = '{enum_name}'"
                ))
                count = result.scalar()
                assert count == 1, f"Enum {enum_name} not found or duplicated (count: {count})"
            
            # Test 3: Foreign key constraints are properly established
            result = await conn.execute(text("""
                SELECT COUNT(*) FROM information_schema.table_constraints 
                WHERE constraint_type = 'FOREIGN KEY'
            """))
            fk_count = result.scalar()
            assert fk_count > 10, f"Expected at least 10 foreign keys, found {fk_count}"
            
            # Test 4: Alembic version is correct
            result = await conn.execute(text("SELECT version_num FROM alembic_version"))
            version = result.scalar()
            assert version == '20250828000000', f"Wrong Alembic version: {version}"

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_migration_rollback_capability(self):
        """Test that migration can be rolled back (downgrade function works)"""
        # This test verifies the downgrade function exists and has proper structure
        migration_path = Path(__file__).parent.parent / "alembic" / "versions" / "20250828_000000_initial_reset_migration.py"
        content = migration_path.read_text()
        
        # Check downgrade function structure
        assert "def downgrade() -> None:" in content
        
        # Check that downgrade drops tables in correct order
        assert "drop_table" in content.lower()
        
        # Check that downgrade drops enum types
        assert "DROP TYPE IF EXISTS" in content or "drop_type" in content.lower()
        
        # Note: We don't actually run the downgrade to avoid breaking the test database


class TestMigrationSafetyIntegration:
    """Test safety mechanisms across the migration system"""

    def test_environment_protection_integration(self):
        """Test that environment protection works across all scripts"""
        # This test ensures that production environments are protected
        
        # Test shell script environment check
        script_path = Path(__file__).parent.parent.parent / "scripts" / "database" / "full_reset.sh"
        if script_path.exists():
            content = script_path.read_text()
            
            # Should have environment check
            assert 'ENVIRONMENT' in content
            assert 'development' in content.lower()
            assert 'production' in content.lower()
            
            # Should exit on non-development
            assert 'exit 1' in content

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_data_preservation_check(self):
        """Test that migration preserves existing data structure expectations"""
        async with async_engine.begin() as conn:
            # Test that we can insert basic data into key tables
            try:
                # Test user insertion with enum
                await conn.execute(text("""
                    INSERT INTO users (id, email, username, hashed_password, full_name, role)
                    VALUES (888888, '<EMAIL>', 'integration_test', 'hash', 'Integration Test', 'HR_SPECIALIST')
                    ON CONFLICT (id) DO UPDATE SET email = EXCLUDED.email
                """))
                
                # Test candidate insertion with enum
                await conn.execute(text("""
                    INSERT INTO candidates (id, name, created_by, status)
                    VALUES (888888, 'Test Candidate', 888888, 'new')
                    ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name
                """))
                
                # Test position insertion with enum
                await conn.execute(text("""
                    INSERT INTO positions (id, title, created_by, status, urgency)
                    VALUES (888888, 'Test Position', 888888, 'draft', 'normal')
                    ON CONFLICT (id) DO UPDATE SET title = EXCLUDED.title
                """))
                
                # Verify insertions worked
                result = await conn.execute(text("SELECT COUNT(*) FROM users WHERE id = 888888"))
                assert result.scalar() == 1
                
                result = await conn.execute(text("SELECT COUNT(*) FROM candidates WHERE id = 888888"))
                assert result.scalar() == 1
                
                result = await conn.execute(text("SELECT COUNT(*) FROM positions WHERE id = 888888"))
                assert result.scalar() == 1
                
                # Clean up
                await conn.execute(text("DELETE FROM positions WHERE id = 888888"))
                await conn.execute(text("DELETE FROM candidates WHERE id = 888888"))
                await conn.execute(text("DELETE FROM users WHERE id = 888888"))
                
            except Exception as e:
                pytest.fail(f"Failed to insert test data: {e}")

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_concurrent_access_safety(self):
        """Test that database can handle concurrent access after migration"""
        async def test_query():
            async with async_engine.begin() as conn:
                result = await conn.execute(text("SELECT COUNT(*) FROM users"))
                return result.scalar()
        
        # Run multiple concurrent queries
        tasks = [test_query() for _ in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should succeed (no exceptions)
        for result in results:
            assert not isinstance(result, Exception), f"Concurrent query failed: {result}"
            assert isinstance(result, int), f"Expected int count, got {type(result)}"


class TestMigrationPerformanceIntegration:
    """Test performance aspects of the migration system"""

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_query_performance_after_migration(self):
        """Test that basic queries perform well after migration"""
        import time
        
        async with async_engine.begin() as conn:
            # Test performance of key queries
            queries_to_test = [
                "SELECT COUNT(*) FROM users",
                "SELECT COUNT(*) FROM candidates",
                "SELECT COUNT(*) FROM positions",
                "SELECT COUNT(*) FROM questionnaires"
            ]
            
            for query in queries_to_test:
                start_time = time.time()
                result = await conn.execute(text(query))
                count = result.scalar()
                end_time = time.time()
                
                query_time = end_time - start_time
                assert query_time < 1.0, f"Query took too long ({query_time:.2f}s): {query}"
                assert isinstance(count, int), f"Query should return count: {query}"

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_index_usage_after_migration(self):
        """Test that indexes are being used effectively"""
        async with async_engine.begin() as conn:
            # Test that common queries use indexes
            # This is done by checking query execution plans
            
            test_queries = [
                "SELECT * FROM users WHERE email = '<EMAIL>'",
                "SELECT * FROM candidates WHERE status = 'new'",
                "SELECT * FROM positions WHERE status = 'open'"
            ]
            
            for query in test_queries:
                # Get query plan
                explain_query = f"EXPLAIN {query}"
                result = await conn.execute(text(explain_query))
                plan = result.fetchall()
                
                # Convert plan to string for analysis
                plan_str = ' '.join([str(row) for row in plan])
                
                # Should not use sequential scans for indexed columns (in most cases)
                # This is a basic heuristic - actual optimization would depend on data volume
                assert len(plan) > 0, f"No execution plan returned for: {query}"


class TestMigrationRegressionPrevention:
    """Test that specific regression issues are prevented"""

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_no_enum_duplication_regression(self):
        """Test that the original enum duplication issue doesn't recur"""
        async with async_engine.begin() as conn:
            # Check that no enum types are duplicated
            result = await conn.execute(text("""
                SELECT typname, COUNT(*) as count
                FROM pg_type 
                WHERE typtype = 'e'
                GROUP BY typname
                HAVING COUNT(*) > 1
            """))
            
            duplicates = result.fetchall()
            assert len(duplicates) == 0, f"Found duplicate enum types: {duplicates}"
            
            # Specifically check the enums that were problematic
            problematic_enums = ['userrole', 'datapermission', 'candidatestatus']
            
            for enum_name in problematic_enums:
                result = await conn.execute(text(
                    f"SELECT COUNT(*) FROM pg_type WHERE typname = '{enum_name}'"
                ))
                count = result.scalar()
                assert count == 1, f"Enum {enum_name} should exist exactly once, found {count}"

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_migration_idempotency_regression(self):
        """Test that migration operations are idempotent (can be run multiple times)"""
        async with async_engine.begin() as conn:
            # Test that key operations can be repeated safely
            
            # Try to create extension again (should not fail)
            await conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            
            # Verify extension still exists
            result = await conn.execute(text(
                "SELECT extname FROM pg_extension WHERE extname = 'vector'"
            ))
            assert result.fetchone() is not None
            
            # Test enum creation idempotency (concept test)
            # In practice, this would test the DO $$ BEGIN ... EXCEPTION pattern
            # from the migration file

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_foreign_key_consistency_regression(self):
        """Test that foreign key relationships are maintained correctly"""
        async with async_engine.begin() as conn:
            # Test critical foreign key relationships
            critical_relationships = [
                ('user_preferences', 'users', 'user_id'),
                ('candidates', 'users', 'created_by'),
                ('positions', 'users', 'created_by'),
                ('questionnaire_responses', 'questionnaires', 'questionnaire_id')
            ]
            
            for child_table, parent_table, fk_column in critical_relationships:
                # Check that foreign key constraint exists
                result = await conn.execute(text(f"""
                    SELECT tc.constraint_name
                    FROM information_schema.table_constraints AS tc 
                    JOIN information_schema.key_column_usage AS kcu
                      ON tc.constraint_name = kcu.constraint_name
                    JOIN information_schema.constraint_column_usage AS ccu
                      ON ccu.constraint_name = tc.constraint_name
                    WHERE tc.constraint_type = 'FOREIGN KEY' 
                      AND tc.table_name = '{child_table}'
                      AND kcu.column_name = '{fk_column}'
                      AND ccu.table_name = '{parent_table}'
                """))
                
                constraint = result.fetchone()
                assert constraint is not None, f"Missing FK constraint: {child_table}.{fk_column} -> {parent_table}"


if __name__ == "__main__":
    # Allow running integration tests directly
    pytest.main([__file__, "-v", "-m", "integration"])