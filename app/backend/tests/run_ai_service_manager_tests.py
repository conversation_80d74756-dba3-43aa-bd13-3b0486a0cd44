#!/usr/bin/env python3
"""
Test Runner for AI Service Manager Comprehensive Test Suite

Runs all AI service manager tests with proper configuration and reporting
"""
import sys
import pytest
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root / "app" / "backend"))

def run_ai_service_manager_tests():
    """Run all AI service manager tests with comprehensive reporting"""
    
    # Test files to run
    test_files = [
        "tests/unit/test_ai_service_manager.py",
        "tests/unit/test_ai_service_manager_caching.py", 
        "tests/unit/test_ai_service_manager_errors.py",
        "tests/unit/test_ai_service_manager_health.py",
        "tests/integration/test_ai_service_manager_integration.py"
    ]
    
    print("🧪 Running AI Service Manager Comprehensive Test Suite")
    print("=" * 60)
    
    # pytest configuration
    pytest_args = [
        "-v",  # Verbose output
        "--tb=short",  # Short traceback format
        "--strict-markers",  # Strict marker validation
        "--asyncio-mode=auto",  # Auto async mode
        "-x",  # Stop on first failure (optional)
        "--durations=10",  # Show 10 slowest tests
        "--cov=app.services.ai_service_manager",  # Coverage for the main module
        "--cov-report=term-missing",  # Show missing lines
        "--cov-report=html:htmlcov/ai_service_manager",  # HTML coverage report
    ] + test_files
    
    # Add markers for different test categories
    print("\n📋 Test Categories:")
    print("  • Unit Tests: Core functionality, caching, error handling, health monitoring")
    print("  • Integration Tests: End-to-end workflows, Redis integration, performance")
    print("  • Stress Tests: High concurrency, sustained load (marked as @pytest.mark.slow)")
    print("  • Redis Tests: Caching functionality (marked as @pytest.mark.redis_required)")
    
    print(f"\n🚀 Running {len(test_files)} test files...")
    print("   Use -m 'not slow' to skip stress tests")
    print("   Use -m 'not redis_required' to skip Redis-dependent tests")
    print()
    
    # Run the tests
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("\n✅ All AI Service Manager tests passed!")
        print("\n📊 Coverage Report Generated:")
        print("   • Terminal: See above coverage summary")
        print("   • HTML: htmlcov/ai_service_manager/index.html")
    else:
        print(f"\n❌ Tests failed with exit code: {exit_code}")
        print("\n🔍 Troubleshooting Tips:")
        print("   • Check that Redis is running for caching tests")
        print("   • Verify AI configuration settings are properly mocked")
        print("   • Review test output for specific failure details")
    
    return exit_code


def run_specific_test_category():
    """Run specific categories of tests"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run specific AI Service Manager test categories")
    parser.add_argument("--unit", action="store_true", help="Run only unit tests")
    parser.add_argument("--integration", action="store_true", help="Run only integration tests")
    parser.add_argument("--caching", action="store_true", help="Run only caching tests")
    parser.add_argument("--errors", action="store_true", help="Run only error handling tests")
    parser.add_argument("--health", action="store_true", help="Run only health monitoring tests")
    parser.add_argument("--fast", action="store_true", help="Skip slow/stress tests")
    parser.add_argument("--no-redis", action="store_true", help="Skip Redis-dependent tests")
    
    args = parser.parse_args()
    
    test_files = []
    markers = []
    
    # Select test files based on arguments
    if args.unit:
        test_files.append("tests/unit/test_ai_service_manager.py")
    if args.caching:
        test_files.append("tests/unit/test_ai_service_manager_caching.py")
    if args.errors:
        test_files.append("tests/unit/test_ai_service_manager_errors.py")
    if args.health:
        test_files.append("tests/unit/test_ai_service_manager_health.py")
    if args.integration:
        test_files.append("tests/integration/test_ai_service_manager_integration.py")
    
    # If no specific category selected, run all
    if not test_files:
        test_files = [
            "tests/unit/test_ai_service_manager.py",
            "tests/unit/test_ai_service_manager_caching.py",
            "tests/unit/test_ai_service_manager_errors.py", 
            "tests/unit/test_ai_service_manager_health.py",
            "tests/integration/test_ai_service_manager_integration.py"
        ]
    
    # Add markers based on arguments
    if args.fast:
        markers.append("not slow")
    if args.no_redis:
        markers.append("not redis_required")
    
    # Build pytest arguments
    pytest_args = [
        "-v",
        "--tb=short",
        "--asyncio-mode=auto",
    ]
    
    if markers:
        pytest_args.extend(["-m", " and ".join(markers)])
    
    pytest_args.extend(test_files)
    
    print(f"🧪 Running selected AI Service Manager tests:")
    for test_file in test_files:
        print(f"   • {test_file}")
    
    if markers:
        print(f"📋 Markers: {' and '.join(markers)}")
    
    exit_code = pytest.main(pytest_args)
    return exit_code


def run_performance_benchmarks():
    """Run performance benchmarks for AI Service Manager"""
    
    print("📊 Running AI Service Manager Performance Benchmarks")
    print("=" * 50)
    
    pytest_args = [
        "-v",
        "--tb=short", 
        "--asyncio-mode=auto",
        "-m", "performance or slow",  # Run performance and stress tests
        "--durations=0",  # Show all test durations
        "tests/unit/test_ai_service_manager_caching.py::TestCachePerformanceImprovement",
        "tests/integration/test_ai_service_manager_integration.py::TestAIServiceManagerStressIntegration"
    ]
    
    exit_code = pytest.main(pytest_args)
    
    if exit_code == 0:
        print("\n✅ Performance benchmarks completed!")
    else:
        print(f"\n❌ Benchmarks failed with exit code: {exit_code}")
    
    return exit_code


def main():
    """Main entry point"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "specific":
            sys.argv.pop(1)  # Remove 'specific' from args
            return run_specific_test_category()
        elif sys.argv[1] == "performance":
            return run_performance_benchmarks()
    
    return run_ai_service_manager_tests()


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)