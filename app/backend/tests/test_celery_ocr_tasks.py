"""
Tests for Celery tasks with OCR integration

This module tests:
- parse_resume_async task with OCR parameters
- batch_resume_parsing task with OCR control
- Task parameter propagation and validation
- Error handling and retry mechanisms
- Task result metadata with OCR information
"""

import base64
import json
import pytest
from unittest.mock import patch, Mock, AsyncMock
from datetime import datetime

from app.tasks import parse_resume_async, batch_resume_parsing
from app.services.resume_parser import resume_parser
from app.services.ocr_service import ocr_service


class TestParseResumeAsyncTask:
    """Test parse_resume_async Celery task with OCR integration"""
    
    def test_parse_resume_async_ocr_enabled(self):
        """Test parse_resume_async task with OCR enabled"""
        file_content = b"test resume content"
        file_content_b64 = base64.b64encode(file_content).decode()
        
        mock_result = {
            "candidate_id": 456,
            "action": "created",
            "parsing_result": {
                "confidence_score": 0.89,
                "ocr_enabled": True,
                "text_extraction_method": "pdf_ocr_enhanced",
                "fields_extracted": 9
            },
            "candidate_data": {
                "name": "OCR Test User",
                "email": "<EMAIL>"
            },
            "embeddings": {
                "embeddings_generated": True
            }
        }
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = mock_result
            
            # Create mock task request
            mock_task = Mock()
            mock_task.request.id = "test-task-123"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            
            result = parse_resume_async(
                mock_task,
                file_content_base64=file_content_b64,
                filename="test_resume.pdf",
                user_id=1,
                candidate_id=None,
                generate_embeddings=True,
                embedding_provider=None,
                enable_ocr=True
            )
            
            # Verify task result
            assert result["status"] == "completed"
            assert result["task_id"] == "test-task-123"
            assert result["result"]["candidate_id"] == 456
            assert result["result"]["parsing_result"]["ocr_enabled"] == True
            
            # Verify service was called with OCR enabled
            mock_parse.assert_called_once()
            call_args = mock_parse.call_args
            assert call_args.kwargs["enable_ocr"] == True
    
    def test_parse_resume_async_ocr_disabled(self):
        """Test parse_resume_async task with OCR disabled"""
        file_content = b"test resume content"
        file_content_b64 = base64.b64encode(file_content).decode()
        
        mock_result = {
            "candidate_id": 457,
            "action": "created",
            "parsing_result": {
                "confidence_score": 0.91,
                "ocr_enabled": False,
                "text_extraction_method": "pdf_text_only",
                "fields_extracted": 7
            },
            "candidate_data": {
                "name": "No OCR Test User",
                "email": "<EMAIL>"
            }
        }
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = mock_result
            
            mock_task = Mock()
            mock_task.request.id = "test-task-124"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            
            result = parse_resume_async(
                mock_task,
                file_content_base64=file_content_b64,
                filename="text_resume.pdf",
                user_id=1,
                enable_ocr=False
            )
            
            assert result["status"] == "completed"
            assert result["result"]["parsing_result"]["ocr_enabled"] == False
            assert result["result"]["parsing_result"]["text_extraction_method"] == "pdf_text_only"
            
            # Verify service was called with OCR disabled
            mock_parse.assert_called_once()
            call_args = mock_parse.call_args
            assert call_args.kwargs["enable_ocr"] == False
    
    def test_parse_resume_async_default_ocr_enabled(self):
        """Test that OCR is enabled by default when not specified"""
        file_content_b64 = base64.b64encode(b"test content").decode()
        
        mock_result = {
            "candidate_id": 458,
            "action": "created",
            "parsing_result": {
                "ocr_enabled": True,
                "text_extraction_method": "image_ocr"
            }
        }
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = mock_result
            
            mock_task = Mock()
            mock_task.request.id = "test-task-125"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            
            # Call without enable_ocr parameter - should default to True
            result = parse_resume_async(
                mock_task,
                file_content_base64=file_content_b64,
                filename="default_test.png",
                user_id=1
            )
            
            assert result["status"] == "completed"
            
            # Verify service was called with OCR enabled by default
            mock_parse.assert_called_once()
            call_args = mock_parse.call_args
            assert call_args.kwargs["enable_ocr"] == True
    
    def test_parse_resume_async_with_ocr_failure_and_retry(self):
        """Test task retry mechanism when OCR processing fails"""
        file_content_b64 = base64.b64encode(b"problematic content").decode()
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            # Simulate OCR-related failure
            mock_parse.side_effect = Exception("OCR processing timeout")
            
            mock_task = Mock()
            mock_task.request.id = "test-task-126"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            mock_task.retry = Mock(side_effect=Exception("Retry triggered"))
            
            with pytest.raises(Exception, match="Retry triggered"):
                parse_resume_async(
                    mock_task,
                    file_content_base64=file_content_b64,
                    filename="problematic.pdf",
                    user_id=1,
                    enable_ocr=True
                )
            
            # Verify retry was triggered
            mock_task.retry.assert_called_once()
            retry_args = mock_task.retry.call_args
            assert retry_args.kwargs["countdown"] == 60  # First retry delay
    
    def test_parse_resume_async_max_retries_exceeded(self):
        """Test task behavior when max retries are exceeded"""
        file_content_b64 = base64.b64encode(b"failing content").decode()
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.side_effect = Exception("Persistent OCR failure")
            
            mock_task = Mock()
            mock_task.request.id = "test-task-127"
            mock_task.request.retries = 3  # At max retries
            mock_task.max_retries = 3
            
            result = parse_resume_async(
                mock_task,
                file_content_base64=file_content_b64,
                filename="failing.pdf",
                user_id=1,
                enable_ocr=True
            )
            
            # Should return failure result
            assert result["status"] == "failed"
            assert result["task_id"] == "test-task-127"
            assert "Persistent OCR failure" in result["error"]
            assert result["filename"] == "failing.pdf"


class TestBatchResumeParsingTask:
    """Test batch_resume_parsing Celery task with OCR integration"""
    
    def test_batch_resume_parsing_ocr_enabled(self):
        """Test batch resume parsing with OCR enabled"""
        resume_files_data = [
            {
                "file_content": base64.b64encode(b"resume 1 content").decode(),
                "filename": "resume1.pdf"
            },
            {
                "file_content": base64.b64encode(b"resume 2 content").decode(),
                "filename": "resume2.png"
            }
        ]
        
        mock_batch_results = [
            {
                "candidate_id": 501,
                "success": True,
                "parsing_result": {
                    "ocr_enabled": True,
                    "text_extraction_method": "pdf_ocr_enhanced"
                },
                "filename": "resume1.pdf"
            },
            {
                "candidate_id": 502,
                "success": True,
                "parsing_result": {
                    "ocr_enabled": True,
                    "text_extraction_method": "image_ocr"
                },
                "filename": "resume2.png"
            }
        ]
        
        with patch.object(resume_parser, 'batch_parse_resumes') as mock_batch:
            mock_batch.return_value = mock_batch_results
            
            mock_task = Mock()
            mock_task.request.id = "batch-task-201"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            
            result = batch_resume_parsing(
                mock_task,
                resume_files_data=resume_files_data,
                user_id=1,
                generate_embeddings=True,
                max_concurrent=2,
                enable_ocr=True
            )
            
            # Verify batch result
            assert result["status"] == "completed"
            assert result["task_id"] == "batch-task-201"
            assert result["summary"]["total_files"] == 2
            assert result["summary"]["successful"] == 2
            assert result["summary"]["failed"] == 0
            assert result["summary"]["success_rate"] == 100.0
            
            # Verify service was called with OCR enabled
            mock_batch.assert_called_once()
            call_args = mock_batch.call_args
            assert call_args.kwargs["enable_ocr"] == True
            assert call_args.kwargs["max_concurrent"] == 2
    
    def test_batch_resume_parsing_ocr_disabled(self):
        """Test batch resume parsing with OCR disabled"""
        resume_files_data = [
            {
                "file_content": base64.b64encode(b"text resume").decode(),
                "filename": "text_resume.pdf"
            }
        ]
        
        mock_batch_results = [
            {
                "candidate_id": 503,
                "success": True,
                "parsing_result": {
                    "ocr_enabled": False,
                    "text_extraction_method": "pdf_text_only"
                }
            }
        ]
        
        with patch.object(resume_parser, 'batch_parse_resumes') as mock_batch:
            mock_batch.return_value = mock_batch_results
            
            mock_task = Mock()
            mock_task.request.id = "batch-task-202"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            
            result = batch_resume_parsing(
                mock_task,
                resume_files_data=resume_files_data,
                user_id=1,
                enable_ocr=False
            )
            
            assert result["status"] == "completed"
            assert result["results"][0]["parsing_result"]["ocr_enabled"] == False
            
            # Verify service was called with OCR disabled
            mock_batch.assert_called_once()
            call_args = mock_batch.call_args
            assert call_args.kwargs["enable_ocr"] == False
    
    def test_batch_resume_parsing_mixed_success(self):
        """Test batch processing with mixed success/failure results"""
        resume_files_data = [
            {
                "file_content": base64.b64encode(b"good resume").decode(),
                "filename": "good.pdf"
            },
            {
                "file_content": base64.b64encode(b"bad resume").decode(),
                "filename": "bad.pdf"
            },
            {
                "file_content": base64.b64encode(b"another good").decode(),
                "filename": "good2.png"
            }
        ]
        
        mock_batch_results = [
            {
                "candidate_id": 504,
                "success": True,
                "parsing_result": {"ocr_enabled": True}
            },
            {
                "success": False,
                "error": "OCR processing failed",
                "filename": "bad.pdf"
            },
            {
                "candidate_id": 505,
                "success": True,
                "parsing_result": {"ocr_enabled": True}
            }
        ]
        
        with patch.object(resume_parser, 'batch_parse_resumes') as mock_batch:
            mock_batch.return_value = mock_batch_results
            
            mock_task = Mock()
            mock_task.request.id = "batch-task-203"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            
            result = batch_resume_parsing(
                mock_task,
                resume_files_data=resume_files_data,
                user_id=1,
                enable_ocr=True
            )
            
            assert result["status"] == "completed"
            assert result["summary"]["total_files"] == 3
            assert result["summary"]["successful"] == 2
            assert result["summary"]["failed"] == 1
            assert result["summary"]["success_rate"] == 66.67
    
    def test_batch_resume_parsing_complete_failure(self):
        """Test batch parsing when entire batch fails"""
        resume_files_data = [
            {
                "file_content": base64.b64encode(b"test").decode(),
                "filename": "test.pdf"
            }
        ]
        
        with patch.object(resume_parser, 'batch_parse_resumes') as mock_batch:
            mock_batch.side_effect = Exception("Batch processing system failure")
            
            mock_task = Mock()
            mock_task.request.id = "batch-task-204"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            mock_task.retry = Mock(side_effect=Exception("Retry triggered"))
            
            with pytest.raises(Exception, match="Retry triggered"):
                batch_resume_parsing(
                    mock_task,
                    resume_files_data=resume_files_data,
                    user_id=1,
                    enable_ocr=True
                )
            
            # Verify retry was triggered with exponential backoff
            mock_task.retry.assert_called_once()
            retry_args = mock_task.retry.call_args
            assert retry_args.kwargs["countdown"] == 120  # Base retry delay for batch


class TestTaskOCRParameterValidation:
    """Test OCR parameter validation in Celery tasks"""
    
    def test_parse_resume_async_invalid_base64(self):
        """Test handling of invalid base64 content"""
        invalid_b64 = "invalid-base64-content!@#"
        
        mock_task = Mock()
        mock_task.request.id = "test-task-300"
        mock_task.request.retries = 0
        mock_task.max_retries = 3
        
        result = parse_resume_async(
            mock_task,
            file_content_base64=invalid_b64,
            filename="invalid.pdf",
            user_id=1,
            enable_ocr=True
        )
        
        # Should handle base64 decode error gracefully
        assert result["status"] == "failed"
        assert "base64" in result["error"].lower() or "decode" in result["error"].lower()
    
    def test_batch_parsing_empty_files_list(self):
        """Test batch parsing with empty files list"""
        mock_task = Mock()
        mock_task.request.id = "batch-task-301"
        mock_task.request.retries = 0
        mock_task.max_retries = 3
        
        with patch.object(resume_parser, 'batch_parse_resumes') as mock_batch:
            mock_batch.return_value = []
            
            result = batch_resume_parsing(
                mock_task,
                resume_files_data=[],
                user_id=1,
                enable_ocr=True
            )
            
            assert result["status"] == "completed"
            assert result["summary"]["total_files"] == 0
            assert result["summary"]["success_rate"] == 0


class TestTaskMetricsAndMonitoring:
    """Test task metrics and monitoring with OCR information"""
    
    def test_task_result_metadata_includes_ocr_info(self):
        """Test that task results include OCR processing metadata"""
        file_content_b64 = base64.b64encode(b"test content").decode()
        
        mock_result = {
            "candidate_id": 600,
            "action": "created",
            "parsing_result": {
                "confidence_score": 0.85,
                "ocr_enabled": True,
                "text_extraction_method": "pdf_ocr_enhanced",
                "fields_extracted": 8,
                "processing_time_ms": 2150,
                "ocr_processing_time_ms": 1800,
                "ocr_confidence": 0.92,
                "ocr_method": "ocr_gpu"
            },
            "candidate_data": {
                "name": "Metrics Test"
            },
            "embeddings": {
                "embeddings_generated": True,
                "generation_time_ms": 450
            }
        }
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = mock_result
            
            mock_task = Mock()
            mock_task.request.id = "metrics-task-600"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            
            result = parse_resume_async(
                mock_task,
                file_content_base64=file_content_b64,
                filename="metrics_test.pdf",
                user_id=1,
                enable_ocr=True
            )
            
            # Verify OCR metadata is preserved in task result
            parsing_result = result["result"]["parsing_result"]
            assert parsing_result["ocr_enabled"] == True
            assert parsing_result["text_extraction_method"] == "pdf_ocr_enhanced"
            assert parsing_result["ocr_processing_time_ms"] == 1800
            assert parsing_result["ocr_confidence"] == 0.92
            assert parsing_result["ocr_method"] == "ocr_gpu"
    
    def test_task_timing_information(self):
        """Test that task includes timing information"""
        file_content_b64 = base64.b64encode(b"timing test").decode()
        
        mock_result = {
            "candidate_id": 601,
            "parsing_result": {"ocr_enabled": True}
        }
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = mock_result
            
            mock_task = Mock()
            mock_task.request.id = "timing-task-601"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            
            result = parse_resume_async(
                mock_task,
                file_content_base64=file_content_b64,
                filename="timing_test.pdf",
                user_id=1,
                enable_ocr=True
            )
            
            # Verify task completion timestamp is included
            assert "completed_at" in result
            assert result["completed_at"] is not None
            
            # Verify timestamp format
            completed_at = result["completed_at"]
            datetime.fromisoformat(completed_at)  # Should not raise exception


class TestTaskErrorRecovery:
    """Test task error recovery and fallback mechanisms"""
    
    def test_ocr_failure_with_graceful_degradation(self):
        """Test task continues when OCR fails but traditional parsing succeeds"""
        file_content_b64 = base64.b64encode(b"degraded test").decode()
        
        # Simulate OCR failure but successful fallback
        mock_result = {
            "candidate_id": 700,
            "action": "created",
            "parsing_result": {
                "confidence_score": 0.75,
                "ocr_enabled": True,
                "text_extraction_method": "pdf_text_fallback",
                "parsing_errors": ["OCR processing failed, used traditional extraction"],
                "ocr_failure_reason": "GPU memory insufficient"
            },
            "candidate_data": {
                "name": "Degraded Test"
            }
        }
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.return_value = mock_result
            
            mock_task = Mock()
            mock_task.request.id = "degraded-task-700"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            
            result = parse_resume_async(
                mock_task,
                file_content_base64=file_content_b64,
                filename="degraded.pdf",
                user_id=1,
                enable_ocr=True
            )
            
            # Task should succeed despite OCR failure
            assert result["status"] == "completed"
            assert result["result"]["parsing_result"]["text_extraction_method"] == "pdf_text_fallback"
            assert len(result["result"]["parsing_result"]["parsing_errors"]) > 0
    
    def test_complete_parsing_failure_handling(self):
        """Test task handling when both OCR and fallback parsing fail"""
        file_content_b64 = base64.b64encode(b"complete failure test").decode()
        
        with patch.object(resume_parser, 'parse_and_store_resume') as mock_parse:
            mock_parse.side_effect = Exception("Complete parsing failure - corrupted file")
            
            mock_task = Mock()
            mock_task.request.id = "failure-task-701"
            mock_task.request.retries = 0
            mock_task.max_retries = 3
            mock_task.retry = Mock(side_effect=Exception("Retry triggered"))
            
            with pytest.raises(Exception, match="Retry triggered"):
                parse_resume_async(
                    mock_task,
                    file_content_base64=file_content_b64,
                    filename="corrupted.pdf",
                    user_id=1,
                    enable_ocr=True
                )
            
            # Should trigger retry
            mock_task.retry.assert_called_once()


# Test fixtures and utilities
@pytest.fixture
def mock_celery_task():
    """Create a mock Celery task for testing"""
    task = Mock()
    task.request.id = "test-task-id"
    task.request.retries = 0
    task.max_retries = 3
    task.retry = Mock()
    return task


@pytest.fixture
def sample_resume_files_data():
    """Sample resume files data for batch testing"""
    return [
        {
            "file_content": base64.b64encode(b"PDF resume content").decode(),
            "filename": "resume1.pdf"
        },
        {
            "file_content": base64.b64encode(b"Image resume content").decode(),
            "filename": "resume2.png"
        },
        {
            "file_content": base64.b64encode(b"DOCX resume content").decode(),
            "filename": "resume3.docx"
        }
    ]


# Test markers
pytestmark = [
    pytest.mark.asyncio,
    pytest.mark.integration,
    pytest.mark.celery,
    pytest.mark.ocr
]