"""
Functional tests for logging improvements in candidate stats service
Tests that print statements were properly replaced with logger calls
"""
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timezone
import logging
from io import StringIO

from app.services.candidate_stats import CandidateStatsService


class TestLoggingImprovements:
    """Test logging improvements in candidate stats service"""
    
    @pytest.fixture
    def stats_service(self, db_session):
        """Create candidate stats service instance"""
        return CandidateStatsService(db_session)
    
    @pytest.fixture
    def log_capture(self):
        """Capture log output for testing"""
        log_stream = StringIO()
        handler = logging.StreamHandler(log_stream)
        handler.setLevel(logging.DEBUG)
        
        # Get the logger for candidate_stats module
        logger = logging.getLogger('app.services.candidate_stats')
        logger.addHandler(handler)
        logger.setLevel(logging.DEBUG)
        
        yield log_stream, logger
        
        # Cleanup
        logger.removeHandler(handler)
    
    @pytest.mark.asyncio
    async def test_cache_hit_logging(self, stats_service, test_user, log_capture):
        """Test that cache hits are properly logged with logger instead of print"""
        log_stream, logger = log_capture
        
        # Mock cache to return data (simulate cache hit)
        cache_data = {
            "total_candidates": 10,
            "active_candidates": 5,
            "new_this_week": 2,
            "new_this_month": 8,
            "avg_match_score": 75.5,
            "with_resume": 6,
            "without_resume": 4,
            "by_status": {"active": 8, "inactive": 2}
        }
        
        with patch.object(stats_service, '_get_cache', return_value=cache_data):
            # Call the method that should log cache hit
            result = await stats_service.get_overview_stats(test_user)
            
            # Verify the result
            assert result == cache_data
            
            # Check that cache hit was logged
            log_output = log_stream.getvalue()
            assert "Cache hit for overview stats" in log_output
            assert f"user {test_user.id}" in log_output
            
            # Verify log level is DEBUG
            assert "DEBUG" in log_output or logger.level <= logging.DEBUG
    
    @pytest.mark.asyncio
    async def test_database_error_logging(self, stats_service, test_user, log_capture):
        """Test that database errors are properly logged"""
        log_stream, logger = log_capture
        
        # Mock database session to raise exception
        mock_session = AsyncMock()
        mock_session.execute = AsyncMock(side_effect=Exception("Database connection lost"))
        mock_session.scalar = AsyncMock(side_effect=Exception("Database connection lost"))
        
        stats_service.db = mock_session
        
        # Mock cache to return None (no cache)
        with patch.object(stats_service, '_get_cache', return_value=None):
            with patch.object(stats_service, '_set_cache'):
                # Call method that should handle database error
                result = await stats_service.get_overview_stats(test_user)
                
                # Should return empty stats on error
                assert result["total_candidates"] == 0
                assert result["active_candidates"] == 0
                
                # Check that error was logged
                log_output = log_stream.getvalue()
                assert "Failed to get overview stats" in log_output
                assert "Database connection lost" in log_output or "ERROR" in log_output
    
    @pytest.mark.asyncio
    async def test_cache_operation_error_logging(self, stats_service, test_user, log_capture):
        """Test that cache operation errors are properly logged as warnings"""
        log_stream, logger = log_capture
        
        # Mock cache operations to fail
        with patch.object(stats_service, '_get_redis', side_effect=Exception("Redis connection failed")):
            # This should trigger cache get failure and warning log
            cache_result = await stats_service._get_cache("test_key")
            assert cache_result is None
            
            # Check warning was logged
            log_output = log_stream.getvalue()
            assert "Cache get failed" in log_output
            assert "Redis connection failed" in log_output
    
    @pytest.mark.asyncio 
    async def test_specific_query_error_logging(self, stats_service, test_user, log_capture):
        """Test that specific database query errors are logged with context"""
        log_stream, logger = log_capture
        
        # Create a mock session that fails on specific queries
        mock_session = AsyncMock()
        
        # Mock total candidates query to fail
        def mock_execute(query):
            query_str = str(query)
            if "func.count(Candidate.id)" in query_str and "is_deleted" in query_str:
                raise Exception("Total candidates query failed")
            return AsyncMock(scalar=AsyncMock(return_value=0))
        
        mock_session.execute = AsyncMock(side_effect=mock_execute)
        stats_service.db = mock_session
        
        # Mock cache and candidate_crud to isolate the logging
        with patch.object(stats_service, '_get_cache', return_value=None):
            with patch.object(stats_service, '_set_cache'):
                with patch('app.services.candidate_stats.candidate_crud.get_active_candidates_count', return_value=0):
                    # Call method
                    result = await stats_service.get_overview_stats(test_user)
                    
                    # Check that specific query error was logged
                    log_output = log_stream.getvalue()
                    assert "Total candidates query failed" in log_output
    
    @pytest.mark.asyncio
    async def test_skill_distribution_error_logging(self, stats_service, test_user, log_capture):
        """Test error logging in skill distribution functionality"""
        log_stream, logger = log_capture
        
        # Mock database to fail
        mock_session = AsyncMock()
        mock_session.execute = AsyncMock(side_effect=Exception("Skills query failed"))
        stats_service.db = mock_session
        
        # Mock cache to return None
        with patch.object(stats_service, '_get_cache', return_value=None):
            result = await stats_service.get_skill_distribution(test_user)
            
            # Should return empty list on error
            assert result == []
            
            # Check error logging
            log_output = log_stream.getvalue()
            assert "Failed to get skill distribution" in log_output
            assert "Skills query failed" in log_output
    
    @pytest.mark.asyncio
    async def test_multiple_error_scenarios_logging(self, stats_service, test_user, log_capture):
        """Test logging in multiple error scenarios"""
        log_stream, logger = log_capture
        
        # Test various service methods that should log errors
        error_methods = [
            ('get_experience_distribution', "Failed to get experience distribution"),
            ('get_education_distribution', "Failed to get education distribution"),
            ('get_salary_distribution', "Failed to get salary distribution"),
            ('get_source_distribution', "Failed to get source distribution"),
            ('get_department_distribution', "Failed to get department distribution"),
            ('get_activity_trend', "Failed to get activity trend")
        ]
        
        # Mock database to fail for all methods
        mock_session = AsyncMock()
        mock_session.execute = AsyncMock(side_effect=Exception("Database error"))
        stats_service.db = mock_session
        
        for method_name, expected_log_message in error_methods:
            # Clear previous log output
            log_stream.seek(0)
            log_stream.truncate(0)
            
            # Mock cache to return None
            with patch.object(stats_service, '_get_cache', return_value=None):
                # Call the method
                method = getattr(stats_service, method_name)
                if method_name == 'get_activity_trend':
                    result = await method(test_user, days=30)
                else:
                    result = await method(test_user)
                
                # Should return empty result
                assert result == []
                
                # Check error logging
                log_output = log_stream.getvalue()
                assert expected_log_message in log_output
                assert "Database error" in log_output
    
    @pytest.mark.asyncio
    async def test_warning_vs_error_log_levels(self, stats_service, test_user, log_capture):
        """Test that warnings vs errors are logged at appropriate levels"""
        log_stream, logger = log_capture
        
        # Test cache warning (should be WARNING level)
        with patch.object(stats_service, '_get_redis', side_effect=Exception("Redis timeout")):
            await stats_service._get_cache("test_key")
            
            log_output = log_stream.getvalue()
            assert "Cache get failed" in log_output
            # Should be logged as warning, not error
            
        # Clear log
        log_stream.seek(0)
        log_stream.truncate(0)
        
        # Test database error (should be ERROR level)
        mock_session = AsyncMock() 
        mock_session.execute = AsyncMock(side_effect=Exception("Critical database error"))
        stats_service.db = mock_session
        
        with patch.object(stats_service, '_get_cache', return_value=None):
            with patch.object(stats_service, '_set_cache'):
                await stats_service.get_overview_stats(test_user)
                
                log_output = log_stream.getvalue()
                assert "Failed to get overview stats" in log_output
                assert "Critical database error" in log_output
    
    @pytest.mark.asyncio
    async def test_no_print_statements_remain(self, stats_service, test_user):
        """Integration test to verify no print statements are used"""
        # Mock stdout to catch any print statements
        import sys
        from io import StringIO
        
        original_stdout = sys.stdout
        captured_output = StringIO()
        sys.stdout = captured_output
        
        try:
            # Mock database operations that might have had print statements
            mock_session = AsyncMock()
            mock_session.execute = AsyncMock(return_value=AsyncMock(scalar=AsyncMock(return_value=5)))
            mock_session.fetchall = AsyncMock(return_value=[])
            stats_service.db = mock_session
            
            # Mock cache operations
            with patch.object(stats_service, '_get_cache', return_value=None):
                with patch.object(stats_service, '_set_cache'):
                    with patch('app.services.candidate_stats.candidate_crud.get_active_candidates_count', return_value=3):
                        # Call various methods that might have had print statements
                        await stats_service.get_overview_stats(test_user)
                        await stats_service.get_skill_distribution(test_user)
                        
                        # Check that nothing was printed to stdout
                        output = captured_output.getvalue()
                        assert output == "", f"Unexpected print output detected: {output}"
                        
        finally:
            sys.stdout = original_stdout
    
    def test_logger_configuration(self):
        """Test that the module logger is properly configured"""
        import app.services.candidate_stats
        
        # Check that the module has a logger
        assert hasattr(app.services.candidate_stats, 'logger')
        
        # Check logger is configured with the correct name
        logger = app.services.candidate_stats.logger
        assert logger.name == 'app.services.candidate_stats'
        
        # Check logger is instance of logging.Logger
        assert isinstance(logger, logging.Logger)
    
    @pytest.mark.asyncio
    async def test_structured_logging_information(self, stats_service, test_user, log_capture):
        """Test that log messages contain useful structured information"""
        log_stream, logger = log_capture
        
        # Test cache hit logging with user context
        cache_data = {"test": "data"}
        
        with patch.object(stats_service, '_get_cache', return_value=cache_data):
            await stats_service.get_overview_stats(test_user)
            
            log_output = log_stream.getvalue()
            
            # Check that log includes contextual information
            assert f"user {test_user.id}" in log_output
            assert "Cache hit" in log_output
            assert "overview stats" in log_output
            
            # Verify log message is structured and informative
            assert "Cache hit for overview stats, user" in log_output