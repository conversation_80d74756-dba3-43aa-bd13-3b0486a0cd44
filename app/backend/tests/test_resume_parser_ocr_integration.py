"""
Integration tests for Resume Parser OCR functionality

This module tests:
- Resume parsing with OCR enabled/disabled
- Different file format handling (PDF, images, DOCX)
- OCR integration in the complete parsing pipeline
- Error handling and fallback mechanisms
- Performance metrics and processing times
"""

import asyncio
import base64
import json
import tempfile
import time
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
from typing import Dict, Any, List

import pytest
from unittest.mock import Mock

# Mock imports for dependencies that might not be available
import sys

# Apply comprehensive module-level mocks before importing services
with patch.dict('sys.modules', {
    'paddle': Mock(),
    'paddleocr': Mock(),
    'PIL': Mock(),
    'cv2': Mock(),
    'numpy': Mock(),
    'torch': Mock(),
    'torch.cuda': Mock(),
    'docx': Mock()
}):
    # Set up fallback objects
    try:
        import numpy as np
        import cv2
        from PIL import Image
        import PyPDF2
        from docx import Document
        import torch
    except ImportError:
        # Create mock objects for missing dependencies
        np = Mock()
        cv2 = Mock()
        Image = Mock()
        PyPDF2 = Mock()
        Document = Mock()
        torch = Mock()

    import io

    from app.services.resume_parser import ResumeParser, resume_parser, ParsedResume
    from app.services.ocr_service import ocr_service
    from app.schemas.matching import ResumeParseRequest, ResumeParsePreviewRequest, ParsedResumeData
    from app.core.config import settings


class TestResumeParserOCRIntegration:
    """Test OCR integration in resume parsing pipeline"""
    
    @pytest.mark.asyncio
    async def test_parse_resume_pdf_with_ocr_enabled(self):
        """Test parsing PDF resume with OCR enabled"""
        parser = ResumeParser()
        
        # Create a scanned PDF (minimal PDF that would need OCR)
        pdf_content = create_minimal_pdf()
        
        with patch.object(parser, '_extract_pdf_with_ocr') as mock_pdf_extract:
            mock_pdf_extract.return_value = "John Doe\nSoftware Engineer\nPython, JavaScript"
            
            with patch.object(parser, '_extract_structured_data') as mock_structure:
                mock_structure.return_value = ParsedResumeData(
                    basic_info={'name': 'John Doe', 'email': '<EMAIL>'},
                    work_experience=[{'position': 'Software Engineer', 'company': 'Tech Corp'}],
                    skills={'technical': ['Python', 'JavaScript']}
                )
                
                request = ResumeParseRequest(
                    file_content=base64.b64encode(pdf_content).decode(),
                    file_type='pdf',
                    candidate_id=None
                )
                
                result = await parser.parse_resume(request)
                
                assert result.confidence > 0
                assert result.parsed_data.basic_info['name'] == 'John Doe'
                mock_pdf_extract.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_parse_resume_pdf_with_ocr_disabled(self):
        """Test parsing PDF resume with OCR disabled"""
        parser = ResumeParser()
        
        pdf_content = create_minimal_pdf()
        
        with patch.object(parser, '_extract_pdf_with_ocr') as mock_pdf_extract:
            # Simulate OCR disabled scenario
            mock_pdf_extract.return_value = "Extracted via traditional PDF parsing"
            
            with patch.object(parser, '_extract_structured_data') as mock_structure:
                mock_structure.return_value = ParsedResumeData(
                    basic_info={'name': 'Traditional Parse', 'email': '<EMAIL>'}
                )
                
                # Mock the enable_ocr parameter flow
                request = ResumeParseRequest(
                    file_content=base64.b64encode(pdf_content).decode(),
                    file_type='pdf'
                )
                
                result = await parser.parse_resume(request)
                
                assert result.parsed_data.basic_info['name'] == 'Traditional Parse'
    
    @pytest.mark.asyncio
    async def test_parse_resume_image_with_ocr(self):
        """Test parsing image resume with OCR"""
        parser = ResumeParser()
        
        # Create test image with resume text
        image_content = create_resume_image()
        
        with patch.object(parser, '_extract_image_text') as mock_image_extract:
            mock_image_extract.return_value = "JANE SMITH\nDATA SCIENTIST\nPython, R, Machine Learning"
            
            with patch.object(parser, '_extract_structured_data') as mock_structure:
                mock_structure.return_value = ParsedResumeData(
                    basic_info={'name': 'Jane Smith', 'email': '<EMAIL>'},
                    work_experience=[{'position': 'Data Scientist', 'company': 'AI Corp'}],
                    skills={'technical': ['Python', 'R', 'Machine Learning']}
                )
                
                request = ResumeParseRequest(
                    file_content=base64.b64encode(image_content).decode(),
                    file_type='png'
                )
                
                result = await parser.parse_resume(request)
                
                assert result.parsed_data.basic_info['name'] == 'Jane Smith'
                assert 'Data Scientist' in str(result.parsed_data.work_experience)
                mock_image_extract.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_parse_resume_image_ocr_disabled(self):
        """Test parsing image resume when OCR is disabled"""
        parser = ResumeParser()
        
        image_content = create_resume_image()
        
        with patch.object(parser, '_extract_text') as mock_extract:
            # Simulate OCR disabled for image - should return empty
            mock_extract.return_value = ""
            
            with patch.object(parser, '_extract_structured_data') as mock_structure:
                mock_structure.return_value = ParsedResumeData(
                    basic_info={},
                    work_experience=[],
                    skills={}
                )
                
                request = ResumeParseRequest(
                    file_content=base64.b64encode(image_content).decode(),
                    file_type='png'
                )
                
                result = await parser.parse_resume(request)
                
                # Should have low confidence due to no text extraction
                assert result.confidence == 0.0
                mock_extract.assert_called_once()


class TestOCRServiceIntegration:
    """Test OCR service integration with actual service calls"""
    
    @pytest.mark.asyncio
    async def test_pdf_ocr_extraction_success(self):
        """Test PDF OCR extraction with mocked OCR service"""
        parser = ResumeParser()
        
        pdf_content = create_scanned_pdf_content()
        
        with patch.object(ocr_service, 'extract_text') as mock_ocr_extract:
            mock_ocr_extract.return_value = {
                'success': True,
                'text': 'Michael Johnson\nSenior Developer\n5 years experience\nJava, Spring Boot, MySQL',
                'confidence': 0.89,
                'method': 'ocr_gpu',
                'processing_time': 2.1
            }
            
            result = await parser._extract_pdf_with_ocr(pdf_content, enable_ocr=True)
            
            assert 'Michael Johnson' in result
            assert 'Senior Developer' in result
            mock_ocr_extract.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_pdf_ocr_extraction_failure_with_fallback(self):
        """Test PDF OCR failure with traditional extraction fallback"""
        parser = ResumeParser()
        
        pdf_content = create_text_extractable_pdf()
        
        with patch.object(ocr_service, 'extract_text') as mock_ocr_extract:
            mock_ocr_extract.return_value = {
                'success': False,
                'error': 'OCR processing failed',
                'processing_time': 1.0
            }
            
            with patch.object(parser, '_extract_text_from_pdf') as mock_pdf_extract:
                mock_pdf_extract.return_value = "Fallback extracted text from PDF"
                
                result = await parser._extract_pdf_with_ocr(pdf_content, enable_ocr=True)
                
                assert result == "Fallback extracted text from PDF"
                mock_pdf_extract.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_image_ocr_extraction_success(self):
        """Test image OCR extraction success"""
        parser = ResumeParser()
        
        image_content = create_resume_image_with_text()
        
        with patch.object(ocr_service, 'extract_text') as mock_ocr_extract:
            mock_ocr_extract.return_value = {
                'success': True,
                'text': 'Sarah Wilson\nProduct Manager\nAgile, Scrum, Product Strategy',
                'confidence': 0.93,
                'method': 'ocr_gpu',
                'processing_time': 1.8
            }
            
            result = await parser._extract_image_text(image_content, 'png')
            
            assert 'Sarah Wilson' in result
            assert 'Product Manager' in result
            mock_ocr_extract.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_image_ocr_extraction_failure(self):
        """Test image OCR extraction failure"""
        parser = ResumeParser()
        
        # Invalid image content
        invalid_image = b'invalid image data'
        
        with patch.object(ocr_service, 'extract_text') as mock_ocr_extract:
            mock_ocr_extract.return_value = {
                'success': False,
                'error': 'Invalid image format',
                'processing_time': 0.5
            }
            
            with pytest.raises(ValueError, match="OCR extraction failed"):
                await parser._extract_image_text(invalid_image, 'png')


class TestOCRParameterPropagation:
    """Test OCR parameter propagation through the parsing pipeline"""
    
    @pytest.mark.asyncio
    async def test_ocr_enabled_parameter_flow(self):
        """Test that enable_ocr parameter flows through the entire pipeline"""
        parser = ResumeParser()
        
        pdf_content = create_minimal_pdf()
        
        with patch.object(parser, '_extract_pdf_with_ocr') as mock_pdf_extract:
            mock_pdf_extract.return_value = "OCR enabled text"
            
            # Test enable_ocr=True
            result = await parser._extract_text(pdf_content, 'pdf', enable_ocr=True)
            mock_pdf_extract.assert_called_with(pdf_content, enable_ocr=True)
            
            # Test enable_ocr=False
            result = await parser._extract_text(pdf_content, 'pdf', enable_ocr=False)
            mock_pdf_extract.assert_called_with(pdf_content, enable_ocr=False)
    
    @pytest.mark.asyncio
    async def test_image_ocr_parameter_handling(self):
        """Test OCR parameter handling for image files"""
        parser = ResumeParser()
        
        image_content = create_resume_image()
        
        with patch.object(parser, '_extract_image_text') as mock_image_extract:
            mock_image_extract.return_value = "Image OCR text"
            
            # Test with OCR enabled
            result = await parser._extract_text(image_content, 'png', enable_ocr=True)
            mock_image_extract.assert_called_once()
            
        # Reset mock
        mock_image_extract.reset_mock()
        
        # Test with OCR disabled - should return empty
        result = await parser._extract_text(image_content, 'png', enable_ocr=False)
        mock_image_extract.assert_not_called()
        assert result == ""


class TestPerformanceAndMetrics:
    """Test OCR performance and metrics collection"""
    
    @pytest.mark.asyncio
    async def test_ocr_processing_time_measurement(self):
        """Test that OCR processing time is measured correctly"""
        parser = ResumeParser()
        
        image_content = create_resume_image()
        
        with patch.object(ocr_service, 'extract_text') as mock_ocr_extract:
            # Simulate processing time
            async def slow_ocr(*args, **kwargs):
                await asyncio.sleep(0.1)  # 100ms delay
                return {
                    'success': True,
                    'text': 'Test text',
                    'confidence': 0.85,
                    'method': 'ocr_gpu',
                    'processing_time': 0.12
                }
            
            mock_ocr_extract.side_effect = slow_ocr
            
            start_time = time.time()
            result = await parser._extract_image_text(image_content, 'png')
            end_time = time.time()
            
            # Verify the method was called and took appropriate time
            assert (end_time - start_time) >= 0.1
            mock_ocr_extract.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_large_file_processing_limits(self):
        """Test processing limits for large files"""
        parser = ResumeParser()
        
        # Mock a large image file by creating large byte content
        large_image_content = b'mock_large_image_data' * 10000  # Simulate large file
        
        with patch.object(ocr_service, 'extract_text') as mock_ocr_extract:
            mock_ocr_extract.return_value = {
                'success': False,
                'error': 'File too large',
                'processing_time': 0.1
            }
            
            with pytest.raises(ValueError, match="OCR extraction failed"):
                await parser._extract_image_text(large_image_content, 'png')


class TestOCRErrorHandlingInPipeline:
    """Test error handling in the OCR-integrated parsing pipeline"""
    
    @pytest.mark.asyncio
    async def test_ocr_timeout_handling(self):
        """Test handling of OCR timeouts"""
        parser = ResumeParser()
        
        pdf_content = create_minimal_pdf()
        
        with patch.object(ocr_service, 'extract_text') as mock_ocr_extract:
            mock_ocr_extract.return_value = {
                'success': False,
                'error': 'Processing timeout (60s)',
                'processing_time': 60.0
            }
            
            with patch.object(parser, '_extract_text_from_pdf') as mock_fallback:
                mock_fallback.return_value = "Fallback text after timeout"
                
                result = await parser._extract_pdf_with_ocr(pdf_content, enable_ocr=True)
                
                assert result == "Fallback text after timeout"
                mock_fallback.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_ocr_resource_error_handling(self):
        """Test handling of OCR resource errors"""
        parser = ResumeParser()
        
        image_content = create_resume_image()
        
        with patch.object(ocr_service, 'extract_text') as mock_ocr_extract:
            mock_ocr_extract.return_value = {
                'success': False,
                'error': 'Insufficient GPU memory',
                'processing_time': 0.5
            }
            
            with pytest.raises(ValueError, match="OCR extraction failed"):
                await parser._extract_image_text(image_content, 'png')
    
    @pytest.mark.asyncio
    async def test_complete_pipeline_error_recovery(self):
        """Test complete pipeline recovery from OCR errors"""
        parser = ResumeParser()
        
        pdf_content = create_text_extractable_pdf()
        
        with patch.object(parser, '_extract_structured_data') as mock_structure:
            mock_structure.return_value = ParsedResumeData(
                basic_info={'name': 'Recovery Test'},
                work_experience=[],
                skills={}
            )
            
            with patch.object(ocr_service, 'extract_text') as mock_ocr_extract:
                mock_ocr_extract.return_value = {
                    'success': False,
                    'error': 'OCR engine failed',
                    'processing_time': 1.0
                }
                
                with patch.object(parser, '_extract_text_from_pdf') as mock_fallback:
                    mock_fallback.return_value = "Recovered text content"
                    
                    request = ResumeParseRequest(
                        file_content=base64.b64encode(pdf_content).decode(),
                        file_type='pdf'
                    )
                    
                    result = await parser.parse_resume(request)
                    
                    assert result.parsed_data.basic_info['name'] == 'Recovery Test'
                    # Verify fallback was used
                    mock_fallback.assert_called_once()


class TestFileFormatSupport:
    """Test OCR integration across different file formats"""
    
    @pytest.mark.asyncio
    async def test_supported_image_formats(self):
        """Test OCR works with all supported image formats"""
        parser = ResumeParser()
        
        formats = ['png', 'jpg', 'jpeg', 'bmp', 'tiff', 'webp']
        
        for fmt in formats:
            image_content = create_resume_image()
            
            with patch.object(ocr_service, 'extract_text') as mock_ocr:
                mock_ocr.return_value = {
                    'success': True,
                    'text': f'Text from {fmt.upper()}',
                    'confidence': 0.9,
                    'method': 'ocr_gpu',
                    'processing_time': 1.0
                }
                
                result = await parser._extract_image_text(image_content, fmt)
                assert f'Text from {fmt.upper()}' in result
    
    @pytest.mark.asyncio
    async def test_unsupported_format_handling(self):
        """Test handling of unsupported file formats"""
        parser = ResumeParser()
        
        # Test unsupported format
        with pytest.raises(ValueError, match="Unsupported file type"):
            await parser._extract_text(b'test content', 'xyz')
    
    @pytest.mark.asyncio
    async def test_docx_format_no_ocr(self):
        """Test DOCX format doesn't use OCR"""
        parser = ResumeParser()
        
        docx_content = create_docx_content()
        
        with patch.object(parser, '_extract_docx_text') as mock_docx:
            mock_docx.return_value = "DOCX extracted text"
            
            result = await parser._extract_text(docx_content, 'docx', enable_ocr=True)
            
            assert result == "DOCX extracted text"
            mock_docx.assert_called_once()


# Test utility functions
def create_minimal_pdf() -> bytes:
    """Create minimal PDF content for testing"""
    # Return mock PDF data to avoid PyPDF2 dependency issues
    return b'%PDF-1.4\n1 0 obj\n<< /Type /Catalog /Pages 2 0 R >>\nendobj\n2 0 obj\n<< /Type /Pages /Kids [3 0 R] /Count 1 >>\nendobj\nxref\ntrailer\n<< /Size 3 /Root 1 0 R >>\n%%EOF'


def create_text_extractable_pdf() -> bytes:
    """Create PDF with extractable text"""
    # This is a simplified version - in practice, we'd need a PDF with actual text
    return create_minimal_pdf()


def create_scanned_pdf_content() -> bytes:
    """Create content representing a scanned PDF"""
    # In practice, this would be a PDF created from scanned images
    return create_minimal_pdf()


def create_resume_image() -> bytes:
    """Create a simple resume image for testing"""
    # Return mock image data instead of creating actual image
    # This avoids dependency on numpy and cv2 being available
    return b'mock_resume_image_data_png'


def create_resume_image_with_text() -> bytes:
    """Create resume image with more detailed text"""
    # Return mock detailed image data instead of creating actual image
    return b'mock_detailed_resume_image_data_png'


def create_docx_content() -> bytes:
    """Create DOCX content for testing"""
    # Return mock DOCX data to avoid docx dependency issues
    return b'PK\x03\x04mock_docx_content_data'


# Fixtures for test data
@pytest.fixture
def sample_resume_files():
    """Create sample resume files for testing"""
    files = {}
    
    # PDF file
    files['pdf'] = create_minimal_pdf()
    
    # Image file
    files['png'] = create_resume_image()
    
    # DOCX file
    files['docx'] = create_docx_content()
    
    return files


@pytest.fixture
def mock_ocr_responses():
    """Mock OCR service responses for different scenarios"""
    return {
        'success': {
            'success': True,
            'text': 'Mock OCR extracted text',
            'confidence': 0.85,
            'method': 'ocr_gpu',
            'processing_time': 1.5
        },
        'failure': {
            'success': False,
            'error': 'Mock OCR failure',
            'processing_time': 0.5
        },
        'low_confidence': {
            'success': False,
            'error': 'Low confidence: 0.25',
            'processing_time': 2.0
        },
        'timeout': {
            'success': False,
            'error': 'Processing timeout (60s)',
            'processing_time': 60.0
        }
    }


# Performance benchmarks
class TestOCRPerformanceBenchmarks:
    """Performance benchmarks for OCR integration"""
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_pdf_ocr_performance_benchmark(self):
        """Benchmark PDF OCR performance"""
        parser = ResumeParser()
        pdf_content = create_minimal_pdf()
        
        with patch.object(ocr_service, 'extract_text') as mock_ocr:
            mock_ocr.return_value = {
                'success': True,
                'text': 'Benchmark text content',
                'confidence': 0.9,
                'method': 'ocr_gpu',
                'processing_time': 1.2
            }
            
            start_time = time.time()
            result = await parser._extract_pdf_with_ocr(pdf_content, enable_ocr=True)
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # Performance assertions
            assert processing_time < 5.0  # Should complete within 5 seconds
            assert len(result) > 0
    
    @pytest.mark.asyncio
    @pytest.mark.performance
    async def test_image_ocr_performance_benchmark(self):
        """Benchmark image OCR performance"""
        parser = ResumeParser()
        image_content = create_resume_image_with_text()
        
        with patch.object(ocr_service, 'extract_text') as mock_ocr:
            mock_ocr.return_value = {
                'success': True,
                'text': 'Benchmark image text',
                'confidence': 0.88,
                'method': 'ocr_gpu',
                'processing_time': 2.1
            }
            
            start_time = time.time()
            result = await parser._extract_image_text(image_content, 'png')
            end_time = time.time()
            
            processing_time = end_time - start_time
            
            # Performance assertions
            assert processing_time < 10.0  # Should complete within 10 seconds
            assert len(result) > 0


# Test markers
pytestmark = [
    pytest.mark.asyncio,
    pytest.mark.integration,
    pytest.mark.ocr
]