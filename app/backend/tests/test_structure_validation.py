#!/usr/bin/env python3
"""
Test Structure Validation

Simple validation that test files are properly structured and can be
imported in the Docker environment.
"""
import ast
import sys
from pathlib import Path


def validate_python_syntax(file_path: Path) -> bool:
    """Validate that a Python file has correct syntax"""
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        ast.parse(content)
        return True
    except SyntaxError as e:
        print(f"❌ Syntax error in {file_path}: {e}")
        return False
    except Exception as e:
        print(f"❌ Error parsing {file_path}: {e}")
        return False


def validate_test_structure(file_path: Path) -> bool:
    """Validate test file structure and naming"""
    if not file_path.name.startswith('test_'):
        print(f"❌ Test file should start with 'test_': {file_path.name}")
        return False
    
    if not file_path.suffix == '.py':
        print(f"❌ Test file should be Python file: {file_path.name}")
        return False
    
    try:
        with open(file_path, 'r') as f:
            content = f.read()
        
        # Check for basic pytest structure
        if 'import pytest' not in content:
            print(f"⚠️ Warning: {file_path.name} doesn't import pytest")
        
        if 'async def test_' not in content and 'def test_' not in content:
            print(f"⚠️ Warning: {file_path.name} doesn't contain test functions")
        
        return True
    except Exception as e:
        print(f"❌ Error reading {file_path}: {e}")
        return False


def main():
    """Validate all authentication test files"""
    test_dir = Path(__file__).parent
    auth_test_files = [
        'test_auth_smoke.py',
        'test_auth_verification.py', 
        'test_auth_ci.py'
    ]
    
    print("🔍 Validating Authentication Test Structure")
    print("=" * 50)
    
    all_valid = True
    
    for test_file in auth_test_files:
        file_path = test_dir / test_file
        
        if not file_path.exists():
            print(f"❌ Missing test file: {test_file}")
            all_valid = False
            continue
        
        print(f"📄 Validating {test_file}...")
        
        # Validate syntax
        if not validate_python_syntax(file_path):
            all_valid = False
            continue
        
        # Validate structure
        if not validate_test_structure(file_path):
            all_valid = False
            continue
        
        print(f"✅ {test_file} is valid")
    
    # Validate supporting files
    supporting_files = [
        'conftest.py',
        'run_auth_tests.py',
        'README_AUTH_TESTS.md'
    ]
    
    print("\n🔧 Validating Supporting Files")
    print("-" * 30)
    
    for support_file in supporting_files:
        file_path = test_dir / support_file
        if not file_path.exists():
            print(f"⚠️ Missing supporting file: {support_file}")
        else:
            print(f"✅ {support_file} exists")
    
    print("\n" + "=" * 50)
    
    if all_valid:
        print("🎉 All authentication test files are properly structured!")
        return 0
    else:
        print("❌ Some authentication test files have issues!")
        return 1


if __name__ == "__main__":
    sys.exit(main())