"""
Authentication Smoke Tests

Quick validation tests that can be run to verify the authentication system
is working correctly. These are minimal, fast tests for CI/CD pipelines.
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core import security
from app.models.user import User
from app.core.enums import UserRole


class TestAuthSmoke:
    """Minimal smoke tests for authentication system"""

    @pytest.mark.asyncio
    async def test_auth_endpoints_accessible(self, client: AsyncClient):
        """Test that authentication endpoints are accessible"""
        # Test login endpoint exists and accepts POST
        response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={"username": "test", "password": "test"}  # Will fail auth but endpoint should exist
        )
        # Should get 401 (unauthorized) not 404 (not found)
        assert response.status_code in [401, 422]  # 422 for validation, 401 for auth
        
        # Test /me endpoint exists but requires auth
        response = await client.get(f"{settings.API_V1_STR}/auth/me")
        assert response.status_code == 401  # Should require authentication

    @pytest.mark.asyncio
    async def test_token_generation_works(self, test_user: User):
        """Test that JWT token generation works"""
        # Should be able to generate tokens without errors
        access_token = security.create_access_token(test_user.id)
        refresh_token = security.create_refresh_token(test_user.id)
        
        assert access_token is not None
        assert refresh_token is not None
        assert len(access_token) > 50  # JWT tokens are long
        assert len(refresh_token) > 50

    @pytest.mark.asyncio
    async def test_token_decode_works(self, test_user: User):
        """Test that JWT token decoding works"""
        token = security.create_access_token(test_user.id)
        payload = security.decode_token(token)
        
        assert payload is not None
        assert payload.get("type") == "access"
        assert int(payload.get("sub")) == test_user.id

    @pytest.mark.asyncio
    async def test_user_creation_with_enums_works(self, db_session: AsyncSession):
        """Test that user creation with enum roles works (regression)"""
        user = User(
            email="<EMAIL>",
            username="smoketest",
            hashed_password=security.get_password_hash("password"),
            full_name="Smoke Test User",
            role=UserRole.USER,
            is_active=True
        )
        
        db_session.add(user)
        await db_session.commit()
        await db_session.refresh(user)
        
        assert user.id is not None
        assert user.role == UserRole.USER

    @pytest.mark.asyncio
    async def test_basic_auth_flow_works(self, client: AsyncClient, test_user: User, test_password: str):
        """Test basic authentication flow end-to-end"""
        # Login should work
        login_response = await client.post(
            f"{settings.API_V1_STR}/auth/login",
            data={
                "username": test_user.email,
                "password": test_password
            }
        )
        
        assert login_response.status_code == 200
        tokens = login_response.json()
        assert "access_token" in tokens
        
        # Should be able to use token to access protected endpoint
        response = await client.get(
            f"{settings.API_V1_STR}/auth/me",
            headers={"Authorization": f"Bearer {tokens['access_token']}"}
        )
        
        assert response.status_code == 200
        user_data = response.json()
        assert user_data["email"] == test_user.email


class TestAuthHealthCheck:
    """Health check tests for authentication components"""

    @pytest.mark.asyncio
    async def test_password_hashing_works(self):
        """Test that password hashing and verification works"""
        password = "test_password_123"
        hashed = security.get_password_hash(password)
        
        assert hashed != password  # Should be hashed
        assert security.verify_password(password, hashed)  # Should verify correctly
        assert not security.verify_password("wrong_password", hashed)  # Should reject wrong password

    @pytest.mark.asyncio
    async def test_jwt_configuration_valid(self):
        """Test that JWT configuration is valid"""
        assert settings.SECRET_KEY is not None
        assert len(settings.SECRET_KEY) >= 32  # Should be reasonably secure
        assert settings.ALGORITHM in ["HS256", "RS256", "ES256"]  # Should use secure algorithm
        assert settings.ACCESS_TOKEN_EXPIRE_MINUTES > 0
        assert settings.REFRESH_TOKEN_EXPIRE_DAYS > 0

    @pytest.mark.asyncio
    async def test_database_accessible_for_auth(self, db_session: AsyncSession):
        """Test that database is accessible for authentication operations"""
        # Simple test to verify database connection works
        from sqlalchemy import text
        result = await db_session.execute(text("SELECT 1"))
        assert result.scalar() == 1
        
        # Test that users table is accessible
        result = await db_session.execute(text("SELECT COUNT(*) FROM users"))
        count = result.scalar()
        assert count is not None  # Should return a number, even if 0

    @pytest.mark.asyncio
    async def test_enum_types_available(self, db_session: AsyncSession):
        """Test that enum types are available in database"""
        from sqlalchemy import text
        
        # Check that UserRole enum exists and has expected values
        result = await db_session.execute(text("""
            SELECT e.enumlabel
            FROM pg_type t 
            JOIN pg_enum e ON t.oid = e.enumtypid  
            WHERE t.typname = 'userrole'
        """))
        
        roles = [row[0] for row in result.fetchall()]
        assert len(roles) > 0  # Should have some role values
        assert "USER" in roles  # Should have basic USER role


class TestAuthFastFailure:
    """Fast failure tests to catch critical authentication issues quickly"""

    @pytest.mark.asyncio
    async def test_no_critical_auth_imports_fail(self):
        """Test that critical auth-related imports don't fail"""
        try:
            from app.core import security
            from app.core.config import settings
            from app.api.deps import get_current_user
            from app.api.v1.auth import router
            from app.crud import user as user_crud
            from app.models.user import User
            from app.core.enums import UserRole
            # If we get here without exceptions, imports are working
            assert True
        except ImportError as e:
            pytest.fail(f"Critical auth import failed: {e}")

    @pytest.mark.asyncio
    async def test_settings_have_required_auth_values(self):
        """Test that required authentication settings are present"""
        required_settings = [
            "SECRET_KEY",
            "ALGORITHM", 
            "ACCESS_TOKEN_EXPIRE_MINUTES",
            "REFRESH_TOKEN_EXPIRE_DAYS"
        ]
        
        for setting_name in required_settings:
            setting_value = getattr(settings, setting_name, None)
            assert setting_value is not None, f"Missing required setting: {setting_name}"
            
            # Check that it's not empty string or zero
            if isinstance(setting_value, str):
                assert len(setting_value) > 0, f"Empty setting: {setting_name}"
            elif isinstance(setting_value, (int, float)):
                assert setting_value > 0, f"Invalid numeric setting: {setting_name}"

    @pytest.mark.asyncio
    async def test_basic_security_functions_dont_crash(self):
        """Test that basic security functions don't crash"""
        # These should not raise exceptions
        try:
            # Test password hashing
            hashed = security.get_password_hash("test123")
            assert len(hashed) > 10
            
            # Test password verification
            is_valid = security.verify_password("test123", hashed)
            assert is_valid is True
            
            # Test token creation (basic smoke test)
            token = security.create_access_token(12345)
            assert len(token) > 50
            
            # Test token decoding
            payload = security.decode_token(token)
            assert payload.get("sub") == "12345"
            
        except Exception as e:
            pytest.fail(f"Basic security function crashed: {e}")