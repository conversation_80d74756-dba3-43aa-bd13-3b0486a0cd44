# Authentication System Tests

Focused test suite for validating the authentication system implementation without expanding requirements.

## Test Files

### 🔥 `test_auth_smoke.py` - Smoke Tests
**Purpose**: Quick validation that basic authentication functionality works
**Runtime**: ~30 seconds
**Use Case**: Fast feedback, pre-commit validation, CI/CD gates

**Test Categories**:
- Basic endpoint accessibility
- JWT token generation/decoding
- Password hashing functionality
- User creation with enums (regression test)
- Essential auth flow validation

### 🔍 `test_auth_verification.py` - Verification Tests
**Purpose**: Comprehensive validation of authentication flows
**Runtime**: ~2-3 minutes
**Use Case**: Full system validation, integration testing

**Test Categories**:
- Complete authentication flow testing
- Token refresh functionality
- Authentication edge cases and error conditions
- Security validation
- Performance validation
- Regression prevention tests

### 🚀 `test_auth_ci.py` - CI/CD Tests
**Purpose**: Tests optimized for continuous integration pipelines
**Runtime**: ~45 seconds
**Use Case**: Automated builds, deployment validation

**Test Categories**:
- Critical import validation
- Database migration state validation
- Environment configuration validation
- Regression prevention (enum conflicts, etc.)

## Running Tests

### Quick Start
```bash
# Smoke tests (fastest)
python -m pytest tests/test_auth_smoke.py -v

# All auth tests
python -m pytest tests/test_auth_*.py -v

# CI/CD tests only
python -m pytest tests/test_auth_ci.py -m "smoke or integration"
```

### Using Test Runner Scripts

#### Python Test Runner
```bash
cd app/backend
python tests/run_auth_tests.py --mode smoke     # Fast smoke tests
python tests/run_auth_tests.py --mode all       # All auth tests
python tests/run_auth_tests.py --mode coverage  # With coverage report
python tests/run_auth_tests.py --fast          # Smoke tests only
```

#### Shell Script Runner  
```bash
cd app/backend
../scripts/test_auth.sh -m smoke               # Smoke tests
../scripts/test_auth.sh -m all -v              # All tests, verbose
../scripts/test_auth.sh -f                     # Fast mode
../scripts/test_auth.sh -m ci                  # CI/CD tests
```

### Docker Environment
```bash
# Run tests in Docker container
make test-auth-smoke      # Smoke tests
make test-auth-full       # All auth tests
```

## Test Categories and Markers

### Pytest Markers
- `@pytest.mark.smoke` - Quick validation tests
- `@pytest.mark.integration` - Full integration tests  
- `@pytest.mark.regression` - Regression prevention tests
- `@pytest.mark.performance` - Basic performance validation
- `@pytest.mark.security` - Security-focused tests
- `@pytest.mark.edge_case` - Edge cases and error conditions

### Running by Category
```bash
# Run only smoke tests
pytest -m smoke

# Run integration tests
pytest -m integration

# Run regression tests
pytest -m regression

# Exclude slow tests
pytest -m "not performance"
```

## What These Tests Validate

### ✅ Functionality Validated
- **Authentication Flow**: Login with valid/invalid credentials
- **JWT Tokens**: Creation, validation, refresh, expiration
- **Password Security**: Hashing, verification, strength
- **User Management**: Creation with enum roles, CRUD operations
- **API Endpoints**: Accessibility, response formats, error codes
- **Database Integration**: Migration state, enum types, relationships
- **Environment Configuration**: Required settings, development features
- **Error Handling**: Proper error codes, graceful failures
- **Performance**: Response times within acceptable limits

### ❌ What's NOT Tested (Out of Scope)
- New authentication methods (OAuth, SAML, etc.)
- Additional RBAC features beyond current implementation
- Complex permission hierarchies
- Advanced security features not yet implemented
- UI/frontend authentication flows
- External integrations (LDAP, AD, etc.)

## CI/CD Integration

### GitHub Actions Example
```yaml
- name: Run Authentication Smoke Tests
  run: |
    cd app/backend
    python -m pytest tests/test_auth_ci.py -m smoke -v
    
- name: Run Full Authentication Tests
  run: |
    cd app/backend  
    python tests/run_auth_tests.py --mode all
```

### Quality Gates
- **Smoke Tests**: Must pass for build to continue
- **Verification Tests**: Must pass for deployment
- **CI Tests**: Must pass for merge to main branch

## Test Configuration

### Pytest Configuration
Configuration is in `pytest_auth.ini`:
- Async test support
- Timeout settings (5 minutes max)
- Logging configuration
- Warning filters
- Test markers

### Database Setup
Tests use in-memory SQLite database by default for speed and isolation:
- Each test gets fresh database
- No persistence between tests
- Automatic cleanup
- Fast execution

## Troubleshooting

### Common Issues

#### Tests Fail with Import Errors
```bash
# Ensure you're in the correct directory
cd app/backend

# Check Python path
export PYTHONPATH=$(pwd):$PYTHONPATH

# Install test dependencies
pip install pytest pytest-asyncio httpx
```

#### Database Connection Errors
```bash
# Check if PostgreSQL is running (for full integration)
make status

# Use test database override if needed
export TEST_DATABASE_URL="sqlite+aiosqlite:///:memory:"
```

#### JWT Token Errors
```bash
# Verify settings
python -c "from app.core.config import settings; print(settings.SECRET_KEY[:10])"

# Check algorithm support
python -c "from app.core.security import create_access_token; print('JWT OK')"
```

### Test Reports
Test reports are automatically generated in:
- `auth_test_report.txt` - Human readable summary
- `htmlcov/auth/` - Coverage report (if --cov used)

## Maintenance

### Adding New Tests
1. Add tests to appropriate file based on category
2. Use existing fixtures from `conftest.py`
3. Follow naming convention: `test_[functionality]_[scenario]`
4. Add appropriate pytest markers
5. Keep tests focused and independent

### Test Data Management
- Use fixtures for test data creation
- Each test should be independent
- Clean up test data automatically
- Use realistic but minimal test data

## Performance Expectations

| Test Suite | Expected Runtime | Test Count | 
|------------|------------------|------------|
| Smoke Tests | 30-45 seconds | ~15 tests |
| Verification Tests | 2-3 minutes | ~35 tests |
| CI Tests | 45-60 seconds | ~20 tests |
| All Tests | 3-4 minutes | ~70 tests |

Times may vary based on system performance and database speed.