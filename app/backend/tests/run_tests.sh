#!/bin/bash
# AI Provider Test Runner
# 运行AI提供商集成测试

set -e

echo "=========================================="
echo "🚀 AI Provider Integration Test Runner"
echo "=========================================="
echo ""

# 确保在正确的目录
cd /app

# 创建测试目录（如果不存在）
mkdir -p tests/integration

# 检查参数
MODE=${1:-all}

case $MODE in
    all)
        echo "📊 Running comprehensive provider tests..."
        python tests/integration/test_ai_providers.py all
        ;;
    
    minimal)
        echo "⚡ Running minimal provider test..."
        python tests/integration/test_ai_providers.py minimal
        ;;
    
    ollama)
        echo "🦙 Running Ollama-specific tests..."
        python tests/integration/test_ai_providers.py ollama
        ;;
    
    rerank)
        echo "🎯 Running Rerank provider tests..."
        python tests/integration/test_rerank_providers.py
        ;;
    
    report)
        echo "📝 Generating test report..."
        python tests/integration/test_summary_generator.py
        ;;
    
    quick)
        echo "🚀 Running quick test suite..."
        echo "1. Minimal test..."
        python tests/integration/test_ai_providers.py minimal
        echo ""
        echo "2. Generating report..."
        python tests/integration/test_summary_generator.py
        ;;
    
    *)
        echo "Usage: $0 [all|minimal|ollama|rerank|report|quick]"
        echo ""
        echo "Options:"
        echo "  all      - Run comprehensive tests for all providers"
        echo "  minimal  - Test only current configured providers"
        echo "  ollama   - Test Ollama-specific models"
        echo "  rerank   - Test rerank functionality"
        echo "  report   - Generate test summary report"
        echo "  quick    - Run minimal test + generate report"
        exit 1
        ;;
esac

echo ""
echo "✨ Test execution complete!"