#!/usr/bin/env python3
"""
AI Provider Test Summary Generator
生成AI提供商测试的汇总报告
"""
import json
import os
from datetime import datetime
from typing import Dict, List, Any
import asyncio
import sys

sys.path.insert(0, '/app')


def generate_markdown_report(results: Dict[str, Any]) -> str:
    """生成Markdown格式的测试报告"""
    report = []
    
    # 标题
    report.append("# AI Provider Test Summary Report")
    report.append(f"\n**Generated**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append(f"**Environment**: {os.getenv('ENVIRONMENT', 'development')}\n")
    
    # 统计信息
    total_providers = len(results)
    working_providers = sum(1 for r in results.values() 
                           if any(t.get("status") == "success" 
                                 for t in r.get("tests", {}).values()))
    
    report.append("## Overall Statistics\n")
    report.append(f"- **Total Providers Tested**: {total_providers}")
    report.append(f"- **Working Providers**: {working_providers}")
    report.append(f"- **Success Rate**: {working_providers/total_providers*100:.1f}%\n")
    
    # 详细结果表格
    report.append("## Provider Test Results\n")
    report.append("| Provider | LLM | Embedding | Rerank | Response Time (ms) | Status |")
    report.append("|----------|-----|-----------|--------|-------------------|---------|")
    
    for provider, result in results.items():
        tests = result.get("tests", {})
        
        # LLM状态
        llm_status = "✅" if tests.get("llm", {}).get("status") == "success" else "❌"
        llm_time = tests.get("llm", {}).get("response_time", 0)
        
        # Embedding状态
        emb_status = "✅" if tests.get("embedding", {}).get("status") == "success" else "❌"
        emb_dim = tests.get("embedding", {}).get("dimension", 0)
        if emb_dim > 0:
            emb_status += f" ({emb_dim}D)"
        elif provider == "moonshot":
            emb_status = "N/A"
        
        # Rerank状态
        rerank_status = "N/A"
        if "rerank" in tests:
            rerank_status = "✅" if tests["rerank"]["status"] == "success" else "❌"
        
        # 平均响应时间
        times = [t.get("response_time", 0) for t in tests.values() if "response_time" in t]
        avg_time = sum(times) / len(times) if times else 0
        
        # 整体状态
        overall = "🟢 Working" if any(t.get("status") == "success" for t in tests.values()) else "🔴 Failed"
        
        report.append(f"| {provider.upper()} | {llm_status} | {emb_status} | {rerank_status} | {avg_time:.0f} | {overall} |")
    
    # 配置信息
    report.append("\n## Current Configuration\n")
    report.append(f"- **LLM Provider**: {os.getenv('LLM_PROVIDER', 'Not set')}")
    report.append(f"- **Embedding Provider**: {os.getenv('EMBEDDING_PROVIDER', 'Not set')}")
    report.append(f"- **Rerank Provider**: {os.getenv('RERANK_PROVIDER', 'Not set')}")
    report.append(f"- **LLM Fallback Chain**: {os.getenv('LLM_FALLBACK_CHAIN', 'Not set')}")
    report.append(f"- **Embedding Fallback Chain**: {os.getenv('EMBEDDING_FALLBACK_CHAIN', 'Not set')}")
    
    # 特殊配置
    openrouter_base = os.getenv('OPENROUTER_API_BASE', '')
    if openrouter_base:
        if 'siliconflow' in openrouter_base.lower():
            report.append(f"\n### SiliconFlow Configuration (via OpenRouter)")
            report.append(f"- **API Base**: {openrouter_base}")
            report.append(f"- **Rerank Model**: {os.getenv('OPENROUTER_RERANK_MODEL', 'Not set')}")
        else:
            report.append(f"\n### OpenRouter Configuration")
            report.append(f"- **API Base**: {openrouter_base}")
    
    # 推荐配置
    report.append("\n## Recommendations\n")
    
    if working_providers == total_providers:
        report.append("✅ **All providers are working correctly!**\n")
    else:
        report.append("### Failed Providers:\n")
        for provider, result in results.items():
            tests = result.get("tests", {})
            if not any(t.get("status") == "success" for t in tests.values()):
                report.append(f"- **{provider.upper()}**: Check API key and configuration")
                for test_name, test_result in tests.items():
                    if test_result.get("status") != "success":
                        error = test_result.get("error", "Unknown error")
                        report.append(f"  - {test_name}: {error}")
    
    # 性能优化建议
    report.append("\n### Performance Optimization:\n")
    
    # 找出最快的提供商
    provider_times = {}
    for provider, result in results.items():
        times = [t.get("response_time", float('inf')) 
                for t in result.get("tests", {}).values() 
                if t.get("status") == "success"]
        if times:
            provider_times[provider] = min(times)
    
    if provider_times:
        fastest = min(provider_times, key=provider_times.get)
        report.append(f"- **Fastest Provider**: {fastest.upper()} ({provider_times[fastest]:.0f}ms)")
        report.append(f"- Consider setting this as your primary provider for better performance")
    
    # Rerank建议
    if not any("rerank" in r.get("tests", {}) for r in results.values()):
        report.append("\n### Rerank Support:")
        report.append("- No rerank provider configured")
        report.append("- Consider using SiliconFlow for rerank support:")
        report.append("  ```bash")
        report.append("  OPENROUTER_API_BASE=https://api.siliconflow.cn/v1")
        report.append("  OPENROUTER_API_KEY=your_siliconflow_key")
        report.append("  RERANK_PROVIDER=openrouter")
        report.append("  ```")
    
    return "\n".join(report)


def generate_json_summary(results: Dict[str, Any]) -> Dict[str, Any]:
    """生成JSON格式的测试摘要"""
    summary = {
        "timestamp": datetime.now().isoformat(),
        "environment": os.getenv("ENVIRONMENT", "development"),
        "statistics": {},
        "providers": {},
        "configuration": {
            "llm_provider": os.getenv("LLM_PROVIDER"),
            "embedding_provider": os.getenv("EMBEDDING_PROVIDER"),
            "rerank_provider": os.getenv("RERANK_PROVIDER"),
            "llm_fallback": os.getenv("LLM_FALLBACK_CHAIN"),
            "embedding_fallback": os.getenv("EMBEDDING_FALLBACK_CHAIN"),
        }
    }
    
    # 统计数据
    total = len(results)
    working = sum(1 for r in results.values() 
                 if any(t.get("status") == "success" 
                       for t in r.get("tests", {}).values()))
    
    summary["statistics"] = {
        "total_providers": total,
        "working_providers": working,
        "failed_providers": total - working,
        "success_rate": working / total * 100 if total > 0 else 0
    }
    
    # 提供商状态
    for provider, result in results.items():
        tests = result.get("tests", {})
        provider_summary = {
            "status": "working" if any(t.get("status") == "success" for t in tests.values()) else "failed",
            "tests": {}
        }
        
        for test_name, test_result in tests.items():
            provider_summary["tests"][test_name] = {
                "status": test_result.get("status"),
                "response_time": test_result.get("response_time"),
                "error": test_result.get("error") if test_result.get("status") != "success" else None
            }
            
            if test_name == "embedding":
                provider_summary["tests"][test_name]["dimension"] = test_result.get("dimension")
        
        summary["providers"][provider] = provider_summary
    
    return summary


async def generate_test_report():
    """生成测试报告"""
    # 检查是否有测试结果文件
    results_file = "/app/tests/integration/test_results.json"
    
    if not os.path.exists(results_file):
        print("⚠️ No test results found. Running tests first...")
        from test_ai_providers import test_all_providers
        results = await test_all_providers()
    else:
        with open(results_file, 'r') as f:
            results = json.load(f)
        print(f"📂 Loaded test results from: {results_file}")
    
    # 生成Markdown报告
    markdown_report = generate_markdown_report(results)
    markdown_file = "/app/tests/integration/TEST_REPORT.md"
    with open(markdown_file, 'w') as f:
        f.write(markdown_report)
    print(f"📝 Markdown report saved to: {markdown_file}")
    
    # 生成JSON摘要
    json_summary = generate_json_summary(results)
    summary_file = "/app/tests/integration/test_summary.json"
    with open(summary_file, 'w') as f:
        json.dump(json_summary, f, indent=2, ensure_ascii=False)
    print(f"📊 JSON summary saved to: {summary_file}")
    
    # 打印报告到控制台
    print("\n" + "="*80)
    print("📋 TEST SUMMARY")
    print("="*80)
    
    stats = json_summary["statistics"]
    print(f"\n✅ Working Providers: {stats['working_providers']}/{stats['total_providers']}")
    print(f"📊 Success Rate: {stats['success_rate']:.1f}%")
    
    print("\n📈 Provider Status:")
    for provider, info in json_summary["providers"].items():
        status_icon = "✅" if info["status"] == "working" else "❌"
        print(f"  {provider.upper()}: {status_icon}")
    
    return json_summary


if __name__ == "__main__":
    asyncio.run(generate_test_report())