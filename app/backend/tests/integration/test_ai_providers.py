#!/usr/bin/env python3
"""
AI Provider Integration Test Suite
全面测试所有AI提供商的集成功能
"""
import asyncio
import os
import sys
import time
from datetime import datetime
from typing import Dict, Any, Optional, List
import json
import traceback

# Add project root to path
sys.path.insert(0, '/app')

from app.services.ai_service_manager import AIServiceManager
from app.core.config import settings


class AIProviderTester:
    """AI提供商测试器"""
    
    def __init__(self):
        self.ai_service = AIServiceManager()
        self.results = {}
        
    async def test_provider(self, provider: str, test_llm: bool = True, test_embedding: bool = True, test_rerank: bool = False) -> Dict[str, Any]:
        """测试单个提供商的所有功能"""
        print(f"\n{'='*80}")
        print(f"🧪 Testing {provider.upper()} Provider")
        print(f"{'='*80}")
        
        result = {
            "provider": provider,
            "timestamp": datetime.now().isoformat(),
            "tests": {}
        }
        
        # 测试LLM功能
        if test_llm:
            llm_result = await self.test_llm(provider)
            result["tests"]["llm"] = llm_result
            
        # 测试Embedding功能
        if test_embedding:
            embedding_result = await self.test_embedding(provider)
            result["tests"]["embedding"] = embedding_result
            
        # 测试Rerank功能（如果支持）
        if test_rerank and provider in ["openrouter", "siliconflow"]:
            rerank_result = await self.test_rerank(provider)
            result["tests"]["rerank"] = rerank_result
            
        return result
    
    async def test_llm(self, provider: str) -> Dict[str, Any]:
        """测试LLM文本生成功能"""
        print(f"\n📝 Testing {provider} LLM...")
        
        # 临时切换提供商
        original_provider = self.ai_service.llm_provider
        self.ai_service.llm_provider = provider
        
        try:
            prompt = "请用一句话介绍Python语言的主要特点"
            
            start = time.time()
            response = await self.ai_service.generate_text(
                messages=prompt,
                max_tokens=100
            )
            elapsed = (time.time() - start) * 1000
            
            if response and len(response) > 0:
                print(f"✅ LLM SUCCESS ({elapsed:.0f}ms)")
                print(f"   Response: {response[:100]}{'...' if len(response) > 100 else ''}")
                return {
                    "status": "success",
                    "response_time": elapsed,
                    "response_length": len(response)
                }
            else:
                print(f"❌ LLM FAILED: Empty response")
                return {"status": "failed", "error": "Empty response"}
                
        except Exception as e:
            print(f"❌ LLM ERROR: {str(e)}")
            return {"status": "error", "error": str(e)}
        finally:
            # 恢复原始提供商
            self.ai_service.llm_provider = original_provider
    
    async def test_embedding(self, provider: str) -> Dict[str, Any]:
        """测试Embedding向量生成功能"""
        print(f"\n🔤 Testing {provider} Embedding...")
        
        # 临时切换提供商
        original_provider = self.ai_service.embedding_provider
        self.ai_service.embedding_provider = provider
        
        try:
            text = "Python是一种高级编程语言，具有简洁易读的语法特点。"
            
            start = time.time()
            embedding = await self.ai_service.generate_embedding(text)
            elapsed = (time.time() - start) * 1000
            
            if embedding and len(embedding) > 0:
                print(f"✅ Embedding SUCCESS ({elapsed:.0f}ms)")
                print(f"   Dimension: {len(embedding)}")
                return {
                    "status": "success",
                    "response_time": elapsed,
                    "dimension": len(embedding)
                }
            else:
                print(f"❌ Embedding FAILED: Empty result")
                return {"status": "failed", "error": "Empty embedding"}
                
        except Exception as e:
            print(f"❌ Embedding ERROR: {str(e)}")
            return {"status": "error", "error": str(e)}
        finally:
            # 恢复原始提供商
            self.ai_service.embedding_provider = original_provider
    
    async def test_rerank(self, provider: str) -> Dict[str, Any]:
        """测试Rerank重排序功能"""
        print(f"\n🎯 Testing {provider} Rerank...")
        
        # 临时切换提供商
        original_provider = self.ai_service.rerank_provider
        self.ai_service.rerank_provider = provider
        
        try:
            query = "Python开发工程师"
            documents = [
                "需要5年以上Python开发经验",
                "前端工程师，精通React",
                "Python后端工程师，3年经验"
            ]
            
            start = time.time()
            results = await self.ai_service.rerank_documents(
                query=query,
                documents=documents,
                top_k=2
            )
            elapsed = (time.time() - start) * 1000
            
            if results and len(results) > 0:
                print(f"✅ Rerank SUCCESS ({elapsed:.0f}ms)")
                print(f"   Top result: {results[0].get('document', '')[:50]}...")
                return {
                    "status": "success",
                    "response_time": elapsed,
                    "results_count": len(results)
                }
            else:
                print(f"❌ Rerank FAILED: No results")
                return {"status": "failed", "error": "No results"}
                
        except Exception as e:
            print(f"❌ Rerank ERROR: {str(e)}")
            return {"status": "error", "error": str(e)}
        finally:
            # 恢复原始提供商
            self.ai_service.rerank_provider = original_provider


async def test_all_providers():
    """测试所有配置的AI提供商"""
    print("\n" + "="*90)
    print("🚀 AI PROVIDER COMPREHENSIVE TEST SUITE")
    print("="*90)
    print(f"⏰ Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌍 Environment: {os.getenv('ENVIRONMENT', 'development')}")
    
    tester = AIProviderTester()
    all_results = {}
    
    # 定义要测试的提供商
    providers_to_test = [
        ("deepseek", True, True, False),
        ("zhipu", True, True, False),
        ("moonshot", True, False, False),  # Moonshot不支持embedding
        ("qwen", True, True, False),
        ("ollama", True, True, False),
    ]
    
    # 检查OpenRouter/SiliconFlow配置
    openrouter_api_base = os.getenv("OPENROUTER_API_BASE", "")
    if "siliconflow" in openrouter_api_base.lower():
        providers_to_test.append(("openrouter", True, True, True))  # SiliconFlow支持rerank
    elif openrouter_api_base:
        providers_to_test.append(("openrouter", True, True, False))
    
    # 测试每个提供商
    success_count = 0
    failed_count = 0
    
    for provider, test_llm, test_embedding, test_rerank in providers_to_test:
        try:
            result = await tester.test_provider(provider, test_llm, test_embedding, test_rerank)
            all_results[provider] = result
            
            # 统计成功/失败
            for test_name, test_result in result["tests"].items():
                if test_result.get("status") == "success":
                    success_count += 1
                else:
                    failed_count += 1
                    
        except Exception as e:
            print(f"\n❌ Failed to test {provider}: {e}")
            all_results[provider] = {"error": str(e)}
            failed_count += 1
    
    # 生成测试报告
    print("\n" + "="*90)
    print("📊 TEST SUMMARY REPORT")
    print("="*90)
    
    print(f"\n📈 Overall Statistics:")
    print(f"   Total Tests: {success_count + failed_count}")
    print(f"   ✅ Successful: {success_count}")
    print(f"   ❌ Failed: {failed_count}")
    print(f"   Success Rate: {success_count/(success_count+failed_count)*100:.1f}%")
    
    print(f"\n📋 Provider Status:")
    print("-"*50)
    
    for provider, result in all_results.items():
        if "error" in result:
            print(f"\n{provider.upper()}: ❌ ERROR")
            print(f"  {result['error']}")
        else:
            print(f"\n{provider.upper()}:")
            for test_name, test_result in result.get("tests", {}).items():
                status = test_result.get("status")
                if status == "success":
                    time_ms = test_result.get("response_time", 0)
                    print(f"  {test_name}: ✅ ({time_ms:.0f}ms)")
                    if test_name == "embedding":
                        print(f"    Dimension: {test_result.get('dimension', 'N/A')}")
                else:
                    print(f"  {test_name}: ❌ {test_result.get('error', 'Failed')}")
    
    # 保存详细结果
    results_file = "/app/tests/integration/test_results.json"
    os.makedirs(os.path.dirname(results_file), exist_ok=True)
    with open(results_file, 'w') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False, default=str)
    print(f"\n💾 Detailed results saved to: {results_file}")
    
    print("\n" + "="*90)
    print("✨ TEST COMPLETE")
    print("="*90)
    
    return all_results


async def test_minimal():
    """最小化测试 - 只测试当前配置的提供商"""
    print("\n" + "="*90)
    print("⚡ MINIMAL PROVIDER TEST")
    print("="*90)
    
    ai_service = AIServiceManager()
    
    print(f"\n📋 Current Configuration:")
    print(f"   LLM Provider: {ai_service.llm_provider}")
    print(f"   Embedding Provider: {ai_service.embedding_provider}")
    print(f"   Rerank Provider: {ai_service.rerank_provider}")
    
    # 测试当前LLM
    print(f"\n📝 Testing Current LLM ({ai_service.llm_provider})...")
    try:
        response = await ai_service.generate_text(
            messages="Hello, what's your name?",
            max_tokens=50
        )
        if response:
            print(f"✅ LLM Working: {response[:100]}")
        else:
            print("❌ LLM Failed: Empty response")
    except Exception as e:
        print(f"❌ LLM Error: {e}")
    
    # 测试当前Embedding
    print(f"\n🔤 Testing Current Embedding ({ai_service.embedding_provider})...")
    try:
        embedding = await ai_service.generate_embedding("Test text")
        if embedding:
            print(f"✅ Embedding Working: {len(embedding)} dimensions")
        else:
            print("❌ Embedding Failed: Empty result")
    except Exception as e:
        print(f"❌ Embedding Error: {e}")
    
    # 测试当前Rerank（如果配置）
    if ai_service.rerank_provider and ai_service.rerank_provider != "none":
        print(f"\n🎯 Testing Current Rerank ({ai_service.rerank_provider})...")
        try:
            results = await ai_service.rerank_documents(
                query="Python",
                documents=["Python developer", "Java developer"],
                top_k=1
            )
            if results:
                print(f"✅ Rerank Working: {len(results)} results")
            else:
                print("❌ Rerank Failed: No results")
        except Exception as e:
            print(f"❌ Rerank Error: {e}")
    
    print("\n✨ Minimal Test Complete")


async def test_ollama_specific():
    """Ollama特定模型测试"""
    print("\n" + "="*90)
    print("🦙 OLLAMA SPECIFIC MODEL TEST")
    print("="*90)
    
    ollama_host = os.getenv("OLLAMA_HOST", "http://ollama:11434")
    
    # 测试已配置的模型
    models_to_test = {
        "llm": os.getenv("OLLAMA_LLM_MODEL", "gemma3:4b"),
        "embedding": os.getenv("OLLAMA_EMBEDDING_MODEL", "bge-m3:latest"),
        "rerank": os.getenv("OLLAMA_RERANK_MODEL", "")
    }
    
    print(f"\n📋 Ollama Configuration:")
    print(f"   Host: {ollama_host}")
    print(f"   LLM Model: {models_to_test['llm']}")
    print(f"   Embedding Model: {models_to_test['embedding']}")
    print(f"   Rerank Model: {models_to_test['rerank'] or 'Not configured'}")
    
    # 检查Ollama连接
    try:
        import httpx
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{ollama_host}/api/tags")
            if response.status_code == 200:
                available_models = response.json().get("models", [])
                print(f"\n📦 Available Models in Ollama:")
                for model in available_models:
                    print(f"   - {model['name']} ({model.get('size', 'N/A')})")
            else:
                print(f"⚠️ Could not fetch Ollama models: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ Failed to connect to Ollama: {e}")
        return
    
    # 测试LLM模型
    if models_to_test["llm"]:
        print(f"\n🧪 Testing Ollama LLM: {models_to_test['llm']}")
        try:
            from ollama import AsyncClient
            client = AsyncClient(host=ollama_host)
            response = await client.chat(
                model=models_to_test["llm"],
                messages=[{"role": "user", "content": "Say hello in 5 words"}]
            )
            if response and response.get("message"):
                print(f"✅ LLM Response: {response['message']['content']}")
            else:
                print("❌ LLM Failed: Empty response")
        except Exception as e:
            print(f"❌ LLM Error: {e}")
    
    # 测试Embedding模型
    if models_to_test["embedding"]:
        print(f"\n🧪 Testing Ollama Embedding: {models_to_test['embedding']}")
        try:
            from ollama import AsyncClient
            client = AsyncClient(host=ollama_host)
            response = await client.embeddings(
                model=models_to_test["embedding"],
                prompt="Test embedding"
            )
            if response and response.get("embedding"):
                print(f"✅ Embedding Dimension: {len(response['embedding'])}")
            else:
                print("❌ Embedding Failed: Empty result")
        except Exception as e:
            print(f"❌ Embedding Error: {e}")
    
    print("\n✨ Ollama Test Complete")


# 主程序入口
async def main():
    """主测试入口 - 根据参数选择测试模式"""
    import sys
    
    if len(sys.argv) > 1:
        mode = sys.argv[1]
        
        if mode == "all":
            await test_all_providers()
        elif mode == "minimal":
            await test_minimal()
        elif mode == "ollama":
            await test_ollama_specific()
        else:
            print(f"Unknown mode: {mode}")
            print("Usage: python test_ai_providers.py [all|minimal|ollama]")
    else:
        # 默认运行全面测试
        await test_all_providers()


if __name__ == "__main__":
    asyncio.run(main())