"""
Integration Tests for Enhanced AIServiceManager
Tests real provider interactions, end-to-end workflows, and system integration
"""
import pytest
import pytest_asyncio
import asyncio
import time
import json
from typing import Dict, Any, List
from unittest.mock import patch, AsyncMock, MagicMock

from app.services.ai_service_manager import (
    AIServiceManager, 
    ai_service_manager,
    AllProvidersFailedError,
    ProviderError
)
from app.core.ai_config import ai_settings
from app.core.redis import get_redis


@pytest.mark.integration
class TestAIServiceManagerIntegration:
    """Integration tests with real-like provider interactions"""

    @pytest.fixture(scope="class")
    async def redis_client(self):
        """Get Redis client for testing"""
        redis = await get_redis()
        yield redis
        await redis.close()

    @pytest.fixture
    def integration_manager(self):
        """Get AI service manager for integration testing"""
        return ai_service_manager

    @pytest.mark.asyncio
    async def test_text_generation_integration_flow(self, integration_manager):
        """Test complete text generation flow with mocked providers"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            # Mock OpenAI client
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            # Mock successful response
            mock_response = AsyncMock()
            mock_response.choices = [
                AsyncMock(message=AsyncMock(content="Integration test response"))
            ]
            mock_client.chat.completions.create.return_value = mock_response
            
            # Test the complete flow
            result = await integration_manager.generate_text(
                "Generate a test response for integration testing",
                temperature=0.7,
                max_tokens=100,
                use_cache=True
            )
            
            assert isinstance(result, str)
            assert len(result) > 0
            assert "Integration test response" == result

    @pytest.mark.asyncio
    async def test_embedding_generation_integration_flow(self, integration_manager):
        """Test complete embedding generation flow with caching"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            # Mock embedding response
            test_embedding = [0.1] * 1024  # Standard embedding size
            mock_response = AsyncMock()
            mock_response.data = [AsyncMock(embedding=test_embedding)]
            mock_client.embeddings.create.return_value = mock_response
            
            # First call - should hit the API
            result1 = await integration_manager.generate_embedding(
                "Integration test text for embedding",
                use_cache=True
            )
            
            assert isinstance(result1, list)
            assert len(result1) == 1024
            assert all(isinstance(x, float) for x in result1)
            
            # Second call with same text - should hit cache
            with patch.object(integration_manager, '_get_cached_result') as mock_cache:
                mock_cache.return_value = test_embedding
                
                result2 = await integration_manager.generate_embedding(
                    "Integration test text for embedding",
                    use_cache=True
                )
                
                assert result2 == test_embedding
                mock_cache.assert_called_once()

    @pytest.mark.asyncio
    async def test_document_reranking_integration_flow(self, integration_manager):
        """Test complete document reranking flow with similarity fallback"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            # Mock embeddings for similarity calculation
            query_embedding = [1.0, 0.0, 0.0, 0.0]
            doc1_embedding = [0.8, 0.6, 0.0, 0.0]  # High similarity
            doc2_embedding = [0.0, 0.0, 1.0, 0.0]  # Low similarity
            doc3_embedding = [0.9, 0.4, 0.0, 0.0]  # Medium similarity
            
            # Mock embedding API calls
            embedding_responses = [
                AsyncMock(data=[AsyncMock(embedding=query_embedding)]),
                AsyncMock(data=[AsyncMock(embedding=doc1_embedding)]),
                AsyncMock(data=[AsyncMock(embedding=doc2_embedding)]),
                AsyncMock(data=[AsyncMock(embedding=doc3_embedding)])
            ]
            mock_client.embeddings.create.side_effect = embedding_responses
            
            documents = [
                "Python is a programming language",
                "Cats are domestic animals",
                "JavaScript runs in browsers"
            ]
            
            result = await integration_manager.rerank_documents(
                query="programming languages",
                documents=documents,
                top_k=3,
                use_cache=False
            )
            
            assert isinstance(result, list)
            assert len(result) <= 3
            assert all("document" in item for item in result)
            assert all("score" in item for item in result)
            assert all("index" in item for item in result)
            
            # Results should be sorted by score (descending)
            scores = [item["score"] for item in result]
            assert scores == sorted(scores, reverse=True)

    @pytest.mark.asyncio
    async def test_provider_fallback_integration(self, integration_manager):
        """Test provider fallback mechanism in integration scenario"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.ai_settings') as mock_settings:
            
            # Mock fallback chain
            mock_settings.get_llm_fallback_chain.return_value = ["zhipu", "moonshot", "deepseek"]
            
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            # Mock primary provider failure, fallback success
            call_count = 0
            def mock_create_completion(*args, **kwargs):
                nonlocal call_count
                call_count += 1
                if call_count == 1:
                    raise Exception("Primary provider failed")
                return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Fallback success"))])
            
            mock_client.chat.completions.create.side_effect = mock_create_completion
            
            result = await integration_manager.generate_text(
                "Test fallback integration",
                use_cache=False
            )
            
            assert result == "Fallback success"
            assert call_count == 2  # Primary attempt + fallback

    @pytest.mark.asyncio
    async def test_redis_caching_integration(self, integration_manager, redis_client):
        """Test Redis caching integration with real cache operations"""
        
        cache_key = "test:integration:cache"
        test_data = {"integration": "test", "timestamp": time.time()}
        
        # Test cache set
        await integration_manager._set_cached_result(cache_key, test_data, ttl=60)
        
        # Test cache get
        cached_result = await integration_manager._get_cached_result(cache_key)
        
        assert cached_result == test_data
        
        # Verify in Redis directly
        raw_cached = await redis_client.get(cache_key)
        assert raw_cached is not None
        assert json.loads(raw_cached) == test_data
        
        # Clean up
        await redis_client.delete(cache_key)

    @pytest.mark.asyncio
    async def test_health_monitoring_integration(self, integration_manager):
        """Test complete health monitoring integration"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.ollama') as mock_ollama:
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            # Mock successful health responses
            mock_openai_client.chat.completions.create.return_value = AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="health_check"))]
            )
            
            mock_ollama.list.return_value = {"models": []}
            
            # Test comprehensive health check
            start_time = time.time()
            health_result = await integration_manager.check_service_method_health()
            check_duration = time.time() - start_time
            
            assert "status" in health_result
            assert health_result["status"] in ["healthy", "degraded", "unhealthy"]
            assert "service_methods" in health_result
            assert "providers" in health_result
            assert "total_check_time_ms" in health_result
            
            # Verify service methods were tested
            service_methods = health_result["service_methods"]
            expected_methods = ["generate_text", "generate_embedding", "rerank_documents"]
            for method in expected_methods:
                assert method in service_methods
                method_result = service_methods[method]
                assert "status" in method_result
                
            # Check should complete reasonably quickly
            assert check_duration < 10.0  # Should complete within 10 seconds

    @pytest.mark.asyncio
    async def test_concurrent_operations_integration(self, integration_manager):
        """Test concurrent operations integration with proper resource management"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            # Mock responses for concurrent requests
            text_responses = [
                AsyncMock(choices=[AsyncMock(message=AsyncMock(content=f"Response {i}"))])
                for i in range(10)
            ]
            embedding_responses = [
                AsyncMock(data=[AsyncMock(embedding=[0.1] * 512)])
                for i in range(5)
            ]
            
            mock_client.chat.completions.create.side_effect = text_responses
            mock_client.embeddings.create.side_effect = embedding_responses
            
            # Create mixed concurrent operations
            text_tasks = [
                integration_manager.generate_text(f"Text request {i}", use_cache=False)
                for i in range(10)
            ]
            
            embedding_tasks = [
                integration_manager.generate_embedding(f"Embedding text {i}", use_cache=False)
                for i in range(5)
            ]
            
            # Execute all tasks concurrently
            start_time = time.time()
            text_results, embedding_results = await asyncio.gather(
                asyncio.gather(*text_tasks),
                asyncio.gather(*embedding_tasks)
            )
            concurrent_duration = time.time() - start_time
            
            # Verify results
            assert len(text_results) == 10
            assert len(embedding_results) == 5
            assert all(isinstance(result, str) for result in text_results)
            assert all(isinstance(result, list) for result in embedding_results)
            
            # Concurrent execution should be efficient
            assert concurrent_duration < 5.0  # Should complete within 5 seconds

    @pytest.mark.asyncio
    async def test_error_recovery_integration(self, integration_manager):
        """Test error recovery and resilience integration"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.ai_settings') as mock_settings:
            
            mock_settings.get_llm_fallback_chain.return_value = ["zhipu", "moonshot"]
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            # Test scenario: intermittent failures then success
            failure_count = 0
            def failing_then_success(*args, **kwargs):
                nonlocal failure_count
                failure_count += 1
                if failure_count <= 2:  # Fail first 2 attempts
                    raise Exception(f"Temporary failure {failure_count}")
                return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Recovery success"))])
            
            mock_client.chat.completions.create.side_effect = failing_then_success
            
            # Should eventually succeed through retry mechanism
            result = await integration_manager.generate_text(
                "Test error recovery",
                max_retries=3,
                use_cache=False
            )
            
            assert result == "Recovery success"
            assert failure_count == 3  # 2 failures + 1 success

    @pytest.mark.asyncio
    async def test_performance_monitoring_integration(self, integration_manager):
        """Test performance monitoring and metrics integration"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            # Mock varying response times
            async def slow_response(*args, **kwargs):
                await asyncio.sleep(0.1)  # 100ms delay
                return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Slow response"))])
            
            async def fast_response(*args, **kwargs):
                return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Fast response"))])
            
            mock_client.chat.completions.create.side_effect = [slow_response, fast_response]
            
            # Test performance tracking
            with patch.object(integration_manager, '_log_operation_metrics') as mock_metrics:
                # Slow operation
                await integration_manager.generate_text("Slow request", use_cache=False)
                
                # Fast operation  
                await integration_manager.generate_text("Fast request", use_cache=False)
                
                # Verify metrics were logged
                assert mock_metrics.call_count == 2
                
                # Check that different durations were recorded
                first_call = mock_metrics.call_args_list[0][0]
                second_call = mock_metrics.call_args_list[1][0]
                
                # First call should have higher duration
                assert first_call[3] > second_call[3]  # duration_ms parameter

    @pytest.mark.asyncio
    async def test_configuration_integration(self, integration_manager):
        """Test integration with AI configuration settings"""
        
        # Test that manager respects configuration
        assert integration_manager.llm_provider == ai_settings.LLM_PROVIDER
        assert integration_manager.embedding_provider == ai_settings.EMBEDDING_PROVIDER
        assert integration_manager.rerank_provider == ai_settings.RERANK_PROVIDER
        
        # Test provider availability
        available_providers = integration_manager.get_available_providers()
        assert isinstance(available_providers, list)
        assert len(available_providers) > 0
        
        # Test provider config retrieval
        for provider in available_providers:
            config = integration_manager.get_provider_config(provider)
            if config:  # Some providers might not be configured
                assert isinstance(config, dict)
                assert "llm_model" in config or "embedding_model" in config

    @pytest.mark.asyncio
    async def test_cache_performance_integration(self, integration_manager):
        """Test cache performance benefits in integration scenario"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            # Mock slow API response
            async def slow_api(*args, **kwargs):
                await asyncio.sleep(0.2)  # 200ms delay
                return AsyncMock(data=[AsyncMock(embedding=[0.1] * 1024)])
            
            mock_client.embeddings.create.side_effect = slow_api
            
            test_text = "Performance test text for caching"
            
            # First call - should be slow (cache miss)
            start_time = time.time()
            result1 = await integration_manager.generate_embedding(test_text, use_cache=True)
            first_call_time = time.time() - start_time
            
            # Second call - should be fast (cache hit)
            start_time = time.time()
            result2 = await integration_manager.generate_embedding(test_text, use_cache=True)
            second_call_time = time.time() - start_time
            
            # Verify results are identical
            assert result1 == result2
            
            # Cache should provide significant performance improvement
            assert second_call_time < first_call_time / 2  # At least 2x faster

    @pytest.mark.asyncio
    async def test_real_world_workflow_integration(self, integration_manager):
        """Test complete real-world workflow integration"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            # Mock responses for a complete workflow
            mock_client.chat.completions.create.return_value = AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="Generated analysis"))]
            )
            
            mock_client.embeddings.create.side_effect = [
                AsyncMock(data=[AsyncMock(embedding=[1.0, 0.0, 0.0])]),  # Query embedding
                AsyncMock(data=[AsyncMock(embedding=[0.8, 0.6, 0.0])]),  # Doc 1
                AsyncMock(data=[AsyncMock(embedding=[0.2, 0.9, 0.0])]),  # Doc 2
                AsyncMock(data=[AsyncMock(embedding=[0.1, 0.1, 0.9])])   # Doc 3
            ]
            
            # Simulate a complete workflow
            # Step 1: Generate text analysis
            analysis = await integration_manager.generate_text(
                "Analyze the following documents for relevance",
                temperature=0.3,
                max_tokens=200
            )
            
            # Step 2: Generate embeddings for documents  
            documents = [
                "Machine learning with Python",
                "Web development using FastAPI",
                "Database design principles"
            ]
            
            # Step 3: Rerank documents
            ranked_docs = await integration_manager.rerank_documents(
                query="Python programming",
                documents=documents,
                top_k=2
            )
            
            # Verify complete workflow
            assert isinstance(analysis, str)
            assert len(analysis) > 0
            
            assert isinstance(ranked_docs, list)
            assert len(ranked_docs) <= 2
            assert all("document" in doc for doc in ranked_docs)
            assert all("score" in doc for doc in ranked_docs)
            
            # Verify ranking order
            if len(ranked_docs) > 1:
                assert ranked_docs[0]["score"] >= ranked_docs[1]["score"]


@pytest.mark.integration 
@pytest.mark.slow
class TestAIServiceManagerStressIntegration:
    """Stress tests for AI service manager under load"""

    @pytest.fixture
    def stress_manager(self):
        """Get manager for stress testing"""
        return ai_service_manager

    @pytest.mark.asyncio
    async def test_high_concurrency_stress(self, stress_manager):
        """Test high concurrency stress scenario"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            # Mock responses for high concurrency
            text_responses = [
                AsyncMock(choices=[AsyncMock(message=AsyncMock(content=f"Response {i}"))])
                for i in range(100)
            ]
            mock_client.chat.completions.create.side_effect = text_responses
            
            # Create 100 concurrent requests
            tasks = [
                stress_manager.generate_text(f"Stress test message {i}", use_cache=False)
                for i in range(100)
            ]
            
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            duration = time.time() - start_time
            
            # Count successful vs failed requests
            successful = [r for r in results if isinstance(r, str)]
            failed = [r for r in results if isinstance(r, Exception)]
            
            # Should handle high concurrency reasonably
            success_rate = len(successful) / len(results)
            assert success_rate > 0.8  # At least 80% success rate
            
            # Should complete within reasonable time
            assert duration < 30.0  # 30 seconds max

    @pytest.mark.asyncio
    async def test_memory_usage_stress(self, stress_manager):
        """Test memory usage under stress conditions"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            # Mock large embedding responses
            large_embedding = [0.1] * 4096  # Large embedding
            mock_client.embeddings.create.return_value = AsyncMock(
                data=[AsyncMock(embedding=large_embedding)]
            )
            
            # Generate many large embeddings
            large_texts = [f"Large text content for embedding {i}" * 100 for i in range(50)]
            
            tasks = [
                stress_manager.generate_embedding(text, use_cache=False)
                for text in large_texts
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Should handle large payloads
            successful = [r for r in results if isinstance(r, list)]
            assert len(successful) > len(results) * 0.8  # 80% success rate

    @pytest.mark.asyncio
    async def test_sustained_load_stress(self, stress_manager):
        """Test sustained load over time"""
        
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            mock_client = AsyncMock()
            mock_openai.return_value = mock_client
            
            mock_client.chat.completions.create.return_value = AsyncMock(
                choices=[AsyncMock(message=AsyncMock(content="Sustained response"))]
            )
            
            # Run sustained load for multiple rounds
            total_successful = 0
            total_requests = 0
            
            for round_num in range(10):  # 10 rounds
                tasks = [
                    stress_manager.generate_text(
                        f"Round {round_num} message {i}", 
                        use_cache=False
                    )
                    for i in range(20)  # 20 requests per round
                ]
                
                results = await asyncio.gather(*tasks, return_exceptions=True)
                
                successful_this_round = len([r for r in results if isinstance(r, str)])
                total_successful += successful_this_round
                total_requests += len(results)
                
                # Small delay between rounds
                await asyncio.sleep(0.1)
            
            success_rate = total_successful / total_requests
            assert success_rate > 0.9  # High success rate under sustained load


@pytest.mark.integration
@pytest.mark.redis_required
class TestAIServiceManagerRedisIntegration:
    """Integration tests specifically for Redis caching functionality"""

    @pytest.fixture(scope="class")
    async def redis_client(self):
        """Get Redis client for testing"""
        redis = await get_redis()
        yield redis
        await redis.close()

    @pytest.fixture
    def manager_with_redis(self):
        """Get manager for Redis testing"""
        return ai_service_manager

    @pytest.mark.asyncio
    async def test_cache_key_consistency_integration(self, manager_with_redis, redis_client):
        """Test cache key consistency across operations"""
        
        # Generate cache keys for same parameters
        params = {"text": "test", "provider": "zhipu", "model": "embedding"}
        
        key1 = manager_with_redis._generate_cache_key("embedding", **params)
        key2 = manager_with_redis._generate_cache_key("embedding", **params)
        
        assert key1 == key2
        
        # Different parameters should generate different keys
        different_params = {**params, "provider": "moonshot"}
        key3 = manager_with_redis._generate_cache_key("embedding", **different_params)
        
        assert key1 != key3

    @pytest.mark.asyncio
    async def test_cache_ttl_integration(self, manager_with_redis, redis_client):
        """Test TTL functionality integration with Redis"""
        
        cache_key = "test:ttl:integration"
        test_data = {"ttl": "test"}
        
        # Set with short TTL
        await manager_with_redis._set_cached_result(cache_key, test_data, ttl=2)
        
        # Should be available immediately
        result1 = await manager_with_redis._get_cached_result(cache_key)
        assert result1 == test_data
        
        # Wait for TTL expiration
        await asyncio.sleep(3)
        
        # Should be expired
        result2 = await manager_with_redis._get_cached_result(cache_key)
        assert result2 is None

    @pytest.mark.asyncio
    async def test_cache_concurrent_access_integration(self, manager_with_redis, redis_client):
        """Test concurrent cache access integration"""
        
        cache_key = "test:concurrent:integration"
        test_data = {"concurrent": "test"}
        
        # Concurrent set and get operations
        async def set_operation():
            await manager_with_redis._set_cached_result(cache_key, test_data, ttl=60)
        
        async def get_operation():
            await asyncio.sleep(0.01)  # Small delay
            return await manager_with_redis._get_cached_result(cache_key)
        
        # Run concurrent operations
        set_task = asyncio.create_task(set_operation())
        get_tasks = [asyncio.create_task(get_operation()) for _ in range(10)]
        
        await set_task
        results = await asyncio.gather(*get_tasks, return_exceptions=True)
        
        # Most get operations should succeed (after the set completes)
        successful_gets = [r for r in results if r == test_data or r is None]
        assert len(successful_gets) == len(results)  # No exceptions
        
        # Clean up
        await redis_client.delete(cache_key)