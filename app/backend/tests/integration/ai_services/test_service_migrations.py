"""
Integration tests for migrated AI services
"""
import pytest
import pytest_asyncio
from unittest.mock import patch, AsyncMock, MagicMock
from typing import Dict, Any, List
import json

from tests.fixtures.ai_mocks import (
    MockAIServiceManager,
    create_mock_ai_service_manager,
    MOCK_RESUME_EXTRACTION,
    MOCK_QUESTIONNAIRE,
    MOCK_EVALUATION
)


class TestChatServiceIntegration:
    """Test chat_service.py migration"""
    
    @pytest.fixture
    def mock_ai_manager(self):
        return create_mock_ai_service_manager()
    
    @pytest.mark.asyncio
    @patch('app.services.chat_service.ai_service_manager')
    async def test_chat_service_uses_ai_manager(self, mock_manager):
        """Test that chat_service uses AIServiceManager"""
        mock_manager.create_completion = AsyncMock(return_value="Test response")
        
        from app.services.chat_service import ChatService
        
        service = ChatService()
        # Verify service has ai_manager attribute
        assert hasattr(service, 'ai_manager')
        
        # Test a chat completion
        messages = [{"role": "user", "content": "Hello"}]
        
        # Mock the service's ai_manager
        service.ai_manager = mock_manager
        response = await service.ai_manager.create_completion(messages)
        
        assert response == "Test response"
        mock_manager.create_completion.assert_called_once()


class TestResumeParserIntegration:
    """Test resume_parser.py migration"""
    
    @pytest.mark.asyncio
    @patch('app.services.resume_parser.ai_service_manager')
    async def test_resume_parser_uses_ai_manager(self, mock_manager):
        """Test that resume_parser uses AIServiceManager"""
        mock_manager.create_completion = AsyncMock(return_value=MOCK_RESUME_EXTRACTION)
        
        from app.services.resume_parser import ResumeParserService
        
        service = ResumeParserService()
        
        # Mock the extract method to use ai_manager
        service.ai_manager = mock_manager
        
        # Test extraction
        test_text = "John Doe, Software Engineer with 5 years experience"
        result = await mock_manager.create_completion(
            [{"role": "user", "content": f"Extract resume: {test_text}"}],
            response_format="json"
        )
        
        assert result == MOCK_RESUME_EXTRACTION
        assert result["name"] == "Jane Smith"
        assert result["years_of_experience"] == 8
        mock_manager.create_completion.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('app.services.resume_parser.ai_service_manager')
    async def test_resume_parser_fallback_chain(self, mock_manager):
        """Test resume parser's 3-tier fallback: Primary → Fallback → Regex"""
        mock_ai = create_mock_ai_service_manager()
        
        # First call fails (trigger fallback to second provider)
        mock_ai.set_provider_fail_rate("deepseek", 1.0)
        
        # Second provider succeeds
        result = await mock_ai.create_completion(
            [{"role": "user", "content": "Extract resume"}],
            response_format="json",
            use_fallback=True
        )
        
        assert result is not None
        # Should have tried primary then fallback
        assert len(mock_ai.call_history) == 2
        assert mock_ai.call_history[1]["method"] == "create_completion_fallback"


class TestAssessmentServiceIntegration:
    """Test assessment_service_enhanced.py migration"""
    
    @pytest.mark.asyncio
    @patch('app.services.assessment_service_enhanced.ai_service_manager')
    async def test_assessment_service_uses_ai_manager(self, mock_manager):
        """Test that assessment service uses AIServiceManager"""
        mock_manager.create_completion = AsyncMock(return_value={
            "assessment": "Strong candidate",
            "score": 85
        })
        
        from app.services.assessment_service_enhanced import AssessmentService
        
        service = AssessmentService()
        service.ai_manager = mock_manager
        
        # Test assessment generation
        result = await mock_manager.create_completion(
            [{"role": "user", "content": "Assess candidate"}],
            response_format="json"
        )
        
        assert result["score"] == 85
        mock_manager.create_completion.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('app.services.assessment_service_enhanced.ai_service_manager') 
    async def test_assessment_fallback_support(self, mock_manager):
        """Test assessment service fallback functionality"""
        mock_ai = create_mock_ai_service_manager()
        
        # Make primary fail
        mock_ai.set_provider_fail_rate("deepseek", 1.0)
        
        # Should fall back to secondary provider
        result = await mock_ai.create_completion(
            [{"role": "user", "content": "Generate assessment"}],
            response_format="json",
            use_fallback=True
        )
        
        assert result is not None
        assert len(mock_ai.call_history) == 2


class TestRecommendationEngineIntegration:
    """Test recommendation_engine.py migration"""
    
    @pytest.mark.asyncio
    @patch('app.services.recommendation_engine.ai_service_manager')
    async def test_recommendation_engine_uses_ai_manager(self, mock_manager):
        """Test that recommendation engine uses AIServiceManager"""
        mock_manager.create_completion = AsyncMock(return_value={
            "recommendations": ["Candidate A", "Candidate B"],
            "reasoning": "Based on skills match"
        })
        
        from app.services.recommendation_engine import RecommendationEngine
        
        engine = RecommendationEngine()
        engine.ai_manager = mock_manager
        
        # Test recommendation generation
        result = await mock_manager.create_completion(
            [{"role": "user", "content": "Generate recommendations"}],
            response_format="json"
        )
        
        assert "recommendations" in result
        assert len(result["recommendations"]) == 2
        mock_manager.create_completion.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('app.services.recommendation_engine.ai_service_manager')
    async def test_recommendation_dynamic_client(self, mock_manager):
        """Test dynamic client acquisition in recommendation engine"""
        mock_ai = create_mock_ai_service_manager()
        
        # Test that it can switch providers dynamically
        mock_ai.current_provider = "openai"
        
        result = await mock_ai.create_completion(
            [{"role": "user", "content": "Test"}]
        )
        
        assert result is not None
        assert mock_ai.call_history[0]["provider"] == "openai"


class TestVectorServiceIntegration:
    """Test vector_service.py migration"""
    
    @pytest.mark.asyncio
    @patch('app.services.vector_service.ai_service_manager')
    async def test_vector_service_uses_ai_manager(self, mock_manager):
        """Test that vector service uses AIServiceManager for embeddings"""
        mock_embedding = [0.1] * 1024  # BGE-M3 dimension
        mock_manager.create_embedding = AsyncMock(return_value=mock_embedding)
        
        from app.services.vector_service import VectorService
        
        service = VectorService()
        service.ai_manager = mock_manager
        
        # Test embedding generation
        text = "Test document for embedding"
        embedding = await mock_manager.create_embedding(text)
        
        assert embedding == mock_embedding
        assert len(embedding) == 1024
        mock_manager.create_embedding.assert_called_once_with(text)
    
    @pytest.mark.asyncio
    @patch('app.services.vector_service.ai_service_manager')
    async def test_vector_service_fallback(self, mock_manager):
        """Test vector service embedding fallback"""
        mock_ai = create_mock_ai_service_manager()
        
        # Make primary embedding provider fail
        mock_ai.set_provider_fail_rate("deepseek", 1.0)
        
        text = "Test document"
        embedding = await mock_ai.create_embedding(text, use_fallback=True)
        
        assert embedding is not None
        assert len(embedding) == 1024
        assert len(mock_ai.call_history) == 2
        assert mock_ai.call_history[1]["method"] == "create_embedding_fallback"


class TestLLMParserIntegration:
    """Test parser/llm_parser.py migration"""
    
    @pytest.mark.asyncio
    @patch('app.services.parser.llm_parser.ai_service_manager')
    async def test_llm_parser_uses_ai_manager(self, mock_manager):
        """Test that LLM parser uses AIServiceManager"""
        mock_manager.create_completion = AsyncMock(return_value=MOCK_RESUME_EXTRACTION)
        mock_manager.is_available = MagicMock(return_value=True)
        
        from app.services.parser.llm_parser import LLMEnhancedParser
        
        parser = LLMEnhancedParser()
        parser.ai_manager = mock_manager
        
        # Test parsing with AI
        assert parser._is_ai_available() is True
        
        # Test AI parsing
        result = await mock_manager.create_completion(
            [{"role": "user", "content": "Parse resume"}],
            response_format="json"
        )
        
        assert result == MOCK_RESUME_EXTRACTION
        mock_manager.create_completion.assert_called_once()


class TestEvaluationServiceIntegration:
    """Test evaluation_service.py migration"""
    
    @pytest.mark.asyncio
    @patch('app.services.evaluation_service.ai_service_manager')
    async def test_evaluation_service_uses_ai_manager(self, mock_manager):
        """Test that evaluation service uses AIServiceManager"""
        mock_manager.evaluate_responses = AsyncMock(return_value=MOCK_EVALUATION)
        mock_manager.evaluate_text_response = AsyncMock(return_value={
            "score": 90,
            "feedback": "Excellent response"
        })
        
        # Test evaluate_responses
        result = await mock_manager.evaluate_responses(
            questionnaire_title="Test",
            position_type="Engineer",
            dimension_scores={},
            answers_summary=""
        )
        
        assert result == MOCK_EVALUATION
        assert result["score"] == 88
        
        # Test evaluate_text_response
        text_result = await mock_manager.evaluate_text_response(
            question="What is Python?",
            response="A programming language"
        )
        
        assert text_result["score"] == 90
        mock_manager.evaluate_text_response.assert_called_once()


class TestGenerationServiceIntegration:
    """Test generation_service.py migration"""
    
    @pytest.mark.asyncio
    @patch('app.services.generation_service.ai_service_manager')
    async def test_generation_service_uses_ai_manager(self, mock_manager):
        """Test that generation service uses AIServiceManager"""
        mock_manager.generate_questionnaire = AsyncMock(return_value=MOCK_QUESTIONNAIRE)
        
        # Test questionnaire generation
        result = await mock_manager.generate_questionnaire(
            position_type="Software Engineer",
            dimensions=["Technical", "Communication"],
            question_count=10,
            industry="Technology"
        )
        
        assert result == MOCK_QUESTIONNAIRE
        assert result["title"] == "Software Engineering Assessment"
        assert len(result["questions"]) == 2
        mock_manager.generate_questionnaire.assert_called_once()


class TestCrossServiceIntegration:
    """Test interaction between multiple migrated services"""
    
    @pytest.mark.asyncio
    async def test_resume_to_assessment_flow(self):
        """Test flow from resume parsing to assessment generation"""
        mock_ai = create_mock_ai_service_manager()
        
        # Step 1: Parse resume
        resume_result = await mock_ai.create_completion(
            [{"role": "user", "content": "Extract resume information"}],
            response_format="json"
        )
        
        assert "name" in resume_result
        
        # Step 2: Generate embeddings for the resume
        embedding = await mock_ai.create_embedding(
            f"Resume for {resume_result.get('name', 'Unknown')}"
        )
        
        assert len(embedding) == 1024
        
        # Step 3: Generate assessment based on resume
        assessment = await mock_ai.create_completion(
            [{"role": "user", "content": f"Assess candidate: {json.dumps(resume_result)}"}],
            response_format="json"
        )
        
        assert "score" in assessment
        
        # Verify all services were called
        assert len(mock_ai.call_history) == 3
    
    @pytest.mark.asyncio
    async def test_questionnaire_to_evaluation_flow(self):
        """Test flow from questionnaire generation to response evaluation"""
        mock_ai = create_mock_ai_service_manager()
        
        # Step 1: Generate questionnaire
        questionnaire = await mock_ai.generate_questionnaire(
            position_type="Engineer",
            dimensions=["Technical"],
            question_count=5,
            industry="Tech"
        )
        
        assert "questions" in questionnaire
        
        # Step 2: Evaluate responses to questionnaire
        evaluation = await mock_ai.evaluate_responses(
            questionnaire_title=questionnaire.get("title", "Test"),
            position_type="Engineer",
            dimension_scores={"Technical": 85},
            answers_summary="Sample answers"
        )
        
        assert "score" in evaluation
        
        # Verify workflow completed
        assert len(mock_ai.call_history) >= 2
    
    @pytest.mark.asyncio
    async def test_full_assessment_pipeline(self):
        """Test complete assessment pipeline with all services"""
        mock_ai = create_mock_ai_service_manager()
        
        # 1. Parse resume
        resume = await mock_ai.create_completion(
            [{"role": "user", "content": "Parse resume"}],
            response_format="json"
        )
        
        # 2. Generate embedding
        embedding = await mock_ai.create_embedding("Resume text")
        
        # 3. Generate questionnaire based on resume
        questionnaire = await mock_ai.generate_questionnaire(
            position_type="Engineer",
            dimensions=["Technical"],
            question_count=5,
            industry="Tech"
        )
        
        # 4. Evaluate responses
        evaluation = await mock_ai.evaluate_responses(
            questionnaire_title="Test",
            position_type="Engineer", 
            dimension_scores={},
            answers_summary=""
        )
        
        # 5. Generate recommendation
        recommendation = await mock_ai.create_completion(
            [{"role": "user", "content": "Generate recommendation"}],
            response_format="json"
        )
        
        # All steps should complete successfully
        assert resume is not None
        assert len(embedding) == 1024
        assert "questions" in questionnaire
        assert "score" in evaluation
        assert recommendation is not None
        
        # Verify complete pipeline execution
        assert len(mock_ai.call_history) >= 5