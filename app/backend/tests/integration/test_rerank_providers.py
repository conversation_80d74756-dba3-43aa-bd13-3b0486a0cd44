#!/usr/bin/env python3
"""
Rerank Provider Integration Tests
测试各种Rerank提供商的集成功能
"""
import asyncio
import os
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
import json

# 测试数据
TEST_QUERY = "Python开发工程师，熟悉FastAPI框架，有微服务架构经验"

TEST_DOCUMENTS = [
    "需要5年以上Python开发经验，精通FastAPI、Django等Web框架，熟悉微服务架构设计，有Docker容器化部署经验",
    "前端工程师，精通React、Vue等前端框架，有移动端开发经验，熟悉TypeScript",
    "Java开发工程师，熟悉Spring Boot框架，有分布式系统开发经验，了解微服务架构",
    "Python后端工程师，3年工作经验，FastAPI框架开发经验，了解异步编程，熟悉PostgreSQL数据库",
    "数据分析师，熟悉Python数据分析库（Pandas、NumPy），有机器学习项目经验",
    "DevOps工程师，熟悉Docker、Kubernetes容器编排，有CI/CD流水线搭建经验，了解微服务部署",
    "Python全栈工程师，FastAPI后端开发经验，React前端开发能力，有完整项目交付经验",
    "机器学习工程师，Python编程基础扎实，熟悉TensorFlow、PyTorch框架，有NLP项目实战经验",
    "后端架构师，精通Python和Go语言，有大规模微服务架构设计经验，熟悉FastAPI、Gin等框架",
    "测试工程师，熟悉Python自动化测试，了解接口测试和性能测试"
]

# 预期的高相关文档索引（基于关键词匹配）
EXPECTED_TOP_INDICES = [0, 3, 6, 8]  # 包含Python + FastAPI + 微服务的文档


async def test_ollama_rerank():
    """测试Ollama的Rerank功能（注意：Ollama不支持真正的reranker）"""
    print("\n" + "="*80)
    print("🦙 Testing Ollama Rerank (Embedding-based)")
    print("="*80)
    
    try:
        from app.services.ai_service_manager import AIServiceManager
        ai_service = AIServiceManager()
        
        if ai_service.rerank_provider != "ollama":
            print(f"⚠️ Rerank provider is {ai_service.rerank_provider}, not ollama")
            return None
            
        print(f"📋 Configuration:")
        print(f"   Provider: ollama")
        print(f"   Model: {os.getenv('OLLAMA_RERANK_MODEL', 'Not configured')}")
        print(f"   Host: {os.getenv('OLLAMA_HOST', 'http://ollama:11434')}")
        
        print(f"\n📡 Testing Rerank...")
        print(f"   Query: {TEST_QUERY[:50]}...")
        print(f"   Documents: {len(TEST_DOCUMENTS)} candidates")
        
        start = time.time()
        results = await ai_service.rerank_documents(
            query=TEST_QUERY,
            documents=TEST_DOCUMENTS,
            top_k=5
        )
        elapsed = (time.time() - start) * 1000
        
        if results:
            print(f"\n✅ Rerank completed ({elapsed:.0f}ms)")
            print("\n📊 Results (Top 5):")
            print("-"*50)
            
            top_3_matches = 0
            for i, result in enumerate(results[:5], 1):
                doc_idx = result.get('index', -1)
                score = result.get('score', 0.0)
                doc = result.get('document', TEST_DOCUMENTS[doc_idx] if doc_idx >= 0 else "")
                
                # 检查是否是预期的高相关文档
                if doc_idx in EXPECTED_TOP_INDICES[:3]:
                    top_3_matches += 1
                
                relevance = analyze_relevance(doc)
                print(f"\n{i}. Score: {score:.4f}{relevance}")
                print(f"   Doc[{doc_idx+1}]: {doc[:80]}...")
            
            # 质量评估
            print("\n🔍 Quality Analysis:")
            print(f"   Top 3 accuracy: {top_3_matches}/3 matched expected documents")
            
            if top_3_matches >= 2:
                print("   ✅ Quality: GOOD")
            elif top_3_matches >= 1:
                print("   ⚠️ Quality: MODERATE")
            else:
                print("   ❌ Quality: POOR (Ollama doesn't support true reranking)")
            
            return {"status": "completed", "time": elapsed, "quality": top_3_matches}
        else:
            print("❌ No results returned")
            return {"status": "failed"}
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return {"status": "error", "error": str(e)}


async def test_openrouter_siliconflow_rerank():
    """测试通过OpenRouter配置使用SiliconFlow的Rerank API"""
    print("\n" + "="*80)
    print("🎯 Testing OpenRouter/SiliconFlow Rerank API")
    print("="*80)
    
    api_key = os.getenv("OPENROUTER_API_KEY")
    api_base = os.getenv("OPENROUTER_API_BASE", "https://openrouter.ai/api/v1")
    rerank_model = os.getenv("OPENROUTER_RERANK_MODEL", "BAAI/bge-reranker-v2-m3")
    
    if not api_key:
        print("⚠️ OPENROUTER_API_KEY not configured")
        return None
    
    print(f"📋 Configuration:")
    print(f"   API Base: {api_base}")
    print(f"   Rerank Model: {rerank_model}")
    print(f"   API Key: {'***' + api_key[-4:] if len(api_key) > 4 else '***'}")
    
    # 检查是否是支持Rerank的API
    if "siliconflow" not in api_base.lower():
        print("\n⚠️ Note: Standard OpenRouter doesn't support Rerank API")
        print("   Rerank API is available on SiliconFlow and similar providers")
    
    try:
        import httpx
        
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        
        # 构建Rerank请求
        rerank_url = api_base.rstrip('/') + "/rerank"
        data = {
            "model": rerank_model,
            "query": TEST_QUERY,
            "documents": TEST_DOCUMENTS,
            "top_n": 5,
            "return_documents": True
        }
        
        print(f"\n📡 Testing Rerank API...")
        print(f"   Endpoint: {rerank_url}")
        print(f"   Query: {TEST_QUERY[:50]}...")
        print(f"   Documents: {len(TEST_DOCUMENTS)} candidates")
        
        start = time.time()
        async with httpx.AsyncClient() as client:
            response = await client.post(
                rerank_url,
                headers=headers,
                json=data,
                timeout=30
            )
        elapsed = (time.time() - start) * 1000
        
        if response.status_code == 200:
            result = response.json()
            print(f"\n✅ Rerank API SUCCESS ({elapsed:.0f}ms)")
            
            if "results" in result:
                print("\n📊 Rerank Results (Top 5):")
                print("-"*50)
                
                top_3_matches = 0
                for i, item in enumerate(result.get("results", [])[:5], 1):
                    index = item.get("index", 0)
                    score = item.get("relevance_score", 0)
                    doc_data = item.get("document", {})
                    
                    # 处理不同的文档格式
                    if isinstance(doc_data, dict):
                        doc_text = doc_data.get("text", str(doc_data))
                    else:
                        doc_text = str(doc_data)
                    
                    if not doc_text and index < len(TEST_DOCUMENTS):
                        doc_text = TEST_DOCUMENTS[index]
                    
                    # 检查是否是预期的高相关文档
                    if index in EXPECTED_TOP_INDICES[:3]:
                        top_3_matches += 1
                    
                    relevance = analyze_relevance(doc_text)
                    print(f"\n{i}. Score: {score:.4f}{relevance}")
                    print(f"   Doc[{index+1}]: {doc_text[:80]}...")
                
                # 分析排序质量
                print("\n🔍 Quality Analysis:")
                print(f"   Top 3 accuracy: {top_3_matches}/3 matched expected documents")
                
                if top_3_matches >= 2:
                    print("   ✅ Rerank quality: GOOD")
                elif top_3_matches >= 1:
                    print("   ⚠️ Rerank quality: MODERATE")
                else:
                    print("   ❌ Rerank quality: POOR")
                
                return {"status": "success", "time": elapsed, "quality": top_3_matches}
            
        elif response.status_code == 404:
            print(f"❌ Rerank API not found (404)")
            print("   This provider may not support Rerank API")
            return {"status": "not_supported"}
        else:
            print(f"❌ Rerank API failed: HTTP {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return {"status": "failed", "code": response.status_code}
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return {"status": "error", "error": str(e)}


def analyze_relevance(doc_text: str) -> str:
    """分析文档与查询的相关性"""
    if not isinstance(doc_text, str):
        doc_text = str(doc_text)
    
    # 检查是否包含关键词
    has_python = "Python" in doc_text or "python" in doc_text.lower()
    has_fastapi = "FastAPI" in doc_text or "fastapi" in doc_text.lower()
    has_microservice = "微服务" in doc_text
    
    relevance = ""
    if has_python and has_fastapi and has_microservice:
        relevance = " ⭐⭐⭐"  # 完美匹配
    elif has_python and (has_fastapi or has_microservice):
        relevance = " ⭐⭐"   # 高度相关
    elif has_python:
        relevance = " ⭐"     # 部分相关
    
    return relevance


async def test_direct_embeddings_comparison():
    """直接比较不同的嵌入模型效果"""
    print("\n" + "="*80)
    print("🔬 Testing Direct Embeddings Comparison")
    print("="*80)
    
    try:
        from app.services.ai_service_manager import AIServiceManager
        from sklearn.metrics.pairwise import cosine_similarity
        import numpy as np
        
        ai_service = AIServiceManager()
        
        print("📊 Testing BGE-M3 Embeddings for Reranking...")
        
        # 生成查询嵌入
        query_embedding = await ai_service.generate_embedding(TEST_QUERY)
        
        # 生成文档嵌入
        doc_embeddings = []
        for doc in TEST_DOCUMENTS:
            emb = await ai_service.generate_embedding(doc)
            doc_embeddings.append(emb)
        
        # 计算相似度
        query_vec = np.array(query_embedding).reshape(1, -1)
        doc_vecs = np.array(doc_embeddings)
        similarities = cosine_similarity(query_vec, doc_vecs)[0]
        
        # 排序结果
        ranked_indices = np.argsort(similarities)[::-1]
        
        print("\n📊 BGE-M3 Embedding Results (Top 5):")
        print("-"*50)
        
        top_3_matches = 0
        for i, idx in enumerate(ranked_indices[:5], 1):
            score = similarities[idx]
            doc = TEST_DOCUMENTS[idx]
            
            if idx in EXPECTED_TOP_INDICES[:3]:
                top_3_matches += 1
            
            relevance = analyze_relevance(doc)
            print(f"\n{i}. Score: {score:.4f}{relevance}")
            print(f"   Doc[{idx+1}]: {doc[:80]}...")
        
        print(f"\n🔍 Quality: {top_3_matches}/3 matched expected documents")
        
        return {"embedding_quality": top_3_matches}
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return {"status": "error", "error": str(e)}


async def main():
    """主测试程序"""
    print("\n" + "="*90)
    print("🚀 RERANK PROVIDERS INTEGRATION TEST")
    print("="*90)
    print(f"⏰ Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌍 Environment: {os.getenv('ENVIRONMENT', 'development')}")
    
    results = {}
    
    # 检测当前配置
    rerank_provider = os.getenv("RERANK_PROVIDER", "none")
    api_base = os.getenv("OPENROUTER_API_BASE", "")
    
    print(f"\n📋 Current Configuration:")
    print(f"   RERANK_PROVIDER: {rerank_provider}")
    if rerank_provider == "openrouter":
        provider_name = "SiliconFlow" if "siliconflow" in api_base.lower() else "OpenRouter"
        print(f"   Using: {provider_name} via OpenRouter configuration")
        print(f"   API Base: {api_base}")
    
    # 根据配置运行相应的测试
    if rerank_provider == "ollama":
        print("\n🧪 Testing Ollama Provider...")
        result = await test_ollama_rerank()
        if result:
            results["ollama"] = result
            
        # 同时测试嵌入效果作为对比
        print("\n🧪 Testing Direct Embeddings for Comparison...")
        emb_result = await test_direct_embeddings_comparison()
        if emb_result:
            results["embeddings"] = emb_result
            
    elif rerank_provider == "openrouter" and "siliconflow" in api_base.lower():
        print("\n🧪 Testing SiliconFlow Provider...")
        result = await test_openrouter_siliconflow_rerank()
        if result:
            results["siliconflow"] = result
    else:
        print("\n⚠️ No Rerank provider configured or unsupported provider")
    
    # 总结
    print("\n" + "="*90)
    print("📊 TEST SUMMARY")
    print("="*90)
    
    if results:
        for provider, result in results.items():
            status = result.get("status", "unknown")
            quality = result.get("quality", result.get("embedding_quality", 0))
            
            if status == "success" or status == "completed":
                quality_desc = "GOOD" if quality >= 2 else ("MODERATE" if quality >= 1 else "POOR")
                print(f"\n{provider.upper()}:")
                print(f"  Status: ✅ {status}")
                print(f"  Quality: {quality_desc} ({quality}/3 matches)")
                if "time" in result:
                    print(f"  Time: {result['time']:.0f}ms")
            elif status == "not_supported":
                print(f"\n{provider.upper()}:")
                print(f"  Status: ⚠️ Not Supported")
            else:
                print(f"\n{provider.upper()}:")
                print(f"  Status: ❌ Failed")
    
    print("\n💡 Recommendations:")
    if rerank_provider == "ollama":
        print("⚠️ Ollama doesn't support true reranker models")
        print("   Consider using SiliconFlow for better rerank quality:")
        print("   - Set OPENROUTER_API_BASE=https://api.siliconflow.cn/v1")
        print("   - Set OPENROUTER_API_KEY=your_siliconflow_key")
        print("   - Set RERANK_PROVIDER=openrouter")
    elif rerank_provider == "openrouter" and "siliconflow" in api_base.lower():
        quality = results.get("siliconflow", {}).get("quality", 0)
        if quality >= 2:
            print("✅ SiliconFlow is working well with good rerank quality!")
        else:
            print("⚠️ SiliconFlow is configured but quality could be improved")
            print("   Check if the model and API key are correct")
    
    print("\n" + "="*90)
    print("✨ TEST COMPLETE")
    print("="*90)


if __name__ == "__main__":
    asyncio.run(main())