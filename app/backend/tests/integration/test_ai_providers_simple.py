#!/usr/bin/env python3
"""
Simple AI Provider Test
直接测试AI服务，避免复杂的初始化链
"""
import asyncio
import os
import sys
from datetime import datetime

# Add project root to path
sys.path.insert(0, '/app')


async def test_providers_directly():
    """直接测试AI提供商，避免服务初始化问题"""
    print("\n" + "="*80)
    print("🚀 DIRECT AI PROVIDER TEST")
    print("="*80)
    print(f"⏰ Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 直接导入并使用AIService
    try:
        from app.services.ai_service_manager import AIServiceManager as AIService
        ai_service = AIService()
        
        print(f"\n📋 Current Configuration:")
        # AIServiceManager doesn't have direct provider properties
        from app.core.ai_config import ai_settings
        print(f"   LLM Provider: {ai_settings.LLM_PROVIDER}")
        print(f"   Embedding Provider: {ai_settings.EMBEDDING_PROVIDER}")
        print(f"   Rerank Provider: {ai_settings.RERANK_PROVIDER}")
        
        # 测试LLM
        print(f"\n📝 Testing LLM ({ai_settings.LLM_PROVIDER})...")
        try:
            llm_client, config = ai_service.get_llm_client(provider_name=ai_settings.LLM_PROVIDER)
            if not llm_client:
                print("❌ LLM client not available")
            else:
                print("✅ LLM client initialized successfully")
        except Exception as e:
            print(f"❌ LLM Error: {e}")
        
        # 测试Embedding
        print(f"\n🔤 Testing Embedding ({ai_settings.EMBEDDING_PROVIDER})...")
        try:
            embedding_client, config = ai_service.get_embedding_client(provider_name=ai_settings.EMBEDDING_PROVIDER)
            if not embedding_client:
                print("❌ Embedding client not available")
            else:
                print("✅ Embedding client initialized successfully")
        except Exception as e:
            print(f"❌ Embedding Error: {e}")
        
        # 测试Rerank（如果配置）
        if ai_settings.RERANK_PROVIDER and ai_settings.RERANK_PROVIDER != "none":
            print(f"\n🎯 Testing Rerank ({ai_settings.RERANK_PROVIDER})...")
            try:
                rerank_client, config = ai_service.get_rerank_client(provider_name=ai_settings.RERANK_PROVIDER)
                if not rerank_client:
                    print("❌ Rerank client not available")
                else:
                    print("✅ Rerank client initialized successfully")
            except Exception as e:
                print(f"❌ Rerank Error: {e}")
        
        print("\n✅ Test Complete!")
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("\n🔧 Trying alternative approach...")
        
        # 尝试直接使用ai_service_unified的逻辑
        try:
            from app.core.ai_config import ai_settings
            print(f"\n📋 AI Settings Loaded:")
            print(f"   LLM Provider: {ai_settings.LLM_PROVIDER}")
            print(f"   Embedding Provider: {ai_settings.EMBEDDING_PROVIDER}")
            print(f"   Rerank Provider: {ai_settings.RERANK_PROVIDER}")
            
            # 测试provider具体配置
            providers = {
                "deepseek": ai_settings.DEEPSEEK_API_KEY,
                "zhipu": ai_settings.ZHIPU_API_KEY,
                "moonshot": ai_settings.MOONSHOT_API_KEY,
                "qwen": ai_settings.QWEN_API_KEY,
                "openrouter": ai_settings.OPENROUTER_API_KEY,
                "ollama": ai_settings.OLLAMA_HOST
            }
            
            print(f"\n📊 Provider Status:")
            for provider, config in providers.items():
                if config:
                    status = "✅ Configured"
                    if provider == "openrouter" and "siliconflow" in ai_settings.OPENROUTER_API_BASE.lower():
                        status += " (SiliconFlow)"
                else:
                    status = "❌ Not configured"
                print(f"   {provider.upper()}: {status}")
            
            print("\n✅ Configuration Check Complete!")
            
        except Exception as e2:
            print(f"❌ Alternative approach also failed: {e2}")
    
    print("\n" + "="*80)
    print("✨ DONE")
    print("="*80)


if __name__ == "__main__":
    asyncio.run(test_providers_directly())