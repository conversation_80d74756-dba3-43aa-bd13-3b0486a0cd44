[tool:pytest]
# Authentication-specific pytest configuration

# Test discovery
python_files = test_auth_*.py
python_classes = Test*
python_functions = test_*

# Test execution
addopts = 
    -v
    --strict-markers
    --strict-config
    --tb=short
    --asyncio-mode=auto

# Markers for test categorization
markers =
    smoke: Quick smoke tests for basic functionality
    integration: Integration tests for complete flows
    regression: Regression tests for previously fixed issues
    performance: Basic performance validation tests
    security: Security-focused tests
    edge_case: Edge case and error condition tests

# Async settings
asyncio_mode = auto

# Logging
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Test timeout (prevent hanging tests)
timeout = 300

# Filter out warnings we can't control
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:jose.*
    ignore::UserWarning:passlib.*