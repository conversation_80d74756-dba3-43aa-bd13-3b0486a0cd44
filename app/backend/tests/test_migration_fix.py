"""
Database migration fix test suite
Tests the comprehensive database migration reset and validation system
"""
import asyncio
import pytest
import subprocess
import sys
from pathlib import Path
from sqlalchemy import text, inspect
from sqlalchemy.ext.asyncio import create_async_engine

from app.core.database import async_engine
from app.core.config import settings

# Add scripts directory to path for imports
sys.path.append(str(Path(__file__).parent.parent.parent / "scripts" / "database"))


class TestMigrationResetScripts:
    """Test individual migration reset script functions"""

    @pytest.mark.asyncio
    async def test_validate_migration_script_imports(self):
        """Test that validation script can be imported and has required functions"""
        try:
            import validate_migration
            
            # Check required functions exist
            assert hasattr(validate_migration, 'validate_extensions')
            assert hasattr(validate_migration, 'validate_enums')
            assert hasattr(validate_migration, 'validate_tables')
            assert hasattr(validate_migration, 'validate_foreign_keys')
            assert hasattr(validate_migration, 'validate_alembic_state')
            assert hasattr(validate_migration, 'test_enum_usage')
            assert hasattr(validate_migration, 'main')
        except ImportError as e:
            pytest.fail(f"Failed to import validate_migration: {e}")

    @pytest.mark.asyncio
    async def test_reset_migrations_script_imports(self):
        """Test that reset migrations script can be imported"""
        try:
            import reset_migrations
            assert hasattr(reset_migrations, 'reset_database_completely')
            assert hasattr(reset_migrations, 'main')
        except ImportError as e:
            pytest.fail(f"Failed to import reset_migrations: {e}")

    @pytest.mark.asyncio
    async def test_clean_migrations_script_imports(self):
        """Test that clean migrations script can be imported"""
        try:
            import clean_migrations
            assert hasattr(clean_migrations, 'clean_migration_files')
        except ImportError as e:
            pytest.fail(f"Failed to import clean_migrations: {e}")


class TestMigrationValidation:
    """Test database migration validation functions"""

    @pytest.mark.asyncio
    async def test_database_connection(self):
        """Test that we can connect to the database"""
        try:
            async with async_engine.begin() as conn:
                result = await conn.execute(text("SELECT 1"))
                assert result.scalar() == 1
        except Exception as e:
            pytest.fail(f"Database connection failed: {e}")

    @pytest.mark.asyncio
    async def test_required_extensions_exist(self):
        """Test that required PostgreSQL extensions are installed"""
        required_extensions = ['vector', 'pg_trgm', 'uuid-ossp']
        
        async with async_engine.begin() as conn:
            for ext in required_extensions:
                result = await conn.execute(text(
                    f"SELECT * FROM pg_extension WHERE extname = '{ext}'"
                ))
                row = result.fetchone()
                assert row is not None, f"Extension '{ext}' is not installed"

    @pytest.mark.asyncio
    async def test_required_enums_exist(self):
        """Test that all required enum types exist"""
        required_enums = [
            'userrole', 'datapermission', 'educationlevel', 'experiencerange',
            'salaryrange', 'candidatesource', 'permission', 'candidatestatus',
            'approvalstatus', 'positionstatus', 'positionurgency', 'servicestatus',
            'questiontype', 'scoringmethod', 'questionnairecategory', 'questionnairestatus',
            'accesstype', 'activitytype', 'batchtaskstatus', 'batchtasktype'
        ]
        
        async with async_engine.begin() as conn:
            for enum_name in required_enums:
                result = await conn.execute(text(
                    f"SELECT typname FROM pg_type WHERE typname = '{enum_name}'"
                ))
                row = result.fetchone()
                assert row is not None, f"Enum '{enum_name}' is missing"

    @pytest.mark.asyncio
    async def test_required_tables_exist(self):
        """Test that all required tables exist"""
        required_tables = [
            'users', 'user_preferences', 'positions', 'candidates',
            'questionnaires', 'questionnaire_sections', 'questions',
            'questionnaire_responses', 'answers', 'monitoring_snapshots',
            'job_vectors', 'resume_vectors', 'resume_files', 'candidate_assessments'
        ]
        
        async with async_engine.begin() as conn:
            for table_name in required_tables:
                result = await conn.execute(text(
                    f"SELECT tablename FROM pg_tables WHERE tablename = '{table_name}'"
                ))
                row = result.fetchone()
                assert row is not None, f"Table '{table_name}' is missing"

    @pytest.mark.asyncio
    async def test_critical_foreign_keys_exist(self):
        """Test that critical foreign key relationships exist"""
        key_relationships = [
            ('user_preferences', 'users', 'user_id'),
            ('positions', 'users', 'created_by'),
            ('candidates', 'users', 'created_by'),
            ('questionnaire_responses', 'questionnaires', 'questionnaire_id'),
            ('questionnaire_responses', 'candidates', 'candidate_id'),
            ('questions', 'questionnaires', 'questionnaire_id'),
            ('answers', 'questionnaire_responses', 'response_id'),
            ('job_vectors', 'positions', 'position_id'),
            ('resume_vectors', 'candidates', 'candidate_id'),
            ('candidate_assessments', 'candidates', 'candidate_id')
        ]
        
        async with async_engine.begin() as conn:
            for child_table, parent_table, foreign_key_col in key_relationships:
                result = await conn.execute(text(f"""
                    SELECT tc.constraint_name
                    FROM information_schema.table_constraints AS tc 
                    JOIN information_schema.key_column_usage AS kcu
                      ON tc.constraint_name = kcu.constraint_name
                      AND tc.table_schema = kcu.table_schema
                    JOIN information_schema.constraint_column_usage AS ccu
                      ON ccu.constraint_name = tc.constraint_name
                      AND ccu.table_schema = tc.table_schema
                    WHERE tc.constraint_type = 'FOREIGN KEY' 
                      AND tc.table_name = '{child_table}'
                      AND kcu.column_name = '{foreign_key_col}'
                      AND ccu.table_name = '{parent_table}'
                """))
                
                row = result.fetchone()
                assert row is not None, f"Missing FK: {child_table}.{foreign_key_col} -> {parent_table}"

    @pytest.mark.asyncio
    async def test_alembic_migration_state(self):
        """Test that Alembic migration state is correct"""
        async with async_engine.begin() as conn:
            # Check that alembic_version table exists
            result = await conn.execute(text(
                "SELECT tablename FROM pg_tables WHERE tablename = 'alembic_version'"
            ))
            assert result.fetchone() is not None, "alembic_version table does not exist"
            
            # Check current revision
            result = await conn.execute(text("SELECT version_num FROM alembic_version"))
            current_version = result.scalar()
            
            assert current_version == '20250828000000', f"Expected migration version 20250828000000, got {current_version}"

    @pytest.mark.asyncio
    async def test_enum_functionality(self):
        """Test that enums can be used in actual database operations"""
        async with async_engine.begin() as conn:
            try:
                # Test creating and querying with enums
                await conn.execute(text("""
                    INSERT INTO users (id, email, username, hashed_password, full_name, role, is_active)
                    VALUES (999999, '<EMAIL>', 'testenumuser', 'hashedpass', 'Test Enum User', 'HR_SPECIALIST', true)
                    ON CONFLICT (id) DO UPDATE SET 
                        email = EXCLUDED.email,
                        username = EXCLUDED.username
                """))
                
                # Test querying with enum
                result = await conn.execute(text(
                    "SELECT id, role FROM users WHERE role = 'HR_SPECIALIST' AND id = 999999"
                ))
                
                row = result.fetchone()
                assert row is not None, "Enum query failed"
                assert row[1] == 'HR_SPECIALIST', f"Expected HR_SPECIALIST, got {row[1]}"
                
                # Clean up test data
                await conn.execute(text("DELETE FROM users WHERE id = 999999"))
                
            except Exception as e:
                pytest.fail(f"Enum functionality test failed: {e}")


class TestMigrationIntegration:
    """Test complete migration workflow integration"""

    def test_full_reset_script_exists(self):
        """Test that full reset script exists and is executable"""
        script_path = Path(__file__).parent.parent.parent / "scripts" / "database" / "full_reset.sh"
        assert script_path.exists(), "full_reset.sh script not found"
        assert script_path.is_file(), "full_reset.sh is not a file"
        
        # Check if it's executable (on Unix systems)
        if hasattr(script_path, 'stat'):
            import stat
            file_stat = script_path.stat()
            assert file_stat.st_mode & stat.S_IEXEC, "full_reset.sh is not executable"

    def test_migration_file_exists(self):
        """Test that the consolidated migration file exists"""
        migration_path = Path(__file__).parent.parent / "alembic" / "versions" / "20250828_000000_initial_reset_migration.py"
        assert migration_path.exists(), "Consolidated migration file not found"
        assert migration_path.is_file(), "Migration path is not a file"

    def test_migration_file_content(self):
        """Test that migration file contains expected content"""
        migration_path = Path(__file__).parent.parent / "alembic" / "versions" / "20250828_000000_initial_reset_migration.py"
        content = migration_path.read_text()
        
        # Check for critical elements
        assert "revision = '20250828000000'" in content, "Incorrect revision ID"
        assert "down_revision = None" in content, "Should be initial migration"
        assert "def upgrade()" in content, "Missing upgrade function"
        assert "def downgrade()" in content, "Missing downgrade function"
        
        # Check for enum creation
        assert "CREATE TYPE userrole" in content, "Missing user role enum creation"
        assert "CREATE TYPE datapermission" in content, "Missing data permission enum creation"
        
        # Check for table creation
        assert "create_table('users'" in content, "Missing users table creation"
        assert "create_table('candidates'" in content, "Missing candidates table creation"
        assert "create_table('positions'" in content, "Missing positions table creation"


class TestMigrationSafety:
    """Test migration safety mechanisms"""

    @pytest.mark.asyncio
    async def test_development_environment_check(self):
        """Test that reset scripts only work in development environment"""
        # This test ensures the safety mechanism is in place
        # In actual usage, the scripts should check ENVIRONMENT variable
        import os
        
        original_env = os.environ.get('ENVIRONMENT')
        
        try:
            # Test with production environment
            os.environ['ENVIRONMENT'] = 'production'
            
            # Try to import and run the reset function (should fail gracefully)
            import reset_migrations
            
            # The actual script should have environment checks
            # This is more of a design verification than functional test
            assert hasattr(reset_migrations, 'main'), "Reset migrations should have main function"
            
        finally:
            # Restore original environment
            if original_env:
                os.environ['ENVIRONMENT'] = original_env
            else:
                os.environ.pop('ENVIRONMENT', None)

    @pytest.mark.asyncio
    async def test_model_imports_work(self):
        """Test that all SQLAlchemy models can be imported after migration"""
        try:
            from app.models.user import User
            from app.models.candidate import Candidate  
            from app.models.position import Position
            from app.models.questionnaire import Questionnaire
            from app.models.questionnaire_response import QuestionnaireResponse
            from app.models.monitoring import MonitoringSnapshot
            
            # Verify models have expected attributes
            assert hasattr(User, '__tablename__')
            assert hasattr(Candidate, '__tablename__')
            assert hasattr(Position, '__tablename__')
            assert hasattr(Questionnaire, '__tablename__')
            
        except ImportError as e:
            pytest.fail(f"Model import failed after migration: {e}")


class TestMigrationRegression:
    """Test to prevent regression of enum conflict issues"""

    @pytest.mark.asyncio
    async def test_no_enum_conflicts(self):
        """Test that there are no enum type conflicts"""
        async with async_engine.begin() as conn:
            # Check for duplicate enum types (which caused the original issue)
            result = await conn.execute(text("""
                SELECT typname, COUNT(*) as count
                FROM pg_type 
                WHERE typtype = 'e' 
                GROUP BY typname
                HAVING COUNT(*) > 1
            """))
            
            conflicts = result.fetchall()
            assert len(conflicts) == 0, f"Found enum conflicts: {conflicts}"

    @pytest.mark.asyncio
    async def test_enum_values_correct(self):
        """Test that enum values are as expected"""
        async with async_engine.begin() as conn:
            # Test specific enum values
            result = await conn.execute(text("""
                SELECT e.enumlabel
                FROM pg_type t 
                JOIN pg_enum e ON t.oid = e.enumtypid  
                WHERE t.typname = 'userrole'
                ORDER BY e.enumsortorder
            """))
            
            role_values = [row[0] for row in result.fetchall()]
            expected_roles = ['ADMIN', 'HR', 'INTERVIEWER', 'USER']
            
            for role in expected_roles:
                assert role in role_values, f"Missing user role: {role}"

    @pytest.mark.asyncio
    async def test_migration_is_idempotent(self):
        """Test that migration can be applied multiple times safely"""
        async with async_engine.begin() as conn:
            # Try to create an extension that already exists
            # This should not fail due to IF NOT EXISTS clause
            await conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            
            # Verify the extension still exists
            result = await conn.execute(text(
                "SELECT * FROM pg_extension WHERE extname = 'vector'"
            ))
            assert result.fetchone() is not None, "Vector extension missing after idempotent test"


class TestMigrationPerformance:
    """Test migration performance characteristics"""

    @pytest.mark.asyncio
    async def test_table_access_performance(self):
        """Test that basic table operations perform reasonably"""
        import time
        
        async with async_engine.begin() as conn:
            # Test basic table operations
            start_time = time.time()
            
            # Simple query on each major table
            tables_to_test = ['users', 'candidates', 'positions', 'questionnaires']
            
            for table in tables_to_test:
                result = await conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                count = result.scalar()
                assert count is not None, f"Failed to query {table}"
            
            end_time = time.time()
            query_time = end_time - start_time
            
            # Should complete within reasonable time (adjust threshold as needed)
            assert query_time < 5.0, f"Table queries took too long: {query_time}s"

    @pytest.mark.asyncio
    async def test_index_effectiveness(self):
        """Test that critical indexes exist and are being used"""
        async with async_engine.begin() as conn:
            # Check that key indexes exist
            critical_indexes = [
                ('users', 'idx_user_email'),
                ('users', 'idx_user_username'),
                ('candidates', 'idx_candidate_status'),
                ('positions', 'idx_position_status')
            ]
            
            for table_name, index_name in critical_indexes:
                result = await conn.execute(text(f"""
                    SELECT indexname FROM pg_indexes 
                    WHERE tablename = '{table_name}' AND indexname = '{index_name}'
                """))
                
                row = result.fetchone()
                # Note: This is a warning, not a failure for performance tests
                if row is None:
                    print(f"Warning: Index '{index_name}' missing on '{table_name}'")


if __name__ == "__main__":
    # Allow running tests directly
    pytest.main([__file__, "-v"])