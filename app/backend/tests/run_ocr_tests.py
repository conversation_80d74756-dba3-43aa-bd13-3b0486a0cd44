#!/usr/bin/env python3
"""
OCR Test Suite Runner

This script runs comprehensive OCR integration tests with organized reporting.
"""

import sys
import argparse
import subprocess
from pathlib import Path
from typing import List, Optional


class OCRTestRunner:
    """Orchestrates OCR test execution and reporting"""
    
    def __init__(self, backend_path: Path = None):
        self.backend_path = backend_path or Path(__file__).parent.parent
        self.test_path = self.backend_path / "tests"
    
    def run_unit_tests(self, verbose: bool = False) -> int:
        """Run OCR service unit tests"""
        print("\n🔬 Running OCR Service Unit Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_path / "test_ocr_service.py"),
            "-v" if verbose else "-q",
            "--tb=short",
            "--durations=10",
            "-m", "not performance"
        ]
        
        return subprocess.run(cmd, cwd=self.backend_path).returncode
    
    def run_integration_tests(self, verbose: bool = False) -> int:
        """Run OCR integration tests"""
        print("\n🔗 Running OCR Integration Tests...")
        
        cmd = [
            "python", "-m", "pytest", 
            str(self.test_path / "test_resume_parser_ocr_integration.py"),
            "-v" if verbose else "-q",
            "--tb=short",
            "--durations=10",
            "-m", "not performance"
        ]
        
        return subprocess.run(cmd, cwd=self.backend_path).returncode
    
    def run_api_tests(self, verbose: bool = False) -> int:
        """Run OCR API endpoint tests"""
        print("\n🌐 Running OCR API Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_path / "test_api_ocr_endpoints.py"),
            "-v" if verbose else "-q",
            "--tb=short",
            "--durations=10",
            "-m", "not performance"
        ]
        
        return subprocess.run(cmd, cwd=self.backend_path).returncode
    
    def run_celery_tests(self, verbose: bool = False) -> int:
        """Run OCR Celery task tests"""
        print("\n⚙️ Running OCR Celery Task Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_path / "test_celery_ocr_tasks.py"),
            "-v" if verbose else "-q", 
            "--tb=short",
            "--durations=10",
            "-m", "not performance"
        ]
        
        return subprocess.run(cmd, cwd=self.backend_path).returncode
    
    def run_performance_tests(self, verbose: bool = False) -> int:
        """Run OCR performance tests"""
        print("\n⚡ Running OCR Performance Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_path / "test_ocr_performance.py"),
            "-v" if verbose else "-q",
            "--tb=short", 
            "--durations=0",
            "-m", "performance",
            "--timeout=300"  # 5 minute timeout for performance tests
        ]
        
        return subprocess.run(cmd, cwd=self.backend_path).returncode
    
    def run_all_ocr_tests(self, verbose: bool = False, include_performance: bool = False) -> int:
        """Run all OCR tests"""
        print("🚀 Running Complete OCR Test Suite...")
        
        markers = ["ocr"]
        if not include_performance:
            markers.append("not performance")
        
        cmd = [
            "python", "-m", "pytest",
            str(self.test_path / "test_ocr_service.py"),
            str(self.test_path / "test_resume_parser_ocr_integration.py"),
            str(self.test_path / "test_api_ocr_endpoints.py"),
            str(self.test_path / "test_celery_ocr_tasks.py"),
            "-v" if verbose else "-q",
            "--tb=short",
            "--durations=20",
            "-m", " and ".join(markers) if len(markers) > 1 else markers[0]
        ]
        
        if include_performance:
            cmd.append(str(self.test_path / "test_ocr_performance.py"))
            cmd.extend(["--timeout=300"])
        
        return subprocess.run(cmd, cwd=self.backend_path).returncode
    
    def run_coverage_report(self) -> int:
        """Run tests with coverage reporting"""
        print("\n📊 Running OCR Tests with Coverage...")
        
        cmd = [
            "python", "-m", "pytest",
            "--cov=app.services.ocr_service",
            "--cov=app.services.resume_parser", 
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov/ocr_coverage",
            str(self.test_path / "test_ocr_service.py"),
            str(self.test_path / "test_resume_parser_ocr_integration.py"),
            str(self.test_path / "test_api_ocr_endpoints.py"),
            str(self.test_path / "test_celery_ocr_tasks.py"),
            "-v",
            "-m", "not performance"
        ]
        
        result = subprocess.run(cmd, cwd=self.backend_path)
        
        if result.returncode == 0:
            print("\n📈 Coverage report generated: htmlcov/ocr_coverage/index.html")
        
        return result.returncode
    
    def run_smoke_tests(self) -> int:
        """Run quick smoke tests for OCR functionality"""
        print("\n💨 Running OCR Smoke Tests...")
        
        cmd = [
            "python", "-m", "pytest",
            "-k", "test_ocr_service_initialization or test_parse_resume_endpoint_ocr_enabled or test_parse_resume_async_ocr_enabled",
            str(self.test_path),
            "-v",
            "--tb=short",
            "--maxfail=3"
        ]
        
        return subprocess.run(cmd, cwd=self.backend_path).returncode


def main():
    """Main test runner entry point"""
    parser = argparse.ArgumentParser(description="OCR Integration Test Suite Runner")
    
    parser.add_argument(
        "test_type",
        choices=["unit", "integration", "api", "celery", "performance", "all", "coverage", "smoke"],
        help="Type of tests to run"
    )
    
    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="Verbose output"
    )
    
    parser.add_argument(
        "--include-performance",
        action="store_true",
        help="Include performance tests when running 'all'"
    )
    
    parser.add_argument(
        "--backend-path",
        type=Path,
        help="Path to backend directory"
    )
    
    args = parser.parse_args()
    
    runner = OCRTestRunner(args.backend_path)
    
    # Test type dispatch
    if args.test_type == "unit":
        result = runner.run_unit_tests(args.verbose)
    elif args.test_type == "integration":
        result = runner.run_integration_tests(args.verbose)
    elif args.test_type == "api":
        result = runner.run_api_tests(args.verbose)
    elif args.test_type == "celery":
        result = runner.run_celery_tests(args.verbose)
    elif args.test_type == "performance":
        result = runner.run_performance_tests(args.verbose)
    elif args.test_type == "all":
        result = runner.run_all_ocr_tests(args.verbose, args.include_performance)
    elif args.test_type == "coverage":
        result = runner.run_coverage_report()
    elif args.test_type == "smoke":
        result = runner.run_smoke_tests()
    else:
        print(f"❌ Unknown test type: {args.test_type}")
        return 1
    
    # Results summary
    if result == 0:
        print(f"\n✅ OCR {args.test_type} tests completed successfully!")
    else:
        print(f"\n❌ OCR {args.test_type} tests failed with exit code {result}")
    
    return result


if __name__ == "__main__":
    sys.exit(main())