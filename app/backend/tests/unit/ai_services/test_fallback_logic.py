"""
Tests for AI Service Manager Fallback Logic
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch
from tests.fixtures.ai_mocks import (
    MockAIServiceManager,
    create_mock_ai_service_manager,
    create_failing_provider,
    create_slow_provider
)


class TestFallbackLogic:
    """Test suite for provider fallback mechanisms"""
    
    @pytest.fixture
    def mock_manager(self):
        """Create mock AI manager for testing"""
        return create_mock_ai_service_manager()
    
    @pytest.mark.asyncio
    async def test_primary_success_no_fallback_needed(self, mock_manager):
        """Test that fallback is not used when primary succeeds"""
        messages = [{"role": "user", "content": "Test message"}]
        
        result = await mock_manager.create_completion(messages)
        
        assert result is not None
        assert len(mock_manager.call_history) == 1
        assert mock_manager.call_history[0]["provider"] == "deepseek"
        assert "fallback" not in mock_manager.call_history[0]["method"]
    
    @pytest.mark.asyncio
    async def test_primary_fails_fallback_succeeds(self, mock_manager):
        """Test fallback activation when primary fails"""
        # Make primary provider fail
        mock_manager.set_provider_fail_rate("deepseek", 1.0)
        
        messages = [{"role": "user", "content": "Test message"}]
        
        result = await mock_manager.create_completion(messages, use_fallback=True)
        
        assert result is not None
        assert len(mock_manager.call_history) == 2
        assert mock_manager.call_history[0]["provider"] == "deepseek"
        assert mock_manager.call_history[1]["provider"] == "moonshot"
        assert mock_manager.call_history[1]["method"] == "create_completion_fallback"
    
    @pytest.mark.asyncio
    async def test_both_providers_fail(self, mock_manager):
        """Test behavior when both primary and fallback fail"""
        # Make both providers fail
        mock_manager.set_provider_fail_rate("deepseek", 1.0)
        mock_manager.set_provider_fail_rate("moonshot", 1.0)
        
        messages = [{"role": "user", "content": "Test message"}]
        
        with pytest.raises(Exception) as exc_info:
            await mock_manager.create_completion(messages, use_fallback=True)
        
        assert "Mock moonshot provider failed" in str(exc_info.value)
        assert len(mock_manager.call_history) == 2
    
    @pytest.mark.asyncio
    async def test_fallback_disabled(self, mock_manager):
        """Test that fallback doesn't activate when disabled"""
        # Make primary provider fail
        mock_manager.set_provider_fail_rate("deepseek", 1.0)
        
        messages = [{"role": "user", "content": "Test message"}]
        
        with pytest.raises(Exception) as exc_info:
            await mock_manager.create_completion(messages, use_fallback=False)
        
        assert "Mock deepseek provider failed" in str(exc_info.value)
        assert len(mock_manager.call_history) == 1
        assert "fallback" not in mock_manager.call_history[0]["method"]
    
    @pytest.mark.asyncio
    async def test_embedding_fallback(self, mock_manager):
        """Test fallback for embedding generation"""
        # Make primary provider fail
        mock_manager.set_provider_fail_rate("deepseek", 1.0)
        
        text = "Test document for embedding"
        
        embedding = await mock_manager.create_embedding(text, use_fallback=True)
        
        assert embedding is not None
        assert len(embedding) == 1024
        assert len(mock_manager.call_history) == 2
        assert mock_manager.call_history[1]["method"] == "create_embedding_fallback"
        assert mock_manager.call_history[1]["provider"] == "moonshot"
    
    @pytest.mark.asyncio
    async def test_intermittent_failures(self, mock_manager):
        """Test behavior with intermittent failures (50% fail rate)"""
        # Set 50% failure rate
        mock_manager.set_provider_fail_rate("deepseek", 0.5)
        
        messages = [{"role": "user", "content": "Test message"}]
        
        # Run multiple times to test both success and failure scenarios
        results = []
        for _ in range(10):
            mock_manager.reset_all()
            mock_manager.set_provider_fail_rate("deepseek", 0.5)
            
            try:
                result = await mock_manager.create_completion(messages, use_fallback=True)
                results.append(("success", len(mock_manager.call_history)))
            except Exception:
                results.append(("failure", len(mock_manager.call_history)))
        
        # Should have mix of direct successes and fallback uses
        successes = [r for r in results if r[0] == "success"]
        assert len(successes) > 0  # Some should succeed
        
        # Check that some used fallback (2 calls) and some didn't (1 call)
        single_calls = [r for r in successes if r[1] == 1]
        double_calls = [r for r in successes if r[1] == 2]
        
        # With 50% fail rate, we expect both scenarios
        assert len(single_calls) > 0 or len(double_calls) > 0
    
    @pytest.mark.asyncio
    async def test_fallback_preserves_parameters(self, mock_manager):
        """Test that parameters are preserved when falling back"""
        # Make primary fail
        mock_manager.set_provider_fail_rate("deepseek", 1.0)
        
        messages = [{"role": "user", "content": "Generate questionnaire"}]
        
        result = await mock_manager.create_completion(
            messages,
            temperature=0.3,
            max_tokens=1500,
            response_format="json",
            use_fallback=True
        )
        
        assert result is not None
        assert isinstance(result, dict)  # JSON format preserved
        # In real implementation, we'd verify temperature and max_tokens were passed
    
    @pytest.mark.asyncio
    async def test_fallback_logging(self, mock_manager):
        """Test that fallback reasons are properly logged"""
        # Make primary fail
        mock_manager.set_provider_fail_rate("deepseek", 1.0)
        
        messages = [{"role": "user", "content": "Test"}]
        
        await mock_manager.create_completion(messages, use_fallback=True)
        
        # Check fallback reason is logged
        fallback_entry = mock_manager.call_history[1]
        assert fallback_entry["method"] == "create_completion_fallback"
        assert "reason" in fallback_entry
        assert "Mock deepseek provider failed" in fallback_entry["reason"]
    
    @pytest.mark.asyncio
    async def test_health_check_affects_fallback(self, mock_manager):
        """Test that health status affects provider selection"""
        # Make primary unhealthy
        mock_manager.set_provider_health("deepseek", False)
        
        # In real implementation, unhealthy provider should trigger fallback
        assert mock_manager.providers["deepseek"].is_healthy is False
        assert mock_manager.providers["moonshot"].is_healthy is True
    
    @pytest.mark.asyncio
    async def test_concurrent_fallback_handling(self, mock_manager):
        """Test fallback behavior under concurrent load"""
        # Make primary fail 70% of the time
        mock_manager.set_provider_fail_rate("deepseek", 0.7)
        
        messages = [{"role": "user", "content": f"Test {i}"} for i in range(10)]
        
        tasks = [
            mock_manager.create_completion([msg], use_fallback=True)
            for msg in messages
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # All should eventually succeed (either primary or fallback)
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == 10
        
        # Check history for fallback usage
        fallback_calls = [
            call for call in mock_manager.call_history
            if "fallback" in call["method"]
        ]
        
        # With 70% failure rate, we expect significant fallback usage
        assert len(fallback_calls) > 3
    
    @pytest.mark.asyncio
    async def test_fallback_for_different_methods(self, mock_manager):
        """Test fallback works for all AI methods"""
        # Make primary fail
        mock_manager.set_provider_fail_rate("deepseek", 1.0)
        
        # Test evaluate_responses
        eval_result = await mock_manager.evaluate_responses(
            questionnaire_title="Test",
            position_type="Engineer",
            dimension_scores={},
            answers_summary=""
        )
        assert eval_result is not None
        
        # Test generate_questionnaire
        quest_result = await mock_manager.generate_questionnaire(
            position_type="Engineer",
            dimensions=["Technical"],
            question_count=5,
            industry="Tech"
        )
        assert quest_result is not None
        
        # Test evaluate_text_response
        text_eval = await mock_manager.evaluate_text_response(
            question="What is Python?",
            response="A programming language"
        )
        assert text_eval is not None
        
        # All should have used fallback
        fallback_calls = [
            call for call in mock_manager.call_history
            if "fallback" in call["method"]
        ]
        assert len(fallback_calls) >= 3
    
    @pytest.mark.asyncio
    async def test_fallback_chain_three_providers(self, mock_manager):
        """Test fallback chain with multiple fallback options"""
        # This tests the current two-provider system
        # In a real implementation with 3+ provider chain:
        # primary -> fallback1 -> fallback2 -> error
        
        # Make first two providers fail
        mock_manager.set_provider_fail_rate("deepseek", 1.0)
        mock_manager.set_provider_fail_rate("moonshot", 1.0)
        
        messages = [{"role": "user", "content": "Test"}]
        
        # Current implementation only supports 2-level fallback
        with pytest.raises(Exception):
            await mock_manager.create_completion(messages, use_fallback=True)
        
        # In enhanced implementation, we'd test:
        # mock_manager.fallback_chain = ["moonshot", "openai", "qwen"]
        # And expect it to try all three before failing
    
    @pytest.mark.asyncio  
    async def test_fallback_performance_tracking(self, mock_manager):
        """Test that performance metrics are tracked during fallback"""
        import time
        
        # Create slow primary provider
        slow_provider = create_slow_provider("deepseek", delay=0.1)
        mock_manager.providers["deepseek"] = slow_provider
        
        start_time = time.time()
        messages = [{"role": "user", "content": "Test"}]
        
        # Make primary fail after delay
        mock_manager.set_provider_fail_rate("deepseek", 1.0)
        
        await mock_manager.create_completion(messages, use_fallback=True)
        
        elapsed = time.time() - start_time
        
        # Should take at least the delay time due to slow primary
        assert elapsed >= 0.1
        
        # Should have tried primary then fallback
        assert len(mock_manager.call_history) == 2