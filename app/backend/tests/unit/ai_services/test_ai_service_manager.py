"""
Unit tests for AIServiceManager
"""
import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List
import json

from app.services.ai_service_manager import ai_service_manager, AIServiceManager
from app.core.ai_config import ai_settings
from tests.fixtures.ai_mocks import (
    MockAIServiceManager,
    create_mock_ai_service_manager,
    create_failing_provider,
    MOCK_RESUME_EXTRACTION,
    MOCK_QUESTIONNAIRE,
    MOCK_EVALUATION
)


class TestAIServiceManager:
    """Test suite for AIServiceManager core functionality"""
    
    @pytest.fixture
    def mock_ai_manager(self):
        """Create mock AI service manager"""
        return create_mock_ai_service_manager()
    
    @pytest.fixture
    def real_ai_manager(self):
        """Get real AI service manager instance"""
        # Note: We'll mock the actual API calls
        return AIServiceManager()
    
    def test_singleton_pattern(self):
        """Test that AIServiceManager follows singleton pattern"""
        # Import the module-level instance
        from app.services.ai_service_manager import ai_service_manager
        
        # Should be the same instance
        assert ai_service_manager is not None
        assert isinstance(ai_service_manager, AIServiceManager)
        
        # Creating new instance should return the same object
        another_instance = AIServiceManager()
        assert another_instance is ai_service_manager
    
    @pytest.mark.asyncio
    async def test_provider_initialization(self, mock_ai_manager):
        """Test provider initialization and configuration"""
        # Check all providers are initialized
        assert "deepseek" in mock_ai_manager.providers
        assert "moonshot" in mock_ai_manager.providers
        assert "openrouter" in mock_ai_manager.providers
        assert "qwen" in mock_ai_manager.providers
        assert "openai" in mock_ai_manager.providers
        
        # Check default provider
        assert mock_ai_manager.current_provider == "deepseek"
        assert mock_ai_manager.fallback_provider == "moonshot"
    
    @pytest.mark.asyncio
    async def test_create_completion_success(self, mock_ai_manager):
        """Test successful completion creation"""
        messages = [
            {"role": "system", "content": "You are a helpful assistant"},
            {"role": "user", "content": "Hello"}
        ]
        
        result = await mock_ai_manager.create_completion(messages)
        
        assert result is not None
        assert mock_ai_manager.call_history[0]["method"] == "create_completion"
        assert mock_ai_manager.call_history[0]["provider"] == "deepseek"
    
    @pytest.mark.asyncio
    async def test_create_completion_with_fallback(self, mock_ai_manager):
        """Test completion creation with fallback on primary failure"""
        # Make primary provider fail
        mock_ai_manager.set_provider_fail_rate("deepseek", 1.0)
        
        messages = [
            {"role": "system", "content": "You are a helpful assistant"},
            {"role": "user", "content": "Hello"}
        ]
        
        result = await mock_ai_manager.create_completion(messages, use_fallback=True)
        
        assert result is not None
        # Check that fallback was used
        assert len(mock_ai_manager.call_history) == 2
        assert mock_ai_manager.call_history[1]["method"] == "create_completion_fallback"
        assert mock_ai_manager.call_history[1]["provider"] == "moonshot"
    
    @pytest.mark.asyncio
    async def test_create_completion_no_fallback(self, mock_ai_manager):
        """Test completion creation fails when fallback is disabled"""
        # Make primary provider fail
        mock_ai_manager.set_provider_fail_rate("deepseek", 1.0)
        
        messages = [
            {"role": "system", "content": "You are a helpful assistant"},
            {"role": "user", "content": "Hello"}
        ]
        
        with pytest.raises(Exception) as exc_info:
            await mock_ai_manager.create_completion(messages, use_fallback=False)
        
        assert "Mock deepseek provider failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_create_embedding_success(self, mock_ai_manager):
        """Test successful embedding creation"""
        text = "This is a test document for embedding"
        
        embedding = await mock_ai_manager.create_embedding(text)
        
        assert embedding is not None
        assert len(embedding) == 1024  # BGE-M3 dimension
        assert all(isinstance(x, float) for x in embedding)
        assert mock_ai_manager.call_history[0]["method"] == "create_embedding"
    
    @pytest.mark.asyncio
    async def test_create_embedding_with_fallback(self, mock_ai_manager):
        """Test embedding creation with fallback"""
        # Make primary provider fail
        mock_ai_manager.set_provider_fail_rate("deepseek", 1.0)
        
        text = "This is a test document for embedding"
        
        embedding = await mock_ai_manager.create_embedding(text, use_fallback=True)
        
        assert embedding is not None
        assert len(embedding) == 1024
        # Check that fallback was used
        assert len(mock_ai_manager.call_history) == 2
        assert mock_ai_manager.call_history[1]["method"] == "create_embedding_fallback"
    
    @pytest.mark.asyncio
    async def test_json_response_format(self, mock_ai_manager):
        """Test JSON response format handling"""
        messages = [
            {"role": "user", "content": "Extract resume information"}
        ]
        
        result = await mock_ai_manager.create_completion(
            messages,
            response_format="json"
        )
        
        assert isinstance(result, dict)
        assert "name" in result or "response" in result
    
    @pytest.mark.asyncio
    async def test_evaluate_responses(self, mock_ai_manager):
        """Test response evaluation method"""
        result = await mock_ai_manager.evaluate_responses(
            questionnaire_title="Test Questionnaire",
            position_type="Software Engineer",
            dimension_scores={"technical": 85, "communication": 90},
            answers_summary="Sample answers"
        )
        
        assert isinstance(result, dict)
        assert "score" in result
        assert result["score"] == 85
    
    @pytest.mark.asyncio
    async def test_generate_questionnaire(self, mock_ai_manager):
        """Test questionnaire generation"""
        result = await mock_ai_manager.generate_questionnaire(
            position_type="Software Engineer",
            dimensions=["Technical", "Communication"],
            question_count=10,
            industry="Technology"
        )
        
        assert isinstance(result, dict)
        assert "title" in result
        assert "questions" in result
        assert len(result["questions"]) > 0
    
    def test_is_available(self, mock_ai_manager):
        """Test provider availability check"""
        # All providers healthy by default
        assert mock_ai_manager.is_available() is True
        
        # Make all providers unhealthy
        for provider in mock_ai_manager.providers:
            mock_ai_manager.set_provider_health(provider, False)
        
        assert mock_ai_manager.is_available() is False
        
        # Make one provider healthy
        mock_ai_manager.set_provider_health("moonshot", True)
        assert mock_ai_manager.is_available() is True
    
    def test_get_current_provider_info(self, mock_ai_manager):
        """Test getting current provider information"""
        info = mock_ai_manager.get_current_provider_info()
        
        assert info["name"] == "deepseek"
        assert info["type"] == "mock"
        assert info["status"] == "healthy"
        
        # Make provider unhealthy
        mock_ai_manager.set_provider_health("deepseek", False)
        info = mock_ai_manager.get_current_provider_info()
        assert info["status"] == "unhealthy"
    
    @pytest.mark.asyncio
    async def test_provider_switching(self, mock_ai_manager):
        """Test switching between providers"""
        # Initial provider
        assert mock_ai_manager.current_provider == "deepseek"
        
        # Switch to different provider
        mock_ai_manager.current_provider = "openai"
        
        messages = [{"role": "user", "content": "Test"}]
        await mock_ai_manager.create_completion(messages)
        
        assert mock_ai_manager.call_history[0]["provider"] == "openai"
    
    @pytest.mark.asyncio
    async def test_cascading_fallback(self, mock_ai_manager):
        """Test cascading fallback when multiple providers fail"""
        # Make primary and first fallback fail
        mock_ai_manager.set_provider_fail_rate("deepseek", 1.0)
        mock_ai_manager.set_provider_fail_rate("moonshot", 1.0)
        
        # Set a second fallback
        mock_ai_manager.fallback_provider = "openai"
        
        # This should fail since we don't have cascading fallback implemented
        # but it tests the current behavior
        messages = [{"role": "user", "content": "Test"}]
        
        with pytest.raises(Exception):
            await mock_ai_manager.create_completion(messages)
    
    @pytest.mark.asyncio
    async def test_evaluate_text_response(self, mock_ai_manager):
        """Test text response evaluation"""
        result = await mock_ai_manager.evaluate_text_response(
            question="What is Python?",
            response="Python is a programming language"
        )
        
        assert isinstance(result, dict)
        assert "score" in result
        assert "feedback" in result
    
    @pytest.mark.asyncio
    async def test_generate_feedback(self, mock_ai_manager):
        """Test feedback generation"""
        result = await mock_ai_manager.generate_feedback(
            scores={"technical": 85},
            total_score=85
        )
        
        assert result is not None
        assert isinstance(result, str) or isinstance(result, dict)
    
    def test_call_history_tracking(self, mock_ai_manager):
        """Test that call history is properly tracked"""
        mock_ai_manager.reset_all()
        assert len(mock_ai_manager.get_call_history()) == 0
        
        # Make some calls
        import asyncio
        loop = asyncio.get_event_loop()
        
        messages = [{"role": "user", "content": "Test"}]
        loop.run_until_complete(mock_ai_manager.create_completion(messages))
        
        history = mock_ai_manager.get_call_history()
        assert len(history) == 1
        assert history[0]["method"] == "create_completion"
    
    @pytest.mark.asyncio
    async def test_temperature_and_max_tokens(self, mock_ai_manager):
        """Test that temperature and max_tokens parameters are passed correctly"""
        messages = [{"role": "user", "content": "Test"}]
        
        result = await mock_ai_manager.create_completion(
            messages,
            temperature=0.5,
            max_tokens=1000
        )
        
        assert result is not None
        # In real implementation, we'd verify these were passed to the provider
    
    @pytest.mark.asyncio
    async def test_concurrent_requests(self, mock_ai_manager):
        """Test handling of concurrent requests"""
        import asyncio
        
        messages = [{"role": "user", "content": f"Test {i}"} for i in range(5)]
        
        tasks = [
            mock_ai_manager.create_completion([msg])
            for msg in messages
        ]
        
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 5
        assert all(r is not None for r in results)
        assert len(mock_ai_manager.get_call_history()) == 5


class TestAIServiceManagerIntegration:
    """Integration tests with real AIServiceManager (mocked API calls)"""
    
    @pytest.mark.asyncio
    @patch('app.services.ai_service_manager.AsyncOpenAI')
    async def test_real_manager_initialization(self, mock_openai_class):
        """Test real AIServiceManager initialization with mocked clients"""
        mock_client = AsyncMock()
        mock_openai_class.return_value = mock_client
        
        # Force reinitialization
        AIServiceManager._instance = None
        manager = AIServiceManager()
        
        assert manager is not None
        assert manager.primary_provider is not None
    
    @pytest.mark.asyncio
    @patch('app.services.ai_service_manager.httpx.AsyncClient')
    async def test_ollama_embedding_integration(self, mock_httpx):
        """Test Ollama embedding integration"""
        mock_client = AsyncMock()
        mock_response = AsyncMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"embedding": [0.1] * 1024}
        mock_client.post.return_value = mock_response
        mock_httpx.return_value = mock_client
        
        # This would test the real Ollama integration
        # For now, we use mocks
        pass