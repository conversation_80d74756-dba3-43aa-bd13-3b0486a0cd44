"""
Unit Tests for AI Service Manager Health Monitoring
Tests health check functionality, provider monitoring, and system status reporting
"""
import pytest
import pytest_asyncio
import asyncio
import time
from datetime import datetime, timezone
from unittest.mock import AsyncMock, patch, MagicMock
from typing import Dict, Any

from app.services.ai_service_manager import AIServiceManager, ai_service_manager
from app.core.ai_config import ai_settings


class TestSingleProviderHealthChecks:
    """Test individual provider health checking"""

    @pytest.fixture
    def mock_manager_health(self):
        """Create manager with mocked clients for health testing"""
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.ollama') as mock_ollama, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis:
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_ollama

    @pytest.mark.asyncio
    async def test_healthy_provider_check(self, mock_manager_health):
        """Test health check for a healthy provider"""
        manager, mock_client, _ = mock_manager_health
        
        # Mock successful health check response
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="pong"))]
        )
        
        result = await manager.check_provider_health("zhipu")
        
        assert result["status"] == "healthy"
        assert result["provider"] == "zhipu"
        assert "response_time_ms" in result
        assert "models" in result
        assert "timestamp" in result
        assert isinstance(result["response_time_ms"], (int, float))
        assert result["response_time_ms"] >= 0

    @pytest.mark.asyncio
    async def test_unhealthy_provider_check(self, mock_manager_health):
        """Test health check for an unhealthy provider"""
        manager, mock_client, _ = mock_manager_health
        
        # Mock health check failure
        mock_client.chat.completions.create.side_effect = Exception("Provider unavailable")
        
        result = await manager.check_provider_health("zhipu")
        
        assert result["status"] == "unhealthy"
        assert result["provider"] == "zhipu"
        assert "error" in result
        assert "Provider unavailable" in result["error"]
        assert "response_time_ms" in result
        assert "timestamp" in result

    @pytest.mark.asyncio
    async def test_unknown_provider_health_check(self, mock_manager_health):
        """Test health check for unknown provider"""
        manager, _, _ = mock_manager_health
        
        result = await manager.check_provider_health("unknown_provider")
        
        assert result["status"] == "unhealthy"
        assert result["provider"] == "unknown_provider"
        assert "Unknown provider" in result["error"]

    @pytest.mark.asyncio
    async def test_unconfigured_provider_health_check(self, mock_manager_health):
        """Test health check for unconfigured provider"""
        manager, _, _ = mock_manager_health
        
        # Test a valid provider that's not configured
        result = await manager.check_provider_health("deepseek")
        
        assert result["status"] == "unhealthy"
        assert result["provider"] == "deepseek"
        assert "not configured" in result["error"]

    @pytest.mark.asyncio
    async def test_ollama_provider_health_check(self, mock_manager_health):
        """Test Ollama-specific health check logic"""
        manager, _, mock_ollama = mock_manager_health
        
        # Mock successful Ollama health check
        mock_ollama.list = AsyncMock(return_value={"models": []})
        
        with patch('asyncio.to_thread') as mock_to_thread:
            mock_to_thread.return_value = {"models": []}
            
            result = await manager.check_provider_health("ollama")
            
            assert result["status"] == "healthy"
            assert result["provider"] == "ollama"

    @pytest.mark.asyncio
    async def test_health_check_response_time_measurement(self, mock_manager_health):
        """Test that response times are accurately measured"""
        manager, mock_client, _ = mock_manager_health
        
        # Mock slow response
        async def slow_response(*args, **kwargs):
            await asyncio.sleep(0.1)  # 100ms delay
            return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="slow_pong"))])
        
        mock_client.chat.completions.create.side_effect = slow_response
        
        result = await manager.check_provider_health("zhipu")
        
        assert result["status"] == "healthy"
        assert result["response_time_ms"] >= 100  # Should be at least 100ms
        assert result["response_time_ms"] < 1000   # But reasonable upper bound

    @pytest.mark.asyncio
    async def test_health_check_timeout_handling(self, mock_manager_health):
        """Test health check timeout handling"""
        manager, mock_client, _ = mock_manager_health
        
        # Mock timeout
        mock_client.chat.completions.create.side_effect = asyncio.TimeoutError("Health check timeout")
        
        result = await manager.check_provider_health("zhipu")
        
        assert result["status"] == "unhealthy"
        assert "timeout" in result["error"].lower()

    @pytest.mark.asyncio
    async def test_health_check_model_info_inclusion(self, mock_manager_health):
        """Test that health check includes model information"""
        manager, mock_client, _ = mock_manager_health
        
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="healthy"))]
        )
        
        result = await manager.check_provider_health("zhipu")
        
        assert result["status"] == "healthy"
        assert "models" in result
        assert "llm" in result["models"]
        assert "embedding" in result["models"]


class TestComprehensiveHealthChecks:
    """Test comprehensive health checking for all providers"""

    @pytest.fixture
    def mock_manager_comprehensive(self):
        """Create manager for comprehensive health testing"""
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.ollama') as mock_ollama, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis:
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_ollama

    @pytest.mark.asyncio
    async def test_all_providers_healthy(self, mock_manager_comprehensive):
        """Test comprehensive health check when all providers are healthy"""
        manager, mock_client, mock_ollama = mock_manager_comprehensive
        
        # Mock all providers as healthy
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="healthy"))]
        )
        mock_ollama.list = AsyncMock(return_value={"models": []})
        
        with patch('asyncio.to_thread', return_value={"models": []}):
            result = await manager.check_all_providers_health()
        
        assert result["status"] == "healthy"
        assert "providers" in result
        assert "total_check_time_ms" in result
        assert "available_providers" in result
        assert "default_providers" in result
        assert isinstance(result["total_check_time_ms"], (int, float))

    @pytest.mark.asyncio
    async def test_mixed_provider_health(self, mock_manager_comprehensive):
        """Test comprehensive health check with mixed provider status"""
        manager, mock_client, mock_ollama = mock_manager_comprehensive
        
        # Mock mixed responses - some succeed, some fail
        call_count = 0
        def mixed_health(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count % 2 == 0:  # Every second provider fails
                raise Exception("Provider temporarily unavailable")
            return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="healthy"))])
        
        mock_client.chat.completions.create.side_effect = mixed_health
        mock_ollama.list = AsyncMock(return_value={"models": []})
        
        with patch('asyncio.to_thread', return_value={"models": []}):
            result = await manager.check_all_providers_health()
        
        # Should be degraded (some healthy, some not)
        assert result["status"] in ["degraded", "unhealthy"]
        assert "providers" in result
        
        # Check individual provider statuses
        providers = result["providers"]
        healthy_count = sum(1 for p in providers.values() if p.get("status") == "healthy")
        unhealthy_count = sum(1 for p in providers.values() if p.get("status") == "unhealthy")
        
        assert healthy_count > 0
        assert unhealthy_count > 0

    @pytest.mark.asyncio
    async def test_all_providers_unhealthy(self, mock_manager_comprehensive):
        """Test comprehensive health check when all providers are unhealthy"""
        manager, mock_client, mock_ollama = mock_manager_comprehensive
        
        # Mock all providers as failing
        mock_client.chat.completions.create.side_effect = Exception("All providers down")
        mock_ollama.list = AsyncMock(side_effect=Exception("Ollama down"))
        
        with patch('asyncio.to_thread', side_effect=Exception("Ollama down")):
            result = await manager.check_all_providers_health()
        
        assert result["status"] == "unhealthy"
        assert "providers" in result
        
        # All providers should be unhealthy
        providers = result["providers"]
        for provider_status in providers.values():
            assert provider_status.get("status") == "unhealthy"

    @pytest.mark.asyncio
    async def test_concurrent_health_checks(self, mock_manager_comprehensive):
        """Test that health checks are performed concurrently"""
        manager, mock_client, mock_ollama = mock_manager_comprehensive
        
        check_times = []
        
        async def timed_response(*args, **kwargs):
            check_times.append(time.time())
            await asyncio.sleep(0.1)  # 100ms delay per check
            return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="healthy"))])
        
        mock_client.chat.completions.create.side_effect = timed_response
        mock_ollama.list = AsyncMock(return_value={"models": []})
        
        start_time = time.time()
        
        with patch('asyncio.to_thread', return_value={"models": []}):
            result = await manager.check_all_providers_health()
        
        total_time = time.time() - start_time
        
        # With concurrent execution, total time should be much less than sequential
        # If we had 5 providers * 0.1s = 0.5s sequential, concurrent should be ~0.1s
        assert total_time < 0.3  # Much less than sequential would take
        assert result["status"] in ["healthy", "degraded", "unhealthy"]

    @pytest.mark.asyncio
    async def test_health_check_error_handling(self, mock_manager_comprehensive):
        """Test error handling in comprehensive health checks"""
        manager, mock_client, mock_ollama = mock_manager_comprehensive
        
        # Mock some providers throwing unexpected errors
        def random_errors(*args, **kwargs):
            import random
            error_types = [
                Exception("Generic error"),
                asyncio.TimeoutError("Timeout"),
                ConnectionError("Connection failed"),
                ValueError("Invalid response")
            ]
            raise random.choice(error_types)
        
        mock_client.chat.completions.create.side_effect = random_errors
        mock_ollama.list = AsyncMock(side_effect=random_errors)
        
        with patch('asyncio.to_thread', side_effect=random_errors):
            result = await manager.check_all_providers_health()
        
        # Should handle all errors gracefully
        assert "status" in result
        assert result["status"] == "unhealthy"
        assert "providers" in result
        
        # All providers should have error information
        for provider_status in result["providers"].values():
            assert provider_status.get("status") == "unhealthy"

    @pytest.mark.asyncio
    async def test_health_check_caching(self, mock_manager_comprehensive):
        """Test health status caching mechanism"""
        manager, mock_client, mock_ollama = mock_manager_comprehensive
        
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="healthy"))]
        )
        mock_ollama.list = AsyncMock(return_value={"models": []})
        
        with patch('asyncio.to_thread', return_value={"models": []}):
            # First check should populate cache
            result1 = await manager.check_all_providers_health()
            
            assert manager._last_health_check is not None
            assert len(manager._health_status) > 0
            
            # Cache should contain status for each provider
            cached_status = manager._health_status
            assert len(cached_status) > 0
            
            for provider_name, status in cached_status.items():
                assert "status" in status
                assert status["status"] in ["healthy", "unhealthy"]


class TestServiceMethodHealthChecks:
    """Test health checking for service methods"""

    @pytest.fixture
    def mock_manager_service_health(self):
        """Create manager for service method health testing"""
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis:
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client

    @pytest.mark.asyncio
    async def test_service_method_health_check_success(self, mock_manager_service_health):
        """Test successful service method health check"""
        manager, mock_client = mock_manager_service_health
        
        # Mock successful responses for all service methods
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="health test response"))]
        )
        mock_client.embeddings.create.return_value = AsyncMock(
            data=[AsyncMock(embedding=[0.1] * 100)]
        )
        
        result = await manager.check_service_method_health()
        
        assert result["status"] in ["healthy", "degraded"]
        assert "service_methods" in result
        assert "providers" in result
        assert "total_check_time_ms" in result
        
        # Check individual service methods
        methods = result["service_methods"]
        expected_methods = ["generate_text", "generate_embedding", "rerank_documents"]
        
        for method in expected_methods:
            assert method in methods
            method_result = methods[method]
            assert "status" in method_result
            if method_result["status"] == "healthy":
                assert "response_time_ms" in method_result
                assert "provider" in method_result

    @pytest.mark.asyncio
    async def test_service_method_health_check_failures(self, mock_manager_service_health):
        """Test service method health check with failures"""
        manager, mock_client = mock_manager_service_health
        
        # Mock failures for service methods
        mock_client.chat.completions.create.side_effect = Exception("LLM service failed")
        mock_client.embeddings.create.side_effect = Exception("Embedding service failed")
        
        result = await manager.check_service_method_health()
        
        assert result["status"] == "unhealthy"
        assert "service_methods" in result
        
        # All service methods should have failed
        methods = result["service_methods"]
        for method_name, method_result in methods.items():
            if method_result["status"] == "unhealthy":
                assert "error" in method_result

    @pytest.mark.asyncio
    async def test_individual_service_method_tests(self, mock_manager_service_health):
        """Test individual service method test functions"""
        manager, mock_client = mock_manager_service_health
        
        # Test _test_text_generation
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="test response"))]
        )
        
        result = await manager._test_text_generation()
        assert "provider" in result
        assert "response_length" in result
        assert result["response_length"] > 0
        
        # Test _test_embedding_generation
        mock_client.embeddings.create.return_value = AsyncMock(
            data=[AsyncMock(embedding=[0.1] * 1024)]
        )
        
        result = await manager._test_embedding_generation()
        assert "provider" in result
        assert "embedding_dimension" in result
        assert result["embedding_dimension"] == 1024
        
        # Test _test_document_reranking
        mock_client.embeddings.create.side_effect = [
            AsyncMock(data=[AsyncMock(embedding=[1.0, 0.0])]),  # Query
            AsyncMock(data=[AsyncMock(embedding=[0.8, 0.6])]),  # Doc 1
            AsyncMock(data=[AsyncMock(embedding=[0.2, 0.9])])   # Doc 2
        ]
        
        result = await manager._test_document_reranking()
        assert "provider" in result
        assert "results_count" in result
        assert result["results_count"] > 0

    @pytest.mark.asyncio
    async def test_service_health_performance_measurement(self, mock_manager_service_health):
        """Test that service health checks measure performance"""
        manager, mock_client = mock_manager_service_health
        
        # Mock slow responses
        async def slow_llm(*args, **kwargs):
            await asyncio.sleep(0.1)
            return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="slow response"))])
        
        async def slow_embedding(*args, **kwargs):
            await asyncio.sleep(0.05)
            return AsyncMock(data=[AsyncMock(embedding=[0.1] * 100)])
        
        mock_client.chat.completions.create.side_effect = slow_llm
        mock_client.embeddings.create.side_effect = slow_embedding
        
        result = await manager.check_service_method_health()
        
        methods = result["service_methods"]
        
        if "generate_text" in methods and methods["generate_text"]["status"] == "healthy":
            assert methods["generate_text"]["response_time_ms"] >= 100
        
        if "generate_embedding" in methods and methods["generate_embedding"]["status"] == "healthy":
            assert methods["generate_embedding"]["response_time_ms"] >= 50


class TestHealthMonitoringConfiguration:
    """Test health monitoring configuration and settings"""

    @pytest.fixture
    def manager(self):
        """Get AI service manager"""
        return ai_service_manager

    def test_health_check_interval_configuration(self, manager):
        """Test health check interval configuration"""
        # Check that health check interval is properly set
        assert hasattr(manager, '_health_check_interval')
        assert isinstance(manager._health_check_interval, int)
        assert manager._health_check_interval > 0

    def test_health_status_initialization(self, manager):
        """Test health status initialization"""
        # Check that health status structures are initialized
        assert hasattr(manager, '_health_status')
        assert hasattr(manager, '_last_health_check')
        assert isinstance(manager._health_status, dict)

    def test_available_providers_list(self, manager):
        """Test getting available providers list"""
        providers = manager.get_available_providers()
        
        assert isinstance(providers, list)
        assert len(providers) > 0
        assert all(isinstance(provider, str) for provider in providers)

    def test_provider_config_retrieval(self, manager):
        """Test provider configuration retrieval"""
        providers = manager.get_available_providers()
        
        for provider in providers:
            config = manager.get_provider_config(provider)
            
            if config:  # Some providers might not be configured
                assert isinstance(config, dict)
                # Should have either LLM or embedding model config
                assert ("llm_model" in config or 
                       "embedding_model" in config or
                       "temperature" in config or
                       "max_tokens" in config)

    def test_invalid_provider_config_retrieval(self, manager):
        """Test configuration retrieval for invalid provider"""
        config = manager.get_provider_config("nonexistent_provider")
        assert config is None


class TestHealthStatusReporting:
    """Test health status reporting and formatting"""

    @pytest.fixture
    def mock_manager_reporting(self):
        """Create manager for health reporting tests"""
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis:
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client

    @pytest.mark.asyncio
    async def test_health_status_format_consistency(self, mock_manager_reporting):
        """Test that health status reports have consistent format"""
        manager, mock_client = mock_manager_reporting
        
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="healthy"))]
        )
        
        # Test single provider health format
        single_result = await manager.check_provider_health("zhipu")
        required_fields = ["status", "provider", "timestamp"]
        
        for field in required_fields:
            assert field in single_result
        
        # Status should be valid value
        assert single_result["status"] in ["healthy", "unhealthy"]
        
        # Timestamp should be ISO format
        timestamp = single_result["timestamp"]
        assert isinstance(timestamp, str)
        # Should be parseable as ISO datetime
        datetime.fromisoformat(timestamp.replace('Z', '+00:00'))

    @pytest.mark.asyncio
    async def test_comprehensive_health_status_format(self, mock_manager_reporting):
        """Test comprehensive health status report format"""
        manager, mock_client = mock_manager_reporting
        
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="healthy"))]
        )
        
        result = await manager.check_all_providers_health()
        
        # Check required top-level fields
        required_fields = [
            "status", "timestamp", "total_check_time_ms", 
            "providers", "available_providers", "default_providers"
        ]
        
        for field in required_fields:
            assert field in result
        
        # Check data types
        assert isinstance(result["status"], str)
        assert isinstance(result["total_check_time_ms"], (int, float))
        assert isinstance(result["providers"], dict)
        assert isinstance(result["available_providers"], list)
        assert isinstance(result["default_providers"], dict)
        
        # Check status is valid
        assert result["status"] in ["healthy", "degraded", "unhealthy"]

    @pytest.mark.asyncio
    async def test_service_method_health_status_format(self, mock_manager_reporting):
        """Test service method health status report format"""
        manager, mock_client = mock_manager_reporting
        
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="test response"))]
        )
        mock_client.embeddings.create.return_value = AsyncMock(
            data=[AsyncMock(embedding=[0.1] * 100)]
        )
        
        result = await manager.check_service_method_health()
        
        # Check required fields
        required_fields = ["status", "timestamp", "service_methods", "providers", "total_check_time_ms"]
        
        for field in required_fields:
            assert field in result
        
        # Check service methods structure
        service_methods = result["service_methods"]
        assert isinstance(service_methods, dict)
        
        expected_methods = ["generate_text", "generate_embedding", "rerank_documents"]
        for method in expected_methods:
            if method in service_methods:
                method_result = service_methods[method]
                assert "status" in method_result
                assert method_result["status"] in ["healthy", "unhealthy"]

    def test_timestamp_consistency(self, mock_manager_reporting):
        """Test that timestamps are consistent and properly formatted"""
        manager, _ = mock_manager_reporting
        
        # All timestamps should be UTC and ISO formatted
        import re
        iso_pattern = r'^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{6})?(\+00:00|Z)?$'
        
        # Create a mock result with timestamp
        mock_result = {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "provider": "test"
        }
        
        assert re.match(iso_pattern, mock_result["timestamp"])

    @pytest.mark.asyncio
    async def test_error_information_inclusion(self, mock_manager_reporting):
        """Test that error information is properly included in health reports"""
        manager, mock_client = mock_manager_reporting
        
        # Mock provider failure
        test_error = Exception("Test provider error")
        mock_client.chat.completions.create.side_effect = test_error
        
        result = await manager.check_provider_health("zhipu")
        
        assert result["status"] == "unhealthy"
        assert "error" in result
        assert "Test provider error" in result["error"]
        
        # Should still include timing information even on failure
        assert "response_time_ms" in result
        assert isinstance(result["response_time_ms"], (int, float))

    @pytest.mark.asyncio  
    async def test_model_information_reporting(self, mock_manager_reporting):
        """Test that model information is included in health reports"""
        manager, mock_client = mock_manager_reporting
        
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="healthy"))]
        )
        
        result = await manager.check_provider_health("zhipu")
        
        if result["status"] == "healthy":
            assert "models" in result
            models = result["models"]
            assert isinstance(models, dict)
            
            # Should include model information
            expected_model_types = ["llm", "embedding"]
            for model_type in expected_model_types:
                if model_type in models:
                    assert models[model_type] is not None