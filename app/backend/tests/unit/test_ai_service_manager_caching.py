"""
Unit Tests for AI Service Manager Caching System
Tests caching functionality, cache key generation, TTL management, and Redis integration
"""
import pytest
import pytest_asyncio
import asyncio
import json
import hashlib
from unittest.mock import AsyncMock, patch, MagicMock
from typing import Dict, Any

from app.services.ai_service_manager import AIServiceManager, ai_service_manager
from app.core.ai_config import ai_settings


class TestCacheKeyGeneration:
    """Test cache key generation logic"""

    @pytest.fixture
    def manager(self):
        """Get AI service manager"""
        return ai_service_manager

    def test_cache_key_deterministic(self, manager):
        """Test that cache keys are deterministic"""
        params = {
            "text": "test text",
            "provider": "zhipu",
            "model": "embedding-model"
        }
        
        key1 = manager._generate_cache_key("embedding", **params)
        key2 = manager._generate_cache_key("embedding", **params)
        
        assert key1 == key2
        assert key1.startswith("ai:embedding:")

    def test_cache_key_different_operations(self, manager):
        """Test that different operations generate different keys"""
        params = {"text": "test"}
        
        embedding_key = manager._generate_cache_key("embedding", **params)
        llm_key = manager._generate_cache_key("llm", **params)
        rerank_key = manager._generate_cache_key("rerank", **params)
        
        assert embedding_key != llm_key
        assert llm_key != rerank_key
        assert embedding_key != rerank_key

    def test_cache_key_parameter_sensitivity(self, manager):
        """Test that different parameters generate different keys"""
        base_params = {"text": "test", "provider": "zhipu"}
        
        key1 = manager._generate_cache_key("embedding", **base_params)
        
        # Different text
        key2 = manager._generate_cache_key("embedding", text="different", provider="zhipu")
        assert key1 != key2
        
        # Different provider
        key3 = manager._generate_cache_key("embedding", text="test", provider="moonshot")
        assert key1 != key3
        
        # Additional parameter
        key4 = manager._generate_cache_key("embedding", **base_params, model="new-model")
        assert key1 != key4

    def test_cache_key_parameter_order_independence(self, manager):
        """Test that parameter order doesn't affect cache key"""
        key1 = manager._generate_cache_key(
            "embedding",
            text="test", 
            provider="zhipu", 
            model="bge-m3"
        )
        
        key2 = manager._generate_cache_key(
            "embedding",
            model="bge-m3",
            provider="zhipu",
            text="test"
        )
        
        assert key1 == key2

    def test_cache_key_complex_objects(self, manager):
        """Test cache key generation with complex objects"""
        complex_params = {
            "messages": [
                {"role": "system", "content": "You are helpful"},
                {"role": "user", "content": "Hello"}
            ],
            "temperature": 0.7,
            "max_tokens": 100,
            "provider": "zhipu"
        }
        
        key = manager._generate_cache_key("llm", **complex_params)
        
        assert isinstance(key, str)
        assert key.startswith("ai:llm:")
        assert len(key) == len("ai:llm:") + 16  # ai:operation:16_char_hash

    def test_cache_key_unicode_handling(self, manager):
        """Test cache key generation with unicode content"""
        unicode_params = {
            "text": "测试文本 with émojis 🚀",
            "provider": "zhipu"
        }
        
        key = manager._generate_cache_key("embedding", **unicode_params)
        
        assert isinstance(key, str)
        assert key.startswith("ai:embedding:")


class TestCacheOperations:
    """Test cache set and get operations"""

    @pytest.fixture
    def mock_manager(self):
        """Create manager with mocked Redis"""
        with patch('app.services.ai_service_manager.get_redis') as mock_get_redis:
            mock_redis_client = AsyncMock()
            mock_get_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_redis_client

    @pytest.mark.asyncio
    async def test_set_cached_result_success(self, mock_manager):
        """Test successful cache set operation"""
        manager, mock_redis = mock_manager
        
        test_key = "test:key"
        test_data = {"result": "test data"}
        ttl = 1800
        
        await manager._set_cached_result(test_key, test_data, ttl=ttl)
        
        mock_redis.setex.assert_called_once_with(
            test_key,
            ttl,
            json.dumps(test_data, default=str)
        )

    @pytest.mark.asyncio
    async def test_set_cached_result_default_ttl(self, mock_manager):
        """Test cache set with default TTL"""
        manager, mock_redis = mock_manager
        
        test_key = "test:key"
        test_data = {"result": "test data"}
        
        await manager._set_cached_result(test_key, test_data)
        
        mock_redis.setex.assert_called_once_with(
            test_key,
            ai_settings.EMBEDDING_CACHE_TTL,
            json.dumps(test_data, default=str)
        )

    @pytest.mark.asyncio
    async def test_set_cached_result_error_handling(self, mock_manager):
        """Test cache set error handling"""
        manager, mock_redis = mock_manager
        
        # Mock Redis error
        mock_redis.setex.side_effect = Exception("Redis connection failed")
        
        # Should not raise exception
        await manager._set_cached_result("test:key", {"data": "test"})
        
        # Should have attempted the operation
        mock_redis.setex.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_cached_result_success(self, mock_manager):
        """Test successful cache get operation"""
        manager, mock_redis = mock_manager
        
        test_key = "test:key"
        test_data = {"result": "cached data"}
        
        mock_redis.get.return_value = json.dumps(test_data)
        
        result = await manager._get_cached_result(test_key)
        
        assert result == test_data
        mock_redis.get.assert_called_once_with(test_key)

    @pytest.mark.asyncio
    async def test_get_cached_result_miss(self, mock_manager):
        """Test cache get with cache miss"""
        manager, mock_redis = mock_manager
        
        mock_redis.get.return_value = None
        
        result = await manager._get_cached_result("test:key")
        
        assert result is None

    @pytest.mark.asyncio
    async def test_get_cached_result_error_handling(self, mock_manager):
        """Test cache get error handling"""
        manager, mock_redis = mock_manager
        
        mock_redis.get.side_effect = Exception("Redis connection failed")
        
        result = await manager._get_cached_result("test:key")
        
        assert result is None  # Should return None on error

    @pytest.mark.asyncio
    async def test_complex_data_serialization(self, mock_manager):
        """Test serialization of complex data types"""
        manager, mock_redis = mock_manager
        
        complex_data = {
            "strings": ["hello", "world"],
            "numbers": [1, 2.5, 3],
            "nested": {
                "bool": True,
                "null": None,
                "array": [1, "two", {"three": 3}]
            }
        }
        
        # Mock successful roundtrip
        mock_redis.get.return_value = json.dumps(complex_data, default=str)
        
        await manager._set_cached_result("complex:key", complex_data)
        result = await manager._get_cached_result("complex:key")
        
        assert result == complex_data


class TestCacheIntegrationWithServices:
    """Test cache integration with service methods"""

    @pytest.fixture
    def mock_manager_with_cache(self):
        """Create manager with mocked dependencies for cache testing"""
        with patch('app.services.ai_service_manager.get_redis') as mock_get_redis, \
             patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            
            mock_redis_client = AsyncMock()
            mock_get_redis.return_value = mock_redis_client
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_redis_client

    @pytest.mark.asyncio
    async def test_generate_text_cache_hit(self, mock_manager_with_cache):
        """Test text generation with cache hit"""
        manager, mock_openai, mock_redis = mock_manager_with_cache
        
        cached_response = "Cached text response"
        mock_redis.get.return_value = json.dumps(cached_response)
        
        result = await manager.generate_text("Test message", use_cache=True)
        
        assert result == cached_response
        mock_redis.get.assert_called_once()
        mock_openai.chat.completions.create.assert_not_called()

    @pytest.mark.asyncio
    async def test_generate_text_cache_miss_and_set(self, mock_manager_with_cache):
        """Test text generation with cache miss and subsequent cache set"""
        manager, mock_openai, mock_redis = mock_manager_with_cache
        
        # Mock cache miss
        mock_redis.get.return_value = None
        
        # Mock API response
        api_response = "Fresh API response"
        mock_openai.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content=api_response))]
        )
        
        result = await manager.generate_text("Test message", use_cache=True)
        
        assert result == api_response
        mock_redis.get.assert_called_once()
        mock_openai.chat.completions.create.assert_called_once()
        mock_redis.setex.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_embedding_cache_behavior(self, mock_manager_with_cache):
        """Test embedding generation cache behavior"""
        manager, mock_openai, mock_redis = mock_manager_with_cache
        
        # Test cache miss
        mock_redis.get.return_value = None
        
        test_embedding = [0.1, 0.2, 0.3] * 300
        mock_openai.embeddings.create.return_value = AsyncMock(
            data=[AsyncMock(embedding=test_embedding)]
        )
        
        result = await manager.generate_embedding("Test text", use_cache=True)
        
        assert result == test_embedding
        mock_redis.get.assert_called_once()
        mock_redis.setex.assert_called_once()
        
        # Verify cache key includes embedding-specific parameters
        cache_key = mock_redis.setex.call_args[0][0]
        assert cache_key.startswith("ai:embedding:")

    @pytest.mark.asyncio
    async def test_rerank_documents_cache_behavior(self, mock_manager_with_cache):
        """Test document reranking cache behavior"""
        manager, mock_openai, mock_redis = mock_manager_with_cache
        
        # Mock cache miss
        mock_redis.get.return_value = None
        
        # Mock embedding responses for similarity calculation
        query_embedding = [1.0, 0.0, 0.0]
        doc_embeddings = [[0.8, 0.6, 0.0], [0.2, 0.9, 0.0]]
        
        embedding_responses = [
            AsyncMock(data=[AsyncMock(embedding=query_embedding)]),
            AsyncMock(data=[AsyncMock(embedding=doc_embeddings[0])]),
            AsyncMock(data=[AsyncMock(embedding=doc_embeddings[1])])
        ]
        mock_openai.embeddings.create.side_effect = embedding_responses
        
        documents = ["Doc 1", "Doc 2"]
        result = await manager.rerank_documents("query", documents, use_cache=True)
        
        assert isinstance(result, list)
        assert len(result) == 2
        
        # Should cache the rerank result
        mock_redis.setex.assert_called()
        cache_key = mock_redis.setex.call_args[0][0]
        assert cache_key.startswith("ai:rerank:")

    @pytest.mark.asyncio
    async def test_cache_disabled_behavior(self, mock_manager_with_cache):
        """Test service behavior when caching is disabled"""
        manager, mock_openai, mock_redis = mock_manager_with_cache
        
        api_response = "Direct API response"
        mock_openai.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content=api_response))]
        )
        
        result = await manager.generate_text("Test message", use_cache=False)
        
        assert result == api_response
        mock_openai.chat.completions.create.assert_called_once()
        # Should not interact with cache
        mock_redis.get.assert_not_called()
        mock_redis.setex.assert_not_called()

    @pytest.mark.asyncio
    async def test_cache_parameter_sensitivity_in_services(self, mock_manager_with_cache):
        """Test that different service parameters generate different cache keys"""
        manager, mock_openai, mock_redis = mock_manager_with_cache
        
        mock_redis.get.return_value = None
        mock_openai.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="response"))]
        )
        
        # Make two requests with different parameters
        await manager.generate_text("Test message", temperature=0.7, use_cache=True)
        await manager.generate_text("Test message", temperature=0.3, use_cache=True)
        
        # Should generate different cache keys
        assert mock_redis.setex.call_count == 2
        
        cache_key1 = mock_redis.setex.call_args_list[0][0][0]
        cache_key2 = mock_redis.setex.call_args_list[1][0][0]
        
        assert cache_key1 != cache_key2


class TestCacheTTLManagement:
    """Test TTL (Time To Live) management for cache entries"""

    @pytest.fixture
    def mock_manager(self):
        """Create manager with mocked Redis"""
        with patch('app.services.ai_service_manager.get_redis') as mock_get_redis:
            mock_redis_client = AsyncMock()
            mock_get_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_redis_client

    @pytest.mark.asyncio
    async def test_embedding_cache_ttl(self, mock_manager):
        """Test embedding cache uses configured TTL"""
        manager, mock_redis = mock_manager
        
        mock_redis.get.return_value = None
        
        await manager._set_cached_result("embed:key", [0.1, 0.2, 0.3])
        
        mock_redis.setex.assert_called_once()
        call_args = mock_redis.setex.call_args[0]
        ttl_used = call_args[1]
        
        assert ttl_used == ai_settings.EMBEDDING_CACHE_TTL

    @pytest.mark.asyncio
    async def test_llm_cache_ttl(self, mock_manager):
        """Test LLM cache uses shorter TTL"""
        manager, mock_redis = mock_manager
        
        await manager._set_cached_result("llm:key", "response", ttl=1800)
        
        mock_redis.setex.assert_called_once()
        call_args = mock_redis.setex.call_args[0]
        ttl_used = call_args[1]
        
        assert ttl_used == 1800  # 30 minutes for LLM

    @pytest.mark.asyncio
    async def test_rerank_cache_ttl(self, mock_manager):
        """Test rerank cache uses appropriate TTL"""
        manager, mock_redis = mock_manager
        
        rerank_result = [{"document": "test", "score": 0.9, "index": 0}]
        await manager._set_cached_result("rerank:key", rerank_result, ttl=1800)
        
        mock_redis.setex.assert_called_once()
        call_args = mock_redis.setex.call_args[0]
        ttl_used = call_args[1]
        
        assert ttl_used == 1800  # 30 minutes for rerank

    @pytest.mark.asyncio
    async def test_custom_ttl_override(self, mock_manager):
        """Test that custom TTL overrides default"""
        manager, mock_redis = mock_manager
        
        custom_ttl = 7200  # 2 hours
        await manager._set_cached_result("custom:key", "data", ttl=custom_ttl)
        
        mock_redis.setex.assert_called_once()
        call_args = mock_redis.setex.call_args[0]
        ttl_used = call_args[1]
        
        assert ttl_used == custom_ttl


class TestCacheErrorResilience:
    """Test cache error handling and system resilience"""

    @pytest.fixture
    def mock_manager_with_failing_cache(self):
        """Create manager with failing Redis for testing resilience"""
        with patch('app.services.ai_service_manager.get_redis') as mock_get_redis, \
             patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            
            mock_redis_client = AsyncMock()
            mock_get_redis.return_value = mock_redis_client
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_redis_client

    @pytest.mark.asyncio
    async def test_service_continues_on_cache_get_failure(self, mock_manager_with_failing_cache):
        """Test that service continues working when cache get fails"""
        manager, mock_openai, mock_redis = mock_manager_with_failing_cache
        
        # Mock cache get failure
        mock_redis.get.side_effect = Exception("Redis connection failed")
        
        # Mock successful API response
        api_response = "API response despite cache failure"
        mock_openai.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content=api_response))]
        )
        
        result = await manager.generate_text("Test message", use_cache=True)
        
        assert result == api_response
        mock_openai.chat.completions.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_service_continues_on_cache_set_failure(self, mock_manager_with_failing_cache):
        """Test that service continues working when cache set fails"""
        manager, mock_openai, mock_redis = mock_manager_with_failing_cache
        
        # Mock cache operations
        mock_redis.get.return_value = None  # Cache miss
        mock_redis.setex.side_effect = Exception("Redis write failed")
        
        # Mock successful API response
        api_response = "API response with cache set failure"
        mock_openai.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content=api_response))]
        )
        
        result = await manager.generate_text("Test message", use_cache=True)
        
        assert result == api_response
        # Should have attempted cache operations
        mock_redis.get.assert_called_once()
        mock_redis.setex.assert_called_once()

    @pytest.mark.asyncio
    async def test_corrupt_cache_data_handling(self, mock_manager_with_failing_cache):
        """Test handling of corrupt cache data"""
        manager, mock_openai, mock_redis = mock_manager_with_failing_cache
        
        # Mock corrupted cache data
        mock_redis.get.return_value = "invalid json data"
        
        # Mock successful API response
        api_response = "Fresh API response"
        mock_openai.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content=api_response))]
        )
        
        result = await manager.generate_text("Test message", use_cache=True)
        
        # Should fall back to API call
        assert result == api_response
        mock_openai.chat.completions.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_intermittent_cache_failures(self, mock_manager_with_failing_cache):
        """Test handling of intermittent cache failures"""
        manager, mock_openai, mock_redis = mock_manager_with_failing_cache
        
        # Mock intermittent failures
        call_count = 0
        def intermittent_get(key):
            nonlocal call_count
            call_count += 1
            if call_count % 2 == 0:  # Fail every second call
                raise Exception("Intermittent Redis failure")
            return None  # Cache miss
        
        mock_redis.get.side_effect = intermittent_get
        
        mock_openai.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="API response"))]
        )
        
        # Should handle multiple requests despite intermittent failures
        results = []
        for i in range(4):
            result = await manager.generate_text(f"Message {i}", use_cache=True)
            results.append(result)
        
        assert len(results) == 4
        assert all(result == "API response" for result in results)
        # Should have attempted cache operations for all requests
        assert call_count == 4