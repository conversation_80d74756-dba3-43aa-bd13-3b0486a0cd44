"""
Unit Tests for AI Service Manager Error Handling and Fallback
Tests comprehensive error scenarios, fallback chains, retry logic, and resilience
"""
import pytest
import pytest_asyncio
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from typing import List, Dict, Any
import httpx

from app.services.ai_service_manager import (
    AIServiceManager,
    AIServiceType,
    AIProvider,
    AIServiceError,
    ProviderError,
    AllProvidersFailedError,
    ai_service_manager
)
from app.core.ai_config import ai_settings


class TestCustomExceptions:
    """Test custom exception classes"""

    def test_ai_service_error_creation(self):
        """Test AIServiceError creation and attributes"""
        error = AIServiceError("Test error message")
        
        assert str(error) == "AI_SERVICE: Test error message"
        assert error.service_name == "AI_SERVICE"
        assert error.original_error is None

    def test_ai_service_error_with_original(self):
        """Test AIServiceError with original exception"""
        original = ValueError("Original error")
        error = AIServiceError("Wrapper error", original)
        
        assert error.original_error is original
        assert str(error) == "AI_SERVICE: Wrapper error"

    def test_provider_error_creation(self):
        """Test ProviderError creation and attributes"""
        error = ProviderError("test_provider", "Provider failed")
        
        assert error.provider == "test_provider"
        assert "Provider test_provider: Provider failed" in str(error)
        assert isinstance(error, AIServiceError)

    def test_all_providers_failed_error_creation(self):
        """Test AllProvidersFailedError creation and attributes"""
        attempted = ["provider1", "provider2", "provider3"]
        last_error = Exception("Final failure")
        
        error = AllProvidersFailedError(attempted, last_error)
        
        assert error.attempted_providers == attempted
        assert error.last_error is last_error
        assert "All providers failed: ['provider1', 'provider2', 'provider3']" in str(error)

    def test_exception_inheritance_hierarchy(self):
        """Test exception inheritance hierarchy"""
        provider_error = ProviderError("test", "message")
        all_failed_error = AllProvidersFailedError(["test"], Exception())
        
        assert isinstance(provider_error, AIServiceError)
        assert isinstance(all_failed_error, AIServiceError)
        
        # Should be catchable as base exception
        try:
            raise provider_error
        except AIServiceError:
            pass  # Should catch
        
        try:
            raise all_failed_error
        except AIServiceError:
            pass  # Should catch


class TestProviderFallbackLogic:
    """Test provider fallback chain logic"""

    @pytest.fixture
    def mock_manager_with_fallback(self):
        """Create manager with mocked fallback configuration"""
        with patch('app.services.ai_service_manager.ai_settings') as mock_settings, \
             patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis:
            
            # Mock fallback chains
            mock_settings.get_llm_fallback_chain.return_value = ["zhipu", "moonshot", "deepseek"]
            mock_settings.get_embedding_fallback_chain.return_value = ["zhipu", "moonshot"]
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_settings

    @pytest.mark.asyncio
    async def test_single_provider_failure_fallback(self, mock_manager_with_fallback):
        """Test fallback when primary provider fails"""
        manager, mock_client, mock_settings = mock_manager_with_fallback
        
        # Mock primary failure, fallback success
        mock_client.chat.completions.create.side_effect = [
            Exception("Primary provider failed"),
            AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Fallback success"))])
        ]
        
        result = await manager.generate_text("Test message", use_cache=False)
        
        assert result == "Fallback success"
        assert mock_client.chat.completions.create.call_count == 2

    @pytest.mark.asyncio
    async def test_multiple_provider_failures(self, mock_manager_with_fallback):
        """Test fallback through multiple provider failures"""
        manager, mock_client, mock_settings = mock_manager_with_fallback
        
        # Mock multiple failures, then success
        mock_client.chat.completions.create.side_effect = [
            Exception("Provider 1 failed"),
            Exception("Provider 2 failed"),
            AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Third provider success"))])
        ]
        
        result = await manager.generate_text("Test message", use_cache=False)
        
        assert result == "Third provider success"
        assert mock_client.chat.completions.create.call_count == 3

    @pytest.mark.asyncio
    async def test_all_providers_in_chain_fail(self, mock_manager_with_fallback):
        """Test AllProvidersFailedError when entire chain fails"""
        manager, mock_client, mock_settings = mock_manager_with_fallback
        
        # Mock all providers fail
        mock_client.chat.completions.create.side_effect = Exception("All providers failed")
        
        with pytest.raises(AllProvidersFailedError) as exc_info:
            await manager.generate_text("Test message", use_cache=False)
        
        error = exc_info.value
        assert len(error.attempted_providers) >= 1
        assert isinstance(error.last_error, Exception)

    @pytest.mark.asyncio
    async def test_fallback_with_specific_provider_request(self, mock_manager_with_fallback):
        """Test behavior when specific provider is requested but fails"""
        manager, mock_client, mock_settings = mock_manager_with_fallback
        
        # Mock specific provider failure
        mock_client.chat.completions.create.side_effect = Exception("Specific provider failed")
        
        # When specific provider is requested, should still try fallbacks
        with pytest.raises(AllProvidersFailedError):
            await manager.generate_text(
                "Test message", 
                provider="zhipu",  # Specific provider
                use_cache=False
            )

    @pytest.mark.asyncio
    async def test_embedding_fallback_chain(self, mock_manager_with_fallback):
        """Test fallback chain for embedding generation"""
        manager, mock_client, mock_settings = mock_manager_with_fallback
        
        # Mock embedding failures then success
        mock_client.embeddings.create.side_effect = [
            Exception("Embedding provider 1 failed"),
            AsyncMock(data=[AsyncMock(embedding=[0.1, 0.2, 0.3])])
        ]
        
        result = await manager.generate_embedding("Test text", use_cache=False)
        
        assert result == [0.1, 0.2, 0.3]
        assert mock_client.embeddings.create.call_count == 2

    @pytest.mark.asyncio
    async def test_invalid_provider_in_chain(self, mock_manager_with_fallback):
        """Test handling of invalid provider in fallback chain"""
        manager, mock_client, mock_settings = mock_manager_with_fallback
        
        # Mock fallback chain with invalid provider
        mock_settings.get_llm_fallback_chain.return_value = ["zhipu", "invalid_provider", "moonshot"]
        
        # Mock first provider fail, invalid provider should be skipped, third succeeds
        mock_client.chat.completions.create.side_effect = [
            Exception("First provider failed"),
            AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Third provider success"))])
        ]
        
        result = await manager.generate_text("Test message", use_cache=False)
        
        assert result == "Third provider success"
        # Should skip invalid provider and go to next valid one
        assert mock_client.chat.completions.create.call_count == 2


class TestRetryMechanism:
    """Test retry logic with exponential backoff"""

    @pytest.fixture
    def mock_manager_with_retry(self):
        """Create manager for retry testing"""
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis:
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client

    @pytest.mark.asyncio
    async def test_retry_on_transient_failure(self, mock_manager_with_retry):
        """Test retry mechanism for transient failures"""
        manager, mock_client = mock_manager_with_retry
        
        # Mock intermittent failures then success
        call_count = 0
        def mock_create(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count < 3:  # Fail first 2 attempts
                raise httpx.RequestError("Transient network error")
            return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Retry success"))])
        
        mock_client.chat.completions.create.side_effect = mock_create
        
        result = await manager.generate_text("Test message", max_retries=3, use_cache=False)
        
        assert result == "Retry success"
        assert call_count == 3

    @pytest.mark.asyncio
    async def test_retry_exhausted(self, mock_manager_with_retry):
        """Test behavior when retry attempts are exhausted"""
        manager, mock_client = mock_manager_with_retry
        
        # Mock persistent failure
        mock_client.chat.completions.create.side_effect = httpx.RequestError("Persistent error")
        
        with pytest.raises(Exception) as exc_info:
            await manager.generate_text("Test message", max_retries=2, use_cache=False)
        
        assert "Persistent error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_retry_with_different_error_types(self, mock_manager_with_retry):
        """Test retry behavior with different error types"""
        manager, mock_client = mock_manager_with_retry
        
        # Mock different types of errors
        errors = [
            httpx.RequestError("Network error"),  # Should retry
            asyncio.TimeoutError("Timeout"),      # Should retry
            ValueError("Invalid request")         # Should not retry (non-retryable)
        ]
        
        mock_client.chat.completions.create.side_effect = errors
        
        with pytest.raises(ValueError):  # Should fail on non-retryable error
            await manager.generate_text("Test message", max_retries=3, use_cache=False)
        
        # Should have attempted only once (ValueError is not retryable)
        assert mock_client.chat.completions.create.call_count == 1

    @pytest.mark.asyncio
    async def test_retry_with_embeddings(self, mock_manager_with_retry):
        """Test retry mechanism for embedding generation"""
        manager, mock_client = mock_manager_with_retry
        
        call_count = 0
        def mock_embed(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count < 2:
                raise httpx.RequestError("Embedding network error")
            return AsyncMock(data=[AsyncMock(embedding=[0.1, 0.2, 0.3])])
        
        mock_client.embeddings.create.side_effect = mock_embed
        
        result = await manager.generate_embedding("Test text", max_retries=3, use_cache=False)
        
        assert result == [0.1, 0.2, 0.3]
        assert call_count == 2

    @pytest.mark.asyncio
    async def test_exponential_backoff_timing(self, mock_manager_with_retry):
        """Test that retry uses exponential backoff (timing test)"""
        manager, mock_client = mock_manager_with_retry
        
        import time
        
        call_times = []
        def mock_create(*args, **kwargs):
            call_times.append(time.time())
            raise httpx.RequestError("Always fail")
        
        mock_client.chat.completions.create.side_effect = mock_create
        
        start_time = time.time()
        
        with pytest.raises(Exception):
            await manager.generate_text("Test message", max_retries=3, use_cache=False)
        
        total_time = time.time() - start_time
        
        # Should have taken some time due to backoff (at least 1+2=3 seconds minimum)
        # But we'll be conservative due to test environment variations
        assert total_time > 0.5  # At least some delay
        assert len(call_times) == 3  # Should have made 3 attempts


class TestProviderSpecificErrors:
    """Test handling of provider-specific error scenarios"""

    @pytest.fixture
    def mock_manager_providers(self):
        """Create manager for provider-specific testing"""
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.ollama') as mock_ollama, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis:
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_ollama

    @pytest.mark.asyncio
    async def test_invalid_provider_name_error(self, mock_manager_providers):
        """Test error when invalid provider name is used"""
        manager, _, _ = mock_manager_providers
        
        with pytest.raises(ProviderError) as exc_info:
            await manager._execute_provider_operation(
                AIServiceType.LLM,
                "nonexistent_provider",
                manager._execute_llm_generation,
                []
            )
        
        error = exc_info.value
        assert "Invalid provider name" in str(error)

    @pytest.mark.asyncio
    async def test_unconfigured_provider_error(self, mock_manager_providers):
        """Test error when provider is not configured"""
        manager, _, _ = mock_manager_providers
        
        # Try to use a provider that exists but isn't configured
        with pytest.raises(ProviderError) as exc_info:
            await manager._execute_provider_operation(
                AIServiceType.LLM,
                "zhipu",  # Exists but may not be configured in test
                manager._execute_llm_generation,
                []
            )
        
        error = exc_info.value
        assert "not configured" in str(error) or "not available" in str(error)

    @pytest.mark.asyncio
    async def test_openai_api_key_error(self, mock_manager_providers):
        """Test handling of API key authentication errors"""
        manager, mock_client, _ = mock_manager_providers
        
        # Mock API key error
        from openai import AuthenticationError
        mock_client.chat.completions.create.side_effect = AuthenticationError("Invalid API key")
        
        with pytest.raises(Exception) as exc_info:
            await manager.generate_text("Test message", use_cache=False)
        
        # Should propagate authentication error
        assert "Invalid API key" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_rate_limit_error(self, mock_manager_providers):
        """Test handling of rate limit errors"""
        manager, mock_client, _ = mock_manager_providers
        
        # Mock rate limit error
        from openai import RateLimitError
        mock_client.chat.completions.create.side_effect = RateLimitError("Rate limit exceeded")
        
        with pytest.raises(Exception) as exc_info:
            await manager.generate_text("Test message", use_cache=False)
        
        # Should handle rate limit error appropriately
        assert "Rate limit" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_ollama_connection_error(self, mock_manager_providers):
        """Test handling of Ollama connection errors"""
        manager, _, mock_ollama = mock_manager_providers
        
        # Mock Ollama connection error
        mock_ollama.AsyncClient.side_effect = Exception("Ollama server unreachable")
        
        with pytest.raises(Exception):
            await manager._execute_llm_generation(
                mock_ollama,
                {"host": "http://localhost:11434"},
                "ollama",
                [{"role": "user", "content": "test"}]
            )

    @pytest.mark.asyncio
    async def test_provider_timeout_error(self, mock_manager_providers):
        """Test handling of provider timeout errors"""
        manager, mock_client, _ = mock_manager_providers
        
        # Mock timeout error
        mock_client.chat.completions.create.side_effect = asyncio.TimeoutError("Request timeout")
        
        with pytest.raises(Exception) as exc_info:
            await manager.generate_text("Test message", use_cache=False, max_retries=1)
        
        # Should handle timeout appropriately (through retry mechanism)
        assert "timeout" in str(exc_info.value).lower()


class TestErrorRecoveryScenarios:
    """Test complex error recovery scenarios"""

    @pytest.fixture
    def mock_manager_recovery(self):
        """Create manager for error recovery testing"""
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.ai_settings') as mock_settings, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis:
            
            mock_settings.get_llm_fallback_chain.return_value = ["zhipu", "moonshot", "deepseek"]
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_redis_client

    @pytest.mark.asyncio
    async def test_partial_provider_recovery(self, mock_manager_recovery):
        """Test recovery when some providers fail intermittently"""
        manager, mock_client, mock_redis = mock_manager_recovery
        
        # Mock intermittent failures for first provider, success for second
        provider_1_calls = 0
        def mock_provider_1(*args, **kwargs):
            nonlocal provider_1_calls
            provider_1_calls += 1
            if provider_1_calls <= 2:  # Fail first 2 calls
                raise Exception("Provider 1 intermittent failure")
            return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Provider 1 recovered"))])
        
        def mock_provider_2(*args, **kwargs):
            return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Provider 2 success"))])
        
        # Mock calls to different providers
        call_count = 0
        def mock_create(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count == 1:  # First attempt - provider 1 fails
                return mock_provider_1(*args, **kwargs)
            else:  # Fallback - provider 2 succeeds
                return mock_provider_2(*args, **kwargs)
        
        mock_client.chat.completions.create.side_effect = mock_create
        
        # First call should use fallback
        result1 = await manager.generate_text("Test 1", use_cache=False)
        assert result1 == "Provider 2 success"
        
        # Second call should still use fallback (provider 1 still failing)
        result2 = await manager.generate_text("Test 2", use_cache=False)
        assert result2 == "Provider 2 success"

    @pytest.mark.asyncio
    async def test_cascading_failure_recovery(self, mock_manager_recovery):
        """Test recovery from cascading failures"""
        manager, mock_client, mock_redis = mock_manager_recovery
        
        # Mock cascading failures: first provider always fails, 
        # second provider fails then recovers, third always works
        failure_counts = {"provider1": 0, "provider2": 0, "provider3": 0}
        
        def mock_cascading_create(*args, **kwargs):
            # Simulate different providers based on call pattern
            failure_counts["provider1"] += 1
            if failure_counts["provider1"] <= 1:  # First provider always fails
                raise Exception("Provider 1 permanent failure")
            
            failure_counts["provider2"] += 1
            if failure_counts["provider2"] <= 2:  # Second provider fails twice
                raise Exception("Provider 2 temporary failure")
            
            failure_counts["provider3"] += 1
            return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Provider 3 success"))])
        
        mock_client.chat.completions.create.side_effect = mock_cascading_create
        
        result = await manager.generate_text("Test message", use_cache=False)
        assert result == "Provider 3 success"

    @pytest.mark.asyncio
    async def test_error_recovery_with_caching(self, mock_manager_recovery):
        """Test error recovery interaction with caching"""
        manager, mock_client, mock_redis = mock_manager_recovery
        
        # Mock cache failure, then API success
        mock_redis.get.side_effect = Exception("Cache failure")
        mock_redis.setex.side_effect = Exception("Cache write failure")
        
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="API success despite cache failure"))]
        )
        
        result = await manager.generate_text("Test message", use_cache=True)
        
        assert result == "API success despite cache failure"
        # Should have attempted cache operations
        mock_redis.get.assert_called_once()

    @pytest.mark.asyncio
    async def test_concurrent_error_recovery(self, mock_manager_recovery):
        """Test error recovery under concurrent load"""
        manager, mock_client, mock_redis = mock_manager_recovery
        
        # Mock failures for some concurrent requests
        call_count = 0
        def mock_concurrent_create(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count % 3 == 0:  # Every third call fails
                raise Exception("Intermittent failure")
            return AsyncMock(choices=[AsyncMock(message=AsyncMock(content=f"Success {call_count}"))])
        
        mock_client.chat.completions.create.side_effect = mock_concurrent_create
        
        # Make many concurrent requests
        tasks = [
            manager.generate_text(f"Message {i}", use_cache=False)
            for i in range(10)
        ]
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Most should succeed, some might fail
        successful = [r for r in results if isinstance(r, str)]
        failed = [r for r in results if isinstance(r, Exception)]
        
        # Should handle concurrent errors gracefully
        assert len(successful) >= len(results) * 0.6  # At least 60% success


class TestErrorLoggingAndMonitoring:
    """Test error logging and monitoring functionality"""

    @pytest.fixture
    def mock_manager_logging(self):
        """Create manager for logging testing"""
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.logger') as mock_logger, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis:
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_logger

    @pytest.mark.asyncio
    async def test_error_logging_on_failure(self, mock_manager_logging):
        """Test that errors are properly logged"""
        manager, mock_client, mock_logger = mock_manager_logging
        
        # Mock API failure
        test_error = Exception("API failure for logging test")
        mock_client.chat.completions.create.side_effect = test_error
        
        with patch.object(manager, '_log_operation_metrics') as mock_log_metrics:
            with pytest.raises(Exception):
                await manager.generate_text("Test message", use_cache=False, max_retries=1)
            
            # Should log the failure
            mock_log_metrics.assert_called()
            call_args = mock_log_metrics.call_args[0]
            assert call_args[0] == "generate_text"  # operation
            assert call_args[2] is False  # success = False
            assert call_args[4] is not None  # error object

    @pytest.mark.asyncio
    async def test_success_logging(self, mock_manager_logging):
        """Test that successful operations are logged"""
        manager, mock_client, mock_logger = mock_manager_logging
        
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="Success"))]
        )
        
        with patch.object(manager, '_log_operation_metrics') as mock_log_metrics:
            await manager.generate_text("Test message", use_cache=False)
            
            # Should log the success
            mock_log_metrics.assert_called()
            call_args = mock_log_metrics.call_args[0]
            assert call_args[0] == "generate_text"  # operation
            assert call_args[2] is True  # success = True

    @pytest.mark.asyncio
    async def test_performance_metrics_logging(self, mock_manager_logging):
        """Test that performance metrics are logged"""
        manager, mock_client, mock_logger = mock_manager_logging
        
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="Success"))]
        )
        
        with patch.object(manager, '_log_operation_metrics') as mock_log_metrics:
            await manager.generate_text("Test message", use_cache=False)
            
            call_args = mock_log_metrics.call_args[0]
            duration_ms = call_args[3]
            
            # Duration should be a reasonable number
            assert isinstance(duration_ms, (int, float))
            assert duration_ms >= 0
            assert duration_ms < 60000  # Should be less than 1 minute