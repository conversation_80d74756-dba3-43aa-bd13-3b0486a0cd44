"""
Comprehensive Unit Tests for Enhanced AIServiceManager
Tests all core service methods, provider management, error handling, caching, and health monitoring
"""
import pytest
import pytest_asyncio
import asyncio
import json
import time
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from typing import Dict, Any, List, Optional
import redis.asyncio as redis

from app.services.ai_service_manager import (
    AIServiceManager, 
    AIServiceType, 
    AIProvider,
    AIServiceError,
    ProviderError,
    AllProvidersFailedError,
    ai_service_manager
)
from app.core.ai_config import ai_settings


class TestAIServiceManagerCore:
    """Test core AIServiceManager functionality"""

    def test_singleton_pattern(self):
        """Test that AIServiceManager follows singleton pattern correctly"""
        # Get the global instance
        manager1 = ai_service_manager
        
        # Create new instance should return same object
        manager2 = AIServiceManager()
        
        assert manager1 is manager2
        assert isinstance(manager1, AIServiceManager)

    def test_service_type_enum(self):
        """Test AIServiceType enum values"""
        assert AIServiceType.LLM.value == "llm"
        assert AIServiceType.EMBEDDING.value == "embedding"
        assert AIServiceType.RERANK.value == "rerank"

    def test_provider_enum(self):
        """Test AIProvider enum values"""
        expected_providers = {
            "deepseek", "zhipu", "moonshot", "openrouter", "qwen", "ollama"
        }
        actual_providers = {provider.value for provider in AIProvider}
        assert actual_providers == expected_providers

    def test_custom_exceptions(self):
        """Test custom exception hierarchy"""
        # Test AIServiceError
        base_error = AIServiceError("test error")
        assert str(base_error) == "AI_SERVICE: test error"
        assert base_error.service_name == "AI_SERVICE"

        # Test ProviderError
        provider_error = ProviderError("test_provider", "provider failed")
        assert "Provider test_provider: provider failed" in str(provider_error)
        assert provider_error.provider == "test_provider"

        # Test AllProvidersFailedError
        attempted = ["provider1", "provider2"]
        last_error = Exception("last error")
        all_failed = AllProvidersFailedError(attempted, last_error)
        assert all_failed.attempted_providers == attempted
        assert all_failed.last_error is last_error

    @pytest.fixture
    def mock_manager(self):
        """Create a test AIServiceManager with mocked dependencies"""
        with patch('app.services.ai_service_manager.get_redis') as mock_redis, \
             patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.ollama') as mock_ollama:
            
            # Mock Redis
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            # Mock OpenAI clients
            mock_openai_instance = AsyncMock()
            mock_openai.return_value = mock_openai_instance
            
            # Create fresh instance for testing
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            
            manager = AIServiceManager()
            manager._redis_client = mock_redis_client
            
            yield manager


class TestProviderProperties:
    """Test provider property management and thread safety"""

    @pytest.fixture
    def manager(self):
        return ai_service_manager

    def test_llm_provider_property(self, manager):
        """Test LLM provider getter and setter"""
        original_provider = manager.llm_provider
        
        # Test getter
        assert isinstance(manager.llm_provider, str)
        
        # Test valid setter
        manager.llm_provider = "zhipu"
        assert manager.llm_provider == "zhipu"
        
        # Test invalid setter (should log warning but not crash)
        manager.llm_provider = "invalid_provider"
        assert manager.llm_provider == "zhipu"  # Should remain unchanged
        
        # Restore original
        manager.llm_provider = original_provider

    def test_embedding_provider_property(self, manager):
        """Test embedding provider getter and setter"""
        original_provider = manager.embedding_provider
        
        # Test getter
        assert isinstance(manager.embedding_provider, str)
        
        # Test valid setter
        manager.embedding_provider = "moonshot"
        assert manager.embedding_provider == "moonshot"
        
        # Test invalid setter
        manager.embedding_provider = "invalid_provider"
        assert manager.embedding_provider == "moonshot"  # Should remain unchanged
        
        # Restore original
        manager.embedding_provider = original_provider

    def test_rerank_provider_property(self, manager):
        """Test rerank provider getter and setter"""
        original_provider = manager.rerank_provider
        
        # Test getter
        assert isinstance(manager.rerank_provider, str)
        
        # Test valid setter
        manager.rerank_provider = "openrouter"
        assert manager.rerank_provider == "openrouter"
        
        # Test 'none' as valid value
        manager.rerank_provider = "none"
        assert manager.rerank_provider == "none"
        
        # Restore original
        manager.rerank_provider = original_provider

    def test_concurrent_provider_switching(self, manager):
        """Test thread-safe provider switching"""
        async def switch_provider(provider_type, value):
            if provider_type == "llm":
                manager.llm_provider = value
            elif provider_type == "embedding":
                manager.embedding_provider = value
            elif provider_type == "rerank":
                manager.rerank_provider = value

        async def test_concurrent():
            tasks = [
                switch_provider("llm", "zhipu"),
                switch_provider("embedding", "moonshot"),
                switch_provider("rerank", "openrouter"),
            ]
            await asyncio.gather(*tasks)
            
            # Properties should be set correctly
            assert manager.llm_provider in ["zhipu", ai_settings.LLM_PROVIDER]
            assert manager.embedding_provider in ["moonshot", ai_settings.EMBEDDING_PROVIDER]
            assert manager.rerank_provider in ["openrouter", ai_settings.RERANK_PROVIDER]

        asyncio.run(test_concurrent())


class TestCoreServiceMethods:
    """Test the three core service methods with comprehensive scenarios"""

    @pytest.fixture
    def mock_manager(self):
        """Create manager with mocked clients"""
        with patch('app.services.ai_service_manager.get_redis') as mock_redis, \
             patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_redis_client

    @pytest.mark.asyncio
    async def test_generate_text_success(self, mock_manager):
        """Test successful text generation"""
        manager, mock_client, mock_redis = mock_manager
        
        # Mock OpenAI response
        mock_response = AsyncMock()
        mock_response.choices = [
            AsyncMock(message=AsyncMock(content="Generated text response"))
        ]
        mock_client.chat.completions.create.return_value = mock_response
        
        # Test string input
        result = await manager.generate_text(
            "Hello, world!",
            temperature=0.7,
            max_tokens=100
        )
        
        assert result == "Generated text response"
        mock_client.chat.completions.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_text_with_messages(self, mock_manager):
        """Test text generation with message format"""
        manager, mock_client, mock_redis = mock_manager
        
        mock_response = AsyncMock()
        mock_response.choices = [
            AsyncMock(message=AsyncMock(content="Message response"))
        ]
        mock_client.chat.completions.create.return_value = mock_response
        
        messages = [
            {"role": "system", "content": "You are helpful"},
            {"role": "user", "content": "Hello"}
        ]
        
        result = await manager.generate_text(messages)
        assert result == "Message response"

    @pytest.mark.asyncio
    async def test_generate_text_caching(self, mock_manager):
        """Test text generation caching mechanism"""
        manager, mock_client, mock_redis = mock_manager
        
        # Mock Redis cache hit
        cached_result = "Cached response"
        mock_redis.get.return_value = json.dumps(cached_result)
        
        result = await manager.generate_text("Test message", use_cache=True)
        
        assert result == cached_result
        # Should not call OpenAI client due to cache hit
        mock_client.chat.completions.create.assert_not_called()

    @pytest.mark.asyncio
    async def test_generate_text_cache_miss(self, mock_manager):
        """Test text generation when cache misses"""
        manager, mock_client, mock_redis = mock_manager
        
        # Mock cache miss
        mock_redis.get.return_value = None
        
        # Mock successful response
        mock_response = AsyncMock()
        mock_response.choices = [
            AsyncMock(message=AsyncMock(content="Fresh response"))
        ]
        mock_client.chat.completions.create.return_value = mock_response
        
        result = await manager.generate_text("Test message", use_cache=True)
        
        assert result == "Fresh response"
        # Should cache the result
        mock_redis.setex.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_embedding_success(self, mock_manager):
        """Test successful embedding generation"""
        manager, mock_client, mock_redis = mock_manager
        
        # Mock embedding response
        mock_embedding = [0.1, 0.2, 0.3] * 300  # Mock 900-dim embedding
        mock_response = AsyncMock()
        mock_response.data = [AsyncMock(embedding=mock_embedding)]
        mock_client.embeddings.create.return_value = mock_response
        
        result = await manager.generate_embedding("Test text")
        
        assert result == mock_embedding
        mock_client.embeddings.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_embedding_caching(self, mock_manager):
        """Test embedding caching"""
        manager, mock_client, mock_redis = mock_manager
        
        # Mock cached embedding
        cached_embedding = [0.5, 0.6, 0.7] * 300
        mock_redis.get.return_value = json.dumps(cached_embedding)
        
        result = await manager.generate_embedding("Test text", use_cache=True)
        
        assert result == cached_embedding
        mock_client.embeddings.create.assert_not_called()

    @pytest.mark.asyncio
    async def test_rerank_documents_empty_input(self, mock_manager):
        """Test rerank with empty document list"""
        manager, _, _ = mock_manager
        
        result = await manager.rerank_documents("query", [])
        
        assert result == []

    @pytest.mark.asyncio 
    async def test_rerank_documents_fallback_to_similarity(self, mock_manager):
        """Test rerank fallback to embedding similarity"""
        manager, mock_client, mock_redis = mock_manager
        
        # Mock embedding responses
        query_embedding = [1.0, 0.0, 0.0]
        doc1_embedding = [0.8, 0.6, 0.0]  # High similarity
        doc2_embedding = [0.0, 0.0, 1.0]  # Low similarity
        
        mock_response1 = AsyncMock()
        mock_response1.data = [AsyncMock(embedding=query_embedding)]
        mock_response2 = AsyncMock()
        mock_response2.data = [AsyncMock(embedding=doc1_embedding)]
        mock_response3 = AsyncMock()
        mock_response3.data = [AsyncMock(embedding=doc2_embedding)]
        
        # Return different embeddings for each call
        mock_client.embeddings.create.side_effect = [
            mock_response1, mock_response2, mock_response3
        ]
        
        documents = ["Document 1", "Document 2"]
        result = await manager.rerank_documents("query", documents, top_k=2)
        
        assert len(result) == 2
        assert all("document" in item for item in result)
        assert all("score" in item for item in result)
        assert all("index" in item for item in result)
        
        # First document should have higher score (higher similarity)
        assert result[0]["score"] > result[1]["score"]

    @pytest.mark.asyncio
    async def test_service_methods_with_specific_providers(self, mock_manager):
        """Test service methods with specific provider selection"""
        manager, mock_client, mock_redis = mock_manager
        
        # Test with specific provider
        mock_response = AsyncMock()
        mock_response.choices = [AsyncMock(message=AsyncMock(content="Specific provider response"))]
        mock_client.chat.completions.create.return_value = mock_response
        
        result = await manager.generate_text(
            "Test message", 
            provider="zhipu",
            use_cache=False
        )
        
        assert result == "Specific provider response"

    @pytest.mark.asyncio
    async def test_service_methods_disable_caching(self, mock_manager):
        """Test service methods with caching disabled"""
        manager, mock_client, mock_redis = mock_manager
        
        mock_response = AsyncMock()
        mock_response.choices = [AsyncMock(message=AsyncMock(content="No cache response"))]
        mock_client.chat.completions.create.return_value = mock_response
        
        result = await manager.generate_text("Test message", use_cache=False)
        
        assert result == "No cache response"
        # Should not interact with Redis
        mock_redis.get.assert_not_called()
        mock_redis.setex.assert_not_called()


class TestErrorHandlingAndFallback:
    """Test comprehensive error handling and fallback mechanisms"""

    @pytest.fixture
    def mock_manager_with_fallback(self):
        """Create manager with fallback chain mocked"""
        with patch('app.services.ai_service_manager.ai_settings') as mock_settings, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis, \
             patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
            
            # Mock settings
            mock_settings.get_llm_fallback_chain.return_value = ["zhipu", "moonshot", "deepseek"]
            mock_settings.get_embedding_fallback_chain.return_value = ["zhipu", "moonshot"]
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_settings

    @pytest.mark.asyncio
    async def test_provider_fallback_chain_success(self, mock_manager_with_fallback):
        """Test successful fallback to secondary provider"""
        manager, mock_client, mock_settings = mock_manager_with_fallback
        
        # Mock first provider fails, second succeeds
        mock_client.chat.completions.create.side_effect = [
            Exception("Primary provider failed"),
            AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Fallback success"))])
        ]
        
        result = await manager.generate_text("Test message", use_cache=False)
        
        assert result == "Fallback success"
        assert mock_client.chat.completions.create.call_count == 2

    @pytest.mark.asyncio
    async def test_all_providers_fail(self, mock_manager_with_fallback):
        """Test AllProvidersFailedError when all providers fail"""
        manager, mock_client, mock_settings = mock_manager_with_fallback
        
        # Mock all providers fail
        mock_client.chat.completions.create.side_effect = Exception("All providers failed")
        
        with pytest.raises(AllProvidersFailedError) as exc_info:
            await manager.generate_text("Test message", use_cache=False)
        
        assert len(exc_info.value.attempted_providers) > 0
        assert isinstance(exc_info.value.last_error, Exception)

    @pytest.mark.asyncio
    async def test_provider_error_handling(self, mock_manager_with_fallback):
        """Test specific provider error scenarios"""
        manager, mock_client, mock_settings = mock_manager_with_fallback
        
        # Test invalid provider name
        with pytest.raises(ProviderError):
            await manager._execute_provider_operation(
                AIServiceType.LLM,
                "invalid_provider", 
                manager._execute_llm_generation,
                []
            )

    @pytest.mark.asyncio
    async def test_retry_mechanism(self, mock_manager_with_fallback):
        """Test retry mechanism with exponential backoff"""
        manager, mock_client, mock_settings = mock_manager_with_fallback
        
        # Mock intermittent failures then success
        call_count = 0
        
        def mock_create(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            if call_count < 3:  # Fail first 2 attempts
                raise Exception("Temporary failure")
            return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="Retry success"))])
        
        mock_client.chat.completions.create.side_effect = mock_create
        
        result = await manager.generate_text("Test message", max_retries=3, use_cache=False)
        
        assert result == "Retry success"
        assert call_count == 3


class TestCachingImplementation:
    """Test Redis caching implementation"""

    @pytest.fixture
    def mock_manager_with_redis(self):
        """Create manager with Redis mocking"""
        with patch('app.services.ai_service_manager.get_redis') as mock_get_redis:
            mock_redis_client = AsyncMock()
            mock_get_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_redis_client

    @pytest.mark.asyncio
    async def test_cache_key_generation(self, mock_manager_with_redis):
        """Test cache key generation consistency"""
        manager, _ = mock_manager_with_redis
        
        # Same parameters should generate same key
        key1 = manager._generate_cache_key("llm", text="test", provider="zhipu")
        key2 = manager._generate_cache_key("llm", text="test", provider="zhipu")
        
        assert key1 == key2
        assert key1.startswith("ai:llm:")
        
        # Different parameters should generate different keys
        key3 = manager._generate_cache_key("llm", text="different", provider="zhipu")
        assert key1 != key3

    @pytest.mark.asyncio
    async def test_cache_set_get_operations(self, mock_manager_with_redis):
        """Test cache set and get operations"""
        manager, mock_redis = mock_manager_with_redis
        
        # Test successful cache operations
        test_key = "test_key"
        test_data = {"test": "data"}
        
        # Test set
        await manager._set_cached_result(test_key, test_data, ttl=3600)
        mock_redis.setex.assert_called_once_with(
            test_key, 3600, json.dumps(test_data, default=str)
        )
        
        # Test get
        mock_redis.get.return_value = json.dumps(test_data)
        result = await manager._get_cached_result(test_key)
        
        assert result == test_data
        mock_redis.get.assert_called_once_with(test_key)

    @pytest.mark.asyncio
    async def test_cache_error_handling(self, mock_manager_with_redis):
        """Test cache error handling doesn't break functionality"""
        manager, mock_redis = mock_manager_with_redis
        
        # Mock Redis errors
        mock_redis.get.side_effect = Exception("Redis connection failed")
        mock_redis.setex.side_effect = Exception("Redis connection failed")
        
        # Should return None on error, not raise exception
        result = await manager._get_cached_result("test_key")
        assert result is None
        
        # Should not raise exception on set error
        await manager._set_cached_result("test_key", {"data": "test"})
        # No exception should be raised

    @pytest.mark.asyncio
    async def test_different_ttl_values(self, mock_manager_with_redis):
        """Test different TTL scenarios"""
        manager, mock_redis = mock_manager_with_redis
        
        # Test default TTL
        await manager._set_cached_result("key1", "data1")
        mock_redis.setex.assert_called_with("key1", ai_settings.EMBEDDING_CACHE_TTL, '"data1"')
        
        # Test custom TTL
        await manager._set_cached_result("key2", "data2", ttl=7200)
        mock_redis.setex.assert_called_with("key2", 7200, '"data2"')


class TestHealthMonitoring:
    """Test comprehensive health monitoring functionality"""

    @pytest.fixture
    def mock_manager_health(self):
        """Create manager with health check mocking"""
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.ollama') as mock_ollama:
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_ollama

    @pytest.mark.asyncio
    async def test_single_provider_health_check_success(self, mock_manager_health):
        """Test successful single provider health check"""
        manager, mock_client, _ = mock_manager_health
        
        # Mock successful health check response
        mock_response = AsyncMock()
        mock_response.choices = [AsyncMock(message=AsyncMock(content="pong"))]
        mock_client.chat.completions.create.return_value = mock_response
        
        result = await manager.check_provider_health("zhipu")
        
        assert result["status"] == "healthy"
        assert result["provider"] == "zhipu"
        assert "response_time_ms" in result
        assert "models" in result
        assert "timestamp" in result

    @pytest.mark.asyncio
    async def test_single_provider_health_check_failure(self, mock_manager_health):
        """Test failed single provider health check"""
        manager, mock_client, _ = mock_manager_health
        
        # Mock health check failure
        mock_client.chat.completions.create.side_effect = Exception("Provider unavailable")
        
        result = await manager.check_provider_health("zhipu")
        
        assert result["status"] == "unhealthy"
        assert result["provider"] == "zhipu"
        assert "error" in result
        assert "response_time_ms" in result

    @pytest.mark.asyncio
    async def test_unknown_provider_health_check(self, mock_manager_health):
        """Test health check for unknown provider"""
        manager, _, _ = mock_manager_health
        
        result = await manager.check_provider_health("unknown_provider")
        
        assert result["status"] == "unhealthy"
        assert "Unknown provider" in result["error"]

    @pytest.mark.asyncio
    async def test_ollama_provider_health_check(self, mock_manager_health):
        """Test Ollama-specific health check"""
        manager, _, mock_ollama = mock_manager_health
        
        # Mock Ollama health check
        mock_ollama.list.return_value = {"models": []}
        
        result = await manager.check_provider_health("ollama")
        
        assert result["status"] == "healthy"
        assert result["provider"] == "ollama"

    @pytest.mark.asyncio
    async def test_all_providers_health_check(self, mock_manager_health):
        """Test comprehensive health check for all providers"""
        manager, mock_client, mock_ollama = mock_manager_health
        
        # Mock different provider responses
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="healthy"))]
        )
        mock_ollama.list.return_value = {"models": []}
        
        result = await manager.check_all_providers_health()
        
        assert "status" in result
        assert result["status"] in ["healthy", "degraded", "unhealthy"]
        assert "providers" in result
        assert "total_check_time_ms" in result
        assert "available_providers" in result
        assert "default_providers" in result

    @pytest.mark.asyncio
    async def test_service_method_health_check(self, mock_manager_health):
        """Test service method health checking"""
        manager, mock_client, _ = mock_manager_health
        
        # Mock successful service calls
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="test response"))]
        )
        mock_client.embeddings.create.return_value = AsyncMock(
            data=[AsyncMock(embedding=[0.1] * 100)]
        )
        
        result = await manager.check_service_method_health()
        
        assert "status" in result
        assert "service_methods" in result
        assert "providers" in result
        assert "total_check_time_ms" in result
        
        # Check individual service methods
        methods = result["service_methods"]
        assert "generate_text" in methods
        assert "generate_embedding" in methods
        assert "rerank_documents" in methods

    @pytest.mark.asyncio
    async def test_health_status_caching(self, mock_manager_health):
        """Test health status caching mechanism"""
        manager, mock_client, _ = mock_manager_health
        
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="healthy"))]
        )
        
        # First call should update cache
        result1 = await manager.check_all_providers_health()
        assert manager._last_health_check is not None
        assert len(manager._health_status) > 0
        
        # Cache should be populated
        cached_status = manager._health_status
        assert len(cached_status) > 0


class TestConcurrencyAndThreadSafety:
    """Test concurrent access and thread safety"""

    @pytest.fixture
    def mock_manager_concurrent(self):
        """Create manager for concurrency testing"""
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis:
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_redis_client

    @pytest.mark.asyncio
    async def test_concurrent_text_generation(self, mock_manager_concurrent):
        """Test concurrent text generation requests"""
        manager, mock_client, _ = mock_manager_concurrent
        
        # Mock responses
        responses = [f"Response {i}" for i in range(10)]
        mock_client.chat.completions.create.side_effect = [
            AsyncMock(choices=[AsyncMock(message=AsyncMock(content=resp))])
            for resp in responses
        ]
        
        # Create concurrent tasks
        tasks = [
            manager.generate_text(f"Message {i}", use_cache=False)
            for i in range(10)
        ]
        
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 10
        assert all(isinstance(result, str) for result in results)
        assert mock_client.chat.completions.create.call_count == 10

    @pytest.mark.asyncio
    async def test_concurrent_embedding_generation(self, mock_manager_concurrent):
        """Test concurrent embedding generation"""
        manager, mock_client, _ = mock_manager_concurrent
        
        # Mock embedding responses
        mock_embeddings = [[0.1] * 100 for _ in range(5)]
        mock_client.embeddings.create.side_effect = [
            AsyncMock(data=[AsyncMock(embedding=emb)])
            for emb in mock_embeddings
        ]
        
        tasks = [
            manager.generate_embedding(f"Text {i}", use_cache=False)
            for i in range(5)
        ]
        
        results = await asyncio.gather(*tasks)
        
        assert len(results) == 5
        assert all(isinstance(result, list) for result in results)

    @pytest.mark.asyncio
    async def test_concurrent_provider_switching(self, mock_manager_concurrent):
        """Test concurrent provider property changes"""
        manager, _, _ = mock_manager_concurrent
        
        async def switch_providers():
            manager.llm_provider = "zhipu"
            await asyncio.sleep(0.01)  # Small delay
            manager.embedding_provider = "moonshot"
            await asyncio.sleep(0.01)
            manager.rerank_provider = "openrouter"
        
        # Run multiple concurrent switches
        tasks = [switch_providers() for _ in range(5)]
        await asyncio.gather(*tasks)
        
        # Properties should be consistently set
        assert manager.llm_provider == "zhipu"
        assert manager.embedding_provider == "moonshot"
        assert manager.rerank_provider == "openrouter"


class TestPerformanceAndOptimization:
    """Test performance characteristics and optimizations"""

    @pytest.fixture
    def mock_manager_perf(self):
        """Create manager for performance testing"""
        with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai, \
             patch('app.services.ai_service_manager.get_redis') as mock_redis:
            
            mock_openai_client = AsyncMock()
            mock_openai.return_value = mock_openai_client
            
            mock_redis_client = AsyncMock()
            mock_redis.return_value = mock_redis_client
            
            AIServiceManager._instance = None
            AIServiceManager._initialized = False
            manager = AIServiceManager()
            
            yield manager, mock_openai_client, mock_redis_client

    @pytest.mark.asyncio
    async def test_cache_performance_improvement(self, mock_manager_perf):
        """Test that caching improves performance"""
        manager, mock_client, mock_redis = mock_manager_perf
        
        # Mock slow API call
        async def slow_api_call(*args, **kwargs):
            await asyncio.sleep(0.1)  # 100ms delay
            return AsyncMock(choices=[AsyncMock(message=AsyncMock(content="response"))])
        
        mock_client.chat.completions.create.side_effect = slow_api_call
        
        # First call (cache miss) - should be slower
        start_time = time.time()
        await manager.generate_text("test message", use_cache=False)
        uncached_time = time.time() - start_time
        
        # Mock cache hit for second call
        mock_redis.get.return_value = json.dumps("cached response")
        
        start_time = time.time()
        result = await manager.generate_text("test message", use_cache=True)
        cached_time = time.time() - start_time
        
        assert result == "cached response"
        assert cached_time < uncached_time / 2  # Should be significantly faster

    @pytest.mark.asyncio
    async def test_operation_metrics_logging(self, mock_manager_perf):
        """Test that operation metrics are properly logged"""
        manager, mock_client, _ = mock_manager_perf
        
        mock_client.chat.completions.create.return_value = AsyncMock(
            choices=[AsyncMock(message=AsyncMock(content="response"))]
        )
        
        # Test successful operation
        with patch.object(manager, '_log_operation_metrics') as mock_log:
            await manager.generate_text("test message", use_cache=False)
            
            mock_log.assert_called_once()
            call_args = mock_log.call_args[0]
            assert call_args[0] == "generate_text"  # operation
            assert isinstance(call_args[1], str)    # provider
            assert call_args[2] is True             # success
            assert isinstance(call_args[3], (int, float))  # duration

    @pytest.mark.asyncio 
    async def test_batch_operations_efficiency(self, mock_manager_perf):
        """Test efficiency of batch operations"""
        manager, mock_client, _ = mock_manager_perf
        
        # Mock embedding responses
        mock_embeddings = [[0.1] * 100 for _ in range(10)]
        mock_client.embeddings.create.side_effect = [
            AsyncMock(data=[AsyncMock(embedding=emb)])
            for emb in mock_embeddings
        ]
        
        texts = [f"Text {i}" for i in range(10)]
        
        # Test concurrent batch processing
        start_time = time.time()
        tasks = [manager.generate_embedding(text, use_cache=False) for text in texts]
        results = await asyncio.gather(*tasks)
        batch_time = time.time() - start_time
        
        assert len(results) == 10
        assert all(len(result) == 100 for result in results)
        
        # Batch processing should be reasonably fast
        assert batch_time < 1.0  # Should complete within 1 second in mocked scenario


@pytest.mark.asyncio
async def test_integration_with_real_config():
    """Integration test with real AI configuration"""
    # Test that the manager can be initialized with real config
    # This tests the configuration integration without making actual API calls
    
    manager = ai_service_manager
    assert manager is not None
    
    # Test provider properties
    assert isinstance(manager.llm_provider, str)
    assert isinstance(manager.embedding_provider, str) 
    assert isinstance(manager.rerank_provider, str)
    
    # Test available providers
    available = manager.get_available_providers()
    assert isinstance(available, list)
    assert len(available) > 0


@pytest.mark.asyncio
async def test_error_propagation():
    """Test that errors are properly propagated through the system"""
    
    with patch('app.services.ai_service_manager.AsyncOpenAI') as mock_openai:
        mock_client = AsyncMock()
        mock_openai.return_value = mock_client
        
        # Test that API errors are properly wrapped
        mock_client.chat.completions.create.side_effect = Exception("API Error")
        
        manager = ai_service_manager
        
        with pytest.raises(Exception):
            await manager.generate_text("test", use_cache=False, max_retries=1)


def test_manager_state_persistence():
    """Test that manager state persists correctly across operations"""
    
    manager = ai_service_manager
    
    # Test that singleton state is maintained
    original_llm = manager.llm_provider
    manager.llm_provider = "zhipu"
    
    # Get the same instance
    same_manager = AIServiceManager()
    assert same_manager.llm_provider == "zhipu"
    
    # Restore original
    manager.llm_provider = original_llm