"""
Test runner for migration fix tests
Provides utilities to run migration tests with proper setup and teardown
"""
import pytest
import asyncio
import sys
import subprocess
from pathlib import Path
from typing import List, Optional


class MigrationTestRunner:
    """Test runner for migration-related tests"""
    
    def __init__(self):
        self.test_files = [
            "test_migration_fix.py",
            "test_migration_scripts_unit.py", 
            "test_migration_integration.py",
            "test_migration_safety.py"
        ]
        self.scripts_path = Path(__file__).parent.parent.parent / "scripts" / "database"
    
    def add_scripts_to_path(self):
        """Add scripts directory to Python path"""
        if str(self.scripts_path) not in sys.path:
            sys.path.insert(0, str(self.scripts_path))
    
    def remove_scripts_from_path(self):
        """Remove scripts directory from Python path"""
        if str(self.scripts_path) in sys.path:
            sys.path.remove(str(self.scripts_path))
    
    def run_unit_tests(self) -> int:
        """Run unit tests for migration scripts"""
        return pytest.main([
            str(Path(__file__).parent / "test_migration_scripts_unit.py"),
            "-v", "--tb=short"
        ])
    
    def run_integration_tests(self) -> int:
        """Run integration tests for migration workflow"""
        return pytest.main([
            str(Path(__file__).parent / "test_migration_integration.py"),
            "-v", "--tb=short", "-m", "integration"
        ])
    
    def run_safety_tests(self) -> int:
        """Run safety tests for migration operations"""
        return pytest.main([
            str(Path(__file__).parent / "test_migration_safety.py"),
            "-v", "--tb=short"
        ])
    
    def run_validation_tests(self) -> int:
        """Run validation tests for migration state"""
        return pytest.main([
            str(Path(__file__).parent / "test_migration_fix.py"),
            "-v", "--tb=short"
        ])
    
    def run_all_tests(self, include_integration: bool = True) -> dict:
        """Run all migration tests and return results"""
        results = {}
        
        print("🧪 Running Migration Fix Test Suite")
        print("=" * 50)
        
        # Add scripts to path
        self.add_scripts_to_path()
        
        try:
            # Run unit tests
            print("\n📋 Running Unit Tests...")
            results['unit'] = self.run_unit_tests()
            
            # Run validation tests
            print("\n✅ Running Validation Tests...")
            results['validation'] = self.run_validation_tests()
            
            # Run safety tests
            print("\n🛡️ Running Safety Tests...")
            results['safety'] = self.run_safety_tests()
            
            # Run integration tests (if requested)
            if include_integration:
                print("\n🔗 Running Integration Tests...")
                results['integration'] = self.run_integration_tests()
        
        finally:
            # Clean up path
            self.remove_scripts_from_path()
        
        return results
    
    def run_quick_tests(self) -> dict:
        """Run quick tests (excludes integration tests)"""
        return self.run_all_tests(include_integration=False)
    
    def print_results_summary(self, results: dict):
        """Print a summary of test results"""
        print("\n" + "=" * 50)
        print("🔍 Migration Test Results Summary")
        print("=" * 50)
        
        total_passed = 0
        total_failed = 0
        
        for test_type, exit_code in results.items():
            status = "✅ PASSED" if exit_code == 0 else "❌ FAILED"
            print(f"{test_type.upper():15} {status}")
            
            if exit_code == 0:
                total_passed += 1
            else:
                total_failed += 1
        
        print("-" * 50)
        print(f"{'TOTAL PASSED:':<15} {total_passed}")
        print(f"{'TOTAL FAILED:':<15} {total_failed}")
        print("-" * 50)
        
        if total_failed == 0:
            print("🎉 All migration tests PASSED!")
            print("✅ Migration fix implementation is working correctly")
        else:
            print("⚠️  Some migration tests FAILED!")
            print("🔧 Please check the failing tests and fix issues")
        
        return total_failed == 0


def run_validation_only():
    """Run only the migration validation tests"""
    runner = MigrationTestRunner()
    runner.add_scripts_to_path()
    
    try:
        result = runner.run_validation_tests()
        return result
    finally:
        runner.remove_scripts_from_path()


def run_safety_only():
    """Run only the migration safety tests"""
    runner = MigrationTestRunner()
    result = runner.run_safety_tests()
    return result


def run_unit_only():
    """Run only the unit tests"""
    runner = MigrationTestRunner()
    runner.add_scripts_to_path()
    
    try:
        result = runner.run_unit_tests()
        return result
    finally:
        runner.remove_scripts_from_path()


async def validate_migration_state():
    """Async function to validate current migration state"""
    from pathlib import Path
    import sys
    
    # Add scripts to path
    scripts_path = Path(__file__).parent.parent.parent / "scripts" / "database"
    sys.path.insert(0, str(scripts_path))
    
    try:
        import validate_migration
        result = await validate_migration.main()
        return result == 0
    except ImportError as e:
        print(f"❌ Could not import validation script: {e}")
        return False
    finally:
        if str(scripts_path) in sys.path:
            sys.path.remove(str(scripts_path))


def main():
    """Main function for running migration tests"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Run migration fix tests")
    parser.add_argument("--type", choices=["all", "quick", "unit", "validation", "safety", "integration"],
                       default="quick", help="Type of tests to run")
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    
    args = parser.parse_args()
    
    runner = MigrationTestRunner()
    
    if args.type == "all":
        results = runner.run_all_tests(include_integration=True)
    elif args.type == "quick":
        results = runner.run_quick_tests()
    elif args.type == "unit":
        return run_unit_only()
    elif args.type == "validation":
        return run_validation_only()
    elif args.type == "safety":
        return run_safety_only()
    elif args.type == "integration":
        runner.add_scripts_to_path()
        try:
            return runner.run_integration_tests()
        finally:
            runner.remove_scripts_from_path()
    
    # Print summary for multi-test runs
    if args.type in ["all", "quick"]:
        success = runner.print_results_summary(results)
        return 0 if success else 1
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)