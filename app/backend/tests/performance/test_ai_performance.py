"""
Performance and load testing for AI services
"""
import pytest
import asyncio
import time
import statistics
from typing import List, Dict, Any
from unittest.mock import patch, AsyncMock
import random

from tests.fixtures.ai_mocks import (
    MockAIServiceManager,
    create_mock_ai_service_manager,
    create_slow_provider,
    MOCK_RESUME_EXTRACTION,
    MOCK_QUESTIONNAIRE,
    MOCK_EVALUATION
)


class TestAIServicePerformance:
    """Performance benchmarks for AI services"""
    
    @pytest.fixture
    def mock_ai_manager(self):
        return create_mock_ai_service_manager()
    
    @pytest.mark.asyncio
    async def test_single_request_latency(self, mock_ai_manager):
        """Benchmark single request latency"""
        messages = [{"role": "user", "content": "Test message"}]
        
        # Warm up
        await mock_ai_manager.create_completion(messages)
        
        # Measure latency
        latencies = []
        for _ in range(10):
            start = time.perf_counter()
            await mock_ai_manager.create_completion(messages)
            latency = (time.perf_counter() - start) * 1000  # Convert to ms
            latencies.append(latency)
        
        avg_latency = statistics.mean(latencies)
        p95_latency = statistics.quantiles(latencies, n=20)[18]  # 95th percentile
        
        # Assert performance targets
        assert avg_latency < 50  # Average should be under 50ms for mocked calls
        assert p95_latency < 100  # P95 should be under 100ms
        
        print(f"Average latency: {avg_latency:.2f}ms")
        print(f"P95 latency: {p95_latency:.2f}ms")
    
    @pytest.mark.asyncio
    async def test_concurrent_request_handling(self, mock_ai_manager):
        """Test concurrent request handling performance"""
        num_requests = 100
        messages = [
            [{"role": "user", "content": f"Test message {i}"}]
            for i in range(num_requests)
        ]
        
        start = time.perf_counter()
        
        # Create concurrent tasks
        tasks = [
            mock_ai_manager.create_completion(msg)
            for msg in messages
        ]
        
        # Execute concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        elapsed = time.perf_counter() - start
        
        # Check all succeeded
        successful = [r for r in results if not isinstance(r, Exception)]
        assert len(successful) == num_requests
        
        # Calculate throughput
        throughput = num_requests / elapsed
        
        print(f"Processed {num_requests} requests in {elapsed:.2f}s")
        print(f"Throughput: {throughput:.2f} requests/second")
        
        # Performance targets
        assert elapsed < 5  # Should handle 100 requests in under 5 seconds
        assert throughput > 20  # Should handle at least 20 requests/second
    
    @pytest.mark.asyncio
    async def test_fallback_performance_impact(self, mock_ai_manager):
        """Measure performance impact of fallback mechanism"""
        messages = [{"role": "user", "content": "Test"}]
        
        # Measure without fallback (all succeed)
        start = time.perf_counter()
        for _ in range(50):
            await mock_ai_manager.create_completion(messages)
        time_no_fallback = time.perf_counter() - start
        
        # Reset and make 50% fail (trigger fallback)
        mock_ai_manager.reset_all()
        mock_ai_manager.set_provider_fail_rate("deepseek", 0.5)
        
        start = time.perf_counter()
        for _ in range(50):
            await mock_ai_manager.create_completion(messages, use_fallback=True)
        time_with_fallback = time.perf_counter() - start
        
        # Fallback should add some overhead but not more than 2x
        overhead_ratio = time_with_fallback / time_no_fallback
        
        print(f"Time without fallback: {time_no_fallback:.2f}s")
        print(f"Time with fallback: {time_with_fallback:.2f}s")
        print(f"Overhead ratio: {overhead_ratio:.2f}x")
        
        assert overhead_ratio < 2.0  # Fallback shouldn't more than double the time
    
    @pytest.mark.asyncio
    async def test_embedding_generation_performance(self, mock_ai_manager):
        """Test embedding generation performance"""
        texts = [f"Document {i} with some content to embed" for i in range(100)]
        
        start = time.perf_counter()
        
        tasks = [
            mock_ai_manager.create_embedding(text)
            for text in texts
        ]
        
        embeddings = await asyncio.gather(*tasks)
        
        elapsed = time.perf_counter() - start
        
        # Verify all embeddings generated
        assert len(embeddings) == 100
        assert all(len(emb) == 1024 for emb in embeddings)
        
        # Performance metrics
        embeddings_per_second = 100 / elapsed
        
        print(f"Generated 100 embeddings in {elapsed:.2f}s")
        print(f"Rate: {embeddings_per_second:.2f} embeddings/second")
        
        assert embeddings_per_second > 20  # Should handle at least 20/second
    
    @pytest.mark.asyncio
    async def test_memory_usage_under_load(self, mock_ai_manager):
        """Test memory usage doesn't grow excessively under load"""
        import gc
        import sys
        
        # Force garbage collection
        gc.collect()
        
        # Get initial memory (approximation using object count)
        initial_objects = len(gc.get_objects())
        
        # Run many requests
        for i in range(500):
            messages = [{"role": "user", "content": f"Test {i}"}]
            await mock_ai_manager.create_completion(messages)
            
            # Reset history periodically to avoid accumulation
            if i % 100 == 0:
                mock_ai_manager.call_history = []
        
        # Force garbage collection
        gc.collect()
        
        # Check memory growth
        final_objects = len(gc.get_objects())
        object_growth = final_objects - initial_objects
        
        print(f"Initial objects: {initial_objects}")
        print(f"Final objects: {final_objects}")
        print(f"Object growth: {object_growth}")
        
        # Should not have excessive object growth
        assert object_growth < 10000  # Reasonable threshold
    
    @pytest.mark.asyncio
    async def test_provider_switching_performance(self, mock_ai_manager):
        """Test performance of dynamic provider switching"""
        providers = ["deepseek", "moonshot", "openai", "qwen"]
        messages = [{"role": "user", "content": "Test"}]
        
        switch_times = []
        
        for _ in range(50):
            # Switch to random provider
            new_provider = random.choice(providers)
            
            start = time.perf_counter()
            mock_ai_manager.current_provider = new_provider
            await mock_ai_manager.create_completion(messages)
            switch_time = (time.perf_counter() - start) * 1000
            
            switch_times.append(switch_time)
        
        avg_switch_time = statistics.mean(switch_times)
        
        print(f"Average provider switch time: {avg_switch_time:.2f}ms")
        
        assert avg_switch_time < 10  # Provider switching should be fast
    
    @pytest.mark.asyncio
    async def test_slow_provider_timeout_behavior(self):
        """Test behavior with slow providers"""
        mock_ai = create_mock_ai_service_manager()
        
        # Create slow primary provider (2 second delay)
        slow_provider = create_slow_provider("deepseek", delay=0.1)
        mock_ai.providers["deepseek"] = slow_provider
        
        messages = [{"role": "user", "content": "Test"}]
        
        start = time.perf_counter()
        result = await mock_ai.create_completion(messages)
        elapsed = time.perf_counter() - start
        
        assert result is not None
        assert elapsed >= 0.1  # Should take at least the delay time
        assert elapsed < 0.5  # But not too long
    
    @pytest.mark.asyncio
    async def test_questionnaire_generation_performance(self, mock_ai_manager):
        """Test questionnaire generation performance"""
        params = {
            "position_type": "Software Engineer",
            "dimensions": ["Technical", "Communication", "Problem Solving"],
            "question_count": 20,
            "industry": "Technology"
        }
        
        # Warm up
        await mock_ai_manager.generate_questionnaire(**params)
        
        # Benchmark
        times = []
        for _ in range(10):
            start = time.perf_counter()
            result = await mock_ai_manager.generate_questionnaire(**params)
            elapsed = (time.perf_counter() - start) * 1000
            times.append(elapsed)
            
            assert "questions" in result
        
        avg_time = statistics.mean(times)
        print(f"Average questionnaire generation time: {avg_time:.2f}ms")
        
        assert avg_time < 100  # Should generate in under 100ms
    
    @pytest.mark.asyncio
    async def test_evaluation_performance(self, mock_ai_manager):
        """Test response evaluation performance"""
        eval_params = {
            "questionnaire_title": "Test Questionnaire",
            "position_type": "Engineer",
            "dimension_scores": {"technical": 85, "communication": 90},
            "answers_summary": "Sample answers " * 100  # Large text
        }
        
        times = []
        for _ in range(10):
            start = time.perf_counter()
            result = await mock_ai_manager.evaluate_responses(**eval_params)
            elapsed = (time.perf_counter() - start) * 1000
            times.append(elapsed)
            
            assert "score" in result
        
        avg_time = statistics.mean(times)
        print(f"Average evaluation time: {avg_time:.2f}ms")
        
        assert avg_time < 150  # Should evaluate in under 150ms


class TestLoadScenarios:
    """Test various load scenarios"""
    
    @pytest.mark.asyncio
    async def test_sustained_load(self):
        """Test sustained load over time"""
        mock_ai = create_mock_ai_service_manager()
        
        duration = 5  # seconds
        start = time.perf_counter()
        request_count = 0
        errors = 0
        
        while time.perf_counter() - start < duration:
            try:
                await mock_ai.create_completion(
                    [{"role": "user", "content": f"Request {request_count}"}]
                )
                request_count += 1
            except Exception:
                errors += 1
            
            # Small delay to simulate realistic load
            await asyncio.sleep(0.01)
        
        elapsed = time.perf_counter() - start
        rps = request_count / elapsed
        error_rate = errors / max(request_count, 1)
        
        print(f"Sustained load test:")
        print(f"  Duration: {elapsed:.2f}s")
        print(f"  Requests: {request_count}")
        print(f"  RPS: {rps:.2f}")
        print(f"  Error rate: {error_rate:.2%}")
        
        assert rps > 50  # Should maintain at least 50 RPS
        assert error_rate < 0.01  # Less than 1% error rate
    
    @pytest.mark.asyncio
    async def test_burst_load(self):
        """Test handling of burst traffic"""
        mock_ai = create_mock_ai_service_manager()
        
        # Simulate burst of 200 requests
        burst_size = 200
        messages = [
            [{"role": "user", "content": f"Burst request {i}"}]
            for i in range(burst_size)
        ]
        
        start = time.perf_counter()
        
        # Send all at once
        tasks = [mock_ai.create_completion(msg) for msg in messages]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        elapsed = time.perf_counter() - start
        
        # Check success rate
        successful = [r for r in results if not isinstance(r, Exception)]
        success_rate = len(successful) / burst_size
        
        print(f"Burst load test:")
        print(f"  Burst size: {burst_size}")
        print(f"  Time: {elapsed:.2f}s")
        print(f"  Success rate: {success_rate:.2%}")
        
        assert success_rate > 0.99  # At least 99% should succeed
        assert elapsed < 10  # Should handle burst in under 10 seconds
    
    @pytest.mark.asyncio
    async def test_mixed_operation_load(self):
        """Test load with mixed operation types"""
        mock_ai = create_mock_ai_service_manager()
        
        # Mix of different operations
        operations = []
        for i in range(100):
            op_type = i % 4
            if op_type == 0:
                # Completion
                operations.append(("completion", [{"role": "user", "content": f"Test {i}"}]))
            elif op_type == 1:
                # Embedding
                operations.append(("embedding", f"Text to embed {i}"))
            elif op_type == 2:
                # Questionnaire
                operations.append(("questionnaire", {
                    "position_type": "Engineer",
                    "dimensions": ["Technical"],
                    "question_count": 5,
                    "industry": "Tech"
                }))
            else:
                # Evaluation
                operations.append(("evaluation", {
                    "questionnaire_title": "Test",
                    "position_type": "Engineer",
                    "dimension_scores": {},
                    "answers_summary": f"Answer {i}"
                }))
        
        start = time.perf_counter()
        
        # Execute all operations
        tasks = []
        for op_type, params in operations:
            if op_type == "completion":
                tasks.append(mock_ai.create_completion(params))
            elif op_type == "embedding":
                tasks.append(mock_ai.create_embedding(params))
            elif op_type == "questionnaire":
                tasks.append(mock_ai.generate_questionnaire(**params))
            elif op_type == "evaluation":
                tasks.append(mock_ai.evaluate_responses(**params))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        elapsed = time.perf_counter() - start
        
        successful = [r for r in results if not isinstance(r, Exception)]
        success_rate = len(successful) / len(operations)
        
        print(f"Mixed operation load test:")
        print(f"  Operations: {len(operations)}")
        print(f"  Time: {elapsed:.2f}s")
        print(f"  Success rate: {success_rate:.2%}")
        print(f"  Throughput: {len(operations)/elapsed:.2f} ops/sec")
        
        assert success_rate > 0.99  # High success rate
        assert elapsed < 15  # Should complete in reasonable time