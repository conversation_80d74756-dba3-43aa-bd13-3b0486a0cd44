"""Add monitoring tables

Revision ID: 5a37128ab6eb
Revises: d84ed73b5c25
Create Date: 2025-08-29 06:00:00.000000

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5a37128ab6eb'
down_revision: Union[str, None] = 'd84ed73b5c25'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Create service_healths table
    op.create_table('service_healths',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('name', sa.String(length=50), nullable=False),
        sa.Column('display_name', sa.String(length=100), nullable=False),
        sa.Column('status', sa.Enum('HEALTHY', 'DEGRADED', 'UNHEALTHY', 'UNKNOWN', name='servicestatus'), nullable=False),
        sa.Column('response_time', sa.Float(), nullable=True),
        sa.Column('last_check', sa.String(length=50), nullable=False),
        sa.Column('details', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_service_healths_name', 'service_healths', ['name'], unique=False)
    op.create_index('idx_service_healths_created_at', 'service_healths', ['created_at'], unique=False)

    # Create system_metricss table
    op.create_table('system_metricss',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('cpu_usage', sa.Float(), nullable=False),
        sa.Column('memory_usage', sa.Float(), nullable=False),
        sa.Column('disk_usage', sa.Float(), nullable=False),
        sa.Column('network_in', sa.Float(), nullable=False),
        sa.Column('network_out', sa.Float(), nullable=False),
        sa.Column('services_total', sa.Integer(), nullable=False),
        sa.Column('services_healthy', sa.Integer(), nullable=False),
        sa.Column('services_degraded', sa.Integer(), nullable=False),
        sa.Column('services_unhealthy', sa.Integer(), nullable=False),
        sa.Column('additional_metrics', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_system_metricss_created_at', 'system_metricss', ['created_at'], unique=False)

    # Create monitoring_snapshots table
    op.create_table('monitoring_snapshots',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('overall_status', sa.Enum('HEALTHY', 'DEGRADED', 'UNHEALTHY', 'UNKNOWN', name='servicestatus'), nullable=False),
        sa.Column('health_score', sa.Float(), nullable=False),
        sa.Column('cpu_usage', sa.Float(), nullable=False),
        sa.Column('memory_usage', sa.Float(), nullable=False),
        sa.Column('disk_usage', sa.Float(), nullable=False),
        sa.Column('services_total', sa.Integer(), nullable=False),
        sa.Column('services_healthy', sa.Integer(), nullable=False),
        sa.Column('services_degraded', sa.Integer(), nullable=False),
        sa.Column('services_unhealthy', sa.Integer(), nullable=False),
        sa.Column('uptime_hours', sa.Float(), nullable=False),
        sa.Column('raw_response_json', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('services_snapshot', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('metrics_snapshot', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
        sa.Column('cache_info', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('snapshot_source', sa.String(length=50), nullable=False, server_default='monitoring_service'),
        sa.Column('snapshot_trigger', sa.String(length=100), nullable=True),
        sa.Column('snapshot_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
        sa.Column('created_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.Column('updated_at', postgresql.TIMESTAMP(timezone=True), server_default=sa.text('CURRENT_TIMESTAMP'), nullable=False),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index('idx_monitoring_snapshots_created_at', 'monitoring_snapshots', ['created_at'], unique=False)
    op.create_index('idx_monitoring_snapshots_overall_status', 'monitoring_snapshots', ['overall_status'], unique=False)


def downgrade() -> None:
    # Drop monitoring_snapshots table
    op.drop_index('idx_monitoring_snapshots_overall_status', table_name='monitoring_snapshots')
    op.drop_index('idx_monitoring_snapshots_created_at', table_name='monitoring_snapshots')
    op.drop_table('monitoring_snapshots')
    
    # Drop system_metricss table
    op.drop_index('idx_system_metricss_created_at', table_name='system_metricss')
    op.drop_table('system_metricss')
    
    # Drop service_healths table
    op.drop_index('idx_service_healths_created_at', table_name='service_healths')
    op.drop_index('idx_service_healths_name', table_name='service_healths')
    op.drop_table('service_healths')
    
    # Drop the enum type (only if no other tables use it)
    op.execute('DROP TYPE IF EXISTS servicestatus')