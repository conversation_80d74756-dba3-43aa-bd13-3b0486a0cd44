"""Fix candidate_assessments table schema - drop and recreate with BigInteger ID

Revision ID: 498bf2a76c6d
Revises: 5a37128ab6eb
Create Date: 2025-08-29 10:22:33.918413

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import func


# revision identifiers, used by Alembic.
revision: str = '498bf2a76c6d'
down_revision: Union[str, None] = '5a37128ab6eb'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # Drop the existing candidate_assessments table (it's empty anyway)
    op.drop_table('candidate_assessments')
    
    # Create the new candidate_assessments table with BigInteger ID
    op.create_table('candidate_assessments',
        sa.Column('id', sa.BigInteger(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=False),
        sa.Column('updated_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=False),
        sa.Column('candidate_id', sa.BigInteger(), nullable=False),
        sa.Column('digital_literacy_score', sa.Float(), nullable=False),
        sa.Column('industry_skills_score', sa.Float(), nullable=False),
        sa.Column('position_skills_score', sa.Float(), nullable=False),
        sa.Column('innovation_score', sa.Float(), nullable=False),
        sa.Column('learning_potential_score', sa.Float(), nullable=False),
        sa.Column('dci_score', sa.Float(), nullable=False),
        sa.Column('overall_percentile', sa.Float(), nullable=True),
        sa.Column('assessment_details', sa.JSON(), nullable=True),
        sa.Column('strengths', sa.JSON(), nullable=True),
        sa.Column('improvement_areas', sa.JSON(), nullable=True),
        sa.Column('recommendations', sa.JSON(), nullable=True),
        sa.Column('assessment_version', sa.String(length=50), default='v1.0'),
        sa.Column('assessed_at', sa.DateTime(timezone=True), server_default=func.now()),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], ondelete='CASCADE'),
        sa.UniqueConstraint('candidate_id', name='uq_assessments_candidate_id'),
        sa.CheckConstraint('digital_literacy_score >= 0 AND digital_literacy_score <= 100', name='ck_digital_literacy_range'),
        sa.CheckConstraint('industry_skills_score >= 0 AND industry_skills_score <= 100', name='ck_industry_skills_range'),
        sa.CheckConstraint('position_skills_score >= 0 AND position_skills_score <= 100', name='ck_position_skills_range'),
        sa.CheckConstraint('innovation_score >= 0 AND innovation_score <= 100', name='ck_innovation_range'),
        sa.CheckConstraint('learning_potential_score >= 0 AND learning_potential_score <= 100', name='ck_learning_potential_range'),
        sa.CheckConstraint('dci_score >= 0 AND dci_score <= 100', name='ck_dci_score_range'),
        sa.CheckConstraint('overall_percentile IS NULL OR (overall_percentile >= 0 AND overall_percentile <= 100)', name='ck_percentile_range'),
    )
    
    # Create indexes
    op.create_index('ix_candidate_assessments_candidate_id', 'candidate_assessments', ['candidate_id'])
    op.create_index('ix_candidate_assessments_dci_score', 'candidate_assessments', ['dci_score'])
    op.create_index('ix_candidate_assessments_overall_percentile', 'candidate_assessments', ['overall_percentile'])
    op.create_index('ix_candidate_assessments_assessed_at', 'candidate_assessments', ['assessed_at'])


def downgrade() -> None:
    # This is a destructive operation - in downgrade we recreate the old UUID-based table
    op.drop_table('candidate_assessments')
    
    # For downgrade, we'll create the old schema (UUID-based)
    # This is just for completeness, in practice this might lose data
    op.create_table('candidate_assessments',
        sa.Column('id', sa.dialects.postgresql.UUID(), nullable=False),
        sa.Column('candidate_id', sa.BigInteger(), nullable=False),
        sa.Column('digital_literacy_score', sa.Float(), nullable=False),
        sa.Column('industry_skills_score', sa.Float(), nullable=False),
        sa.Column('position_skills_score', sa.Float(), nullable=False),
        sa.Column('innovation_score', sa.Float(), nullable=False),
        sa.Column('learning_potential_score', sa.Float(), nullable=False),
        sa.Column('dci_score', sa.Float(), nullable=False),
        sa.Column('overall_percentile', sa.Float(), nullable=True),
        sa.Column('assessment_details', sa.JSON(), nullable=True),
        sa.Column('strengths', sa.JSON(), nullable=True),
        sa.Column('improvement_areas', sa.JSON(), nullable=True),
        sa.Column('recommendations', sa.JSON(), nullable=True),
        sa.Column('assessment_version', sa.String(length=50), default='v1.0'),
        sa.Column('assessed_at', sa.DateTime(timezone=True), server_default=func.now()),
        sa.Column('updated_at', sa.DateTime(timezone=True)),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], ondelete='CASCADE'),
        sa.UniqueConstraint('candidate_id', name='uq_assessments_candidate_id'),
    )