"""complete models with evaluation and preferences

Revision ID: c4fbd1463232
Revises: 
Create Date: 2025-08-28 02:17:40.903406

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import pgvector.sqlalchemy

# revision identifiers, used by Alembic.
revision: str = 'c4fbd1463232'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # 0. Create pgvector extension if not exists
    op.execute('CREATE EXTENSION IF NOT EXISTS vector')
    
    # 1. First create independent tables (no foreign keys)
    op.create_table('permissions',
    sa.Column('code', sa.String(length=100), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('module', sa.String(length=50), nullable=False),
    sa.Column('resource', sa.String(length=50), nullable=False),
    sa.Column('action', sa.String(length=50), nullable=False),
    sa.Column('access_level', sa.String(length=20), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_system', sa.Boolean(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_permissions'))
    )
    op.create_index(op.f('ix_permissions_code'), 'permissions', ['code'], unique=True)
    op.create_index(op.f('ix_permissions_module'), 'permissions', ['module'], unique=False)
    
    op.create_table('roles',
    sa.Column('code', sa.String(length=50), nullable=False),
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('level', sa.Integer(), nullable=False),
    sa.Column('is_super_admin', sa.Boolean(), nullable=False),
    sa.Column('is_system', sa.Boolean(), nullable=False),
    sa.Column('is_default', sa.Boolean(), nullable=False),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('can_be_assigned', sa.Boolean(), nullable=False),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_roles'))
    )
    op.create_index(op.f('ix_roles_code'), 'roles', ['code'], unique=True)
    
    # 2. Create role_permissions junction table
    op.create_table('role_permissions',
    sa.Column('role_id', sa.BigInteger(), nullable=False),
    sa.Column('permission_id', sa.BigInteger(), nullable=False),
    sa.ForeignKeyConstraint(['permission_id'], ['permissions.id'], name=op.f('fk_role_permissions_permission_id_permissions'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], name=op.f('fk_role_permissions_role_id_roles'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('role_id', 'permission_id', name=op.f('pk_role_permissions'))
    )
    
    # 3. Create users table (references roles)
    op.create_table('users',
    sa.Column('email', sa.String(length=255), nullable=False),
    sa.Column('username', sa.String(length=50), nullable=False),
    sa.Column('hashed_password', sa.String(length=255), nullable=False),
    sa.Column('full_name', sa.String(length=100), nullable=True),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('avatar_url', sa.String(length=500), nullable=True),
    sa.Column('role', sa.String(length=50), nullable=False),
    sa.Column('role_id', sa.BigInteger(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=False),
    sa.Column('is_superuser', sa.Boolean(), nullable=False),
    sa.Column('is_verified', sa.Boolean(), nullable=False),
    sa.Column('failed_login_attempts', sa.Integer(), nullable=False),
    sa.Column('locked_until', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_login_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('last_password_change', sa.DateTime(timezone=True), nullable=True),
    sa.Column('department', sa.String(length=100), nullable=True),
    sa.Column('position', sa.String(length=100), nullable=True),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['role_id'], ['roles.id'], name=op.f('fk_users_role_id_roles')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_users'))
    )
    op.create_index(op.f('ix_users_email'), 'users', ['email'], unique=True)
    op.create_index(op.f('ix_users_username'), 'users', ['username'], unique=True)
    
    # 4. Create user preferences
    op.create_table('user_preferences',
    sa.Column('id', sa.Integer(), nullable=False),
    sa.Column('user_id', sa.BigInteger(), nullable=False),
    sa.Column('locale', sa.String(length=10), nullable=False),
    sa.Column('timezone', sa.String(length=50), nullable=False),
    sa.Column('date_format', sa.String(length=20), nullable=False),
    sa.Column('time_format', sa.String(length=10), nullable=False),
    sa.Column('number_format', sa.String(length=10), nullable=False),
    sa.Column('currency', sa.String(length=3), nullable=False),
    sa.Column('theme', sa.String(length=10), nullable=False),
    sa.Column('email_notifications', sa.Boolean(), nullable=False),
    sa.Column('push_notifications', sa.Boolean(), nullable=False),
    sa.Column('show_dashboard_stats', sa.Boolean(), nullable=False),
    sa.Column('show_candidate_stats', sa.Boolean(), nullable=False),
    sa.Column('show_position_stats', sa.Boolean(), nullable=False),
    sa.Column('show_user_management_stats', sa.Boolean(), nullable=False),
    sa.Column('show_notification_badges', sa.Boolean(), nullable=False),
    sa.Column('stats_refresh_interval', sa.Integer(), nullable=False),
    sa.Column('stats_display_mode', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['user_id'], ['users.id'], name=op.f('fk_user_preferences_user_id_users'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_user_preferences')),
    sa.UniqueConstraint('user_id', name=op.f('uq_user_preferences_user_id'))
    )
    op.create_index(op.f('ix_user_preferences_id'), 'user_preferences', ['id'], unique=False)
    
    # 5. Create candidates table (references users)
    op.create_table('candidates',
    sa.Column('name', sa.String(length=100), nullable=False),
    sa.Column('email', sa.String(length=255), nullable=True),
    sa.Column('phone', sa.String(length=20), nullable=True),
    sa.Column('gender', sa.String(length=10), nullable=True),
    sa.Column('birth_date', sa.DateTime(timezone=True), nullable=True),
    sa.Column('current_position', sa.String(length=200), nullable=True),
    sa.Column('current_company', sa.String(length=200), nullable=True),
    sa.Column('years_of_experience', sa.Integer(), nullable=True),
    sa.Column('current_salary', sa.Integer(), nullable=True),
    sa.Column('expected_salary', sa.Integer(), nullable=True),
    sa.Column('expected_salary_min', sa.Integer(), nullable=True),
    sa.Column('expected_salary_max', sa.Integer(), nullable=True),
    sa.Column('education', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('education_level', sa.String(length=50), nullable=True),
    sa.Column('work_experience', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('skills', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('resume_url', sa.String(length=500), nullable=True),
    sa.Column('resume_text', sa.Text(), nullable=True),
    sa.Column('resume_parsed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('dci_score', sa.Float(), nullable=True),
    sa.Column('jfs_score', sa.Float(), nullable=True),
    sa.Column('assessment_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('match_score_avg', sa.Float(), nullable=True),
    sa.Column('interview_count', sa.Integer(), nullable=True),
    sa.Column('last_active_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('status', sa.Enum('new', 'screening', 'interview', 'offer', 'hired', 'rejected', 'withdrawn', name='candidatestatus'), nullable=True),
    sa.Column('data_permission', sa.Enum('PRIVATE', 'SHARED', 'TEAM', 'PUBLIC', name='datapermission'), nullable=True),
    sa.Column('shared_with', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('source', sa.String(length=50), nullable=True),
    sa.Column('source_channel', sa.String(length=100), nullable=True),
    sa.Column('referrer_id', sa.BigInteger(), nullable=True),
    sa.Column('approval_status', sa.Enum('pending', 'approved', 'rejected', 'withdrawn', name='approvalstatus'), nullable=True),
    sa.Column('reviewed_by', sa.BigInteger(), nullable=True),
    sa.Column('reviewed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('review_notes', sa.Text(), nullable=True),
    sa.Column('notes', sa.Text(), nullable=True),
    sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_by', sa.BigInteger(), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], name=op.f('fk_candidates_created_by_users')),
    sa.ForeignKeyConstraint(['referrer_id'], ['users.id'], name=op.f('fk_candidates_referrer_id_users')),
    sa.ForeignKeyConstraint(['reviewed_by'], ['users.id'], name=op.f('fk_candidates_reviewed_by_users')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_candidates'))
    )
    op.create_index('idx_candidate_approval_status', 'candidates', ['approval_status'], unique=False)
    op.create_index('idx_candidate_created_by', 'candidates', ['created_by'], unique=False)
    op.create_index('idx_candidate_deleted', 'candidates', ['is_deleted'], unique=False)
    op.create_index('idx_candidate_education_level', 'candidates', ['education_level'], unique=False)
    op.create_index('idx_candidate_email_phone', 'candidates', ['email', 'phone'], unique=False)
    op.create_index('idx_candidate_experience_years', 'candidates', ['years_of_experience'], unique=False)
    op.create_index('idx_candidate_interview_count', 'candidates', ['interview_count'], unique=False)
    # GIN index for text search - removed as it requires pg_trgm extension
    # op.create_index('idx_candidate_name_search', 'candidates', ['name'], unique=False, postgresql_using='gin')
    op.create_index('idx_candidate_permission', 'candidates', ['data_permission'], unique=False)
    op.create_index('idx_candidate_reviewed_by', 'candidates', ['reviewed_by'], unique=False)
    op.create_index('idx_candidate_salary_range', 'candidates', ['expected_salary_min', 'expected_salary_max'], unique=False)
    op.create_index('idx_candidate_skills', 'candidates', ['skills'], unique=False, postgresql_using='gin')
    op.create_index('idx_candidate_source', 'candidates', ['source'], unique=False)
    op.create_index('idx_candidate_status', 'candidates', ['status'], unique=False)
    op.create_index('idx_candidate_status_active', 'candidates', ['status', 'last_active_at'], unique=False)
    op.create_index(op.f('ix_candidates_email'), 'candidates', ['email'], unique=False)
    op.create_index(op.f('ix_candidates_name'), 'candidates', ['name'], unique=False)
    op.create_index(op.f('ix_candidates_phone'), 'candidates', ['phone'], unique=False)
    
    # 6. Create positions table (references users)
    op.create_table('positions',
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('department', sa.String(length=100), nullable=True),
    sa.Column('location', sa.String(length=100), nullable=True),
    sa.Column('job_level', sa.String(length=50), nullable=True),
    sa.Column('job_type', sa.String(length=50), nullable=True),
    sa.Column('headcount', sa.Integer(), nullable=True),
    sa.Column('urgency', sa.Enum('low', 'normal', 'high', 'critical', name='positionurgency'), nullable=True),
    sa.Column('status', sa.Enum('draft', 'reviewing', 'open', 'paused', 'closed', name='positionstatus'), nullable=True),
    sa.Column('salary_min', sa.Integer(), nullable=True),
    sa.Column('salary_max', sa.Integer(), nullable=True),
    sa.Column('salary_currency', sa.String(length=10), nullable=True),
    sa.Column('requirements', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('responsibilities', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('qualifications', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('benefits', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('capability_weights', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('required_skills', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('preferred_skills', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('data_permission', sa.Enum('PRIVATE', 'SHARED', 'TEAM', 'PUBLIC', name='datapermission'), nullable=True),
    sa.Column('shared_with', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('created_by', sa.BigInteger(), nullable=False),
    sa.Column('deadline', sa.DateTime(timezone=True), nullable=True),
    sa.Column('is_deleted', sa.Boolean(), nullable=True),
    sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], name=op.f('fk_positions_created_by_users')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_positions'))
    )
    op.create_index('idx_position_created_by', 'positions', ['created_by'], unique=False)
    op.create_index('idx_position_deleted', 'positions', ['is_deleted'], unique=False)
    op.create_index('idx_position_department', 'positions', ['department'], unique=False)
    op.create_index('idx_position_permission', 'positions', ['data_permission'], unique=False)
    op.create_index('idx_position_required_skills', 'positions', ['required_skills'], unique=False, postgresql_using='gin')
    op.create_index('idx_position_status', 'positions', ['status'], unique=False)
    # GIN index for text search - removed as it requires pg_trgm extension  
    # op.create_index('idx_position_title', 'positions', ['title'], unique=False, postgresql_using='gin')
    op.create_index(op.f('ix_positions_department'), 'positions', ['department'], unique=False)
    op.create_index(op.f('ix_positions_title'), 'positions', ['title'], unique=False)
    
    # 7. Create questionnaires table (references users)
    op.create_table('questionnaires',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('category', sa.Enum('SURVEY', 'ASSESSMENT', 'EVALUATION', 'QUIZ', 'FEEDBACK', name='questionnairecategory'), nullable=True),
    sa.Column('version', sa.Integer(), nullable=True),
    sa.Column('created_by', sa.BigInteger(), nullable=True),
    sa.Column('updated_by', sa.BigInteger(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('updated_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('status', sa.Enum('DRAFT', 'REVIEWING', 'ACTIVE', 'PAUSED', 'COMPLETED', 'ARCHIVED', name='questionnairestatus'), nullable=True),
    sa.Column('published_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('archived_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('theme', sa.String(length=50), nullable=True),
    sa.Column('branding', sa.JSON(), nullable=True),
    sa.Column('allow_back_navigation', sa.Boolean(), nullable=True),
    sa.Column('show_progress_bar', sa.Boolean(), nullable=True),
    sa.Column('randomize_questions', sa.Boolean(), nullable=True),
    sa.Column('one_question_per_page', sa.Boolean(), nullable=True),
    sa.Column('auto_save', sa.Boolean(), nullable=True),
    sa.Column('time_limit_minutes', sa.Integer(), nullable=True),
    sa.Column('scheduled_start', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('scheduled_end', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('scoring_enabled', sa.Boolean(), nullable=True),
    sa.Column('show_score_on_completion', sa.Boolean(), nullable=True),
    sa.Column('passing_score', sa.Float(), nullable=True),
    sa.Column('scoring_method', sa.Enum('SUM', 'AVERAGE', 'WEIGHTED', 'CUSTOM', name='scoringmethod'), nullable=True),
    sa.Column('custom_scoring_formula', sa.Text(), nullable=True),
    sa.Column('max_responses', sa.Integer(), nullable=True),
    sa.Column('allow_multiple_submissions', sa.Boolean(), nullable=True),
    sa.Column('require_authentication', sa.Boolean(), nullable=True),
    sa.Column('collect_email', sa.Boolean(), nullable=True),
    sa.Column('collect_name', sa.Boolean(), nullable=True),
    sa.Column('notify_on_submission', sa.Boolean(), nullable=True),
    sa.Column('notification_emails', sa.JSON(), nullable=True),
    sa.Column('respondent_notification', sa.Boolean(), nullable=True),
    sa.Column('completion_message', sa.Text(), nullable=True),
    sa.Column('redirect_url', sa.String(length=500), nullable=True),
    sa.Column('ai_generated', sa.Boolean(), nullable=True),
    sa.Column('generation_prompt', sa.Text(), nullable=True),
    sa.Column('ai_template_id', sa.BigInteger(), nullable=True),
    sa.Column('evaluation_criteria', sa.JSON(), nullable=True),
    sa.Column('industry', sa.String(length=50), nullable=True),
    sa.Column('position_type', sa.String(length=100), nullable=True),
    sa.Column('dimensions', sa.JSON(), nullable=True),
    sa.Column('slug', sa.String(length=200), nullable=True),
    sa.Column('valid_until', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('access_type', sa.Enum('PUBLIC', 'PRIVATE', 'PASSWORD', 'RESTRICTED', name='accesstype'), nullable=True),
    sa.Column('access_password', sa.String(length=255), nullable=True),
    sa.Column('max_submissions', sa.Integer(), nullable=True),
    sa.Column('reviewed_by', sa.BigInteger(), nullable=True),
    sa.Column('review_comments', sa.Text(), nullable=True),
    sa.Column('rejection_reason', sa.Text(), nullable=True),
    sa.Column('approved_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('total_responses', sa.Integer(), nullable=True),
    sa.Column('completion_rate', sa.Float(), nullable=True),
    sa.Column('average_score', sa.Float(), nullable=True),
    sa.Column('average_time_minutes', sa.Float(), nullable=True),
    sa.Column('last_response_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], name=op.f('fk_questionnaires_created_by_users'), ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['reviewed_by'], ['users.id'], name=op.f('fk_questionnaires_reviewed_by_users'), ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['updated_by'], ['users.id'], name=op.f('fk_questionnaires_updated_by_users'), ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_questionnaires')),
    sa.UniqueConstraint('slug', name=op.f('uq_questionnaires_slug'))
    )
    op.create_index('idx_questionnaire_category', 'questionnaires', ['category'], unique=False)
    op.create_index('idx_questionnaire_created_at', 'questionnaires', ['created_at'], unique=False)
    op.create_index('idx_questionnaire_created_by', 'questionnaires', ['created_by'], unique=False)
    op.create_index('idx_questionnaire_status', 'questionnaires', ['status'], unique=False)
    
    # 8. Create tables that depend on candidates and other basic tables
    
    # Create evaluation_reports first (without the circular foreign key to questionnaire_responses)
    op.create_table('evaluation_reports',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('questionnaire_id', sa.BigInteger(), nullable=False),
    sa.Column('candidate_id', sa.BigInteger(), nullable=False),
    sa.Column('submission_id', sa.BigInteger(), nullable=True),  # Will be linked later
    sa.Column('dimension_scores', sa.JSON(), nullable=False),
    sa.Column('total_score', sa.Float(), nullable=False),
    sa.Column('percentile_rank', sa.Float(), nullable=True),
    sa.Column('overall_evaluation', sa.Text(), nullable=True),
    sa.Column('strengths', sa.JSON(), nullable=True),
    sa.Column('weaknesses', sa.JSON(), nullable=True),
    sa.Column('recommendations', sa.Text(), nullable=True),
    sa.Column('key_insights', sa.Text(), nullable=True),
    sa.Column('is_qualified', sa.Boolean(), nullable=True),
    sa.Column('match_score', sa.Float(), nullable=True),
    sa.Column('risk_level', sa.String(length=20), nullable=True),
    sa.Column('rank_in_batch', sa.BigInteger(), nullable=True),
    sa.Column('comparison_group', sa.String(length=100), nullable=True),
    sa.Column('evaluated_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('evaluation_model', sa.String(length=50), nullable=True),
    sa.Column('evaluation_version', sa.String(length=20), nullable=True),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], name=op.f('fk_evaluation_reports_candidate_id_candidates'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['questionnaire_id'], ['questionnaires.id'], name=op.f('fk_evaluation_reports_questionnaire_id_questionnaires'), ondelete='CASCADE'),
    # submission_id FK will be added later after questionnaire_responses is created
    sa.PrimaryKeyConstraint('id', name=op.f('pk_evaluation_reports'))
    )
    op.create_index('idx_evaluation_candidate_id', 'evaluation_reports', ['candidate_id'], unique=False)
    op.create_index('idx_evaluation_evaluated_at', 'evaluation_reports', ['evaluated_at'], unique=False)
    op.create_index('idx_evaluation_is_qualified', 'evaluation_reports', ['is_qualified'], unique=False)
    op.create_index('idx_evaluation_questionnaire_id', 'evaluation_reports', ['questionnaire_id'], unique=False)
    op.create_index('idx_evaluation_rank', 'evaluation_reports', ['questionnaire_id', 'total_score'], unique=False)
    op.create_index('idx_evaluation_total_score', 'evaluation_reports', ['total_score'], unique=False)
    
    # Create questionnaire_responses (references evaluation_reports)
    op.create_table('questionnaire_responses',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('questionnaire_id', sa.BigInteger(), nullable=False),
    sa.Column('session_id', sa.String(), nullable=True),
    sa.Column('candidate_id', sa.BigInteger(), nullable=True),
    sa.Column('candidate_name', sa.String(length=100), nullable=True),
    sa.Column('candidate_email', sa.String(length=255), nullable=True),
    sa.Column('candidate_phone', sa.String(length=20), nullable=True),
    sa.Column('candidate_position', sa.String(length=100), nullable=True),
    sa.Column('responses', sa.JSON(), nullable=True),
    sa.Column('response_times', sa.JSON(), nullable=True),
    sa.Column('submitted_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completion_time', sa.Integer(), nullable=True),
    sa.Column('current_page', sa.Integer(), nullable=True),
    sa.Column('is_complete', sa.Boolean(), nullable=True),
    sa.Column('last_saved_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('save_count', sa.Integer(), nullable=True),
    sa.Column('total_score', sa.Integer(), nullable=True),
    sa.Column('dimension_scores', sa.JSON(), nullable=True),
    sa.Column('ai_evaluation_id', sa.BigInteger(), nullable=True),
    sa.Column('is_qualified', sa.Boolean(), nullable=True),
    sa.Column('ip_address', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.Text(), nullable=True),
    sa.Column('browser_info', sa.JSON(), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['ai_evaluation_id'], ['evaluation_reports.id'], name=op.f('fk_questionnaire_responses_ai_evaluation_id_evaluation_reports')),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], name=op.f('fk_questionnaire_responses_candidate_id_candidates')),
    sa.ForeignKeyConstraint(['questionnaire_id'], ['questionnaires.id'], name=op.f('fk_questionnaire_responses_questionnaire_id_questionnaires')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_questionnaire_responses'))
    )
    op.create_index(op.f('ix_questionnaire_responses_candidate_email'), 'questionnaire_responses', ['candidate_email'], unique=False)
    op.create_index(op.f('ix_questionnaire_responses_session_id'), 'questionnaire_responses', ['session_id'], unique=False)
    
    # Now add the foreign key from evaluation_reports to questionnaire_responses
    op.create_foreign_key(
        op.f('fk_evaluation_reports_submission_id_questionnaire_responses'),
        'evaluation_reports', 
        'questionnaire_responses',
        ['submission_id'], 
        ['id'], 
        ondelete='CASCADE'
    )
    
    # Continue with other tables...
    op.create_table('application_forms',
    sa.Column('title', sa.String(length=200), nullable=False),
    sa.Column('slug', sa.String(length=100), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('fields', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('questionnaire_id', sa.BigInteger(), nullable=True),
    sa.Column('is_active', sa.Boolean(), nullable=True),
    sa.Column('requires_resume', sa.Boolean(), nullable=True),
    sa.Column('auto_approve', sa.Boolean(), nullable=True),
    sa.Column('created_by', sa.BigInteger(), nullable=False),
    sa.Column('submission_count', sa.Integer(), nullable=True),
    sa.Column('success_message', sa.Text(), nullable=True),
    sa.Column('redirect_url', sa.String(length=500), nullable=True),
    sa.Column('notify_on_submission', sa.Boolean(), nullable=True),
    sa.Column('notification_emails', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['created_by'], ['users.id'], name=op.f('fk_application_forms_created_by_users')),
    sa.ForeignKeyConstraint(['questionnaire_id'], ['questionnaires.id'], name=op.f('fk_application_forms_questionnaire_id_questionnaires')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_application_forms'))
    )
    op.create_index('idx_application_form_active', 'application_forms', ['is_active'], unique=False)
    op.create_index('idx_application_form_created_by', 'application_forms', ['created_by'], unique=False)
    op.create_index(op.f('ix_application_forms_is_active'), 'application_forms', ['is_active'], unique=False)
    op.create_index(op.f('ix_application_forms_slug'), 'application_forms', ['slug'], unique=True)
    
    op.create_table('candidate_assessments',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('candidate_id', sa.BigInteger(), nullable=False),
    sa.Column('digital_literacy_score', sa.Float(), nullable=False),
    sa.Column('industry_skills_score', sa.Float(), nullable=False),
    sa.Column('position_skills_score', sa.Float(), nullable=False),
    sa.Column('innovation_score', sa.Float(), nullable=False),
    sa.Column('learning_potential_score', sa.Float(), nullable=False),
    sa.Column('dci_score', sa.Float(), nullable=False),
    sa.Column('overall_percentile', sa.Float(), nullable=True),
    sa.Column('assessment_details', sa.JSON(), nullable=True),
    sa.Column('strengths', sa.JSON(), nullable=True),
    sa.Column('improvement_areas', sa.JSON(), nullable=True),
    sa.Column('recommendations', sa.JSON(), nullable=True),
    sa.Column('assessment_version', sa.String(length=50), nullable=True),
    sa.Column('assessed_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.CheckConstraint('dci_score >= 0 AND dci_score <= 100', name=op.f('ck_candidate_assessments_ck_dci_score_range')),
    sa.CheckConstraint('digital_literacy_score >= 0 AND digital_literacy_score <= 100', name=op.f('ck_candidate_assessments_ck_digital_literacy_range')),
    sa.CheckConstraint('industry_skills_score >= 0 AND industry_skills_score <= 100', name=op.f('ck_candidate_assessments_ck_industry_skills_range')),
    sa.CheckConstraint('innovation_score >= 0 AND innovation_score <= 100', name=op.f('ck_candidate_assessments_ck_innovation_range')),
    sa.CheckConstraint('learning_potential_score >= 0 AND learning_potential_score <= 100', name=op.f('ck_candidate_assessments_ck_learning_potential_range')),
    sa.CheckConstraint('overall_percentile IS NULL OR (overall_percentile >= 0 AND overall_percentile <= 100)', name=op.f('ck_candidate_assessments_ck_percentile_range')),
    sa.CheckConstraint('position_skills_score >= 0 AND position_skills_score <= 100', name=op.f('ck_candidate_assessments_ck_position_skills_range')),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], name=op.f('fk_candidate_assessments_candidate_id_candidates'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_candidate_assessments')),
    sa.UniqueConstraint('candidate_id', name='uq_assessments_candidate_id')
    )
    op.create_index(op.f('ix_candidate_assessments_assessed_at'), 'candidate_assessments', ['assessed_at'], unique=False)
    op.create_index(op.f('ix_candidate_assessments_candidate_id'), 'candidate_assessments', ['candidate_id'], unique=False)
    op.create_index(op.f('ix_candidate_assessments_dci_score'), 'candidate_assessments', ['dci_score'], unique=False)
    op.create_index(op.f('ix_candidate_assessments_overall_percentile'), 'candidate_assessments', ['overall_percentile'], unique=False)
    
    op.create_table('job_vectors',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('position_id', sa.BigInteger(), nullable=False),
    sa.Column('title_vector', pgvector.sqlalchemy.vector.VECTOR(dim=1024), nullable=True),
    sa.Column('requirements_vector', pgvector.sqlalchemy.vector.VECTOR(dim=1024), nullable=True),
    sa.Column('responsibilities_vector', pgvector.sqlalchemy.vector.VECTOR(dim=1024), nullable=True),
    sa.Column('full_text_vector', pgvector.sqlalchemy.vector.VECTOR(dim=1024), nullable=False),
    sa.Column('embedding_model', sa.String(length=50), nullable=True),
    sa.Column('embedding_provider', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['position_id'], ['positions.id'], name=op.f('fk_job_vectors_position_id_positions'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_job_vectors')),
    sa.UniqueConstraint('position_id', name='uq_job_vectors_position_id')
    )
    op.create_index(op.f('ix_job_vectors_created_at'), 'job_vectors', ['created_at'], unique=False)
    op.create_index(op.f('ix_job_vectors_position_id'), 'job_vectors', ['position_id'], unique=False)
    
    op.create_table('questionnaire_sections',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('questionnaire_id', sa.BigInteger(), nullable=False),
    sa.Column('title', sa.String(length=255), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.Column('is_visible', sa.Boolean(), nullable=True),
    sa.Column('is_collapsible', sa.Boolean(), nullable=True),
    sa.Column('is_collapsed_by_default', sa.Boolean(), nullable=True),
    sa.Column('show_if_conditions', sa.JSON(), nullable=True),
    sa.ForeignKeyConstraint(['questionnaire_id'], ['questionnaires.id'], name=op.f('fk_questionnaire_sections_questionnaire_id_questionnaires'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_questionnaire_sections')),
    sa.UniqueConstraint('questionnaire_id', 'order', name='uq_section_order')
    )
    
    op.create_table('resume_files',
    sa.Column('candidate_id', sa.BigInteger(), nullable=False),
    sa.Column('file_key', sa.String(length=255), nullable=False, comment='MinIO对象键'),
    sa.Column('filename', sa.String(length=255), nullable=False, comment='原始文件名'),
    sa.Column('file_type', sa.String(length=50), nullable=False, comment='文件类型(pdf/docx/txt等)'),
    sa.Column('file_size', sa.Integer(), nullable=False, comment='文件大小(bytes)'),
    sa.Column('version', sa.Integer(), nullable=False, comment='版本号'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否为当前活跃版本'),
    sa.Column('upload_method', sa.String(length=50), nullable=True, comment='上传方式(manual/email/api)'),
    sa.Column('parsed_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='结构化解析数据'),
    sa.Column('raw_text', sa.Text(), nullable=True, comment='提取的原始文本'),
    sa.Column('parsing_confidence', sa.Float(), nullable=True, comment='解析置信度(0-1)'),
    sa.Column('parsing_status', sa.String(length=50), nullable=True, comment='解析状态(pending/processing/completed/failed)'),
    sa.Column('parsed_at', sa.DateTime(timezone=True), nullable=True, comment='解析完成时间'),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], name=op.f('fk_resume_files_candidate_id_candidates'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_resume_files')),
    sa.UniqueConstraint('candidate_id', 'version', name='uq_resume_files_candidate_version')
    )
    op.create_index('idx_resume_files_candidate_active', 'resume_files', ['candidate_id', 'is_active'], unique=False)
    op.create_index('idx_resume_files_candidate_version', 'resume_files', ['candidate_id', 'version'], unique=False)
    op.create_index('idx_resume_files_parsing_status', 'resume_files', ['parsing_status'], unique=False)
    
    op.create_table('resume_vectors',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('candidate_id', sa.BigInteger(), nullable=False),
    sa.Column('full_vector', pgvector.sqlalchemy.vector.VECTOR(dim=1024), nullable=False),
    sa.Column('experience_vector', pgvector.sqlalchemy.vector.VECTOR(dim=1024), nullable=True),
    sa.Column('education_vector', pgvector.sqlalchemy.vector.VECTOR(dim=1024), nullable=True),
    sa.Column('skills_vector', pgvector.sqlalchemy.vector.VECTOR(dim=1024), nullable=True),
    sa.Column('parsed_data', sa.JSON(), nullable=False),
    sa.Column('parser_confidence', sa.Float(), nullable=True),
    sa.Column('embedding_model', sa.String(length=50), nullable=True),
    sa.Column('embedding_provider', sa.String(length=20), nullable=True),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
    sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], name=op.f('fk_resume_vectors_candidate_id_candidates'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_resume_vectors')),
    sa.UniqueConstraint('candidate_id', name='uq_resume_vectors_candidate_id')
    )
    op.create_index(op.f('ix_resume_vectors_candidate_id'), 'resume_vectors', ['candidate_id'], unique=False)
    
    op.create_table('application_submissions',
    sa.Column('form_id', sa.BigInteger(), nullable=False),
    sa.Column('candidate_id', sa.BigInteger(), nullable=True),
    sa.Column('form_data', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('resume_file_url', sa.String(length=500), nullable=True),
    sa.Column('resume_parsed_data', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('submitted_at', sa.DateTime(timezone=True), nullable=False),
    sa.Column('submission_ip', sa.String(length=45), nullable=True),
    sa.Column('user_agent', sa.String(length=500), nullable=True),
    sa.Column('is_draft', sa.Boolean(), nullable=True),
    sa.Column('draft_saved_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('validation_passed', sa.Boolean(), nullable=True),
    sa.Column('validation_errors', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('confirmation_email_sent', sa.Boolean(), nullable=True),
    sa.Column('confirmation_email_sent_at', sa.DateTime(timezone=True), nullable=True),
    sa.Column('referrer_url', sa.String(length=500), nullable=True),
    sa.Column('utm_source', sa.String(length=100), nullable=True),
    sa.Column('utm_medium', sa.String(length=100), nullable=True),
    sa.Column('utm_campaign', sa.String(length=100), nullable=True),
    sa.Column('captcha_token', sa.String(length=500), nullable=True),
    sa.Column('captcha_verified', sa.Boolean(), nullable=True),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['candidate_id'], ['candidates.id'], name=op.f('fk_application_submissions_candidate_id_candidates')),
    sa.ForeignKeyConstraint(['form_id'], ['application_forms.id'], name=op.f('fk_application_submissions_form_id_application_forms')),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_application_submissions')),
    sa.UniqueConstraint('candidate_id', name=op.f('uq_application_submissions_candidate_id'))
    )
    op.create_index('idx_submission_candidate', 'application_submissions', ['candidate_id'], unique=False)
    op.create_index('idx_submission_draft', 'application_submissions', ['is_draft'], unique=False)
    op.create_index('idx_submission_form', 'application_submissions', ['form_id'], unique=False)
    op.create_index('idx_submission_ip', 'application_submissions', ['submission_ip'], unique=False)
    op.create_index('idx_submission_submitted_at', 'application_submissions', ['submitted_at'], unique=False)
    op.create_index(op.f('ix_application_submissions_submitted_at'), 'application_submissions', ['submitted_at'], unique=False)
    
    op.create_table('questions',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('questionnaire_id', sa.BigInteger(), nullable=False),
    sa.Column('section_id', sa.BigInteger(), nullable=True),
    sa.Column('type', sa.Enum('SINGLE_CHOICE', 'MULTIPLE_CHOICE', 'TEXT_INPUT', 'RATING_SCALE', 'MATRIX', 'RANKING', 'DATE', 'FILE_UPLOAD', name='questiontype'), nullable=False),
    sa.Column('title', sa.Text(), nullable=False),
    sa.Column('description', sa.Text(), nullable=True),
    sa.Column('help_text', sa.Text(), nullable=True),
    sa.Column('order', sa.Integer(), nullable=False),
    sa.Column('is_required', sa.Boolean(), nullable=True),
    sa.Column('layout', sa.String(length=20), nullable=True),
    sa.Column('media_url', sa.String(length=500), nullable=True),
    sa.Column('media_type', sa.String(length=20), nullable=True),
    sa.Column('randomize_options', sa.Boolean(), nullable=True),
    sa.Column('scoring_enabled', sa.Boolean(), nullable=True),
    sa.Column('max_score', sa.Float(), nullable=True),
    sa.Column('weight', sa.Float(), nullable=True),
    sa.Column('scoring_type', sa.String(length=20), nullable=True),
    sa.Column('custom_scoring_formula', sa.Text(), nullable=True),
    sa.Column('options', sa.JSON(), nullable=True),
    sa.Column('validation', sa.JSON(), nullable=True),
    sa.Column('logic', sa.JSON(), nullable=True),
    sa.Column('response_count', sa.Integer(), nullable=True),
    sa.Column('average_score', sa.Float(), nullable=True),
    sa.Column('correct_percentage', sa.Float(), nullable=True),
    sa.CheckConstraint('max_score >= 0', name=op.f('ck_questions_check_max_score_positive')),
    sa.CheckConstraint('weight >= 0', name=op.f('ck_questions_check_weight_positive')),
    sa.ForeignKeyConstraint(['questionnaire_id'], ['questionnaires.id'], name=op.f('fk_questions_questionnaire_id_questionnaires'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['section_id'], ['questionnaire_sections.id'], name=op.f('fk_questions_section_id_questionnaire_sections'), ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_questions')),
    sa.UniqueConstraint('questionnaire_id', 'order', name='uq_question_order')
    )
    
    op.create_table('resume_parse_queue',
    sa.Column('resume_file_id', sa.BigInteger(), nullable=False),
    sa.Column('priority', sa.Integer(), nullable=True, comment='优先级(1-10, 数字越小优先级越高)'),
    sa.Column('status', sa.String(length=50), nullable=True, comment='状态(pending/processing/completed/failed)'),
    sa.Column('attempts', sa.Integer(), nullable=True, comment='尝试次数'),
    sa.Column('last_error', sa.Text(), nullable=True, comment='最后一次错误信息'),
    sa.Column('started_at', sa.DateTime(timezone=True), nullable=True, comment='开始处理时间'),
    sa.Column('completed_at', sa.DateTime(timezone=True), nullable=True, comment='完成时间'),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['resume_file_id'], ['resume_files.id'], name=op.f('fk_resume_parse_queue_resume_file_id_resume_files'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_resume_parse_queue'))
    )
    op.create_index('idx_resume_parse_queue_resume_file', 'resume_parse_queue', ['resume_file_id'], unique=False)
    op.create_index('idx_resume_parse_queue_status_priority', 'resume_parse_queue', ['status', 'priority'], unique=False)
    
    op.create_table('resume_version_logs',
    sa.Column('resume_file_id', sa.BigInteger(), nullable=False),
    sa.Column('action', sa.String(length=50), nullable=False, comment='操作类型(upload/parse/activate/delete)'),
    sa.Column('details', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='操作详情'),
    sa.Column('performed_by', sa.BigInteger(), nullable=True, comment='操作用户ID'),
    sa.Column('performed_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False, comment='操作时间'),
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=False),
    sa.ForeignKeyConstraint(['performed_by'], ['users.id'], name=op.f('fk_resume_version_logs_performed_by_users'), ondelete='SET NULL'),
    sa.ForeignKeyConstraint(['resume_file_id'], ['resume_files.id'], name=op.f('fk_resume_version_logs_resume_file_id_resume_files'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_resume_version_logs'))
    )
    op.create_index('idx_resume_version_logs_action', 'resume_version_logs', ['action'], unique=False)
    op.create_index('idx_resume_version_logs_performed_at', 'resume_version_logs', ['performed_at'], unique=False)
    op.create_index('idx_resume_version_logs_resume_file', 'resume_version_logs', ['resume_file_id'], unique=False)
    
    op.create_table('answers',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('response_id', sa.BigInteger(), nullable=False),
    sa.Column('question_id', sa.BigInteger(), nullable=False),
    sa.Column('text_value', sa.Text(), nullable=True),
    sa.Column('selected_option_ids', sa.JSON(), nullable=True),
    sa.Column('rating_value', sa.Integer(), nullable=True),
    sa.Column('ranking_order', sa.JSON(), nullable=True),
    sa.Column('matrix_responses', sa.JSON(), nullable=True),
    sa.Column('date_value', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('file_url', sa.String(length=500), nullable=True),
    sa.Column('answered_at', sa.TIMESTAMP(timezone=True), nullable=True),
    sa.Column('time_spent_seconds', sa.Integer(), nullable=True),
    sa.Column('score', sa.Float(), nullable=True),
    sa.Column('max_score', sa.Float(), nullable=True),
    sa.Column('is_correct', sa.Boolean(), nullable=True),
    sa.Column('feedback', sa.Text(), nullable=True),
    sa.ForeignKeyConstraint(['question_id'], ['questions.id'], name=op.f('fk_answers_question_id_questions'), ondelete='CASCADE'),
    sa.ForeignKeyConstraint(['response_id'], ['questionnaire_responses.id'], name=op.f('fk_answers_response_id_questionnaire_responses'), ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id', name=op.f('pk_answers')),
    sa.UniqueConstraint('response_id', 'question_id', name='uq_response_question')
    )
    op.create_index('idx_answer_question', 'answers', ['question_id'], unique=False)
    op.create_index('idx_answer_response', 'answers', ['response_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Drop in reverse order of creation
    op.drop_index('idx_answer_response', table_name='answers')
    op.drop_index('idx_answer_question', table_name='answers')
    op.drop_table('answers')
    op.drop_index('idx_resume_version_logs_resume_file', table_name='resume_version_logs')
    op.drop_index('idx_resume_version_logs_performed_at', table_name='resume_version_logs')
    op.drop_index('idx_resume_version_logs_action', table_name='resume_version_logs')
    op.drop_table('resume_version_logs')
    op.drop_index('idx_resume_parse_queue_status_priority', table_name='resume_parse_queue')
    op.drop_index('idx_resume_parse_queue_resume_file', table_name='resume_parse_queue')
    op.drop_table('resume_parse_queue')
    op.drop_table('questions')
    op.drop_index(op.f('ix_application_submissions_submitted_at'), table_name='application_submissions')
    op.drop_index('idx_submission_submitted_at', table_name='application_submissions')
    op.drop_index('idx_submission_ip', table_name='application_submissions')
    op.drop_index('idx_submission_form', table_name='application_submissions')
    op.drop_index('idx_submission_draft', table_name='application_submissions')
    op.drop_index('idx_submission_candidate', table_name='application_submissions')
    op.drop_table('application_submissions')
    op.drop_index(op.f('ix_resume_vectors_candidate_id'), table_name='resume_vectors')
    op.drop_table('resume_vectors')
    op.drop_index('idx_resume_files_parsing_status', table_name='resume_files')
    op.drop_index('idx_resume_files_candidate_version', table_name='resume_files')
    op.drop_index('idx_resume_files_candidate_active', table_name='resume_files')
    op.drop_table('resume_files')
    op.drop_table('questionnaire_sections')
    op.drop_index(op.f('ix_job_vectors_position_id'), table_name='job_vectors')
    op.drop_index(op.f('ix_job_vectors_created_at'), table_name='job_vectors')
    op.drop_table('job_vectors')
    op.drop_index(op.f('ix_candidate_assessments_overall_percentile'), table_name='candidate_assessments')
    op.drop_index(op.f('ix_candidate_assessments_dci_score'), table_name='candidate_assessments')
    op.drop_index(op.f('ix_candidate_assessments_candidate_id'), table_name='candidate_assessments')
    op.drop_index(op.f('ix_candidate_assessments_assessed_at'), table_name='candidate_assessments')
    op.drop_table('candidate_assessments')
    op.drop_index(op.f('ix_application_forms_slug'), table_name='application_forms')
    op.drop_index(op.f('ix_application_forms_is_active'), table_name='application_forms')
    op.drop_index('idx_application_form_created_by', table_name='application_forms')
    op.drop_index('idx_application_form_active', table_name='application_forms')
    op.drop_table('application_forms')
    
    # Drop the foreign key from evaluation_reports to questionnaire_responses first
    op.drop_constraint(op.f('fk_evaluation_reports_submission_id_questionnaire_responses'), 'evaluation_reports', type_='foreignkey')
    
    op.drop_index(op.f('ix_questionnaire_responses_session_id'), table_name='questionnaire_responses')
    op.drop_index(op.f('ix_questionnaire_responses_candidate_email'), table_name='questionnaire_responses')
    op.drop_table('questionnaire_responses')
    
    op.drop_index('idx_evaluation_total_score', table_name='evaluation_reports')
    op.drop_index('idx_evaluation_rank', table_name='evaluation_reports')
    op.drop_index('idx_evaluation_questionnaire_id', table_name='evaluation_reports')
    op.drop_index('idx_evaluation_is_qualified', table_name='evaluation_reports')
    op.drop_index('idx_evaluation_evaluated_at', table_name='evaluation_reports')
    op.drop_index('idx_evaluation_candidate_id', table_name='evaluation_reports')
    op.drop_table('evaluation_reports')
    
    op.drop_index('idx_questionnaire_status', table_name='questionnaires')
    op.drop_index('idx_questionnaire_created_by', table_name='questionnaires')
    op.drop_index('idx_questionnaire_created_at', table_name='questionnaires')
    op.drop_index('idx_questionnaire_category', table_name='questionnaires')
    op.drop_table('questionnaires')
    
    op.drop_index(op.f('ix_positions_title'), table_name='positions')
    op.drop_index(op.f('ix_positions_department'), table_name='positions')
    # op.drop_index('idx_position_title', table_name='positions', postgresql_using='gin')
    op.drop_index('idx_position_status', table_name='positions')
    op.drop_index('idx_position_required_skills', table_name='positions', postgresql_using='gin')
    op.drop_index('idx_position_permission', table_name='positions')
    op.drop_index('idx_position_department', table_name='positions')
    op.drop_index('idx_position_deleted', table_name='positions')
    op.drop_index('idx_position_created_by', table_name='positions')
    op.drop_table('positions')
    
    op.drop_index(op.f('ix_candidates_phone'), table_name='candidates')
    op.drop_index(op.f('ix_candidates_name'), table_name='candidates')
    op.drop_index(op.f('ix_candidates_email'), table_name='candidates')
    op.drop_index('idx_candidate_status_active', table_name='candidates')
    op.drop_index('idx_candidate_status', table_name='candidates')
    op.drop_index('idx_candidate_source', table_name='candidates')
    op.drop_index('idx_candidate_skills', table_name='candidates', postgresql_using='gin')
    op.drop_index('idx_candidate_salary_range', table_name='candidates')
    op.drop_index('idx_candidate_reviewed_by', table_name='candidates')
    op.drop_index('idx_candidate_permission', table_name='candidates')
    # op.drop_index('idx_candidate_name_search', table_name='candidates', postgresql_using='gin')
    op.drop_index('idx_candidate_interview_count', table_name='candidates')
    op.drop_index('idx_candidate_experience_years', table_name='candidates')
    op.drop_index('idx_candidate_email_phone', table_name='candidates')
    op.drop_index('idx_candidate_education_level', table_name='candidates')
    op.drop_index('idx_candidate_deleted', table_name='candidates')
    op.drop_index('idx_candidate_created_by', table_name='candidates')
    op.drop_index('idx_candidate_approval_status', table_name='candidates')
    op.drop_table('candidates')
    
    op.drop_index(op.f('ix_user_preferences_id'), table_name='user_preferences')
    op.drop_table('user_preferences')
    
    op.drop_index(op.f('ix_users_username'), table_name='users')
    op.drop_index(op.f('ix_users_email'), table_name='users')
    op.drop_table('users')
    
    op.drop_table('role_permissions')
    
    op.drop_index(op.f('ix_roles_code'), table_name='roles')
    op.drop_table('roles')
    
    op.drop_index(op.f('ix_permissions_module'), table_name='permissions')
    op.drop_index(op.f('ix_permissions_code'), table_name='permissions')
    op.drop_table('permissions')
    # ### end Alembic commands ###