#!/usr/bin/env python3
"""
Create admin user script for TalentForge Pro
This script creates an admin user with full permissions for system management
"""
import asyncio
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import AsyncSessionLocal, engine
from app.core.security import get_password_hash
from app.crud.user import user as user_crud
from app.models.user import User
from app.core.enums import UserRole
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_admin_user(
    email: str = "<EMAIL>",
    username: str = "admin",
    password: str = "Admin123!@#",
    full_name: str = "System Administrator"
):
    """Create an admin user with full permissions"""
    
    async with AsyncSessionLocal() as db:
        try:
            # Check if admin already exists
            existing_user = await user_crud.get_by_email(db, email=email)
            if existing_user:
                logger.info(f"Admin user already exists: {email}")
                
                # Update to admin role if not already
                if existing_user.role != UserRole.SUPER_ADMIN.value:
                    existing_user.role = UserRole.SUPER_ADMIN.value
                    existing_user.is_superuser = True
                    await db.commit()
                    logger.info(f"Updated user {email} to SUPER_ADMIN role")
                return existing_user
            
            # Create new admin user
            admin_user = User(
                email=email,
                username=username,
                hashed_password=get_password_hash(password),
                full_name=full_name,
                role=UserRole.SUPER_ADMIN.value,
                is_active=True,
                is_superuser=True,
                is_verified=True
            )
            
            db.add(admin_user)
            await db.commit()
            await db.refresh(admin_user)
            
            logger.info(f"✅ Admin user created successfully!")
            logger.info(f"   Email: {email}")
            logger.info(f"   Username: {username}")
            logger.info(f"   Password: {password}")
            logger.info(f"   Role: SUPER_ADMIN")
            logger.info(f"   Permissions: ALL")
            
            return admin_user
            
        except Exception as e:
            logger.error(f"Error creating admin user: {e}")
            await db.rollback()
            raise


async def create_test_users():
    """Create additional test users with different roles"""
    
    test_users = [
        {
            "email": "<EMAIL>",
            "username": "hr_manager",
            "password": "Test123!@#",
            "full_name": "HR Manager",
            "role": UserRole.HR_MANAGER.value
        },
        {
            "email": "<EMAIL>",
            "username": "hr_specialist",
            "password": "Test123!@#",
            "full_name": "HR Specialist",
            "role": UserRole.HR_SPECIALIST.value
        },
        {
            "email": "<EMAIL>",
            "username": "interviewer",
            "password": "Test123!@#",
            "full_name": "Interviewer",
            "role": UserRole.INTERVIEWER.value
        }
    ]
    
    async with AsyncSessionLocal() as db:
        for user_data in test_users:
            try:
                # Check if user already exists
                existing_user = await user_crud.get_by_email(db, email=user_data["email"])
                if existing_user:
                    logger.info(f"User already exists: {user_data['email']}")
                    continue
                
                # Create new user
                new_user = User(
                    email=user_data["email"],
                    username=user_data["username"],
                    hashed_password=get_password_hash(user_data["password"]),
                    full_name=user_data["full_name"],
                    role=user_data["role"],
                    is_active=True,
                    is_verified=True
                )
                
                db.add(new_user)
                await db.commit()
                
                logger.info(f"✅ Created test user: {user_data['email']} with role {user_data['role']}")
                
            except Exception as e:
                logger.error(f"Error creating test user {user_data['email']}: {e}")
                await db.rollback()


async def main():
    """Main function"""
    logger.info("🚀 Starting admin user creation...")
    
    # Create admin user
    await create_admin_user()
    
    # Optionally create test users
    create_test = input("\nDo you want to create test users with different roles? (y/n): ")
    if create_test.lower() == 'y':
        await create_test_users()
    
    logger.info("\n✅ All done! You can now login with the admin credentials.")


if __name__ == "__main__":
    asyncio.run(main())