#!/usr/bin/env python3
"""
Celery Worker with Hot Reload for Development
使用watchdog监控文件变化，自动重启Celery Worker
"""
import os
import sys
import subprocess
import signal
import time
import logging
from pathlib import Path
from typing import Optional

from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler, FileModifiedEvent, FileCreatedEvent


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('celery-dev')


class CeleryReloadHandler(FileSystemEventHandler):
    """处理文件变化事件，重启Celery Worker"""
    
    def __init__(self, restart_callback):
        self.restart_callback = restart_callback
        self.last_restart = 0
        self.debounce_seconds = 2  # 防抖：2秒内的多次变化只触发一次重启
        
    def should_restart(self, event) -> bool:
        """判断是否应该重启Celery"""
        # 只监控Python文件
        if not event.src_path.endswith('.py'):
            return False
            
        # 忽略__pycache__和.pyc文件
        if '__pycache__' in event.src_path or event.src_path.endswith('.pyc'):
            return False
            
        # 防抖处理
        current_time = time.time()
        if current_time - self.last_restart < self.debounce_seconds:
            return False
            
        return True
    
    def on_modified(self, event):
        if isinstance(event, FileModifiedEvent) and self.should_restart(event):
            logger.info(f"检测到文件变化: {event.src_path}")
            self.last_restart = time.time()
            self.restart_callback()
    
    def on_created(self, event):
        if isinstance(event, FileCreatedEvent) and self.should_restart(event):
            logger.info(f"检测到新文件: {event.src_path}")
            self.last_restart = time.time()
            self.restart_callback()


class CeleryDevServer:
    """Celery开发服务器，支持热重载"""
    
    def __init__(self, app_module: str = "app.worker", watch_dirs: list = None):
        self.app_module = app_module
        self.watch_dirs = watch_dirs or ["/app"]
        self.celery_process: Optional[subprocess.Popen] = None
        self.observer: Optional[Observer] = None
        self.running = False
        
    def start_celery(self):
        """启动Celery Worker进程"""
        if self.celery_process:
            self.stop_celery()
            
        logger.info("🚀 启动Celery Worker...")
        
        # 构建启动命令
        cmd = [
            "celery", "-A", self.app_module, "worker",
            "--loglevel=info",
            "--concurrency=1",  # 开发环境使用单进程
            "--pool=solo"  # 使用solo pool，更适合开发调试
        ]
        
        try:
            self.celery_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                preexec_fn=os.setsid  # 创建新的进程组
            )
            
            logger.info(f"✅ Celery Worker启动成功 (PID: {self.celery_process.pid})")
            
            # 启动日志转发线程
            self._forward_logs()
            
        except Exception as e:
            logger.error(f"❌ 启动Celery Worker失败: {e}")
            
    def stop_celery(self):
        """优雅停止Celery Worker进程"""
        if not self.celery_process:
            return
            
        logger.info("🛑 停止Celery Worker...")
        
        try:
            # 发送SIGTERM信号进行优雅关闭
            os.killpg(os.getpgid(self.celery_process.pid), signal.SIGTERM)
            
            # 等待进程结束
            try:
                self.celery_process.wait(timeout=10)
                logger.info("✅ Celery Worker已优雅停止")
            except subprocess.TimeoutExpired:
                # 如果10秒后还没结束，强制杀死
                logger.warning("⚠️ 优雅停止超时，强制终止进程")
                os.killpg(os.getpgid(self.celery_process.pid), signal.SIGKILL)
                self.celery_process.wait()
                
        except ProcessLookupError:
            # 进程已经不存在
            pass
        except Exception as e:
            logger.error(f"停止Celery Worker时出错: {e}")
        finally:
            self.celery_process = None
    
    def restart_celery(self):
        """重启Celery Worker"""
        logger.info("🔄 重启Celery Worker...")
        self.stop_celery()
        # 稍微等待一下，确保进程完全停止
        time.sleep(1)
        self.start_celery()
    
    def _forward_logs(self):
        """转发Celery日志到控制台"""
        def forward():
            if not self.celery_process:
                return
                
            try:
                for line in iter(self.celery_process.stdout.readline, ''):
                    if line.strip():
                        print(f"[CELERY] {line.rstrip()}")
            except Exception:
                pass
        
        import threading
        threading.Thread(target=forward, daemon=True).start()
    
    def start_file_watcher(self):
        """启动文件监控"""
        logger.info("👀 启动文件监控...")
        
        self.observer = Observer()
        handler = CeleryReloadHandler(self.restart_celery)
        
        for watch_dir in self.watch_dirs:
            if os.path.exists(watch_dir):
                self.observer.schedule(handler, watch_dir, recursive=True)
                logger.info(f"📁 监控目录: {watch_dir}")
            else:
                logger.warning(f"⚠️ 监控目录不存在: {watch_dir}")
        
        self.observer.start()
        logger.info("✅ 文件监控已启动")
    
    def stop_file_watcher(self):
        """停止文件监控"""
        if self.observer:
            logger.info("🛑 停止文件监控...")
            self.observer.stop()
            self.observer.join()
            self.observer = None
    
    def run(self):
        """运行开发服务器"""
        logger.info("🎯 Celery开发服务器启动")
        logger.info("📝 监控Python文件变化，自动重启Worker")
        logger.info("💡 按Ctrl+C停止服务")
        
        self.running = True
        
        # 注册信号处理
        def signal_handler(signum, frame):
            logger.info("\n🛑 收到停止信号，正在关闭...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # 启动文件监控
            self.start_file_watcher()
            
            # 启动Celery
            self.start_celery()
            
            # 保持运行
            while self.running:
                time.sleep(1)
                
                # 检查Celery进程是否意外退出
                if self.celery_process and self.celery_process.poll() is not None:
                    logger.error("❌ Celery Worker进程意外退出，重新启动...")
                    self.start_celery()
                    
        except KeyboardInterrupt:
            pass
        finally:
            self.shutdown()
    
    def shutdown(self):
        """关闭开发服务器"""
        if not self.running:
            return
            
        self.running = False
        logger.info("🧹 清理资源...")
        
        self.stop_file_watcher()
        self.stop_celery()
        
        logger.info("✅ Celery开发服务器已关闭")


def main():
    """主入口函数"""
    # 从环境变量获取配置
    app_module = os.getenv("CELERY_APP", "app.worker")
    watch_dirs = os.getenv("CELERY_WATCH_DIRS", "/app").split(",")
    
    logger.info(f"🔧 配置信息:")
    logger.info(f"   App模块: {app_module}")
    logger.info(f"   监控目录: {watch_dirs}")
    
    # 启动开发服务器
    server = CeleryDevServer(app_module=app_module, watch_dirs=watch_dirs)
    server.run()


if __name__ == "__main__":
    main()