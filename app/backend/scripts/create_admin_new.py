#!/usr/bin/env python3
"""
Create admin user script for TalentForge Pro
This script creates an admin user with full permissions for system management

Admin user specification from tech spec:
- Email: <EMAIL>  
- Username: admin
- Password: test123
- Full name: System Administrator
- Role: SUPER_ADMIN
- Active, Superuser, Verified: True
"""
import asyncio
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import AsyncSessionLocal, engine
from app.core.security import get_password_hash
from app.crud.user import user as user_crud
from app.models.user import User
from app.core.enums import UserRole
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def create_admin_user(
    email: str = "<EMAIL>",
    username: str = "admin", 
    password: str = "test123",
    full_name: str = "System Administrator"
):
    """
    Create admin user as specified in technical specification
    
    Args:
        email: Admin email address
        username: Admin username
        password: Admin password (default matches spec)
        full_name: Admin display name
        
    Returns:
        Created or existing User object
        
    Raises:
        Exception: If database operation fails
    """
    
    async with AsyncSessionLocal() as db:
        try:
            # Check if admin already exists by email
            existing_user = await user_crud.get_by_email(db, email=email)
            if existing_user:
                logger.info(f"✅ Admin user already exists: {email}")
                logger.info(f"   ID: {existing_user.id}")
                logger.info(f"   Username: {existing_user.username}")
                logger.info(f"   Role: {existing_user.role}")
                logger.info(f"   Is Superuser: {existing_user.is_superuser}")
                logger.info(f"   Is Active: {existing_user.is_active}")
                logger.info(f"   Is Verified: {existing_user.is_verified}")
                
                # Update to admin role if not already
                if existing_user.role != UserRole.SUPER_ADMIN.value or not existing_user.is_superuser:
                    existing_user.role = UserRole.SUPER_ADMIN.value
                    existing_user.is_superuser = True
                    existing_user.is_verified = True
                    existing_user.is_active = True
                    await db.commit()
                    await db.refresh(existing_user)
                    logger.info(f"🔧 Updated user {email} to SUPER_ADMIN role with all privileges")
                
                return existing_user
            
            # Check if username already exists
            existing_username = await user_crud.get_by_username(db, username=username)
            if existing_username and existing_username.email != email:
                logger.warning(f"⚠️  Username '{username}' already exists for different email: {existing_username.email}")
                logger.info(f"   Using email as fallback username: {email.split('@')[0]}")
                username = email.split('@')[0]  # Use email prefix as username
            
            # Create new admin user according to spec
            admin_user = User(
                email=email,
                username=username,
                hashed_password=get_password_hash(password),
                full_name=full_name,
                role=UserRole.SUPER_ADMIN.value,
                is_active=True,
                is_superuser=True,
                is_verified=True
            )
            
            db.add(admin_user)
            await db.commit()
            await db.refresh(admin_user)
            
            logger.info(f"✅ Admin user created successfully!")
            logger.info(f"   ID: {admin_user.id}")
            logger.info(f"   Email: {email}")
            logger.info(f"   Username: {username}")
            logger.info(f"   Password: {password}")
            logger.info(f"   Full Name: {full_name}")
            logger.info(f"   Role: SUPER_ADMIN")
            logger.info(f"   Is Superuser: True")
            logger.info(f"   Is Active: True")
            logger.info(f"   Is Verified: True")
            logger.info(f"   Permissions: {admin_user.permissions}")
            
            return admin_user
            
        except Exception as e:
            logger.error(f"❌ Error creating admin user: {e}")
            await db.rollback()
            raise


async def verify_admin_user(email: str = "<EMAIL>"):
    """
    Verify admin user exists and has correct privileges
    
    Args:
        email: Admin email to verify
        
    Returns:
        bool: True if admin user is properly configured
    """
    async with AsyncSessionLocal() as db:
        try:
            user = await user_crud.get_by_email(db, email=email)
            if not user:
                logger.error(f"❌ Admin user not found: {email}")
                return False
            
            # Check all required properties
            checks = [
                ("is_active", user.is_active, True),
                ("is_superuser", user.is_superuser, True), 
                ("is_verified", user.is_verified, True),
                ("role", user.role, UserRole.SUPER_ADMIN.value)
            ]
            
            all_passed = True
            for check_name, actual, expected in checks:
                if actual != expected:
                    logger.error(f"❌ {check_name}: expected {expected}, got {actual}")
                    all_passed = False
                else:
                    logger.info(f"✅ {check_name}: {actual}")
            
            if all_passed:
                logger.info(f"✅ Admin user verification passed: {email}")
                logger.info(f"   User has {len(user.permissions)} permissions")
            else:
                logger.error(f"❌ Admin user verification failed: {email}")
            
            return all_passed
            
        except Exception as e:
            logger.error(f"❌ Error verifying admin user: {e}")
            return False


async def create_test_users():
    """Create additional test users with different roles"""
    
    test_users = [
        {
            "email": "<EMAIL>",
            "username": "hr_manager",
            "password": "Test123!@#",
            "full_name": "HR Manager",
            "role": UserRole.HR_MANAGER.value
        },
        {
            "email": "<EMAIL>",
            "username": "hr_specialist",
            "password": "Test123!@#",
            "full_name": "HR Specialist",
            "role": UserRole.HR_SPECIALIST.value
        },
        {
            "email": "<EMAIL>",
            "username": "interviewer",
            "password": "Test123!@#",
            "full_name": "Interviewer",
            "role": UserRole.INTERVIEWER.value
        }
    ]
    
    async with AsyncSessionLocal() as db:
        for user_data in test_users:
            try:
                # Check if user already exists
                existing_user = await user_crud.get_by_email(db, email=user_data["email"])
                if existing_user:
                    logger.info(f"User already exists: {user_data['email']}")
                    continue
                
                # Create new user
                new_user = User(
                    email=user_data["email"],
                    username=user_data["username"],
                    hashed_password=get_password_hash(user_data["password"]),
                    full_name=user_data["full_name"],
                    role=user_data["role"],
                    is_active=True,
                    is_verified=True
                )
                
                db.add(new_user)
                await db.commit()
                
                logger.info(f"✅ Created test user: {user_data['email']} with role {user_data['role']}")
                
            except Exception as e:
                logger.error(f"Error creating test user {user_data['email']}: {e}")
                await db.rollback()


async def main():
    """
    Main function for admin user creation and verification
    
    This script ensures the admin user exists with correct credentials
    as specified in the technical specification.
    """
    logger.info("🚀 TalentForge Pro - Admin User Creation & Verification")
    logger.info("=" * 60)
    
    try:
        # Step 1: Create admin user
        logger.info("Step 1: Creating admin user...")
        admin_user = await create_admin_user()
        
        # Step 2: Verify admin user configuration
        logger.info("\nStep 2: Verifying admin user configuration...")
        verification_passed = await verify_admin_user()
        
        if verification_passed:
            logger.info("\n✅ ADMIN USER READY FOR USE")
            logger.info("=" * 60)
            logger.info("Login Credentials:")
            logger.info("  Email: <EMAIL>")
            logger.info("  Password: test123")
            logger.info("  Development Token: dev_bypass_token_2025_talentforge")
            logger.info("=" * 60)
            
            # Optionally create test users
            create_test = input("\nDo you want to create additional test users with different roles? (y/n): ")
            if create_test.lower() == 'y':
                logger.info("\nStep 3: Creating test users...")
                await create_test_users()
        else:
            logger.error("\n❌ ADMIN USER VERIFICATION FAILED")
            logger.error("Please check the logs above for specific issues.")
            return False
            
    except Exception as e:
        logger.error(f"❌ Fatal error in main: {e}")
        return False
    
    logger.info("\n✅ All operations completed successfully!")
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)