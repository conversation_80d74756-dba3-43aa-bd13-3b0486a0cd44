#!/usr/bin/env python3
"""
Script to seed mock position data into the database
"""
import asyncio
import random
from datetime import datetime, timedelta, timezone
from typing import List

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import AsyncSessionLocal
from app.models.position import Position, PositionStatus, PositionUrgency
from app.core.enums import DataPermission
from app.models.user import User


# Job types
JOB_TYPES = ["full-time", "part-time", "contract", "internship"]

# Mock data templates
DEPARTMENTS = [
    "Engineering", "Product", "Design", "Marketing", 
    "Sales", "Operations", "Finance", "Legal", "HR", "Customer Success"
]

LOCATIONS = [
    "San Francisco, CA", "New York, NY", "Austin, TX", "Seattle, WA",
    "Boston, MA", "Los Angeles, CA", "Chicago, IL", "Denver, CO",
    "Remote", "上海", "北京", "深圳", "杭州", "广州"
]

JOB_LEVELS = [
    "Junior", "Mid-Level", "Senior", "Lead", "Principal",
    "Manager", "Senior Manager", "Director", "VP", "C-Level"
]

POSITION_TITLES = [
    # Engineering
    ("Software Engineer", "Engineering"),
    ("Senior Software Engineer", "Engineering"),
    ("Staff Software Engineer", "Engineering"),
    ("Frontend Developer", "Engineering"),
    ("Backend Developer", "Engineering"),
    ("Full Stack Developer", "Engineering"),
    ("DevOps Engineer", "Engineering"),
    ("Data Engineer", "Engineering"),
    ("Machine Learning Engineer", "Engineering"),
    ("QA Engineer", "Engineering"),
    
    # Product
    ("Product Manager", "Product"),
    ("Senior Product Manager", "Product"),
    ("Product Owner", "Product"),
    ("Technical Product Manager", "Product"),
    
    # Design
    ("UI/UX Designer", "Design"),
    ("Senior Designer", "Design"),
    ("Product Designer", "Design"),
    ("Visual Designer", "Design"),
    ("Design Lead", "Design"),
    
    # Marketing
    ("Marketing Manager", "Marketing"),
    ("Content Marketing Manager", "Marketing"),
    ("Digital Marketing Specialist", "Marketing"),
    ("SEO Specialist", "Marketing"),
    ("Growth Marketing Manager", "Marketing"),
    
    # Sales
    ("Sales Representative", "Sales"),
    ("Account Executive", "Sales"),
    ("Sales Manager", "Sales"),
    ("Business Development Manager", "Sales"),
    ("Enterprise Sales Manager", "Sales"),
    
    # Operations
    ("Operations Manager", "Operations"),
    ("Project Manager", "Operations"),
    ("Scrum Master", "Operations"),
    ("Business Analyst", "Operations"),
    
    # Finance
    ("Financial Analyst", "Finance"),
    ("Senior Accountant", "Finance"),
    ("Finance Manager", "Finance"),
    ("Controller", "Finance"),
    
    # HR
    ("HR Manager", "HR"),
    ("Recruiter", "HR"),
    ("Talent Acquisition Manager", "HR"),
    ("HR Business Partner", "HR"),
    
    # Customer Success
    ("Customer Success Manager", "Customer Success"),
    ("Customer Support Specialist", "Customer Success"),
    ("Technical Support Engineer", "Customer Success"),
]

SKILLS_POOL = [
    # Programming Languages
    "Python", "JavaScript", "TypeScript", "Java", "C++", "Go", "Rust", "Ruby", "PHP", "Swift",
    "Kotlin", "Scala", "R", "MATLAB", "C#", "Objective-C",
    
    # Frameworks & Libraries
    "React", "Vue.js", "Angular", "Next.js", "Django", "FastAPI", "Flask", "Spring Boot",
    "Express.js", "Node.js", "Laravel", "Ruby on Rails", ".NET", "TensorFlow", "PyTorch",
    
    # Databases
    "PostgreSQL", "MySQL", "MongoDB", "Redis", "Elasticsearch", "Cassandra", "DynamoDB",
    "Oracle", "SQL Server", "Neo4j", "InfluxDB",
    
    # Cloud & DevOps
    "AWS", "Azure", "Google Cloud", "Docker", "Kubernetes", "Jenkins", "GitLab CI/CD",
    "Terraform", "Ansible", "CircleCI", "GitHub Actions",
    
    # Tools & Technologies
    "Git", "Linux", "REST API", "GraphQL", "Microservices", "Agile", "Scrum", "JIRA",
    "Confluence", "Figma", "Sketch", "Adobe Creative Suite", "Tableau", "Power BI",
    
    # Soft Skills
    "Leadership", "Communication", "Problem Solving", "Team Collaboration", "Project Management",
    "Critical Thinking", "Time Management", "Adaptability", "Creativity", "Attention to Detail"
]

RESPONSIBILITIES_TEMPLATES = [
    "Design and implement scalable {tech} solutions",
    "Collaborate with cross-functional teams to deliver high-quality products",
    "Participate in code reviews and maintain coding standards",
    "Mentor junior team members and share knowledge",
    "Contribute to architectural decisions and technical discussions",
    "Write clean, maintainable, and well-documented code",
    "Troubleshoot and debug complex issues in production",
    "Optimize application performance and scalability",
    "Implement security best practices and ensure data protection",
    "Participate in agile development processes and sprint planning"
]

QUALIFICATIONS_TEMPLATES = [
    "Bachelor's degree in Computer Science or related field",
    "{years}+ years of experience in {domain}",
    "Strong proficiency in {tech1} and {tech2}",
    "Experience with cloud platforms (AWS/Azure/GCP)",
    "Excellent problem-solving and analytical skills",
    "Strong communication and collaboration abilities",
    "Experience with agile development methodologies",
    "Proven track record of delivering high-quality software",
    "Experience with CI/CD pipelines and DevOps practices"
]

BENEFITS_TEMPLATES = [
    "Competitive salary and equity package",
    "Comprehensive health, dental, and vision insurance",
    "Flexible working hours and remote work options",
    "Professional development budget",
    "Annual company retreats and team building events",
    "Generous PTO and parental leave",
    "401(k) matching program",
    "Free lunch and snacks",
    "Gym membership reimbursement",
    "Learning and development opportunities"
]


async def generate_mock_positions(db: AsyncSession, user_id: int, count: int = 30) -> List[Position]:
    """Generate mock position data"""
    positions = []
    
    for i in range(count):
        # Select random position title and department
        title, default_dept = random.choice(POSITION_TITLES)
        department = default_dept if random.random() > 0.3 else random.choice(DEPARTMENTS)
        
        # Generate random data
        location = random.choice(LOCATIONS)
        job_level = random.choice(JOB_LEVELS)
        job_type = random.choice(JOB_TYPES)
        status = random.choice(list(PositionStatus))
        urgency = random.choice(list(PositionUrgency))
        
        # Salary range based on job level
        base_salary = random.randint(50, 150) * 1000
        if "Senior" in job_level or "Lead" in job_level:
            base_salary *= 1.5
        elif "Principal" in job_level or "Staff" in job_level:
            base_salary *= 2
        elif "Manager" in job_level:
            base_salary *= 1.8
        elif "Director" in job_level:
            base_salary *= 2.5
        elif "VP" in job_level:
            base_salary *= 3
            
        salary_min = int(base_salary * 0.9)
        salary_max = int(base_salary * 1.3)
        
        # Select random skills
        num_required_skills = random.randint(3, 8)
        num_preferred_skills = random.randint(2, 5)
        all_skills = random.sample(SKILLS_POOL, num_required_skills + num_preferred_skills)
        required_skills = all_skills[:num_required_skills]
        preferred_skills = all_skills[num_required_skills:]
        
        # Generate responsibilities, qualifications, and benefits
        responsibilities = random.sample(RESPONSIBILITIES_TEMPLATES, random.randint(4, 7))
        responsibilities = [r.format(tech=random.choice(required_skills)) for r in responsibilities]
        
        qualifications = []
        for template in random.sample(QUALIFICATIONS_TEMPLATES, random.randint(3, 6)):
            qual = template.format(
                years=random.randint(2, 8),
                domain=department.lower(),
                tech1=random.choice(required_skills),
                tech2=random.choice(required_skills)
            )
            qualifications.append(qual)
            
        benefits = random.sample(BENEFITS_TEMPLATES, random.randint(5, 8))
        
        # Set dates
        created_days_ago = random.randint(0, 60)
        created_at = datetime.now(timezone.utc) - timedelta(days=created_days_ago)
        
        deadline = None
        if status in [PositionStatus.OPEN, PositionStatus.REVIEWING]:
            deadline = datetime.now(timezone.utc) + timedelta(days=random.randint(14, 90))
        
        # Create position
        position = Position(
            title=f"{title} - {location}",
            department=department,
            location=location,
            job_level=job_level,
            job_type=job_type,
            headcount=random.randint(1, 5),
            urgency=urgency,
            status=status,
            salary_min=salary_min,
            salary_max=salary_max,
            salary_currency="USD" if location not in ["上海", "北京", "深圳", "杭州", "广州"] else "CNY",
            requirements={
                "experience_years": random.randint(2, 10),
                "education": "Bachelor's degree or equivalent",
                "languages": ["English"] if location != "Remote" else ["English", "Mandarin"]
            },
            responsibilities=responsibilities,
            qualifications=qualifications,
            benefits=benefits,
            required_skills=required_skills,
            preferred_skills=preferred_skills,
            capability_weights={
                "digital": 0.2,
                "industry": 0.25,
                "position": 0.3,
                "innovation": 0.15,
                "learning": 0.1
            },
            data_permission=random.choice(list(DataPermission)),
            deadline=deadline,
            created_by=user_id,
            created_at=created_at,
            updated_at=created_at
        )
        
        positions.append(position)
    
    # Add all positions to database
    db.add_all(positions)
    await db.commit()
    
    # Refresh to get IDs
    for position in positions:
        await db.refresh(position)
    
    return positions


async def main():
    """Main function to seed positions"""
    async with AsyncSessionLocal() as db:
        try:
            # Get the admin user (ID: 1) or first available user
            from sqlalchemy import select
            from app.models.user import User
            
            result = await db.execute(
                select(User.id).where(User.is_active == True).order_by(User.id).limit(1)
            )
            user = result.first()
            
            if not user:
                print("❌ No active user found in database. Please create a user first.")
                return
                
            user_id = user[0]
            print(f"✅ Using user ID: {user_id}")
            
            # Check if positions already exist
            from sqlalchemy import func
            result = await db.execute(
                select(func.count()).select_from(Position)
            )
            existing_count = result.scalar()
            
            if existing_count > 0:
                print(f"ℹ️ Found {existing_count} existing positions")
                response = input("Do you want to add more positions? (y/n): ")
                if response.lower() != 'y':
                    print("Skipping position generation.")
                    return
            
            # Generate positions
            print("🔄 Generating mock positions...")
            positions = await generate_mock_positions(db, user_id, count=30)
            
            print(f"✅ Successfully created {len(positions)} positions!")
            
            # Show some statistics
            by_status = {}
            by_dept = {}
            for pos in positions:
                status = pos.status.value if hasattr(pos.status, 'value') else str(pos.status)
                by_status[status] = by_status.get(status, 0) + 1
                by_dept[pos.department] = by_dept.get(pos.department, 0) + 1
            
            print("\n📊 Position Statistics:")
            print("By Status:")
            for status, count in sorted(by_status.items()):
                print(f"  - {status}: {count}")
            
            print("\nBy Department (top 5):")
            for dept, count in sorted(by_dept.items(), key=lambda x: x[1], reverse=True)[:5]:
                print(f"  - {dept}: {count}")
                
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())