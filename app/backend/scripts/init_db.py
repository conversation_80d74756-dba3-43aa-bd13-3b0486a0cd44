"""
Initialize database with test data
"""
import asyncio
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from sqlalchemy.ext.asyncio import AsyncSession
from app.core.database import engine, AsyncSessionLocal, Base
from app.core.security import get_password_hash
from app.models.user import User, UserRole
from app.core.config import settings


async def init_db():
    """Initialize database with tables and test data"""
    print("Creating database tables...")
    
    # Create tables
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    print("Tables created successfully!")
    
    # Create test users
    async with AsyncSessionLocal() as db:
        # Check if admin user exists
        from sqlalchemy import select
        result = await db.execute(
            select(User).where(User.email == "<EMAIL>")
        )
        admin_user = result.scalar_one_or_none()
        
        if not admin_user:
            print("Creating admin user...")
            admin_user = User(
                email="<EMAIL>",
                username="admin",
                hashed_password=get_password_hash("Admin123!@#"),
                full_name="System Administrator",
                role=UserRole.SUPER_ADMIN,
                is_active=True,
                is_superuser=True,
                is_verified=True
            )
            db.add(admin_user)
            await db.commit()
            print("Admin user created!")
            print("  Email: <EMAIL>")
            print("  Password: Admin123!@#")
        
        # Check if test user exists
        result = await db.execute(
            select(User).where(User.email == "<EMAIL>")
        )
        test_user = result.scalar_one_or_none()
        
        if not test_user:
            print("Creating test user...")
            test_user = User(
                email="<EMAIL>",
                username="testuser",
                hashed_password=get_password_hash("Test123!@#"),
                full_name="Test User",
                role=UserRole.HR_SPECIALIST,
                is_active=True,
                is_superuser=False,
                is_verified=True,
                department="人力资源部",
                position="HR专员"
            )
            db.add(test_user)
            await db.commit()
            print("Test user created!")
            print("  Email: <EMAIL>")
            print("  Password: Test123!@#")
    
    print("\nDatabase initialization completed!")


if __name__ == "__main__":
    asyncio.run(init_db())