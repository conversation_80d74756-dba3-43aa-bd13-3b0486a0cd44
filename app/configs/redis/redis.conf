# Redis Configuration for Monitoring Data Persistence
# Optimized for monitoring cache with persistence

# Network and Security
bind 0.0.0.0
port 6379
protected-mode yes
requirepass Pass1234

# Persistence Configuration
# AOF (Append Only File) for durability
appendonly yes
appendfsync everysec
no-appendfsync-on-rewrite no
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# RDB Snapshots as backup
save 900 1      # Save after 900 seconds if at least 1 key changed
save 300 10     # Save after 300 seconds if at least 10 keys changed
save 60 10000   # Save after 60 seconds if at least 10000 keys changed

stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
dbfilename dump.rdb

# Memory Management
maxmemory 512mb
maxmemory-policy allkeys-lru  # Evict least recently used keys when memory limit reached

# Monitoring Database Configuration
databases 16
# DB 0: Default (monitoring cache)
# DB 1: Session data
# DB 2: Celery results
# DB 3-15: Available for future use

# Performance Tuning
timeout 300
tcp-keepalive 300
tcp-backlog 511

# Logging
loglevel notice
logfile ""

# Slow Log
slowlog-log-slower-than 10000
slowlog-max-len 128

# Client Management
maxclients 10000

# Advanced
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64

# Disable potentially dangerous commands in production
rename-command CONFIG ""
rename-command SHUTDOWN SHUTDOWN_RENAMED
rename-command DEBUG ""
rename-command EVAL ""
rename-command FLUSHALL ""
rename-command FLUSHDB ""