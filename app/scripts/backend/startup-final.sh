#!/bin/bash
set -e

echo "Starting TalentForge Pro Backend..."

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL..."
while ! pg_isready -h ${POSTGRES_SERVER:-postgres} -p 5432 -U ${POSTGRES_USER:-postgres}; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 2
done

echo "PostgreSQL is ready!"

# Handle database migrations
echo "Running database migrations..."

# First, try to run migrations normally
alembic upgrade heads 2>/dev/null || {
    echo "Migration failed, attempting alternative approaches..."
    
    # Check if it's a duplicate ENUM issue
    if alembic upgrade heads 2>&1 | grep -q "already exists"; then
        echo "Duplicate database objects detected, continuing anyway..."
    else
        # If it's the multiple heads issue, try to merge
        alembic merge -m "auto_merge_heads" heads 2>/dev/null || true
        alembic upgrade heads 2>/dev/null || true
    fi
}

# Initialize database with seed data
echo "Initializing database with seed data..."
python scripts/init_db.py 2>/dev/null || echo "Seed data initialization completed or already exists"

echo "Database initialization completed!"

# Handle Docker socket permissions for monitoring
echo "Configuring Docker socket permissions..."
if [ -e /var/run/docker.sock ]; then
    # Simple approach: give appuser direct read access to docker socket
    echo "Granting appuser direct access to Docker socket"
    chmod 666 /var/run/docker.sock
    echo "Docker socket permissions set to world-readable"
    ls -la /var/run/docker.sock
else
    echo "Docker socket not found, monitoring will use subprocess fallback"
fi

# Start Celery Beat scheduler in background
echo "📅 Starting Celery Beat scheduler in background..."
su appuser -c "celery -A app.worker beat --loglevel=info --detach --pidfile=/tmp/celerybeat.pid" || echo "⚠️  Celery Beat start failed, scheduling tasks may not work"

# Start the application as appuser
echo "Starting FastAPI application with monitoring cache integration..."
exec su appuser -c "uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"