#!/bin/bash
set -e

echo "Starting TalentForge Pro Backend..."

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL..."
while ! pg_isready -h ${POSTGRES_SERVER:-postgres} -p 5432 -U ${POSTGRES_USER:-postgres}; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 2
done

echo "PostgreSQL is ready!"

# Handle database migrations
echo "Running database migrations..."

# First, try to run migrations normally
alembic upgrade heads 2>/dev/null || {
    echo "Migration failed, attempting alternative approaches..."
    
    # Check if it's a duplicate ENUM issue
    if alembic upgrade heads 2>&1 | grep -q "already exists"; then
        echo "Duplicate database objects detected, continuing anyway..."
    else
        # If it's the multiple heads issue, try to merge
        alembic merge -m "auto_merge_heads" heads 2>/dev/null || true
        alembic upgrade heads 2>/dev/null || true
    fi
}

# Initialize database with seed data
echo "Initializing database with seed data..."
python scripts/init_db.py 2>/dev/null || echo "Seed data initialization completed or already exists"

echo "Database initialization completed!"

# Start the application
echo "Starting FastAPI application..."
exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload