#!/usr/bin/env python3
"""
向量化功能测试脚本
用于验证DEF-004: 向量化实现是否正确配置
"""

import asyncio
import sys
import os
import json
from typing import Optional

from app.core.config import settings
from app.core.ai_config import ai_settings
from app.core.database import AsyncSessionLocal
from app.services.vector_service import VectorService
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_ollama_connection():
    """测试Ollama连接"""
    print("\n🔗 测试 Ollama 连接...")
    
    try:
        async with VectorService() as service:
            is_connected = await service.check_ollama_connection()
            if is_connected:
                print("✅ Ollama 连接成功")
                
                # 检查模型是否加载
                model_loaded = await service.ensure_model_loaded()
                if model_loaded:
                    print("✅ BGE-M3 模型已加载")
                else:
                    print("⚠️  BGE-M3 模型未加载，将尝试下载")
                    
                return True
            else:
                print("❌ Ollama 连接失败")
                return False
                
    except Exception as e:
        print(f"❌ Ollama 连接测试出错: {str(e)}")
        return False


async def test_embedding_generation():
    """测试嵌入生成"""
    print("\n🧮 测试嵌入生成...")
    
    test_texts = [
        "软件工程师，熟悉Python、React、数据库设计",
        "Product Manager with 5 years experience in fintech",
        "数据科学家，专业机器学习和深度学习算法"
    ]
    
    try:
        async with VectorService() as service:
            for i, text in enumerate(test_texts, 1):
                print(f"\n测试文本 {i}: {text}")
                
                embedding = await service.generate_embedding(text)
                
                if embedding and len(embedding) == 1024:
                    # 检查向量是否为全零
                    non_zero_count = sum(1 for x in embedding if x != 0)
                    print(f"✅ 生成嵌入成功: 维度={len(embedding)}, 非零元素={non_zero_count}")
                    print(f"   前5个元素: {embedding[:5]}")
                else:
                    print(f"❌ 嵌入生成失败: 维度={len(embedding) if embedding else 0}")
                    return False
                    
            return True
            
    except Exception as e:
        print(f"❌ 嵌入生成测试出错: {str(e)}")
        return False


async def test_vector_database():
    """测试向量数据库操作"""
    print("\n🗄️  测试向量数据库操作...")
    
    try:
        async with AsyncSessionLocal() as db:
            # 测试基本查询
            from sqlalchemy import text
            
            # 检查pgvector扩展
            result = await db.execute(text("SELECT extname FROM pg_extension WHERE extname = 'vector'"))
            vector_ext = result.scalar()
            
            if vector_ext:
                print("✅ pgvector 扩展已安装")
                
                # 检查candidate_vectors表
                result = await db.execute(text("""
                    SELECT table_name FROM information_schema.tables 
                    WHERE table_name = 'candidate_vectors'
                """))
                table_exists = result.scalar()
                
                if table_exists:
                    print("✅ candidate_vectors 表存在")
                    
                    # 检查索引
                    result = await db.execute(text("""
                        SELECT indexname FROM pg_indexes 
                        WHERE tablename = 'candidate_vectors' 
                        AND indexname LIKE '%hnsw%'
                    """))
                    indexes = result.fetchall()
                    
                    if indexes:
                        print(f"✅ HNSW 索引已创建: {[idx[0] for idx in indexes]}")
                    else:
                        print("⚠️  HNSW 索引未找到，可能影响搜索性能")
                        
                else:
                    print("❌ candidate_vectors 表不存在")
                    return False
                    
            else:
                print("❌ pgvector 扩展未安装")
                return False
                
        return True
        
    except Exception as e:
        print(f"❌ 向量数据库测试出错: {str(e)}")
        return False


async def test_candidate_vectorization():
    """测试候选人向量化"""
    print("\n👤 测试候选人向量化...")
    
    try:
        async with AsyncSessionLocal() as db:
            from app.models.candidate import Candidate
            from sqlalchemy import select
            
            # 查找一个候选人进行测试
            result = await db.execute(
                select(Candidate)
                .where(Candidate.deleted_at.is_(None))
                .limit(1)
            )
            candidate = result.scalar_one_or_none()
            
            if not candidate:
                print("⚠️  未找到候选人数据，跳过向量化测试")
                return True
                
            print(f"使用候选人: {candidate.name} (ID: {candidate.id})")
            
            async with VectorService() as service:
                # 向量化候选人
                success = await service.vectorize_candidate(
                    db, 
                    candidate.id, 
                    force_update=True
                )
                
                if success:
                    print("✅ 候选人向量化成功")
                    
                    # 验证向量是否存储
                    from app.models.candidate_vector import CandidateVector
                    result = await db.execute(
                        select(CandidateVector).where(
                            CandidateVector.candidate_id == candidate.id
                        )
                    )
                    vector_record = result.scalar_one_or_none()
                    
                    if vector_record:
                        print("✅ 向量记录已存储到数据库")
                        print(f"   模型: {vector_record.embedding_model}")
                        print(f"   提供商: {vector_record.embedding_provider}")
                        
                        return True
                    else:
                        print("❌ 向量记录未找到")
                        return False
                        
                else:
                    print("❌ 候选人向量化失败")
                    return False
                    
    except Exception as e:
        print(f"❌ 候选人向量化测试出错: {str(e)}")
        return False


async def test_vector_search():
    """测试向量搜索"""
    print("\n🔍 测试向量搜索...")
    
    search_queries = [
        "Python 开发工程师",
        "Frontend React developer",
        "数据科学家 机器学习"
    ]
    
    try:
        async with AsyncSessionLocal() as db:
            async with VectorService() as service:
                for query in search_queries:
                    print(f"\n搜索查询: {query}")
                    
                    results = await service.search_similar_candidates(
                        db=db,
                        query=query,
                        limit=5,
                        threshold=0.1  # 降低阈值以便测试
                    )
                    
                    if results:
                        print(f"✅ 找到 {len(results)} 个相似候选人:")
                        for candidate, score in results[:3]:  # 显示前3个结果
                            print(f"   - {candidate.name}: 相似度 {score:.3f}")
                    else:
                        print("⚠️  未找到相似候选人（可能是向量数据不足）")
                        
            return True
            
    except Exception as e:
        print(f"❌ 向量搜索测试出错: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始向量化功能测试")
    print("=" * 50)
    
    # 显示配置信息
    print(f"📋 配置信息:")
    print(f"   Ollama Host: {ai_settings.OLLAMA_HOST}")
    print(f"   Ollama Model: {ai_settings.OLLAMA_MODEL}")
    
    tests = [
        ("Ollama连接", test_ollama_connection),
        ("嵌入生成", test_embedding_generation), 
        ("向量数据库", test_vector_database),
        ("候选人向量化", test_candidate_vectorization),
        ("向量搜索", test_vector_search),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ 测试 {test_name} 出现异常: {str(e)}")
            results[test_name] = False
    
    # 总结报告
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = sum(1 for r in results.values() if r)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总计: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有向量化功能测试通过！DEF-004 修复验证成功。")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查向量化配置。")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)