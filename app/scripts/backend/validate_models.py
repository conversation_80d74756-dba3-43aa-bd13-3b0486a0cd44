#!/usr/bin/env python3
"""
Database Model Validation CLI Tool
=================================

Comprehensive validation tool for SQLAlchemy models after service consolidation.

Usage:
    python validate_models.py [--verbose] [--fix-common-issues]

Options:
    --verbose              Enable detailed logging
    --fix-common-issues    Attempt to fix common model issues automatically

This tool validates:
- Model relationship consistency
- Foreign key configurations
- Back-populate relationships
- Cross-model dependencies
- Database schema compatibility

Author: TalentForge Pro Development Team
Date: August 27, 2025
"""

import sys
import os
import argparse
import logging
import asyncio
from pathlib import Path

# Add the backend app directory to Python path
backend_dir = Path(__file__).parent.parent.parent
sys.path.insert(0, str(backend_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def main():
    """Main validation function"""
    parser = argparse.ArgumentParser(
        description="Database Model Validation Tool",
        epilog="Run this tool after any model changes to ensure relationship integrity"
    )
    parser.add_argument(
        "--verbose", 
        action="store_true", 
        help="Enable verbose logging"
    )
    parser.add_argument(
        "--fix-common-issues",
        action="store_true",
        help="Attempt to fix common issues automatically"
    )
    parser.add_argument(
        "--output-report",
        type=str,
        help="Save validation report to file"
    )
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    try:
        # Import the validation service
        from app.services.model_validation_service import model_validation_service
        
        logger.info("🔍 Starting comprehensive model validation...")
        logger.info("=" * 60)
        
        # Run validation
        results = model_validation_service.validate_all_models()
        
        # Print results summary
        print(f"\n{results['status_emoji']} VALIDATION RESULTS: {results['status']}")
        print("=" * 60)
        print(f"📊 Total Checks: {results['summary']['total_checks']}")
        print(f"✅ Passed: {results['summary']['passed']}")
        print(f"❌ Failed: {results['summary']['failed']}")
        print(f"⚠️ Warnings: {results['summary']['warnings']}")
        print(f"🚨 Critical: {results['summary']['critical_errors']}")
        print(f"📈 Success Rate: {results['summary']['success_rate']:.1f}%")
        
        # Show detailed results if verbose
        if args.verbose:
            print("\n🔍 DETAILED RESULTS:")
            print("-" * 40)
            
            if results['details']['passed']:
                print("\n✅ PASSED CHECKS:")
                for item in results['details']['passed']:
                    print(f"  • {item}")
            
            if results['details']['failed']:
                print("\n❌ FAILED CHECKS:")
                for item in results['details']['failed']:
                    print(f"  • {item}")
            
            if results['details']['warnings']:
                print("\n⚠️ WARNINGS:")
                for item in results['details']['warnings']:
                    print(f"  • {item}")
            
            if results['details']['critical_errors']:
                print("\n🚨 CRITICAL ERRORS:")
                for item in results['details']['critical_errors']:
                    print(f"  • {item}")
        
        # Show recommendations
        if results.get('recommendations'):
            print("\n💡 RECOMMENDATIONS:")
            print("-" * 40)
            for rec in results['recommendations']:
                print(f"  • {rec}")
        
        # Save report if requested
        if args.output_report:
            import json
            with open(args.output_report, 'w') as f:
                json.dump(results, f, indent=2, default=str)
            print(f"\n📄 Report saved to: {args.output_report}")
        
        # Attempt fixes if requested
        if args.fix_common_issues and results['status'] in ['FAILED', 'CRITICAL_FAILED']:
            print(f"\n🔧 ATTEMPTING AUTOMATIC FIXES...")
            print("-" * 40)
            
            # Here you could implement automatic fixes for common issues
            fixes_attempted = attempt_common_fixes(results['details'])
            
            if fixes_attempted:
                print("✅ Some issues were automatically fixed")
                print("🔄 Re-run validation to verify fixes")
            else:
                print("⚠️ No automatic fixes available for detected issues")
                print("🛠️ Manual intervention required")
        
        print("\n" + "=" * 60)
        
        # Exit with appropriate code
        if results['status'] == 'CRITICAL_FAILED':
            print("🚨 CRITICAL ERRORS DETECTED - Application may not start")
            print("🔧 Fix critical issues before proceeding")
            sys.exit(2)
        elif results['status'] == 'FAILED':
            print("❌ VALIDATION FAILED - Some features may not work correctly")
            sys.exit(1)
        elif results['status'] == 'PASSED_WITH_WARNINGS':
            print("⚠️ VALIDATION PASSED WITH WARNINGS - Review recommendations")
            sys.exit(0)
        else:
            print("✅ ALL VALIDATIONS PASSED - Models are properly configured")
            sys.exit(0)
            
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.error("💡 Make sure you're running this from the correct directory")
        logger.error("💡 Ensure all dependencies are installed: poetry install")
        sys.exit(1)
        
    except Exception as e:
        logger.error(f"❌ Validation failed with unexpected error: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

def attempt_common_fixes(details: dict) -> bool:
    """
    Attempt to fix common model validation issues
    
    Args:
        details: Validation details from the validation service
        
    Returns:
        bool: True if any fixes were attempted
    """
    fixes_applied = False
    
    # Analyze failed checks for common patterns
    failed_checks = details.get('failed', [])
    critical_errors = details.get('critical_errors', [])
    
    for error in failed_checks + critical_errors:
        # Check for missing back_populates relationships
        if "Missing back_populates" in error:
            logger.info(f"🔧 Attempting to fix: {error}")
            # Here you could implement automatic relationship fixes
            # For now, just log what would be fixed
            logger.info("  💡 This requires manual editing of model files")
            
        # Check for foreign key issues  
        elif "foreign key" in error.lower():
            logger.info(f"🔧 Analyzing foreign key issue: {error}")
            logger.info("  💡 Check foreign key column definitions in models")
            
        # Check for circular import issues
        elif "import" in error.lower():
            logger.info(f"🔧 Analyzing import issue: {error}")
            logger.info("  💡 Review import statements and model organization")
    
    if not fixes_applied:
        logger.info("ℹ️ No automatic fixes available for current issues")
        logger.info("📖 Review the recommendations for manual fix instructions")
    
    return fixes_applied

if __name__ == "__main__":
    asyncio.run(main())