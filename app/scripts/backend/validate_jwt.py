#!/usr/bin/env python3
"""
JWT Validation Utilities for TalentForge Pro

This module provides comprehensive JWT token validation, analysis,
and security testing functionality for the authentication system.

Features:
- Token structure validation
- Signature verification
- Expiration checking
- Claims analysis
- Security vulnerability detection
- Performance benchmarking
"""
import asyncio
import sys
import time
import json
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta, timezone
from dataclasses import dataclass

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from app.core.security import create_access_token, create_refresh_token, decode_token
from app.core.config import settings
from app.core.database import AsyncSessionLocal
from app.crud.user import user as user_crud
from jose import JWTError, jwt
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class TokenValidationResult:
    """Result of token validation"""
    is_valid: bool
    token_type: str
    user_id: Optional[str] = None
    expiry: Optional[datetime] = None
    issued_at: Optional[datetime] = None
    preferences: Optional[Dict[str, Any]] = None
    errors: List[str] = None
    warnings: List[str] = None
    security_score: float = 0.0
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


class JWTValidator:
    """Comprehensive JWT token validator"""
    
    def __init__(self):
        self.secret_key = settings.SECRET_KEY
        self.algorithm = settings.ALGORITHM
        
    def validate_token_structure(self, token: str) -> TokenValidationResult:
        """
        Validate JWT token structure and claims
        
        Args:
            token: JWT token string to validate
            
        Returns:
            TokenValidationResult with detailed validation info
        """
        result = TokenValidationResult(
            is_valid=False,
            token_type="unknown"
        )
        
        try:
            # Decode without verification first to check structure
            unverified_payload = jwt.get_unverified_claims(token)
            
            # Check required claims
            required_claims = ["sub", "type", "exp", "iat"]
            missing_claims = [claim for claim in required_claims if claim not in unverified_payload]
            
            if missing_claims:
                result.errors.append(f"Missing required claims: {missing_claims}")
                return result
            
            # Validate token type
            token_type = unverified_payload.get("type")
            if token_type not in ["access", "refresh", "password_reset"]:
                result.errors.append(f"Invalid token type: {token_type}")
                return result
            
            result.token_type = token_type
            
            # Extract and validate timestamps
            try:
                exp_timestamp = unverified_payload.get("exp")
                iat_timestamp = unverified_payload.get("iat")
                
                if exp_timestamp:
                    result.expiry = datetime.fromtimestamp(exp_timestamp, tz=timezone.utc)
                if iat_timestamp:
                    result.issued_at = datetime.fromtimestamp(iat_timestamp, tz=timezone.utc)
                
                # Check if token is expired
                if result.expiry and result.expiry < datetime.now(timezone.utc):
                    result.errors.append("Token is expired")
                
                # Check if token was issued in the future (clock skew tolerance: 5 minutes)
                if result.issued_at and result.issued_at > datetime.now(timezone.utc) + timedelta(minutes=5):
                    result.errors.append("Token issued in the future")
                    
            except (ValueError, TypeError) as e:
                result.errors.append(f"Invalid timestamp format: {str(e)}")
            
            # Extract user ID
            result.user_id = unverified_payload.get("sub")
            if not result.user_id:
                result.errors.append("Missing user ID (sub claim)")
            
            # Extract preferences for access tokens
            if token_type == "access":
                result.preferences = unverified_payload.get("preferences", {})
                
                # Validate preferences structure for access tokens
                if result.preferences:
                    expected_prefs = ["locale", "timezone"]
                    for pref in expected_prefs:
                        if pref not in result.preferences:
                            result.warnings.append(f"Missing preference: {pref}")
            
            # Now verify signature
            try:
                verified_payload = decode_token(token)
                result.is_valid = True
                logger.info(f"✅ Token signature verified")
                
                # Calculate security score
                result.security_score = self._calculate_security_score(
                    verified_payload, result.expiry, result.issued_at
                )
                
            except JWTError as e:
                result.errors.append(f"Signature verification failed: {str(e)}")
            
        except Exception as e:
            result.errors.append(f"Token structure validation failed: {str(e)}")
        
        return result
    
    def _calculate_security_score(
        self,
        payload: Dict[str, Any],
        expiry: Optional[datetime],
        issued_at: Optional[datetime]
    ) -> float:
        """
        Calculate security score for token (0.0 to 1.0)
        
        Args:
            payload: Decoded JWT payload
            expiry: Token expiration time
            issued_at: Token issue time
            
        Returns:
            Security score between 0.0 and 1.0
        """
        score = 0.0
        
        # Base score for valid signature
        score += 0.3
        
        # Check expiration time (shorter = more secure)
        if expiry and issued_at:
            token_lifetime = (expiry - issued_at).total_seconds()
            if token_lifetime <= 900:  # 15 minutes
                score += 0.3
            elif token_lifetime <= 3600:  # 1 hour
                score += 0.2
            elif token_lifetime <= 86400:  # 24 hours
                score += 0.1
            # Longer lifetimes get no points
        
        # Check for sensitive data in payload (lower is better)
        sensitive_keys = ["password", "secret", "key", "hash"]
        has_sensitive = any(key in str(payload).lower() for key in sensitive_keys)
        if not has_sensitive:
            score += 0.2
        
        # Check for proper token type
        if payload.get("type") in ["access", "refresh"]:
            score += 0.1
        
        # Check for required claims
        required_claims = ["sub", "type", "exp", "iat"]
        if all(claim in payload for claim in required_claims):
            score += 0.1
        
        return min(score, 1.0)  # Cap at 1.0
    
    def analyze_token_security(self, token: str) -> Dict[str, Any]:
        """
        Perform comprehensive security analysis of JWT token
        
        Args:
            token: JWT token to analyze
            
        Returns:
            Security analysis results
        """
        analysis = {
            "overall_security": "unknown",
            "vulnerabilities": [],
            "recommendations": [],
            "compliance": {}
        }
        
        validation_result = self.validate_token_structure(token)
        
        if not validation_result.is_valid:
            analysis["overall_security"] = "invalid"
            analysis["vulnerabilities"].extend(validation_result.errors)
            return analysis
        
        # Check token lifetime
        if validation_result.expiry and validation_result.issued_at:
            lifetime = (validation_result.expiry - validation_result.issued_at).total_seconds()
            
            if lifetime > 86400 * 7:  # More than 7 days
                analysis["vulnerabilities"].append("Token lifetime too long (>7 days)")
                analysis["recommendations"].append("Reduce token expiration time")
            elif lifetime > 86400:  # More than 24 hours
                analysis["recommendations"].append("Consider shorter token lifetime for better security")
        
        # Check for algorithm security
        try:
            header = jwt.get_unverified_header(token)
            algorithm = header.get("alg", "").upper()
            
            if algorithm == "NONE":
                analysis["vulnerabilities"].append("CRITICAL: Token uses 'none' algorithm")
            elif algorithm.startswith("HS"):
                if algorithm == "HS256":
                    analysis["compliance"]["algorithm"] = "secure"
                else:
                    analysis["recommendations"].append("Consider using HS256 for better compatibility")
            elif algorithm.startswith("RS") or algorithm.startswith("ES"):
                analysis["compliance"]["algorithm"] = "secure"
                analysis["recommendations"].append("Asymmetric algorithm detected - ensure key management is secure")
            else:
                analysis["vulnerabilities"].append(f"Unknown or insecure algorithm: {algorithm}")
                
        except Exception as e:
            analysis["vulnerabilities"].append(f"Could not analyze token algorithm: {str(e)}")
        
        # Check payload size (large payloads can be a security risk)
        try:
            payload = decode_token(token)
            payload_size = len(json.dumps(payload))
            
            if payload_size > 2048:  # 2KB limit
                analysis["vulnerabilities"].append("Token payload too large (>2KB)")
            elif payload_size > 1024:  # 1KB warning
                analysis["recommendations"].append("Consider reducing token payload size")
                
            analysis["compliance"]["payload_size"] = f"{payload_size} bytes"
            
        except Exception:
            pass
        
        # Overall security assessment
        if validation_result.security_score >= 0.8:
            analysis["overall_security"] = "excellent"
        elif validation_result.security_score >= 0.6:
            analysis["overall_security"] = "good"
        elif validation_result.security_score >= 0.4:
            analysis["overall_security"] = "fair"
        else:
            analysis["overall_security"] = "poor"
        
        analysis["security_score"] = validation_result.security_score
        
        return analysis
    
    async def validate_user_token_context(self, token: str) -> Dict[str, Any]:
        """
        Validate token against actual user context in database
        
        Args:
            token: JWT token to validate
            
        Returns:
            Context validation results
        """
        result = {
            "user_exists": False,
            "user_active": False,
            "user_verified": False,
            "preferences_match": False,
            "context_valid": False,
            "user_info": None
        }
        
        try:
            validation = self.validate_token_structure(token)
            
            if not validation.is_valid or not validation.user_id:
                result["error"] = "Invalid token or missing user ID"
                return result
            
            # Look up user in database
            async with AsyncSessionLocal() as db:
                try:
                    user_id = int(validation.user_id)
                    user = await user_crud.get(db, id=user_id)
                    
                    if user:
                        result["user_exists"] = True
                        result["user_active"] = user.is_active
                        result["user_verified"] = user.is_verified
                        result["user_info"] = {
                            "id": user.id,
                            "email": user.email,
                            "username": user.username,
                            "role": user.role,
                            "is_superuser": user.is_superuser
                        }
                        
                        # Check preferences match for access tokens
                        if validation.token_type == "access" and validation.preferences:
                            # In a real implementation, you would compare with user preferences
                            # For now, we'll just check if preferences are present
                            result["preferences_match"] = bool(validation.preferences.get("locale"))
                        else:
                            result["preferences_match"] = True  # N/A for refresh tokens
                        
                        # Overall context validation
                        result["context_valid"] = (
                            result["user_exists"] and
                            result["user_active"] and
                            result["preferences_match"]
                        )
                        
                    else:
                        result["error"] = f"User not found: {user_id}"
                        
                except ValueError:
                    result["error"] = f"Invalid user ID format: {validation.user_id}"
                    
        except Exception as e:
            result["error"] = f"Context validation failed: {str(e)}"
        
        return result
    
    def benchmark_token_operations(self, iterations: int = 100) -> Dict[str, Any]:
        """
        Benchmark JWT token operations for performance analysis
        
        Args:
            iterations: Number of iterations to run for each operation
            
        Returns:
            Performance benchmark results
        """
        logger.info(f"🚀 Starting JWT performance benchmark ({iterations} iterations)")
        
        results = {
            "iterations": iterations,
            "token_generation": {},
            "token_validation": {},
            "token_parsing": {}
        }
        
        # Benchmark token generation
        logger.info("📊 Benchmarking token generation...")
        generation_times = []
        
        for _ in range(iterations):
            start_time = time.perf_counter()
            token = create_access_token(
                subject="benchmark_user_123",
                preferences={"locale": "en", "timezone": "UTC"}
            )
            end_time = time.perf_counter()
            generation_times.append((end_time - start_time) * 1000)
        
        results["token_generation"] = {
            "average_ms": sum(generation_times) / len(generation_times),
            "min_ms": min(generation_times),
            "max_ms": max(generation_times),
            "target_ms": 10.0,
            "passed": sum(generation_times) / len(generation_times) < 10.0
        }
        
        # Benchmark token validation
        logger.info("📊 Benchmarking token validation...")
        test_token = create_access_token(
            subject="benchmark_user_123",
            preferences={"locale": "en", "timezone": "UTC"}
        )
        validation_times = []
        
        for _ in range(iterations):
            start_time = time.perf_counter()
            try:
                decode_token(test_token)
            except:
                pass
            end_time = time.perf_counter()
            validation_times.append((end_time - start_time) * 1000)
        
        results["token_validation"] = {
            "average_ms": sum(validation_times) / len(validation_times),
            "min_ms": min(validation_times),
            "max_ms": max(validation_times),
            "target_ms": 5.0,
            "passed": sum(validation_times) / len(validation_times) < 5.0
        }
        
        # Benchmark token parsing (structure validation)
        logger.info("📊 Benchmarking token parsing...")
        parsing_times = []
        
        for _ in range(iterations):
            start_time = time.perf_counter()
            self.validate_token_structure(test_token)
            end_time = time.perf_counter()
            parsing_times.append((end_time - start_time) * 1000)
        
        results["token_parsing"] = {
            "average_ms": sum(parsing_times) / len(parsing_times),
            "min_ms": min(parsing_times),
            "max_ms": max(parsing_times),
            "target_ms": 15.0,
            "passed": sum(parsing_times) / len(parsing_times) < 15.0
        }
        
        # Overall performance assessment
        all_passed = all(
            results[op]["passed"] for op in ["token_generation", "token_validation", "token_parsing"]
        )
        
        results["overall_performance"] = {
            "passed": all_passed,
            "status": "excellent" if all_passed else "needs_improvement"
        }
        
        logger.info("✅ Performance benchmark completed")
        
        return results


class TokenTestSuite:
    """Test suite for JWT validation and security"""
    
    def __init__(self):
        self.validator = JWTValidator()
    
    def create_test_tokens(self) -> Dict[str, str]:
        """Create various test tokens for validation testing"""
        tokens = {}
        
        # Valid access token
        tokens["valid_access"] = create_access_token(
            subject="123456",
            preferences={"locale": "en", "timezone": "UTC"}
        )
        
        # Valid refresh token
        tokens["valid_refresh"] = create_refresh_token(subject="123456")
        
        # Expired token (manually create with past expiration)
        expired_payload = {
            "sub": "123456",
            "type": "access",
            "exp": int((datetime.now(timezone.utc) - timedelta(hours=1)).timestamp()),
            "iat": int((datetime.now(timezone.utc) - timedelta(hours=2)).timestamp())
        }
        tokens["expired"] = jwt.encode(expired_payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
        
        # Token with invalid signature
        invalid_payload = {
            "sub": "123456",
            "type": "access",
            "exp": int((datetime.now(timezone.utc) + timedelta(hours=1)).timestamp()),
            "iat": int(datetime.now(timezone.utc).timestamp())
        }
        tokens["invalid_signature"] = jwt.encode(invalid_payload, "wrong_secret", algorithm=settings.ALGORITHM)
        
        # Malformed token
        tokens["malformed"] = "this.is.not.a.jwt.token"
        
        return tokens
    
    async def run_comprehensive_tests(self) -> Dict[str, Any]:
        """Run comprehensive JWT validation tests"""
        logger.info("🧪 Running comprehensive JWT validation tests...")
        
        test_tokens = self.create_test_tokens()
        results = {}
        
        # Test each token type
        for token_name, token in test_tokens.items():
            logger.info(f"Testing {token_name} token...")
            
            # Structure validation
            validation_result = self.validator.validate_token_structure(token)
            
            # Security analysis
            security_analysis = self.validator.analyze_token_security(token)
            
            # Context validation (only for valid tokens)
            context_validation = None
            if validation_result.is_valid:
                context_validation = await self.validator.validate_user_token_context(token)
            
            results[token_name] = {
                "validation": {
                    "is_valid": validation_result.is_valid,
                    "token_type": validation_result.token_type,
                    "errors": validation_result.errors,
                    "warnings": validation_result.warnings,
                    "security_score": validation_result.security_score
                },
                "security": security_analysis,
                "context": context_validation
            }
        
        # Performance benchmarks
        logger.info("🏃 Running performance benchmarks...")
        performance_results = self.validator.benchmark_token_operations(iterations=50)
        results["performance"] = performance_results
        
        return results
    
    def generate_validation_report(self, results: Dict[str, Any]) -> str:
        """Generate comprehensive validation report"""
        report_lines = []
        report_lines.append("=" * 70)
        report_lines.append("JWT VALIDATION & SECURITY ANALYSIS REPORT")
        report_lines.append("=" * 70)
        report_lines.append(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # Token validation results
        report_lines.append("TOKEN VALIDATION RESULTS:")
        report_lines.append("-" * 50)
        
        for token_name, result in results.items():
            if token_name == "performance":
                continue
                
            token_display = token_name.replace("_", " ").title()
            validation = result["validation"]
            security = result["security"]
            
            status = "✅ VALID" if validation["is_valid"] else "❌ INVALID"
            report_lines.append(f"{status} {token_display} Token")
            report_lines.append(f"         Type: {validation['token_type']}")
            report_lines.append(f"         Security Score: {validation['security_score']:.2f}/1.0")
            report_lines.append(f"         Security Level: {security['overall_security'].title()}")
            
            if validation["errors"]:
                report_lines.append(f"         Errors: {'; '.join(validation['errors'])}")
            
            if validation["warnings"]:
                report_lines.append(f"         Warnings: {'; '.join(validation['warnings'])}")
            
            if security["vulnerabilities"]:
                report_lines.append(f"         Vulnerabilities: {'; '.join(security['vulnerabilities'])}")
            
            report_lines.append("")
        
        # Performance results
        if "performance" in results:
            perf = results["performance"]
            report_lines.append("PERFORMANCE BENCHMARK RESULTS:")
            report_lines.append("-" * 50)
            
            for op_name, op_result in perf.items():
                if op_name in ["iterations", "overall_performance"]:
                    continue
                    
                op_display = op_name.replace("_", " ").title()
                status = "✅ PASSED" if op_result["passed"] else "❌ FAILED"
                avg_time = op_result["average_ms"]
                target_time = op_result["target_ms"]
                
                report_lines.append(f"{status} {op_display}")
                report_lines.append(f"         Average: {avg_time:.2f}ms (target: <{target_time}ms)")
                report_lines.append(f"         Range: {op_result['min_ms']:.2f}ms - {op_result['max_ms']:.2f}ms")
                report_lines.append("")
            
            overall_status = "✅ EXCELLENT" if perf["overall_performance"]["passed"] else "⚠️  NEEDS IMPROVEMENT"
            report_lines.append(f"Overall Performance: {overall_status}")
        
        report_lines.append("")
        report_lines.append("=" * 70)
        
        return "\n".join(report_lines)


async def main():
    """Main function for JWT validation utilities"""
    logger.info("🚀 TalentForge Pro - JWT Validation Utilities")
    logger.info("=" * 60)
    
    test_suite = TokenTestSuite()
    
    try:
        # Run comprehensive tests
        results = await test_suite.run_comprehensive_tests()
        
        # Generate and display report
        report = test_suite.generate_validation_report(results)
        print("\n" + report)
        
        # Save report to file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = Path(__file__).parent.parent / "test" / f"jwt_validation_report_{timestamp}.txt"
        report_file.parent.mkdir(exist_ok=True)
        report_file.write_text(report)
        logger.info(f"📄 Validation report saved to: {report_file}")
        
        # Check if all critical tests passed
        valid_tokens = ["valid_access", "valid_refresh"]
        all_valid = all(
            results[token]["validation"]["is_valid"] 
            for token in valid_tokens 
            if token in results
        )
        
        performance_passed = results.get("performance", {}).get("overall_performance", {}).get("passed", False)
        
        success = all_valid and performance_passed
        logger.info(f"🎯 Overall validation: {'✅ PASSED' if success else '❌ FAILED'}")
        
        return success
        
    except Exception as e:
        logger.error(f"❌ JWT validation failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)