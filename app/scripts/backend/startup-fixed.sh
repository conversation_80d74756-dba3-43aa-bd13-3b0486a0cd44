#!/bin/bash
set -e

echo "Starting TalentForge Pro Backend..."

# Wait for PostgreSQL to be ready
echo "Waiting for PostgreSQL..."
while ! pg_isready -h ${POSTGRES_SERVER:-postgres} -p 5432 -U ${POSTGRES_USER:-postgres}; do
  echo "PostgreSQL is unavailable - sleeping"
  sleep 2
done

echo "PostgreSQL is ready!"

# Run database migrations with heads handling
echo "Running database migrations..."
# Try to upgrade to heads (multiple heads) instead of head (single)
alembic upgrade heads || {
    echo "Warning: Migration failed, attempting to merge heads..."
    # If there are multiple heads, try to merge them
    alembic merge -m "auto_merge_heads" heads || echo "Could not auto-merge heads"
    # Try upgrade again
    alembic upgrade heads || echo "Migration still failed, continuing anyway..."
}

# Initialize database with seed data
echo "Initializing database with seed data..."
python scripts/init_db.py || echo "Seed data initialization failed or already done"

echo "Database initialization completed!"

# Start the application
echo "Starting FastAPI application..."
exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload