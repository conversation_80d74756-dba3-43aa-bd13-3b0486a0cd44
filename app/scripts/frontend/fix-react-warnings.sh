#!/bin/bash

# Fix common React DevTools warnings and development issues
# Usage: ./fix-react-warnings.sh [options]
#
# Options:
#   --clear-cache    Clear all caches (Next.js, node_modules, etc.)
#   --fix-imports    Fix unused imports with ESLint
#   --check-keys     Check for missing keys in map iterations
#   --all            Run all fixes (default)

set -e

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Change to frontend directory
cd "$(dirname "$0")/../../frontend"

echo -e "${GREEN}🔧 React DevTools Warning Fixer${NC}"
echo "================================"

# Parse command line arguments
CLEAR_CACHE=false
FIX_IMPORTS=false
CHECK_KEYS=false
ALL=true

if [ $# -gt 0 ]; then
    ALL=false
    for arg in "$@"; do
        case $arg in
            --clear-cache)
                CLEAR_CACHE=true
                ;;
            --fix-imports)
                FIX_IMPORTS=true
                ;;
            --check-keys)
                CHECK_KEYS=true
                ;;
            --all)
                ALL=true
                ;;
            --help|-h)
                echo "Usage: $0 [options]"
                echo ""
                echo "Options:"
                echo "  --clear-cache    Clear all caches (Next.js, node_modules, etc.)"
                echo "  --fix-imports    Fix unused imports with ESLint"
                echo "  --check-keys     Check for missing keys in map iterations"
                echo "  --all            Run all fixes (default)"
                echo "  --help, -h       Show this help message"
                exit 0
                ;;
            *)
                echo -e "${RED}Unknown option: $arg${NC}"
                echo "Usage: $0 [--clear-cache] [--fix-imports] [--check-keys] [--all] [--help]"
                exit 1
                ;;
        esac
    done
fi

# Set all flags if --all is specified
if [ "$ALL" = true ]; then
    CLEAR_CACHE=true
    FIX_IMPORTS=true
    CHECK_KEYS=true
fi

# Function to check for missing display names
check_display_names() {
    echo -e "\n${GREEN}✅ Checking for missing display names...${NC}"
    local found_issues=false
    
    while IFS= read -r file; do
        if grep -q "forwardRef" "$file" && ! grep -q "displayName" "$file"; then
            echo -e "  ${YELLOW}⚠️  Missing displayName in: $file${NC}"
            found_issues=true
        fi
    done < <(find ./components ./app -name "*.tsx" -type f 2>/dev/null)
    
    if [ "$found_issues" = false ]; then
        echo "  ✓ All forwardRef components have display names"
    fi
}

# Function to check for missing keys in map iterations
check_missing_keys() {
    echo -e "\n${GREEN}✅ Checking for missing keys in map iterations...${NC}"
    local found_issues=false
    
    # More sophisticated key checking
    while IFS=: read -r file line_num line_content; do
        # Skip if line already has key prop or is a comment
        if ! echo "$line_content" | grep -qE "(key=|key:|\s*//|\s*\*)"; then
            # Check if this is likely a JSX element being mapped
            if echo "$line_content" | grep -qE "(<[A-Z]|<div|<span|<li|<tr)"; then
                echo -e "  ${YELLOW}⚠️  Potential missing key at $file:$line_num${NC}"
                found_issues=true
            fi
        fi
    done < <(grep -rn "\.map(" ./components ./app --include="*.tsx" --include="*.jsx" 2>/dev/null | head -20)
    
    if [ "$found_issues" = false ]; then
        echo "  ✓ No missing keys detected"
    fi
}

# Function to fix unused imports
fix_unused_imports() {
    echo -e "\n${GREEN}✅ Fixing unused imports...${NC}"
    
    if command -v npx &> /dev/null; then
        echo "  Running ESLint auto-fix..."
        npx eslint --fix --ext .ts,.tsx,.js,.jsx . \
            --rule 'no-unused-vars: warn' \
            --rule '@typescript-eslint/no-unused-vars: warn' \
            --quiet 2>/dev/null || true
        echo "  ✓ ESLint fixes applied"
    else
        echo -e "  ${YELLOW}⚠️  npx not found. Skipping ESLint fixes${NC}"
    fi
}

# Function to clear caches
clear_caches() {
    echo -e "\n${GREEN}✅ Clearing Next.js and build caches...${NC}"
    
    # Clear Next.js build cache
    if [ -d ".next" ]; then
        rm -rf .next
        echo "  ✓ Cleared .next directory"
    fi
    
    # Clear other caches
    [ -d ".next-cache" ] && rm -rf .next-cache && echo "  ✓ Cleared .next-cache"
    [ -d "node_modules/.cache" ] && rm -rf node_modules/.cache && echo "  ✓ Cleared node_modules/.cache"
    [ -d ".turbo" ] && rm -rf .turbo && echo "  ✓ Cleared .turbo cache"
    
    echo "  ✓ All caches cleared"
}

# Function to check React strict mode
check_strict_mode() {
    echo -e "\n${GREEN}✅ Checking React strict mode...${NC}"
    
    if [ -f "next.config.ts" ] || [ -f "next.config.js" ]; then
        config_file=$([ -f "next.config.ts" ] && echo "next.config.ts" || echo "next.config.js")
        
        if grep -q "reactStrictMode: true" "$config_file"; then
            echo -e "  ${YELLOW}ℹ️  React strict mode is enabled${NC}"
            echo "     This causes intentional double renders in development for detecting side effects"
            echo "     Set 'reactStrictMode: false' in $config_file if you want to disable it"
        else
            echo "  ✓ React strict mode is not enabled"
        fi
    fi
}

# Function to check for console statements
check_console_statements() {
    echo -e "\n${GREEN}✅ Checking for console statements...${NC}"
    
    local count=$(grep -r "console\.\(log\|error\|warn\|debug\)" ./components ./app \
        --include="*.tsx" --include="*.ts" --include="*.jsx" --include="*.js" \
        --exclude-dir=node_modules 2>/dev/null | wc -l)
    
    if [ "$count" -gt 0 ]; then
        echo -e "  ${YELLOW}⚠️  Found $count console statements${NC}"
        echo "     Consider removing or using a proper logging library in production"
    else
        echo "  ✓ No console statements found"
    fi
}

# Main execution
echo -e "\n${GREEN}Starting React warning checks...${NC}"

# Always run these checks
check_display_names
check_strict_mode
check_console_statements

# Conditional operations
[ "$CHECK_KEYS" = true ] && check_missing_keys
[ "$FIX_IMPORTS" = true ] && fix_unused_imports
[ "$CLEAR_CACHE" = true ] && clear_caches

echo -e "\n${GREEN}✨ React DevTools warning check complete!${NC}"
echo ""
echo "📝 Next steps:"
echo "  1. Run 'pnpm dev' to restart the development server"
echo "  2. Check browser console for any remaining warnings"
echo "  3. Use React DevTools Profiler to identify performance issues"
echo "  4. Consider running 'pnpm type-check' to catch type errors"
echo ""

# Return to original directory
cd - > /dev/null