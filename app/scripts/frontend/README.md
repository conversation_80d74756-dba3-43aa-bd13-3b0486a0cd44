# Frontend Scripts

Frontend development and maintenance scripts for TalentForge Pro.

## Available Scripts

### fix-react-warnings.sh
Fix common React DevTools warnings and development issues.

**Usage:**
```bash
# Run all fixes (default)
./fix-react-warnings.sh

# Clear caches only
./fix-react-warnings.sh --clear-cache

# Fix unused imports
./fix-react-warnings.sh --fix-imports

# Check for missing keys in map iterations
./fix-react-warnings.sh --check-keys

# Run all fixes explicitly
./fix-react-warnings.sh --all
```

**Features:**
- ✅ Check for missing display names in forwardRef components
- ✅ Detect missing keys in map iterations
- ✅ Fix unused imports with ESLint
- ✅ Clear Next.js and build caches
- ✅ Check React strict mode status
- ✅ Detect console statements in code

## Running Scripts

All scripts should be run from the project root or app directory:

```bash
# From project root
./app/scripts/frontend/fix-react-warnings.sh

# From app directory
./scripts/frontend/fix-react-warnings.sh

# From frontend directory
../scripts/frontend/fix-react-warnings.sh
```

## Adding New Scripts

When adding new frontend scripts:
1. Place them in this directory (`app/scripts/frontend/`)
2. Make them executable: `chmod +x script-name.sh`
3. Add documentation to this README
4. Use proper error handling and colored output for better UX
5. Include help/usage information in the script

## Common Tasks

### Clear All Caches
```bash
./fix-react-warnings.sh --clear-cache
```

### Full Development Reset
```bash
# Clear caches and fix common issues
./fix-react-warnings.sh --all

# Reinstall dependencies
cd app/frontend && pnpm install

# Start development server
pnpm dev
```

### Pre-commit Checks
```bash
# Check for common issues before committing
./fix-react-warnings.sh --check-keys --fix-imports
```