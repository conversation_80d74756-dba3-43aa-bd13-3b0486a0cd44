#!/bin/bash
# Authentication Test Runner Script
# Simple script to run auth tests with different options

set -e

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
MODE="smoke"
VERBOSE=false
COVERAGE=false
FAST=false

# Help function
show_help() {
    echo "Authentication Test Runner"
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -m, --mode MODE        Test mode: smoke, verification, all, ci (default: smoke)"
    echo "  -f, --fast             Run only smoke tests for quick feedback"
    echo "  -v, --verbose          Verbose output"
    echo "  -c, --coverage         Run with coverage report"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 -m smoke            # Quick smoke tests"
    echo "  $0 -m all -v           # All tests with verbose output"
    echo "  $0 -f                  # Fast mode (smoke tests only)"
    echo "  $0 -m ci               # CI/CD tests only"
}

# Parse arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -m|--mode)
            MODE="$2"
            shift 2
            ;;
        -f|--fast)
            MODE="smoke"
            FAST=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -c|--coverage)
            COVERAGE=true
            shift
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Validate mode
case $MODE in
    smoke|verification|all|ci)
        ;;
    *)
        echo -e "${RED}Invalid mode: $MODE${NC}"
        echo "Valid modes: smoke, verification, all, ci"
        exit 1
        ;;
esac

echo -e "${BLUE}🚀 TalentForge Pro - Authentication Test Runner${NC}"
echo -e "Mode: ${YELLOW}$MODE${NC}"
echo -e "Directory: $(pwd)"
echo "----------------------------------------"

# Set pytest arguments based on options
PYTEST_ARGS=()

if [ "$VERBOSE" = true ]; then
    PYTEST_ARGS+=("-v")
else
    PYTEST_ARGS+=("-q")
fi

# Add markers for CI mode
if [ "$MODE" = "ci" ]; then
    PYTEST_ARGS+=("-m" "smoke or integration or regression")
fi

# Run tests based on mode
case $MODE in
    smoke)
        echo -e "${GREEN}🔥 Running Smoke Tests${NC}"
        python -m pytest tests/test_auth_smoke.py "${PYTEST_ARGS[@]}" --tb=short
        ;;
    verification)
        echo -e "${GREEN}🔍 Running Verification Tests${NC}"
        python -m pytest tests/test_auth_verification.py "${PYTEST_ARGS[@]}" --tb=short
        ;;
    ci)
        echo -e "${GREEN}🚀 Running CI/CD Tests${NC}"
        python -m pytest tests/test_auth_ci.py "${PYTEST_ARGS[@]}" --tb=short
        ;;
    all)
        echo -e "${GREEN}🧪 Running All Authentication Tests${NC}"
        if [ "$COVERAGE" = true ]; then
            python -m pytest tests/test_auth_*.py "${PYTEST_ARGS[@]}" \
                --cov=app.api.v1.auth \
                --cov=app.api.deps \
                --cov=app.core.security \
                --cov-report=term-missing \
                --tb=short
        else
            python -m pytest tests/test_auth_*.py "${PYTEST_ARGS[@]}" --tb=short
        fi
        ;;
esac

exit_code=$?

if [ $exit_code -eq 0 ]; then
    echo -e "\n${GREEN}✅ Authentication tests completed successfully${NC}"
else
    echo -e "\n${RED}❌ Authentication tests failed${NC}"
fi

exit $exit_code