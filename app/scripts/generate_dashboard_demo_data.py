#!/usr/bin/env python3
"""
Generate realistic demo data for dashboard testing
Creates candidates with assessments and positions to populate dashboard with real data
"""
import os
import sys
import asyncio
import random
from datetime import datetime, timedelta
from faker import Faker
import uuid

# Add the project root to sys.path
sys.path.append('/home/<USER>/source_code/talent_forge_pro/app/backend')

from app.core.database import get_async_session
from app.models.candidate import Candidate, CandidateStatus
from app.models.assessment import CandidateAssessment
from app.models.position import Position
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

# Initialize Faker with Chinese locale
fake = Faker(['zh_CN', 'en_US'])
Faker.seed(2025)
random.seed(2025)

# Configuration
NUM_CANDIDATES = 500
NUM_POSITIONS = 25
NUM_ASSESSMENTS = 400  # Not all candidates have assessments

# Skill categories for realistic distribution
SKILL_CATEGORIES = [
    "AI/ML", "前端开发", "后端开发", "数据分析", "产品管理", "其他"
]

# Position departments and roles
DEPARTMENTS = [
    "技术部", "产品部", "市场部", "人力资源", "运营部", "财务部"
]

TECH_POSITIONS = [
    "高级软件工程师", "全栈开发工程师", "数据科学家", "AI算法工程师",
    "前端开发工程师", "后端开发工程师", "DevOps工程师", "测试工程师"
]

NON_TECH_POSITIONS = [
    "产品经理", "项目经理", "市场专员", "人力资源专员", 
    "运营专员", "财务分析师", "商务拓展"
]


async def generate_candidates(session: AsyncSession, num_candidates: int):
    """Generate realistic candidate data"""
    print(f"生成 {num_candidates} 个候选人...")
    
    candidates = []
    for i in range(num_candidates):
        # Generate candidate over the last 90 days with higher recent activity
        days_ago = random.choices(
            range(90),
            weights=[3.0 if d < 30 else 2.0 if d < 60 else 1.0 for d in range(90)]
        )[0]
        created_at = datetime.now() - timedelta(days=days_ago, 
                                               hours=random.randint(0, 23),
                                               minutes=random.randint(0, 59))
        
        # Use original schema fields
        candidate = Candidate(
            name=fake.name(),
            email=fake.email(),
            phone=fake.phone_number()[:20],
            gender=random.choice(["男", "女", "其他"]),
            current_position=random.choice(TECH_POSITIONS + NON_TECH_POSITIONS),
            current_company=fake.company(),
            years_of_experience=random.randint(1, 15),
            current_salary=random.randint(10, 80),  # 10万-80万
            expected_salary=random.randint(15, 100),
            status=random.choice(list(CandidateStatus)),
            created_at=created_at,
            updated_at=created_at
        )
        candidates.append(candidate)
    
    session.add_all(candidates)
    await session.commit()
    print(f"✅ 成功创建 {len(candidates)} 个候选人")
    return candidates


async def generate_assessments(session: AsyncSession, candidates: list, num_assessments: int):
    """Generate realistic assessment data"""
    print(f"生成 {num_assessments} 个评估记录...")
    
    # Select random candidates for assessments
    assessed_candidates = random.sample(candidates, min(num_assessments, len(candidates)))
    
    assessments = []
    for candidate in assessed_candidates:
        # Assessment happens 1-30 days after candidate creation
        assessment_delay = random.randint(1, 30)
        assessed_at = candidate.created_at + timedelta(days=assessment_delay,
                                                      hours=random.randint(0, 23))
        
        # Generate realistic five-dimensional scores
        # Some correlation between dimensions but still random
        base_ability = random.uniform(60, 95)  # Overall ability level
        
        digital_literacy = max(0, min(100, random.normalvariate(base_ability, 10)))
        industry_skills = max(0, min(100, random.normalvariate(base_ability, 12)))
        position_skills = max(0, min(100, random.normalvariate(base_ability, 8)))
        innovation = max(0, min(100, random.normalvariate(base_ability, 15)))
        learning_potential = max(0, min(100, random.normalvariate(base_ability, 10)))
        
        # Calculate DCI score (weighted average)
        dci_score = (
            digital_literacy * 0.20 +
            industry_skills * 0.25 +
            position_skills * 0.30 +
            innovation * 0.15 +
            learning_potential * 0.10
        )
        
        # Calculate percentile (simplified)
        percentile = min(99, max(1, (dci_score - 50) * 2))
        
        assessment = CandidateAssessment(
            candidate_id=candidate.id,
            digital_literacy_score=round(digital_literacy, 1),
            industry_skills_score=round(industry_skills, 1),
            position_skills_score=round(position_skills, 1),
            innovation_score=round(innovation, 1),
            learning_potential_score=round(learning_potential, 1),
            dci_score=round(dci_score, 1),
            overall_percentile=round(percentile, 1),
            assessment_details={
                "test_duration_minutes": random.randint(15, 45),
                "question_count": random.randint(20, 50),
                "difficulty_level": random.choice(["初级", "中级", "高级"]),
                "completion_rate": random.uniform(0.8, 1.0)
            },
            strengths=[
                random.choice(["逻辑思维", "技术能力", "沟通能力", "创新思维", "学习能力"])
                for _ in range(random.randint(2, 4))
            ],
            improvement_areas=[
                random.choice(["项目管理", "领导力", "团队协作", "技术广度", "业务理解"])
                for _ in range(random.randint(1, 3))
            ],
            assessed_at=assessed_at,
            updated_at=assessed_at
        )
        assessments.append(assessment)
    
    session.add_all(assessments)
    await session.commit()
    print(f"✅ 成功创建 {len(assessments)} 个评估记录")
    return assessments


async def generate_positions(session: AsyncSession, num_positions: int):
    """Generate realistic position data"""
    print(f"生成 {num_positions} 个职位...")
    
    positions = []
    for i in range(num_positions):
        # Generate positions over the last 60 days
        days_ago = random.randint(0, 60)
        created_at = datetime.now() - timedelta(days=days_ago,
                                               hours=random.randint(0, 23))
        
        department = random.choice(DEPARTMENTS)
        if department == "技术部":
            title = random.choice(TECH_POSITIONS)
        else:
            title = random.choice(NON_TECH_POSITIONS)
        
        position = Position(
            title=title,
            department=department,
            description=f"{title}职位，负责{department}相关工作。" + fake.text(max_nb_chars=200),
            requirements=fake.text(max_nb_chars=300),
            salary_min=random.randint(15, 50),
            salary_max=random.randint(50, 120),
            status=random.choice(["draft", "open", "closed"]),
            urgency=random.choice(["low", "normal", "high", "critical"]),
            location=random.choice(["北京", "上海", "深圳", "杭州", "广州", "成都"]),
            job_type=random.choice(["full-time", "part-time", "contract"]),
            remote_ok=random.choice([True, False]),
            created_at=created_at,
            updated_at=created_at
        )
        positions.append(position)
    
    session.add_all(positions)
    await session.commit()
    print(f"✅ 成功创建 {len(positions)} 个职位")
    return positions


async def print_summary(session: AsyncSession):
    """Print summary statistics"""
    print("\n📊 数据库统计摘要:")
    
    # Count candidates
    candidate_count = await session.execute(select(func.count(Candidate.id)))
    total_candidates = candidate_count.scalar()
    
    # Count assessments
    assessment_count = await session.execute(select(func.count(CandidateAssessment.id)))
    total_assessments = assessment_count.scalar()
    
    # Count positions
    position_count = await session.execute(select(func.count(Position.id)))
    total_positions = position_count.scalar()
    
    # Average DCI score
    avg_dci = await session.execute(select(func.avg(CandidateAssessment.dci_score)))
    average_dci = avg_dci.scalar() or 0
    
    # Recent activity (last 7 days)
    week_ago = datetime.now() - timedelta(days=7)
    recent_candidates = await session.execute(
        select(func.count(Candidate.id)).where(Candidate.created_at >= week_ago)
    )
    recent_count = recent_candidates.scalar()
    
    print(f"  • 总候选人: {total_candidates}")
    print(f"  • 总评估: {total_assessments}")
    print(f"  • 总职位: {total_positions}")
    print(f"  • 平均 DCI 分数: {average_dci:.1f}")
    print(f"  • 最近7天新增候选人: {recent_count}")
    
    completion_rate = (total_assessments / max(total_candidates, 1)) * 100
    print(f"  • 评估完成率: {completion_rate:.1f}%")


async def main():
    """Main function to generate all demo data"""
    print("🚀 开始生成 TalentForge Pro 仪表盘演示数据")
    print("=" * 60)
    
    try:
        # Get database session
        async for session in get_async_session():
            # Check if we already have data
            existing_candidates = await session.execute(select(func.count(Candidate.id)))
            if existing_candidates.scalar() > 100:
                print("⚠️  数据库中已有大量候选人数据，跳过生成")
                await print_summary(session)
                return
            
            # Generate demo data
            candidates = await generate_candidates(session, NUM_CANDIDATES)
            assessments = await generate_assessments(session, candidates, NUM_ASSESSMENTS)
            positions = await generate_positions(session, NUM_POSITIONS)
            
            # Print summary
            await print_summary(session)
            
            print("\n✅ 演示数据生成完成!")
            print("🎯 现在可以访问 http://localhost:8088/dashboard 查看真实数据")
            
    except Exception as e:
        print(f"❌ 生成演示数据时出错: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())