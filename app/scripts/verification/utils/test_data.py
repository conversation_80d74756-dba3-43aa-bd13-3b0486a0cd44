"""
Test data generation utilities for verification framework
"""
import json
import os
import random
from typing import Dict, List, Any, Optional
from datetime import datetime, timezone
import tempfile
from pathlib import Path


class TestDataGenerator:
    """Generate test data for verification testing"""
    
    def __init__(self, data_dir: str = "app/scripts/verification/data"):
        self.data_dir = Path(data_dir)
        self.test_resumes_dir = self.data_dir / "test_resumes"
        self.expected_results_dir = self.data_dir / "expected_results"
        
        # Ensure directories exist
        self.test_resumes_dir.mkdir(parents=True, exist_ok=True)
        self.expected_results_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_test_resume_text(self, complexity: str = "simple") -> str:
        """Generate text content for test resumes"""
        
        templates = {
            "simple": """
John Doe
Software Engineer

Contact Information:
Email: <EMAIL>
Phone: ******-0123
Location: San Francisco, CA

Experience:
• 3 years of software development
• Python programming
• Web development with FastAPI
• Database management

Skills:
Python, FastAPI, PostgreS<PERSON>, Docker, Git

Education:
Bachelor of Computer Science
University of Technology, 2020
            """.strip(),
            
            "complex": """
<PERSON>
Senior Full-Stack Developer & AI Engineer

Contact Information:
Email: <EMAIL>
Phone: ******-0156
LinkedIn: linkedin.com/in/janesmith
Location: Seattle, WA

Professional Summary:
Experienced full-stack developer with 7+ years in web development and 3+ years in AI/ML. 
Proven track record in building scalable applications using Python, React, and cloud technologies.
Passionate about leveraging AI to solve complex business problems.

Technical Skills:
• Programming Languages: Python (Expert), TypeScript/JavaScript (Advanced), Go (Intermediate)
• Web Frameworks: FastAPI, Django, React, Next.js, Vue.js
• Databases: PostgreSQL, MongoDB, Redis, Vector Databases (Pinecone, Weaviate)
• AI/ML: TensorFlow, PyTorch, Hugging Face, OpenAI API, LangChain
• Cloud Platforms: AWS (EC2, S3, Lambda), Google Cloud Platform, Docker, Kubernetes
• Tools: Git, CI/CD (GitHub Actions), Monitoring (Prometheus, Grafana)

Professional Experience:

Senior AI Engineer | TechCorp Inc. | 2022 - Present
• Developed and deployed ML models for customer recommendation systems
• Built real-time vector search infrastructure using PostgreSQL with pgvector
• Implemented LLM-powered chatbots with 95% accuracy using OpenAI and custom models
• Led migration of legacy systems to microservices architecture
• Improved system performance by 40% through optimization and caching strategies

Full-Stack Developer | StartupXYZ | 2019 - 2022
• Built responsive web applications using React and FastAPI
• Designed and implemented RESTful APIs serving 10k+ daily active users
• Implemented OAuth2 authentication and role-based access control
• Created automated testing suites achieving 90%+ code coverage
• Collaborated with UX team to improve user engagement by 25%

Software Developer | WebSolutions Ltd | 2017 - 2019
• Developed e-commerce platforms using Django and PostgreSQL
• Implemented payment processing integration with Stripe and PayPal
• Built automated deployment pipelines using Docker and Jenkins
• Provided technical support and maintenance for client applications

Education:
Master of Science in Computer Science | Stanford University | 2017
• Specialization: Artificial Intelligence and Machine Learning
• Thesis: "Deep Learning Applications in Natural Language Processing"
• GPA: 3.8/4.0

Bachelor of Science in Software Engineering | UC Berkeley | 2015
• Minor: Data Science
• Relevant Coursework: Algorithms, Database Systems, Software Architecture
• Dean's List: Fall 2014, Spring 2015

Certifications:
• AWS Certified Solutions Architect - Associate (2023)
• Google Cloud Professional Machine Learning Engineer (2022)
• Certified Kubernetes Administrator (CKA) (2021)

Projects:
• ResumeAI: Built an AI-powered resume parsing and matching system using FastAPI, 
  PostgreSQL with pgvector, and OpenAI API. Achieved 92% accuracy in skill extraction.
• SmartChat: Developed a customer service chatbot using LangChain and Streamlit,
  reducing response time by 60% and improving customer satisfaction.
• CloudMigrator: Created a tool for automated cloud migration with zero downtime,
  used by 5+ enterprise clients.

Languages:
• English (Native)
• Spanish (Conversational)
• Mandarin (Basic)
            """.strip(),
            
            "multilingual": """
张伟 (Zhang Wei)
高级软件工程师 | Senior Software Engineer

联系方式 | Contact Information:
邮箱 | Email: <EMAIL>
电话 | Phone: +86-138-0013-8000
地址 | Location: 北京，中国 | Beijing, China

专业技能 | Technical Skills:
• 编程语言 | Programming: Python, Java, JavaScript/TypeScript
• Web框架 | Web Frameworks: FastAPI, Spring Boot, React, Vue.js
• 数据库 | Databases: PostgreSQL, MySQL, MongoDB, Redis
• 云平台 | Cloud: 阿里云 (Alibaba Cloud), AWS, Docker, Kubernetes

工作经验 | Professional Experience:

高级工程师 | Senior Engineer | 科技公司 Tech Corp | 2020 - Present
• 负责微服务架构设计和实施 | Led microservices architecture design and implementation
• 开发AI驱动的人才匹配系统 | Developed AI-powered talent matching system
• 优化系统性能，响应时间减少50% | Optimized system performance, reduced response time by 50%

软件工程师 | Software Engineer | 互联网公司 Internet Co. | 2018 - 2020  
• 开发RESTful API服务 | Developed RESTful API services
• 实现用户认证和权限管理 | Implemented user authentication and permission management

教育背景 | Education:
计算机科学硕士 | Master of Computer Science | 清华大学 Tsinghua University | 2018
学士学位软件工程 | Bachelor of Software Engineering | 北京理工大学 Beijing Institute of Technology | 2016
            """.strip()
        }
        
        return templates.get(complexity, templates["simple"])
    
    def generate_test_resume_file(self, filename: str, content_type: str = "text", 
                                complexity: str = "simple") -> str:
        """Generate a test resume file"""
        file_path = self.test_resumes_dir / filename
        
        if content_type == "text":
            content = self.generate_test_resume_text(complexity)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
                
        elif content_type == "json":
            # Generate structured resume data
            resume_data = self.generate_structured_resume_data(complexity)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(resume_data, f, indent=2, ensure_ascii=False)
        
        return str(file_path)
    
    def generate_structured_resume_data(self, complexity: str = "simple") -> Dict[str, Any]:
        """Generate structured resume data for testing"""
        
        base_data = {
            "personal_info": {
                "name": "John Doe",
                "email": "<EMAIL>", 
                "phone": "******-0123",
                "location": "San Francisco, CA"
            },
            "skills": ["Python", "FastAPI", "PostgreSQL", "Docker", "Git"],
            "experience": [
                {
                    "company": "TechCorp",
                    "position": "Software Engineer",
                    "duration": "2021 - Present",
                    "description": "Developed web applications using Python and FastAPI"
                }
            ],
            "education": [
                {
                    "degree": "Bachelor of Computer Science",
                    "institution": "University of Technology", 
                    "year": "2020"
                }
            ]
        }
        
        if complexity == "complex":
            base_data.update({
                "certifications": [
                    "AWS Certified Developer",
                    "Google Cloud Professional"
                ],
                "projects": [
                    {
                        "name": "AI Resume Parser",
                        "technologies": ["Python", "FastAPI", "OpenAI", "PostgreSQL"],
                        "description": "Built an AI-powered resume parsing system"
                    }
                ],
                "languages": ["English (Native)", "Spanish (Conversational)"]
            })
            
            # Add more detailed experience
            base_data["experience"].extend([
                {
                    "company": "StartupXYZ", 
                    "position": "Full-Stack Developer",
                    "duration": "2019 - 2021",
                    "description": "Built responsive web applications using React and Node.js"
                }
            ])
        
        elif complexity == "multilingual":
            base_data = {
                "personal_info": {
                    "name": "张伟 (Zhang Wei)",
                    "email": "<EMAIL>",
                    "phone": "+86-138-0013-8000",
                    "location": "北京，中国 (Beijing, China)"
                },
                "skills": ["Python", "Java", "JavaScript", "微服务架构", "云计算"],
                "experience": [
                    {
                        "company": "科技公司 (Tech Corp)",
                        "position": "高级工程师 (Senior Engineer)",
                        "duration": "2020 - Present",
                        "description": "负责AI系统开发和架构设计"
                    }
                ],
                "education": [
                    {
                        "degree": "计算机科学硕士 (Master of Computer Science)",
                        "institution": "清华大学 (Tsinghua University)",
                        "year": "2018"
                    }
                ],
                "languages": ["中文 (Chinese - Native)", "English (Fluent)", "日本语 (Japanese - Basic)"]
            }
        
        return base_data
    
    def generate_test_vectors(self, dimension: int = 1024, count: int = 10) -> List[List[float]]:
        """Generate test vectors for vector operations"""
        vectors = []
        
        for i in range(count):
            # Generate random normalized vectors
            vector = [random.random() for _ in range(dimension)]
            # Normalize to unit vector
            magnitude = sum(x**2 for x in vector) ** 0.5
            if magnitude > 0:
                vector = [x / magnitude for x in vector]
            vectors.append(vector)
        
        return vectors
    
    def generate_candidate_test_data(self, count: int = 5) -> List[Dict[str, Any]]:
        """Generate test candidate data"""
        candidates = []
        
        names = ["John Doe", "Jane Smith", "Alice Johnson", "Bob Wilson", "Carol Brown"]
        titles = ["Software Engineer", "Data Scientist", "Product Manager", "DevOps Engineer", "UX Designer"]
        skills_sets = [
            ["Python", "FastAPI", "PostgreSQL"],
            ["React", "TypeScript", "Node.js"],
            ["Machine Learning", "TensorFlow", "Python"],
            ["Kubernetes", "Docker", "AWS"],
            ["UI/UX", "Figma", "HTML/CSS"]
        ]
        
        for i in range(min(count, len(names))):
            candidate = {
                "name": names[i],
                "email": f"{names[i].lower().replace(' ', '.')}@example.com",
                "phone": f"******-{random.randint(1000, 9999)}",
                "title": titles[i],
                "skills": skills_sets[i],
                "experience_years": random.randint(1, 10),
                "location": random.choice(["San Francisco, CA", "New York, NY", "Seattle, WA"]),
                "resume_text": self.generate_test_resume_text("simple")
            }
            candidates.append(candidate)
        
        return candidates
    
    def generate_job_test_data(self, count: int = 3) -> List[Dict[str, Any]]:
        """Generate test job position data"""
        jobs = []
        
        positions = [
            {
                "title": "Senior Python Developer",
                "required_skills": ["Python", "FastAPI", "PostgreSQL", "Docker"],
                "experience_years": 5,
                "department": "Engineering"
            },
            {
                "title": "Frontend Developer", 
                "required_skills": ["React", "TypeScript", "Next.js", "CSS"],
                "experience_years": 3,
                "department": "Engineering"
            },
            {
                "title": "DevOps Engineer",
                "required_skills": ["Kubernetes", "Docker", "AWS", "CI/CD"],
                "experience_years": 4,
                "department": "Infrastructure"
            }
        ]
        
        for i in range(min(count, len(positions))):
            job = {
                "id": f"test_job_{i+1}",
                "title": positions[i]["title"],
                "description": f"We are looking for a {positions[i]['title']} with expertise in {', '.join(positions[i]['required_skills'][:3])}.",
                "required_skills": positions[i]["required_skills"],
                "preferred_skills": ["Git", "Agile", "Communication"],
                "experience_years": positions[i]["experience_years"],
                "department": positions[i]["department"],
                "location": "San Francisco, CA",
                "status": "active",
                "created_at": datetime.now(timezone.utc).isoformat()
            }
            jobs.append(job)
        
        return jobs
    
    def create_test_file(self, filename: str, content: str, 
                        directory: Optional[str] = None) -> str:
        """Create a test file with specified content"""
        if directory:
            file_path = Path(directory) / filename
            file_path.parent.mkdir(parents=True, exist_ok=True)
        else:
            file_path = self.test_resumes_dir / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return str(file_path)
    
    def create_binary_test_file(self, filename: str, size_kb: int = 100,
                              content_type: str = "application/pdf") -> str:
        """Create a binary test file (simulated PDF/DOC)"""
        file_path = self.test_resumes_dir / filename
        
        # Generate random binary content
        content = os.urandom(size_kb * 1024)
        
        with open(file_path, 'wb') as f:
            f.write(content)
        
        return str(file_path)
    
    def generate_performance_test_data(self, operation_type: str, 
                                     scale: str = "small") -> Dict[str, Any]:
        """Generate data for performance testing"""
        
        scales = {
            "small": {"users": 5, "requests": 50, "concurrent": 3},
            "medium": {"users": 20, "requests": 200, "concurrent": 10},
            "large": {"users": 50, "requests": 500, "concurrent": 25}
        }
        
        scale_config = scales.get(scale, scales["small"])
        
        if operation_type == "api_load_test":
            return {
                "endpoints": [
                    {"url": "/api/v1/health", "method": "GET"},
                    {"url": "/api/v1/auth/me", "method": "GET", "auth_required": True},
                    {"url": "/api/v1/candidates/", "method": "GET", "auth_required": True}
                ],
                "users": scale_config["users"],
                "requests_per_user": scale_config["requests"] // scale_config["users"],
                "concurrent_users": scale_config["concurrent"]
            }
        
        elif operation_type == "vector_search":
            return {
                "search_vectors": self.generate_test_vectors(1024, scale_config["requests"]),
                "concurrent_searches": scale_config["concurrent"],
                "search_queries": scale_config["requests"]
            }
        
        elif operation_type == "file_upload":
            return {
                "file_sizes": [random.randint(50, 500) for _ in range(scale_config["requests"])],
                "concurrent_uploads": scale_config["concurrent"],
                "file_types": ["application/pdf", "application/msword", "text/plain"]
            }
        
        return {}
    
    def save_test_scenario(self, scenario_name: str, test_data: Dict[str, Any]):
        """Save test scenario data to file"""
        scenario_file = self.expected_results_dir / f"{scenario_name}_data.json"
        
        with open(scenario_file, 'w', encoding='utf-8') as f:
            json.dump(test_data, f, indent=2, ensure_ascii=False, default=str)
        
        return str(scenario_file)
    
    def load_test_scenario(self, scenario_name: str) -> Optional[Dict[str, Any]]:
        """Load test scenario data from file"""
        scenario_file = self.expected_results_dir / f"{scenario_name}_data.json"
        
        if scenario_file.exists():
            with open(scenario_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        
        return None
    
    def cleanup_test_files(self):
        """Clean up generated test files"""
        import shutil
        
        # Remove all generated test files
        if self.test_resumes_dir.exists():
            shutil.rmtree(self.test_resumes_dir)
            self.test_resumes_dir.mkdir(parents=True, exist_ok=True)
        
        # Keep expected results but clean up test-specific data
        test_files = self.expected_results_dir.glob("test_*")
        for file_path in test_files:
            if file_path.is_file():
                file_path.unlink()
    
    def get_sample_api_requests(self) -> Dict[str, Dict[str, Any]]:
        """Get sample API requests for testing"""
        return {
            "health_check": {
                "method": "GET",
                "url": "/api/v1/health",
                "headers": {},
                "body": None
            },
            
            "user_login": {
                "method": "POST", 
                "url": "/api/v1/auth/login",
                "headers": {"Content-Type": "application/json"},
                "body": {
                    "username": "<EMAIL>",
                    "password": "test123"
                }
            },
            
            "candidate_list": {
                "method": "GET",
                "url": "/api/v1/candidates/",
                "headers": {"Authorization": "Bearer {token}"},
                "body": None
            },
            
            "position_create": {
                "method": "POST",
                "url": "/api/v1/positions/",
                "headers": {
                    "Content-Type": "application/json", 
                    "Authorization": "Bearer {token}"
                },
                "body": {
                    "title": "Test Position",
                    "description": "Test position for verification",
                    "required_skills": ["Python", "FastAPI"],
                    "department": "Engineering"
                }
            },
            
            "resume_upload": {
                "method": "POST",
                "url": "/api/v1/candidates/upload-resume",
                "headers": {"Authorization": "Bearer {token}"},
                "body": "multipart_form_data"  # Special handling needed
            }
        }


# Singleton instance for easy access
test_data_generator = TestDataGenerator()