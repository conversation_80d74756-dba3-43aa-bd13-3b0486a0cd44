"""
Verification report generation utilities
"""
import json
import os
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from pathlib import Path


class ReportGenerator:
    """Generate comprehensive verification reports in multiple formats"""
    
    def __init__(self, report_directory: str = "app/scripts/verification/reports"):
        self.report_dir = Path(report_directory)
        self.report_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_comprehensive_report(self, verification_results: Dict[str, Any], 
                                    formats: List[str] = ["json", "html", "md"]) -> Dict[str, str]:
        """Generate comprehensive verification report in multiple formats"""
        
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        report_files = {}
        
        for format_type in formats:
            if format_type == "json":
                file_path = self.report_dir / f"verification_report_{timestamp}.json"
                content = self._generate_json_report(verification_results)
            elif format_type == "html":
                file_path = self.report_dir / f"verification_report_{timestamp}.html"
                content = self._generate_html_report(verification_results)
            elif format_type == "md":
                file_path = self.report_dir / f"verification_report_{timestamp}.md"
                content = self._generate_markdown_report(verification_results)
            else:
                continue
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            report_files[format_type] = str(file_path)
        
        return report_files
    
    def _generate_json_report(self, results: Dict[str, Any]) -> str:
        """Generate JSON format report"""
        return json.dumps(results, indent=2, default=str, ensure_ascii=False)
    
    def _generate_markdown_report(self, results: Dict[str, Any]) -> str:
        """Generate Markdown format report"""
        
        # Extract summary information
        summary = results.get("summary", {})
        execution_info = results.get("execution_info", {})
        component_results = results.get("component_results", {})
        integration_results = results.get("integration_results", {})
        business_flow_results = results.get("business_flow_results", {})
        
        lines = [
            "# TalentForge Pro - System Verification Report",
            "",
            f"**Generated**: {execution_info.get('timestamp', datetime.now(timezone.utc).isoformat())}",
            f"**Verification Mode**: {execution_info.get('mode', 'standard')}",
            f"**Total Duration**: {execution_info.get('total_duration_ms', 0) / 1000:.1f} seconds",
            "",
            "## Executive Summary",
            "",
            f"- **Overall Status**: {summary.get('overall_status', 'unknown').upper()}",
            f"- **Health Score**: {summary.get('overall_health_score', 0):.1f}%",
            f"- **Components Tested**: {summary.get('total_components', 0)}",
            f"- **Integration Points**: {summary.get('total_integrations', 0)}", 
            f"- **Business Flows**: {summary.get('total_business_flows', 0)}",
            f"- **Critical Issues**: {summary.get('total_critical_issues', 0)}",
            "",
            "### Quick Status Overview",
            ""
        ]
        
        # Component status table
        if component_results:
            lines.extend([
                "| Component | Status | Health Score | Issues |",
                "|-----------|--------|--------------|--------|"
            ])
            
            for component_name, component_data in component_results.items():
                component_summary = component_data.get("summary", {})
                status_icon = {
                    True: "✅ HEALTHY",
                    False: "❌ UNHEALTHY"
                }.get(component_summary.get("is_healthy"), "❓ UNKNOWN")
                
                health_score = component_summary.get("health_score", 0)
                critical_issues = component_summary.get("critical_issues", 0)
                
                lines.append(f"| {component_name} | {status_icon} | {health_score:.1f}% | {critical_issues} |")
        
        lines.extend([
            "",
            "## Detailed Results",
            ""
        ])
        
        # Component detailed results
        if component_results:
            lines.append("### Component Verification Results")
            lines.append("")
            
            for component_name, component_data in component_results.items():
                lines.append(f"#### {component_name}")
                
                component_summary = component_data.get("summary", {})
                timing = component_data.get("timing", {})
                
                lines.extend([
                    f"- **Status**: {('✅ HEALTHY' if component_summary.get('is_healthy') else '❌ UNHEALTHY')}",
                    f"- **Tests**: {component_summary.get('passed', 0)}/{component_summary.get('total_tests', 0)} passed",
                    f"- **Duration**: {timing.get('total_duration_ms', 0) / 1000:.1f}s",
                    f"- **Health Score**: {component_summary.get('health_score', 0):.1f}%",
                    ""
                ])
                
                # Individual test results
                test_results = component_data.get("results", [])
                if test_results:
                    lines.append("**Test Results:**")
                    for test in test_results:
                        status_icon = {
                            "PASS": "✅",
                            "FAIL": "❌", 
                            "WARNING": "⚠️",
                            "SKIP": "⏭️",
                            "ERROR": "💥"
                        }.get(test.get("status"), "❓")
                        
                        test_name = test.get("name", "Unknown")
                        test_message = test.get("message", "No message")
                        test_duration = test.get("duration_ms", 0)
                        
                        lines.append(f"- {status_icon} **{test_name}** ({test_duration:.0f}ms): {test_message}")
                        
                        # Add recommendations for failed tests
                        recommendations = test.get("recommendations", [])
                        if recommendations:
                            lines.append("  - **Recommendations**:")
                            for rec in recommendations[:3]:  # Limit to first 3
                                lines.append(f"    - {rec}")
                
                lines.append("")
        
        # Integration results
        if integration_results:
            lines.append("### Integration Verification Results")
            lines.append("")
            
            for integration_name, integration_data in integration_results.items():
                lines.append(f"#### {integration_name}")
                
                integration_summary = integration_data.get("summary", {})
                lines.extend([
                    f"- **Status**: {('✅ WORKING' if integration_summary.get('is_healthy') else '❌ ISSUES')}",
                    f"- **Success Rate**: {integration_summary.get('success_rate', 0):.1%}",
                    f"- **Critical Issues**: {integration_summary.get('critical_issues', 0)}",
                    ""
                ])
        
        # Business flow results
        if business_flow_results:
            lines.append("### Business Flow Verification Results")
            lines.append("")
            
            for flow_name, flow_data in business_flow_results.items():
                lines.append(f"#### {flow_name}")
                
                flow_summary = flow_data.get("summary", {})
                lines.extend([
                    f"- **Status**: {('✅ WORKING' if flow_summary.get('is_healthy') else '❌ FAILED')}",
                    f"- **Completion Rate**: {flow_summary.get('success_rate', 0):.1%}",
                    f"- **Total Duration**: {flow_summary.get('total_duration_ms', 0) / 1000:.1f}s",
                    ""
                ])
                
                # Flow step breakdown
                step_results = flow_data.get("results", [])
                pipeline_test = next((test for test in step_results if "pipeline" in test.get("name", "")), None)
                
                if pipeline_test and pipeline_test.get("details"):
                    pipeline_details = pipeline_test["details"]
                    pipeline_summary = pipeline_details.get("pipeline_summary", {})
                    
                    lines.extend([
                        "**Pipeline Steps:**",
                        f"- Steps Completed: {pipeline_summary.get('steps_completed', 0)}",
                        f"- Steps Failed: {pipeline_summary.get('steps_failed', 0)}",
                        f"- Processing Success: {('✅' if pipeline_summary.get('processing_successful') else '❌')}",
                        ""
                    ])
        
        # Recommendations section
        lines.append("## Recommendations")
        lines.append("")
        
        # Collect all recommendations
        all_recommendations = []
        
        for component_data in component_results.values():
            for test in component_data.get("results", []):
                all_recommendations.extend(test.get("recommendations", []))
        
        for integration_data in integration_results.values():
            for test in integration_data.get("results", []):
                all_recommendations.extend(test.get("recommendations", []))
        
        for flow_data in business_flow_results.values():
            for test in flow_data.get("results", []):
                all_recommendations.extend(test.get("recommendations", []))
        
        # Remove duplicates and categorize
        unique_recommendations = list(set(all_recommendations))
        
        if unique_recommendations:
            lines.append("### Action Items")
            for i, rec in enumerate(unique_recommendations[:10], 1):  # Top 10 recommendations
                lines.append(f"{i}. {rec}")
        else:
            lines.append("✅ No action items - system is healthy")
        
        lines.extend([
            "",
            "## Production Readiness Assessment",
            ""
        ])
        
        # Production readiness decision
        critical_issues = summary.get('total_critical_issues', 0)
        overall_health = summary.get('overall_health_score', 0)
        
        if critical_issues == 0 and overall_health >= 90:
            lines.append("✅ **RECOMMENDATION: READY FOR PRODUCTION**")
            lines.append("- No critical issues found")
            lines.append("- High system health score")
            lines.append("- All core functionality verified")
        elif critical_issues == 0 and overall_health >= 75:
            lines.append("⚠️ **RECOMMENDATION: PRODUCTION READY WITH MONITORING**")
            lines.append("- No critical issues but some performance concerns")
            lines.append("- Implement enhanced monitoring")
            lines.append("- Address performance issues post-deployment")
        elif critical_issues <= 2 and overall_health >= 60:
            lines.append("🔧 **RECOMMENDATION: RESOLVE ISSUES BEFORE PRODUCTION**")
            lines.append("- Critical issues must be resolved")
            lines.append("- System stability concerns")
            lines.append("- Complete verification after fixes")
        else:
            lines.append("🚫 **RECOMMENDATION: NOT READY FOR PRODUCTION**")
            lines.append("- Multiple critical issues found")
            lines.append("- System health below acceptable threshold")
            lines.append("- Major development work required")
        
        lines.extend([
            "",
            f"**Report Generated**: {datetime.now(timezone.utc).isoformat()}",
            "**Framework**: TalentForge Pro Business Flow Verification v1.0"
        ])
        
        return "\\n".join(lines)
    
    def _generate_html_report(self, results: Dict[str, Any]) -> str:
        """Generate HTML format report"""
        
        summary = results.get("summary", {})
        execution_info = results.get("execution_info", {})
        
        html_content = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TalentForge Pro - Verification Report</title>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 40px; background: #f5f5f5; }}
        .container {{ max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .header {{ border-bottom: 3px solid #2563eb; padding-bottom: 20px; margin-bottom: 30px; }}
        .status-healthy {{ color: #16a34a; font-weight: bold; }}
        .status-warning {{ color: #ea580c; font-weight: bold; }}
        .status-critical {{ color: #dc2626; font-weight: bold; }}
        .metric {{ display: inline-block; margin: 10px 20px 10px 0; padding: 15px; background: #f8fafc; border-radius: 6px; border-left: 4px solid #2563eb; }}
        .component {{ margin: 20px 0; padding: 20px; border: 1px solid #e2e8f0; border-radius: 6px; }}
        .test-pass {{ color: #16a34a; }}
        .test-fail {{ color: #dc2626; }}
        .test-warning {{ color: #ea580c; }}
        .recommendations {{ background: #fef3c7; padding: 15px; border-radius: 6px; margin: 15px 0; }}
        .evidence {{ background: #e0f2fe; padding: 10px; border-radius: 4px; margin: 10px 0; font-family: monospace; font-size: 12px; }}
        .timestamp {{ color: #64748b; font-size: 14px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>TalentForge Pro - System Verification Report</h1>
            <p class="timestamp">Generated: {execution_info.get('timestamp', datetime.now(timezone.utc).isoformat())}</p>
        </div>
        
        <div class="summary">
            <h2>Executive Summary</h2>
            
            <div class="metrics">
                <div class="metric">
                    <strong>Overall Status</strong><br>
                    <span class="status-{'healthy' if summary.get('overall_status') == 'healthy' else 'critical'}">
                        {summary.get('overall_status', 'unknown').upper()}
                    </span>
                </div>
                
                <div class="metric">
                    <strong>Health Score</strong><br>
                    {summary.get('overall_health_score', 0):.1f}%
                </div>
                
                <div class="metric">
                    <strong>Components</strong><br>
                    {summary.get('healthy_components', 0)}/{summary.get('total_components', 0)} Healthy
                </div>
                
                <div class="metric">
                    <strong>Critical Issues</strong><br>
                    {summary.get('total_critical_issues', 0)}
                </div>
            </div>
        </div>
        """
        
        # Add component results
        component_results = results.get("component_results", {})
        if component_results:
            html_content += """
        <div class="components">
            <h2>Component Verification Results</h2>
            """
            
            for component_name, component_data in component_results.items():
                component_summary = component_data.get("summary", {})
                status_class = "status-healthy" if component_summary.get("is_healthy") else "status-critical"
                
                html_content += f"""
            <div class="component">
                <h3>{component_name}</h3>
                <p><strong>Status:</strong> <span class="{status_class}">{'HEALTHY' if component_summary.get('is_healthy') else 'ISSUES'}</span></p>
                <p><strong>Tests:</strong> {component_summary.get('passed', 0)}/{component_summary.get('total_tests', 0)} passed</p>
                <p><strong>Health Score:</strong> {component_summary.get('health_score', 0):.1f}%</p>
                
                <h4>Test Results:</h4>
                <ul>
                """
                
                for test in component_data.get("results", []):
                    test_class = f"test-{test.get('status', 'unknown').lower()}"
                    status_symbol = {
                        "PASS": "✅",
                        "FAIL": "❌",
                        "WARNING": "⚠️", 
                        "SKIP": "⏭️",
                        "ERROR": "💥"
                    }.get(test.get("status"), "❓")
                    
                    html_content += f"""
                    <li class="{test_class}">
                        {status_symbol} <strong>{test.get('name', 'Unknown')}</strong> ({test.get('duration_ms', 0):.0f}ms)
                        <br>{test.get('message', 'No message')}
                    </li>
                    """
                
                html_content += "</ul>"
                
                # Add recommendations if any
                all_recommendations = []
                for test in component_data.get("results", []):
                    all_recommendations.extend(test.get("recommendations", []))
                
                unique_recommendations = list(set(all_recommendations))
                if unique_recommendations:
                    html_content += """
                <div class="recommendations">
                    <strong>Recommendations:</strong>
                    <ul>
                    """
                    for rec in unique_recommendations[:5]:
                        html_content += f"<li>{rec}</li>"
                    html_content += "</ul></div>"
                
                html_content += "</div>"
            
            html_content += "</div>"
        
        # Add production readiness assessment
        critical_issues = summary.get('total_critical_issues', 0)
        overall_health = summary.get('overall_health_score', 0)
        
        readiness_class = "status-healthy"
        readiness_text = "READY FOR PRODUCTION"
        readiness_details = "No critical issues found and high system health score."
        
        if critical_issues > 0 or overall_health < 75:
            readiness_class = "status-critical"
            readiness_text = "NOT READY FOR PRODUCTION"
            readiness_details = f"Critical issues: {critical_issues}, Health score: {overall_health:.1f}%"
        elif overall_health < 90:
            readiness_class = "status-warning"
            readiness_text = "READY WITH MONITORING"
            readiness_details = "Good health but monitor performance closely."
        
        html_content += f"""
        <div class="production-readiness">
            <h2>Production Readiness Assessment</h2>
            
            <div class="metric" style="border-left-color: {'#16a34a' if readiness_class == 'status-healthy' else '#dc2626' if readiness_class == 'status-critical' else '#ea580c'};">
                <strong>Recommendation</strong><br>
                <span class="{readiness_class}">{readiness_text}</span>
                <br><small>{readiness_details}</small>
            </div>
        </div>
        
        <div class="footer">
            <p class="timestamp">
                Report generated by TalentForge Pro Business Flow Verification Framework v1.0<br>
                Timestamp: {datetime.now(timezone.utc).isoformat()}
            </p>
        </div>
        
    </div>
</body>
</html>
        """
        
        return html_content
    
    def generate_executive_summary(self, results: Dict[str, Any]) -> str:
        """Generate executive summary for stakeholders"""
        
        summary = results.get("summary", {})
        
        # Production readiness decision
        critical_issues = summary.get('total_critical_issues', 0)
        overall_health = summary.get('overall_health_score', 0)
        
        if critical_issues == 0 and overall_health >= 90:
            recommendation = "✅ READY FOR PRODUCTION DEPLOYMENT"
            confidence = "HIGH"
        elif critical_issues == 0 and overall_health >= 75:
            recommendation = "⚠️ READY WITH ENHANCED MONITORING"
            confidence = "MEDIUM"
        elif critical_issues <= 2:
            recommendation = "🔧 RESOLVE ISSUES BEFORE DEPLOYMENT"
            confidence = "LOW"
        else:
            recommendation = "🚫 NOT READY - MAJOR ISSUES DETECTED"
            confidence = "CRITICAL"
        
        return f"""
TALENTFORGE PRO - EXECUTIVE VERIFICATION SUMMARY
=====================================================

PRODUCTION READINESS: {recommendation}
CONFIDENCE LEVEL: {confidence}

KEY METRICS:
- Overall System Health: {overall_health:.1f}%
- Components Tested: {summary.get('total_components', 0)}
- Critical Issues: {critical_issues}
- Verification Duration: {summary.get('total_duration_ms', 0) / 1000 / 60:.1f} minutes

SYSTEM STATUS:
- Database (PostgreSQL + pgvector): {'✅' if summary.get('component_health', {}).get('postgresql', False) else '❌'}
- Cache Layer (Redis): {'✅' if summary.get('component_health', {}).get('redis', False) else '❌'}
- File Storage (MinIO): {'✅' if summary.get('component_health', {}).get('minio', False) else '❌'}
- AI Services: {'✅' if summary.get('component_health', {}).get('ai_services', False) else '❌'}
- Backend API: {'✅' if summary.get('component_health', {}).get('backend_api', False) else '❌'}

BUSINESS FLOWS:
- Resume Processing Pipeline: {'✅ WORKING' if summary.get('business_flow_health', {}).get('resume_processing', False) else '❌ ISSUES'}
- User Authentication: {'✅ WORKING' if summary.get('business_flow_health', {}).get('authentication', False) else '❌ ISSUES'}
- Data Integration: {'✅ WORKING' if summary.get('integration_health', {}).get('data_flow', False) else '❌ ISSUES'}

Generated: {datetime.now(timezone.utc).strftime("%Y-%m-%d %H:%M:%S")} UTC
        """.strip()
    
    def save_performance_baseline(self, performance_data: Dict[str, Any], baseline_file: str = None) -> str:
        """Save performance baseline for future comparisons"""
        
        if baseline_file is None:
            baseline_file = self.report_dir / "performance_baseline.json"
        else:
            baseline_file = Path(baseline_file)
        
        baseline_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "baseline_version": "1.0",
            "performance_metrics": performance_data
        }
        
        with open(baseline_file, 'w', encoding='utf-8') as f:
            json.dump(baseline_data, f, indent=2, default=str)
        
        return str(baseline_file)
    
    def compare_with_baseline(self, current_results: Dict[str, Any], baseline_file: str) -> Dict[str, Any]:
        """Compare current results with performance baseline"""
        
        baseline_path = Path(baseline_file)
        if not baseline_path.exists():
            return {"error": "Baseline file not found"}
        
        with open(baseline_path, 'r') as f:
            baseline_data = json.load(f)
        
        comparison = {
            "baseline_timestamp": baseline_data.get("timestamp"),
            "current_timestamp": datetime.now(timezone.utc).isoformat(),
            "comparisons": {},
            "regressions": [],
            "improvements": []
        }
        
        # Extract performance metrics for comparison
        # This would compare response times, success rates, etc.
        # Implementation would depend on specific metrics structure
        
        return comparison