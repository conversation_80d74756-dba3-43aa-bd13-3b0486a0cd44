"""
System health checking utilities
"""
import asyncio
import time
from typing import Dict, Any, List, Optional
import logging

import httpx
import asyncpg
import redis.asyncio as redis
from minio import Minio


class HealthChecker:
    """Quick health checks for all system components"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    async def quick_health_check(self) -> Dict[str, Any]:
        """Perform quick health check of all components"""
        results = {
            "timestamp": time.time(),
            "overall_status": "unknown",
            "components": {}
        }
        
        # Run all health checks concurrently
        health_tasks = {
            "postgresql": self._check_postgresql_health(),
            "redis": self._check_redis_health(),
            "minio": self._check_minio_health(),
            "backend_api": self._check_api_health(),
            "ollama": self._check_ollama_health()
        }
        
        # Execute all checks with timeout
        try:
            component_results = await asyncio.wait_for(
                asyncio.gather(*[
                    asyncio.create_task(task, name=name) 
                    for name, task in health_tasks.items()
                ], return_exceptions=True),
                timeout=30.0
            )
            
            # Process results
            for i, (component_name, task_result) in enumerate(zip(health_tasks.keys(), component_results)):
                if isinstance(task_result, Exception):
                    results["components"][component_name] = {
                        "status": "error",
                        "error": str(task_result),
                        "response_time_ms": None
                    }
                else:
                    results["components"][component_name] = task_result
            
        except asyncio.TimeoutError:
            self.logger.warning("Health check timeout - some components may be slow")
            for component_name in health_tasks.keys():
                if component_name not in results["components"]:
                    results["components"][component_name] = {
                        "status": "timeout",
                        "error": "Health check timeout",
                        "response_time_ms": None
                    }
        
        # Calculate overall status
        component_statuses = [comp.get("status", "unknown") for comp in results["components"].values()]
        healthy_components = sum(1 for status in component_statuses if status == "healthy")
        total_components = len(component_statuses)
        
        if healthy_components == total_components:
            results["overall_status"] = "healthy"
        elif healthy_components >= total_components * 0.8:
            results["overall_status"] = "degraded"
        else:
            results["overall_status"] = "unhealthy"
        
        results["health_summary"] = {
            "healthy_components": healthy_components,
            "total_components": total_components,
            "health_percentage": (healthy_components / total_components) * 100
        }
        
        return results
    
    async def _check_postgresql_health(self) -> Dict[str, Any]:
        """Quick PostgreSQL health check"""
        try:
            db_config = self.config.get("database", {})
            
            start_time = time.time()
            connection = await asyncpg.connect(
                host=db_config.get("host", "localhost"),
                port=db_config.get("port", 5432),
                database=db_config.get("database", "talentforge"),
                user=db_config.get("user", "tfuser"), 
                password=db_config.get("password", "tfpass123"),
                command_timeout=5
            )
            
            # Test basic query
            result = await connection.fetchval("SELECT 1")
            await connection.close()
            
            response_time = (time.time() - start_time) * 1000
            
            if result == 1:
                return {
                    "status": "healthy",
                    "response_time_ms": response_time,
                    "details": "PostgreSQL responding normally"
                }
            else:
                return {
                    "status": "unhealthy", 
                    "response_time_ms": response_time,
                    "error": f"Unexpected query result: {result}"
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    async def _check_redis_health(self) -> Dict[str, Any]:
        """Quick Redis health check"""
        try:
            redis_config = self.config.get("redis", {})
            
            start_time = time.time()
            redis_client = redis.Redis(
                host=redis_config.get("host", "localhost"),
                port=redis_config.get("port", 6379),
                password=redis_config.get("password"),
                db=0,
                decode_responses=True,
                socket_timeout=5.0
            )
            
            # Test ping
            ping_result = await redis_client.ping()
            await redis_client.close()
            
            response_time = (time.time() - start_time) * 1000
            
            if ping_result:
                return {
                    "status": "healthy",
                    "response_time_ms": response_time,
                    "details": "Redis responding to ping"
                }
            else:
                return {
                    "status": "unhealthy",
                    "error": "Redis ping failed"
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    async def _check_minio_health(self) -> Dict[str, Any]:
        """Quick MinIO health check"""
        try:
            minio_config = self.config.get("minio", {})
            
            start_time = time.time()
            client = Minio(
                minio_config.get("endpoint", "localhost:9000"),
                access_key=minio_config.get("access_key", "minioadmin"),
                secret_key=minio_config.get("secret_key", "minioadmin123"),
                secure=minio_config.get("secure", False)
            )
            
            # Test by listing buckets
            buckets = list(client.list_buckets())
            response_time = (time.time() - start_time) * 1000
            
            return {
                "status": "healthy",
                "response_time_ms": response_time,
                "details": f"MinIO accessible ({len(buckets)} buckets)"
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    async def _check_api_health(self) -> Dict[str, Any]:
        """Quick Backend API health check"""
        try:
            api_config = self.config.get("api", {})
            base_url = api_config.get("base_url", "http://localhost:8088")
            api_prefix = api_config.get("api_prefix", "/api/v1")
            
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{base_url}{api_prefix}/health")
                response_time = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    health_data = response.json()
                    return {
                        "status": "healthy",
                        "response_time_ms": response_time,
                        "details": f"API healthy: {health_data.get('status', 'unknown')}"
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "error": f"API health check returned {response.status_code}"
                    }
                    
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    async def _check_ollama_health(self) -> Dict[str, Any]:
        """Quick Ollama service health check"""
        try:
            ollama_config = self.config.get("ollama", {})
            base_url = ollama_config.get("base_url", "http://localhost:11434")
            
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(f"{base_url}/api/tags")
                response_time = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    models_data = response.json()
                    model_count = len(models_data.get("models", []))
                    
                    return {
                        "status": "healthy",
                        "response_time_ms": response_time,
                        "details": f"Ollama running ({model_count} models available)"
                    }
                else:
                    return {
                        "status": "unhealthy",
                        "error": f"Ollama API returned {response.status_code}"
                    }
                    
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "error_type": type(e).__name__
            }
    
    async def check_service_dependencies(self) -> Dict[str, Any]:
        """Check dependencies between services"""
        results = {
            "dependency_checks": {},
            "critical_path_healthy": False
        }
        
        try:
            # Check critical service dependencies
            dependencies = {
                "api_to_database": {
                    "services": ["backend_api", "postgresql"],
                    "critical": True
                },
                "api_to_cache": {
                    "services": ["backend_api", "redis"], 
                    "critical": True
                },
                "api_to_storage": {
                    "services": ["backend_api", "minio"],
                    "critical": False
                },
                "workers_to_queue": {
                    "services": ["celery", "redis"],
                    "critical": True
                }
            }
            
            # Get current component health
            health_status = await self.quick_health_check()
            component_health = {
                name: comp.get("status") == "healthy" 
                for name, comp in health_status["components"].items()
            }
            
            # Check each dependency chain
            for dep_name, dep_config in dependencies.items():
                services = dep_config["services"]
                all_healthy = all(component_health.get(service, False) for service in services)
                
                results["dependency_checks"][dep_name] = {
                    "services": services,
                    "all_healthy": all_healthy,
                    "critical": dep_config["critical"],
                    "status": "healthy" if all_healthy else "broken"
                }
            
            # Check critical path
            critical_deps = [
                dep for dep in results["dependency_checks"].values() 
                if dep["critical"]
            ]
            results["critical_path_healthy"] = all(dep["all_healthy"] for dep in critical_deps)
            
            # Summary
            healthy_deps = sum(1 for dep in results["dependency_checks"].values() if dep["all_healthy"])
            total_deps = len(results["dependency_checks"])
            
            results["dependency_summary"] = {
                "healthy_dependencies": healthy_deps,
                "total_dependencies": total_deps,
                "dependency_health_rate": healthy_deps / total_deps
            }
            
            return results
            
        except Exception as e:
            results["error"] = str(e)
            return results
    
    def create_health_report(self, health_data: Dict[str, Any]) -> str:
        """Create a formatted health report"""
        report_lines = [
            "=== SYSTEM HEALTH REPORT ===",
            f"Timestamp: {datetime.fromtimestamp(health_data['timestamp']).isoformat()}",
            f"Overall Status: {health_data['overall_status'].upper()}",
            ""
        ]
        
        # Component status
        report_lines.append("Component Status:")
        for component, status in health_data["components"].items():
            status_icon = {"healthy": "✅", "unhealthy": "❌", "timeout": "⏰", "error": "💥"}.get(status.get("status"), "❓")
            response_time = status.get("response_time_ms")
            time_info = f" ({response_time:.1f}ms)" if response_time else ""
            
            report_lines.append(f"  {status_icon} {component}: {status.get('status', 'unknown')}{time_info}")
            
            if status.get("error"):
                report_lines.append(f"      Error: {status['error']}")
        
        # Summary
        summary = health_data.get("health_summary", {})
        if summary:
            report_lines.extend([
                "",
                f"Health Summary: {summary['healthy_components']}/{summary['total_components']} components healthy ({summary['health_percentage']:.1f}%)"
            ])
        
        return "\\n".join(report_lines)