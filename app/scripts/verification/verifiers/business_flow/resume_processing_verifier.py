"""
End-to-end resume processing pipeline verification
"""
import asyncio
import io
import json
import time
from typing import Dict, Any, List
from datetime import datetime, timezone

import httpx
import asyncpg
from minio import Minio

from ..base_verifier import BusinessFlowVerifier, VerificationStatus, VerificationSeverity, VerificationSummary


class ResumeProcessingVerifier(BusinessFlowVerifier):
    """Verify complete resume processing pipeline: Upload → OCR → AI Parse → Vector → Database"""
    
    def __init__(self, connection_config: Dict[str, Any]):
        expected_steps = [
            "file_upload",
            "ocr_processing", 
            "ai_parsing",
            "vector_generation",
            "database_storage",
            "search_availability"
        ]
        super().__init__("Resume Processing Pipeline", expected_steps)
        self.config = connection_config
        
        # Service configurations
        self.api_config = connection_config.get("api", {})
        self.db_config = connection_config.get("database", {})
        self.minio_config = connection_config.get("minio", {})
        self.dev_token = connection_config.get("dev_token", "dev_bypass_token_2025_talentforge")
    
    async def execute_flow(self, test_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute complete resume processing flow"""
        try:
            flow_results = {}
            pipeline_start_time = time.time()
            
            # Extract test data
            resume_text = test_data.get("resume_text", """
John Doe
Senior Software Engineer

Contact Information:
Email: <EMAIL>
Phone: ******-0123
Location: San Francisco, CA

Technical Skills:
• Programming Languages: Python (Expert), JavaScript (Advanced)
• Frameworks: FastAPI, React, Django, Next.js
• Databases: PostgreSQL, Redis, MongoDB
• Tools: Docker, Git, AWS

Professional Experience:

Senior Software Engineer | TechCorp Inc. | 2021 - Present
• Developed scalable web applications using Python and FastAPI
• Built microservices architecture serving 100k+ users
• Implemented AI-powered features using OpenAI API
• Led team of 4 developers in agile environment

Software Developer | StartupXYZ | 2019 - 2021  
• Created responsive web applications using React and Node.js
• Designed and implemented RESTful APIs
• Integrated third-party payment systems
• Improved application performance by 40%

Education:
Bachelor of Computer Science | UC Berkeley | 2019
• Relevant Coursework: Data Structures, Algorithms, Database Systems
• Senior Project: AI-powered job matching system

Certifications:
• AWS Certified Developer Associate (2022)
• Google Cloud Professional Developer (2023)
            """.strip())
            
            candidate_data = test_data.get("candidate_data", {
                "name": "John Doe",
                "email": "<EMAIL>",
                "phone": "******-0123",
                "notes": "Verification test candidate"
            })
            
            # Step 1: Create test candidate
            async with httpx.AsyncClient(timeout=60.0) as client:
                api_base = self.api_config.get("base_url", "http://localhost:8088")
                api_prefix = self.api_config.get("api_prefix", "/api/v1")
                headers = {
                    "Authorization": f"Bearer {self.dev_token}",
                    "Content-Type": "application/json"
                }
                
                # Create candidate
                step_start_time = time.time()
                candidate_response = await client.post(
                    f"{api_base}{api_prefix}/candidates/",
                    headers=headers,
                    json=candidate_data
                )
                step_duration = (time.time() - step_start_time) * 1000
                
                if candidate_response.status_code in [200, 201]:
                    candidate_info = candidate_response.json()
                    candidate_id = candidate_info.get("id")
                    
                    flow_results["step_1_candidate_creation"] = {
                        "success": True,
                        "duration_ms": step_duration,
                        "candidate_id": candidate_id,
                        "status_code": candidate_response.status_code
                    }
                else:
                    flow_results["step_1_candidate_creation"] = {
                        "success": False,
                        "duration_ms": step_duration,
                        "status_code": candidate_response.status_code,
                        "error": candidate_response.text[:200]
                    }
                    return flow_results  # Cannot continue without candidate
            
                # Step 2: Create resume file and upload
                resume_filename = f"verification_resume_{int(time.time())}.txt"
                
                # Create multipart form data for file upload
                files = {
                    'file': (resume_filename, io.BytesIO(resume_text.encode()), 'text/plain')
                }
                
                upload_headers = {"Authorization": f"Bearer {self.dev_token}"}
                
                step_start_time = time.time()
                upload_response = await client.post(
                    f"{api_base}{api_prefix}/candidates/{candidate_id}/upload-resume",
                    headers=upload_headers,
                    files=files
                )
                upload_duration = (time.time() - step_start_time) * 1000
                
                if upload_response.status_code in [200, 201]:
                    upload_data = upload_response.json()
                    
                    flow_results["step_2_resume_upload"] = {
                        "success": True,
                        "duration_ms": upload_duration,
                        "upload_response": upload_data,
                        "status_code": upload_response.status_code
                    }
                    
                    # Extract resume ID or file path from response
                    resume_id = upload_data.get("resume_id") or upload_data.get("id")
                    file_path = upload_data.get("file_path") or upload_data.get("path")
                    
                    flow_results["step_2_resume_upload"]["resume_id"] = resume_id
                    flow_results["step_2_resume_upload"]["file_path"] = file_path
                    
                else:
                    flow_results["step_2_resume_upload"] = {
                        "success": False,
                        "duration_ms": upload_duration,
                        "status_code": upload_response.status_code,
                        "error": upload_response.text[:200]
                    }
                    return flow_results  # Cannot continue without upload
                
                # Step 3: Wait for and verify processing completion
                processing_timeout = 45  # 45 seconds timeout for processing
                processing_start = time.time()
                processing_complete = False
                processing_result = None
                
                while (time.time() - processing_start) < processing_timeout and not processing_complete:
                    await asyncio.sleep(2)  # Check every 2 seconds
                    
                    # Check candidate data for processed resume
                    check_response = await client.get(
                        f"{api_base}{api_prefix}/candidates/{candidate_id}",
                        headers=headers
                    )
                    
                    if check_response.status_code == 200:
                        candidate_data_updated = check_response.json()
                        
                        # Check if resume has been processed (look for parsed data)
                        has_skills = bool(candidate_data_updated.get("skills"))
                        has_experience = bool(candidate_data_updated.get("experience"))
                        has_parsed_content = bool(candidate_data_updated.get("parsed_content"))
                        
                        if has_skills or has_experience or has_parsed_content:
                            processing_complete = True
                            processing_result = candidate_data_updated
                
                processing_total_time = (time.time() - processing_start) * 1000
                
                flow_results["step_3_processing_completion"] = {
                    "success": processing_complete,
                    "duration_ms": processing_total_time,
                    "timeout_reached": processing_total_time >= processing_timeout * 1000,
                    "processed_data_available": processing_complete
                }
                
                if processing_complete and processing_result:
                    flow_results["step_3_processing_completion"]["processed_data"] = {
                        "skills_extracted": bool(processing_result.get("skills")),
                        "experience_extracted": bool(processing_result.get("experience")),
                        "parsed_content_available": bool(processing_result.get("parsed_content")),
                        "skills_count": len(processing_result.get("skills", [])),
                        "experience_count": len(processing_result.get("experience", []))
                    }
                
                # Step 4: Verify vector search availability 
                if processing_complete:
                    step_start_time = time.time()
                    
                    # Try vector search endpoint if available
                    search_response = await client.post(
                        f"{api_base}{api_prefix}/matching/search",
                        headers=headers,
                        json={
                            "query": "Python developer",
                            "limit": 10
                        }
                    )
                    search_duration = (time.time() - step_start_time) * 1000
                    
                    if search_response.status_code == 200:
                        search_results = search_response.json()
                        
                        # Check if our candidate appears in search results
                        candidate_found_in_search = any(
                            result.get("candidate_id") == candidate_id or result.get("id") == candidate_id
                            for result in search_results.get("items", [])
                        )
                        
                        flow_results["step_4_vector_search"] = {
                            "success": True,
                            "duration_ms": search_duration,
                            "search_results_count": len(search_results.get("items", [])),
                            "candidate_searchable": candidate_found_in_search,
                            "status_code": search_response.status_code
                        }
                    else:
                        flow_results["step_4_vector_search"] = {
                            "success": False,
                            "duration_ms": search_duration,
                            "status_code": search_response.status_code,
                            "error": search_response.text[:200]
                        }
                
                # Step 5: Cleanup - Delete test candidate
                cleanup_start_time = time.time()
                delete_response = await client.delete(
                    f"{api_base}{api_prefix}/candidates/{candidate_id}",
                    headers=headers
                )
                cleanup_duration = (time.time() - cleanup_start_time) * 1000
                
                flow_results["step_5_cleanup"] = {
                    "success": delete_response.status_code in [200, 204],
                    "duration_ms": cleanup_duration,
                    "status_code": delete_response.status_code
                }
            
            # Calculate total pipeline time
            flow_results["pipeline_summary"] = {
                "total_duration_ms": (time.time() - pipeline_start_time) * 1000,
                "steps_completed": sum(1 for step in flow_results.values() if isinstance(step, dict) and step.get("success", False)),
                "steps_failed": sum(1 for step in flow_results.values() if isinstance(step, dict) and not step.get("success", True)),
                "processing_successful": processing_complete
            }
            
            return flow_results
            
        except Exception as e:
            return {
                "pipeline_error": str(e),
                "error_type": type(e).__name__,
                "pipeline_failed": True
            }
    
    async def validate_outcome(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the complete resume processing outcome"""
        try:
            validation_results = {}
            
            # Check pipeline completion
            pipeline_summary = result.get("pipeline_summary", {})
            steps_completed = pipeline_summary.get("steps_completed", 0)
            steps_failed = pipeline_summary.get("steps_failed", 0)
            total_duration = pipeline_summary.get("total_duration_ms", 0)
            
            validation_results["pipeline_completion"] = {
                "steps_completed": steps_completed,
                "steps_failed": steps_failed,
                "completion_rate": steps_completed / (steps_completed + steps_failed) if (steps_completed + steps_failed) > 0 else 0,
                "total_duration_ms": total_duration
            }
            
            # Validate individual steps
            step_validations = {}
            
            # Step 1: Candidate creation
            step1 = result.get("step_1_candidate_creation", {})
            step_validations["candidate_creation"] = {
                "completed": step1.get("success", False),
                "duration_acceptable": step1.get("duration_ms", 0) < 5000,  # 5s limit
                "has_candidate_id": bool(step1.get("candidate_id"))
            }
            
            # Step 2: Resume upload
            step2 = result.get("step_2_resume_upload", {})
            step_validations["resume_upload"] = {
                "completed": step2.get("success", False),
                "duration_acceptable": step2.get("duration_ms", 0) < 10000,  # 10s limit
                "file_stored": bool(step2.get("file_path"))
            }
            
            # Step 3: Processing completion
            step3 = result.get("step_3_processing_completion", {})
            processed_data = step3.get("processed_data", {})
            step_validations["ai_processing"] = {
                "completed": step3.get("success", False),
                "duration_acceptable": step3.get("duration_ms", 0) < 45000,  # 45s limit
                "skills_extracted": processed_data.get("skills_extracted", False),
                "experience_extracted": processed_data.get("experience_extracted", False),
                "quality_acceptable": (
                    processed_data.get("skills_count", 0) >= 3 and 
                    processed_data.get("experience_count", 0) >= 1
                )
            }
            
            # Step 4: Vector search
            step4 = result.get("step_4_vector_search", {})
            step_validations["vector_search"] = {
                "completed": step4.get("success", False),
                "duration_acceptable": step4.get("duration_ms", 0) < 5000,  # 5s limit
                "candidate_searchable": step4.get("candidate_searchable", False),
                "search_results_returned": step4.get("search_results_count", 0) > 0
            }
            
            # Step 5: Cleanup
            step5 = result.get("step_5_cleanup", {})
            step_validations["cleanup"] = {
                "completed": step5.get("success", False),
                "duration_acceptable": step5.get("duration_ms", 0) < 3000  # 3s limit
            }
            
            validation_results["step_validations"] = step_validations
            
            # Overall validation
            critical_steps = ["candidate_creation", "resume_upload", "ai_processing"]
            critical_success = all(
                step_validations[step]["completed"] 
                for step in critical_steps
            )
            
            quality_steps = ["ai_processing", "vector_search"]
            quality_success = all(
                step_validations[step]["quality_acceptable"] if "quality_acceptable" in step_validations[step]
                else step_validations[step]["completed"]
                for step in quality_steps
            )
            
            performance_acceptable = all(
                step_validations[step]["duration_acceptable"]
                for step in step_validations
            )
            
            validation_results["overall_validation"] = {
                "critical_steps_success": critical_success,
                "quality_acceptable": quality_success,
                "performance_acceptable": performance_acceptable,
                "pipeline_viable": critical_success and quality_success,
                "production_ready": critical_success and quality_success and performance_acceptable
            }
            
            # Performance breakdown
            step_durations = {
                step_name: result.get(f"step_{i+1}_{step_name.split('_')[0] if '_' in step_name else step_name}", {}).get("duration_ms", 0)
                for i, step_name in enumerate(["candidate_creation", "resume_upload", "processing_completion", "vector_search", "cleanup"])
            }
            
            validation_results["performance_breakdown"] = step_durations
            
            return validation_results
            
        except Exception as e:
            return {
                "validation_error": str(e),
                "error_type": type(e).__name__,
                "validation_failed": True
            }
    
    async def verify(self) -> VerificationSummary:
        """Execute resume processing pipeline verification"""
        self.start_time = datetime.now(timezone.utc)
        
        # Test data for resume processing
        test_data = {
            "resume_text": """
Jane Smith
Senior Python Developer & AI Engineer

Contact Information:
Email: <EMAIL>
Phone: ******-0156
LinkedIn: linkedin.com/in/janesmith-verification
Location: Seattle, WA

Professional Summary:
Experienced full-stack developer with 7+ years in web development and 3+ years in AI/ML. 
Expert in Python, FastAPI, and PostgreSQL. Proven track record in building scalable applications.

Technical Skills:
• Programming Languages: Python (Expert), TypeScript (Advanced), Go (Intermediate)
• Web Frameworks: FastAPI, Django, React, Next.js, Vue.js
• Databases: PostgreSQL, Redis, Vector Databases (pgvector), MongoDB
• AI/ML: OpenAI API, LangChain, TensorFlow, Hugging Face Transformers
• Cloud & DevOps: Docker, Kubernetes, AWS, CI/CD

Professional Experience:

Senior AI Engineer | TechCorp Inc. | 2022 - Present
• Developed ML models for customer recommendation systems
• Built real-time vector search infrastructure using PostgreSQL with pgvector
• Implemented LLM-powered chatbots with 95% accuracy using OpenAI
• Led migration of legacy systems to microservices architecture

Full-Stack Developer | StartupXYZ | 2019 - 2022  
• Built responsive web applications using React and FastAPI
• Designed RESTful APIs serving 10k+ daily active users
• Implemented OAuth2 authentication and RBAC
• Created automated testing achieving 90%+ coverage

Software Developer | WebSolutions Ltd | 2017 - 2019
• Developed e-commerce platforms using Django and PostgreSQL  
• Implemented payment processing with Stripe and PayPal
• Built automated deployment pipelines using Docker

Education:
Master of Science in Computer Science | Stanford University | 2017
Bachelor of Science in Software Engineering | UC Berkeley | 2015

Projects:
• ResumeAI: Built AI-powered resume parsing using FastAPI and OpenAI API
• SmartChat: Customer service chatbot using LangChain reducing response time 60%
            """.strip(),
            
            "candidate_data": {
                "name": "Jane Smith", 
                "email": "<EMAIL>",
                "phone": "******-0156",
                "notes": "Verification test - Senior Python Developer"
            }
        }
        
        # Execute complete flow
        flow_execution_result = await self.run_test(
            "resume_processing_pipeline",
            self.execute_flow,
            VerificationSeverity.CRITICAL,
            test_data=test_data
        )
        
        # Validate outcome if execution was successful
        if flow_execution_result and flow_execution_result.details:
            await self.run_test(
                "pipeline_outcome_validation",
                self.validate_outcome,
                VerificationSeverity.HIGH,
                result=flow_execution_result.details
            )
        
        return self.get_summary()