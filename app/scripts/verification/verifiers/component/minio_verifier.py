"""
MinIO object storage verification
"""
import asyncio
import io
import time
from typing import Dict, Any, List
from datetime import datetime, timezone

from minio import Minio
from minio.error import S3Error

from ..base_verifier import ComponentVerifier, VerificationStatus, VerificationSeverity, VerificationSummary


class MinIOVerifier(ComponentVerifier):
    """Verify MinIO object storage functionality"""
    
    def __init__(self, connection_config: Dict[str, Any]):
        super().__init__("MinIO Object Storage", connection_config)
        self.client = None
        
        # Extract connection parameters
        self.endpoint = connection_config.get("endpoint", "localhost:9000")
        self.access_key = connection_config.get("access_key", "minioadmin")
        self.secret_key = connection_config.get("secret_key", "minioadmin123")
        self.secure = connection_config.get("secure", False)
        
        # Test bucket names
        self.test_bucket = "verification-test"
        self.production_buckets = ["talentforge", "resumes", "job-descriptions"]
    
    async def check_connectivity(self) -> Dict[str, Any]:
        """Test MinIO connectivity"""
        try:
            # Create MinIO client
            self.client = Minio(
                self.endpoint,
                access_key=self.access_key,
                secret_key=self.secret_key,
                secure=self.secure
            )
            
            # Test connection by listing buckets
            start_time = time.time()
            buckets = list(self.client.list_buckets())
            connection_time = (time.time() - start_time) * 1000
            
            return {
                "status": "PASS",
                "message": f"MinIO connection successful ({len(buckets)} buckets found)",
                "details": {
                    "endpoint": self.endpoint,
                    "secure": self.secure,
                    "bucket_count": len(buckets),
                    "bucket_names": [bucket.name for bucket in buckets],
                    "connection_time_ms": connection_time
                },
                "evidence": [
                    {
                        "type": "connection_test",
                        "data": {
                            "buckets": [{"name": b.name, "creation_date": b.creation_date.isoformat() if b.creation_date else None} for b in buckets],
                            "response_time_ms": connection_time
                        },
                        "description": "MinIO connection and bucket listing test"
                    }
                ]
            }
            
        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"MinIO connection failed: {str(e)}",
                "details": {
                    "error_type": type(e).__name__,
                    "endpoint": self.endpoint,
                    "secure": self.secure
                }
            }
    
    async def check_functionality(self) -> Dict[str, Any]:
        """Test MinIO core functionality"""
        if not self.client:
            return {
                "status": "SKIP",
                "message": "Skipping functionality test - no MinIO connection",
                "details": {}
            }
        
        try:
            results = {}
            
            # Test 1: Bucket operations
            # Check if test bucket exists, create if needed
            bucket_exists = self.client.bucket_exists(self.test_bucket)
            results["test_bucket_exists"] = bucket_exists
            
            if not bucket_exists:
                start_time = time.time()
                self.client.make_bucket(self.test_bucket)
                bucket_creation_time = (time.time() - start_time) * 1000
                results["bucket_created"] = True
                results["bucket_creation_time_ms"] = bucket_creation_time
            else:
                results["bucket_already_exists"] = True
            
            # Test 2: File upload operations
            test_files = [
                {"name": "test1.txt", "content": "Hello MinIO World!", "content_type": "text/plain"},
                {"name": "test2.json", "content": '{"test": true, "timestamp": "2025-08-29"}', "content_type": "application/json"},
                {"name": "test3.bin", "content": b"Binary test data" * 100, "content_type": "application/octet-stream"}
            ]
            
            upload_results = {}
            
            for test_file in test_files:
                file_name = test_file["name"]
                content = test_file["content"]
                content_type = test_file["content_type"]
                
                # Convert content to bytes if needed
                if isinstance(content, str):
                    content_bytes = content.encode('utf-8')
                else:
                    content_bytes = content
                
                content_stream = io.BytesIO(content_bytes)
                
                # Upload file
                start_time = time.time()
                result = self.client.put_object(
                    self.test_bucket,
                    file_name,
                    content_stream,
                    length=len(content_bytes),
                    content_type=content_type
                )
                upload_time = (time.time() - start_time) * 1000
                
                upload_results[file_name] = {
                    "upload_successful": result is not None,
                    "upload_time_ms": upload_time,
                    "file_size_bytes": len(content_bytes)
                }
            
            results["upload_operations"] = upload_results
            results["all_uploads_successful"] = all(
                r["upload_successful"] for r in upload_results.values()
            )
            
            # Test 3: File download operations  
            download_results = {}
            
            for test_file in test_files:
                file_name = test_file["name"]
                original_content = test_file["content"]
                
                try:
                    start_time = time.time()
                    response = self.client.get_object(self.test_bucket, file_name)
                    downloaded_data = response.read()
                    download_time = (time.time() - start_time) * 1000
                    
                    # Convert back to string if original was string
                    if isinstance(original_content, str):
                        downloaded_content = downloaded_data.decode('utf-8')
                        content_matches = downloaded_content == original_content
                    else:
                        content_matches = downloaded_data == original_content
                    
                    download_results[file_name] = {
                        "download_successful": True,
                        "download_time_ms": download_time,
                        "content_matches": content_matches,
                        "downloaded_size_bytes": len(downloaded_data)
                    }
                    
                    response.close()
                    response.release_conn()
                    
                except Exception as e:
                    download_results[file_name] = {
                        "download_successful": False,
                        "error": str(e)
                    }
            
            results["download_operations"] = download_results
            results["all_downloads_successful"] = all(
                r["download_successful"] and r.get("content_matches", False) 
                for r in download_results.values()
            )
            
            # Test 4: File listing and metadata
            start_time = time.time()
            objects = list(self.client.list_objects(self.test_bucket))
            list_time = (time.time() - start_time) * 1000
            
            results["object_listing"] = {
                "list_time_ms": list_time,
                "object_count": len(objects),
                "objects": [{"name": obj.object_name, "size": obj.size} for obj in objects]
            }
            
            expected_files = set(test_file["name"] for test_file in test_files)
            found_files = set(obj.object_name for obj in objects)
            results["all_files_listed"] = expected_files.issubset(found_files)
            
            # Test 5: File deletion
            delete_results = {}
            for test_file in test_files:
                file_name = test_file["name"]
                
                try:
                    start_time = time.time()
                    self.client.remove_object(self.test_bucket, file_name)
                    delete_time = (time.time() - start_time) * 1000
                    
                    delete_results[file_name] = {
                        "delete_successful": True,
                        "delete_time_ms": delete_time
                    }
                    
                except Exception as e:
                    delete_results[file_name] = {
                        "delete_successful": False,
                        "error": str(e)
                    }
            
            results["delete_operations"] = delete_results
            results["all_deletes_successful"] = all(
                r["delete_successful"] for r in delete_results.values()
            )
            
            # Clean up test bucket if we created it
            if results.get("bucket_created"):
                try:
                    self.client.remove_bucket(self.test_bucket)
                    results["test_bucket_cleaned"] = True
                except Exception:
                    results["test_bucket_cleanup_failed"] = True
            
            # Assessment
            critical_operations = [
                results["all_uploads_successful"],
                results["all_downloads_successful"],
                results["all_files_listed"],
                results["all_deletes_successful"]
            ]
            
            if all(critical_operations):
                avg_upload_time = sum(r["upload_time_ms"] for r in upload_results.values()) / len(upload_results)
                avg_download_time = sum(r["download_time_ms"] for r in download_results.values() if r["download_successful"]) / len([r for r in download_results.values() if r["download_successful"]])
                
                return {
                    "status": "PASS",
                    "message": f"MinIO functionality complete (avg upload: {avg_upload_time:.1f}ms, download: {avg_download_time:.1f}ms)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "file_operations",
                            "data": {"uploads": upload_results, "downloads": download_results},
                            "description": "MinIO file operation test results"
                        }
                    ]
                }
            else:
                failed_operations = []
                if not results["all_uploads_successful"]:
                    failed_operations.append("upload")
                if not results["all_downloads_successful"]: 
                    failed_operations.append("download")
                if not results["all_files_listed"]:
                    failed_operations.append("listing")
                if not results["all_deletes_successful"]:
                    failed_operations.append("deletion")
                
                return {
                    "status": "FAIL",
                    "message": f"MinIO functionality failed: {', '.join(failed_operations)}",
                    "details": results,
                    "recommendations": [
                        "Check MinIO server status and logs",
                        "Verify bucket permissions and policies",
                        "Check network connectivity to MinIO server",
                        "Review access key and secret key configuration"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"MinIO functionality test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_production_buckets(self) -> Dict[str, Any]:
        """Check production bucket configuration"""
        if not self.client:
            return {
                "status": "SKIP",
                "message": "Skipping production bucket test - no MinIO connection",
                "details": {}
            }
        
        try:
            results = {}
            bucket_info = {}
            
            for bucket_name in self.production_buckets:
                try:
                    # Check if bucket exists
                    exists = self.client.bucket_exists(bucket_name)
                    bucket_info[bucket_name] = {"exists": exists}
                    
                    if exists:
                        # Get bucket policy if available
                        try:
                            policy = self.client.get_bucket_policy(bucket_name)
                            bucket_info[bucket_name]["has_policy"] = bool(policy)
                        except S3Error:
                            bucket_info[bucket_name]["has_policy"] = False
                        
                        # Count objects in bucket
                        objects = list(self.client.list_objects(bucket_name, recursive=True))
                        bucket_info[bucket_name]["object_count"] = len(objects)
                        
                        # Calculate total size
                        total_size = sum(obj.size for obj in objects if obj.size)
                        bucket_info[bucket_name]["total_size_bytes"] = total_size
                        bucket_info[bucket_name]["total_size_mb"] = total_size / (1024 * 1024)
                        
                except Exception as e:
                    bucket_info[bucket_name] = {
                        "error": str(e),
                        "exists": False
                    }
            
            results["production_buckets"] = bucket_info
            results["buckets_exist"] = sum(1 for info in bucket_info.values() if info.get("exists", False))
            results["total_objects"] = sum(info.get("object_count", 0) for info in bucket_info.values())
            results["total_storage_mb"] = sum(info.get("total_size_mb", 0) for info in bucket_info.values())
            
            # Assessment
            existing_buckets = [name for name, info in bucket_info.items() if info.get("exists", False)]
            
            if len(existing_buckets) == len(self.production_buckets):
                return {
                    "status": "PASS",
                    "message": f"All production buckets available ({len(existing_buckets)}/{len(self.production_buckets)})",
                    "details": results,
                    "evidence": [
                        {
                            "type": "bucket_info",
                            "data": bucket_info,
                            "description": "Production bucket status and statistics"
                        }
                    ]
                }
            elif len(existing_buckets) > 0:
                missing_buckets = [name for name in self.production_buckets if name not in existing_buckets]
                return {
                    "status": "WARNING",
                    "message": f"Some production buckets missing: {missing_buckets}",
                    "details": results,
                    "recommendations": [
                        f"Create missing buckets: {', '.join(missing_buckets)}",
                        "Check bucket naming conventions",
                        "Verify MinIO server configuration",
                        "Review bucket creation permissions"
                    ]
                }
            else:
                return {
                    "status": "FAIL", 
                    "message": "No production buckets found",
                    "details": results,
                    "recommendations": [
                        "Create required production buckets",
                        "Check MinIO server initialization",
                        "Verify access permissions",
                        "Review application configuration"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"Production bucket test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_performance(self) -> Dict[str, Any]:
        """Test MinIO performance characteristics"""
        if not self.client:
            return {
                "status": "SKIP",
                "message": "Skipping performance test - no MinIO connection",
                "details": {}
            }
        
        try:
            results = {}
            
            # Ensure test bucket exists
            if not self.client.bucket_exists(self.test_bucket):
                self.client.make_bucket(self.test_bucket)
            
            # Test 1: Upload performance with different file sizes
            test_sizes = [1, 10, 100, 500]  # KB
            upload_performance = {}
            
            for size_kb in test_sizes:
                file_name = f"perf_test_{size_kb}kb.dat"
                file_content = b"x" * (size_kb * 1024)
                content_stream = io.BytesIO(file_content)
                
                start_time = time.time()
                self.client.put_object(
                    self.test_bucket,
                    file_name,
                    content_stream,
                    length=len(file_content)
                )
                upload_time = (time.time() - start_time) * 1000
                
                upload_performance[f"{size_kb}kb"] = {
                    "upload_time_ms": upload_time,
                    "upload_speed_mbps": (size_kb / 1024) / (upload_time / 1000) if upload_time > 0 else 0
                }
            
            results["upload_performance"] = upload_performance
            
            # Test 2: Download performance
            download_performance = {}
            
            for size_kb in test_sizes:
                file_name = f"perf_test_{size_kb}kb.dat"
                
                start_time = time.time()
                response = self.client.get_object(self.test_bucket, file_name)
                downloaded_data = response.read()
                download_time = (time.time() - start_time) * 1000
                
                download_performance[f"{size_kb}kb"] = {
                    "download_time_ms": download_time,
                    "download_speed_mbps": (len(downloaded_data) / 1024 / 1024) / (download_time / 1000) if download_time > 0 else 0,
                    "size_correct": len(downloaded_data) == size_kb * 1024
                }
                
                response.close()
                response.release_conn()
            
            results["download_performance"] = download_performance
            
            # Test 3: Concurrent operations performance
            concurrent_files = []
            for i in range(10):
                concurrent_files.append({
                    "name": f"concurrent_test_{i}.txt",
                    "content": f"Concurrent test file {i} content " * 50
                })
            
            async def upload_file_async(file_info):
                """Async wrapper for file upload"""
                def upload():
                    content_bytes = file_info["content"].encode('utf-8')
                    content_stream = io.BytesIO(content_bytes)
                    
                    start_time = time.time()
                    self.client.put_object(
                        self.test_bucket,
                        file_info["name"],
                        content_stream,
                        length=len(content_bytes),
                        content_type="text/plain"
                    )
                    return (time.time() - start_time) * 1000
                
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, upload)
            
            # Execute concurrent uploads
            start_time = time.time()
            concurrent_upload_times = await asyncio.gather(*[
                upload_file_async(file_info) for file_info in concurrent_files
            ])
            total_concurrent_time = (time.time() - start_time) * 1000
            
            results["concurrent_upload_performance"] = {
                "total_files": len(concurrent_files),
                "total_time_ms": total_concurrent_time,
                "avg_file_time_ms": sum(concurrent_upload_times) / len(concurrent_upload_times),
                "max_file_time_ms": max(concurrent_upload_times),
                "files_per_second": len(concurrent_files) / (total_concurrent_time / 1000)
            }
            
            # Test 4: Large file handling
            large_file_size_mb = 5  # 5MB test file
            large_file_name = "large_file_test.dat"
            large_file_content = b"x" * (large_file_size_mb * 1024 * 1024)
            
            start_time = time.time()
            content_stream = io.BytesIO(large_file_content)
            self.client.put_object(
                self.test_bucket,
                large_file_name,
                content_stream,
                length=len(large_file_content)
            )
            large_upload_time = (time.time() - start_time) * 1000
            
            start_time = time.time()
            response = self.client.get_object(self.test_bucket, large_file_name)
            downloaded_large = response.read()
            large_download_time = (time.time() - start_time) * 1000
            
            response.close()
            response.release_conn()
            
            results["large_file_performance"] = {
                "size_mb": large_file_size_mb,
                "upload_time_ms": large_upload_time,
                "download_time_ms": large_download_time,
                "upload_speed_mbps": large_file_size_mb / (large_upload_time / 1000),
                "download_speed_mbps": large_file_size_mb / (large_download_time / 1000),
                "data_integrity": len(downloaded_large) == len(large_file_content)
            }
            
            # Clean up all test files
            cleanup_files = [f"perf_test_{s}kb.dat" for s in test_sizes] + \
                          [f["name"] for f in concurrent_files] + \
                          [large_file_name]
            
            for file_name in cleanup_files:
                try:
                    self.client.remove_object(self.test_bucket, file_name)
                except Exception:
                    pass  # Ignore cleanup errors
            
            # Performance assessment
            thresholds = self.config.get("performance_thresholds", {})
            upload_threshold_mbps = thresholds.get("upload_speed_mbps", 10)
            download_threshold_mbps = thresholds.get("download_speed_mbps", 50)
            
            performance_issues = []
            
            # Check upload speeds
            for size, perf in upload_performance.items():
                if perf["upload_speed_mbps"] < upload_threshold_mbps:
                    performance_issues.append(f"Upload speed for {size} ({perf['upload_speed_mbps']:.1f} Mbps) below threshold ({upload_threshold_mbps} Mbps)")
            
            # Check download speeds
            for size, perf in download_performance.items():
                if perf["download_speed_mbps"] < download_threshold_mbps:
                    performance_issues.append(f"Download speed for {size} ({perf['download_speed_mbps']:.1f} Mbps) below threshold ({download_threshold_mbps} Mbps)")
            
            if performance_issues:
                return {
                    "status": "WARNING",
                    "message": f"MinIO performance issues: {len(performance_issues)} issues",
                    "details": results,
                    "recommendations": [
                        "Check MinIO server disk I/O performance",
                        "Review network bandwidth to MinIO server",
                        "Consider MinIO server hardware upgrade",
                        "Check for concurrent load on storage system"
                    ] + performance_issues
                }
            else:
                return {
                    "status": "PASS",
                    "message": f"MinIO performance excellent (large file: {results['large_file_performance']['upload_speed_mbps']:.1f} Mbps up, {results['large_file_performance']['download_speed_mbps']:.1f} Mbps down)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "performance_metrics",
                            "data": {
                                "upload_performance": upload_performance,
                                "download_performance": download_performance,
                                "concurrent_performance": results["concurrent_upload_performance"]
                            },
                            "description": "MinIO performance test measurements"
                        }
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"MinIO performance test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def verify(self) -> VerificationSummary:
        """Execute all MinIO verification tests"""
        self.start_time = datetime.now(timezone.utc)
        
        try:
            # Test 1: Basic connectivity
            await self.run_test(
                "minio_connectivity",
                self.check_connectivity,
                VerificationSeverity.CRITICAL
            )
            
            # Test 2: Core functionality 
            await self.run_test(
                "minio_functionality",
                self.check_functionality,
                VerificationSeverity.HIGH
            )
            
            # Test 3: Production bucket status
            await self.run_test(
                "production_buckets",
                self.check_production_buckets,
                VerificationSeverity.MEDIUM
            )
            
            # Test 4: Performance characteristics
            await self.run_test(
                "minio_performance",
                self.check_performance,
                VerificationSeverity.MEDIUM
            )
            
        except Exception as e:
            self.log_error(f"MinIO verification failed: {e}")
        
        return self.get_summary()