"""
Redis cache verification
"""
import asyncio
import json
import time
from typing import Dict, Any, List
from datetime import datetime, timezone

import redis.asyncio as redis

from ..base_verifier import ComponentVerifier, VerificationStatus, VerificationSeverity, VerificationSummary


class RedisVerifier(ComponentVerifier):
    """Verify Redis cache functionality"""
    
    def __init__(self, connection_config: Dict[str, Any]):
        super().__init__("Redis Cache", connection_config)
        self.redis_client = None
        
        # Extract connection parameters
        self.host = connection_config.get("host", "localhost")
        self.port = connection_config.get("port", 6379)
        self.password = connection_config.get("password")
        self.db = connection_config.get("db", 0)
    
    async def check_connectivity(self) -> Dict[str, Any]:
        """Test Redis connectivity and basic operations"""
        try:
            # Create Redis connection
            self.redis_client = redis.Redis(
                host=self.host,
                port=self.port,
                password=self.password,
                db=self.db,
                decode_responses=True,
                socket_timeout=5.0,
                socket_connect_timeout=5.0
            )
            
            # Test ping
            start_time = time.time()
            ping_result = await self.redis_client.ping()
            ping_time = (time.time() - start_time) * 1000
            
            if ping_result:
                return {
                    "status": "PASS",
                    "message": "Redis connection successful",
                    "details": {
                        "host": self.host,
                        "port": self.port, 
                        "database": self.db,
                        "ping_time_ms": ping_time
                    },
                    "evidence": [
                        {
                            "type": "connection_test",
                            "data": {"ping_result": ping_result, "response_time_ms": ping_time},
                            "description": "Redis ping test result"
                        }
                    ]
                }
            else:
                return {
                    "status": "FAIL",
                    "message": "Redis ping failed",
                    "details": {"ping_result": ping_result}
                }
                
        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"Redis connection failed: {str(e)}",
                "details": {
                    "error_type": type(e).__name__,
                    "connection_string": f"redis://{self.host}:{self.port}/{self.db}"
                }
            }
    
    async def check_functionality(self) -> Dict[str, Any]:
        """Test Redis core functionality"""
        if not self.redis_client:
            return {
                "status": "SKIP",
                "message": "Skipping functionality test - no Redis connection",
                "details": {}
            }
        
        try:
            results = {}
            test_key_prefix = "verification_test"
            
            # Test 1: Basic SET/GET operations
            test_data = {
                "string_value": "Hello Redis",
                "number_value": "42",
                "json_value": json.dumps({"test": True, "timestamp": datetime.now(timezone.utc).isoformat()})
            }
            
            set_results = {}
            get_results = {}
            
            for key_suffix, value in test_data.items():
                test_key = f"{test_key_prefix}:{key_suffix}"
                
                # SET operation
                start_time = time.time()
                set_result = await self.redis_client.set(test_key, value, ex=60)  # 60 second expiry
                set_time = (time.time() - start_time) * 1000
                set_results[key_suffix] = {"success": bool(set_result), "time_ms": set_time}
                
                # GET operation
                start_time = time.time() 
                get_result = await self.redis_client.get(test_key)
                get_time = (time.time() - start_time) * 1000
                get_results[key_suffix] = {
                    "success": get_result == value,
                    "time_ms": get_time,
                    "value_match": get_result == value
                }
            
            results["set_operations"] = set_results
            results["get_operations"] = get_results
            
            # Test 2: TTL and expiration
            ttl_test_key = f"{test_key_prefix}:ttl_test"
            await self.redis_client.set(ttl_test_key, "expires_soon", ex=2)
            
            ttl_before = await self.redis_client.ttl(ttl_test_key)
            results["ttl_set_correctly"] = 0 < ttl_before <= 2
            
            # Wait for expiration
            await asyncio.sleep(3)
            
            exists_after_expiry = await self.redis_client.exists(ttl_test_key)
            results["expiration_works"] = not exists_after_expiry
            
            # Test 3: Hash operations (used for monitoring cache)
            hash_key = f"{test_key_prefix}:hash_test"
            hash_data = {
                "field1": "value1",
                "field2": "value2", 
                "numeric_field": "123"
            }
            
            await self.redis_client.hset(hash_key, mapping=hash_data)
            
            retrieved_hash = await self.redis_client.hgetall(hash_key)
            results["hash_operations_work"] = retrieved_hash == hash_data
            
            # Test 4: List operations
            list_key = f"{test_key_prefix}:list_test"
            list_items = ["item1", "item2", "item3"]
            
            for item in list_items:
                await self.redis_client.lpush(list_key, item)
            
            list_length = await self.redis_client.llen(list_key)
            retrieved_list = await self.redis_client.lrange(list_key, 0, -1)
            
            results["list_operations_work"] = list_length == len(list_items)
            results["list_data_integrity"] = set(retrieved_list) == set(list_items)
            
            # Clean up test data
            test_keys = await self.redis_client.keys(f"{test_key_prefix}:*")
            if test_keys:
                await self.redis_client.delete(*test_keys)
                results["cleanup_successful"] = True
            
            # Assessment
            critical_operations = [
                all(op["success"] for op in set_results.values()),
                all(op["success"] and op["value_match"] for op in get_results.values()),
                results["expiration_works"],
                results["hash_operations_work"],
                results["list_operations_work"]
            ]
            
            if all(critical_operations):
                avg_set_time = sum(op["time_ms"] for op in set_results.values()) / len(set_results)
                avg_get_time = sum(op["time_ms"] for op in get_results.values()) / len(get_results)
                
                return {
                    "status": "PASS",
                    "message": f"All Redis functionality tests passed (avg SET: {avg_set_time:.1f}ms, GET: {avg_get_time:.1f}ms)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "redis_operations", 
                            "data": {"set_results": set_results, "get_results": get_results},
                            "description": "Redis operation performance metrics"
                        }
                    ]
                }
            else:
                failed_operations = [k for k, v in results.items() if isinstance(v, bool) and not v]
                return {
                    "status": "FAIL",
                    "message": f"Redis functionality failed: {failed_operations}",
                    "details": results,
                    "recommendations": [
                        "Check Redis server configuration",
                        "Verify Redis memory settings",
                        "Check network connectivity to Redis server",
                        "Review Redis logs for errors"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"Redis functionality test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_monitoring_cache(self) -> Dict[str, Any]:
        """Test monitoring-specific cache functionality"""
        if not self.redis_client:
            return {
                "status": "SKIP",
                "message": "Skipping monitoring cache test - no Redis connection",
                "details": {}
            }
        
        try:
            results = {}
            
            # Test monitoring cache patterns used in the application
            cache_prefix = "monitoring"
            cache_version = "v1"
            
            # Test 1: System health cache
            health_data = {
                "status": "healthy",
                "services": {
                    "postgresql": {"status": "up", "response_time_ms": 45},
                    "redis": {"status": "up", "response_time_ms": 8},
                    "minio": {"status": "up", "response_time_ms": 102}
                },
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            health_key = f"{cache_prefix}:system_health:{cache_version}"
            
            # Store with TTL
            start_time = time.time()
            set_success = await self.redis_client.setex(
                health_key,
                300,  # 5 minute TTL
                json.dumps(health_data)
            )
            set_time = (time.time() - start_time) * 1000
            
            # Retrieve and verify
            start_time = time.time()
            cached_health = await self.redis_client.get(health_key)
            get_time = (time.time() - start_time) * 1000
            
            if cached_health:
                parsed_health = json.loads(cached_health)
                results["health_cache_works"] = parsed_health == health_data
            else:
                results["health_cache_works"] = False
            
            results["health_cache_set_time_ms"] = set_time
            results["health_cache_get_time_ms"] = get_time
            
            # Test 2: Service-specific cache
            service_caches = {}
            for service_name in ["postgresql", "minio", "ollama"]:
                service_key = f"{cache_prefix}:service:{service_name}:{cache_version}"
                service_data = {
                    "status": "healthy",
                    "last_check": datetime.now(timezone.utc).isoformat(),
                    "metrics": {"response_time": random.uniform(10, 100)}
                }
                
                await self.redis_client.setex(service_key, 300, json.dumps(service_data))
                cached_service = await self.redis_client.get(service_key)
                
                service_caches[service_name] = {
                    "cache_works": json.loads(cached_service) == service_data if cached_service else False,
                    "ttl_seconds": await self.redis_client.ttl(service_key)
                }
            
            results["service_caches"] = service_caches
            results["all_service_caches_work"] = all(
                cache["cache_works"] for cache in service_caches.values()
            )
            
            # Test 3: Cache pattern search
            pattern = f"{cache_prefix}:*"
            cache_keys = await self.redis_client.keys(pattern)
            results["cache_key_discovery"] = len(cache_keys)
            results["cache_keys"] = cache_keys
            
            # Test 4: Cache info and statistics
            try:
                info = await self.redis_client.info("memory")
                results["redis_memory_info"] = {
                    "used_memory": info.get("used_memory"),
                    "used_memory_human": info.get("used_memory_human"),
                    "maxmemory": info.get("maxmemory")
                }
            except Exception:
                results["redis_memory_info"] = "unavailable"
            
            # Clean up test data
            if cache_keys:
                await self.redis_client.delete(*cache_keys)
                results["cleanup_successful"] = True
            
            # Assessment
            if (results["health_cache_works"] and 
                results["all_service_caches_work"] and 
                results["cache_key_discovery"] > 0):
                
                return {
                    "status": "PASS",
                    "message": f"Monitoring cache fully functional ({len(service_caches)} services cached)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "monitoring_cache",
                            "data": {"cache_operations": service_caches, "performance": {
                                "set_time_ms": set_time, "get_time_ms": get_time
                            }},
                            "description": "Monitoring cache operation results"
                        }
                    ]
                }
            else:
                issues = []
                if not results["health_cache_works"]:
                    issues.append("System health cache not working")
                if not results["all_service_caches_work"]:
                    issues.append("Service-specific cache issues")
                
                return {
                    "status": "FAIL",
                    "message": f"Monitoring cache issues: {', '.join(issues)}",
                    "details": results,
                    "recommendations": [
                        "Check Redis memory configuration",
                        "Verify JSON serialization/deserialization",
                        "Check cache key naming conventions",
                        "Review TTL settings for monitoring data"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR", 
                "message": f"Monitoring cache test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_performance(self) -> Dict[str, Any]:
        """Test Redis performance characteristics"""
        if not self.redis_client:
            return {
                "status": "SKIP",
                "message": "Skipping performance test - no Redis connection", 
                "details": {}
            }
        
        try:
            results = {}
            
            # Test 1: Operation latency
            operation_times = {
                "set": [],
                "get": [],
                "delete": [],
                "exists": []
            }
            
            test_keys = [f"perf_test_{i}" for i in range(10)]
            test_value = "performance_test_value" * 10  # ~200 chars
            
            # SET performance
            for key in test_keys:
                start_time = time.time()
                await self.redis_client.set(key, test_value)
                operation_times["set"].append((time.time() - start_time) * 1000)
            
            # GET performance
            for key in test_keys:
                start_time = time.time()
                value = await self.redis_client.get(key)
                operation_times["get"].append((time.time() - start_time) * 1000)
                results[f"get_accuracy_{key}"] = value == test_value
            
            # EXISTS performance
            for key in test_keys:
                start_time = time.time()
                exists = await self.redis_client.exists(key)
                operation_times["exists"].append((time.time() - start_time) * 1000)
            
            # DELETE performance
            for key in test_keys:
                start_time = time.time()
                deleted = await self.redis_client.delete(key)
                operation_times["delete"].append((time.time() - start_time) * 1000)
            
            # Calculate averages
            for operation, times in operation_times.items():
                results[f"avg_{operation}_time_ms"] = sum(times) / len(times)
                results[f"max_{operation}_time_ms"] = max(times)
                results[f"min_{operation}_time_ms"] = min(times)
            
            # Test 2: Concurrent operations performance
            concurrent_test_data = []
            for i in range(20):
                concurrent_test_data.append({
                    "key": f"concurrent_test_{i}",
                    "value": f"concurrent_value_{i}"
                })
            
            async def concurrent_set_get(data):
                """Concurrent set and get operation"""
                start_time = time.time()
                await self.redis_client.set(data["key"], data["value"])
                retrieved = await self.redis_client.get(data["key"])
                duration = (time.time() - start_time) * 1000
                return {
                    "duration_ms": duration,
                    "success": retrieved == data["value"]
                }
            
            start_time = time.time()
            concurrent_results = await asyncio.gather(*[
                concurrent_set_get(data) for data in concurrent_test_data
            ])
            total_concurrent_time = (time.time() - start_time) * 1000
            
            successful_concurrent = [r for r in concurrent_results if r["success"]]
            results["concurrent_operations"] = {
                "total_operations": len(concurrent_test_data),
                "successful": len(successful_concurrent), 
                "success_rate": len(successful_concurrent) / len(concurrent_test_data),
                "total_time_ms": total_concurrent_time,
                "avg_operation_time_ms": sum(r["duration_ms"] for r in successful_concurrent) / len(successful_concurrent) if successful_concurrent else 0,
                "operations_per_second": len(concurrent_test_data) / (total_concurrent_time / 1000)
            }
            
            # Test 3: Memory usage test
            try:
                info = await self.redis_client.info("memory")
                results["memory_usage"] = {
                    "used_memory": info.get("used_memory", 0),
                    "used_memory_human": info.get("used_memory_human", "unknown"),
                    "used_memory_peak": info.get("used_memory_peak", 0),
                    "total_system_memory": info.get("total_system_memory", 0)
                }
            except Exception:
                results["memory_usage"] = "unavailable"
            
            # Clean up concurrent test data
            concurrent_keys = [data["key"] for data in concurrent_test_data]
            if concurrent_keys:
                await self.redis_client.delete(*concurrent_keys)
            
            # Performance assessment
            thresholds = self.config.get("performance_thresholds", {})
            set_threshold = thresholds.get("set_operation_ms", 5)
            get_threshold = thresholds.get("get_operation_ms", 5)
            
            performance_issues = []
            if results["avg_set_time_ms"] > set_threshold:
                performance_issues.append(f"SET operations slow ({results['avg_set_time_ms']:.1f}ms > {set_threshold}ms)")
            
            if results["avg_get_time_ms"] > get_threshold:
                performance_issues.append(f"GET operations slow ({results['avg_get_time_ms']:.1f}ms > {get_threshold}ms)")
            
            if results["concurrent_operations"]["success_rate"] < 0.95:
                performance_issues.append(f"Concurrent operation success rate low ({results['concurrent_operations']['success_rate']:.1%})")
            
            if performance_issues:
                return {
                    "status": "WARNING",
                    "message": f"Redis performance issues: {len(performance_issues)} issues",
                    "details": results,
                    "recommendations": [
                        "Check Redis server load and available memory",
                        "Review Redis configuration (maxmemory, timeout settings)",
                        "Consider connection pooling optimization",
                        "Monitor network latency to Redis server"
                    ] + performance_issues
                }
            else:
                return {
                    "status": "PASS",
                    "message": f"Redis performance excellent (SET: {results['avg_set_time_ms']:.1f}ms, GET: {results['avg_get_time_ms']:.1f}ms)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "performance_metrics",
                            "data": operation_times,
                            "description": "Redis operation performance measurements"
                        },
                        {
                            "type": "concurrent_performance",
                            "data": results["concurrent_operations"],
                            "description": "Concurrent operation test results"
                        }
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"Redis performance test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def verify(self) -> VerificationSummary:
        """Execute all Redis verification tests"""
        self.start_time = datetime.now(timezone.utc)
        
        try:
            # Test 1: Basic connectivity
            await self.run_test(
                "redis_connectivity",
                self.check_connectivity,
                VerificationSeverity.CRITICAL
            )
            
            # Test 2: Core functionality
            await self.run_test(
                "redis_functionality", 
                self.check_functionality,
                VerificationSeverity.HIGH
            )
            
            # Test 3: Monitoring cache patterns
            await self.run_test(
                "monitoring_cache",
                self.check_monitoring_cache,
                VerificationSeverity.MEDIUM
            )
            
            # Test 4: Performance characteristics
            await self.run_test(
                "redis_performance",
                self.check_performance, 
                VerificationSeverity.MEDIUM
            )
            
        finally:
            # Clean up Redis connection
            if self.redis_client:
                await self.redis_client.close()
        
        return self.get_summary()