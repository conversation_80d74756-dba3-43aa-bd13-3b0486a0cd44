"""
Database verification for PostgreSQL with pgvector extension
"""
import asyncio
import json
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

import asyncpg
import sqlalchemy as sa
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.sql import text

from ..base_verifier import ComponentVerifier, VerificationStatus, VerificationSeverity, VerificationSummary


class DatabaseVerifier(ComponentVerifier):
    """Verify PostgreSQL database with pgvector extension"""
    
    def __init__(self, connection_config: Dict[str, Any]):
        super().__init__("PostgreSQL + pgvector", connection_config)
        self.engine = None
        self.connection = None
        
        # Extract connection parameters
        self.host = connection_config.get("host", "localhost")
        self.port = connection_config.get("port", 5432)
        self.database = connection_config.get("database", "talentforge") 
        self.user = connection_config.get("user", "tfuser")
        self.password = connection_config.get("password", "tfpass123")
        
        self.async_db_url = f"postgresql+asyncpg://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"
        self.sync_db_url = f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.database}"
    
    async def check_connectivity(self) -> Dict[str, Any]:
        """Test basic database connectivity"""
        try:
            # Test with asyncpg directly for fastest connection test
            self.connection = await asyncpg.connect(
                host=self.host,
                port=self.port,
                database=self.database,
                user=self.user,
                password=self.password,
                command_timeout=5
            )
            
            # Test basic query
            result = await self.connection.fetchval("SELECT 1")
            
            if result == 1:
                return {
                    "status": "PASS",
                    "message": "Database connection successful",
                    "details": {
                        "host": self.host,
                        "port": self.port,
                        "database": self.database,
                        "connection_type": "asyncpg"
                    }
                }
            else:
                return {
                    "status": "FAIL", 
                    "message": "Database query test failed",
                    "details": {"expected": 1, "actual": result}
                }
                
        except Exception as e:
            return {
                "status": "FAIL",
                "message": f"Database connection failed: {str(e)}",
                "details": {
                    "error_type": type(e).__name__,
                    "connection_string": f"postgresql://{self.user}:***@{self.host}:{self.port}/{self.database}"
                }
            }
    
    async def check_functionality(self) -> Dict[str, Any]:
        """Test core database functionality"""
        if not self.connection:
            return {
                "status": "SKIP",
                "message": "Skipping functionality test - no database connection",
                "details": {}
            }
        
        try:
            results = {}
            
            # Test 1: Basic CRUD operations
            test_table = "verification_test_table"
            
            # Create test table
            await self.connection.execute(f"""
                CREATE TABLE IF NOT EXISTS {test_table} (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(100),
                    created_at TIMESTAMP DEFAULT NOW()
                )
            """)
            
            # Insert test data
            insert_id = await self.connection.fetchval(f"""
                INSERT INTO {test_table} (name) VALUES ('test_record') RETURNING id
            """)
            results["insert_works"] = insert_id is not None
            
            # Select test data
            selected_name = await self.connection.fetchval(f"""
                SELECT name FROM {test_table} WHERE id = $1
            """, insert_id)
            results["select_works"] = selected_name == "test_record"
            
            # Update test data
            await self.connection.execute(f"""
                UPDATE {test_table} SET name = 'updated_record' WHERE id = $1
            """, insert_id)
            
            updated_name = await self.connection.fetchval(f"""
                SELECT name FROM {test_table} WHERE id = $1
            """, insert_id)
            results["update_works"] = updated_name == "updated_record"
            
            # Delete test data
            deleted_count = await self.connection.fetchval(f"""
                DELETE FROM {test_table} WHERE id = $1
            """, insert_id)
            results["delete_works"] = deleted_count == 1
            
            # Clean up test table
            await self.connection.execute(f"DROP TABLE IF EXISTS {test_table}")
            results["cleanup_successful"] = True
            
            # Test 2: Transaction handling
            async with self.connection.transaction():
                await self.connection.execute(f"""
                    CREATE TEMP TABLE tx_test (id INT, value TEXT)
                """)
                await self.connection.execute("""
                    INSERT INTO tx_test VALUES (1, 'test')
                """)
                
                # Verify data exists within transaction
                count = await self.connection.fetchval("SELECT COUNT(*) FROM tx_test")
                results["transaction_works"] = count == 1
            
            # Test 3: Check database version and features
            version = await self.connection.fetchval("SELECT version()")
            results["postgresql_version"] = version
            
            all_passed = all([
                results["insert_works"],
                results["select_works"],
                results["update_works"], 
                results["delete_works"],
                results["transaction_works"]
            ])
            
            if all_passed:
                return {
                    "status": "PASS",
                    "message": "All database functionality tests passed",
                    "details": results,
                    "evidence": [
                        {
                            "type": "database_operations",
                            "data": results,
                            "description": "Database CRUD and transaction test results"
                        }
                    ]
                }
            else:
                failed_operations = [k for k, v in results.items() if isinstance(v, bool) and not v]
                return {
                    "status": "FAIL",
                    "message": f"Database functionality test failed: {failed_operations}",
                    "details": results,
                    "recommendations": [
                        "Check database permissions",
                        "Verify table creation privileges",
                        "Check transaction isolation settings"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"Database functionality test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_pgvector_extension(self) -> Dict[str, Any]:
        """Test pgvector extension functionality"""
        if not self.connection:
            return {
                "status": "SKIP",
                "message": "Skipping pgvector test - no database connection",
                "details": {}
            }
        
        try:
            results = {}
            
            # Test 1: Check if pgvector extension exists
            extension_exists = await self.connection.fetchval("""
                SELECT EXISTS(
                    SELECT 1 FROM pg_extension WHERE extname = 'vector'
                )
            """)
            results["extension_installed"] = extension_exists
            
            if not extension_exists:
                return {
                    "status": "FAIL",
                    "message": "pgvector extension is not installed",
                    "details": results,
                    "recommendations": [
                        "Install pgvector extension: CREATE EXTENSION vector;",
                        "Check if extension files are available in PostgreSQL"
                    ]
                }
            
            # Test 2: Create test table with vector column
            test_table = "verification_vector_test"
            await self.connection.execute(f"""
                CREATE TABLE IF NOT EXISTS {test_table} (
                    id SERIAL PRIMARY KEY,
                    embedding vector(3),
                    metadata JSONB
                )
            """)
            results["vector_table_created"] = True
            
            # Test 3: Insert test vectors
            test_vectors = [
                [0.1, 0.2, 0.3],
                [0.4, 0.5, 0.6],
                [0.7, 0.8, 0.9]
            ]
            
            for i, vector in enumerate(test_vectors):
                await self.connection.execute(f"""
                    INSERT INTO {test_table} (embedding, metadata) 
                    VALUES ($1, $2)
                """, vector, json.dumps({"test_id": i}))
            
            results["vector_insert_works"] = True
            
            # Test 4: Vector similarity search
            query_vector = [0.35, 0.45, 0.55]  # Close to second vector
            
            similar_results = await self.connection.fetch(f"""
                SELECT id, embedding <-> $1 as distance, metadata
                FROM {test_table}
                ORDER BY embedding <-> $1
                LIMIT 3
            """, query_vector)
            
            results["similarity_search_works"] = len(similar_results) == 3
            results["closest_vector_id"] = similar_results[0]["id"] if similar_results else None
            
            # The closest should be vector index 1 (0.4, 0.5, 0.6)
            expected_closest_id = 2  # Second inserted vector (id starts from 1)
            results["similarity_accuracy"] = results["closest_vector_id"] == expected_closest_id
            
            # Test 5: Vector operations and indexing
            await self.connection.execute(f"""
                CREATE INDEX IF NOT EXISTS verification_vector_idx 
                ON {test_table} USING hnsw (embedding vector_l2_ops)
            """)
            results["vector_index_created"] = True
            
            # Test index usage
            explain_result = await self.connection.fetchval(f"""
                EXPLAIN (FORMAT JSON) 
                SELECT * FROM {test_table} 
                ORDER BY embedding <-> $1 LIMIT 1
            """, query_vector)
            
            results["index_usage"] = "Index Scan" in str(explain_result)
            
            # Clean up
            await self.connection.execute(f"DROP TABLE IF EXISTS {test_table}")
            results["cleanup_successful"] = True
            
            # Check overall success
            critical_tests = [
                results["extension_installed"],
                results["vector_table_created"], 
                results["vector_insert_works"],
                results["similarity_search_works"]
            ]
            
            if all(critical_tests):
                return {
                    "status": "PASS",
                    "message": "pgvector extension fully functional",
                    "details": results,
                    "evidence": [
                        {
                            "type": "vector_operations",
                            "data": {
                                "test_vectors": test_vectors,
                                "query_vector": query_vector,
                                "search_results": [dict(r) for r in similar_results]
                            },
                            "description": "Vector similarity search test results"
                        }
                    ]
                }
            else:
                failed_tests = [k for k, v in results.items() if isinstance(v, bool) and not v]
                return {
                    "status": "FAIL",
                    "message": f"pgvector functionality failed: {failed_tests}",
                    "details": results,
                    "recommendations": [
                        "Verify pgvector installation",
                        "Check vector column type support",
                        "Test vector index creation permissions"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"pgvector test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_performance(self) -> Dict[str, Any]:
        """Test database performance characteristics"""
        if not self.connection:
            return {
                "status": "SKIP",
                "message": "Skipping performance test - no database connection",
                "details": {}
            }
        
        try:
            results = {}
            
            # Test 1: Connection pool performance
            import time
            
            connection_times = []
            for i in range(5):
                start_time = time.time()
                test_conn = await asyncpg.connect(
                    host=self.host, port=self.port, database=self.database,
                    user=self.user, password=self.password
                )
                connection_time = (time.time() - start_time) * 1000
                connection_times.append(connection_time)
                await test_conn.close()
            
            results["avg_connection_time_ms"] = sum(connection_times) / len(connection_times)
            results["max_connection_time_ms"] = max(connection_times)
            results["connection_times"] = connection_times
            
            # Test 2: Query performance
            query_times = []
            test_queries = [
                "SELECT 1",
                "SELECT NOW()",
                "SELECT COUNT(*) FROM information_schema.tables",
                "SELECT COUNT(*) FROM pg_stat_activity"
            ]
            
            for query in test_queries:
                start_time = time.time()
                await self.connection.fetchval(query)
                query_time = (time.time() - start_time) * 1000
                query_times.append(query_time)
            
            results["avg_query_time_ms"] = sum(query_times) / len(query_times) 
            results["max_query_time_ms"] = max(query_times)
            results["query_performance"] = dict(zip(test_queries, query_times))
            
            # Test 3: Vector performance (if pgvector available)
            try:
                # Create temp vector table for performance test
                await self.connection.execute("""
                    CREATE TEMP TABLE perf_vector_test (
                        id SERIAL PRIMARY KEY,
                        embedding vector(1024)
                    )
                """)
                
                # Insert test vectors
                import random
                vector_insert_times = []
                
                for i in range(10):
                    vector = [random.random() for _ in range(1024)]
                    
                    start_time = time.time()
                    await self.connection.execute("""
                        INSERT INTO perf_vector_test (embedding) VALUES ($1)
                    """, vector)
                    insert_time = (time.time() - start_time) * 1000
                    vector_insert_times.append(insert_time)
                
                results["avg_vector_insert_ms"] = sum(vector_insert_times) / len(vector_insert_times)
                results["max_vector_insert_ms"] = max(vector_insert_times)
                
                # Test vector search performance
                query_vector = [random.random() for _ in range(1024)]
                
                start_time = time.time()
                search_results = await self.connection.fetch("""
                    SELECT id, embedding <-> $1 as distance
                    FROM perf_vector_test
                    ORDER BY embedding <-> $1
                    LIMIT 5
                """, query_vector)
                vector_search_time = (time.time() - start_time) * 1000
                
                results["vector_search_time_ms"] = vector_search_time
                results["vector_search_results_count"] = len(search_results)
                
            except Exception as e:
                results["vector_performance_error"] = str(e)
            
            # Test 4: Database statistics
            db_stats = await self.connection.fetchrow("""
                SELECT 
                    count(*) as total_connections,
                    sum(case when state = 'active' then 1 else 0 end) as active_connections,
                    sum(case when state = 'idle' then 1 else 0 end) as idle_connections
                FROM pg_stat_activity
                WHERE datname = $1
            """, self.database)
            
            if db_stats:
                results["connection_stats"] = dict(db_stats)
            
            # Performance assessment
            thresholds = self.config.get("performance_thresholds", {})
            connection_threshold = thresholds.get("connection_time_ms", 500)
            query_threshold = thresholds.get("query_time_ms", 100)
            vector_threshold = thresholds.get("vector_search_ms", 200)
            
            performance_issues = []
            if results["avg_connection_time_ms"] > connection_threshold:
                performance_issues.append(f"Connection time ({results['avg_connection_time_ms']:.1f}ms) exceeds threshold ({connection_threshold}ms)")
            
            if results["avg_query_time_ms"] > query_threshold:
                performance_issues.append(f"Query time ({results['avg_query_time_ms']:.1f}ms) exceeds threshold ({query_threshold}ms)")
            
            if "vector_search_time_ms" in results and results["vector_search_time_ms"] > vector_threshold:
                performance_issues.append(f"Vector search time ({results['vector_search_time_ms']:.1f}ms) exceeds threshold ({vector_threshold}ms)")
            
            if performance_issues:
                return {
                    "status": "WARNING",
                    "message": f"Database performance issues detected: {len(performance_issues)} issues",
                    "details": results,
                    "recommendations": [
                        "Consider connection pooling optimization",
                        "Review database configuration (shared_buffers, work_mem)",
                        "Check for concurrent load on database",
                        "Consider adding indexes for frequently queried columns"
                    ] + performance_issues
                }
            else:
                return {
                    "status": "PASS",
                    "message": "Database performance within acceptable thresholds", 
                    "details": results,
                    "evidence": [
                        {
                            "type": "performance_metrics",
                            "data": results,
                            "description": "Database performance test results"
                        }
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR", 
                "message": f"Database performance test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_schema_integrity(self) -> Dict[str, Any]:
        """Check database schema and table integrity"""
        if not self.connection:
            return {
                "status": "SKIP", 
                "message": "Skipping schema test - no database connection",
                "details": {}
            }
        
        try:
            results = {}
            
            # Check expected tables exist
            expected_tables = [
                "users", "roles", "permissions", "user_preferences",
                "candidates", "positions", "applications",
                "resume_vectors", "job_vectors", 
                "assessments", "application_forms", "submissions"
            ]
            
            existing_tables = await self.connection.fetch("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_type = 'BASE TABLE'
            """)
            
            existing_table_names = [row["table_name"] for row in existing_tables]
            results["total_tables"] = len(existing_table_names)
            results["expected_tables"] = expected_tables
            results["existing_tables"] = existing_table_names
            
            missing_tables = [t for t in expected_tables if t not in existing_table_names]
            extra_tables = [t for t in existing_table_names if t not in expected_tables and not t.startswith("alembic")]
            
            results["missing_tables"] = missing_tables
            results["extra_tables"] = extra_tables
            
            # Check vector columns exist
            vector_columns = await self.connection.fetch("""
                SELECT table_name, column_name, data_type
                FROM information_schema.columns 
                WHERE data_type = 'USER-DEFINED' AND udt_name = 'vector'
            """)
            
            results["vector_columns"] = [
                {"table": row["table_name"], "column": row["column_name"]}
                for row in vector_columns
            ]
            results["vector_columns_count"] = len(vector_columns)
            
            # Check indexes on vector columns
            vector_indexes = await self.connection.fetch("""
                SELECT 
                    schemaname, tablename, indexname, indexdef
                FROM pg_indexes 
                WHERE indexdef LIKE '%vector%' OR indexdef LIKE '%hnsw%'
            """)
            
            results["vector_indexes"] = [dict(row) for row in vector_indexes]
            results["vector_indexes_count"] = len(vector_indexes)
            
            # Assessment
            if missing_tables:
                return {
                    "status": "FAIL",
                    "message": f"Missing required tables: {missing_tables}",
                    "details": results,
                    "recommendations": [
                        "Run database migrations: alembic upgrade head",
                        "Check if application models are properly defined",
                        "Verify database initialization completed"
                    ]
                }
            elif results["vector_columns_count"] == 0:
                return {
                    "status": "WARNING",
                    "message": "No vector columns found - vector functionality may not be available",
                    "details": results,
                    "recommendations": [
                        "Check if vector-enabled models have been created",
                        "Verify pgvector extension is properly installed",
                        "Run any pending migrations for vector columns"
                    ]
                }
            else:
                return {
                    "status": "PASS",
                    "message": f"Database schema healthy: {results['total_tables']} tables, {results['vector_columns_count']} vector columns",
                    "details": results,
                    "evidence": [
                        {
                            "type": "schema_info",
                            "data": {
                                "tables": existing_table_names,
                                "vector_columns": results["vector_columns"],
                                "indexes": results["vector_indexes"]
                            },
                            "description": "Database schema and vector configuration"
                        }
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"Schema integrity test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def verify(self) -> VerificationSummary:
        """Execute all database verification tests"""
        self.start_time = datetime.now(timezone.utc)
        
        try:
            # Test 1: Basic connectivity
            await self.run_test(
                "database_connectivity",
                self.check_connectivity,
                VerificationSeverity.CRITICAL
            )
            
            # Test 2: Core functionality 
            await self.run_test(
                "database_functionality",
                self.check_functionality,
                VerificationSeverity.HIGH
            )
            
            # Test 3: pgvector extension
            await self.run_test(
                "pgvector_extension", 
                self.check_pgvector_extension,
                VerificationSeverity.HIGH
            )
            
            # Test 4: Schema integrity
            await self.run_test(
                "schema_integrity",
                self.check_schema_integrity,
                VerificationSeverity.MEDIUM
            )
            
            # Test 5: Performance characteristics
            await self.run_test(
                "database_performance",
                self.check_performance,
                VerificationSeverity.MEDIUM
            )
            
        finally:
            # Clean up connections
            if self.connection:
                await self.connection.close()
            if self.engine:
                await self.engine.dispose()
        
        return self.get_summary()