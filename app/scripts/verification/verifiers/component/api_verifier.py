"""
Backend API verification
"""
import asyncio
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

import httpx

from ..base_verifier import ComponentVerifier, VerificationStatus, VerificationSeverity, VerificationSummary


class APIVerifier(ComponentVerifier):
    """Verify Backend API functionality and performance"""
    
    def __init__(self, connection_config: Dict[str, Any]):
        super().__init__("Backend API", connection_config)
        
        # API configuration
        self.base_url = connection_config.get("base_url", "http://localhost:8088")
        self.api_prefix = connection_config.get("api_prefix", "/api/v1")
        self.dev_token = connection_config.get("dev_token", "dev_bypass_token_2025_talentforge")
        self.timeout = connection_config.get("timeout", 30)
        
        # Test endpoints
        self.test_endpoints = {
            "health": {"method": "GET", "path": "/health", "auth_required": False},
            "auth_me": {"method": "GET", "path": "/auth/me", "auth_required": True},
            "candidates_list": {"method": "GET", "path": "/candidates/", "auth_required": True},
            "positions_list": {"method": "GET", "path": "/positions/", "auth_required": True},
            "users_list": {"method": "GET", "path": "/users/", "auth_required": True}
        }
    
    async def check_connectivity(self) -> Dict[str, Any]:
        """Test API connectivity and basic health"""
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                
                # Test 1: Health endpoint
                start_time = time.time()
                health_response = await client.get(f"{self.base_url}{self.api_prefix}/health")
                health_time = (time.time() - start_time) * 1000
                
                if health_response.status_code == 200:
                    health_data = health_response.json()
                    
                    return {
                        "status": "PASS",
                        "message": f"API healthy and responding ({health_time:.1f}ms)",
                        "details": {
                            "base_url": self.base_url,
                            "api_prefix": self.api_prefix,
                            "health_response_ms": health_time,
                            "health_data": health_data
                        },
                        "evidence": [
                            {
                                "type": "health_check",
                                "data": health_data,
                                "description": "API health endpoint response"
                            }
                        ]
                    }
                else:
                    return {
                        "status": "FAIL",
                        "message": f"API health check failed: {health_response.status_code}",
                        "details": {
                            "status_code": health_response.status_code,
                            "response_text": health_response.text[:200]
                        }
                    }
                    
        except httpx.ConnectError as e:
            return {
                "status": "FAIL",
                "message": f"Cannot connect to API server: {str(e)}",
                "details": {
                    "base_url": self.base_url,
                    "error_type": type(e).__name__
                },
                "recommendations": [
                    "Check if backend server is running",
                    "Verify API server port and host configuration",
                    "Check Docker container status",
                    "Review nginx proxy configuration"
                ]
            }
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"API connectivity test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_functionality(self) -> Dict[str, Any]:
        """Test API endpoint functionality"""
        try:
            results = {}
            endpoint_results = {}
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                
                # Test each endpoint
                for endpoint_name, endpoint_config in self.test_endpoints.items():
                    method = endpoint_config["method"]
                    path = endpoint_config["path"]
                    auth_required = endpoint_config["auth_required"]
                    
                    url = f"{self.base_url}{self.api_prefix}{path}"
                    headers = {}
                    
                    if auth_required:
                        headers["Authorization"] = f"Bearer {self.dev_token}"
                    
                    try:
                        start_time = time.time()
                        
                        if method == "GET":
                            response = await client.get(url, headers=headers)
                        elif method == "POST":
                            response = await client.post(url, headers=headers, json={})
                        else:
                            response = await client.request(method, url, headers=headers)
                        
                        response_time = (time.time() - start_time) * 1000
                        
                        endpoint_result = {
                            "status_code": response.status_code,
                            "response_time_ms": response_time,
                            "success": response.status_code < 400
                        }
                        
                        if response.status_code < 400:
                            try:
                                response_data = response.json()
                                endpoint_result["response_data"] = response_data
                                endpoint_result["has_json_response"] = True
                                
                                # Validate response structure for list endpoints
                                if path.endswith("/"):
                                    expected_fields = ["items", "total", "skip", "limit"]
                                    if isinstance(response_data, dict) and all(field in response_data for field in expected_fields):
                                        endpoint_result["pagination_format_valid"] = True
                                    else:
                                        endpoint_result["pagination_format_valid"] = False
                                        
                            except json.JSONDecodeError:
                                endpoint_result["has_json_response"] = False
                                endpoint_result["response_text"] = response.text[:200]
                        else:
                            endpoint_result["error_response"] = response.text[:200]
                        
                        endpoint_results[endpoint_name] = endpoint_result
                        
                    except httpx.TimeoutException:
                        endpoint_results[endpoint_name] = {
                            "status_code": None,
                            "success": False,
                            "error": "timeout",
                            "response_time_ms": self.timeout * 1000
                        }
                    except Exception as e:
                        endpoint_results[endpoint_name] = {
                            "status_code": None,
                            "success": False,
                            "error": str(e),
                            "error_type": type(e).__name__
                        }
            
            results["endpoint_results"] = endpoint_results
            results["total_endpoints"] = len(self.test_endpoints)
            results["successful_endpoints"] = sum(1 for r in endpoint_results.values() if r.get("success", False))
            results["success_rate"] = results["successful_endpoints"] / results["total_endpoints"]
            
            # Check specific endpoint categories
            auth_endpoints = [name for name, config in self.test_endpoints.items() if config["auth_required"]]
            auth_successes = sum(
                1 for name in auth_endpoints 
                if endpoint_results.get(name, {}).get("success", False)
            )
            
            results["auth_endpoint_success_rate"] = auth_successes / len(auth_endpoints) if auth_endpoints else 1.0
            
            # Check response times
            response_times = [
                r["response_time_ms"] for r in endpoint_results.values() 
                if "response_time_ms" in r and r.get("success", False)
            ]
            
            if response_times:
                results["avg_response_time_ms"] = sum(response_times) / len(response_times)
                results["max_response_time_ms"] = max(response_times)
                results["min_response_time_ms"] = min(response_times)
            
            # Assessment
            if results["success_rate"] >= 0.8 and results["auth_endpoint_success_rate"] >= 0.8:
                return {
                    "status": "PASS",
                    "message": f"API functionality excellent ({results['successful_endpoints']}/{results['total_endpoints']} endpoints working)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "endpoint_tests",
                            "data": endpoint_results,
                            "description": "API endpoint functionality test results"
                        }
                    ]
                }
            elif results["success_rate"] >= 0.5:
                failed_endpoints = [
                    name for name, result in endpoint_results.items()
                    if not result.get("success", False)
                ]
                return {
                    "status": "WARNING",
                    "message": f"API partially functional: {failed_endpoints} endpoints failing",
                    "details": results,
                    "recommendations": [
                        "Check failed endpoint implementations",
                        "Verify authentication token configuration",
                        "Review API routing and middleware",
                        "Check database connectivity from API"
                    ]
                }
            else:
                return {
                    "status": "FAIL",
                    "message": f"API functionality insufficient ({results['success_rate']:.1%} success rate)",
                    "details": results,
                    "recommendations": [
                        "Check API server startup and configuration", 
                        "Verify database and Redis connections",
                        "Review application logs for errors",
                        "Test authentication system independently"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"API functionality test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_performance(self) -> Dict[str, Any]:
        """Test API performance characteristics"""
        try:
            results = {}
            
            # Performance test configuration
            test_scenarios = [
                {"name": "health_check", "endpoint": "/health", "concurrent": 10, "requests": 50},
                {"name": "auth_endpoint", "endpoint": "/auth/me", "concurrent": 5, "requests": 25},
                {"name": "list_endpoint", "endpoint": "/candidates/", "concurrent": 3, "requests": 15}
            ]
            
            performance_results = {}
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                
                for scenario in test_scenarios:
                    scenario_name = scenario["name"]
                    endpoint = scenario["endpoint"]
                    concurrent = scenario["concurrent"]
                    total_requests = scenario["requests"]
                    
                    url = f"{self.base_url}{self.api_prefix}{endpoint}"
                    headers = {}
                    
                    # Add auth if needed (not for health endpoint)
                    if endpoint != "/health":
                        headers["Authorization"] = f"Bearer {self.dev_token}"
                    
                    # Execute concurrent requests
                    semaphore = asyncio.Semaphore(concurrent)
                    
                    async def make_request():
                        async with semaphore:
                            start_time = time.time()
                            try:
                                response = await client.get(url, headers=headers)
                                duration = (time.time() - start_time) * 1000
                                return {
                                    "success": response.status_code < 400,
                                    "status_code": response.status_code,
                                    "duration_ms": duration
                                }
                            except Exception as e:
                                duration = (time.time() - start_time) * 1000
                                return {
                                    "success": False,
                                    "error": str(e),
                                    "duration_ms": duration
                                }
                    
                    # Execute load test
                    start_time = time.time()
                    request_results = await asyncio.gather(*[
                        make_request() for _ in range(total_requests)
                    ])
                    total_time = (time.time() - start_time) * 1000
                    
                    # Analyze results
                    successful = [r for r in request_results if r["success"]]
                    failed = [r for r in request_results if not r["success"]]
                    
                    if successful:
                        response_times = [r["duration_ms"] for r in successful]
                        
                        performance_results[scenario_name] = {
                            "total_requests": total_requests,
                            "successful": len(successful),
                            "failed": len(failed),
                            "success_rate": len(successful) / total_requests,
                            "total_duration_ms": total_time,
                            "requests_per_second": total_requests / (total_time / 1000),
                            "avg_response_time_ms": sum(response_times) / len(response_times),
                            "min_response_time_ms": min(response_times),
                            "max_response_time_ms": max(response_times),
                            "p95_response_time_ms": sorted(response_times)[int(len(response_times) * 0.95)],
                            "concurrent_users": concurrent
                        }
                    else:
                        performance_results[scenario_name] = {
                            "total_requests": total_requests,
                            "successful": 0,
                            "failed": len(failed),
                            "success_rate": 0,
                            "error_summary": [r.get("error", "unknown") for r in failed[:3]]
                        }
            
            results["load_test_results"] = performance_results
            
            # Overall performance assessment
            successful_scenarios = [
                name for name, perf in performance_results.items()
                if perf.get("success_rate", 0) >= 0.95
            ]
            
            if successful_scenarios:
                avg_response_times = [
                    perf["avg_response_time_ms"] for perf in performance_results.values()
                    if "avg_response_time_ms" in perf
                ]
                
                overall_avg_response = sum(avg_response_times) / len(avg_response_times)
                results["overall_avg_response_ms"] = overall_avg_response
                
                fastest_scenario = min(
                    successful_scenarios,
                    key=lambda s: performance_results[s]["avg_response_time_ms"]
                )
                results["fastest_scenario"] = fastest_scenario
            
            # Performance assessment
            thresholds = self.config.get("performance_thresholds", {})
            response_threshold = thresholds.get("api_response_ms", 200)
            success_rate_threshold = 0.95
            
            performance_issues = []
            
            for scenario_name, perf in performance_results.items():
                success_rate = perf.get("success_rate", 0)
                avg_response = perf.get("avg_response_time_ms", 0)
                
                if success_rate < success_rate_threshold:
                    performance_issues.append(f"{scenario_name}: {success_rate:.1%} success rate (< {success_rate_threshold:.1%})")
                
                if avg_response > response_threshold:
                    performance_issues.append(f"{scenario_name}: {avg_response:.0f}ms response time (> {response_threshold}ms)")
            
            if not successful_scenarios:
                return {
                    "status": "FAIL",
                    "message": "No API scenarios passing performance tests",
                    "details": results,
                    "recommendations": [
                        "Check API server performance and load",
                        "Review database query optimization",
                        "Check system resources (CPU, memory)",
                        "Verify network connectivity quality"
                    ]
                }
            elif performance_issues:
                return {
                    "status": "WARNING",
                    "message": f"API performance issues: {len(performance_issues)} issues",
                    "details": results,
                    "recommendations": [
                        "Optimize slow endpoints",
                        "Review database indexing",
                        "Consider API response caching",
                        "Monitor server resources during peak load"
                    ] + performance_issues
                }
            else:
                return {
                    "status": "PASS",
                    "message": f"API performance excellent ({overall_avg_response:.0f}ms avg response)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "load_test_metrics",
                            "data": performance_results,
                            "description": "API load testing performance results"
                        }
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"API performance test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_authentication(self) -> Dict[str, Any]:
        """Test API authentication system"""
        try:
            results = {}
            
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                
                # Test 1: Access without token (should fail)
                auth_endpoint = f"{self.base_url}{self.api_prefix}/auth/me"
                
                start_time = time.time()
                unauth_response = await client.get(auth_endpoint)
                unauth_time = (time.time() - start_time) * 1000
                
                results["unauthorized_access"] = {
                    "status_code": unauth_response.status_code,
                    "response_time_ms": unauth_time,
                    "properly_rejected": unauth_response.status_code == 401
                }
                
                # Test 2: Access with development token
                headers = {"Authorization": f"Bearer {self.dev_token}"}
                
                start_time = time.time()
                auth_response = await client.get(auth_endpoint, headers=headers)
                auth_time = (time.time() - start_time) * 1000
                
                results["dev_token_access"] = {
                    "status_code": auth_response.status_code,
                    "response_time_ms": auth_time,
                    "access_granted": auth_response.status_code == 200
                }
                
                if auth_response.status_code == 200:
                    user_data = auth_response.json()
                    results["authenticated_user"] = {
                        "user_id": user_data.get("id"),
                        "email": user_data.get("email"),
                        "role": user_data.get("role"),
                        "permissions": len(user_data.get("permissions", []))
                    }
                
                # Test 3: Token validation consistency
                token_validation_times = []
                for i in range(5):
                    start_time = time.time()
                    test_response = await client.get(auth_endpoint, headers=headers)
                    validation_time = (time.time() - start_time) * 1000
                    token_validation_times.append(validation_time)
                    
                    if test_response.status_code != 200:
                        break
                
                results["token_validation"] = {
                    "consistent_responses": len(token_validation_times),
                    "avg_validation_time_ms": sum(token_validation_times) / len(token_validation_times),
                    "max_validation_time_ms": max(token_validation_times) if token_validation_times else 0
                }
                
                # Test 4: Invalid token handling
                invalid_headers = {"Authorization": "Bearer invalid_token_test"}
                
                start_time = time.time()
                invalid_response = await client.get(auth_endpoint, headers=invalid_headers)
                invalid_time = (time.time() - start_time) * 1000
                
                results["invalid_token_handling"] = {
                    "status_code": invalid_response.status_code,
                    "response_time_ms": invalid_time,
                    "properly_rejected": invalid_response.status_code == 401
                }
            
            # Authentication assessment
            auth_checks = [
                results["unauthorized_access"]["properly_rejected"],
                results["dev_token_access"]["access_granted"],
                results["invalid_token_handling"]["properly_rejected"],
                results["token_validation"]["consistent_responses"] >= 3
            ]
            
            if all(auth_checks):
                return {
                    "status": "PASS",
                    "message": f"Authentication system working (validation: {results['token_validation']['avg_validation_time_ms']:.1f}ms avg)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "authentication_tests",
                            "data": {
                                "user_info": results.get("authenticated_user"),
                                "validation_performance": results["token_validation"]
                            },
                            "description": "Authentication system test results"
                        }
                    ]
                }
            else:
                failed_checks = []
                if not results["unauthorized_access"]["properly_rejected"]:
                    failed_checks.append("unauthorized access not properly rejected")
                if not results["dev_token_access"]["access_granted"]:
                    failed_checks.append("dev token access failed")
                if not results["invalid_token_handling"]["properly_rejected"]:
                    failed_checks.append("invalid token not properly rejected")
                    
                return {
                    "status": "FAIL",
                    "message": f"Authentication issues: {', '.join(failed_checks)}",
                    "details": results,
                    "recommendations": [
                        "Check JWT token validation middleware",
                        "Verify dev token configuration",
                        "Review authentication error handling",
                        "Test token expiration handling"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"Authentication test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def verify(self) -> VerificationSummary:
        """Execute all API verification tests"""
        self.start_time = datetime.now(timezone.utc)
        
        # Test 1: Basic connectivity
        await self.run_test(
            "api_connectivity",
            self.check_connectivity,
            VerificationSeverity.CRITICAL
        )
        
        # Test 2: Authentication system
        await self.run_test(
            "api_authentication",
            self.check_authentication,
            VerificationSeverity.CRITICAL
        )
        
        # Test 3: Endpoint functionality
        await self.run_test(
            "api_functionality",
            self.check_functionality,
            VerificationSeverity.HIGH
        )
        
        # Test 4: Performance characteristics
        await self.run_test(
            "api_performance",
            self.check_performance,
            VerificationSeverity.MEDIUM
        )
        
        return self.get_summary()