"""
OCR Service verification for PaddleOCR with GPU/CPU fallback
"""
import asyncio
import io
import os
import time
from typing import Dict, Any, List
from datetime import datetime, timezone
from PIL import Image, ImageDraw, ImageFont

from ..base_verifier import ComponentVerifier, VerificationStatus, VerificationSeverity, VerificationSummary


class OCRServiceVerifier(ComponentVerifier):
    """Verify OCR service functionality with PaddleOCR"""
    
    def __init__(self, connection_config: Dict[str, Any]):
        super().__init__("OCR Service (PaddleOCR)", connection_config)
        self.ocr_instance = None
        
        # OCR configuration
        self.use_gpu = connection_config.get("use_gpu", True)
        self.supported_languages = connection_config.get("supported_languages", ["en"])
        self.confidence_threshold = connection_config.get("confidence_threshold", 0.3)
        self.cpu_thread_num = connection_config.get("cpu_thread_num", 2)
    
    async def check_connectivity(self) -> Dict[str, Any]:
        """Test OCR service availability and installation"""
        try:
            results = {}
            
            # Test 1: PaddleOCR import
            start_time = time.time()
            try:
                import paddleocr
                import_time = (time.time() - start_time) * 1000
                results["paddleocr_available"] = True
                results["import_time_ms"] = import_time
                results["paddleocr_version"] = paddleocr.__version__
            except ImportError as e:
                return {
                    "status": "FAIL",
                    "message": f"PaddleOCR not available: {str(e)}",
                    "details": {"error": str(e)},
                    "recommendations": [
                        "Install PaddleOCR: pip install paddleocr",
                        "Check Python environment and dependencies",
                        "Verify system requirements for PaddleOCR"
                    ]
                }
            
            # Test 2: GPU availability check
            try:
                import paddle
                gpu_available = paddle.device.is_compiled_with_cuda()
                gpu_count = paddle.device.cuda.device_count() if gpu_available else 0
                
                results["gpu_support"] = {
                    "cuda_available": gpu_available,
                    "gpu_count": gpu_count,
                    "paddle_version": paddle.__version__
                }
                
                if gpu_available:
                    results["recommended_mode"] = "gpu"
                else:
                    results["recommended_mode"] = "cpu"
                    
            except Exception as e:
                results["gpu_support"] = {
                    "error": str(e),
                    "cuda_available": False,
                    "recommended_mode": "cpu"
                }
            
            # Test 3: OCR initialization
            try:
                start_time = time.time()
                
                # Initialize with CPU first (more reliable for testing)
                self.ocr_instance = paddleocr.PaddleOCR(
                    use_angle_cls=True,
                    lang='en',
                    use_gpu=False,  # Start with CPU for stability
                    show_log=False
                )
                
                init_time = (time.time() - start_time) * 1000
                results["ocr_initialization"] = {
                    "successful": True,
                    "init_time_ms": init_time,
                    "mode": "cpu"
                }
                
                # Test GPU initialization if available
                if self.use_gpu and results["gpu_support"]["cuda_available"]:
                    try:
                        start_time = time.time()
                        gpu_ocr = paddleocr.PaddleOCR(
                            use_angle_cls=True,
                            lang='en',
                            use_gpu=True,
                            show_log=False
                        )
                        gpu_init_time = (time.time() - start_time) * 1000
                        
                        results["gpu_ocr_initialization"] = {
                            "successful": True,
                            "init_time_ms": gpu_init_time,
                            "mode": "gpu"
                        }
                        
                        # Use GPU instance if successful
                        self.ocr_instance = gpu_ocr
                        
                    except Exception as e:
                        results["gpu_ocr_initialization"] = {
                            "successful": False,
                            "error": str(e),
                            "fallback_to_cpu": True
                        }
                
            except Exception as e:
                return {
                    "status": "FAIL",
                    "message": f"OCR initialization failed: {str(e)}",
                    "details": {"error": str(e), "error_type": type(e).__name__},
                    "recommendations": [
                        "Check PaddleOCR installation",
                        "Verify system dependencies",
                        "Try CPU mode if GPU initialization fails",
                        "Check available disk space for model downloads"
                    ]
                }
            
            # Success assessment
            return {
                "status": "PASS",
                "message": f"OCR service available ({results['ocr_initialization']['mode']} mode)",
                "details": results,
                "evidence": [
                    {
                        "type": "ocr_initialization",
                        "data": results,
                        "description": "OCR service initialization and GPU detection results"
                    }
                ]
            }
            
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"OCR connectivity test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_functionality(self) -> Dict[str, Any]:
        """Test OCR core functionality"""
        if not self.ocr_instance:
            return {
                "status": "SKIP",
                "message": "Skipping functionality test - OCR not initialized",
                "details": {}
            }
        
        try:
            results = {}
            
            # Test 1: Simple text recognition
            simple_text = "HELLO WORLD"
            simple_image = self._create_text_image(simple_text, font_size=48)
            
            start_time = time.time()
            ocr_result = self.ocr_instance.ocr(simple_image, cls=True)
            processing_time = (time.time() - start_time) * 1000
            
            # Parse OCR result
            extracted_text = ""
            confidence_scores = []
            
            if ocr_result and ocr_result[0]:
                for line in ocr_result[0]:
                    text = line[1][0]
                    confidence = line[1][1]
                    extracted_text += text + " "
                    confidence_scores.append(confidence)
            
            extracted_text = extracted_text.strip()
            avg_confidence = sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0
            
            results["simple_text_test"] = {
                "expected_text": simple_text,
                "extracted_text": extracted_text,
                "text_match": simple_text.upper() in extracted_text.upper(),
                "confidence_score": avg_confidence,
                "processing_time_ms": processing_time,
                "confidence_acceptable": avg_confidence >= self.confidence_threshold
            }
            
            # Test 2: Resume-like text recognition
            resume_text = """John Doe
Software Engineer
Email: <EMAIL>
Phone: ******-0123
Skills: Python, FastAPI, PostgreSQL"""
            
            resume_image = self._create_text_image(resume_text, font_size=24, width=500, height=300)
            
            start_time = time.time()
            resume_ocr_result = self.ocr_instance.ocr(resume_image, cls=True)
            resume_processing_time = (time.time() - start_time) * 1000
            
            # Parse resume OCR result
            resume_extracted = ""
            resume_confidences = []
            
            if resume_ocr_result and resume_ocr_result[0]:
                for line in resume_ocr_result[0]:
                    text = line[1][0]
                    confidence = line[1][1]
                    resume_extracted += text + "\\n"
                    resume_confidences.append(confidence)
            
            resume_avg_confidence = sum(resume_confidences) / len(resume_confidences) if resume_confidences else 0
            
            # Check for key elements
            key_elements = ["John Doe", "Software Engineer", "Python", "FastAPI"]
            elements_found = sum(1 for element in key_elements if element.lower() in resume_extracted.lower())
            
            results["resume_text_test"] = {
                "expected_elements": key_elements,
                "extracted_text": resume_extracted.strip(),
                "elements_found": elements_found,
                "element_accuracy": elements_found / len(key_elements),
                "avg_confidence": resume_avg_confidence,
                "processing_time_ms": resume_processing_time,
                "quality_acceptable": elements_found >= len(key_elements) * 0.7  # 70% accuracy
            }
            
            # Test 3: Language support test
            if "zh" in self.supported_languages:
                chinese_text = "张伟\\n软件工程师"
                chinese_image = self._create_text_image(chinese_text, font_size=36, font_type="chinese")
                
                try:
                    # Initialize Chinese OCR if not already done
                    chinese_ocr = paddleocr.PaddleOCR(use_angle_cls=True, lang='ch', use_gpu=False, show_log=False)
                    
                    start_time = time.time()
                    chinese_result = chinese_ocr.ocr(chinese_image, cls=True)
                    chinese_processing_time = (time.time() - start_time) * 1000
                    
                    chinese_extracted = ""
                    if chinese_result and chinese_result[0]:
                        for line in chinese_result[0]:
                            chinese_extracted += line[1][0] + " "
                    
                    results["chinese_text_test"] = {
                        "expected_text": chinese_text,
                        "extracted_text": chinese_extracted.strip(),
                        "processing_time_ms": chinese_processing_time,
                        "chinese_support_works": "张伟" in chinese_extracted
                    }
                    
                except Exception as e:
                    results["chinese_text_test"] = {
                        "error": str(e),
                        "chinese_support_works": False
                    }
            
            # Assessment
            simple_success = results["simple_text_test"]["text_match"]
            resume_success = results["resume_text_test"]["quality_acceptable"]
            
            if simple_success and resume_success:
                return {
                    "status": "PASS",
                    "message": f"OCR functionality excellent ({results['resume_text_test']['element_accuracy']:.1%} resume accuracy)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "ocr_samples",
                            "data": {
                                "simple_test": results["simple_text_test"],
                                "resume_test": results["resume_text_test"]
                            },
                            "description": "OCR text extraction test results"
                        }
                    ]
                }
            elif simple_success:
                return {
                    "status": "WARNING", 
                    "message": f"OCR works for simple text but resume accuracy low ({results['resume_text_test']['element_accuracy']:.1%})",
                    "details": results,
                    "recommendations": [
                        "Optimize OCR parameters for resume documents",
                        "Consider image preprocessing improvements",
                        "Test with higher resolution images",
                        "Review confidence threshold settings"
                    ]
                }
            else:
                return {
                    "status": "FAIL",
                    "message": "OCR functionality insufficient - basic text recognition failing",
                    "details": results,
                    "recommendations": [
                        "Check PaddleOCR model downloads",
                        "Verify image processing pipeline",
                        "Test with different image formats",
                        "Consider alternative OCR solutions"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"OCR functionality test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    def _create_text_image(self, text: str, font_size: int = 24, width: int = 400, 
                          height: int = 200, font_type: str = "latin") -> bytes:
        """Create a simple test image with text"""
        try:
            # Create image
            image = Image.new('RGB', (width, height), color='white')
            draw = ImageDraw.Draw(image)
            
            # Try to use a system font
            try:
                if font_type == "chinese":
                    # Try to find a Chinese font
                    font_paths = [
                        "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
                        "/System/Library/Fonts/Arial.ttf", 
                        "/Windows/Fonts/arial.ttf"
                    ]
                    font = None
                    for path in font_paths:
                        if os.path.exists(path):
                            font = ImageFont.truetype(path, font_size)
                            break
                    
                    if not font:
                        font = ImageFont.load_default()
                else:
                    # Use default font for Latin text
                    font = ImageFont.load_default()
                    
            except Exception:
                font = ImageFont.load_default()
            
            # Draw text
            lines = text.split('\\n')
            y_offset = 20
            
            for line in lines:
                draw.text((20, y_offset), line, fill='black', font=font)
                y_offset += font_size + 10
            
            # Convert to bytes
            img_byte_arr = io.BytesIO()
            image.save(img_byte_arr, format='PNG')
            img_byte_arr = img_byte_arr.getvalue()
            
            return img_byte_arr
            
        except Exception as e:
            # Fallback: create minimal image
            simple_image = Image.new('RGB', (width, height), color='white')
            draw = ImageDraw.Draw(simple_image)
            draw.text((20, 50), text, fill='black')
            
            img_byte_arr = io.BytesIO()
            simple_image.save(img_byte_arr, format='PNG')
            return img_byte_arr.getvalue()
    
    async def check_performance(self) -> Dict[str, Any]:
        """Test OCR performance characteristics"""
        if not self.ocr_instance:
            return {
                "status": "SKIP",
                "message": "Skipping performance test - OCR not initialized",
                "details": {}
            }
        
        try:
            results = {}
            
            # Test with different image sizes and complexities
            test_cases = [
                {"name": "small_simple", "size": (200, 100), "text": "Simple Text", "font_size": 20},
                {"name": "medium_resume", "size": (400, 300), "text": "John Doe\\nSoftware Engineer\\nPython Developer", "font_size": 16},
                {"name": "large_document", "size": (800, 600), "text": "Large Document\\nMultiple Lines\\nVarious Skills\\nComplex Layout", "font_size": 14}
            ]
            
            performance_results = {}
            
            for test_case in test_cases:
                test_name = test_case["name"]
                
                # Create test image
                test_image = self._create_text_image(
                    test_case["text"],
                    font_size=test_case["font_size"],
                    width=test_case["size"][0],
                    height=test_case["size"][1]
                )
                
                # Run OCR multiple times for average
                processing_times = []
                accuracy_scores = []
                
                for i in range(3):  # 3 runs per test case
                    start_time = time.time()
                    ocr_result = self.ocr_instance.ocr(test_image, cls=True)
                    processing_time = (time.time() - start_time) * 1000
                    processing_times.append(processing_time)
                    
                    # Calculate accuracy
                    if ocr_result and ocr_result[0]:
                        extracted = ""
                        confidences = []
                        for line in ocr_result[0]:
                            extracted += line[1][0] + " "
                            confidences.append(line[1][1])
                        
                        expected_words = test_case["text"].replace("\\n", " ").split()
                        extracted_words = extracted.split()
                        
                        # Simple word-based accuracy
                        matches = sum(1 for word in expected_words 
                                    if any(word.lower() in ext_word.lower() for ext_word in extracted_words))
                        accuracy = matches / len(expected_words) if expected_words else 0
                        accuracy_scores.append(accuracy)
                    else:
                        accuracy_scores.append(0)
                
                performance_results[test_name] = {
                    "avg_processing_time_ms": sum(processing_times) / len(processing_times),
                    "min_processing_time_ms": min(processing_times),
                    "max_processing_time_ms": max(processing_times),
                    "avg_accuracy": sum(accuracy_scores) / len(accuracy_scores),
                    "consistency": 1 - (max(processing_times) - min(processing_times)) / max(processing_times) if max(processing_times) > 0 else 1,
                    "image_size": test_case["size"]
                }
            
            results["performance_by_complexity"] = performance_results
            
            # Overall performance metrics
            all_times = [perf["avg_processing_time_ms"] for perf in performance_results.values()]
            all_accuracies = [perf["avg_accuracy"] for perf in performance_results.values()]
            
            results["overall_performance"] = {
                "avg_processing_time_ms": sum(all_times) / len(all_times),
                "avg_accuracy": sum(all_accuracies) / len(all_accuracies),
                "fastest_case": min(performance_results.keys(), key=lambda k: performance_results[k]["avg_processing_time_ms"]),
                "most_accurate_case": max(performance_results.keys(), key=lambda k: performance_results[k]["avg_accuracy"])
            }
            
            # Performance assessment
            thresholds = self.config.get("performance_thresholds", {})
            processing_threshold = thresholds.get("ocr_processing_ms", 10000)
            accuracy_threshold = thresholds.get("accuracy_threshold", 0.8)
            
            performance_issues = []
            
            # Check processing time thresholds
            for case_name, perf in performance_results.items():
                if perf["avg_processing_time_ms"] > processing_threshold:
                    performance_issues.append(f"{case_name}: {perf['avg_processing_time_ms']:.0f}ms > {processing_threshold}ms")
            
            # Check accuracy thresholds
            if results["overall_performance"]["avg_accuracy"] < accuracy_threshold:
                performance_issues.append(f"Overall accuracy {results['overall_performance']['avg_accuracy']:.1%} < {accuracy_threshold:.1%}")
            
            if performance_issues:
                return {
                    "status": "WARNING",
                    "message": f"OCR performance issues: {len(performance_issues)} issues",
                    "details": results,
                    "recommendations": [
                        "Consider GPU acceleration if available",
                        "Optimize image preprocessing",
                        "Adjust confidence threshold",
                        "Test with higher quality source images"
                    ] + performance_issues
                }
            else:
                return {
                    "status": "PASS",
                    "message": f"OCR performance excellent ({results['overall_performance']['avg_accuracy']:.1%} accuracy, {results['overall_performance']['avg_processing_time_ms']:.0f}ms avg)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "performance_benchmarks",
                            "data": performance_results,
                            "description": "OCR performance test results across different complexities"
                        }
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"OCR functionality test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_gpu_cpu_fallback(self) -> Dict[str, Any]:
        """Test GPU/CPU fallback mechanism"""
        try:
            results = {}
            
            # Test text for fallback testing
            test_text = "GPU CPU FALLBACK TEST"
            test_image = self._create_text_image(test_text, font_size=32)
            
            # Test CPU mode
            try:
                import paddleocr
                cpu_ocr = paddleocr.PaddleOCR(
                    use_angle_cls=True,
                    lang='en', 
                    use_gpu=False,
                    show_log=False
                )
                
                start_time = time.time()
                cpu_result = cpu_ocr.ocr(test_image, cls=True)
                cpu_time = (time.time() - start_time) * 1000
                
                cpu_text = ""
                if cpu_result and cpu_result[0]:
                    for line in cpu_result[0]:
                        cpu_text += line[1][0] + " "
                
                results["cpu_mode"] = {
                    "available": True,
                    "processing_time_ms": cpu_time,
                    "extracted_text": cpu_text.strip(),
                    "accuracy": test_text.upper() in cpu_text.upper()
                }
                
            except Exception as e:
                results["cpu_mode"] = {
                    "available": False,
                    "error": str(e)
                }
            
            # Test GPU mode if available  
            gpu_available = False
            try:
                import paddle
                gpu_available = paddle.device.is_compiled_with_cuda() and paddle.device.cuda.device_count() > 0
            except Exception:
                gpu_available = False
            
            if gpu_available:
                try:
                    gpu_ocr = paddleocr.PaddleOCR(
                        use_angle_cls=True,
                        lang='en',
                        use_gpu=True,
                        show_log=False
                    )
                    
                    start_time = time.time()
                    gpu_result = gpu_ocr.ocr(test_image, cls=True)
                    gpu_time = (time.time() - start_time) * 1000
                    
                    gpu_text = ""
                    if gpu_result and gpu_result[0]:
                        for line in gpu_result[0]:
                            gpu_text += line[1][0] + " "
                    
                    results["gpu_mode"] = {
                        "available": True,
                        "processing_time_ms": gpu_time,
                        "extracted_text": gpu_text.strip(),
                        "accuracy": test_text.upper() in gpu_text.upper(),
                        "speed_improvement": (cpu_time - gpu_time) / cpu_time if results["cpu_mode"]["available"] else 0
                    }
                    
                except Exception as e:
                    results["gpu_mode"] = {
                        "available": False,
                        "error": str(e),
                        "fallback_required": True
                    }
            else:
                results["gpu_mode"] = {
                    "available": False,
                    "reason": "no_gpu_detected"
                }
            
            # Fallback mechanism assessment
            cpu_works = results["cpu_mode"]["available"] and results["cpu_mode"].get("accuracy", False)
            gpu_works = results["gpu_mode"]["available"] and results["gpu_mode"].get("accuracy", False)
            
            results["fallback_assessment"] = {
                "cpu_functional": cpu_works,
                "gpu_functional": gpu_works,
                "fallback_viable": cpu_works,  # CPU is the fallback
                "primary_mode": "gpu" if gpu_works else "cpu",
                "fallback_mode": "cpu"
            }
            
            if cpu_works:
                mode_info = f"CPU mode functional"
                if gpu_works:
                    speed_improvement = results["gpu_mode"].get("speed_improvement", 0)
                    mode_info += f", GPU {speed_improvement:.1%} faster" if speed_improvement > 0 else ", GPU available"
                
                return {
                    "status": "PASS",
                    "message": f"OCR fallback system working: {mode_info}",
                    "details": results,
                    "evidence": [
                        {
                            "type": "fallback_testing",
                            "data": results["fallback_assessment"],
                            "description": "GPU/CPU fallback mechanism test results"
                        }
                    ]
                }
            else:
                return {
                    "status": "FAIL",
                    "message": "OCR fallback system not working - CPU mode failed",
                    "details": results,
                    "recommendations": [
                        "Fix CPU mode OCR configuration",
                        "Check system dependencies",
                        "Verify PaddleOCR CPU installation",
                        "Test with simpler images"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"OCR fallback test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def verify(self) -> VerificationSummary:
        """Execute all OCR verification tests"""
        self.start_time = datetime.now(timezone.utc)
        
        # Test 1: OCR service availability
        await self.run_test(
            "ocr_connectivity",
            self.check_connectivity,
            VerificationSeverity.CRITICAL
        )
        
        # Test 2: OCR functionality
        await self.run_test(
            "ocr_functionality",
            self.check_functionality,
            VerificationSeverity.HIGH
        )
        
        # Test 3: GPU/CPU fallback
        await self.run_test(
            "ocr_fallback",
            self.check_gpu_cpu_fallback,
            VerificationSeverity.MEDIUM
        )
        
        return self.get_summary()