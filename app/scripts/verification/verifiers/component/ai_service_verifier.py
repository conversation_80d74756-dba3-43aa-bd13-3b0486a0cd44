"""
AI Service verification for multi-provider setup (<PERSON>Seek, <PERSON>shot, OpenRouter, <PERSON><PERSON>, <PERSON>llama)
"""
import asyncio
import json
import time
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

import httpx

from ..base_verifier import ComponentVerifier, VerificationStatus, VerificationSeverity, VerificationSummary


class AIServiceVerifier(ComponentVerifier):
    """Verify AI service providers and fallback chains"""
    
    def __init__(self, connection_config: Dict[str, Any]):
        super().__init__("AI Services", connection_config)
        
        # AI Provider configurations
        self.providers = {
            "deepseek": {
                "api_base": connection_config.get("deepseek_api_base", "https://api.deepseek.com/v1"),
                "api_key": connection_config.get("deepseek_api_key"),
                "model": connection_config.get("deepseek_model", "deepseek-chat"),
                "rate_limit": connection_config.get("deepseek_rate_limit", 10)
            },
            "moonshot": {
                "api_base": connection_config.get("moonshot_api_base", "https://api.moonshot.cn/v1"),
                "api_key": connection_config.get("moonshot_api_key"),
                "model": connection_config.get("moonshot_model", "moonshot-v1-8k"),
                "rate_limit": connection_config.get("moonshot_rate_limit", 10)
            },
            "openrouter": {
                "api_base": connection_config.get("openrouter_api_base", "https://openrouter.ai/api/v1"),
                "api_key": connection_config.get("openrouter_api_key"),
                "model": connection_config.get("openrouter_model", "anthropic/claude-3-sonnet"),
                "rate_limit": connection_config.get("openrouter_rate_limit", 30)
            },
            "qwen": {
                "api_base": connection_config.get("qwen_api_base", "https://dashscope.aliyuncs.com/compatible-mode/v1"),
                "api_key": connection_config.get("qwen_api_key"),
                "model": connection_config.get("qwen_model", "qwen-turbo"),
                "rate_limit": connection_config.get("qwen_rate_limit", 60)
            },
            "ollama": {
                "api_base": connection_config.get("ollama_base_url", "http://localhost:11434"),
                "api_key": None,  # Ollama doesn't use API keys
                "model": connection_config.get("ollama_model", "bge-m3"),
                "rate_limit": 100  # Local model, higher rate limit
            }
        }
        
        self.primary_provider = connection_config.get("primary_provider", "deepseek")
        self.fallback_chain = connection_config.get("fallback_chain", ["deepseek", "moonshot", "openrouter", "qwen"])
    
    async def check_connectivity(self) -> Dict[str, Any]:
        """Test connectivity to all AI providers"""
        try:
            results = {}
            provider_status = {}
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                
                for provider_name, config in self.providers.items():
                    try:
                        start_time = time.time()
                        
                        if provider_name == "ollama":
                            # Ollama uses different API endpoint
                            response = await client.get(f"{config['api_base']}/api/tags")
                        else:
                            # OpenAI-compatible providers
                            headers = {}
                            if config["api_key"]:
                                headers["Authorization"] = f"Bearer {config['api_key']}"
                            
                            response = await client.get(
                                f"{config['api_base']}/models",
                                headers=headers
                            )
                        
                        response_time = (time.time() - start_time) * 1000
                        
                        if response.status_code == 200:
                            provider_status[provider_name] = {
                                "status": "available",
                                "response_time_ms": response_time,
                                "status_code": response.status_code
                            }
                            
                            # Try to parse response to get model info
                            try:
                                data = response.json()
                                if provider_name == "ollama":
                                    models = [model["name"] for model in data.get("models", [])]
                                else:
                                    models = [model["id"] for model in data.get("data", [])]
                                provider_status[provider_name]["available_models"] = models[:5]  # First 5 models
                            except Exception:
                                provider_status[provider_name]["model_list"] = "unavailable"
                                
                        elif response.status_code == 401:
                            provider_status[provider_name] = {
                                "status": "auth_failed",
                                "response_time_ms": response_time,
                                "status_code": 401
                            }
                        else:
                            provider_status[provider_name] = {
                                "status": "error",
                                "response_time_ms": response_time,
                                "status_code": response.status_code,
                                "error": response.text[:200]
                            }
                            
                    except httpx.TimeoutException:
                        provider_status[provider_name] = {
                            "status": "timeout",
                            "error": "Connection timeout"
                        }
                    except Exception as e:
                        provider_status[provider_name] = {
                            "status": "connection_failed", 
                            "error": str(e),
                            "error_type": type(e).__name__
                        }
            
            results["provider_status"] = provider_status
            results["total_providers"] = len(self.providers)
            results["available_providers"] = sum(1 for status in provider_status.values() if status.get("status") == "available")
            results["auth_failed_providers"] = sum(1 for status in provider_status.values() if status.get("status") == "auth_failed")
            
            # Check primary provider status
            primary_status = provider_status.get(self.primary_provider, {})
            results["primary_provider"] = self.primary_provider
            results["primary_provider_available"] = primary_status.get("status") == "available"
            
            # Check fallback availability
            available_fallbacks = [
                provider for provider in self.fallback_chain 
                if provider_status.get(provider, {}).get("status") == "available"
            ]
            results["available_fallbacks"] = available_fallbacks
            results["fallback_coverage"] = len(available_fallbacks) / len(self.fallback_chain) if self.fallback_chain else 0
            
            # Assessment
            if results["available_providers"] == 0:
                return {
                    "status": "FAIL",
                    "message": "No AI providers available",
                    "details": results,
                    "recommendations": [
                        "Check internet connectivity",
                        "Verify API keys for external providers",
                        "Check Ollama local service status",
                        "Review firewall and network settings"
                    ]
                }
            elif not results["primary_provider_available"]:
                return {
                    "status": "WARNING",
                    "message": f"Primary provider ({self.primary_provider}) unavailable, {results['available_providers']} fallbacks available",
                    "details": results,
                    "recommendations": [
                        f"Check {self.primary_provider} service status and API key",
                        "Verify fallback configuration is working",
                        "Consider switching primary provider to available alternative"
                    ]
                }
            else:
                return {
                    "status": "PASS",
                    "message": f"AI services healthy: {results['available_providers']}/{results['total_providers']} providers available",
                    "details": results,
                    "evidence": [
                        {
                            "type": "provider_connectivity",
                            "data": provider_status,
                            "description": "AI provider connection test results"
                        }
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"AI connectivity test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_functionality(self) -> Dict[str, Any]:
        """Test AI service functionality with actual API calls"""
        try:
            results = {}
            
            # Test prompt for functionality verification
            test_prompt = """
Extract skills and experience from this resume text:

John Doe
Software Engineer
Skills: Python, FastAPI, PostgreSQL, Docker
Experience: 3 years software development

Return JSON format:
{
  "skills": ["skill1", "skill2"],
  "experience_years": number,
  "title": "job title"
}
            """.strip()
            
            provider_functionality = {}
            
            async with httpx.AsyncClient(timeout=60.0) as client:
                
                for provider_name, config in self.providers.items():
                    if not config.get("api_key") and provider_name != "ollama":
                        provider_functionality[provider_name] = {
                            "status": "skipped",
                            "reason": "no_api_key"
                        }
                        continue
                    
                    try:
                        start_time = time.time()
                        
                        if provider_name == "ollama":
                            # Ollama API format
                            payload = {
                                "model": config["model"],
                                "prompt": test_prompt,
                                "stream": False
                            }
                            
                            response = await client.post(
                                f"{config['api_base']}/api/generate",
                                json=payload
                            )
                        else:
                            # OpenAI-compatible API format
                            payload = {
                                "model": config["model"],
                                "messages": [
                                    {"role": "user", "content": test_prompt}
                                ],
                                "temperature": 0.3,
                                "max_tokens": 500
                            }
                            
                            headers = {"Authorization": f"Bearer {config['api_key']}"}
                            response = await client.post(
                                f"{config['api_base']}/chat/completions",
                                json=payload,
                                headers=headers
                            )
                        
                        response_time = (time.time() - start_time) * 1000
                        
                        if response.status_code == 200:
                            response_data = response.json()
                            
                            # Extract AI response text
                            if provider_name == "ollama":
                                ai_response = response_data.get("response", "")
                            else:
                                choices = response_data.get("choices", [])
                                ai_response = choices[0]["message"]["content"] if choices else ""
                            
                            # Try to validate JSON response
                            try:
                                parsed_response = json.loads(ai_response.strip())
                                json_valid = isinstance(parsed_response, dict) and "skills" in parsed_response
                            except Exception:
                                json_valid = False
                            
                            provider_functionality[provider_name] = {
                                "status": "functional",
                                "response_time_ms": response_time,
                                "response_length": len(ai_response),
                                "json_format_valid": json_valid,
                                "ai_response": ai_response[:200] + "..." if len(ai_response) > 200 else ai_response
                            }
                            
                        else:
                            provider_functionality[provider_name] = {
                                "status": "api_error",
                                "response_time_ms": response_time,
                                "status_code": response.status_code,
                                "error": response.text[:200]
                            }
                            
                    except httpx.TimeoutException:
                        provider_functionality[provider_name] = {
                            "status": "timeout",
                            "error": "API call timeout"
                        }
                    except Exception as e:
                        provider_functionality[provider_name] = {
                            "status": "error",
                            "error": str(e),
                            "error_type": type(e).__name__
                        }
            
            results["provider_functionality"] = provider_functionality
            results["functional_providers"] = [
                name for name, status in provider_functionality.items() 
                if status.get("status") == "functional"
            ]
            results["functional_count"] = len(results["functional_providers"])
            
            # Test fallback chain functionality
            fallback_results = []
            for provider in self.fallback_chain:
                provider_result = provider_functionality.get(provider, {})
                fallback_results.append({
                    "provider": provider,
                    "functional": provider_result.get("status") == "functional",
                    "response_time_ms": provider_result.get("response_time_ms", 0)
                })
            
            results["fallback_chain"] = fallback_results
            results["fallback_chain_viable"] = any(r["functional"] for r in fallback_results)
            
            # Assessment
            if results["functional_count"] == 0:
                return {
                    "status": "FAIL",
                    "message": "No AI providers are functional",
                    "details": results,
                    "recommendations": [
                        "Check API keys for all providers",
                        "Verify internet connectivity",
                        "Check Ollama local service",
                        "Review provider API status pages",
                        "Test with simplified prompts"
                    ]
                }
            elif results["primary_provider"] in results["functional_providers"]:
                primary_perf = provider_functionality[self.primary_provider]
                return {
                    "status": "PASS",
                    "message": f"AI services functional: primary {self.primary_provider} + {results['functional_count']-1} fallbacks",
                    "details": results,
                    "evidence": [
                        {
                            "type": "ai_responses",
                            "data": {
                                "primary_response": primary_perf.get("ai_response", ""),
                                "response_time": primary_perf.get("response_time_ms", 0),
                                "json_valid": primary_perf.get("json_format_valid", False)
                            },
                            "description": "Primary AI provider response test"
                        }
                    ]
                }
            else:
                available_providers = results["functional_providers"]
                return {
                    "status": "WARNING",
                    "message": f"Primary provider failed, {len(available_providers)} fallbacks available: {available_providers}",
                    "details": results,
                    "recommendations": [
                        f"Fix primary provider ({self.primary_provider}) configuration",
                        "Consider changing primary provider to working alternative",
                        "Monitor fallback provider performance",
                        "Set up alerts for provider failures"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"AI functionality test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_resume_parsing_capability(self) -> Dict[str, Any]:
        """Test AI resume parsing capability"""
        try:
            results = {}
            
            # Test resume samples
            test_resumes = [
                {
                    "name": "simple_resume",
                    "text": """
John Doe
Software Engineer

Contact: <EMAIL>, ******-0123
Location: San Francisco, CA

Experience:
• 3 years software development 
• Python programming with FastAPI
• Database management with PostgreSQL

Skills: Python, FastAPI, PostgreSQL, Docker, Git

Education:
Bachelor of Computer Science, University of Technology, 2020
                    """.strip()
                },
                {
                    "name": "complex_resume", 
                    "text": """
Jane Smith - Senior Full-Stack Developer & AI Engineer

Contact Information:
Email: <EMAIL> | Phone: ******-0156 | LinkedIn: linkedin.com/in/janesmith
Location: Seattle, WA

Professional Summary:
Experienced full-stack developer with 7+ years in web development and 3+ years in AI/ML.

Technical Skills:
• Programming Languages: Python (Expert), TypeScript/JavaScript (Advanced), Go (Intermediate)
• Web Frameworks: FastAPI, Django, React, Next.js, Vue.js
• Databases: PostgreSQL, MongoDB, Redis, Vector Databases (Pinecone, Weaviate)
• AI/ML: TensorFlow, PyTorch, Hugging Face, OpenAI API, LangChain

Experience:
Senior AI Engineer | TechCorp Inc. | 2022 - Present
• Developed ML models for recommendation systems
• Built vector search infrastructure using PostgreSQL with pgvector  
• Implemented LLM-powered chatbots with 95% accuracy

Full-Stack Developer | StartupXYZ | 2019 - 2022
• Built responsive web applications using React and FastAPI
• Designed RESTful APIs serving 10k+ daily active users

Education:
Master of Science in Computer Science | Stanford University | 2017
Bachelor of Science in Software Engineering | UC Berkeley | 2015
                    """.strip()
                }
            ]
            
            parsing_prompt = """
Analyze this resume and extract information in the following JSON format:

{
  "personal_info": {
    "name": "Full Name",
    "email": "<EMAIL>", 
    "phone": "phone number",
    "location": "city, state/country"
  },
  "skills": ["skill1", "skill2", "skill3"],
  "experience": [
    {
      "company": "Company Name",
      "position": "Job Title", 
      "duration": "start - end",
      "description": "brief description"
    }
  ],
  "education": [
    {
      "degree": "degree name",
      "institution": "school name",
      "year": "graduation year"
    }
  ],
  "summary": {
    "total_experience_years": number,
    "primary_skills": ["top 3 skills"],
    "seniority_level": "junior/mid/senior"
  }
}

Resume text:
{resume_text}

Return only valid JSON, no additional text.
            """
            
            parsing_results = {}
            
            # Test with functional providers only
            functional_providers = []
            
            # First, identify which providers are functional
            async with httpx.AsyncClient(timeout=60.0) as client:
                for provider_name, config in self.providers.items():
                    if provider_name == "ollama":
                        try:
                            response = await client.get(f"{config['api_base']}/api/tags", timeout=5)
                            if response.status_code == 200:
                                functional_providers.append(provider_name)
                        except Exception:
                            pass
                    elif config.get("api_key"):
                        functional_providers.append(provider_name)  # Assume functional if has API key
            
            # Test resume parsing with functional providers
            for provider_name in functional_providers[:2]:  # Test top 2 providers to save time
                config = self.providers[provider_name]
                provider_parsing = {}
                
                for resume in test_resumes:
                    resume_name = resume["name"]
                    resume_text = resume["text"]
                    prompt = parsing_prompt.format(resume_text=resume_text)
                    
                    try:
                        start_time = time.time()
                        
                        if provider_name == "ollama":
                            payload = {
                                "model": config["model"],
                                "prompt": prompt,
                                "stream": False
                            }
                            
                            response = await client.post(
                                f"{config['api_base']}/api/generate",
                                json=payload
                            )
                        else:
                            payload = {
                                "model": config["model"],
                                "messages": [{"role": "user", "content": prompt}],
                                "temperature": 0.3,
                                "max_tokens": 2000
                            }
                            
                            headers = {"Authorization": f"Bearer {config['api_key']}"}
                            response = await client.post(
                                f"{config['api_base']}/chat/completions",
                                json=payload,
                                headers=headers
                            )
                        
                        response_time = (time.time() - start_time) * 1000
                        
                        if response.status_code == 200:
                            response_data = response.json()
                            
                            # Extract response text
                            if provider_name == "ollama":
                                ai_response = response_data.get("response", "")
                            else:
                                choices = response_data.get("choices", [])
                                ai_response = choices[0]["message"]["content"] if choices else ""
                            
                            # Validate JSON parsing
                            try:
                                parsed_data = json.loads(ai_response.strip())
                                
                                # Validate required fields
                                required_fields = ["personal_info", "skills", "experience", "education", "summary"]
                                field_validation = {field: field in parsed_data for field in required_fields}
                                
                                # Check data quality
                                quality_checks = {
                                    "has_name": bool(parsed_data.get("personal_info", {}).get("name")),
                                    "has_skills": len(parsed_data.get("skills", [])) > 0,
                                    "has_experience": len(parsed_data.get("experience", [])) > 0,
                                    "experience_years_valid": isinstance(parsed_data.get("summary", {}).get("total_experience_years"), (int, float))
                                }
                                
                                provider_parsing[resume_name] = {
                                    "status": "success",
                                    "response_time_ms": response_time,
                                    "json_valid": True,
                                    "field_validation": field_validation,
                                    "quality_checks": quality_checks,
                                    "parsing_accuracy": sum(quality_checks.values()) / len(quality_checks),
                                    "extracted_data": parsed_data
                                }
                                
                            except json.JSONDecodeError as e:
                                provider_parsing[resume_name] = {
                                    "status": "json_error",
                                    "response_time_ms": response_time,
                                    "json_valid": False,
                                    "error": str(e),
                                    "raw_response": ai_response[:300]
                                }
                        else:
                            provider_parsing[resume_name] = {
                                "status": "api_error",
                                "status_code": response.status_code,
                                "error": response.text[:200]
                            }
                            
                    except Exception as e:
                        provider_parsing[resume_name] = {
                            "status": "error",
                            "error": str(e),
                            "error_type": type(e).__name__
                        }
                
                parsing_results[provider_name] = provider_parsing
            
            # Analyze parsing results
            results["parsing_results"] = parsing_results
            
            total_tests = sum(len(provider_results) for provider_results in parsing_results.values())
            successful_parses = sum(
                1 for provider_results in parsing_results.values()
                for result in provider_results.values()
                if result.get("status") == "success"
            )
            
            results["total_parsing_tests"] = total_tests
            results["successful_parses"] = successful_parses
            results["parsing_success_rate"] = successful_parses / total_tests if total_tests > 0 else 0
            
            # Calculate average accuracy for successful parses
            accuracy_scores = []
            for provider_results in parsing_results.values():
                for result in provider_results.values():
                    if result.get("status") == "success":
                        accuracy_scores.append(result.get("parsing_accuracy", 0))
            
            results["average_parsing_accuracy"] = sum(accuracy_scores) / len(accuracy_scores) if accuracy_scores else 0
            
            # Assessment
            if results["parsing_success_rate"] >= 0.8 and results["average_parsing_accuracy"] >= 0.7:
                return {
                    "status": "PASS", 
                    "message": f"AI parsing capability excellent ({results['parsing_success_rate']:.1%} success rate, {results['average_parsing_accuracy']:.1%} accuracy)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "parsing_capability",
                            "data": parsing_results,
                            "description": "AI resume parsing test results"
                        }
                    ]
                }
            elif results["parsing_success_rate"] >= 0.5:
                return {
                    "status": "WARNING",
                    "message": f"AI parsing capability needs improvement ({results['parsing_success_rate']:.1%} success rate)",
                    "details": results,
                    "recommendations": [
                        "Review and optimize parsing prompts",
                        "Test with different AI models",
                        "Implement response validation and retry logic",
                        "Consider prompt engineering improvements"
                    ]
                }
            else:
                return {
                    "status": "FAIL",
                    "message": f"AI parsing capability insufficient ({results['parsing_success_rate']:.1%} success rate)",
                    "details": results,
                    "recommendations": [
                        "Fix AI provider configurations", 
                        "Review prompt templates",
                        "Test with working providers manually",
                        "Consider alternative AI services"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"AI functionality test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_performance(self) -> Dict[str, Any]:
        """Test AI service performance and rate limits"""
        try:
            results = {}
            
            # Simple performance test prompt
            perf_prompt = "Extract the main programming language from this text: 'John is a Python developer.' Return only the language name."
            
            performance_results = {}
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                
                # Test each functional provider
                for provider_name, config in self.providers.items():
                    if not config.get("api_key") and provider_name != "ollama":
                        continue
                    
                    try:
                        # Test multiple requests to measure consistency
                        request_times = []
                        
                        for i in range(3):  # 3 requests per provider
                            start_time = time.time()
                            
                            if provider_name == "ollama":
                                payload = {
                                    "model": config["model"], 
                                    "prompt": perf_prompt,
                                    "stream": False
                                }
                                response = await client.post(
                                    f"{config['api_base']}/api/generate",
                                    json=payload
                                )
                            else:
                                payload = {
                                    "model": config["model"],
                                    "messages": [{"role": "user", "content": perf_prompt}],
                                    "temperature": 0.1,
                                    "max_tokens": 10
                                }
                                headers = {"Authorization": f"Bearer {config['api_key']}"}
                                response = await client.post(
                                    f"{config['api_base']}/chat/completions",
                                    json=payload,
                                    headers=headers
                                )
                            
                            request_time = (time.time() - start_time) * 1000
                            
                            if response.status_code == 200:
                                request_times.append(request_time)
                            else:
                                break  # Stop testing this provider on error
                            
                            # Small delay between requests to respect rate limits
                            await asyncio.sleep(0.5)
                        
                        if request_times:
                            performance_results[provider_name] = {
                                "avg_response_time_ms": sum(request_times) / len(request_times),
                                "min_response_time_ms": min(request_times),
                                "max_response_time_ms": max(request_times),
                                "consistency": (max(request_times) - min(request_times)) / max(request_times) if max(request_times) > 0 else 0,
                                "successful_requests": len(request_times),
                                "rate_limit_compliance": True  # No rate limit errors
                            }
                        else:
                            performance_results[provider_name] = {
                                "error": "No successful requests",
                                "successful_requests": 0
                            }
                            
                    except Exception as e:
                        performance_results[provider_name] = {
                            "error": str(e),
                            "error_type": type(e).__name__,
                            "successful_requests": 0
                        }
            
            results["performance_by_provider"] = performance_results
            
            # Calculate overall performance metrics
            successful_providers = [
                name for name, perf in performance_results.items()
                if perf.get("successful_requests", 0) > 0
            ]
            
            if successful_providers:
                avg_response_times = [
                    perf["avg_response_time_ms"] for perf in performance_results.values()
                    if "avg_response_time_ms" in perf
                ]
                
                results["overall_avg_response_ms"] = sum(avg_response_times) / len(avg_response_times)
                results["fastest_provider"] = min(
                    successful_providers, 
                    key=lambda p: performance_results[p].get("avg_response_time_ms", float('inf'))
                )
                results["slowest_provider"] = max(
                    successful_providers,
                    key=lambda p: performance_results[p].get("avg_response_time_ms", 0)
                )
            
            # Performance assessment  
            thresholds = self.config.get("performance_thresholds", {})
            response_threshold = thresholds.get("api_response_ms", 5000)
            
            performance_issues = []
            for provider, perf in performance_results.items():
                avg_time = perf.get("avg_response_time_ms", 0)
                if avg_time > response_threshold:
                    performance_issues.append(f"{provider}: {avg_time:.0f}ms > {response_threshold}ms")
            
            if len(successful_providers) == 0:
                return {
                    "status": "FAIL",
                    "message": "No AI providers responding to performance tests",
                    "details": results,
                    "recommendations": [
                        "Check AI provider service status",
                        "Verify API keys and quotas", 
                        "Test with smaller requests",
                        "Check rate limiting settings"
                    ]
                }
            elif performance_issues:
                return {
                    "status": "WARNING",
                    "message": f"AI performance issues: {len(performance_issues)} slow providers",
                    "details": results,
                    "recommendations": [
                        "Monitor AI provider performance over time",
                        "Consider adjusting timeout settings",
                        "Implement request optimization",
                        "Use faster providers for time-critical operations"
                    ] + performance_issues
                }
            else:
                fastest = results["fastest_provider"]
                fastest_time = performance_results[fastest]["avg_response_time_ms"]
                return {
                    "status": "PASS",
                    "message": f"AI performance excellent (fastest: {fastest} @ {fastest_time:.0f}ms)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "performance_benchmarks",
                            "data": performance_results,
                            "description": "AI provider performance test results"
                        }
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"AI performance test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def verify(self) -> VerificationSummary:
        """Execute all AI service verification tests"""
        self.start_time = datetime.now(timezone.utc)
        
        # Test 1: Provider connectivity
        await self.run_test(
            "ai_connectivity",
            self.check_connectivity,
            VerificationSeverity.CRITICAL
        )
        
        # Test 2: AI functionality
        await self.run_test(
            "ai_functionality",
            self.check_functionality,
            VerificationSeverity.HIGH
        )
        
        # Test 3: Resume parsing capability
        await self.run_test(
            "resume_parsing",
            self.check_resume_parsing_capability,
            VerificationSeverity.HIGH
        )
        
        # Test 4: Performance characteristics
        await self.run_test(
            "ai_performance",
            self.check_performance,
            VerificationSeverity.MEDIUM
        )
        
        return self.get_summary()