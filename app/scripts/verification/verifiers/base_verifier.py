"""
Base verification classes providing common functionality for all verifiers
"""
import asyncio
import logging
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union
from dataclasses import dataclass
from datetime import datetime, timezone
from enum import Enum


class VerificationStatus(Enum):
    """Verification result status"""
    PASS = "PASS"
    FAIL = "FAIL"  
    WARNING = "WARNING"
    SKIP = "SKIP"
    ERROR = "ERROR"


class VerificationSeverity(Enum):
    """Verification issue severity levels"""
    CRITICAL = "CRITICAL"  # System cannot function
    HIGH = "HIGH"         # Major functionality impacted
    MEDIUM = "MEDIUM"     # Minor functionality impacted  
    LOW = "LOW"          # Performance or usability issues
    INFO = "INFO"        # Informational only


@dataclass
class VerificationResult:
    """Individual verification test result"""
    name: str
    status: VerificationStatus
    severity: VerificationSeverity
    message: str
    details: Dict[str, Any]
    duration_ms: float
    timestamp: datetime
    evidence: List[Dict[str, Any]] = None
    recommendations: List[str] = None
    
    def __post_init__(self):
        if self.evidence is None:
            self.evidence = []
        if self.recommendations is None:
            self.recommendations = []
    
    @property
    def is_success(self) -> bool:
        """Check if verification passed"""
        return self.status == VerificationStatus.PASS
    
    @property 
    def is_failure(self) -> bool:
        """Check if verification failed"""
        return self.status == VerificationStatus.FAIL
    
    @property
    def is_critical(self) -> bool:
        """Check if verification has critical severity"""
        return self.severity == VerificationSeverity.CRITICAL
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "name": self.name,
            "status": self.status.value,
            "severity": self.severity.value,
            "message": self.message,
            "details": self.details,
            "duration_ms": self.duration_ms,
            "timestamp": self.timestamp.isoformat(),
            "evidence": self.evidence,
            "recommendations": self.recommendations
        }


@dataclass
class VerificationSummary:
    """Summary of all verification results"""
    component_name: str
    total_tests: int
    passed: int
    failed: int
    warnings: int
    skipped: int
    errors: int
    critical_issues: int
    total_duration_ms: float
    start_time: datetime
    end_time: datetime
    results: List[VerificationResult]
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate percentage"""
        if self.total_tests == 0:
            return 0.0
        return (self.passed / self.total_tests) * 100
    
    @property
    def is_healthy(self) -> bool:
        """Check if component is healthy (no critical failures)"""
        return self.critical_issues == 0 and self.failed == 0
    
    @property
    def health_score(self) -> float:
        """Calculate overall health score (0-100)"""
        if self.total_tests == 0:
            return 0.0
        
        # Weight different statuses
        score = (
            (self.passed * 100) +
            (self.warnings * 50) + 
            (self.skipped * 25) +
            (self.errors * 0) +
            (self.failed * 0)
        ) / self.total_tests
        
        # Penalize critical issues heavily
        if self.critical_issues > 0:
            score = max(0, score - (self.critical_issues * 25))
        
        return min(100.0, score)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "component_name": self.component_name,
            "summary": {
                "total_tests": self.total_tests,
                "passed": self.passed,
                "failed": self.failed,
                "warnings": self.warnings,
                "skipped": self.skipped,
                "errors": self.errors,
                "critical_issues": self.critical_issues,
                "success_rate": self.success_rate,
                "health_score": self.health_score,
                "is_healthy": self.is_healthy
            },
            "timing": {
                "total_duration_ms": self.total_duration_ms,
                "start_time": self.start_time.isoformat(),
                "end_time": self.end_time.isoformat()
            },
            "results": [result.to_dict() for result in self.results]
        }


class BaseVerifier(ABC):
    """Base class for all verification components"""
    
    def __init__(self, name: str, logger: Optional[logging.Logger] = None):
        self.name = name
        self.logger = logger or logging.getLogger(self.__class__.__name__)
        self.results: List[VerificationResult] = []
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        
    def log_info(self, message: str, **kwargs):
        """Log info message with component context"""
        self.logger.info(f"[{self.name}] {message}", extra=kwargs)
        
    def log_warning(self, message: str, **kwargs):
        """Log warning message with component context"""
        self.logger.warning(f"[{self.name}] {message}", extra=kwargs)
        
    def log_error(self, message: str, **kwargs):
        """Log error message with component context"""
        self.logger.error(f"[{self.name}] {message}", extra=kwargs)
        
    def log_debug(self, message: str, **kwargs):
        """Log debug message with component context"""
        self.logger.debug(f"[{self.name}] {message}", extra=kwargs)
    
    def add_evidence(self, test_name: str, evidence_type: str, data: Any, description: str = None):
        """Add evidence for a verification test"""
        evidence = {
            "type": evidence_type,
            "data": data,
            "description": description or f"{evidence_type} evidence for {test_name}",
            "collected_at": datetime.now(timezone.utc).isoformat()
        }
        
        # Find the most recent result and add evidence
        for result in reversed(self.results):
            if result.name == test_name:
                result.evidence.append(evidence)
                break
    
    def add_recommendation(self, test_name: str, recommendation: str):
        """Add recommendation for a verification test"""
        # Find the most recent result and add recommendation
        for result in reversed(self.results):
            if result.name == test_name:
                result.recommendations.append(recommendation)
                break
    
    def record_result(self, 
                     name: str,
                     status: VerificationStatus, 
                     severity: VerificationSeverity,
                     message: str,
                     details: Dict[str, Any] = None,
                     duration_ms: float = 0,
                     evidence: List[Dict[str, Any]] = None,
                     recommendations: List[str] = None):
        """Record a verification result"""
        
        result = VerificationResult(
            name=name,
            status=status,
            severity=severity, 
            message=message,
            details=details or {},
            duration_ms=duration_ms,
            timestamp=datetime.now(timezone.utc),
            evidence=evidence or [],
            recommendations=recommendations or []
        )
        
        self.results.append(result)
        
        # Log the result
        log_level = {
            VerificationStatus.PASS: "info",
            VerificationStatus.FAIL: "error", 
            VerificationStatus.WARNING: "warning",
            VerificationStatus.SKIP: "info",
            VerificationStatus.ERROR: "error"
        }.get(status, "info")
        
        getattr(self.logger, log_level)(
            f"[{self.name}] {name}: {status.value} - {message}"
        )
        
        return result
    
    async def run_test(self, test_name: str, test_func, severity: VerificationSeverity = VerificationSeverity.MEDIUM, **kwargs):
        """Run a single verification test with timing and error handling"""
        start_time = time.time()
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func(**kwargs)
            else:
                result = test_func(**kwargs)
            
            duration_ms = (time.time() - start_time) * 1000
            
            if isinstance(result, dict) and "status" in result:
                # Test function returned a structured result
                return self.record_result(
                    name=test_name,
                    status=VerificationStatus(result["status"]),
                    severity=severity,
                    message=result.get("message", ""),
                    details=result.get("details", {}),
                    duration_ms=duration_ms,
                    evidence=result.get("evidence", []),
                    recommendations=result.get("recommendations", [])
                )
            else:
                # Test function returned simple success/failure
                if result:
                    return self.record_result(
                        name=test_name,
                        status=VerificationStatus.PASS,
                        severity=severity,
                        message=f"{test_name} completed successfully",
                        details={"result": result} if result != True else {},
                        duration_ms=duration_ms
                    )
                else:
                    return self.record_result(
                        name=test_name,
                        status=VerificationStatus.FAIL,
                        severity=severity,
                        message=f"{test_name} failed",
                        details={},
                        duration_ms=duration_ms
                    )
                    
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            
            return self.record_result(
                name=test_name,
                status=VerificationStatus.ERROR,
                severity=VerificationSeverity.HIGH,
                message=f"{test_name} threw an exception: {str(e)}",
                details={
                    "exception_type": type(e).__name__,
                    "exception_message": str(e)
                },
                duration_ms=duration_ms,
                recommendations=[f"Fix the underlying issue causing {type(e).__name__}"]
            )
    
    @abstractmethod
    async def verify(self) -> VerificationSummary:
        """Main verification method - must be implemented by subclasses"""
        pass
    
    def get_summary(self) -> VerificationSummary:
        """Generate verification summary from recorded results"""
        if not self.start_time:
            self.start_time = datetime.now(timezone.utc)
        if not self.end_time:
            self.end_time = datetime.now(timezone.utc)
        
        # Count results by status
        status_counts = {
            VerificationStatus.PASS: 0,
            VerificationStatus.FAIL: 0, 
            VerificationStatus.WARNING: 0,
            VerificationStatus.SKIP: 0,
            VerificationStatus.ERROR: 0
        }
        
        critical_issues = 0
        total_duration = 0
        
        for result in self.results:
            status_counts[result.status] += 1
            if result.severity == VerificationSeverity.CRITICAL:
                critical_issues += 1
            total_duration += result.duration_ms
        
        return VerificationSummary(
            component_name=self.name,
            total_tests=len(self.results),
            passed=status_counts[VerificationStatus.PASS],
            failed=status_counts[VerificationStatus.FAIL],
            warnings=status_counts[VerificationStatus.WARNING],
            skipped=status_counts[VerificationStatus.SKIP],
            errors=status_counts[VerificationStatus.ERROR],
            critical_issues=critical_issues,
            total_duration_ms=total_duration,
            start_time=self.start_time,
            end_time=self.end_time,
            results=self.results
        )
    
    async def run_verification(self) -> VerificationSummary:
        """Execute verification with timing and summary generation"""
        self.start_time = datetime.now(timezone.utc)
        self.log_info(f"Starting verification for {self.name}")
        
        try:
            summary = await self.verify()
            self.end_time = datetime.now(timezone.utc)
            
            self.log_info(f"Verification completed: {summary.passed}/{summary.total_tests} tests passed")
            
            if summary.critical_issues > 0:
                self.log_error(f"Found {summary.critical_issues} critical issues")
            elif summary.failed > 0:
                self.log_warning(f"Found {summary.failed} failed tests")
                
            return summary
            
        except Exception as e:
            self.end_time = datetime.now(timezone.utc)
            self.log_error(f"Verification failed with exception: {e}")
            
            # Record the exception as a critical failure
            self.record_result(
                name="verification_execution",
                status=VerificationStatus.ERROR,
                severity=VerificationSeverity.CRITICAL,
                message=f"Verification execution failed: {str(e)}",
                details={"exception_type": type(e).__name__, "exception_message": str(e)},
                duration_ms=0
            )
            
            return self.get_summary()


class ComponentVerifier(BaseVerifier):
    """Base class for component-level verifiers (PostgreSQL, Redis, etc.)"""
    
    def __init__(self, component_name: str, connection_config: Dict[str, Any]):
        super().__init__(component_name)
        self.config = connection_config
        
    @abstractmethod
    async def check_connectivity(self) -> Dict[str, Any]:
        """Check basic connectivity to component"""
        pass
        
    @abstractmethod  
    async def check_functionality(self) -> Dict[str, Any]:
        """Check core functionality of component"""
        pass
        
    @abstractmethod
    async def check_performance(self) -> Dict[str, Any]:
        """Check performance characteristics of component"""
        pass


class IntegrationVerifier(BaseVerifier):
    """Base class for integration-level verifiers"""
    
    def __init__(self, integration_name: str, components: List[str]):
        super().__init__(integration_name)
        self.components = components
        
    @abstractmethod
    async def check_data_flow(self) -> Dict[str, Any]:
        """Check data flow between components"""
        pass
        
    @abstractmethod
    async def check_error_handling(self) -> Dict[str, Any]:
        """Check error handling and fallback mechanisms"""
        pass


class BusinessFlowVerifier(BaseVerifier):
    """Base class for end-to-end business flow verifiers"""
    
    def __init__(self, flow_name: str, expected_steps: List[str]):
        super().__init__(flow_name)
        self.expected_steps = expected_steps
        
    @abstractmethod
    async def execute_flow(self, test_data: Dict[str, Any]) -> Dict[str, Any]:
        """Execute complete business flow with test data"""
        pass
        
    @abstractmethod
    async def validate_outcome(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Validate the business flow outcome"""
        pass


class PerformanceBenchmarker:
    """Utility class for performance testing and benchmarking"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
    
    async def measure_async_operation(self, operation, *args, **kwargs) -> Dict[str, Any]:
        """Measure performance of async operation"""
        start_time = time.time()
        memory_before = self._get_memory_usage()
        
        try:
            if asyncio.iscoroutinefunction(operation):
                result = await operation(*args, **kwargs)
            else:
                result = operation(*args, **kwargs)
            
            end_time = time.time()
            memory_after = self._get_memory_usage()
            
            return {
                "success": True,
                "result": result,
                "duration_ms": (end_time - start_time) * 1000,
                "memory_used_mb": memory_after - memory_before if memory_after and memory_before else None,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            end_time = time.time()
            
            return {
                "success": False,
                "error": str(e),
                "exception_type": type(e).__name__,
                "duration_ms": (end_time - start_time) * 1000,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def _get_memory_usage(self) -> Optional[float]:
        """Get current memory usage in MB"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except ImportError:
            return None
        except Exception:
            return None
    
    async def load_test(self, operation, test_data_list: List[Any], 
                       max_concurrent: int = 5, timeout: float = 30) -> Dict[str, Any]:
        """Execute load testing with multiple concurrent operations"""
        
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def bounded_operation(data):
            async with semaphore:
                return await self.measure_async_operation(operation, data)
        
        start_time = time.time()
        
        try:
            # Execute all operations concurrently with timeout
            results = await asyncio.wait_for(
                asyncio.gather(*[bounded_operation(data) for data in test_data_list]),
                timeout=timeout
            )
            
            total_duration = time.time() - start_time
            
            # Analyze results
            successful = [r for r in results if r.get("success", False)]
            failed = [r for r in results if not r.get("success", False)]
            
            avg_duration = sum(r.get("duration_ms", 0) for r in successful) / len(successful) if successful else 0
            min_duration = min(r.get("duration_ms", 0) for r in successful) if successful else 0
            max_duration = max(r.get("duration_ms", 0) for r in successful) if successful else 0
            
            return {
                "total_operations": len(test_data_list),
                "successful": len(successful),
                "failed": len(failed),
                "success_rate": len(successful) / len(test_data_list) * 100,
                "total_duration_s": total_duration,
                "operations_per_second": len(test_data_list) / total_duration,
                "avg_operation_duration_ms": avg_duration,
                "min_operation_duration_ms": min_duration,
                "max_operation_duration_ms": max_duration,
                "failed_operations": failed,
                "concurrency_used": max_concurrent
            }
            
        except asyncio.TimeoutError:
            return {
                "error": "Load test timed out",
                "timeout_seconds": timeout,
                "operations_attempted": len(test_data_list),
                "concurrency_used": max_concurrent
            }
        except Exception as e:
            return {
                "error": f"Load test failed: {str(e)}",
                "exception_type": type(e).__name__,
                "operations_attempted": len(test_data_list)
            }


def create_test_logger(name: str, level: str = "INFO") -> logging.Logger:
    """Create a logger for verification testing"""
    
    logger = logging.getLogger(f"verification.{name}")
    logger.setLevel(getattr(logging, level.upper()))
    
    # Only add handler if not already present
    if not logger.handlers:
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.DEBUG)
        
        # Formatter with verification context
        formatter = logging.Formatter(
            fmt='%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # Prevent propagation to avoid duplicate logs
        logger.propagate = False
    
    return logger