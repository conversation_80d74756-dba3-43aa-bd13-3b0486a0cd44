"""
Service integration verification for component interconnections
"""
import asyncio
import json
import time
from typing import Dict, Any, List
from datetime import datetime, timezone

import httpx
import asyncpg
import redis.asyncio as redis
from minio import Minio

from ..base_verifier import IntegrationVerifier, VerificationStatus, VerificationSeverity, VerificationSummary


class ServiceIntegrationVerifier(IntegrationVerifier):
    """Verify integration between different system services"""
    
    def __init__(self, connection_config: Dict[str, Any]):
        super().__init__("Service Integration", ["postgresql", "redis", "minio", "backend_api", "celery"])
        self.config = connection_config
        
        # Connection configurations
        self.db_config = connection_config.get("database", {})
        self.redis_config = connection_config.get("redis", {})
        self.minio_config = connection_config.get("minio", {})
        self.api_config = connection_config.get("api", {})
        self.dev_token = connection_config.get("dev_token", "dev_bypass_token_2025_talentforge")
    
    async def check_data_flow(self) -> Dict[str, Any]:
        """Test data flow between API, database, and cache"""
        try:
            results = {}
            
            # Test 1: API → Database → Cache flow
            async with httpx.AsyncClient(timeout=30.0) as client:
                api_base = self.api_config.get("base_url", "http://localhost:8088")
                api_prefix = self.api_config.get("api_prefix", "/api/v1")
                
                headers = {"Authorization": f"Bearer {self.dev_token}"}
                
                # Step 1: Make API call that should trigger database query
                start_time = time.time()
                response = await client.get(f"{api_base}{api_prefix}/auth/me", headers=headers)
                api_response_time = (time.time() - start_time) * 1000
                
                if response.status_code == 200:
                    user_data = response.json()
                    results["api_database_flow"] = {
                        "api_success": True,
                        "response_time_ms": api_response_time,
                        "user_data_received": bool(user_data.get("id"))
                    }
                    
                    # Step 2: Verify data exists in database directly
                    try:
                        db_connection = await asyncpg.connect(
                            host=self.db_config.get("host", "localhost"),
                            port=self.db_config.get("port", 5432),
                            database=self.db_config.get("database", "talentforge"),
                            user=self.db_config.get("user", "tfuser"),
                            password=self.db_config.get("password", "tfpass123")
                        )
                        
                        user_id = user_data.get("id")
                        if user_id:
                            db_user = await db_connection.fetchrow(
                                "SELECT id, email, username FROM users WHERE id = $1", 
                                int(user_id)
                            )
                            
                            results["database_verification"] = {
                                "user_exists_in_db": db_user is not None,
                                "data_consistent": db_user["email"] == user_data.get("email") if db_user else False
                            }
                        
                        await db_connection.close()
                        
                    except Exception as e:
                        results["database_verification"] = {
                            "error": str(e),
                            "user_exists_in_db": False
                        }
                    
                else:
                    results["api_database_flow"] = {
                        "api_success": False,
                        "status_code": response.status_code,
                        "error": response.text[:200]
                    }
            
            # Test 2: Cache integration flow
            try:
                redis_client = redis.Redis(
                    host=self.redis_config.get("host", "localhost"),
                    port=self.redis_config.get("port", 6379),
                    password=self.redis_config.get("password"),
                    db=0,
                    decode_responses=True
                )
                
                # Test cache operations that API might use
                cache_test_key = "verification:integration_test"
                test_cache_data = {
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "test_id": "integration_flow"
                }
                
                # Set cache data
                await redis_client.setex(
                    cache_test_key,
                    60,
                    json.dumps(test_cache_data)
                )
                
                # Verify cache data
                cached_data = await redis_client.get(cache_test_key)
                cache_ttl = await redis_client.ttl(cache_test_key)
                
                if cached_data:
                    parsed_cache = json.loads(cached_data)
                    results["cache_integration"] = {
                        "cache_set_successful": True,
                        "cache_get_successful": True,
                        "data_integrity": parsed_cache == test_cache_data,
                        "ttl_set_correctly": 0 < cache_ttl <= 60
                    }
                else:
                    results["cache_integration"] = {
                        "cache_set_successful": False,
                        "cache_get_successful": False
                    }
                
                # Clean up
                await redis_client.delete(cache_test_key)
                await redis_client.close()
                
            except Exception as e:
                results["cache_integration"] = {
                    "error": str(e),
                    "cache_available": False
                }
            
            # Test 3: API → Storage integration
            try:
                # Test MinIO through API if file upload endpoint exists
                minio_client = Minio(
                    self.minio_config.get("endpoint", "localhost:9000"),
                    access_key=self.minio_config.get("access_key", "minioadmin"),
                    secret_key=self.minio_config.get("secret_key", "minioadmin123"),
                    secure=self.minio_config.get("secure", False)
                )
                
                # Check if test bucket exists
                test_bucket = "verification-integration"
                bucket_exists = minio_client.bucket_exists(test_bucket)
                
                if not bucket_exists:
                    minio_client.make_bucket(test_bucket)
                
                # Test file operation that API might perform
                import io
                test_file_content = "Integration test file content"
                test_file_stream = io.BytesIO(test_file_content.encode())
                
                minio_client.put_object(
                    test_bucket,
                    "integration_test.txt",
                    test_file_stream,
                    length=len(test_file_content)
                )
                
                # Verify file exists
                try:
                    file_response = minio_client.get_object(test_bucket, "integration_test.txt")
                    file_content = file_response.read().decode()
                    file_response.close()
                    file_response.release_conn()
                    
                    results["storage_integration"] = {
                        "file_upload_successful": True,
                        "file_download_successful": True,
                        "content_integrity": file_content == test_file_content,
                        "bucket_operations_work": True
                    }
                    
                    # Clean up
                    minio_client.remove_object(test_bucket, "integration_test.txt")
                    
                except Exception as e:
                    results["storage_integration"] = {
                        "file_upload_successful": True,
                        "file_download_successful": False,
                        "error": str(e)
                    }
                
            except Exception as e:
                results["storage_integration"] = {
                    "error": str(e),
                    "storage_available": False
                }
            
            # Integration assessment
            integrations_working = [
                results.get("api_database_flow", {}).get("api_success", False),
                results.get("database_verification", {}).get("data_consistent", False),
                results.get("cache_integration", {}).get("data_integrity", False),
                results.get("storage_integration", {}).get("content_integrity", False)
            ]
            
            working_count = sum(1 for integration in integrations_working if integration)
            total_integrations = len(integrations_working)
            
            if working_count == total_integrations:
                return {
                    "status": "PASS",
                    "message": f"All service integrations working ({working_count}/{total_integrations})",
                    "details": results,
                    "evidence": [
                        {
                            "type": "integration_flows",
                            "data": results,
                            "description": "Service integration test results"
                        }
                    ]
                }
            elif working_count >= total_integrations * 0.7:
                return {
                    "status": "WARNING",
                    "message": f"Some integration issues ({working_count}/{total_integrations} working)",
                    "details": results,
                    "recommendations": [
                        "Check failed service connections",
                        "Verify data consistency between services",
                        "Review error logs for integration failures",
                        "Test individual service health"
                    ]
                }
            else:
                return {
                    "status": "FAIL", 
                    "message": f"Major integration failures ({working_count}/{total_integrations} working)",
                    "details": results,
                    "recommendations": [
                        "Check all service connections",
                        "Verify network connectivity between services",
                        "Review service configuration and credentials",
                        "Check Docker networking and port mappings"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"Data flow test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_error_handling(self) -> Dict[str, Any]:
        """Test error handling and recovery mechanisms"""
        try:
            results = {}
            
            # Test 1: API error handling with invalid requests
            async with httpx.AsyncClient(timeout=30.0) as client:
                api_base = self.api_config.get("base_url", "http://localhost:8088")
                api_prefix = self.api_config.get("api_prefix", "/api/v1")
                
                error_scenarios = [
                    {
                        "name": "invalid_auth",
                        "url": f"{api_base}{api_prefix}/auth/me",
                        "headers": {"Authorization": "Bearer invalid_token"},
                        "expected_status": 401
                    },
                    {
                        "name": "missing_auth",
                        "url": f"{api_base}{api_prefix}/auth/me", 
                        "headers": {},
                        "expected_status": 401
                    },
                    {
                        "name": "not_found",
                        "url": f"{api_base}{api_prefix}/nonexistent/endpoint",
                        "headers": {"Authorization": f"Bearer {self.dev_token}"},
                        "expected_status": 404
                    }
                ]
                
                error_handling_results = {}
                
                for scenario in error_scenarios:
                    try:
                        start_time = time.time()
                        response = await client.get(scenario["url"], headers=scenario["headers"])
                        response_time = (time.time() - start_time) * 1000
                        
                        error_handling_results[scenario["name"]] = {
                            "status_code": response.status_code,
                            "expected_status": scenario["expected_status"],
                            "correct_error_code": response.status_code == scenario["expected_status"],
                            "response_time_ms": response_time,
                            "has_error_response": bool(response.text)
                        }
                        
                        # Check if response has proper error format
                        try:
                            error_data = response.json()
                            error_handling_results[scenario["name"]]["has_json_error"] = True
                            error_handling_results[scenario["name"]]["has_error_code"] = "error_code" in error_data
                        except json.JSONDecodeError:
                            error_handling_results[scenario["name"]]["has_json_error"] = False
                        
                    except Exception as e:
                        error_handling_results[scenario["name"]] = {
                            "error": str(e),
                            "test_failed": True
                        }
                
                results["api_error_handling"] = error_handling_results
            
            # Test 2: Database connection failure simulation
            # Note: This is verification only, we don't actually break connections
            try:
                # Test with invalid database credentials to see error handling
                try:
                    invalid_connection = await asyncpg.connect(
                        host=self.db_config.get("host", "localhost"),
                        port=self.db_config.get("port", 5432),
                        database=self.db_config.get("database", "talentforge"),
                        user="invalid_user",
                        password="invalid_password",
                        command_timeout=5
                    )
                    
                    # Should not reach here
                    results["db_error_simulation"] = {
                        "connection_should_fail": False,
                        "unexpected_success": True
                    }
                    
                    await invalid_connection.close()
                    
                except asyncpg.InvalidAuthorizationSpecificationError:
                    # Expected behavior
                    results["db_error_simulation"] = {
                        "auth_error_handled": True,
                        "proper_error_type": True
                    }
                except Exception as e:
                    results["db_error_simulation"] = {
                        "auth_error_handled": True,
                        "error_type": type(e).__name__,
                        "proper_error_handling": True
                    }
                
            except Exception as e:
                results["db_error_simulation"] = {
                    "simulation_error": str(e)
                }
            
            # Test 3: Redis connection failure simulation
            try:
                # Test with invalid Redis connection
                try:
                    invalid_redis = redis.Redis(
                        host="nonexistent.host",
                        port=6379,
                        socket_timeout=2.0,
                        socket_connect_timeout=2.0
                    )
                    
                    await invalid_redis.ping()
                    
                    # Should not reach here
                    results["redis_error_simulation"] = {
                        "connection_should_fail": False,
                        "unexpected_success": True
                    }
                    
                    await invalid_redis.close()
                    
                except Exception as e:
                    # Expected behavior
                    results["redis_error_simulation"] = {
                        "connection_error_handled": True,
                        "error_type": type(e).__name__,
                        "proper_error_handling": True
                    }
                    
            except Exception as e:
                results["redis_error_simulation"] = {
                    "simulation_error": str(e)
                }
            
            # Test 4: Service dependency chain validation
            # Verify that API gracefully handles service unavailability
            dependency_results = {}
            
            # Test API behavior with services
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {"Authorization": f"Bearer {self.dev_token}"}
                
                # Test endpoints that depend on different services
                service_dependent_endpoints = [
                    {"name": "user_data", "url": "/auth/me", "depends_on": ["postgresql"]},
                    {"name": "candidate_list", "url": "/candidates/", "depends_on": ["postgresql", "redis"]},
                    {"name": "health_check", "url": "/health", "depends_on": ["postgresql", "redis"]}
                ]
                
                for endpoint in service_dependent_endpoints:
                    endpoint_name = endpoint["name"]
                    url = f"{api_base}{api_prefix}{endpoint['url']}"
                    
                    try:
                        start_time = time.time()
                        response = await client.get(url, headers=headers)
                        response_time = (time.time() - start_time) * 1000
                        
                        dependency_results[endpoint_name] = {
                            "status_code": response.status_code,
                            "response_time_ms": response_time,
                            "success": response.status_code < 400,
                            "depends_on": endpoint["depends_on"]
                        }
                        
                        if response.status_code == 200:
                            try:
                                data = response.json()
                                dependency_results[endpoint_name]["has_data"] = bool(data)
                            except Exception:
                                dependency_results[endpoint_name]["has_data"] = False
                        
                    except Exception as e:
                        dependency_results[endpoint_name] = {
                            "success": False,
                            "error": str(e),
                            "depends_on": endpoint["depends_on"]
                        }
                
                results["service_dependency_validation"] = dependency_results
            
            # Assessment
            api_db_working = results.get("api_database_flow", {}).get("api_success", False)
            db_consistency = results.get("database_verification", {}).get("data_consistent", False) 
            error_handling_proper = all(
                result.get("correct_error_code", False) 
                for result in results.get("api_error_handling", {}).values()
            )
            
            service_deps_working = sum(
                1 for result in dependency_results.values() 
                if result.get("success", False)
            ) / len(dependency_results) if dependency_results else 0
            
            integration_score = sum([api_db_working, db_consistency, error_handling_proper, service_deps_working >= 0.8]) / 4
            
            if integration_score >= 0.8:
                return {
                    "status": "PASS",
                    "message": f"Service integration excellent ({integration_score:.1%} overall score)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "integration_validation",
                            "data": {
                                "data_flow": results.get("api_database_flow"),
                                "error_handling": results.get("api_error_handling"),
                                "dependencies": dependency_results
                            },
                            "description": "Service integration test results"
                        }
                    ]
                }
            elif integration_score >= 0.5:
                return {
                    "status": "WARNING",
                    "message": f"Service integration issues detected ({integration_score:.1%} overall score)",
                    "details": results,
                    "recommendations": [
                        "Review service error handling implementation",
                        "Check data consistency between API and database",
                        "Verify proper error response formats",
                        "Test service dependency resilience"
                    ]
                }
            else:
                return {
                    "status": "FAIL",
                    "message": f"Service integration failing ({integration_score:.1%} overall score)",
                    "details": results,
                    "recommendations": [
                        "Fix critical service integration issues",
                        "Review API to database data flow",
                        "Check service authentication and authorization",
                        "Verify all service dependencies are running"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"Service integration test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def check_async_task_integration(self) -> Dict[str, Any]:
        """Test Celery task queue integration"""
        try:
            results = {}
            
            # Test 1: Task dispatch through API
            async with httpx.AsyncClient(timeout=60.0) as client:
                api_base = self.api_config.get("base_url", "http://localhost:8088")
                api_prefix = self.api_config.get("api_prefix", "/api/v1")
                headers = {"Authorization": f"Bearer {self.dev_token}"}
                
                # Look for async task endpoints
                async_endpoints = [
                    {"name": "batch_operations", "url": "/batch/status", "method": "GET"},
                    # Add other async endpoints as they exist
                ]
                
                task_integration_results = {}
                
                for endpoint in async_endpoints:
                    endpoint_name = endpoint["name"]
                    url = f"{api_base}{api_prefix}{endpoint['url']}"
                    
                    try:
                        start_time = time.time()
                        response = await client.request(endpoint["method"], url, headers=headers)
                        response_time = (time.time() - start_time) * 1000
                        
                        task_integration_results[endpoint_name] = {
                            "status_code": response.status_code,
                            "response_time_ms": response_time,
                            "endpoint_available": response.status_code != 404,
                            "success": response.status_code < 500  # Allow 4xx but not 5xx
                        }
                        
                        if response.status_code == 200:
                            try:
                                data = response.json()
                                task_integration_results[endpoint_name]["has_task_data"] = bool(data)
                            except Exception:
                                task_integration_results[endpoint_name]["has_task_data"] = False
                        
                    except Exception as e:
                        task_integration_results[endpoint_name] = {
                            "error": str(e),
                            "endpoint_available": False
                        }
                
                results["task_api_integration"] = task_integration_results
            
            # Test 2: Direct Redis queue verification
            try:
                redis_client = redis.Redis(
                    host=self.redis_config.get("host", "localhost"),
                    port=self.redis_config.get("port", 6379),
                    password=self.redis_config.get("password"),
                    db=0,
                    decode_responses=True
                )
                
                # Check Celery queue keys
                celery_patterns = [
                    "celery-task-meta-*",  # Task results
                    "_kombu.binding.*",    # Queue bindings  
                ]
                
                celery_keys_found = 0
                for pattern in celery_patterns:
                    keys = await redis_client.keys(pattern)
                    celery_keys_found += len(keys)
                
                results["celery_queue_integration"] = {
                    "celery_keys_found": celery_keys_found,
                    "queue_infrastructure_present": celery_keys_found > 0,
                    "redis_available_for_tasks": True
                }
                
                await redis_client.close()
                
            except Exception as e:
                results["celery_queue_integration"] = {
                    "error": str(e),
                    "queue_infrastructure_present": False
                }
            
            # Assessment
            task_endpoints_available = sum(
                1 for result in task_integration_results.values()
                if result.get("endpoint_available", False)
            ) if task_integration_results else 0
            
            queue_infrastructure = results.get("celery_queue_integration", {}).get("queue_infrastructure_present", False)
            
            if queue_infrastructure and task_endpoints_available > 0:
                return {
                    "status": "PASS",
                    "message": f"Async task integration working ({task_endpoints_available} task endpoints available)",
                    "details": results,
                    "evidence": [
                        {
                            "type": "task_integration",
                            "data": {
                                "api_endpoints": task_integration_results,
                                "queue_status": results["celery_queue_integration"]
                            },
                            "description": "Async task integration test results"
                        }
                    ]
                }
            elif queue_infrastructure:
                return {
                    "status": "WARNING",
                    "message": "Task queue infrastructure present but no task endpoints found",
                    "details": results,
                    "recommendations": [
                        "Verify async task endpoints are implemented",
                        "Check Celery worker status",
                        "Test task dispatch functionality",
                        "Review task routing configuration"
                    ]
                }
            else:
                return {
                    "status": "FAIL",
                    "message": "Async task integration not working - no queue infrastructure",
                    "details": results,
                    "recommendations": [
                        "Start Celery workers",
                        "Check Redis connection for task queue",
                        "Verify Celery configuration",
                        "Review task queue setup"
                    ]
                }
                
        except Exception as e:
            return {
                "status": "ERROR",
                "message": f"Async task integration test error: {str(e)}",
                "details": {"error_type": type(e).__name__}
            }
    
    async def verify(self) -> VerificationSummary:
        """Execute all service integration verification tests"""
        self.start_time = datetime.now(timezone.utc)
        
        # Test 1: Data flow between services
        await self.run_test(
            "service_data_flow",
            self.check_data_flow,
            VerificationSeverity.HIGH
        )
        
        # Test 2: Error handling across services
        await self.run_test(
            "service_error_handling",
            self.check_error_handling,
            VerificationSeverity.HIGH
        )
        
        # Test 3: Async task integration
        await self.run_test(
            "async_task_integration",
            self.check_async_task_integration,
            VerificationSeverity.MEDIUM
        )
        
        return self.get_summary()