"""
Main verification runner for TalentForge Pro business flow verification
"""
import asyncio
import json
import logging
import os
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional

# Add parent directories to path for imports
current_dir = Path(__file__).parent
sys.path.append(str(current_dir.parent.parent.parent))  # Add app/backend to path

from config.settings import VerificationConfig, VerificationMode
from utils.health_checker import HealthChecker
from utils.report_generator import ReportGenerator
from utils.test_data import TestDataGenerator

# Component verifiers
from verifiers.component.database_verifier import DatabaseVerifier
from verifiers.component.redis_verifier import RedisVerifier
from verifiers.component.minio_verifier import MinIOVerifier
from verifiers.component.ai_service_verifier import AIServiceVerifier
from verifiers.component.ocr_service_verifier import OCRServiceVerifier
from verifiers.component.api_verifier import APIVerifier

# Integration verifiers
from verifiers.integration.service_integration_verifier import ServiceIntegrationVerifier

# Business flow verifiers  
from verifiers.business_flow.resume_processing_verifier import ResumeProcessingVerifier


class VerificationRunner:
    """Main orchestrator for TalentForge Pro verification framework"""
    
    def __init__(self, config: Optional[VerificationConfig] = None):
        self.config = config or VerificationConfig()
        self.logger = self._setup_logging()
        self.health_checker = HealthChecker(self._get_service_configs())
        self.report_generator = ReportGenerator(self.config.report_directory)
        self.test_data = TestDataGenerator()
        
        # Results storage
        self.component_results = {}
        self.integration_results = {}
        self.business_flow_results = {}
        self.verification_start_time = None
        self.verification_end_time = None
    
    def _setup_logging(self) -> logging.Logger:
        """Setup logging configuration"""
        logger = logging.getLogger("verification_framework")
        logger.setLevel(getattr(logging, self.config.log_level))
        
        # Console handler
        if not logger.handlers:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setLevel(logging.DEBUG)
            
            formatter = logging.Formatter(
                '%(asctime)s | %(levelname)-8s | %(name)s | %(message)s',
                datefmt='%Y-%m-%d %H:%M:%S'
            )
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
            
            # File handler if enabled
            if self.config.log_to_file:
                log_file_path = Path(self.config.log_file_path)
                log_file_path.parent.mkdir(parents=True, exist_ok=True)
                
                file_handler = logging.FileHandler(log_file_path)
                file_handler.setLevel(logging.INFO)
                file_handler.setFormatter(formatter)
                logger.addHandler(file_handler)
        
        return logger
    
    def _get_service_configs(self) -> Dict[str, Any]:
        """Get service configuration from environment or defaults"""
        
        return {
            "database": {
                "host": os.getenv("POSTGRES_SERVER", "localhost"),
                "port": int(os.getenv("POSTGRES_PORT", 5432)),
                "database": os.getenv("POSTGRES_DB", "talentforge"),
                "user": os.getenv("POSTGRES_USER", "tfuser"),
                "password": os.getenv("POSTGRES_PASSWORD", "tfpass123")
            },
            "redis": {
                "host": os.getenv("REDIS_HOST", "localhost"),
                "port": int(os.getenv("REDIS_PORT", 6379)),
                "password": os.getenv("REDIS_PASSWORD"),
                "db": 0
            },
            "minio": {
                "endpoint": os.getenv("MINIO_ENDPOINT", "localhost:9000"),
                "access_key": os.getenv("MINIO_ROOT_USER", "minioadmin"),
                "secret_key": os.getenv("MINIO_ROOT_PASSWORD", "minioadmin123"),
                "secure": os.getenv("MINIO_USE_SSL", "false").lower() == "true"
            },
            "api": {
                "base_url": os.getenv("API_BASE_URL", "http://localhost:8088"),
                "api_prefix": "/api/v1",
                "timeout": 30
            },
            "ai_services": {
                "deepseek_api_key": os.getenv("DEEPSEEK_API_KEY"),
                "deepseek_api_base": os.getenv("DEEPSEEK_API_BASE", "https://api.deepseek.com/v1"),
                "moonshot_api_key": os.getenv("MOONSHOT_API_KEY"),
                "moonshot_api_base": os.getenv("MOONSHOT_API_BASE", "https://api.moonshot.cn/v1"),
                "openrouter_api_key": os.getenv("OPENROUTER_API_KEY"),
                "qwen_api_key": os.getenv("QWEN_API_KEY"),
                "ollama_base_url": os.getenv("OLLAMA_BASE_URL", "http://localhost:11434"),
                "primary_provider": os.getenv("LLM_PROVIDER", "deepseek")
            },
            "ocr": {
                "use_gpu": os.getenv("OCR_USE_GPU", "true").lower() == "true",
                "supported_languages": os.getenv("OCR_SUPPORTED_LANGUAGES", "en").split(","),
                "confidence_threshold": float(os.getenv("OCR_CONFIDENCE_THRESHOLD", "0.3"))
            },
            "dev_token": os.getenv("DEV_BYPASS_TOKEN", "dev_bypass_token_2025_talentforge")
        }
    
    async def run_verification(self, mode: Optional[VerificationMode] = None) -> Dict[str, Any]:
        """Execute complete verification suite"""
        
        if mode:
            self.config.set_mode(mode)
        
        self.verification_start_time = datetime.now(timezone.utc)
        self.logger.info(f"Starting TalentForge Pro verification (mode: {self.config.mode.value})")
        
        try:
            # Phase 1: Quick health check
            self.logger.info("Phase 1: Quick health assessment")
            health_status = await self.health_checker.quick_health_check()
            
            if health_status["overall_status"] == "unhealthy":
                self.logger.error("Quick health check failed - system may not be ready for verification")
                if not self.config.continue_on_failure:
                    return {
                        "verification_aborted": True,
                        "reason": "System unhealthy",
                        "health_status": health_status
                    }
            
            # Phase 2: Component verification
            if self.config.parallel_execution:
                await self._run_component_verification_parallel()
            else:
                await self._run_component_verification_sequential()
            
            # Phase 3: Integration verification
            self.logger.info("Phase 3: Integration verification")
            await self._run_integration_verification()
            
            # Phase 4: Business flow verification
            self.logger.info("Phase 4: Business flow verification")
            await self._run_business_flow_verification()
            
            # Phase 5: Generate comprehensive results
            self.verification_end_time = datetime.now(timezone.utc)
            verification_results = self._compile_results()
            
            # Phase 6: Generate reports
            if self.config.generate_reports:
                self.logger.info("Phase 6: Generating verification reports")
                report_files = self.report_generator.generate_comprehensive_report(
                    verification_results,
                    formats=self.config.export_formats
                )
                verification_results["report_files"] = report_files
                
                # Generate executive summary
                executive_summary = self.report_generator.generate_executive_summary(verification_results)
                self.logger.info("\\n" + executive_summary)
            
            self.logger.info("Verification completed successfully")
            return verification_results
            
        except Exception as e:
            self.logger.error(f"Verification failed with error: {e}")
            return {
                "verification_failed": True,
                "error": str(e),
                "error_type": type(e).__name__,
                "partial_results": self._compile_results()
            }
    
    async def _run_component_verification_parallel(self):
        """Run component verification in parallel"""
        self.logger.info("Phase 2: Component verification (parallel mode)")
        
        # Create component verifiers
        service_configs = self._get_service_configs()
        
        verifiers = {}
        enabled_components = self.config.get_enabled_components()
        
        if "postgresql" in enabled_components:
            verifiers["postgresql"] = DatabaseVerifier(service_configs["database"])
        
        if "redis" in enabled_components:
            verifiers["redis"] = RedisVerifier(service_configs["redis"])
        
        if "minio" in enabled_components:
            verifiers["minio"] = MinIOVerifier(service_configs["minio"])
        
        if "ai_services" in enabled_components:
            verifiers["ai_services"] = AIServiceVerifier(service_configs["ai_services"])
        
        if "ocr_service" in enabled_components:
            verifiers["ocr_service"] = OCRServiceVerifier(service_configs["ocr"])
        
        if "backend_api" in enabled_components:
            verifiers["backend_api"] = APIVerifier(service_configs["api"])
        
        # Execute verifications with concurrency limit
        semaphore = asyncio.Semaphore(self.config.max_concurrent_verifiers)
        
        async def run_verifier(name, verifier):
            async with semaphore:
                self.logger.info(f"Starting {name} verification")
                start_time = time.time()
                
                try:
                    result = await verifier.run_verification()
                    duration = time.time() - start_time
                    
                    self.logger.info(f"Completed {name} verification ({duration:.1f}s): {result.health_score:.1f}% health")
                    return name, result
                    
                except Exception as e:
                    duration = time.time() - start_time
                    self.logger.error(f"Failed {name} verification ({duration:.1f}s): {e}")
                    return name, None
        
        # Run all verifiers in parallel
        verification_tasks = [
            run_verifier(name, verifier) 
            for name, verifier in verifiers.items()
        ]
        
        results = await asyncio.gather(*verification_tasks, return_exceptions=True)
        
        # Process results
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"Verifier task failed: {result}")
            elif result[1] is not None:
                name, verification_summary = result
                self.component_results[name] = verification_summary.to_dict()
            else:
                name = result[0] if result else "unknown"
                self.logger.warning(f"Verifier {name} returned no results")
    
    async def _run_component_verification_sequential(self):
        """Run component verification sequentially"""
        self.logger.info("Phase 2: Component verification (sequential mode)")
        
        service_configs = self._get_service_configs()
        enabled_components = self.config.get_enabled_components()
        
        # Define verification order (dependencies first)
        verification_order = [
            ("postgresql", DatabaseVerifier),
            ("redis", RedisVerifier),
            ("minio", MinIOVerifier),
            ("backend_api", APIVerifier),
            ("ai_services", AIServiceVerifier),
            ("ocr_service", OCRServiceVerifier)
        ]
        
        for component_name, verifier_class in verification_order:
            if component_name not in enabled_components:
                self.logger.info(f"Skipping {component_name} verification (disabled)")
                continue
            
            self.logger.info(f"Starting {component_name} verification")
            start_time = time.time()
            
            try:
                # Get component-specific config
                if component_name == "ai_services":
                    config = service_configs["ai_services"]
                elif component_name == "ocr_service":
                    config = service_configs["ocr"]
                elif component_name == "backend_api":
                    config = service_configs["api"]
                else:
                    config = service_configs[component_name]
                
                verifier = verifier_class(config)
                result = await verifier.run_verification()
                
                duration = time.time() - start_time
                self.logger.info(f"Completed {component_name} verification ({duration:.1f}s): {result.health_score:.1f}% health")
                
                self.component_results[component_name] = result.to_dict()
                
                # Stop on critical failure if configured
                if not self.config.continue_on_failure and result.critical_issues > 0:
                    self.logger.error(f"Critical issues in {component_name} - stopping verification")
                    break
                    
            except Exception as e:
                duration = time.time() - start_time
                self.logger.error(f"Failed {component_name} verification ({duration:.1f}s): {e}")
                
                self.component_results[component_name] = {
                    "component_name": component_name,
                    "error": str(e),
                    "verification_failed": True
                }
    
    async def _run_integration_verification(self):
        """Run integration verification tests"""
        
        if not self.component_results:
            self.logger.warning("No component results available - skipping integration verification")
            return
        
        service_configs = self._get_service_configs()
        
        # Only run integration tests if core components are healthy
        core_components = ["postgresql", "redis", "backend_api"]
        core_healthy = all(
            self.component_results.get(comp, {}).get("summary", {}).get("is_healthy", False)
            for comp in core_components
            if comp in self.component_results
        )
        
        if not core_healthy:
            self.logger.warning("Core components unhealthy - skipping integration verification") 
            return
        
        self.logger.info("Running service integration verification")
        
        try:
            integration_verifier = ServiceIntegrationVerifier(service_configs)
            result = await integration_verifier.run_verification()
            
            self.integration_results["service_integration"] = result.to_dict()
            self.logger.info(f"Integration verification completed: {result.health_score:.1f}% health")
            
        except Exception as e:
            self.logger.error(f"Integration verification failed: {e}")
            self.integration_results["service_integration"] = {
                "error": str(e),
                "verification_failed": True
            }
    
    async def _run_business_flow_verification(self):
        """Run business flow verification tests"""
        
        # Check if prerequisites are met
        prerequisites_met = (
            self.component_results.get("backend_api", {}).get("summary", {}).get("is_healthy", False) and
            self.component_results.get("postgresql", {}).get("summary", {}).get("is_healthy", False)
        )
        
        if not prerequisites_met:
            self.logger.warning("Prerequisites not met - skipping business flow verification")
            return
        
        service_configs = self._get_service_configs()
        
        # Resume processing pipeline
        self.logger.info("Running resume processing pipeline verification")
        
        try:
            resume_verifier = ResumeProcessingVerifier(service_configs)
            result = await resume_verifier.run_verification()
            
            self.business_flow_results["resume_processing"] = result.to_dict()
            self.logger.info(f"Resume processing verification completed: {result.health_score:.1f}% health")
            
        except Exception as e:
            self.logger.error(f"Resume processing verification failed: {e}")
            self.business_flow_results["resume_processing"] = {
                "error": str(e),
                "verification_failed": True
            }
    
    def _compile_results(self) -> Dict[str, Any]:
        """Compile all verification results into comprehensive report"""
        
        compilation_time = datetime.now(timezone.utc)
        
        # Calculate overall metrics
        total_components = len(self.component_results)
        healthy_components = sum(
            1 for result in self.component_results.values()
            if result.get("summary", {}).get("is_healthy", False)
        )
        
        total_integrations = len(self.integration_results)
        healthy_integrations = sum(
            1 for result in self.integration_results.values()
            if result.get("summary", {}).get("is_healthy", False)
        )
        
        total_business_flows = len(self.business_flow_results)
        healthy_business_flows = sum(
            1 for result in self.business_flow_results.values()
            if result.get("summary", {}).get("is_healthy", False)
        )
        
        # Calculate total critical issues
        total_critical_issues = 0
        for result in list(self.component_results.values()) + list(self.integration_results.values()) + list(self.business_flow_results.values()):
            total_critical_issues += result.get("summary", {}).get("critical_issues", 0)
        
        # Calculate overall health score
        component_health_scores = [
            result.get("summary", {}).get("health_score", 0)
            for result in self.component_results.values()
        ]
        
        integration_health_scores = [
            result.get("summary", {}).get("health_score", 0)
            for result in self.integration_results.values()
        ]
        
        business_flow_health_scores = [
            result.get("summary", {}).get("health_score", 0)
            for result in self.business_flow_results.values()
        ]
        
        all_health_scores = component_health_scores + integration_health_scores + business_flow_health_scores
        overall_health_score = sum(all_health_scores) / len(all_health_scores) if all_health_scores else 0
        
        # Determine overall status
        if total_critical_issues == 0 and overall_health_score >= 90:
            overall_status = "healthy"
        elif total_critical_issues == 0 and overall_health_score >= 75:
            overall_status = "degraded"
        elif total_critical_issues <= 2 and overall_health_score >= 60:
            overall_status = "issues"
        else:
            overall_status = "unhealthy"
        
        # Compile comprehensive results
        verification_duration = (
            (self.verification_end_time - self.verification_start_time).total_seconds() * 1000
            if self.verification_end_time and self.verification_start_time else 0
        )
        
        return {
            "summary": {
                "overall_status": overall_status,
                "overall_health_score": overall_health_score,
                "total_components": total_components,
                "healthy_components": healthy_components,
                "total_integrations": total_integrations, 
                "healthy_integrations": healthy_integrations,
                "total_business_flows": total_business_flows,
                "healthy_business_flows": healthy_business_flows,
                "total_critical_issues": total_critical_issues,
                "component_health_rate": healthy_components / total_components if total_components > 0 else 0,
                "integration_health_rate": healthy_integrations / total_integrations if total_integrations > 0 else 1.0,
                "business_flow_health_rate": healthy_business_flows / total_business_flows if total_business_flows > 0 else 1.0,
                
                # Individual component health status
                "component_health": {
                    name: result.get("summary", {}).get("is_healthy", False)
                    for name, result in self.component_results.items()
                },
                "integration_health": {
                    name: result.get("summary", {}).get("is_healthy", False)
                    for name, result in self.integration_results.items()
                },
                "business_flow_health": {
                    name: result.get("summary", {}).get("is_healthy", False)
                    for name, result in self.business_flow_results.items()
                }
            },
            "execution_info": {
                "timestamp": compilation_time.isoformat(),
                "mode": self.config.mode.value,
                "parallel_execution": self.config.parallel_execution,
                "total_duration_ms": verification_duration,
                "start_time": self.verification_start_time.isoformat() if self.verification_start_time else None,
                "end_time": self.verification_end_time.isoformat() if self.verification_end_time else None
            },
            "component_results": self.component_results,
            "integration_results": self.integration_results,
            "business_flow_results": self.business_flow_results,
            "configuration": {
                "mode": self.config.mode.value,
                "enabled_components": self.config.get_enabled_components(),
                "performance_thresholds": {
                    name: config.performance_thresholds
                    for name, config in self.config.components.items()
                }
            }
        }
    
    async def run_quick_verification(self) -> Dict[str, Any]:
        """Run quick verification (essential checks only)"""
        quick_config = VerificationConfig.create_quick_config()
        original_config = self.config
        self.config = quick_config
        
        try:
            return await self.run_verification(VerificationMode.QUICK)
        finally:
            self.config = original_config
    
    async def run_production_verification(self) -> Dict[str, Any]:
        """Run production readiness verification"""
        production_config = VerificationConfig.create_production_config() 
        original_config = self.config
        self.config = production_config
        
        try:
            return await self.run_verification(VerificationMode.PRODUCTION)
        finally:
            self.config = original_config
    
    def get_production_readiness_decision(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Generate production readiness decision with evidence"""
        
        summary = results.get("summary", {})
        
        # Decision criteria
        criteria = {
            "no_critical_issues": summary.get("total_critical_issues", 1) == 0,
            "high_health_score": summary.get("overall_health_score", 0) >= 75,
            "core_components_healthy": all([
                summary.get("component_health", {}).get("postgresql", False),
                summary.get("component_health", {}).get("redis", False),
                summary.get("component_health", {}).get("backend_api", False)
            ]),
            "business_flows_working": summary.get("business_flow_health_rate", 0) >= 0.8,
            "integration_stable": summary.get("integration_health_rate", 0) >= 0.8
        }
        
        # Calculate decision score
        decision_score = sum(1 for criterion in criteria.values() if criterion) / len(criteria)
        
        # Make decision
        if decision_score >= 0.9:
            decision = "APPROVED"
            confidence = "HIGH"
            rationale = "All verification criteria met with high confidence"
        elif decision_score >= 0.8:
            decision = "APPROVED_WITH_MONITORING"
            confidence = "MEDIUM"
            rationale = "Most criteria met, enhanced monitoring recommended"
        elif decision_score >= 0.6:
            decision = "CONDITIONAL"
            confidence = "LOW" 
            rationale = "Some criteria met, resolve issues before deployment"
        else:
            decision = "REJECTED"
            confidence = "CRITICAL"
            rationale = "Multiple criteria failed, major issues must be resolved"
        
        return {
            "decision": decision,
            "confidence": confidence,
            "decision_score": decision_score,
            "rationale": rationale,
            "criteria_met": criteria,
            "criteria_score": f"{sum(1 for c in criteria.values() if c)}/{len(criteria)}",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "evidence": {
                "health_score": summary.get("overall_health_score", 0),
                "critical_issues": summary.get("total_critical_issues", 0),
                "component_health_rate": summary.get("component_health_rate", 0),
                "integration_health_rate": summary.get("integration_health_rate", 0),
                "business_flow_health_rate": summary.get("business_flow_health_rate", 0)
            }
        }


async def main():
    """Main entry point for verification framework"""
    
    import argparse
    
    parser = argparse.ArgumentParser(description="TalentForge Pro Business Flow Verification")
    parser.add_argument("--mode", choices=["quick", "standard", "deep", "production"], 
                       default="standard", help="Verification mode")
    parser.add_argument("--parallel", action="store_true", help="Run verifications in parallel")
    parser.add_argument("--no-reports", action="store_true", help="Skip report generation")
    parser.add_argument("--log-level", choices=["DEBUG", "INFO", "WARNING", "ERROR"], 
                       default="INFO", help="Logging level")
    
    args = parser.parse_args()
    
    # Create configuration
    config = VerificationConfig()
    config.set_mode(VerificationMode(args.mode))
    config.parallel_execution = args.parallel
    config.generate_reports = not args.no_reports
    config.log_level = args.log_level
    
    # Run verification
    runner = VerificationRunner(config)
    
    print(f"🚀 Starting TalentForge Pro verification (mode: {args.mode})")
    print(f"📊 Parallel execution: {args.parallel}")
    print(f"📝 Generate reports: {not args.no_reports}")
    print("=" * 60)
    
    results = await runner.run_verification()
    
    if results.get("verification_failed"):
        print(f"❌ Verification failed: {results.get('error')}")
        sys.exit(1)
    elif results.get("verification_aborted"):
        print(f"🛑 Verification aborted: {results.get('reason')}")
        sys.exit(1)
    else:
        # Print executive summary
        summary = results.get("summary", {})
        print("\\n" + "=" * 60)
        print("📋 VERIFICATION SUMMARY")
        print("=" * 60)
        print(f"Overall Status: {summary.get('overall_status', 'unknown').upper()}")
        print(f"Health Score: {summary.get('overall_health_score', 0):.1f}%")
        print(f"Components: {summary.get('healthy_components', 0)}/{summary.get('total_components', 0)} healthy")
        print(f"Critical Issues: {summary.get('total_critical_issues', 0)}")
        
        # Production readiness decision
        decision = runner.get_production_readiness_decision(results)
        print(f"\\n🎯 PRODUCTION READINESS: {decision['decision']} ({decision['confidence']} confidence)")
        print(f"📝 Rationale: {decision['rationale']}")
        
        if config.generate_reports:
            report_files = results.get("report_files", {})
            print(f"\\n📄 Reports generated:")
            for format_type, file_path in report_files.items():
                print(f"  - {format_type.upper()}: {file_path}")
        
        print("\\n✅ Verification completed successfully")
        
        # Exit code based on critical issues
        exit_code = 1 if summary.get('total_critical_issues', 0) > 0 else 0
        sys.exit(exit_code)


if __name__ == "__main__":
    asyncio.run(main())