"""
TalentForge Pro Business Flow Verification Framework

Comprehensive verification system for validating all critical integration points
and business flows without modifying production code.
"""

__version__ = "1.0.0"
__author__ = "TalentForge Pro Team"

# Import main verification components
from .main import VerificationRunner
from .config.settings import VerificationConfig
from .utils.report_generator import ReportGenerator

__all__ = [
    "VerificationRunner",
    "VerificationConfig", 
    "ReportGenerator"
]