"""
Test scenarios and test data definitions for verification framework
"""
from typing import Dict, List, Any
from dataclasses import dataclass


@dataclass
class TestScenario:
    """Definition of a test scenario"""
    name: str
    description: str
    test_data: Dict[str, Any]
    expected_outcome: Dict[str, Any]
    performance_requirements: Dict[str, Any]
    prerequisites: List[str]


class TestScenarios:
    """Test scenarios for different verification types"""
    
    @staticmethod
    def get_database_scenarios() -> List[TestScenario]:
        """Database verification test scenarios"""
        return [
            TestScenario(
                name="basic_connection",
                description="Test basic database connectivity",
                test_data={},
                expected_outcome={"connected": True},
                performance_requirements={"connection_time_ms": 500},
                prerequisites=["postgresql_running"]
            ),
            
            TestScenario(
                name="pgvector_functionality",
                description="Test pgvector extension functionality",
                test_data={
                    "test_vectors": [
                        [0.1, 0.2, 0.3] * 342,  # 1024-dimensional vector
                        [0.4, 0.5, 0.6] * 342,
                        [0.7, 0.8, 0.9] * 342
                    ]
                },
                expected_outcome={"similarity_search_works": True},
                performance_requirements={"vector_search_ms": 200},
                prerequisites=["postgresql_connected", "pgvector_installed"]
            ),
            
            TestScenario(
                name="transaction_integrity",
                description="Test database transaction integrity",
                test_data={"test_operations": 5},
                expected_outcome={"rollback_works": True, "commit_works": True},
                performance_requirements={"transaction_time_ms": 100},
                prerequisites=["postgresql_connected"]
            )
        ]
    
    @staticmethod
    def get_redis_scenarios() -> List[TestScenario]:
        """Redis cache verification test scenarios"""
        return [
            TestScenario(
                name="basic_operations",
                description="Test basic Redis operations (SET/GET/DEL)",
                test_data={"test_keys": ["test1", "test2", "test3"]},
                expected_outcome={"all_operations_work": True},
                performance_requirements={"operation_time_ms": 10},
                prerequisites=["redis_running"]
            ),
            
            TestScenario(
                name="cache_expiration", 
                description="Test cache TTL and expiration",
                test_data={"ttl_seconds": 2},
                expected_outcome={"expiration_works": True},
                performance_requirements={"check_time_ms": 5},
                prerequisites=["redis_connected"]
            ),
            
            TestScenario(
                name="monitoring_cache",
                description="Test monitoring-specific cache operations",
                test_data={
                    "system_health": {"status": "healthy", "services": 8},
                    "metrics": {"cpu": 45.5, "memory": 67.2}
                },
                expected_outcome={"monitoring_cache_works": True},
                performance_requirements={"cache_time_ms": 20},
                prerequisites=["redis_connected"]
            )
        ]
    
    @staticmethod
    def get_minio_scenarios() -> List[TestScenario]:
        """MinIO storage verification test scenarios"""
        return [
            TestScenario(
                name="bucket_operations",
                description="Test bucket creation and listing",
                test_data={"test_bucket": "verification-test"},
                expected_outcome={"bucket_operations_work": True},
                performance_requirements={"bucket_op_ms": 500},
                prerequisites=["minio_running"]
            ),
            
            TestScenario(
                name="file_operations",
                description="Test file upload, download, and deletion",
                test_data={
                    "test_files": [
                        {"name": "test.txt", "content": "Hello World", "content_type": "text/plain"},
                        {"name": "test.pdf", "size_kb": 100, "content_type": "application/pdf"}
                    ]
                },
                expected_outcome={"file_operations_work": True},
                performance_requirements={"upload_speed_mbps": 10, "download_speed_mbps": 50},
                prerequisites=["minio_connected", "bucket_exists"]
            ),
            
            TestScenario(
                name="version_management",
                description="Test file versioning functionality",
                test_data={"versions": 3},
                expected_outcome={"versioning_works": True},
                performance_requirements={"version_op_ms": 200},
                prerequisites=["minio_connected", "bucket_exists"]
            )
        ]
    
    @staticmethod
    def get_ai_service_scenarios() -> List[TestScenario]:
        """AI service verification test scenarios"""
        return [
            TestScenario(
                name="deepseek_connectivity",
                description="Test DeepSeek API connectivity and authentication",
                test_data={"test_prompt": "Say 'Hello' in JSON format"},
                expected_outcome={"api_works": True},
                performance_requirements={"response_time_ms": 5000},
                prerequisites=["internet_connection", "api_key_valid"]
            ),
            
            TestScenario(
                name="provider_fallback",
                description="Test AI provider fallback chain",
                test_data={
                    "providers": ["deepseek", "moonshot", "openrouter", "qwen"],
                    "fallback_test": True
                },
                expected_outcome={"fallback_chain_works": True},
                performance_requirements={"fallback_time_ms": 1000},
                prerequisites=["at_least_one_provider_available"]
            ),
            
            TestScenario(
                name="resume_parsing",
                description="Test resume parsing with AI",
                test_data={
                    "resume_text": "John Doe\\n Software Engineer\\n Python, FastAPI, React\\n 3 years experience"
                },
                expected_outcome={"parsed_successfully": True, "has_skills": True},
                performance_requirements={"parsing_time_ms": 10000},
                prerequisites=["ai_service_available"]
            )
        ]
    
    @staticmethod
    def get_ocr_scenarios() -> List[TestScenario]:
        """OCR service verification test scenarios"""
        return [
            TestScenario(
                name="ocr_installation",
                description="Test PaddleOCR installation and import",
                test_data={},
                expected_outcome={"paddleocr_available": True},
                performance_requirements={"import_time_ms": 3000},
                prerequisites=["python_environment"]
            ),
            
            TestScenario(
                name="gpu_detection",
                description="Test GPU availability for OCR",
                test_data={},
                expected_outcome={"gpu_status_known": True},
                performance_requirements={"detection_time_ms": 500},
                prerequisites=["paddleocr_available"]
            ),
            
            TestScenario(
                name="ocr_processing",
                description="Test OCR processing with sample image",
                test_data={
                    "test_image": "simple_text_image",
                    "expected_text": "Sample Resume Text"
                },
                expected_outcome={"text_extracted": True, "accuracy_acceptable": True},
                performance_requirements={"processing_time_ms": 10000},
                prerequisites=["paddleocr_available", "test_image_available"]
            )
        ]
    
    @staticmethod
    def get_integration_scenarios() -> List[TestScenario]:
        """Integration verification test scenarios"""
        return [
            TestScenario(
                name="api_database_integration",
                description="Test API to database integration",
                test_data={"api_endpoint": "/api/v1/health"},
                expected_outcome={"api_can_query_db": True},
                performance_requirements={"end_to_end_ms": 300},
                prerequisites=["backend_api_running", "postgresql_connected"]
            ),
            
            TestScenario(
                name="cache_invalidation",
                description="Test cache invalidation between API and Redis",
                test_data={"cache_keys": ["test_user_1", "test_stats"]},
                expected_outcome={"cache_sync_works": True},
                performance_requirements={"invalidation_ms": 50},
                prerequisites=["backend_api_running", "redis_connected"]
            ),
            
            TestScenario(
                name="async_task_processing",
                description="Test Celery async task processing",
                test_data={"task_type": "test_task", "task_data": {"test": True}},
                expected_outcome={"task_completed": True, "result_stored": True},
                performance_requirements={"task_execution_ms": 5000},
                prerequisites=["celery_worker_running", "redis_connected"]
            ),
            
            TestScenario(
                name="file_storage_integration",
                description="Test file upload API to MinIO storage",
                test_data={"file_size_kb": 100, "file_type": "application/pdf"},
                expected_outcome={"file_uploaded": True, "metadata_stored": True},
                performance_requirements={"upload_pipeline_ms": 2000},
                prerequisites=["backend_api_running", "minio_connected"]
            )
        ]
    
    @staticmethod
    def get_business_flow_scenarios() -> List[TestScenario]:
        """Complete business flow verification scenarios"""
        return [
            TestScenario(
                name="complete_resume_processing",
                description="End-to-end resume upload and processing pipeline",
                test_data={
                    "resume_file": "sample_resume.pdf",
                    "candidate_data": {
                        "name": "John Doe",
                        "email": "<EMAIL>",
                        "phone": "******-0123"
                    }
                },
                expected_outcome={
                    "file_uploaded": True,
                    "ocr_completed": True,
                    "ai_parsed": True,
                    "vector_generated": True,
                    "stored_in_db": True,
                    "searchable": True
                },
                performance_requirements={
                    "total_pipeline_ms": 30000,
                    "ocr_step_ms": 10000,
                    "ai_parsing_ms": 15000,
                    "vector_generation_ms": 3000,
                    "db_storage_ms": 1000
                },
                prerequisites=[
                    "all_services_running",
                    "test_resume_available",
                    "ai_service_available",
                    "ocr_service_available"
                ]
            ),
            
            TestScenario(
                name="candidate_job_matching",
                description="End-to-end candidate to job matching pipeline",
                test_data={
                    "candidate_id": "test_candidate_1",
                    "job_id": "test_job_1", 
                    "matching_criteria": {"min_score": 0.7}
                },
                expected_outcome={
                    "matching_completed": True,
                    "score_calculated": True,
                    "results_cached": True,
                    "results_accurate": True
                },
                performance_requirements={
                    "matching_time_ms": 5000,
                    "vector_search_ms": 500,
                    "score_calculation_ms": 100
                },
                prerequisites=[
                    "candidate_data_available",
                    "job_data_available", 
                    "vector_indexes_built"
                ]
            ),
            
            TestScenario(
                name="user_authentication_flow",
                description="Complete user authentication and authorization flow",
                test_data={
                    "test_user": {
                        "email": "<EMAIL>",
                        "password": "testpass123"
                    }
                },
                expected_outcome={
                    "login_successful": True,
                    "token_generated": True,
                    "permissions_loaded": True,
                    "access_granted": True
                },
                performance_requirements={
                    "auth_flow_ms": 500,
                    "token_validation_ms": 50,
                    "permission_check_ms": 20
                },
                prerequisites=["backend_api_running", "test_user_exists"]
            ),
            
            TestScenario(
                name="batch_processing_workflow", 
                description="Batch resume processing workflow",
                test_data={
                    "batch_size": 5,
                    "test_files": ["resume1.pdf", "resume2.pdf", "resume3.pdf", "resume4.pdf", "resume5.pdf"]
                },
                expected_outcome={
                    "all_processed": True,
                    "no_failures": True,
                    "results_consistent": True,
                    "performance_acceptable": True
                },
                performance_requirements={
                    "per_resume_avg_ms": 25000,
                    "batch_completion_ms": 150000,
                    "success_rate": 0.95
                },
                prerequisites=[
                    "celery_workers_running",
                    "ai_services_available",
                    "sufficient_test_files"
                ]
            )
        ]
    
    @staticmethod
    def get_load_test_scenarios() -> List[TestScenario]:
        """Load testing scenarios"""
        return [
            TestScenario(
                name="api_load_test",
                description="Load test API endpoints with concurrent requests",
                test_data={
                    "endpoints": [
                        {"url": "/api/v1/health", "method": "GET"},
                        {"url": "/api/v1/auth/me", "method": "GET", "auth_required": True},
                        {"url": "/api/v1/candidates/", "method": "GET", "auth_required": True}
                    ],
                    "concurrent_users": 10,
                    "duration_seconds": 30
                },
                expected_outcome={
                    "no_errors": True,
                    "response_times_acceptable": True,
                    "throughput_acceptable": True
                },
                performance_requirements={
                    "avg_response_ms": 200,
                    "p95_response_ms": 500,
                    "error_rate": 0.01
                },
                prerequisites=["backend_api_running", "test_auth_token"]
            ),
            
            TestScenario(
                name="vector_search_load_test",
                description="Load test vector search operations",
                test_data={
                    "search_queries": 50,
                    "concurrent_searches": 5,
                    "vector_dimension": 1024
                },
                expected_outcome={
                    "searches_completed": True,
                    "results_consistent": True,
                    "performance_maintained": True
                },
                performance_requirements={
                    "avg_search_ms": 200,
                    "memory_usage_mb": 500
                },
                prerequisites=["postgresql_connected", "vector_data_available"]
            )
        ]
    
    @staticmethod
    def get_error_scenarios() -> List[TestScenario]:
        """Error handling and recovery test scenarios"""
        return [
            TestScenario(
                name="database_connection_failure",
                description="Test behavior when database is unavailable",
                test_data={"simulate_db_down": True},
                expected_outcome={
                    "graceful_degradation": True,
                    "error_messages_clear": True,
                    "recovery_possible": True
                },
                performance_requirements={"error_response_ms": 100},
                prerequisites=["api_running"]
            ),
            
            TestScenario(
                name="ai_service_fallback",
                description="Test AI service fallback when primary provider fails",
                test_data={"primary_provider": "deepseek", "simulate_failure": True},
                expected_outcome={
                    "fallback_activated": True,
                    "processing_continued": True,
                    "quality_maintained": True
                },
                performance_requirements={"fallback_time_ms": 1000},
                prerequisites=["multiple_ai_providers_configured"]
            ),
            
            TestScenario(
                name="rate_limit_handling",
                description="Test rate limit handling and queuing",
                test_data={"requests_per_minute": 100},  # Exceed rate limits
                expected_outcome={
                    "rate_limiting_active": True,
                    "queue_functioning": True,
                    "no_data_loss": True
                },
                performance_requirements={"queue_processing_ms": 5000},
                prerequisites=["celery_workers_running"]
            )
        ]
    
    @staticmethod
    def get_all_scenarios() -> Dict[str, List[TestScenario]]:
        """Get all test scenarios organized by category"""
        return {
            "database": TestScenarios.get_database_scenarios(),
            "redis": TestScenarios.get_redis_scenarios(),
            "minio": TestScenarios.get_minio_scenarios(),
            "ai_services": TestScenarios.get_ai_service_scenarios(),
            "ocr": TestScenarios.get_ocr_scenarios(),
            "integration": TestScenarios.get_integration_scenarios(),
            "business_flow": TestScenarios.get_business_flow_scenarios(),
            "load_test": TestScenarios.get_load_test_scenarios(),
            "error_handling": TestScenarios.get_error_scenarios()
        }
    
    @staticmethod
    def get_scenarios_for_mode(mode: str) -> Dict[str, List[TestScenario]]:
        """Filter scenarios based on verification mode"""
        all_scenarios = TestScenarios.get_all_scenarios()
        
        if mode == "quick":
            # Only essential connectivity tests
            return {
                "database": all_scenarios["database"][:1],  # Basic connection only
                "redis": all_scenarios["redis"][:1],        # Basic operations only
                "minio": all_scenarios["minio"][:1],        # Bucket operations only
                "ai_services": all_scenarios["ai_services"][:1],  # Connectivity only
            }
        elif mode == "standard":
            # Core functionality tests
            return {
                "database": all_scenarios["database"],
                "redis": all_scenarios["redis"],
                "minio": all_scenarios["minio"],
                "ai_services": all_scenarios["ai_services"],
                "integration": all_scenarios["integration"][:2],  # Limited integration tests
                "business_flow": all_scenarios["business_flow"][:1]  # One business flow
            }
        elif mode == "deep":
            # All tests except load testing
            filtered = dict(all_scenarios)
            filtered.pop("load_test", None)  # Remove load tests for deep mode
            return filtered
        elif mode == "production":
            # All tests including load and stress testing
            return all_scenarios
        else:
            return all_scenarios