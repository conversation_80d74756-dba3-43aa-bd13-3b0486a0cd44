"""
Verification framework configuration settings
"""
import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum


class VerificationMode(Enum):
    """Different verification execution modes"""
    QUICK = "quick"           # Essential checks only (~5 min)
    STANDARD = "standard"     # Comprehensive checks (~15 min) 
    DEEP = "deep"            # Exhaustive checks (~30 min)
    PRODUCTION = "production" # Production readiness (~45 min)


@dataclass
class ComponentConfig:
    """Configuration for individual component verification"""
    name: str
    enabled: bool = True
    timeout_seconds: int = 30
    retry_attempts: int = 3
    skip_on_failure: bool = False
    performance_thresholds: Dict[str, Any] = field(default_factory=dict)
    custom_settings: Dict[str, Any] = field(default_factory=dict)


@dataclass 
class VerificationConfig:
    """Main configuration for verification framework"""
    
    # Execution settings
    mode: VerificationMode = VerificationMode.STANDARD
    parallel_execution: bool = True
    max_concurrent_verifiers: int = 5
    global_timeout_minutes: int = 30
    continue_on_failure: bool = True
    
    # Logging settings
    log_level: str = "INFO"
    log_to_file: bool = True
    log_file_path: str = "app/scripts/verification/reports/verification.log"
    detailed_logging: bool = True
    
    # Report settings
    generate_reports: bool = True
    report_directory: str = "app/scripts/verification/reports"
    include_evidence: bool = True
    include_recommendations: bool = True
    export_formats: List[str] = field(default_factory=lambda: ["json", "html", "md"])
    
    # Performance settings
    load_test_enabled: bool = True
    load_test_concurrency: int = 5
    load_test_duration_seconds: int = 30
    performance_baseline_file: str = "app/scripts/verification/data/benchmarks/baseline.json"
    
    # Component configurations
    components: Dict[str, ComponentConfig] = field(default_factory=dict)
    
    # Environment detection
    environment: str = field(default_factory=lambda: os.getenv("ENVIRONMENT", "development"))
    
    def __post_init__(self):
        """Initialize default component configurations"""
        if not self.components:
            self.components = self._get_default_component_configs()
    
    def _get_default_component_configs(self) -> Dict[str, ComponentConfig]:
        """Get default configuration for all components"""
        return {
            # Database components
            "postgresql": ComponentConfig(
                name="PostgreSQL Database",
                timeout_seconds=10,
                performance_thresholds={
                    "connection_time_ms": 500,
                    "query_time_ms": 100,
                    "max_connections": 100
                }
            ),
            
            "pgvector": ComponentConfig(
                name="pgvector Extension",
                timeout_seconds=15,
                performance_thresholds={
                    "vector_search_ms": 200,
                    "embedding_insert_ms": 50,
                    "index_build_ms": 5000
                }
            ),
            
            # Cache and queue components  
            "redis": ComponentConfig(
                name="Redis Cache",
                timeout_seconds=5,
                performance_thresholds={
                    "ping_time_ms": 10,
                    "set_operation_ms": 5,
                    "get_operation_ms": 5
                }
            ),
            
            "celery": ComponentConfig(
                name="Celery Task Queue", 
                timeout_seconds=30,
                performance_thresholds={
                    "task_dispatch_ms": 100,
                    "worker_response_ms": 5000,
                    "queue_size_limit": 1000
                }
            ),
            
            # Storage components
            "minio": ComponentConfig(
                name="MinIO Object Storage",
                timeout_seconds=10,
                performance_thresholds={
                    "upload_speed_mbps": 10,
                    "download_speed_mbps": 50,
                    "list_objects_ms": 500
                }
            ),
            
            # AI/ML components
            "ollama": ComponentConfig(
                name="Ollama Local LLM",
                timeout_seconds=20,
                performance_thresholds={
                    "embedding_generation_ms": 1000,
                    "model_load_time_ms": 10000,
                    "api_response_ms": 500
                }
            ),
            
            "ai_services": ComponentConfig(
                name="AI Services (DeepSeek/Moonshot/etc)",
                timeout_seconds=30,
                performance_thresholds={
                    "api_response_ms": 5000,
                    "fallback_time_ms": 1000,
                    "rate_limit_compliance": True
                }
            ),
            
            "ocr_service": ComponentConfig(
                name="OCR Service (PaddleOCR)",
                timeout_seconds=60,
                performance_thresholds={
                    "ocr_processing_ms": 10000,
                    "gpu_fallback_ms": 2000,
                    "accuracy_threshold": 0.8
                }
            ),
            
            # Application components
            "backend_api": ComponentConfig(
                name="Backend API",
                timeout_seconds=10,
                performance_thresholds={
                    "api_response_ms": 200,
                    "health_check_ms": 100,
                    "authentication_ms": 300
                }
            ),
            
            "frontend": ComponentConfig(
                name="Frontend Application",
                timeout_seconds=15,
                performance_thresholds={
                    "page_load_ms": 3000,
                    "api_call_ms": 1000,
                    "bundle_size_mb": 5
                }
            ),
            
            # Infrastructure components
            "nginx": ComponentConfig(
                name="Nginx Reverse Proxy",
                timeout_seconds=5,
                performance_thresholds={
                    "proxy_response_ms": 50,
                    "static_file_ms": 100,
                    "ssl_handshake_ms": 200
                }
            )
        }
    
    def get_component_config(self, component_name: str) -> Optional[ComponentConfig]:
        """Get configuration for a specific component"""
        return self.components.get(component_name)
    
    def update_component_config(self, component_name: str, config: ComponentConfig):
        """Update configuration for a component"""
        self.components[component_name] = config
    
    def get_enabled_components(self) -> List[str]:
        """Get list of enabled components for verification"""
        return [name for name, config in self.components.items() if config.enabled]
    
    def set_mode(self, mode: VerificationMode):
        """Set verification mode and adjust timeouts accordingly"""
        self.mode = mode
        
        # Adjust timeouts based on mode
        if mode == VerificationMode.QUICK:
            self.global_timeout_minutes = 5
            self.max_concurrent_verifiers = 10
            # Reduce individual component timeouts
            for config in self.components.values():
                config.timeout_seconds = max(5, config.timeout_seconds // 2)
                
        elif mode == VerificationMode.DEEP:
            self.global_timeout_minutes = 60
            self.max_concurrent_verifiers = 3
            # Increase individual component timeouts
            for config in self.components.values():
                config.timeout_seconds = config.timeout_seconds * 2
                
        elif mode == VerificationMode.PRODUCTION:
            self.global_timeout_minutes = 90
            self.max_concurrent_verifiers = 2
            # Maximum timeouts for thorough production testing
            for config in self.components.values():
                config.timeout_seconds = config.timeout_seconds * 3
    
    def get_performance_thresholds(self, component_name: str) -> Dict[str, Any]:
        """Get performance thresholds for a component"""
        component = self.get_component_config(component_name)
        return component.performance_thresholds if component else {}
    
    def is_production_mode(self) -> bool:
        """Check if running in production verification mode"""
        return self.mode == VerificationMode.PRODUCTION or self.environment == "production"
    
    @classmethod
    def create_quick_config(cls) -> 'VerificationConfig':
        """Create configuration for quick verification"""
        config = cls()
        config.set_mode(VerificationMode.QUICK)
        config.load_test_enabled = False
        config.detailed_logging = False
        return config
    
    @classmethod
    def create_production_config(cls) -> 'VerificationConfig':
        """Create configuration for production readiness verification"""
        config = cls()
        config.set_mode(VerificationMode.PRODUCTION)
        config.load_test_enabled = True
        config.load_test_duration_seconds = 60
        config.include_evidence = True
        config.include_recommendations = True
        return config