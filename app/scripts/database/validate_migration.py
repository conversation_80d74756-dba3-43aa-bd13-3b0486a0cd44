"""
Database migration validation script
Verifies that all tables, enums, and relationships exist correctly
"""
import asyncio
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent / "backend"))

from sqlalchemy import text, inspect
from app.core.database import async_engine


async def validate_extensions():
    """Validate that required PostgreSQL extensions are installed"""
    print("🔍 Validating PostgreSQL extensions...")
    
    required_extensions = ['vector', 'pg_trgm', 'uuid-ossp']
    
    async with async_engine.begin() as conn:
        for ext in required_extensions:
            try:
                result = await conn.execute(text(
                    f"SELECT * FROM pg_extension WHERE extname = '{ext}'"
                ))
                if result.fetchone():
                    print(f"  ✅ Extension '{ext}' is installed")
                else:
                    print(f"  ❌ Extension '{ext}' is NOT installed")
                    return False
            except Exception as e:
                print(f"  ❌ Error checking extension '{ext}': {e}")
                return False
    
    return True


async def validate_enums():
    """Validate that all required enum types exist"""
    print("🔍 Validating enum types...")
    
    required_enums = [
        'userrole', 'datapermission', 'educationlevel', 'experiencerange',
        'salaryrange', 'candidatesource', 'permission', 'candidatestatus',
        'approvalstatus', 'positionstatus', 'positionurgency', 'servicestatus',
        'questiontype', 'scoringmethod', 'questionnairecategory', 'questionnairestatus',
        'accesstype', 'activitytype', 'batchtaskstatus', 'batchtasktype'
    ]
    
    async with async_engine.begin() as conn:
        for enum_name in required_enums:
            try:
                result = await conn.execute(text(
                    f"SELECT typname FROM pg_type WHERE typname = '{enum_name}'"
                ))
                if result.fetchone():
                    print(f"  ✅ Enum '{enum_name}' exists")
                else:
                    print(f"  ❌ Enum '{enum_name}' is missing")
                    return False
            except Exception as e:
                print(f"  ❌ Error checking enum '{enum_name}': {e}")
                return False
    
    return True


async def validate_tables():
    """Validate that all required tables exist with correct structure"""
    print("🔍 Validating database tables...")
    
    required_tables = [
        'users', 'user_preferences', 'positions', 'candidates',
        'questionnaires', 'questionnaire_sections', 'questions',
        'questionnaire_responses', 'answers', 'monitoring_snapshots',
        'job_vectors', 'resume_vectors', 'resume_files', 'candidate_assessments'
    ]
    
    async with async_engine.begin() as conn:
        for table_name in required_tables:
            try:
                result = await conn.execute(text(
                    f"SELECT tablename FROM pg_tables WHERE tablename = '{table_name}'"
                ))
                if result.fetchone():
                    print(f"  ✅ Table '{table_name}' exists")
                else:
                    print(f"  ❌ Table '{table_name}' is missing")
                    return False
            except Exception as e:
                print(f"  ❌ Error checking table '{table_name}': {e}")
                return False
    
    return True


async def validate_foreign_keys():
    """Validate that key foreign key relationships exist"""
    print("🔍 Validating foreign key constraints...")
    
    key_relationships = [
        ('user_preferences', 'users', 'user_id'),
        ('positions', 'users', 'created_by'),
        ('candidates', 'users', 'created_by'),
        ('questionnaire_responses', 'questionnaires', 'questionnaire_id'),
        ('questionnaire_responses', 'candidates', 'candidate_id'),
        ('questions', 'questionnaires', 'questionnaire_id'),
        ('answers', 'questionnaire_responses', 'response_id'),
        ('job_vectors', 'positions', 'position_id'),
        ('resume_vectors', 'candidates', 'candidate_id'),
        ('candidate_assessments', 'candidates', 'candidate_id')
    ]
    
    async with async_engine.begin() as conn:
        for child_table, parent_table, foreign_key_col in key_relationships:
            try:
                result = await conn.execute(text(f"""
                    SELECT tc.constraint_name
                    FROM information_schema.table_constraints AS tc 
                    JOIN information_schema.key_column_usage AS kcu
                      ON tc.constraint_name = kcu.constraint_name
                      AND tc.table_schema = kcu.table_schema
                    JOIN information_schema.constraint_column_usage AS ccu
                      ON ccu.constraint_name = tc.constraint_name
                      AND ccu.table_schema = tc.table_schema
                    WHERE tc.constraint_type = 'FOREIGN KEY' 
                      AND tc.table_name = '{child_table}'
                      AND kcu.column_name = '{foreign_key_col}'
                      AND ccu.table_name = '{parent_table}'
                """))
                
                if result.fetchone():
                    print(f"  ✅ Foreign key: {child_table}.{foreign_key_col} -> {parent_table}")
                else:
                    print(f"  ❌ Missing FK: {child_table}.{foreign_key_col} -> {parent_table}")
                    return False
            except Exception as e:
                print(f"  ❌ Error checking FK {child_table}.{foreign_key_col}: {e}")
                return False
    
    return True


async def validate_indexes():
    """Validate that key indexes exist"""
    print("🔍 Validating critical indexes...")
    
    # Check some critical indexes
    critical_indexes = [
        ('users', 'idx_user_email'),
        ('users', 'idx_user_username'),
        ('candidates', 'idx_candidate_status'),
        ('positions', 'idx_position_status'),
        ('questionnaire_responses', 'idx_response_questionnaire'),
        ('job_vectors', 'idx_job_vector_position'),
        ('resume_vectors', 'idx_resume_vector_candidate')
    ]
    
    async with async_engine.begin() as conn:
        for table_name, index_name in critical_indexes:
            try:
                result = await conn.execute(text(f"""
                    SELECT indexname FROM pg_indexes 
                    WHERE tablename = '{table_name}' AND indexname = '{index_name}'
                """))
                
                if result.fetchone():
                    print(f"  ✅ Index '{index_name}' exists on '{table_name}'")
                else:
                    print(f"  ⚠️ Index '{index_name}' missing on '{table_name}'")
                    # Note: This is a warning, not a failure
            except Exception as e:
                print(f"  ❌ Error checking index '{index_name}': {e}")
    
    return True


async def validate_alembic_state():
    """Validate that Alembic migration state is correct"""
    print("🔍 Validating Alembic migration state...")
    
    async with async_engine.begin() as conn:
        try:
            # Check that alembic_version table exists
            result = await conn.execute(text(
                "SELECT tablename FROM pg_tables WHERE tablename = 'alembic_version'"
            ))
            
            if not result.fetchone():
                print("  ❌ alembic_version table does not exist")
                return False
            
            # Check current revision
            result = await conn.execute(text("SELECT version_num FROM alembic_version"))
            current_version = result.scalar()
            
            if current_version == '20250828000000':
                print(f"  ✅ Alembic head is at correct revision: {current_version}")
                return True
            else:
                print(f"  ❌ Alembic head is at wrong revision: {current_version}")
                print(f"      Expected: 20250828000000")
                return False
                
        except Exception as e:
            print(f"  ❌ Error checking Alembic state: {e}")
            return False


async def test_enum_usage():
    """Test that enums can be used in queries"""
    print("🔍 Testing enum functionality...")
    
    async with async_engine.begin() as conn:
        try:
            # Test creating and querying with enums
            await conn.execute(text("""
                INSERT INTO users (id, email, username, hashed_password, full_name, role, is_active)
                VALUES (1, '<EMAIL>', 'testuser', 'hashedpass', 'Test User', 'ADMIN', true)
                ON CONFLICT (id) DO UPDATE SET 
                    email = EXCLUDED.email,
                    username = EXCLUDED.username
            """))
            
            # Test querying with enum
            result = await conn.execute(text(
                "SELECT id, role FROM users WHERE role = 'ADMIN' LIMIT 1"
            ))
            
            row = result.fetchone()
            if row:
                print("  ✅ Enum queries work correctly")
                # Clean up test data
                await conn.execute(text("DELETE FROM users WHERE id = 1"))
                return True
            else:
                print("  ❌ Enum query failed")
                return False
                
        except Exception as e:
            print(f"  ❌ Error testing enum usage: {e}")
            return False


async def validate_model_imports():
    """Test that all SQLAlchemy models can be imported"""
    print("🔍 Validating SQLAlchemy model imports...")
    
    try:
        from app.models.user import User
        from app.models.candidate import Candidate  
        from app.models.position import Position
        from app.models.questionnaire import Questionnaire
        from app.models.questionnaire_response import QuestionnaireResponse
        from app.models.monitoring import MonitoringSnapshot
        print("  ✅ All critical models import successfully")
        return True
    except Exception as e:
        print(f"  ❌ Error importing models: {e}")
        return False


async def main():
    """Run all validation checks"""
    print("🚀 Starting database migration validation...\n")
    
    validation_functions = [
        ("PostgreSQL Extensions", validate_extensions),
        ("Enum Types", validate_enums),
        ("Database Tables", validate_tables),
        ("Foreign Key Constraints", validate_foreign_keys),
        ("Critical Indexes", validate_indexes),
        ("Alembic State", validate_alembic_state),
        ("Enum Functionality", test_enum_usage),
        ("Model Imports", validate_model_imports)
    ]
    
    all_passed = True
    
    for name, validation_func in validation_functions:
        print(f"\n{'='*50}")
        print(f"🧪 {name}")
        print(f"{'='*50}")
        
        try:
            if not await validation_func():
                all_passed = False
                print(f"❌ {name} validation FAILED")
            else:
                print(f"✅ {name} validation PASSED")
        except Exception as e:
            print(f"❌ {name} validation ERROR: {e}")
            all_passed = False
    
    print(f"\n{'='*50}")
    if all_passed:
        print("🎉 ALL VALIDATIONS PASSED!")
        print("✅ Database migration reset was successful.")
        print("✅ All tables, enums, and relationships are working correctly.")
        return 0
    else:
        print("❌ SOME VALIDATIONS FAILED!")
        print("⚠️  Please check the errors above and fix the issues.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)