-- TalentForge Pro Clear Seed Data Script
-- This script removes all development test data for clean state

-- Remove recruitment activities first (referencing candidates/positions)
DELETE FROM recruitment_activities WHERE id BETWEEN 900001 AND 900010;

-- Remove candidate assessments (referencing candidates)
DELETE FROM candidate_assessments WHERE assessor_id = 99999;

-- Remove test candidates
DELETE FROM candidates WHERE email LIKE '%@testdev.com' OR id BETWEEN 900000 AND 999999;

-- Remove test positions
DELETE FROM positions WHERE created_by = 99999 OR id BETWEEN 900000 AND 999999;

-- Reset sequences if needed (optional)
-- SELECT setval('candidates_id_seq', (SELECT MAX(id) FROM candidates WHERE id < 900000));
-- SELECT setval('positions_id_seq', (SELECT MAX(id) FROM positions WHERE id < 900000));
-- SELECT setval('candidate_assessments_id_seq', (SELECT MAX(id) FROM candidate_assessments WHERE id < 900000));
-- SELECT setval('recruitment_activities_id_seq', (SELECT MAX(id) FROM recruitment_activities WHERE id < 900000));

-- Verification
SELECT 'Test candidates removed:' as info, COUNT(*) as count FROM candidates WHERE email LIKE '%@testdev.com';
SELECT 'Test positions removed:' as info, COUNT(*) as count FROM positions WHERE created_by = 99999;
SELECT 'Test assessments removed:' as info, COUNT(*) as count FROM candidate_assessments WHERE assessor_id = 99999;
SELECT 'Test activities removed:' as info, COUNT(*) as count FROM recruitment_activities WHERE id BETWEEN 900001 AND 900010;

SELECT 'Seed data cleanup completed' as status, NOW() as cleaned_at;