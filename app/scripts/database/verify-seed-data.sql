-- TalentForge Pro Seed Data Verification Script
-- This script verifies that the development seed data has been correctly inserted

-- Verify candidates data
SELECT 
    'Test Candidates' as category,
    COUNT(*) as total_count,
    COUNT(CASE WHEN years_of_experience >= 7 THEN 1 END) as senior_count,
    COUNT(CASE WHEN years_of_experience BETWEEN 3 AND 6 THEN 1 END) as mid_count,
    COUNT(CASE WHEN years_of_experience <= 2 THEN 1 END) as junior_count,
    ROUND(AVG(years_of_experience), 1) as avg_experience
FROM candidates 
WHERE email LIKE '%@testdev.com';

-- Verify positions data
SELECT 
    'Test Positions' as category,
    COUNT(*) as total_count,
    COUNT(CASE WHEN urgency_level = 'critical' THEN 1 END) as critical_count,
    COUNT(CASE WHEN urgency_level = 'high' THEN 1 END) as high_count,
    COUNT(CASE WHEN urgency_level = 'medium' THEN 1 END) as medium_count,
    COUNT(CASE WHEN urgency_level = 'low' THEN 1 END) as low_count
FROM positions 
WHERE created_by = 99999;

-- Verify assessments data
SELECT 
    'Test Assessments' as category,
    COUNT(*) as total_count,
    ROUND(AVG(dci_score), 1) as avg_dci_score,
    COUNT(CASE WHEN dci_score >= 85 THEN 1 END) as high_performers,
    COUNT(CASE WHEN dci_score BETWEEN 70 AND 84 THEN 1 END) as average_performers,
    COUNT(CASE WHEN dci_score < 70 THEN 1 END) as developing_performers
FROM candidate_assessments 
WHERE assessor_id = 99999;

-- Verify five-dimensional scores distribution
SELECT 
    'Five-Dimensional Scores' as category,
    ROUND(AVG(digital_literacy_score), 1) as avg_digital_literacy,
    ROUND(AVG(industry_skills_score), 1) as avg_industry_skills,
    ROUND(AVG(position_skills_score), 1) as avg_position_skills,
    ROUND(AVG(innovation_score), 1) as avg_innovation,
    ROUND(AVG(learning_potential_score), 1) as avg_learning_potential
FROM candidate_assessments 
WHERE assessor_id = 99999;

-- Verify recruitment activities
SELECT 
    'Test Activities' as category,
    COUNT(*) as total_count,
    COUNT(CASE WHEN activity_type = 'candidate_added' THEN 1 END) as candidate_added_count,
    COUNT(CASE WHEN activity_type = 'assessment_completed' THEN 1 END) as assessment_completed_count,
    COUNT(CASE WHEN activity_type = 'matching_completed' THEN 1 END) as matching_completed_count,
    COUNT(CASE WHEN activity_type = 'position_posted' THEN 1 END) as position_posted_count
FROM recruitment_activities 
WHERE id BETWEEN 900001 AND 900010;

-- Verify data relationships
SELECT 
    'Data Relationships' as category,
    COUNT(DISTINCT ca.candidate_id) as candidates_with_assessments,
    COUNT(DISTINCT ra.candidate_id) as candidates_in_activities,
    COUNT(DISTINCT ra.position_id) as positions_in_activities
FROM candidate_assessments ca
LEFT JOIN recruitment_activities ra ON ca.candidate_id = ra.candidate_id
WHERE ca.assessor_id = 99999;

-- Verify data timeline distribution
SELECT 
    'Timeline Distribution' as category,
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '7 days' THEN 1 END) as last_week,
    COUNT(CASE WHEN created_at >= NOW() - INTERVAL '30 days' AND created_at < NOW() - INTERVAL '7 days' THEN 1 END) as last_month,
    COUNT(CASE WHEN created_at < NOW() - INTERVAL '30 days' THEN 1 END) as older
FROM candidates 
WHERE email LIKE '%@testdev.com';

-- Verify skills distribution
SELECT 
    'Skills Analysis' as category,
    COUNT(CASE WHEN skills LIKE '%React%' THEN 1 END) as react_skills,
    COUNT(CASE WHEN skills LIKE '%Python%' THEN 1 END) as python_skills,
    COUNT(CASE WHEN skills LIKE '%JavaScript%' THEN 1 END) as javascript_skills,
    COUNT(CASE WHEN skills LIKE '%TypeScript%' THEN 1 END) as typescript_skills,
    COUNT(CASE WHEN skills LIKE '%Management%' OR skills LIKE '%管理%' THEN 1 END) as management_skills
FROM candidates 
WHERE email LIKE '%@testdev.com';

-- Summary verification
SELECT 
    'Seed Data Summary' as info,
    'All verification checks completed' as status,
    NOW() as verified_at;