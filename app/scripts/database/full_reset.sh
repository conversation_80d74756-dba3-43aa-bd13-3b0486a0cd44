#!/bin/bash

# Full Database Migration Reset Script
# This script performs a complete database reset for development environment only

set -e  # Exit on any error

PROJECT_ROOT="/home/<USER>/source_code/talent_forge_pro"
SCRIPT_DIR="$PROJECT_ROOT/app/scripts/database"

echo "🚀 TalentForge Pro - Database Migration Reset"
echo "============================================="
echo ""

# Check if we're in development environment
if [ "${ENVIRONMENT:-development}" != "development" ]; then
    echo "❌ ERROR: This script is for DEVELOPMENT environment only!"
    echo "   Current environment: ${ENVIRONMENT:-unknown}"
    echo "   This script will NOT run in production!"
    exit 1
fi

echo "⚠️  WARNING: This will completely reset your development database!"
echo "   - All existing data will be LOST"
echo "   - All migration files will be removed"
echo "   - Database will be recreated from scratch"
echo ""

# Interactive confirmation (only if not in CI)
if [ -z "$CI" ] && [ -z "$SKIP_CONFIRMATION" ]; then
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "❌ Aborted by user"
        exit 1
    fi
fi

echo "🔧 Starting database migration reset process..."
echo ""

# Step 1: Stop services (if running)
echo "📍 Step 1: Stopping services..."
cd "$PROJECT_ROOT"
if command -v make &> /dev/null; then
    make down 2>/dev/null || echo "   Services were not running"
else
    echo "   Make command not found, skipping service stop"
fi
echo ""

# Step 2: Clean existing migration files
echo "📍 Step 2: Cleaning existing migration files..."
python3 "$SCRIPT_DIR/clean_migrations.py"
if [ $? -ne 0 ]; then
    echo "❌ Failed to clean migration files"
    exit 1
fi
echo ""

# Step 3: Start database service only
echo "📍 Step 3: Starting database service..."
cd "$PROJECT_ROOT"
if command -v make &> /dev/null; then
    # Start only postgres service and wait for it to be ready
    if make ps | grep -q "postgres"; then
        echo "   Database service already running"
    else
        echo "   Starting database service..."
        make up postgres
        sleep 10  # Wait for PostgreSQL to be ready
        
        # Wait for database to accept connections
        echo "   Waiting for database to be ready..."
        for i in {1..30}; do
            if make logs postgres 2>/dev/null | grep -q "database system is ready to accept connections"; then
                echo "   ✅ Database is ready"
                break
            fi
            if [ $i -eq 30 ]; then
                echo "   ❌ Timeout waiting for database to start"
                exit 1
            fi
            sleep 2
        done
    fi
else
    echo "   Make command not found, assuming database is running"
fi
echo ""

# Step 4: Reset database completely
echo "📍 Step 4: Resetting database schema..."
python3 "$SCRIPT_DIR/reset_migrations.py"
if [ $? -ne 0 ]; then
    echo "❌ Failed to reset database"
    exit 1
fi
echo ""

# Step 5: Apply new migration
echo "📍 Step 5: Applying consolidated migration..."
cd "$PROJECT_ROOT"
if command -v make &> /dev/null; then
    make db-migrate
    if [ $? -ne 0 ]; then
        echo "❌ Failed to apply migration"
        exit 1
    fi
else
    echo "   Make command not found, please run 'make db-migrate' manually"
fi
echo ""

# Step 6: Validate migration
echo "📍 Step 6: Validating migration success..."
python3 "$SCRIPT_DIR/validate_migration.py"
if [ $? -ne 0 ]; then
    echo "❌ Migration validation failed"
    echo "   Please check the errors above and run validation manually:"
    echo "   python3 $SCRIPT_DIR/validate_migration.py"
    exit 1
fi
echo ""

# Step 7: Start all services
echo "📍 Step 7: Starting all services..."
cd "$PROJECT_ROOT"
if command -v make &> /dev/null; then
    make up
    if [ $? -ne 0 ]; then
        echo "⚠️  Warning: Some services failed to start"
        echo "   Check service status with: make status"
        echo "   Check logs with: make logs"
    else
        echo "   ✅ All services started successfully"
    fi
else
    echo "   Make command not found, please start services manually"
fi
echo ""

# Final status check
echo "📍 Final Step: Checking system status..."
if command -v make &> /dev/null; then
    echo "Service Status:"
    make status || echo "   Could not check service status"
    echo ""
    
    echo "Database Migration Status:"
    if make db-current 2>/dev/null; then
        echo "   ✅ Migration state looks good"
    else
        echo "   ⚠️  Could not verify migration state"
    fi
else
    echo "   Make command not found, please check status manually"
fi

echo ""
echo "🎉 DATABASE MIGRATION RESET COMPLETED!"
echo "============================================="
echo "✅ Database schema has been completely reset"
echo "✅ New consolidated migration has been applied"
echo "✅ All services are running"
echo ""
echo "Next steps:"
echo "  - Access the application: http://localhost:8088"
echo "  - Check API health: http://localhost:8088/api/v1/health"
echo "  - View database docs: http://localhost:8088/api/docs"
echo ""
echo "If you encounter any issues:"
echo "  - Check logs: make logs"
echo "  - Validate migration: python3 $SCRIPT_DIR/validate_migration.py"
echo "  - Check service status: make status"
echo ""