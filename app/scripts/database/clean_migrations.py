"""
Clean existing migration files for reset
"""
import os
import shutil
from pathlib import Path


def clean_migration_files():
    """Remove all existing migration files except __init__.py"""
    migrations_dir = Path(__file__).parent.parent.parent / "backend" / "alembic" / "versions"
    
    print(f"🗂️ Cleaning migration files in: {migrations_dir}")
    
    if not migrations_dir.exists():
        print(f"❌ Migrations directory not found: {migrations_dir}")
        return
    
    # Keep track of removed files
    removed_count = 0
    
    for file_path in migrations_dir.glob("*.py"):
        if file_path.name != "__init__.py":
            try:
                file_path.unlink()
                print(f"  ✅ Removed: {file_path.name}")
                removed_count += 1
            except Exception as e:
                print(f"  ❌ Failed to remove {file_path.name}: {e}")
    
    # Also clean __pycache__ directories
    pycache_dir = migrations_dir / "__pycache__"
    if pycache_dir.exists():
        try:
            shutil.rmtree(pycache_dir)
            print(f"  ✅ Removed: __pycache__ directory")
        except Exception as e:
            print(f"  ❌ Failed to remove __pycache__: {e}")
    
    # Clean any .gitkeep files as we'll add our own migration
    gitkeep_file = migrations_dir / ".gitkeep"
    if gitkeep_file.exists():
        try:
            gitkeep_file.unlink()
            print(f"  ✅ Removed: .gitkeep file")
        except Exception as e:
            print(f"  ❌ Failed to remove .gitkeep: {e}")
    
    print(f"✅ Migration cleanup completed - removed {removed_count} files")


if __name__ == "__main__":
    clean_migration_files()