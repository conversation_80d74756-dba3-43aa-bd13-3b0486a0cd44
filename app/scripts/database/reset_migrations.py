"""
Database migration reset script for development environment
Handles complete database reset and migration cleanup
"""
import asyncio
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent.parent / "backend"))

from sqlalchemy import text
from sqlalchemy.exc import ProgrammingError
from app.core.config import settings
from app.core.database import AsyncSessionLocal, async_engine


async def reset_database_completely():
    """Reset database to clean state"""
    print("🗄️ Resetting database to clean state...")
    
    async with async_engine.begin() as conn:
        # Drop all tables in correct order (dependencies first)
        tables_to_drop = [
            'answers', 'questionnaire_responses', 'questions', 'questionnaire_sections',
            'questionnaires', 'application_submissions', 'application_forms',
            'candidate_assessments', 'resume_vectors', 'resume_files',
            'job_vectors', 'candidates', 'positions', 'web_applications',
            'monitoring_snapshots', 'user_preferences', 'roles', 'users',
            'alembic_version'
        ]
        
        for table in tables_to_drop:
            try:
                await conn.execute(text(f"DROP TABLE IF EXISTS {table} CASCADE"))
                print(f"  ✅ Dropped table: {table}")
            except Exception as e:
                print(f"  ⚠️ Could not drop {table}: {e}")
        
        # Drop all custom enum types
        enums_to_drop = [
            'positionstatus', 'positionurgency', 'candidatestatus', 'approvalstatus',
            'datapermission', 'userrole', 'educationlevel', 'experiencerange',
            'salaryrange', 'candidatesource', 'permission', 'servicestatus',
            'questiontype', 'scoringmethod', 'questionnairecategory', 
            'questionnairestatus', 'accesstype', 'activitytype', 'batchtaskstatus',
            'batchtasktype'
        ]
        
        for enum_type in enums_to_drop:
            try:
                await conn.execute(text(f"DROP TYPE IF EXISTS {enum_type} CASCADE"))
                print(f"  ✅ Dropped enum type: {enum_type}")
            except Exception as e:
                print(f"  ⚠️ Could not drop enum {enum_type}: {e}")
        
        # Drop sequences if they exist
        sequences_to_drop = [
            'users_id_seq', 'candidates_id_seq', 'positions_id_seq',
            'questionnaires_id_seq', 'questionnaire_questions_id_seq',
            'questionnaire_responses_id_seq', 'questions_id_seq',
            'answers_id_seq', 'questionnaire_sections_id_seq'
        ]
        
        for seq in sequences_to_drop:
            try:
                await conn.execute(text(f"DROP SEQUENCE IF EXISTS {seq} CASCADE"))
                print(f"  ✅ Dropped sequence: {seq}")
            except Exception as e:
                print(f"  ⚠️ Could not drop sequence {seq}: {e}")
        
        # Ensure pgvector extension exists for future migrations
        try:
            await conn.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            print(f"  ✅ Ensured vector extension exists")
        except Exception as e:
            print(f"  ⚠️ Could not ensure vector extension: {e}")
        
        # Ensure pg_trgm extension exists for full-text search
        try:
            await conn.execute(text("CREATE EXTENSION IF NOT EXISTS pg_trgm"))
            print(f"  ✅ Ensured pg_trgm extension exists")
        except Exception as e:
            print(f"  ⚠️ Could not ensure pg_trgm extension: {e}")
    
    print("✅ Database reset completed")


async def main():
    """Main reset function"""
    try:
        await reset_database_completely()
        print("\n🎉 Database reset successful! Run 'make db-migrate' to apply clean migrations.")
    except Exception as e:
        print(f"\n❌ Reset failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())