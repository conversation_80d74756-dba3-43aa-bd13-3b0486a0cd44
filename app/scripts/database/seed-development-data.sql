-- TalentForge Pro Development Data Seeding Script
-- This script creates comprehensive test data for immediate development and testing

-- Clear existing test data first
DELETE FROM candidate_assessments WHERE assessor_id = 99999;
DELETE FROM candidates WHERE email LIKE '%@testdev.com' OR id BETWEEN 900000 AND 999999;
DELETE FROM positions WHERE created_by = 99999 OR id BETWEEN 900000 AND 999999;

-- Insert 25 realistic test candidates
-- Technology Professionals (10 candidates)
INSERT INTO candidates (
    id, first_name, last_name, email, phone, current_position, 
    years_of_experience, location, skills, education_level,
    created_at, updated_at, is_deleted
) VALUES 
    -- Senior Technology Professionals
    (900001, '张', '三', '<EMAIL>', '+86-138-0013-8001', '高级前端工程师', 5, '北京市朝阳区', 'React,TypeScript,Next.js,Vue.js,JavaScript,Webpack,Jest', 'bachelor', NOW() - INTERVAL '45 days', NOW(), false),
    (900002, '李', '四', '<EMAIL>', '+86-138-0013-8002', '全栈开发工程师', 7, '上海市浦东新区', 'Python,Django,React,PostgreSQL,Docker,Redis,AWS', 'master', NOW() - INTERVAL '32 days', NOW(), false),
    (900003, '王', '五', '<EMAIL>', '+86-138-0013-8003', 'DevOps工程师', 4, '深圳市南山区', 'Kubernetes,Docker,AWS,CI/CD,Jenkins,Terraform,Ansible', 'bachelor', NOW() - INTERVAL '28 days', NOW(), false),
    (900004, '赵', '六', '<EMAIL>', '+86-138-0013-8004', 'AI算法工程师', 6, '杭州市西湖区', 'Python,TensorFlow,PyTorch,Machine Learning,Deep Learning,OpenCV', 'master', NOW() - INTERVAL '23 days', NOW(), false),
    (900005, '钱', '七', '<EMAIL>', '+86-138-0013-8005', '后端架构师', 8, '成都市武侯区', 'Java,Spring Boot,Microservices,Redis,MySQL,Kafka,RabbitMQ', 'bachelor', NOW() - INTERVAL '19 days', NOW(), false),
    (900006, '孙', '八', '<EMAIL>', '+86-138-0013-8006', '前端技术专家', 9, '广州市天河区', 'Angular,Vue.js,React,TypeScript,Webpack,Vite,Node.js', 'master', NOW() - INTERVAL '15 days', NOW(), false),
    (900007, '周', '九', '<EMAIL>', '+86-138-0013-8007', '数据工程师', 5, '南京市江宁区', 'Python,Spark,Hadoop,Kafka,Elasticsearch,ClickHouse,Airflow', 'bachelor', NOW() - INTERVAL '12 days', NOW(), false),
    (900008, '吴', '十', '<EMAIL>', '+86-138-0013-8008', '移动端开发工程师', 4, '武汉市光谷', 'React Native,Flutter,iOS,Android,Swift,Kotlin', 'bachelor', NOW() - INTERVAL '8 days', NOW(), false),
    (900009, '郑', '十一', '<EMAIL>', '+86-138-0013-8009', '云计算架构师', 10, '西安市高新区', 'AWS,Azure,Kubernetes,Terraform,Microservices,Istio', 'master', NOW() - INTERVAL '6 days', NOW(), false),
    (900010, '刘', '十二', '<EMAIL>', '+86-138-0013-8010', '网络安全工程师', 6, '青岛市市南区', 'Cybersecurity,Penetration Testing,CISSP,Firewall,SIEM,Splunk', 'bachelor', NOW() - INTERVAL '4 days', NOW(), false),

    -- Business & Management Professionals (10 candidates)
    (900011, '陈', '十三', '<EMAIL>', '+86-138-0013-8011', '产品经理', 6, '北京市海淀区', 'Product Management,User Research,Prototyping,Agile,Scrum', 'master', NOW() - INTERVAL '42 days', NOW(), false),
    (900012, '林', '十四', '<EMAIL>', '+86-138-0013-8012', '业务分析师', 4, '上海市徐汇区', 'Business Analysis,SQL,Power BI,Process Improvement,Requirements Analysis', 'bachelor', NOW() - INTERVAL '38 days', NOW(), false),
    (900013, '黄', '十五', '<EMAIL>', '+86-138-0013-8013', '项目经理', 7, '深圳市福田区', 'Project Management,PMP,Risk Management,Stakeholder Management', 'master', NOW() - INTERVAL '35 days', NOW(), false),
    (900014, '张', '十六', '<EMAIL>', '+86-138-0013-8014', '运营经理', 5, '广州市番禺区', 'Operations Management,Data Analysis,Process Optimization,Team Leadership', 'bachelor', NOW() - INTERVAL '31 days', NOW(), false),
    (900015, '李', '十七', '<EMAIL>', '+86-138-0013-8015', '市场总监', 8, '杭州市余杭区', 'Marketing Strategy,Digital Marketing,Brand Management,Market Research', 'master', NOW() - INTERVAL '27 days', NOW(), false),
    (900016, '王', '十八', '<EMAIL>', '+86-138-0013-8016', 'HR经理', 6, '成都市高新区', 'Human Resources,Talent Acquisition,Performance Management,Employee Relations', 'bachelor', NOW() - INTERVAL '24 days', NOW(), false),
    (900017, '赵', '十九', '<EMAIL>', '+86-138-0013-8017', '财务经理', 7, '南京市建邺区', 'Financial Analysis,Budget Planning,Financial Reporting,Cost Management', 'master', NOW() - INTERVAL '20 days', NOW(), false),
    (900018, '钱', '二十', '<EMAIL>', '+86-138-0013-8018', '质量经理', 5, '武汉市江汉区', 'Quality Assurance,Process Improvement,ISO Standards,Lean Six Sigma', 'bachelor', NOW() - INTERVAL '16 days', NOW(), false),
    (900019, '孙', '二一', '<EMAIL>', '+86-138-0013-8019', '销售总监', 9, '西安市雁塔区', 'Sales Management,Customer Relations,Revenue Growth,Team Leadership', 'bachelor', NOW() - INTERVAL '13 days', NOW(), false),
    (900020, '周', '二二', '<EMAIL>', '+86-138-0013-8020', '客服经理', 4, '青岛市崂山区', 'Customer Service,Team Management,Process Optimization,CRM Systems', 'bachelor', NOW() - INTERVAL '9 days', NOW(), false),

    -- Entry-Level Professionals (5 candidates)
    (900021, '吴', '二三', '<EMAIL>', '+86-138-0013-8021', '初级前端开发工程师', 1, '北京市昌平区', 'HTML,CSS,JavaScript,React,Git,基础算法', 'bachelor', NOW() - INTERVAL '7 days', NOW(), false),
    (900022, '郑', '二四', '<EMAIL>', '+86-138-0013-8022', '助理产品经理', 2, '上海市闵行区', 'Product Analysis,User Research,Prototyping,Figma,SQL', 'bachelor', NOW() - INTERVAL '5 days', NOW(), false),
    (900023, '刘', '二五', '<EMAIL>', '+86-138-0013-8023', '初级数据分析师', 1, '深圳市宝安区', 'Python,SQL,Excel,Power BI,Statistics,Data Visualization', 'master', NOW() - INTERVAL '3 days', NOW(), false),
    (900024, '陈', '二六', '<EMAIL>', '+86-138-0013-8024', '运营专员', 2, '杭州市拱墅区', 'Content Marketing,Social Media,Data Analysis,Customer Service', 'bachelor', NOW() - INTERVAL '2 days', NOW(), false),
    (900025, '林', '二七', '<EMAIL>', '+86-138-0013-8025', '测试工程师', 1, '成都市青羊区', 'Manual Testing,Automation Testing,Selenium,Python,Bug Tracking', 'bachelor', NOW() - INTERVAL '1 day', NOW(), false);

-- Insert 15 test positions across departments
INSERT INTO positions (
    id, title, department, description, requirements, salary_range,
    urgency_level, status, created_at, updated_at, is_deleted, created_by
) VALUES 
    -- Technology Positions (8 positions)
    (900001, '高级前端工程师', '技术部', '负责前端架构设计和开发，优化用户体验，技术方案制定', 'React,TypeScript,5年以上经验,前端架构设计', '25000-35000', 'high', 'active', NOW() - INTERVAL '50 days', NOW(), false, 99999),
    (900002, 'AI算法专家', '研发中心', '机器学习算法研究和应用，深度学习模型优化，算法工程化', 'Python,TensorFlow,博士优先,机器学习,深度学习', '35000-50000', 'critical', 'active', NOW() - INTERVAL '45 days', NOW(), false, 99999),
    (900003, 'DevOps工程师', '技术部', '自动化部署和运维，CI/CD流水线建设，云平台管理', 'Kubernetes,Docker,CI/CD,AWS,运维经验', '20000-30000', 'medium', 'active', NOW() - INTERVAL '40 days', NOW(), false, 99999),
    (900004, '全栈开发工程师', '产品部', '前后端全栈开发，系统架构设计，技术选型', 'React,Node.js,Python,数据库,全栈经验', '22000-32000', 'high', 'active', NOW() - INTERVAL '35 days', NOW(), false, 99999),
    (900005, '数据科学家', '数据部', '数据分析和建模，机器学习算法应用，业务洞察提供', 'Python,SQL,统计学,机器学习,数据挖掘', '30000-45000', 'medium', 'active', NOW() - INTERVAL '30 days', NOW(), false, 99999),
    (900006, '移动端技术负责人', '移动部', '移动端技术架构，团队技术管理，跨平台开发', 'React Native,Flutter,团队管理,移动端架构', '28000-40000', 'high', 'active', NOW() - INTERVAL '25 days', NOW(), false, 99999),
    (900007, '网络安全专家', '安全部', '网络安全防护和审计，安全体系建设，风险评估', 'Cybersecurity,CISSP认证,安全架构,渗透测试', '25000-38000', 'critical', 'active', NOW() - INTERVAL '20 days', NOW(), false, 99999),
    (900008, '云计算架构师', '基础架构部', '云平台架构设计，微服务架构，容器编排', 'AWS,Azure,Kubernetes,微服务,架构设计', '32000-48000', 'medium', 'active', NOW() - INTERVAL '15 days', NOW(), false, 99999),

    -- Business Positions (4 positions)
    (900009, '高级产品经理', '产品部', '产品战略规划，用户需求分析，产品生命周期管理', '产品管理,用户研究,数据分析,项目管理', '25000-38000', 'high', 'active', NOW() - INTERVAL '38 days', NOW(), false, 99999),
    (900010, '商业分析师', '战略部', '业务流程分析，数据驱动决策，商业洞察提供', 'Business Analysis,SQL,Power BI,流程分析', '18000-28000', 'medium', 'active', NOW() - INTERVAL '33 days', NOW(), false, 99999),
    (900011, '运营总监', '运营部', '运营体系建设，团队管理，业务增长策略', '运营管理,团队领导,数据分析,增长策略', '30000-45000', 'high', 'active', NOW() - INTERVAL '28 days', NOW(), false, 99999),
    (900012, '人力资源专家', 'HR部', '人才战略，招聘体系建设，组织发展', 'HR管理,招聘,组织发展,人才战略', '20000-30000', 'medium', 'active', NOW() - INTERVAL '22 days', NOW(), false, 99999),

    -- Entry-Level Positions (3 positions)
    (900013, '初级软件开发工程师', '技术部', '参与软件开发，代码编写，技术学习成长', '编程基础,学习能力,团队协作,计算机基础', '12000-18000', 'low', 'active', NOW() - INTERVAL '18 days', NOW(), false, 99999),
    (900014, '产品助理', '产品部', '产品需求整理，用户调研协助，数据分析支持', '产品思维,用户研究,数据分析,沟通能力', '10000-15000', 'low', 'active', NOW() - INTERVAL '12 days', NOW(), false, 99999),
    (900015, '市场专员', '市场部', '市场活动执行，内容创作，数据收集分析', '市场营销,内容创作,活动策划,数据分析', '8000-12000', 'medium', 'active', NOW() - INTERVAL '8 days', NOW(), false, 99999);

-- Insert candidate assessments with five-dimensional scores
-- High performers (top 20% - 5 candidates)
INSERT INTO candidate_assessments (
    id, candidate_id, 
    digital_literacy_score, industry_skills_score, position_skills_score, 
    innovation_score, learning_potential_score, 
    dci_score, assessed_at, assessor_id, assessment_data
) VALUES 
    -- Top Technology Performers
    (900001, 900001, 88.5, 85.2, 92.1, 78.3, 85.7, 87.2, NOW() - INTERVAL '10 days', 99999, '{"assessment_type": "comprehensive", "strengths": ["前端架构", "技术领导力"], "areas_for_improvement": ["后端技能"]}'),
    (900002, 900002, 91.2, 87.8, 89.5, 82.1, 88.3, 89.1, NOW() - INTERVAL '15 days', 99999, '{"assessment_type": "comprehensive", "strengths": ["全栈能力", "系统设计"], "areas_for_improvement": ["团队管理"]}'),
    (900003, 900004, 89.8, 88.5, 91.2, 85.2, 87.1, 88.9, NOW() - INTERVAL '18 days', 99999, '{"assessment_type": "comprehensive", "strengths": ["AI算法", "创新思维"], "areas_for_improvement": ["工程化能力"]}'),
    (900004, 900005, 86.2, 89.1, 88.8, 79.5, 86.4, 87.8, NOW() - INTERVAL '12 days', 99999, '{"assessment_type": "comprehensive", "strengths": ["后端架构", "系统设计"], "areas_for_improvement": ["前沿技术"]}'),
    (900005, 900009, 87.5, 86.3, 90.2, 81.7, 88.9, 87.6, NOW() - INTERVAL '8 days', 99999, '{"assessment_type": "comprehensive", "strengths": ["云计算", "架构设计"], "areas_for_improvement": ["业务理解"]}'),

    -- Average performers (middle 60% - 15 candidates)
    (900006, 900003, 75.3, 73.1, 78.9, 71.2, 76.8, 75.8, NOW() - INTERVAL '20 days', 99999, '{"assessment_type": "standard", "strengths": ["DevOps", "自动化"], "areas_for_improvement": ["架构设计", "创新思维"]}'),
    (900007, 900006, 77.8, 76.5, 79.2, 73.1, 78.3, 77.2, NOW() - INTERVAL '25 days', 99999, '{"assessment_type": "standard", "strengths": ["前端技术", "经验丰富"], "areas_for_improvement": ["新技术学习"]}'),
    (900008, 900007, 74.5, 75.8, 76.3, 70.8, 75.2, 75.1, NOW() - INTERVAL '22 days', 99999, '{"assessment_type": "standard", "strengths": ["数据处理", "工具使用"], "areas_for_improvement": ["业务理解", "沟通能力"]}'),
    (900009, 900008, 73.2, 74.1, 77.5, 72.3, 76.9, 75.3, NOW() - INTERVAL '28 days', 99999, '{"assessment_type": "standard", "strengths": ["移动开发", "跨平台"], "areas_for_improvement": ["架构能力", "团队协作"]}'),
    (900010, 900010, 76.1, 77.3, 78.8, 71.5, 74.8, 76.2, NOW() - INTERVAL '30 days', 99999, '{"assessment_type": "standard", "strengths": ["安全技术", "风险评估"], "areas_for_improvement": ["创新能力", "业务理解"]}'),
    (900011, 900011, 72.5, 78.2, 75.6, 74.8, 77.1, 75.8, NOW() - INTERVAL '35 days', 99999, '{"assessment_type": "standard", "strengths": ["产品思维", "用户研究"], "areas_for_improvement": ["技术理解", "数据分析"]}'),
    (900012, 900012, 71.8, 74.5, 76.2, 72.1, 75.8, 74.6, NOW() - INTERVAL '32 days', 99999, '{"assessment_type": "standard", "strengths": ["业务分析", "流程优化"], "areas_for_improvement": ["技术能力", "创新思维"]}'),
    (900013, 900013, 74.2, 76.8, 77.9, 73.5, 76.3, 76.1, NOW() - INTERVAL '40 days', 99999, '{"assessment_type": "standard", "strengths": ["项目管理", "团队协调"], "areas_for_improvement": ["技术深度", "战略思维"]}'),
    (900014, 900014, 73.1, 75.2, 74.8, 71.9, 74.5, 74.2, NOW() - INTERVAL '38 days', 99999, '{"assessment_type": "standard", "strengths": ["运营管理", "流程优化"], "areas_for_improvement": ["数字化技能", "创新能力"]}'),
    (900015, 900015, 75.8, 77.5, 76.1, 74.2, 77.8, 76.4, NOW() - INTERVAL '42 days', 99999, '{"assessment_type": "standard", "strengths": ["市场洞察", "品牌管理"], "areas_for_improvement": ["数字营销", "数据分析"]}'),
    (900016, 900016, 72.3, 76.1, 75.4, 71.8, 75.9, 74.8, NOW() - INTERVAL '45 days', 99999, '{"assessment_type": "standard", "strengths": ["人才管理", "组织发展"], "areas_for_improvement": ["数字化HR", "数据驱动"]}'),
    (900017, 900017, 74.6, 78.3, 77.2, 72.5, 76.1, 76.0, NOW() - INTERVAL '48 days', 99999, '{"assessment_type": "standard", "strengths": ["财务分析", "成本控制"], "areas_for_improvement": ["数字化工具", "战略财务"]}'),
    (900018, 900018, 73.8, 75.6, 76.8, 73.2, 75.4, 75.2, NOW() - INTERVAL '50 days', 99999, '{"assessment_type": "standard", "strengths": ["质量管理", "流程改进"], "areas_for_improvement": ["数字化质量", "创新方法"]}'),
    (900019, 900019, 74.5, 77.8, 75.9, 72.8, 76.2, 75.9, NOW() - INTERVAL '44 days', 99999, '{"assessment_type": "standard", "strengths": ["销售管理", "客户关系"], "areas_for_improvement": ["数字化销售", "数据分析"]}'),
    (900020, 900020, 71.2, 73.8, 74.5, 70.9, 74.1, 73.5, NOW() - INTERVAL '46 days', 99999, '{"assessment_type": "standard", "strengths": ["客服管理", "流程优化"], "areas_for_improvement": ["数字化服务", "数据洞察"]}'),

    -- Developing performers (bottom 20% - 5 candidates)
    (900021, 900021, 68.2, 65.8, 69.5, 64.1, 67.9, 67.5, NOW() - INTERVAL '5 days', 99999, '{"assessment_type": "entry_level", "strengths": ["学习能力", "基础扎实"], "areas_for_improvement": ["实战经验", "架构理解", "项目经验"]}'),
    (900022, 900022, 66.8, 67.2, 68.1, 65.5, 69.8, 67.8, NOW() - INTERVAL '7 days', 99999, '{"assessment_type": "entry_level", "strengths": ["产品思维", "用户理解"], "areas_for_improvement": ["数据分析", "技术理解", "项目管理"]}'),
    (900023, 900023, 69.1, 66.5, 67.8, 63.9, 68.5, 67.3, NOW() - INTERVAL '4 days', 99999, '{"assessment_type": "entry_level", "strengths": ["数据敏感", "分析思维"], "areas_for_improvement": ["业务理解", "工具熟练度", "沟通表达"]}'),
    (900024, 900024, 65.5, 68.3, 66.9, 64.7, 67.1, 66.8, NOW() - INTERVAL '6 days', 99999, '{"assessment_type": "entry_level", "strengths": ["运营思维", "执行能力"], "areas_for_improvement": ["数据分析", "策略思维", "创新能力"]}'),
    (900025, 900025, 67.3, 64.9, 68.8, 62.5, 66.8, 66.5, NOW() - INTERVAL '3 days', 99999, '{"assessment_type": "entry_level", "strengths": ["测试思维", "细致认真"], "areas_for_improvement": ["自动化技能", "编程能力", "系统思维"]}');

-- Create indexes for better query performance
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_candidates_test_data ON candidates(email) WHERE email LIKE '%@testdev.com';
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_positions_test_data ON positions(created_by) WHERE created_by = 99999;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_assessments_test_data ON candidate_assessments(assessor_id) WHERE assessor_id = 99999;

-- Insert recent recruitment activities for dashboard
INSERT INTO recruitment_activities (
    id, activity_type, title, description, candidate_id, position_id, 
    created_at, metadata
) VALUES
    (900001, 'candidate_added', '新增候选人：张三', '高级前端工程师张三已加入人才库', 900001, NULL, NOW() - INTERVAL '2 hours', '{"source": "内推", "priority": "high"}'),
    (900002, 'assessment_completed', '完成评估：李四', '全栈开发工程师李四完成五维能力评估', 900002, NULL, NOW() - INTERVAL '4 hours', '{"dci_score": 89.1, "assessment_type": "comprehensive"}'),
    (900003, 'matching_completed', '岗位匹配：AI算法专家', '为AI算法专家岗位匹配到3名候选人', NULL, 900002, NOW() - INTERVAL '6 hours', '{"matched_candidates": 3, "top_score": 88.9}'),
    (900004, 'position_posted', '新发布岗位：云计算架构师', '基础架构部发布云计算架构师职位', NULL, 900008, NOW() - INTERVAL '8 hours', '{"department": "基础架构部", "urgency": "medium"}'),
    (900005, 'candidate_added', '新增候选人：王五', 'DevOps工程师王五通过官网投递', 900003, NULL, NOW() - INTERVAL '12 hours', '{"source": "官网", "priority": "medium"}'),
    (900006, 'assessment_completed', '完成评估：赵六', 'AI算法工程师赵六完成技能评估', 900004, NULL, NOW() - INTERVAL '18 hours', '{"dci_score": 88.9, "assessment_type": "comprehensive"}'),
    (900007, 'matching_completed', '岗位匹配：高级前端工程师', '为高级前端工程师岗位匹配到5名候选人', NULL, 900001, NOW() - INTERVAL '1 day', '{"matched_candidates": 5, "top_score": 87.2}'),
    (900008, 'candidate_added', '新增候选人：钱七', '后端架构师钱七加入人才库', 900005, NULL, NOW() - INTERVAL '1.5 days', '{"source": "猎头", "priority": "high"}'),
    (900009, 'position_posted', '新发布岗位：数据科学家', '数据部发布数据科学家职位', NULL, 900005, NOW() - INTERVAL '2 days', '{"department": "数据部", "urgency": "medium"}'),
    (900010, 'assessment_completed', '完成评估：孙八', '前端技术专家孙八完成能力评估', 900006, NULL, NOW() - INTERVAL '3 days', '{"dci_score": 77.2, "assessment_type": "standard"}');

COMMIT;

-- Verification queries
SELECT 'Candidates inserted:' as info, COUNT(*) as count FROM candidates WHERE email LIKE '%@testdev.com';
SELECT 'Positions inserted:' as info, COUNT(*) as count FROM positions WHERE created_by = 99999;
SELECT 'Assessments inserted:' as info, COUNT(*) as count FROM candidate_assessments WHERE assessor_id = 99999;
SELECT 'Activities inserted:' as info, COUNT(*) as count FROM recruitment_activities WHERE id BETWEEN 900001 AND 900010;