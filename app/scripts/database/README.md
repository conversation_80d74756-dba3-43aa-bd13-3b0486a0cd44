# Database Seeding Scripts

This directory contains database seeding scripts for TalentForge Pro development and testing.

## Quick Start

To populate your development database with test data, run:

```bash
# From the project root
cd app/backend
docker exec -i hephaestus_postgres psql -U postgres -d talentforge < ../scripts/database/seed-development-data.sql
```

Alternatively, if you have access to the container directly:

```bash
# Copy and execute the seed script
make db-seed
```

## Scripts Overview

### `seed-development-data.sql`
**Purpose**: Creates comprehensive test data for immediate development use.

**Content**:
- **25 Test Candidates**: Realistic profiles across different experience levels
  - 10 Senior Technology Professionals (5-10 years experience)
  - 10 Business & Management Professionals (4-9 years experience) 
  - 5 Entry-Level Professionals (1-2 years experience)
- **15 Test Positions**: Diverse roles across departments and urgency levels
  - 8 Technology positions (Frontend, AI, DevOps, etc.)
  - 4 Business positions (Product Manager, Business Analyst, etc.)
  - 3 Entry-Level positions
- **Assessment Records**: Five-dimensional scores for all candidates
  - High performers: 80+ DCI scores (top 20%)
  - Average performers: 70-80 DCI scores (middle 60%)
  - Developing performers: <70 DCI scores (bottom 20%)
- **Activity Records**: Recent recruitment activities for dashboard

### `verify-seed-data.sql`
**Purpose**: Validates that seed data was inserted correctly.

**Checks**:
- Candidate count and distribution
- Position count and urgency levels
- Assessment score distributions
- Data relationships and constraints
- Timeline distribution for realistic dates

### `clear-seed-data.sql`
**Purpose**: Removes all test data for clean state.

**Actions**:
- Removes all test candidates (emails ending with @testdev.com)
- Removes all test positions (created_by = 99999)
- Removes all test assessments (assessor_id = 99999)
- Cleans up recruitment activities

## Data Characteristics

### Realistic Candidate Profiles
- **Names**: Chinese names following common patterns
- **Skills**: Technology stacks, business skills, domain expertise
- **Experience**: Distributed across junior (1-2y), mid (3-6y), senior (7+y)
- **Positions**: Current roles reflecting career progression
- **Education**: Mix of bachelor's and master's degrees

### Assessment Score Distribution
- **Five Dimensions**: Digital Literacy, Industry Skills, Position Skills, Innovation, Learning Potential
- **Weighted DCI Calculation**: 20%, 25%, 30%, 15%, 10% respectively
- **Realistic Ranges**: 
  - Digital Literacy: 65-95 points
  - Industry Skills: 60-90 points
  - Position Skills: 55-95 points
  - Innovation: 60-85 points
  - Learning Potential: 65-90 points

### Position Variety
- **Departments**: Technology, Research, Data, Security, Business, HR, Finance
- **Urgency Levels**: Critical (2), High (4), Medium (6), Low (3)
- **Salary Ranges**: Realistic for Chinese market (8K-50K RMB/month)
- **Requirements**: Detailed skill lists matching candidate profiles

## API Integration Points

The seed data is designed to work seamlessly with:

### Dashboard APIs
- `/api/v1/recruitment/dashboard/stats/` - Statistics use real candidate/assessment counts
- `/api/v1/recruitment/dashboard/trends/` - Trend data based on creation timestamps  
- `/api/v1/recruitment/dashboard/activities/` - Real recruitment activities

### Assessment APIs
- `/api/v1/assessment/generate` - Can generate new assessments for test candidates
- `/api/v1/assessments/candidate/{id}` - Retrieve existing assessments
- `/api/v1/assessments/statistics` - Real statistics from test data

### Candidate APIs
- `/api/v1/candidates/` - List includes assessment data
- Individual candidate endpoints work with test IDs

## Validation Checklist

After running the seed script, verify:

1. **Database Population**:
   ```sql
   -- Run verify-seed-data.sql
   SELECT * FROM candidates WHERE email LIKE '%@testdev.com'; -- Should return 25 rows
   SELECT * FROM positions WHERE created_by = 99999; -- Should return 15 rows
   SELECT * FROM candidate_assessments WHERE assessor_id = 99999; -- Should return 25 rows
   ```

2. **Frontend Integration**:
   - Dashboard page shows real statistics (not 0/0/0)
   - Recent activities display actual data
   - Assessment page lists candidates with scores
   - All data refreshes properly

3. **API Responses**:
   ```bash
   # Test dashboard stats
   curl -H "Authorization: Bearer dev_bypass_token_2025_talentforge" \
        http://localhost:8088/api/v1/recruitment/dashboard/stats/
   
   # Test assessment list
   curl -H "Authorization: Bearer dev_bypass_token_2025_talentforge" \
        http://localhost:8088/api/v1/candidates/?include_assessment=true
   ```

## Cleanup

To remove test data:

```bash
# Execute cleanup script
docker exec -i hephaestus_postgres psql -U postgres -d talentforge < ../scripts/database/clear-seed-data.sql
```

## Development Workflow

1. **Initial Setup**: Run `seed-development-data.sql` once
2. **Development**: Use test data for frontend development
3. **Testing**: Verify features work with realistic data
4. **Cleanup**: Run `clear-seed-data.sql` before production deployment

## Notes

- All test emails use `@testdev.com` domain for easy identification
- Test user ID `99999` is used as assessor/creator for easy cleanup
- Snowflake IDs start from 900000 to avoid conflicts
- Timestamps span the last 50 days for realistic timeline
- Skills and requirements are carefully matched for realistic matching results

This seed data provides a solid foundation for development and testing while ensuring the frontend displays realistic, meaningful information instead of empty states or mock data.