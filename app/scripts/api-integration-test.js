/**
 * API Integration Test Script
 * Tests critical API endpoints to verify fixes
 * 
 * Usage: node api-integration-test.js
 */

const axios = require('axios');

const API_BASE = process.env.API_BASE_URL || 'http://localhost:8088/api/v1';
const DEV_TOKEN = 'dev_bypass_token_2025_talentforge';

// Create axios instance with dev token
const api = axios.create({
  baseURL: API_BASE,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${DEV_TOKEN}`
  }
});

// Enhanced error logging
api.interceptors.response.use(
  response => response,
  error => {
    console.error(`❌ API Error:`, {
      url: error.config?.url,
      method: error.config?.method?.toUpperCase(),
      status: error.response?.status,
      error_code: error.response?.data?.error_code,
      detail: error.response?.data?.detail,
      message: error.response?.data?.message
    });
    return Promise.reject(error);
  }
);

// Test cases
const tests = [
  // Authentication & Health
  {
    name: '🔐 Auth - Get current user',
    method: 'GET',
    url: '/auth/me',
    expected: 200
  },
  {
    name: '❤️ Health Check',
    method: 'GET',
    url: '/health',
    expected: 200
  },

  // Dashboard Endpoints (Fixed URLs)
  {
    name: '📊 Dashboard - Get stats',
    method: 'GET',
    url: '/recruitment/dashboard/stats/',
    expected: 200
  },
  {
    name: '📈 Dashboard - Get trends',
    method: 'GET',
    url: '/recruitment/dashboard/trends/',
    expected: 200
  },
  {
    name: '⚡ Dashboard - Get activities',
    method: 'GET',
    url: '/recruitment/dashboard/activities/',
    expected: 200
  },

  // Assessment Endpoints (Fixed URLs)
  {
    name: '🎯 Assessment - Get statistics',
    method: 'GET',
    url: '/assessment/statistics',
    expected: 200
  },
  {
    name: '🧠 Assessment - Health check',
    method: 'GET',
    url: '/assessment/health',
    expected: 200
  },

  // Candidates Endpoints
  {
    name: '👥 Candidates - List',
    method: 'GET',
    url: '/candidates/',
    expected: 200
  },
  {
    name: '📈 Candidates - Stats',
    method: 'GET',
    url: '/candidates/stats',
    expected: 200
  },

  // Position Endpoints
  {
    name: '💼 Positions - List',
    method: 'GET',
    url: '/positions/',
    expected: 200
  }
];

// Test runner
async function runTests() {
  console.log('🚀 Starting API Integration Tests...\n');
  
  let passed = 0;
  let failed = 0;
  const results = [];

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);
      
      const startTime = Date.now();
      const response = await api({
        method: test.method,
        url: test.url,
        params: test.params || {}
      });
      const duration = Date.now() - startTime;

      if (response.status === test.expected) {
        console.log(`✅ PASS (${duration}ms) - ${response.status}\n`);
        passed++;
        results.push({
          ...test,
          status: 'PASS',
          responseStatus: response.status,
          duration,
          hasData: !!response.data
        });
      } else {
        console.log(`❌ FAIL - Expected ${test.expected}, got ${response.status}\n`);
        failed++;
        results.push({
          ...test,
          status: 'FAIL',
          responseStatus: response.status,
          duration,
          error: `Status mismatch: expected ${test.expected}, got ${response.status}`
        });
      }

      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
      console.log(`❌ FAIL - ${error.message}\n`);
      failed++;
      results.push({
        ...test,
        status: 'FAIL',
        error: error.message,
        errorCode: error.response?.data?.error_code,
        responseStatus: error.response?.status
      });
    }
  }

  // Summary
  console.log('📊 Test Results Summary:');
  console.log('='.repeat(50));
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / tests.length) * 100).toFixed(1)}%`);
  
  // Detailed results
  console.log('\n📋 Detailed Results:');
  console.log('='.repeat(50));
  
  results.forEach(result => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    const duration = result.duration ? `${result.duration}ms` : 'N/A';
    
    console.log(`${status} ${result.name}`);
    console.log(`   Status: ${result.responseStatus || 'N/A'} | Duration: ${duration}`);
    
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
    if (result.errorCode) {
      console.log(`   Error Code: ${result.errorCode}`);
    }
    console.log('');
  });

  // Critical issue analysis
  console.log('🔍 Critical Issue Analysis:');
  console.log('='.repeat(50));

  const criticalEndpoints = [
    '/recruitment/dashboard/stats/',
    '/recruitment/dashboard/trends/', 
    '/recruitment/dashboard/activities/',
    '/assessment/statistics'
  ];

  const criticalResults = results.filter(r => 
    criticalEndpoints.some(endpoint => r.url === endpoint)
  );

  const criticalPassed = criticalResults.filter(r => r.status === 'PASS').length;
  const criticalTotal = criticalResults.length;

  console.log(`Critical Endpoints: ${criticalPassed}/${criticalTotal} passed`);
  
  if (criticalPassed === criticalTotal) {
    console.log('✅ All critical API endpoints are working correctly!');
    console.log('✅ URL mismatch issues have been resolved');
    console.log('✅ Error handling is properly implemented');
  } else {
    console.log('❌ Some critical endpoints are still failing');
    console.log('❌ Review the detailed results above');
  }

  // Return success if at least 90% pass rate
  const successRate = (passed / tests.length) * 100;
  if (successRate >= 90) {
    console.log(`\n🎉 API Integration Test: SUCCESS (${successRate.toFixed(1)}% pass rate)`);
    process.exit(0);
  } else {
    console.log(`\n💥 API Integration Test: FAILED (${successRate.toFixed(1)}% pass rate)`);
    process.exit(1);
  }
}

// Error handling for the test runner
process.on('unhandledRejection', (error) => {
  console.error('💥 Unhandled promise rejection:', error);
  process.exit(1);
});

// Run tests
if (require.main === module) {
  runTests();
}

module.exports = { runTests };