#!/usr/bin/env python3
"""
Test script for OCR integration in resume parsing
"""
import asyncio
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.tasks import parse_resume_async
from app.services.resume_parser import resume_parsing_service
import base64
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ocr_integration():
    """Test OCR integration in resume parsing"""
    
    # Create a simple test PDF content (base64 encoded)
    # This is a minimal valid PDF with text "Test Resume Content"
    test_pdf_b64 = """JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCjIg
MCBvYmoKPDwKL1R5cGUgL1BhZ2VzCi9LaWRzIFszIDAgUl0KL0NvdW50IDEKPD4KZW5kb2JqCjMgMCBv
YmoKPDwKL1R5cGUgL1BhZ2UKL1BhcmVudCAyIDAgUgovTWVkaWFCb3ggWzAgMCA2MTIgNzkyXQovQ29u
dGVudHMgNCAwIFIKL1Jlc291cmNlcyA1IDAgUgo+PgplbmRvYmoKNCAwIG9iago8PAovTGVuZ3RoIDQ0
Cj4+CnN0cmVhbQpCVApxCjM2IDQ1MiBUZApxCi9GMSAxMiBUZgooVGVzdCBSZXN1bWUgQ29udGVudCkg
VGoKUQpRCmVuZHN0cmVhbQplbmRvYmoKNSAwIG9iago8PAovRm9udCA2IDAgUgo+PgplbmRvYmoKNiAw
IG9iago8PAovRjEgNyAwIFIKPj4KZW5kb2JqCjcgMCBvYmoKPDwKL1R5cGUgL0ZvbnQKL1N1YnR5cGUg
L1R5cGUxCi9CYXNlRm9udCAvSGVsdmV0aWNhCj4+CmVuZG9iagp4cmVmCjAgOAowMDAwMDAwMDAwIDY1
NTM1IGYgCjAwMDAwMDAwMDkgMDAwMDAgbiAKMDAwMDAwMDA1OCAwMDAwMCBuIAowMDAwMDAwMTE1IDAw
MDAwIG4gCjAwMDAwMDAyNDUgMDAwMDAgbiAKMDAwMDAwMDMzOSAwMDAwMCBuIAowMDAwMDAwMzc2IDAw
MDAwIG4gCjAwMDAwMDA0MDcgMDAwMDAgbiAKdHJhaWxlcgo8PAovU2l6ZSA4Ci9Sb290IDEgMCBSCj4+
CnN0YXJ0eHJlZgo0ODYKJSVFT0Y="""

    logger.info("Testing OCR integration with enable_ocr=True")
    
    # Test with OCR enabled
    try:
        # Simulate calling the Celery task directly (in production this would be async)
        task_result_ocr_enabled = parse_resume_async.apply(
            args=[
                test_pdf_b64,
                "test_resume_ocr.pdf", 
                1,  # user_id
                None,  # candidate_id
                False,  # generate_embeddings (skip for test)
                None,  # embedding_provider
                True   # enable_ocr
            ]
        )
        
        logger.info(f"OCR Enabled Result: {task_result_ocr_enabled.result}")
        
        # Test with OCR disabled
        logger.info("Testing OCR integration with enable_ocr=False")
        task_result_ocr_disabled = parse_resume_async.apply(
            args=[
                test_pdf_b64,
                "test_resume_no_ocr.pdf", 
                1,  # user_id
                None,  # candidate_id
                False,  # generate_embeddings
                None,  # embedding_provider
                False   # enable_ocr
            ]
        )
        
        logger.info(f"OCR Disabled Result: {task_result_ocr_disabled.result}")
        
        # Verify OCR flag is properly passed through
        ocr_enabled_result = task_result_ocr_enabled.result
        ocr_disabled_result = task_result_ocr_disabled.result
        
        if ocr_enabled_result and 'result' in ocr_enabled_result:
            parsing_result = ocr_enabled_result['result'].get('parsing_result', {})
            if parsing_result.get('ocr_enabled') == True:
                logger.info("✓ OCR enabled flag correctly passed to task")
            else:
                logger.error("✗ OCR enabled flag not found in result")
        
        if ocr_disabled_result and 'result' in ocr_disabled_result:
            parsing_result = ocr_disabled_result['result'].get('parsing_result', {})
            if parsing_result.get('ocr_enabled') == False:
                logger.info("✓ OCR disabled flag correctly passed to task")
            else:
                logger.error("✗ OCR disabled flag not correctly set")
                
        logger.info("OCR integration test completed successfully")
        
    except Exception as e:
        logger.error(f"OCR integration test failed: {str(e)}")
        raise

if __name__ == "__main__":
    print("Testing OCR Integration in Resume Parsing Tasks...")
    print("Note: This test requires a running backend with database and OCR service")
    asyncio.run(test_ocr_integration())