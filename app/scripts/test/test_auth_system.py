#!/usr/bin/env python3
"""
Comprehensive Authentication System Test Suite for TalentForge Pro

This test suite validates all authentication endpoints and functionality
as specified in the authentication system verification requirements.

Test Coverage:
- OAuth2 login endpoint (/auth/login)
- JSON login endpoint (/auth/login/access-token) 
- Current user endpoint (/auth/me)
- Token refresh endpoint (/auth/refresh)
- Logout endpoint (/auth/logout)
- Development bypass token functionality
- JWT token structure validation
- Error handling and security measures
"""
import asyncio
import sys
import time
from pathlib import Path
from typing import Dict, Any, Optional
import json
import httpx
from datetime import datetime, timedelta, timezone

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from app.core.config import settings
from app.core.security import decode_token, create_access_token, create_refresh_token
from app.core.database import AsyncSessionLocal
from app.crud.user import user as user_crud
from app.models.user import User
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AuthTestSuite:
    """Comprehensive authentication test suite"""
    
    def __init__(self, base_url: str = "http://localhost:8088"):
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
        self.test_results = {}
        self.performance_metrics = {}
        self.admin_credentials = {
            "username": "<EMAIL>",
            "password": "test123"
        }
        self.dev_bypass_token = "dev_bypass_token_2025_talentforge"
        
    async def setup_test_environment(self):
        """Setup test environment and verify prerequisites"""
        logger.info("🔧 Setting up test environment...")
        
        # Verify admin user exists
        async with AsyncSessionLocal() as db:
            admin_user = await user_crud.get_by_email(db, email=self.admin_credentials["username"])
            if not admin_user:
                logger.error("❌ Admin user not found. Run create_admin_user.py first.")
                return False
            
            if not admin_user.is_active:
                logger.error("❌ Admin user is not active")
                return False
                
            logger.info(f"✅ Admin user found: {admin_user.email}")
            return True
    
    async def test_oauth2_login(self) -> Dict[str, Any]:
        """Test OAuth2 compatible login endpoint"""
        test_name = "OAuth2 Login"
        logger.info(f"🧪 Testing {test_name}...")
        
        start_time = time.time()
        
        try:
            # Test successful login
            async with httpx.AsyncClient() as client:
                form_data = {
                    "username": self.admin_credentials["username"],
                    "password": self.admin_credentials["password"]
                }
                
                response = await client.post(
                    f"{self.api_base}/auth/login",
                    data=form_data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000  # Convert to ms
                
                if response.status_code == 200:
                    token_data = response.json()
                    
                    # Validate response structure
                    required_fields = ["access_token", "refresh_token", "token_type"]
                    missing_fields = [field for field in required_fields if field not in token_data]
                    
                    if missing_fields:
                        return {
                            "passed": False,
                            "error": f"Missing fields: {missing_fields}",
                            "response_time": response_time
                        }
                    
                    # Validate JWT token structure
                    try:
                        payload = decode_token(token_data["access_token"])
                        required_claims = ["sub", "type", "exp", "iat"]
                        missing_claims = [claim for claim in required_claims if claim not in payload]
                        
                        if missing_claims:
                            return {
                                "passed": False,
                                "error": f"Missing JWT claims: {missing_claims}",
                                "response_time": response_time
                            }
                        
                        if payload.get("type") != "access":
                            return {
                                "passed": False,
                                "error": f"Invalid token type: {payload.get('type')}",
                                "response_time": response_time
                            }
                        
                        logger.info(f"✅ {test_name} passed ({response_time:.2f}ms)")
                        return {
                            "passed": True,
                            "tokens": token_data,
                            "payload": payload,
                            "response_time": response_time
                        }
                        
                    except Exception as e:
                        return {
                            "passed": False,
                            "error": f"JWT validation failed: {str(e)}",
                            "response_time": response_time
                        }
                else:
                    return {
                        "passed": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "response_time": response_time
                    }
                    
        except Exception as e:
            return {
                "passed": False,
                "error": f"Request failed: {str(e)}",
                "response_time": 0
            }
    
    async def test_json_login(self) -> Dict[str, Any]:
        """Test JSON body login endpoint with remember_me option"""
        test_name = "JSON Login"
        logger.info(f"🧪 Testing {test_name}...")
        
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient() as client:
                json_data = {
                    "username": self.admin_credentials["username"],
                    "password": self.admin_credentials["password"],
                    "remember_me": True
                }
                
                response = await client.post(
                    f"{self.api_base}/auth/login/access-token",
                    json=json_data,
                    headers={"Content-Type": "application/json"}
                )
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                if response.status_code == 200:
                    token_data = response.json()
                    
                    # Validate remember_me functionality (longer token expiry)
                    try:
                        payload = decode_token(token_data["access_token"])
                        exp_time = datetime.fromtimestamp(payload["exp"], tz=timezone.utc)
                        now = datetime.now(timezone.utc)
                        time_diff = exp_time - now
                        
                        # With remember_me=True, should have 7 days expiry (approximately)
                        if time_diff.total_seconds() < 6 * 24 * 3600:  # Less than 6 days
                            logger.warning(f"⚠️  Remember me token expiry seems short: {time_diff}")
                        
                        logger.info(f"✅ {test_name} passed ({response_time:.2f}ms)")
                        logger.info(f"   Token expires in: {time_diff}")
                        return {
                            "passed": True,
                            "tokens": token_data,
                            "expiry_info": str(time_diff),
                            "response_time": response_time
                        }
                        
                    except Exception as e:
                        return {
                            "passed": False,
                            "error": f"Token validation failed: {str(e)}",
                            "response_time": response_time
                        }
                else:
                    return {
                        "passed": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "response_time": response_time
                    }
                    
        except Exception as e:
            return {
                "passed": False,
                "error": f"Request failed: {str(e)}",
                "response_time": 0
            }
    
    async def test_current_user_endpoint(self, access_token: str) -> Dict[str, Any]:
        """Test /auth/me endpoint with JWT token"""
        test_name = "Current User"
        logger.info(f"🧪 Testing {test_name}...")
        
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_base}/auth/me",
                    headers={"Authorization": f"Bearer {access_token}"}
                )
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                if response.status_code == 200:
                    user_data = response.json()
                    
                    # Validate user data structure
                    required_fields = ["id", "email", "username", "role", "permissions"]
                    missing_fields = [field for field in required_fields if field not in user_data]
                    
                    if missing_fields:
                        return {
                            "passed": False,
                            "error": f"Missing user fields: {missing_fields}",
                            "response_time": response_time
                        }
                    
                    # Validate admin user properties
                    if user_data["email"] != self.admin_credentials["username"]:
                        return {
                            "passed": False,
                            "error": f"Email mismatch: {user_data['email']}",
                            "response_time": response_time
                        }
                    
                    logger.info(f"✅ {test_name} passed ({response_time:.2f}ms)")
                    logger.info(f"   User ID: {user_data['id']}")
                    logger.info(f"   Role: {user_data['role']}")
                    logger.info(f"   Permissions: {len(user_data['permissions'])}")
                    return {
                        "passed": True,
                        "user_data": user_data,
                        "response_time": response_time
                    }
                else:
                    return {
                        "passed": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "response_time": response_time
                    }
                    
        except Exception as e:
            return {
                "passed": False,
                "error": f"Request failed: {str(e)}",
                "response_time": 0
            }
    
    async def test_token_refresh(self, refresh_token: str) -> Dict[str, Any]:
        """Test token refresh endpoint with both header and body methods"""
        test_name = "Token Refresh"
        logger.info(f"🧪 Testing {test_name}...")
        
        results = {}
        
        # Test 1: Header method (RFC 6750 compliant)
        start_time = time.time()
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_base}/auth/refresh",
                    headers={"Authorization": f"Bearer {refresh_token}"}
                )
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                if response.status_code == 200:
                    token_data = response.json()
                    if token_data.get("method_used") == "header":
                        logger.info(f"✅ {test_name} (Header) passed ({response_time:.2f}ms)")
                        results["header_method"] = {
                            "passed": True,
                            "tokens": token_data,
                            "response_time": response_time
                        }
                    else:
                        results["header_method"] = {
                            "passed": False,
                            "error": f"Wrong method reported: {token_data.get('method_used')}",
                            "response_time": response_time
                        }
                else:
                    results["header_method"] = {
                        "passed": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "response_time": response_time
                    }
        except Exception as e:
            results["header_method"] = {
                "passed": False,
                "error": f"Request failed: {str(e)}",
                "response_time": 0
            }
        
        # Test 2: Body method (legacy, should show deprecation warning)
        start_time = time.time()
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_base}/auth/refresh",
                    json={"refresh_token": refresh_token}
                )
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                if response.status_code == 200:
                    token_data = response.json()
                    if token_data.get("method_used") == "body":
                        logger.info(f"✅ {test_name} (Body/Deprecated) passed ({response_time:.2f}ms)")
                        if token_data.get("deprecation_warning"):
                            logger.info(f"   Deprecation warning received: ✓")
                        results["body_method"] = {
                            "passed": True,
                            "tokens": token_data,
                            "has_deprecation_warning": bool(token_data.get("deprecation_warning")),
                            "response_time": response_time
                        }
                    else:
                        results["body_method"] = {
                            "passed": False,
                            "error": f"Wrong method reported: {token_data.get('method_used')}",
                            "response_time": response_time
                        }
                else:
                    results["body_method"] = {
                        "passed": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "response_time": response_time
                    }
        except Exception as e:
            results["body_method"] = {
                "passed": False,
                "error": f"Request failed: {str(e)}",
                "response_time": 0
            }
        
        return results
    
    async def test_dev_bypass_token(self) -> Dict[str, Any]:
        """Test development bypass token functionality"""
        test_name = "Development Bypass Token"
        logger.info(f"🧪 Testing {test_name}...")
        
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    f"{self.api_base}/auth/me",
                    headers={"Authorization": f"Bearer {self.dev_bypass_token}"}
                )
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                if response.status_code == 200:
                    user_data = response.json()
                    
                    # Should return admin user data
                    if user_data["email"] == self.admin_credentials["username"]:
                        logger.info(f"✅ {test_name} passed ({response_time:.2f}ms)")
                        logger.info(f"   Mapped to admin user: {user_data['email']}")
                        return {
                            "passed": True,
                            "user_data": user_data,
                            "response_time": response_time
                        }
                    else:
                        return {
                            "passed": False,
                            "error": f"Dev token mapped to wrong user: {user_data['email']}",
                            "response_time": response_time
                        }
                else:
                    return {
                        "passed": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "response_time": response_time
                    }
                    
        except Exception as e:
            return {
                "passed": False,
                "error": f"Request failed: {str(e)}",
                "response_time": 0
            }
    
    async def test_logout_endpoint(self, access_token: str) -> Dict[str, Any]:
        """Test logout endpoint"""
        test_name = "Logout"
        logger.info(f"🧪 Testing {test_name}...")
        
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.api_base}/auth/logout",
                    headers={"Authorization": f"Bearer {access_token}"}
                )
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                if response.status_code == 200:
                    logout_data = response.json()
                    
                    if logout_data.get("success"):
                        logger.info(f"✅ {test_name} passed ({response_time:.2f}ms)")
                        return {
                            "passed": True,
                            "response_data": logout_data,
                            "response_time": response_time
                        }
                    else:
                        return {
                            "passed": False,
                            "error": "Success flag not set in response",
                            "response_time": response_time
                        }
                else:
                    return {
                        "passed": False,
                        "error": f"HTTP {response.status_code}: {response.text}",
                        "response_time": response_time
                    }
                    
        except Exception as e:
            return {
                "passed": False,
                "error": f"Request failed: {str(e)}",
                "response_time": 0
            }
    
    async def test_invalid_credentials(self) -> Dict[str, Any]:
        """Test error handling for invalid credentials"""
        test_name = "Invalid Credentials Handling"
        logger.info(f"🧪 Testing {test_name}...")
        
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient() as client:
                form_data = {
                    "username": "<EMAIL>",
                    "password": "wrongpassword"
                }
                
                response = await client.post(
                    f"{self.api_base}/auth/login",
                    data=form_data,
                    headers={"Content-Type": "application/x-www-form-urlencoded"}
                )
                
                end_time = time.time()
                response_time = (end_time - start_time) * 1000
                
                if response.status_code == 401:
                    error_data = response.json()
                    
                    # Check for proper error code
                    if error_data.get("error_code") == "AUTH_LOGIN_INVALID_CREDENTIALS":
                        logger.info(f"✅ {test_name} passed ({response_time:.2f}ms)")
                        logger.info(f"   Proper error code returned: {error_data.get('error_code')}")
                        return {
                            "passed": True,
                            "error_code": error_data.get("error_code"),
                            "response_time": response_time
                        }
                    else:
                        return {
                            "passed": False,
                            "error": f"Wrong error code: {error_data.get('error_code')}",
                            "response_time": response_time
                        }
                else:
                    return {
                        "passed": False,
                        "error": f"Expected 401, got {response.status_code}",
                        "response_time": response_time
                    }
                    
        except Exception as e:
            return {
                "passed": False,
                "error": f"Request failed: {str(e)}",
                "response_time": 0
            }
    
    async def test_jwt_performance(self) -> Dict[str, Any]:
        """Test JWT token generation and validation performance"""
        test_name = "JWT Performance"
        logger.info(f"🧪 Testing {test_name}...")
        
        results = {}
        
        # Test token generation performance
        generation_times = []
        for _ in range(10):
            start_time = time.time()
            token = create_access_token(subject="123456")
            end_time = time.time()
            generation_times.append((end_time - start_time) * 1000)
        
        avg_generation_time = sum(generation_times) / len(generation_times)
        results["token_generation"] = {
            "average_ms": avg_generation_time,
            "target_ms": 10,
            "passed": avg_generation_time < 10
        }
        
        # Test token validation performance
        test_token = create_access_token(subject="123456")
        validation_times = []
        for _ in range(10):
            start_time = time.time()
            try:
                decode_token(test_token)
            except:
                pass
            end_time = time.time()
            validation_times.append((end_time - start_time) * 1000)
        
        avg_validation_time = sum(validation_times) / len(validation_times)
        results["token_validation"] = {
            "average_ms": avg_validation_time,
            "target_ms": 5,
            "passed": avg_validation_time < 5
        }
        
        logger.info(f"✅ {test_name} completed")
        logger.info(f"   Token Generation: {avg_generation_time:.2f}ms (target: <10ms)")
        logger.info(f"   Token Validation: {avg_validation_time:.2f}ms (target: <5ms)")
        
        return results
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run complete authentication test suite"""
        logger.info("🚀 Starting Comprehensive Authentication Test Suite")
        logger.info("=" * 70)
        
        # Setup
        if not await self.setup_test_environment():
            return {"setup_failed": True}
        
        all_results = {}
        
        try:
            # Basic login tests
            oauth2_result = await self.test_oauth2_login()
            all_results["oauth2_login"] = oauth2_result
            
            json_result = await self.test_json_login()
            all_results["json_login"] = json_result
            
            # Get tokens for subsequent tests
            tokens = None
            if oauth2_result.get("passed"):
                tokens = oauth2_result["tokens"]
            elif json_result.get("passed"):
                tokens = json_result["tokens"]
            
            if tokens:
                # Test endpoints that require authentication
                current_user_result = await self.test_current_user_endpoint(tokens["access_token"])
                all_results["current_user"] = current_user_result
                
                refresh_result = await self.test_token_refresh(tokens["refresh_token"])
                all_results["token_refresh"] = refresh_result
                
                logout_result = await self.test_logout_endpoint(tokens["access_token"])
                all_results["logout"] = logout_result
            else:
                logger.error("❌ Cannot test authenticated endpoints - no valid tokens")
            
            # Test dev bypass token
            dev_token_result = await self.test_dev_bypass_token()
            all_results["dev_bypass_token"] = dev_token_result
            
            # Test error handling
            invalid_creds_result = await self.test_invalid_credentials()
            all_results["invalid_credentials"] = invalid_creds_result
            
            # Performance tests
            performance_result = await self.test_jwt_performance()
            all_results["jwt_performance"] = performance_result
            
        except Exception as e:
            logger.error(f"❌ Test suite failed with error: {e}")
            all_results["suite_error"] = str(e)
        
        return all_results
    
    def generate_test_report(self, results: Dict[str, Any]) -> str:
        """Generate comprehensive test report"""
        report_lines = []
        report_lines.append("=" * 70)
        report_lines.append("TALENTFORGE PRO - AUTHENTICATION TEST REPORT")
        report_lines.append("=" * 70)
        report_lines.append(f"Test Run: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        total_tests = 0
        passed_tests = 0
        
        # Count and analyze results
        for test_name, result in results.items():
            if test_name == "suite_error":
                continue
                
            if isinstance(result, dict):
                if "passed" in result:
                    total_tests += 1
                    if result["passed"]:
                        passed_tests += 1
                elif test_name == "token_refresh":
                    # Special handling for token refresh (has sub-tests)
                    for method, method_result in result.items():
                        if isinstance(method_result, dict) and "passed" in method_result:
                            total_tests += 1
                            if method_result["passed"]:
                                passed_tests += 1
                elif test_name == "jwt_performance":
                    # Special handling for performance tests
                    for perf_test, perf_result in result.items():
                        if isinstance(perf_result, dict) and "passed" in perf_result:
                            total_tests += 1
                            if perf_result["passed"]:
                                passed_tests += 1
        
        # Overall results
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        report_lines.append(f"OVERALL RESULTS: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        report_lines.append("")
        
        # Detailed results
        report_lines.append("DETAILED RESULTS:")
        report_lines.append("-" * 50)
        
        for test_name, result in results.items():
            if test_name == "suite_error":
                report_lines.append(f"❌ SUITE ERROR: {result}")
                continue
            
            test_display_name = test_name.replace("_", " ").title()
            
            if isinstance(result, dict):
                if "passed" in result:
                    status = "✅ PASSED" if result["passed"] else "❌ FAILED"
                    response_time = result.get("response_time", 0)
                    report_lines.append(f"{status} {test_display_name} ({response_time:.2f}ms)")
                    
                    if not result["passed"] and "error" in result:
                        report_lines.append(f"         Error: {result['error']}")
                
                elif test_name == "token_refresh":
                    report_lines.append(f"📝 {test_display_name}:")
                    for method, method_result in result.items():
                        if isinstance(method_result, dict) and "passed" in method_result:
                            method_name = method.replace("_", " ").title()
                            status = "✅ PASSED" if method_result["passed"] else "❌ FAILED"
                            response_time = method_result.get("response_time", 0)
                            report_lines.append(f"    {status} {method_name} ({response_time:.2f}ms)")
                            
                            if not method_result["passed"] and "error" in method_result:
                                report_lines.append(f"              Error: {method_result['error']}")
                
                elif test_name == "jwt_performance":
                    report_lines.append(f"📊 {test_display_name}:")
                    for perf_test, perf_result in result.items():
                        if isinstance(perf_result, dict):
                            perf_name = perf_test.replace("_", " ").title()
                            status = "✅ PASSED" if perf_result.get("passed") else "❌ FAILED"
                            avg_time = perf_result.get("average_ms", 0)
                            target_time = perf_result.get("target_ms", 0)
                            report_lines.append(f"    {status} {perf_name}: {avg_time:.2f}ms (target: <{target_time}ms)")
        
        report_lines.append("")
        report_lines.append("=" * 70)
        
        if success_rate >= 90:
            report_lines.append("🎉 AUTHENTICATION SYSTEM: HEALTHY")
        elif success_rate >= 70:
            report_lines.append("⚠️  AUTHENTICATION SYSTEM: NEEDS ATTENTION")
        else:
            report_lines.append("🚨 AUTHENTICATION SYSTEM: CRITICAL ISSUES")
        
        report_lines.append("=" * 70)
        
        return "\n".join(report_lines)


async def main():
    """Main test runner function"""
    test_suite = AuthTestSuite()
    
    # Run all tests
    results = await test_suite.run_all_tests()
    
    # Generate and display report
    report = test_suite.generate_test_report(results)
    print("\n" + report)
    
    # Save report to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = Path(__file__).parent / f"auth_test_report_{timestamp}.txt"
    report_file.write_text(report)
    logger.info(f"📄 Test report saved to: {report_file}")
    
    # Return success based on results
    total_tests = sum(1 for result in results.values() 
                     if isinstance(result, dict) and result.get("passed") is not None)
    passed_tests = sum(1 for result in results.values() 
                      if isinstance(result, dict) and result.get("passed") is True)
    
    success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
    return success_rate >= 90  # 90% pass rate required for success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)