#!/usr/bin/env python3
"""
Role-Based Access Control (RBAC) Validation Test Suite for TalentForge Pro

This test suite validates the RBAC system implementation including:
- Role permission mappings
- User role assignments
- Permission checking logic
- Access control enforcement
- Superuser privilege verification
- Permission inheritance and wildcards

Test Scenarios:
- Superuser access to all resources
- Admin role permissions
- HR Manager vs HR Specialist permissions
- Interviewer limited access
- Permission wildcard matching
- Cross-role permission scenarios
"""
import asyncio
import sys
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))

from app.core.database import AsyncSessionLocal
from app.crud.user import user as user_crud
from app.models.user import User
from app.core.enums import UserRole, ROLE_PERMISSIONS
from app.schemas.user import UserCreate
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class PermissionTestCase:
    """Test case for permission validation"""
    permission: str
    description: str
    should_have_access: bool
    test_resource: Optional[str] = None


@dataclass 
class RoleTestResult:
    """Result of role-based testing"""
    role: str
    total_tests: int
    passed_tests: int
    failed_tests: List[str]
    performance_ms: float
    security_score: float


class RBACTestSuite:
    """Comprehensive RBAC test suite"""
    
    def __init__(self):
        self.test_users = {}
        self.test_results = {}
        self.performance_metrics = {}
        
    async def setup_test_users(self) -> bool:
        """Create test users for each role if they don't exist"""
        logger.info("🔧 Setting up test users for RBAC testing...")
        
        test_user_configs = [
            {
                "role": UserRole.SUPER_ADMIN,
                "email": "<EMAIL>",
                "username": "test_superadmin",
                "password": "test123",
                "full_name": "Test Super Admin",
                "is_superuser": True
            },
            {
                "role": UserRole.ADMIN,
                "email": "<EMAIL>", 
                "username": "test_admin",
                "password": "test123",
                "full_name": "Test Admin",
                "is_superuser": False
            },
            {
                "role": UserRole.HR_MANAGER,
                "email": "<EMAIL>",
                "username": "test_hr_manager", 
                "password": "test123",
                "full_name": "Test HR Manager",
                "is_superuser": False
            },
            {
                "role": UserRole.HR_SPECIALIST,
                "email": "<EMAIL>",
                "username": "test_hr_specialist",
                "password": "test123", 
                "full_name": "Test HR Specialist",
                "is_superuser": False
            },
            {
                "role": UserRole.INTERVIEWER,
                "email": "<EMAIL>",
                "username": "test_interviewer",
                "password": "test123",
                "full_name": "Test Interviewer",
                "is_superuser": False
            }
        ]
        
        async with AsyncSessionLocal() as db:
            try:
                for config in test_user_configs:
                    # Check if user already exists
                    existing_user = await user_crud.get_by_email(db, email=config["email"])
                    
                    if existing_user:
                        # Update existing user to ensure correct role
                        existing_user.role = config["role"].value
                        existing_user.is_superuser = config["is_superuser"]
                        existing_user.is_active = True
                        existing_user.is_verified = True
                        await db.commit()
                        await db.refresh(existing_user)
                        self.test_users[config["role"]] = existing_user
                        logger.info(f"✅ Updated test user: {config['email']}")
                    else:
                        # Create new test user
                        user_create = UserCreate(
                            email=config["email"],
                            username=config["username"],
                            password=config["password"],
                            full_name=config["full_name"],
                            role=config["role"],
                            is_superuser=config["is_superuser"],
                            is_active=True
                        )
                        
                        new_user = await user_crud.create(db, obj_in=user_create)
                        new_user.is_verified = True  # Set verified after creation
                        await db.commit()
                        await db.refresh(new_user)
                        self.test_users[config["role"]] = new_user
                        logger.info(f"✅ Created test user: {config['email']}")
                
                logger.info(f"✅ Test users setup completed ({len(self.test_users)} users)")
                return True
                
            except Exception as e:
                logger.error(f"❌ Failed to setup test users: {e}")
                await db.rollback()
                return False
    
    def get_permission_test_cases(self, role: UserRole) -> List[PermissionTestCase]:
        """Get test cases for specific role"""
        
        # Common test permissions across the system
        base_permissions = [
            # User management permissions
            PermissionTestCase("users:read", "Read user information", False),
            PermissionTestCase("users:create", "Create new users", False), 
            PermissionTestCase("users:update", "Update user information", False),
            PermissionTestCase("users:delete", "Delete users", False),
            
            # Candidate management permissions
            PermissionTestCase("candidates:read", "Read candidate information", False),
            PermissionTestCase("candidates:create", "Create new candidates", False),
            PermissionTestCase("candidates:update", "Update candidate information", False),
            PermissionTestCase("candidates:delete", "Delete candidates", False),
            
            # Assessment permissions
            PermissionTestCase("assessments:read", "Read assessments", False),
            PermissionTestCase("assessments:create", "Create assessments", False),
            PermissionTestCase("assessments:update", "Update assessments", False), 
            PermissionTestCase("assessments:delete", "Delete assessments", False),
            
            # Position management permissions
            PermissionTestCase("positions:read", "Read position information", False),
            PermissionTestCase("positions:create", "Create new positions", False),
            PermissionTestCase("positions:update", "Update positions", False),
            PermissionTestCase("positions:delete", "Delete positions", False),
            
            # Report permissions
            PermissionTestCase("reports:read", "Read reports", False),
            PermissionTestCase("reports:create", "Generate reports", False),
            
            # System administration permissions
            PermissionTestCase("system:read", "Read system information", False),
            PermissionTestCase("system:update", "Update system settings", False),
            PermissionTestCase("system:admin", "System administration", False),
            
            # Wildcard permissions
            PermissionTestCase("candidates:*", "All candidate permissions", False),
            PermissionTestCase("*", "Global wildcard permission", False),
        ]
        
        # Set expected permissions based on role
        role_permissions = ROLE_PERMISSIONS.get(role, [])
        
        for test_case in base_permissions:
            # Check if role should have this permission
            if role == UserRole.SUPER_ADMIN:
                # Super admin should have all permissions via is_superuser flag
                test_case.should_have_access = True
            elif test_case.permission in role_permissions:
                test_case.should_have_access = True
            elif any(perm.endswith(":*") and test_case.permission.startswith(perm[:-1]) 
                    for perm in role_permissions):
                # Check wildcard permissions
                test_case.should_have_access = True
            elif "*" in role_permissions:
                # Global wildcard
                test_case.should_have_access = True
            else:
                test_case.should_have_access = False
        
        return base_permissions
    
    async def test_user_permissions(self, user: User, test_cases: List[PermissionTestCase]) -> RoleTestResult:
        """Test all permissions for a specific user"""
        logger.info(f"🧪 Testing permissions for role: {user.role}")
        
        start_time = time.perf_counter()
        passed_tests = 0
        failed_tests = []
        
        for test_case in test_cases:
            try:
                # Test permission check
                has_permission = user.has_permission(test_case.permission)
                
                if has_permission == test_case.should_have_access:
                    passed_tests += 1
                    status = "✅" if has_permission else "✅ (correctly denied)"
                    logger.debug(f"  {status} {test_case.permission}: {test_case.description}")
                else:
                    failed_tests.append(
                        f"{test_case.permission}: expected {test_case.should_have_access}, got {has_permission}"
                    )
                    status = "❌"
                    logger.warning(f"  {status} {test_case.permission}: {test_case.description}")
                    
            except Exception as e:
                failed_tests.append(f"{test_case.permission}: exception {str(e)}")
                logger.error(f"  ❌ {test_case.permission}: Exception - {str(e)}")
        
        end_time = time.perf_counter()
        performance_ms = (end_time - start_time) * 1000
        
        # Calculate security score
        security_score = self._calculate_role_security_score(user, passed_tests, len(test_cases))
        
        result = RoleTestResult(
            role=user.role,
            total_tests=len(test_cases),
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            performance_ms=performance_ms,
            security_score=security_score
        )
        
        logger.info(f"✅ Role {user.role}: {passed_tests}/{len(test_cases)} tests passed ({performance_ms:.2f}ms)")
        
        return result
    
    def _calculate_role_security_score(self, user: User, passed_tests: int, total_tests: int) -> float:
        """Calculate security score for role testing"""
        base_score = passed_tests / total_tests if total_tests > 0 else 0
        
        # Bonus for superuser having all permissions
        if user.is_superuser and passed_tests == total_tests:
            base_score = min(base_score + 0.1, 1.0)
        
        # Penalty for regular users having too many permissions
        if not user.is_superuser:
            user_permissions = user.permissions
            total_permissions = len(user_permissions)
            
            # More than 15 permissions for non-admin might be excessive
            if total_permissions > 15 and user.role not in [UserRole.ADMIN.value]:
                base_score = max(base_score - 0.1, 0.0)
        
        return base_score
    
    async def test_permission_inheritance(self) -> Dict[str, Any]:
        """Test permission inheritance and wildcard matching"""
        logger.info("🧪 Testing permission inheritance and wildcards...")
        
        test_results = {
            "wildcard_tests": [],
            "inheritance_tests": [],
            "overall_passed": False
        }
        
        # Test wildcard permissions
        if UserRole.ADMIN in self.test_users:
            admin_user = self.test_users[UserRole.ADMIN]
            
            # Test if users:* grants users:read, users:create, etc.
            wildcard_tests = [
                ("users:*", "users:read", "Wildcard should grant specific permission"),
                ("candidates:*", "candidates:update", "Candidate wildcard should grant update"),
                ("*", "any:permission", "Global wildcard should grant any permission")
            ]
            
            for wildcard_perm, specific_perm, description in wildcard_tests:
                # Check if admin has wildcard permission
                has_wildcard = wildcard_perm in admin_user.permissions
                has_specific = admin_user.has_permission(specific_perm)
                
                test_passed = not has_wildcard or has_specific  # If has wildcard, should have specific
                
                test_results["wildcard_tests"].append({
                    "wildcard": wildcard_perm,
                    "specific": specific_perm,
                    "description": description,
                    "passed": test_passed,
                    "has_wildcard": has_wildcard,
                    "has_specific": has_specific
                })
                
                status = "✅" if test_passed else "❌"
                logger.info(f"  {status} {description}")
        
        # Test role hierarchy (if implemented)
        hierarchy_tests = [
            (UserRole.SUPER_ADMIN, UserRole.ADMIN, "Super admin should have all admin permissions"),
            (UserRole.ADMIN, UserRole.HR_MANAGER, "Admin should have all HR manager permissions"),
            (UserRole.HR_MANAGER, UserRole.HR_SPECIALIST, "HR Manager should have all HR Specialist permissions")
        ]
        
        for higher_role, lower_role, description in hierarchy_tests:
            if higher_role in self.test_users and lower_role in self.test_users:
                higher_user = self.test_users[higher_role]
                lower_user = self.test_users[lower_role]
                
                # Check if higher role has all permissions of lower role
                lower_permissions = set(lower_user.permissions)
                higher_permissions = set(higher_user.permissions)
                
                # For superuser, they should have access to everything via is_superuser flag
                if higher_user.is_superuser:
                    has_all_lower = True
                else:
                    has_all_lower = lower_permissions.issubset(higher_permissions) or "*" in higher_permissions
                
                test_results["inheritance_tests"].append({
                    "higher_role": higher_role.value,
                    "lower_role": lower_role.value,
                    "description": description,
                    "passed": has_all_lower,
                    "missing_permissions": list(lower_permissions - higher_permissions) if not has_all_lower else []
                })
                
                status = "✅" if has_all_lower else "❌"
                logger.info(f"  {status} {description}")
        
        # Overall assessment
        all_wildcard_passed = all(test["passed"] for test in test_results["wildcard_tests"])
        all_inheritance_passed = all(test["passed"] for test in test_results["inheritance_tests"])
        test_results["overall_passed"] = all_wildcard_passed and all_inheritance_passed
        
        return test_results
    
    async def test_superuser_bypass(self) -> Dict[str, Any]:
        """Test superuser permission bypass functionality"""
        logger.info("🧪 Testing superuser permission bypass...")
        
        if UserRole.SUPER_ADMIN not in self.test_users:
            return {"error": "Super admin test user not found"}
        
        super_admin = self.test_users[UserRole.SUPER_ADMIN]
        
        # Test a variety of permissions that superuser should bypass
        test_permissions = [
            "some:random:permission",
            "non:existent:permission", 
            "system:critical:operation",
            "delete:everything:dangerous",
            "admin:only:function"
        ]
        
        results = {
            "tested_permissions": len(test_permissions),
            "granted_permissions": 0,
            "bypass_working": False,
            "details": []
        }
        
        for permission in test_permissions:
            has_access = super_admin.has_permission(permission)
            
            if has_access:
                results["granted_permissions"] += 1
            
            results["details"].append({
                "permission": permission,
                "granted": has_access
            })
            
            status = "✅" if has_access else "❌"
            logger.info(f"  {status} Superuser access to '{permission}': {has_access}")
        
        # Superuser should have access to all tested permissions
        results["bypass_working"] = results["granted_permissions"] == results["tested_permissions"]
        
        logger.info(f"✅ Superuser bypass: {results['granted_permissions']}/{results['tested_permissions']} permissions granted")
        
        return results
    
    async def run_comprehensive_rbac_tests(self) -> Dict[str, Any]:
        """Run all RBAC tests"""
        logger.info("🚀 Starting Comprehensive RBAC Test Suite")
        logger.info("=" * 60)
        
        # Setup test users
        setup_success = await self.setup_test_users()
        if not setup_success:
            return {"setup_failed": True}
        
        all_results = {
            "role_tests": {},
            "inheritance_tests": {},
            "superuser_tests": {},
            "overall_metrics": {}
        }
        
        try:
            # Test each role's permissions
            for role, user in self.test_users.items():
                test_cases = self.get_permission_test_cases(role)
                role_result = await self.test_user_permissions(user, test_cases)
                all_results["role_tests"][role.value] = role_result
            
            # Test permission inheritance
            inheritance_results = await self.test_permission_inheritance()
            all_results["inheritance_tests"] = inheritance_results
            
            # Test superuser bypass
            superuser_results = await self.test_superuser_bypass()
            all_results["superuser_tests"] = superuser_results
            
            # Calculate overall metrics
            total_tests = sum(result.total_tests for result in all_results["role_tests"].values())
            total_passed = sum(result.passed_tests for result in all_results["role_tests"].values())
            avg_performance = sum(result.performance_ms for result in all_results["role_tests"].values()) / len(all_results["role_tests"])
            avg_security_score = sum(result.security_score for result in all_results["role_tests"].values()) / len(all_results["role_tests"])
            
            all_results["overall_metrics"] = {
                "total_tests": total_tests,
                "total_passed": total_passed,
                "success_rate": (total_passed / total_tests * 100) if total_tests > 0 else 0,
                "average_performance_ms": avg_performance,
                "average_security_score": avg_security_score,
                "inheritance_passed": inheritance_results.get("overall_passed", False),
                "superuser_bypass_working": superuser_results.get("bypass_working", False)
            }
            
        except Exception as e:
            logger.error(f"❌ RBAC test suite failed: {e}")
            all_results["suite_error"] = str(e)
        
        return all_results
    
    def generate_rbac_report(self, results: Dict[str, Any]) -> str:
        """Generate comprehensive RBAC test report"""
        report_lines = []
        report_lines.append("=" * 70)
        report_lines.append("TALENTFORGE PRO - RBAC VALIDATION REPORT")
        report_lines.append("=" * 70)
        report_lines.append(f"Report Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        if "setup_failed" in results:
            report_lines.append("❌ SETUP FAILED - Could not create test users")
            return "\n".join(report_lines)
        
        if "suite_error" in results:
            report_lines.append(f"❌ SUITE ERROR: {results['suite_error']}")
            return "\n".join(report_lines)
        
        # Overall metrics
        metrics = results.get("overall_metrics", {})
        report_lines.append("OVERALL RESULTS:")
        report_lines.append("-" * 50)
        report_lines.append(f"Total Tests: {metrics.get('total_tests', 0)}")
        report_lines.append(f"Passed Tests: {metrics.get('total_passed', 0)}")
        report_lines.append(f"Success Rate: {metrics.get('success_rate', 0):.1f}%")
        report_lines.append(f"Average Performance: {metrics.get('average_performance_ms', 0):.2f}ms")
        report_lines.append(f"Average Security Score: {metrics.get('average_security_score', 0):.2f}/1.0")
        report_lines.append("")
        
        # Role-specific results
        report_lines.append("ROLE-BASED PERMISSION TESTS:")
        report_lines.append("-" * 50)
        
        role_tests = results.get("role_tests", {})
        for role_name, role_result in role_tests.items():
            success_rate = (role_result.passed_tests / role_result.total_tests * 100) if role_result.total_tests > 0 else 0
            status = "✅" if success_rate >= 90 else "⚠️" if success_rate >= 70 else "❌"
            
            report_lines.append(f"{status} {role_name.upper().replace('_', ' ')}")
            report_lines.append(f"     Tests: {role_result.passed_tests}/{role_result.total_tests} ({success_rate:.1f}%)")
            report_lines.append(f"     Performance: {role_result.performance_ms:.2f}ms")
            report_lines.append(f"     Security Score: {role_result.security_score:.2f}/1.0")
            
            if role_result.failed_tests:
                report_lines.append(f"     Failed Tests:")
                for failure in role_result.failed_tests[:3]:  # Show first 3 failures
                    report_lines.append(f"       - {failure}")
                if len(role_result.failed_tests) > 3:
                    report_lines.append(f"       ... and {len(role_result.failed_tests) - 3} more")
            
            report_lines.append("")
        
        # Inheritance tests
        inheritance_tests = results.get("inheritance_tests", {})
        if inheritance_tests:
            report_lines.append("PERMISSION INHERITANCE TESTS:")
            report_lines.append("-" * 50)
            
            overall_inheritance = inheritance_tests.get("overall_passed", False)
            status = "✅ PASSED" if overall_inheritance else "❌ FAILED"
            report_lines.append(f"Overall Inheritance: {status}")
            
            # Wildcard tests
            wildcard_tests = inheritance_tests.get("wildcard_tests", [])
            if wildcard_tests:
                report_lines.append("  Wildcard Permission Tests:")
                for test in wildcard_tests:
                    status = "✅" if test["passed"] else "❌"
                    report_lines.append(f"    {status} {test['description']}")
            
            # Hierarchy tests
            hierarchy_tests = inheritance_tests.get("inheritance_tests", [])
            if hierarchy_tests:
                report_lines.append("  Role Hierarchy Tests:")
                for test in hierarchy_tests:
                    status = "✅" if test["passed"] else "❌"
                    report_lines.append(f"    {status} {test['description']}")
                    if not test["passed"] and test.get("missing_permissions"):
                        report_lines.append(f"      Missing: {', '.join(test['missing_permissions'][:3])}")
            
            report_lines.append("")
        
        # Superuser tests
        superuser_tests = results.get("superuser_tests", {})
        if superuser_tests and "error" not in superuser_tests:
            report_lines.append("SUPERUSER BYPASS TESTS:")
            report_lines.append("-" * 50)
            
            bypass_working = superuser_tests.get("bypass_working", False)
            granted = superuser_tests.get("granted_permissions", 0)
            tested = superuser_tests.get("tested_permissions", 0)
            
            status = "✅ PASSED" if bypass_working else "❌ FAILED"
            report_lines.append(f"Superuser Bypass: {status} ({granted}/{tested} permissions granted)")
            report_lines.append("")
        
        # Security assessment
        report_lines.append("=" * 70)
        overall_success = metrics.get("success_rate", 0) >= 90
        inheritance_success = inheritance_tests.get("overall_passed", False)
        superuser_success = superuser_tests.get("bypass_working", False)
        
        if overall_success and inheritance_success and superuser_success:
            report_lines.append("🎉 RBAC SYSTEM: FULLY COMPLIANT")
        elif metrics.get("success_rate", 0) >= 70:
            report_lines.append("⚠️  RBAC SYSTEM: MOSTLY COMPLIANT - MINOR ISSUES")
        else:
            report_lines.append("🚨 RBAC SYSTEM: CRITICAL SECURITY ISSUES DETECTED")
        
        report_lines.append("=" * 70)
        
        return "\n".join(report_lines)


async def main():
    """Main function for RBAC testing"""
    rbac_suite = RBACTestSuite()
    
    # Run comprehensive RBAC tests
    results = await rbac_suite.run_comprehensive_rbac_tests()
    
    # Generate and display report
    report = rbac_suite.generate_rbac_report(results)
    print("\n" + report)
    
    # Save report to file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = Path(__file__).parent / f"rbac_test_report_{timestamp}.txt"
    report_file.write_text(report)
    logger.info(f"📄 RBAC test report saved to: {report_file}")
    
    # Determine success based on results
    if "overall_metrics" in results:
        success_rate = results["overall_metrics"].get("success_rate", 0)
        inheritance_passed = results.get("inheritance_tests", {}).get("overall_passed", False)
        superuser_working = results.get("superuser_tests", {}).get("bypass_working", False)
        
        success = success_rate >= 90 and inheritance_passed and superuser_working
        logger.info(f"🎯 Overall RBAC validation: {'✅ PASSED' if success else '❌ FAILED'}")
        return success
    
    return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)