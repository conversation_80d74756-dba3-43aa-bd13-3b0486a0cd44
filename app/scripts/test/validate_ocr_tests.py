#!/usr/bin/env python3
"""
OCR Test Validation Script

This script validates the OCR integration test suite and provides comprehensive reporting.
"""

import sys
import subprocess
import json
import time
from pathlib import Path
from typing import Dict, List, Any
import argparse


class OCRTestValidator:
    """Validates OCR test implementation and coverage"""
    
    def __init__(self, backend_path: Path = None):
        self.backend_path = backend_path or Path(__file__).parent.parent.parent / "backend"
        self.test_path = self.backend_path / "tests"
        
        # Test suite structure expectations
        self.expected_test_files = [
            "test_ocr_service.py",
            "test_resume_parser_ocr_integration.py", 
            "test_api_ocr_endpoints.py",
            "test_celery_ocr_tasks.py",
            "test_ocr_performance.py"
        ]
        
        self.expected_fixtures = [
            "fixtures/ocr_fixtures.py"
        ]
        
        self.expected_test_data = [
            "data/ocr/README.md"
        ]
        
        self.validation_results = {
            "file_structure": {},
            "test_discovery": {},
            "test_execution": {},
            "coverage": {},
            "performance": {},
            "overall_score": 0
        }
    
    def validate_file_structure(self) -> bool:
        """Validate test file structure"""
        print("📁 Validating OCR test file structure...")
        
        structure_valid = True
        
        # Check test files
        for test_file in self.expected_test_files:
            file_path = self.test_path / test_file
            exists = file_path.exists()
            self.validation_results["file_structure"][test_file] = {
                "exists": exists,
                "path": str(file_path)
            }
            if not exists:
                print(f"  ❌ Missing: {test_file}")
                structure_valid = False
            else:
                print(f"  ✅ Found: {test_file}")
        
        # Check fixtures
        for fixture_file in self.expected_fixtures:
            file_path = self.test_path / fixture_file
            exists = file_path.exists()
            self.validation_results["file_structure"][fixture_file] = {
                "exists": exists,
                "path": str(file_path)
            }
            if not exists:
                print(f"  ❌ Missing: {fixture_file}")
                structure_valid = False
            else:
                print(f"  ✅ Found: {fixture_file}")
        
        # Check test data
        for data_file in self.expected_test_data:
            file_path = self.test_path / data_file
            exists = file_path.exists()
            self.validation_results["file_structure"][data_file] = {
                "exists": exists,
                "path": str(file_path)
            }
            if not exists:
                print(f"  ⚠️  Missing: {data_file}")
            else:
                print(f"  ✅ Found: {data_file}")
        
        return structure_valid
    
    def validate_test_discovery(self) -> bool:
        """Validate that tests can be discovered by pytest"""
        print("\n🔍 Validating test discovery...")
        
        try:
            # Run pytest collection dry-run
            cmd = [
                "python", "-m", "pytest",
                "--collect-only", "-q",
                str(self.test_path / "test_ocr_service.py"),
                str(self.test_path / "test_resume_parser_ocr_integration.py"),
                str(self.test_path / "test_api_ocr_endpoints.py"),
                str(self.test_path / "test_celery_ocr_tasks.py"),
                str(self.test_path / "test_ocr_performance.py")
            ]
            
            result = subprocess.run(
                cmd,
                cwd=self.backend_path,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                # Parse test count from output
                output_lines = result.stdout.strip().split('\n')
                test_count_line = [line for line in output_lines if "test session starts" in line.lower()]
                
                # Count discovered tests
                test_count = 0
                for line in output_lines:
                    if "::" in line and "test_" in line:
                        test_count += 1
                
                self.validation_results["test_discovery"] = {
                    "success": True,
                    "test_count": test_count,
                    "output": result.stdout
                }
                
                print(f"  ✅ Discovered {test_count} OCR tests")
                return True
            else:
                self.validation_results["test_discovery"] = {
                    "success": False,
                    "error": result.stderr,
                    "output": result.stdout
                }
                print(f"  ❌ Test discovery failed:")
                print(f"     {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            print("  ❌ Test discovery timeout")
            return False
        except Exception as e:
            print(f"  ❌ Test discovery error: {e}")
            return False
    
    def run_smoke_tests(self) -> bool:
        """Run quick smoke tests to validate basic functionality"""
        print("\n💨 Running OCR smoke tests...")
        
        try:
            cmd = [
                "python", "-m", "pytest",
                "-k", "test_singleton_pattern or test_ocr_service_initialization or test_parse_resume_async_ocr_enabled",
                str(self.test_path),
                "-v", "--tb=short", "--maxfail=3",
                "--timeout=60"
            ]
            
            result = subprocess.run(
                cmd,
                cwd=self.backend_path,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            success = result.returncode == 0
            self.validation_results["test_execution"]["smoke_tests"] = {
                "success": success,
                "returncode": result.returncode,
                "output": result.stdout,
                "errors": result.stderr
            }
            
            if success:
                print("  ✅ Smoke tests passed")
            else:
                print("  ❌ Smoke tests failed:")
                print(f"     Return code: {result.returncode}")
                if result.stderr:
                    print(f"     Errors: {result.stderr[:500]}...")
            
            return success
            
        except subprocess.TimeoutExpired:
            print("  ❌ Smoke tests timeout")
            return False
        except Exception as e:
            print(f"  ❌ Smoke test error: {e}")
            return False
    
    def validate_test_coverage(self) -> Dict[str, Any]:
        """Validate test coverage for OCR components"""
        print("\n📊 Validating test coverage...")
        
        coverage_results = {
            "ocr_service": 0,
            "resume_parser": 0,
            "api_endpoints": 0,
            "celery_tasks": 0,
            "overall": 0
        }
        
        try:
            # Run tests with coverage
            cmd = [
                "python", "-m", "pytest",
                "--cov=app.services.ocr_service",
                "--cov=app.services.resume_parser",
                "--cov-report=json:coverage.json",
                "--cov-report=term-missing",
                str(self.test_path / "test_ocr_service.py"),
                str(self.test_path / "test_resume_parser_ocr_integration.py"),
                "-q", "--tb=no",
                "-m", "not performance"  # Skip performance tests for coverage
            ]
            
            result = subprocess.run(
                cmd,
                cwd=self.backend_path,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            # Parse coverage results
            coverage_file = self.backend_path / "coverage.json"
            if coverage_file.exists():
                with open(coverage_file) as f:
                    coverage_data = json.load(f)
                
                # Extract coverage percentages
                files = coverage_data.get("files", {})
                
                for file_path, file_data in files.items():
                    if "ocr_service" in file_path:
                        coverage_results["ocr_service"] = file_data.get("summary", {}).get("percent_covered", 0)
                    elif "resume_parser" in file_path:
                        coverage_results["resume_parser"] = file_data.get("summary", {}).get("percent_covered", 0)
                
                coverage_results["overall"] = coverage_data.get("totals", {}).get("percent_covered", 0)
                
                # Cleanup
                coverage_file.unlink()
            
            self.validation_results["coverage"] = coverage_results
            
            # Report results
            for component, coverage in coverage_results.items():
                status = "✅" if coverage >= 80 else "⚠️" if coverage >= 60 else "❌"
                print(f"  {status} {component}: {coverage:.1f}%")
            
            return coverage_results
            
        except subprocess.TimeoutExpired:
            print("  ❌ Coverage test timeout")
            return coverage_results
        except Exception as e:
            print(f"  ❌ Coverage validation error: {e}")
            return coverage_results
    
    def validate_performance_tests(self) -> bool:
        """Validate performance test structure"""
        print("\n⚡ Validating performance test structure...")
        
        try:
            # Check if performance tests exist and are properly marked
            cmd = [
                "python", "-m", "pytest",
                "--collect-only", "-q",
                "-m", "performance",
                str(self.test_path / "test_ocr_performance.py")
            ]
            
            result = subprocess.run(
                cmd,
                cwd=self.backend_path,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode == 0:
                # Count performance tests
                performance_test_count = result.stdout.count("test_")
                
                self.validation_results["performance"] = {
                    "test_count": performance_test_count,
                    "structure_valid": performance_test_count > 0
                }
                
                if performance_test_count > 0:
                    print(f"  ✅ Found {performance_test_count} performance tests")
                    return True
                else:
                    print("  ❌ No performance tests found")
                    return False
            else:
                print("  ❌ Performance test validation failed")
                return False
                
        except Exception as e:
            print(f"  ❌ Performance test validation error: {e}")
            return False
    
    def calculate_overall_score(self) -> int:
        """Calculate overall test suite quality score"""
        score = 0
        max_score = 100
        
        # File structure (20 points)
        structure_files = len([f for f in self.validation_results["file_structure"].values() if f.get("exists", False)])
        total_files = len(self.expected_test_files) + len(self.expected_fixtures)
        structure_score = (structure_files / total_files) * 20
        score += structure_score
        
        # Test discovery (20 points)
        if self.validation_results["test_discovery"].get("success", False):
            test_count = self.validation_results["test_discovery"].get("test_count", 0)
            if test_count >= 50:
                score += 20
            elif test_count >= 30:
                score += 15
            elif test_count >= 20:
                score += 10
            elif test_count > 0:
                score += 5
        
        # Test execution (20 points)
        if self.validation_results["test_execution"].get("smoke_tests", {}).get("success", False):
            score += 20
        
        # Coverage (25 points)
        overall_coverage = self.validation_results["coverage"].get("overall", 0)
        coverage_score = min(25, (overall_coverage / 100) * 25)
        score += coverage_score
        
        # Performance tests (15 points)
        if self.validation_results["performance"].get("structure_valid", False):
            perf_test_count = self.validation_results["performance"].get("test_count", 0)
            if perf_test_count >= 10:
                score += 15
            elif perf_test_count >= 5:
                score += 10
            elif perf_test_count > 0:
                score += 5
        
        return int(score)
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        overall_score = self.calculate_overall_score()
        self.validation_results["overall_score"] = overall_score
        
        print(f"\n📋 OCR Test Suite Validation Report")
        print(f"=" * 50)
        
        # Summary
        print(f"Overall Score: {overall_score}/100")
        
        if overall_score >= 95:
            grade = "A+"
            status = "🏆 Excellent"
        elif overall_score >= 90:
            grade = "A"
            status = "✅ Very Good"
        elif overall_score >= 80:
            grade = "B"
            status = "👍 Good"
        elif overall_score >= 70:
            grade = "C"
            status = "⚠️ Acceptable"
        else:
            grade = "F"
            status = "❌ Needs Improvement"
        
        print(f"Grade: {grade} - {status}")
        
        # Detailed breakdown
        print(f"\nDetailed Results:")
        print(f"- File Structure: {len([f for f in self.validation_results['file_structure'].values() if f.get('exists', False)])}/{len(self.expected_test_files) + len(self.expected_fixtures)} files")
        print(f"- Test Discovery: {self.validation_results['test_discovery'].get('test_count', 0)} tests discovered")
        print(f"- Smoke Tests: {'✅ Passed' if self.validation_results['test_execution'].get('smoke_tests', {}).get('success', False) else '❌ Failed'}")
        print(f"- Overall Coverage: {self.validation_results['coverage'].get('overall', 0):.1f}%")
        print(f"- Performance Tests: {self.validation_results['performance'].get('test_count', 0)} tests")
        
        # Recommendations
        print(f"\nRecommendations:")
        
        if overall_score < 95:
            recommendations = []
            
            # File structure recommendations
            missing_files = [f for f, info in self.validation_results['file_structure'].items() if not info.get('exists', False)]
            if missing_files:
                recommendations.append(f"📁 Create missing test files: {', '.join(missing_files)}")
            
            # Coverage recommendations
            if self.validation_results['coverage'].get('overall', 0) < 80:
                recommendations.append("📊 Improve test coverage to at least 80%")
            
            # Performance test recommendations
            if self.validation_results['performance'].get('test_count', 0) < 5:
                recommendations.append("⚡ Add more performance tests (target: 5+)")
            
            # Test discovery recommendations
            if self.validation_results['test_discovery'].get('test_count', 0) < 30:
                recommendations.append("🔍 Add more comprehensive test cases (target: 30+)")
            
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        
        if not recommendations:
            print("  🎉 Test suite meets all quality standards!")
        
        return self.validation_results
    
    def run_full_validation(self) -> int:
        """Run complete validation suite"""
        print("🚀 Starting OCR Test Suite Validation")
        print("=" * 50)
        
        # Run all validations
        structure_valid = self.validate_file_structure()
        discovery_valid = self.validate_test_discovery()
        smoke_valid = self.run_smoke_tests() if discovery_valid else False
        self.validate_test_coverage()
        self.validate_performance_tests()
        
        # Generate report
        self.generate_report()
        
        # Return appropriate exit code
        overall_score = self.validation_results["overall_score"]
        if overall_score >= 95:
            return 0  # Success
        elif overall_score >= 70:
            return 1  # Warning
        else:
            return 2  # Error


def main():
    """Main validation entry point"""
    parser = argparse.ArgumentParser(description="Validate OCR test suite implementation")
    
    parser.add_argument(
        "--backend-path",
        type=Path,
        help="Path to backend directory"
    )
    
    parser.add_argument(
        "--output-json",
        type=Path,
        help="Output validation results to JSON file"
    )
    
    parser.add_argument(
        "--min-score",
        type=int,
        default=70,
        help="Minimum acceptable score (default: 70)"
    )
    
    args = parser.parse_args()
    
    # Run validation
    validator = OCRTestValidator(args.backend_path)
    exit_code = validator.run_full_validation()
    
    # Save results to JSON if requested
    if args.output_json:
        with open(args.output_json, 'w') as f:
            json.dump(validator.validation_results, f, indent=2)
        print(f"\n📄 Results saved to: {args.output_json}")
    
    # Check minimum score
    actual_score = validator.validation_results["overall_score"]
    if actual_score < args.min_score:
        print(f"\n❌ Score {actual_score} below minimum {args.min_score}")
        return 2
    
    return exit_code


if __name__ == "__main__":
    sys.exit(main())