#!/usr/bin/env python3
"""
AI Provider Testing CLI Runner (Simple Version)
Real API testing for all 6 AI providers - no external dependencies required
"""
import argparse
import asyncio
import json
import os
import sys
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

import httpx

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../backend'))

from tests.ai_providers.test_comprehensive_provider_validation import ComprehensiveProviderValidator
from tests.ai_providers.base_provider_test import ProviderTestConfig, TestStatus


class SimpleAIProviderTestRunner:
    """
    Simple CLI test runner for AI provider validation (no external dependencies)
    """
    
    def __init__(self):
        self.validator = None
        self.results = {}
    
    def setup_validator(self, timeout: int = 30, max_retries: int = 2) -> None:
        """Setup test validator with configuration"""
        config = ProviderTestConfig(
            timeout=timeout,
            max_retries=max_retries,
            conversation_test_prompt="你好，你是谁？",
            expected_response_min_length=10
        )
        self.validator = ComprehensiveProviderValidator(config)
    
    async def check_api_connectivity(self, base_url: str = "http://localhost:8001") -> bool:
        """
        Check if backend API is reachable
        
        Args:
            base_url: Backend API base URL
            
        Returns:
            bool: True if API is reachable
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{base_url}/api/v1/health", timeout=5)
                return response.status_code == 200
        except Exception as e:
            print(f"❌ Cannot connect to API at {base_url}")
            print(f"   Error: {e}")
            return False
    
    def print_header(self, providers: List[str], test_types: List[str]) -> None:
        """Print test suite header"""
        print("\n" + "="*60)
        print("🤖 AI Provider Testing Suite")
        print("="*60)
        print(f"Testing {len(providers)} providers with real API calls")
        print(f"Providers: {', '.join(providers)}")
        print(f"Test Types: {', '.join(test_types)}")
        print(f"Timestamp: {datetime.now().isoformat()}")
        print("="*60)
    
    def print_provider_result(self, provider: str, models_result: Optional[Any], conversation_result: Optional[Any]) -> None:
        """Print individual provider test results"""
        # Determine overall status
        if models_result and models_result.status == TestStatus.SUCCESS:
            if conversation_result and conversation_result.status == TestStatus.SUCCESS:
                status_icon = "✅"
            elif conversation_result and conversation_result.status == TestStatus.FAILED:
                status_icon = "⚠️"
            else:
                status_icon = "⚠️"
        elif models_result and models_result.status == TestStatus.NOT_CONFIGURED:
            status_icon = "⚪"
        else:
            status_icon = "❌"
        
        print(f"\n{status_icon} {provider.upper()}")
        
        # Models test result
        if models_result:
            if models_result.status == TestStatus.SUCCESS:
                models_count = models_result.data.get('models_count', 0) if models_result.data else 0
                sample_models = models_result.data.get('sample_models', []) if models_result.data else []
                print(f"   └─ Models: ✅ {models_count} models found")
                if sample_models:
                    print(f"      Sample: {', '.join(sample_models[:3])}")
                if models_result.response_time_ms:
                    print(f"      Time: {models_result.response_time_ms:.0f}ms")
            elif models_result.status == TestStatus.NOT_CONFIGURED:
                print(f"   └─ Models: ⚪ Not configured")
            else:
                print(f"   └─ Models: ❌ {models_result.error[:60]}...")
        
        # Conversation test result  
        if conversation_result:
            if conversation_result.status == TestStatus.SUCCESS:
                response = conversation_result.data.get('response', '') if conversation_result.data else ''
                response_preview = response[:50] + '...' if len(response) > 50 else response
                print(f"   └─ Conversation: ✅ Response: \"{response_preview}\"")
                if conversation_result.response_time_ms:
                    print(f"      Time: {conversation_result.response_time_ms:.0f}ms")
            elif conversation_result.status == TestStatus.SKIPPED:
                print(f"   └─ Conversation: ⏸️ Skipped (models unavailable)")
            elif conversation_result.status == TestStatus.NOT_CONFIGURED:
                print(f"   └─ Conversation: ⚪ Not configured")
            else:
                print(f"   └─ Conversation: ❌ {conversation_result.error[:60]}...")
    
    def print_summary(self, results: Dict[str, Any]) -> None:
        """Print test suite summary"""
        summary = results.get('summary', {})
        
        print("\n" + "="*60)
        print("📊 Test Suite Summary")
        print("="*60)
        
        print(f"Total Tests:      {summary.get('total_tests', 0)}")
        print(f"Successful Tests: {summary.get('successful_tests', 0)}")
        print(f"Failed Tests:     {summary.get('failed_tests', 0)}")
        
        success_rate = summary.get('success_rate', 0)
        print(f"Success Rate:     {success_rate*100:.1f}%")
        print(f"Execution Time:   {summary.get('execution_time_seconds', 0):.1f}s")
        print(f"Overall Status:   {summary.get('overall_status', 'unknown').upper()}")
        
        # Print failed providers if any
        failed_providers = summary.get('failed_providers', [])
        if failed_providers:
            print(f"Failed Tests:     {', '.join(failed_providers)}")
        
        print("="*60)
    
    def generate_json_report(self, results: Dict[str, Any], output_file: str) -> None:
        """Generate JSON format report"""
        # Convert TestResult objects to dictionaries
        json_results = self._convert_results_to_json(results)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)
        
        print(f"✅ JSON report saved to: {output_file}")
    
    def _convert_results_to_json(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Convert TestResult objects to JSON-serializable format"""
        json_results = {}
        
        for key, value in results.items():
            if isinstance(value, dict):
                json_results[key] = {}
                for sub_key, sub_value in value.items():
                    if hasattr(sub_value, '__dict__'):
                        # Convert TestResult to dict
                        json_results[key][sub_key] = {
                            'status': sub_value.status.value if hasattr(sub_value.status, 'value') else str(sub_value.status),
                            'provider': getattr(sub_value, 'provider', None),
                            'test_type': getattr(sub_value, 'test_type', None),
                            'response_time_ms': getattr(sub_value, 'response_time_ms', None),
                            'data': getattr(sub_value, 'data', None),
                            'error': getattr(sub_value, 'error', None),
                            'error_category': getattr(sub_value, 'error_category', {}).get('value', None) if hasattr(getattr(sub_value, 'error_category', {}), 'value') else None,
                            'timestamp': getattr(sub_value, 'timestamp', None)
                        }
                    else:
                        json_results[key][sub_key] = sub_value
            else:
                json_results[key] = value
        
        return json_results
    
    async def run_tests(
        self,
        providers: Optional[List[str]] = None,
        test_types: Optional[List[str]] = None,
        timeout: int = 30,
        max_retries: int = 2,
        output_format: str = "console",
        output_file: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Run AI provider tests
        
        Args:
            providers: List of providers to test
            test_types: List of test types to run
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts
            output_format: Output format (console, json)
            output_file: Output file path
            
        Returns:
            Dict: Test results
        """
        # Setup validator
        self.setup_validator(timeout=timeout, max_retries=max_retries)
        
        # Default providers and test types
        all_providers = ["zhipu", "deepseek", "moonshot", "openrouter", "qwen", "ollama"]
        providers = providers or all_providers
        test_types = test_types or ["models", "conversation"]
        
        # Print header
        if output_format == "console":
            self.print_header(providers, test_types)
        
        # Run comprehensive test suite
        print(f"\n🔄 Running tests for {len(providers)} providers...")
        
        results = await self.validator.run_comprehensive_test_suite(
            providers=providers,
            test_types=test_types
        )
        
        # Output results
        if output_format == "console":
            print("\n" + "="*60)
            print("📋 Test Results")
            print("="*60)
            
            models_results = results.get('test_results', {}).get('models', {})
            conversation_results = results.get('test_results', {}).get('conversation', {})
            
            for provider in providers:
                models_result = models_results.get(provider)
                conversation_result = conversation_results.get(provider)
                self.print_provider_result(provider, models_result, conversation_result)
            
            self.print_summary(results)
        
        elif output_format == "json":
            if output_file:
                self.generate_json_report(results, output_file)
            else:
                print(json.dumps(self._convert_results_to_json(results), indent=2))
        
        return results


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="AI Provider Testing CLI - Test all 6 AI providers with real API calls",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                    # Test all providers
  %(prog)s --providers deepseek,moonshot     # Test specific providers
  %(prog)s --tests models                    # Test only models endpoint
  %(prog)s --output json --output-file report.json  # Generate JSON report
  %(prog)s --timeout 60 --max-retries 3     # Custom timeout and retry settings
        """
    )
    
    parser.add_argument(
        '--providers',
        help='Comma-separated list of providers to test (zhipu,deepseek,moonshot,openrouter,qwen,ollama)',
        default=None
    )
    
    parser.add_argument(
        '--tests', 
        help='Comma-separated list of test types (models,conversation,fallback,configuration)',
        default='models,conversation'
    )
    
    parser.add_argument(
        '--timeout',
        type=int,
        default=30,
        help='Request timeout in seconds (default: 30)'
    )
    
    parser.add_argument(
        '--max-retries',
        type=int, 
        default=2,
        help='Maximum retry attempts (default: 2)'
    )
    
    parser.add_argument(
        '--output',
        choices=['console', 'json'],
        default='console',
        help='Output format (default: console)'
    )
    
    parser.add_argument(
        '--output-file',
        help='Output file path (required for json format)'
    )
    
    parser.add_argument(
        '--api-url',
        default='http://localhost:8001',
        help='Backend API URL (default: http://localhost:8001)'
    )
    
    parser.add_argument(
        '--skip-connectivity-check',
        action='store_true',
        help='Skip API connectivity check'
    )
    
    args = parser.parse_args()
    
    # Parse providers and test types
    providers = [p.strip() for p in args.providers.split(',')] if args.providers else None
    test_types = [t.strip() for t in args.tests.split(',')]
    
    async def run_async():
        runner = SimpleAIProviderTestRunner()
        
        # Check API connectivity
        if not args.skip_connectivity_check:
            print(f"🔍 Checking API connectivity at {args.api_url}...")
            if not await runner.check_api_connectivity(args.api_url):
                print("❌ Backend API is not reachable. Please ensure the service is running.")
                print(f"   Expected URL: {args.api_url}/api/v1/health")
                sys.exit(1)
            print("✅ API connectivity confirmed")
        
        # Run tests
        try:
            results = await runner.run_tests(
                providers=providers,
                test_types=test_types,
                timeout=args.timeout,
                max_retries=args.max_retries,
                output_format=args.output,
                output_file=args.output_file
            )
            
            # Exit with error code if tests failed
            summary = results.get('summary', {})
            if summary.get('overall_status') == 'failure':
                sys.exit(1)
            elif summary.get('overall_status') == 'partial_failure':
                sys.exit(2)  # Partial failure exit code
                
        except KeyboardInterrupt:
            print("\n⚠️ Test execution interrupted by user")
            sys.exit(130)
        except Exception as e:
            print(f"❌ Test execution failed: {e}")
            import traceback
            traceback.print_exc()
            sys.exit(1)
    
    # Run async main function
    asyncio.run(run_async())


if __name__ == "__main__":
    main()