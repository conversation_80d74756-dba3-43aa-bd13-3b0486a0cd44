{"name": "talentforge-pro-playwright-tests", "version": "1.0.0", "description": "Comprehensive Playwright E2E test suite for TalentForge Pro", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:ui": "playwright test --ui", "test:admin": "playwright test tests/1-admin/", "test:candidates": "playwright test tests/2-candidate-assessment/", "test:dashboard": "playwright test tests/3-dashboard/", "test:jobs": "playwright test tests/4-job-management/", "test:ai": "playwright test tests/5-ai-questionnaire/", "test:api": "playwright test --grep 'API'", "test:critical": "playwright test --grep '@critical'", "test:smoke": "playwright test --grep '@smoke'", "test:regression": "npm run test", "test:parallel": "playwright test --workers=4", "test:serial": "playwright test --workers=1", "install-browsers": "playwright install", "install-deps": "playwright install-deps", "report": "playwright show-report", "report:html": "playwright show-report reports/html-report", "generate-report": "node scripts/generate-report.js", "health-check": "node scripts/health-check.js", "setup": "npm run install-browsers && npm run health-check", "ci": "npm run setup && npm run test:parallel", "dev": "npm run test:headed -- --reporter=list", "clean": "rm -rf reports/ test-results/ playwright-report/"}, "keywords": ["playwright", "e2e-testing", "black-box-testing", "talentforge-pro", "test-automation"], "dependencies": {"@playwright/test": "^1.40.0"}, "devDependencies": {"typescript": "^5.3.0", "@types/node": "^20.10.0"}, "engines": {"node": ">=18.0.0"}, "playwright": {"configFile": "config/playwright.config.ts"}}