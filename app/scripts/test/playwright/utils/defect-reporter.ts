/**
 * Defect Reporter for TalentForge Pro E2E Tests
 * 
 * Generates comprehensive defect reports in LLM-friendly Markdown format:
 * - Test failure analysis
 * - Error categorization
 * - Reproduction steps
 * - Screenshot and trace collection
 * - Priority and severity assessment
 */

import fs from 'fs/promises';
import path from 'path';

interface TestFailure {
  testName: string;
  testFile: string;
  error: string;
  duration: number;
  browser: string;
  screenshots?: string[];
  trace?: string;
  category?: DefectCategory;
  severity?: DefectSeverity;
  priority?: DefectPriority;
}

interface DefectSummary {
  totalTests: number;
  failedTests: number;
  passedTests: number;
  skippedTests: number;
  testDuration: number;
  categories: Record<DefectCategory, number>;
  severities: Record<DefectSeverity, number>;
  priorities: Record<DefectPriority, number>;
}

enum DefectCategory {
  AUTHENTICATION = 'Authentication',
  UI_INTERACTION = 'UI Interaction',
  API_FAILURE = 'API Failure',
  DATA_VALIDATION = 'Data Validation',
  NAVIGATION = 'Navigation',
  PERFORMANCE = 'Performance',
  BROWSER_COMPATIBILITY = 'Browser Compatibility',
  ACCESSIBILITY = 'Accessibility',
  UNKNOWN = 'Unknown',
}

enum DefectSeverity {
  CRITICAL = 'Critical',    // Application crashes, data loss, security issues
  HIGH = 'High',           // Major functionality broken, workarounds difficult
  MEDIUM = 'Medium',       // Functionality impaired, workarounds available
  LOW = 'Low',            // Minor issues, cosmetic problems
}

enum DefectPriority {
  P1 = 'P1',              // Must fix before release
  P2 = 'P2',              // Should fix before release
  P3 = 'P3',              // Fix in next iteration
  P4 = 'P4',              // Fix when time permits
}

export class DefectReporter {
  private readonly reportsDir: string;
  
  constructor() {
    this.reportsDir = path.join(__dirname, '../reports');
  }
  
  /**
   * Generate comprehensive defect report from test results
   */
  async generateReport(testResults: any): Promise<void> {
    console.log('📋 Generating defect report...');
    
    try {
      const failures = this.extractTestFailures(testResults);
      const summary = this.generateDefectSummary(testResults, failures);
      
      // Categorize and prioritize defects
      const categorizedFailures = await this.categorizeDefects(failures);
      
      // Generate markdown report
      const markdownReport = this.generateMarkdownReport(summary, categorizedFailures);
      
      // Save report
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const reportPath = path.join(this.reportsDir, `defects-report-${timestamp}.md`);
      
      await fs.writeFile(reportPath, markdownReport);
      
      // Also save as latest report
      await fs.writeFile(path.join(this.reportsDir, 'defects-report-latest.md'), markdownReport);
      
      console.log(`✅ Defect report generated: ${reportPath}`);
      
      // Generate summary for console output
      this.logDefectSummary(summary, categorizedFailures);
      
    } catch (error) {
      console.error('❌ Failed to generate defect report:', error);
      throw error;
    }
  }
  
  /**
   * Extract test failures from Playwright test results
   */
  private extractTestFailures(testResults: any): TestFailure[] {
    const failures: TestFailure[] = [];
    
    if (!testResults.suites) {
      return failures;
    }
    
    testResults.suites.forEach((suite: any) => {
      if (!suite.tests) return;
      
      suite.tests.forEach((test: any) => {
        if (test.outcome === 'unexpected' || test.outcome === 'flaky') {
          const failure: TestFailure = {
            testName: test.title || 'Unknown Test',
            testFile: suite.file || 'Unknown File',
            error: this.extractErrorMessage(test),
            duration: test.results?.[0]?.duration || 0,
            browser: this.extractBrowser(test),
            screenshots: this.extractScreenshots(test),
            trace: this.extractTrace(test),
          };
          
          failures.push(failure);
        }
      });
    });
    
    return failures;
  }
  
  /**
   * Extract error message from test result
   */
  private extractErrorMessage(test: any): string {
    if (!test.results || test.results.length === 0) {
      return 'No error details available';
    }
    
    const result = test.results[0];
    if (result.error) {
      return result.error.message || result.error.toString();
    }
    
    if (result.errors && result.errors.length > 0) {
      return result.errors[0].message || result.errors[0].toString();
    }
    
    return `Test failed with status: ${result.status}`;
  }
  
  /**
   * Extract browser information from test result
   */
  private extractBrowser(test: any): string {
    // Try to extract browser from test or project name
    if (test.projectName) {
      return test.projectName;
    }
    
    if (test.results?.[0]?.workerIndex !== undefined) {
      return `Worker ${test.results[0].workerIndex}`;
    }
    
    return 'Unknown Browser';
  }
  
  /**
   * Extract screenshots from test result
   */
  private extractScreenshots(test: any): string[] | undefined {
    const screenshots: string[] = [];
    
    if (test.results?.[0]?.attachments) {
      test.results[0].attachments.forEach((attachment: any) => {
        if (attachment.name === 'screenshot' && attachment.path) {
          screenshots.push(attachment.path);
        }
      });
    }
    
    return screenshots.length > 0 ? screenshots : undefined;
  }
  
  /**
   * Extract trace file from test result
   */
  private extractTrace(test: any): string | undefined {
    if (test.results?.[0]?.attachments) {
      for (const attachment of test.results[0].attachments) {
        if (attachment.name === 'trace' && attachment.path) {
          return attachment.path;
        }
      }
    }
    
    return undefined;
  }
  
  /**
   * Categorize defects based on error patterns
   */
  private async categorizeDefects(failures: TestFailure[]): Promise<TestFailure[]> {
    return failures.map(failure => {
      // Analyze error message and test context to categorize
      const category = this.determineCategory(failure);
      const severity = this.determineSeverity(failure);
      const priority = this.determinePriority(failure, severity);
      
      return {
        ...failure,
        category,
        severity,
        priority,
      };
    });
  }
  
  /**
   * Determine defect category based on error patterns
   */
  private determineCategory(failure: TestFailure): DefectCategory {
    const error = failure.error.toLowerCase();
    const testName = failure.testName.toLowerCase();
    
    if (error.includes('unauthorized') || error.includes('authentication') || error.includes('login')) {
      return DefectCategory.AUTHENTICATION;
    }
    
    if (error.includes('element not found') || error.includes('click') || error.includes('selector')) {
      return DefectCategory.UI_INTERACTION;
    }
    
    if (error.includes('api') || error.includes('request') || error.includes('response') || error.includes('500') || error.includes('404')) {
      return DefectCategory.API_FAILURE;
    }
    
    if (error.includes('validation') || error.includes('required') || error.includes('invalid')) {
      return DefectCategory.DATA_VALIDATION;
    }
    
    if (error.includes('navigation') || error.includes('url') || error.includes('redirect')) {
      return DefectCategory.NAVIGATION;
    }
    
    if (error.includes('timeout') || error.includes('slow') || error.includes('performance')) {
      return DefectCategory.PERFORMANCE;
    }
    
    if (testName.includes('mobile') || testName.includes('firefox') || testName.includes('safari')) {
      return DefectCategory.BROWSER_COMPATIBILITY;
    }
    
    if (testName.includes('accessibility') || testName.includes('a11y')) {
      return DefectCategory.ACCESSIBILITY;
    }
    
    return DefectCategory.UNKNOWN;
  }
  
  /**
   * Determine defect severity
   */
  private determineSeverity(failure: TestFailure): DefectSeverity {
    const error = failure.error.toLowerCase();
    const testName = failure.testName.toLowerCase();
    
    // Critical: Security, data loss, crashes
    if (error.includes('security') || error.includes('sql injection') || error.includes('xss') || 
        error.includes('crash') || error.includes('data loss')) {
      return DefectSeverity.CRITICAL;
    }
    
    // High: Core functionality broken
    if (testName.includes('login') || testName.includes('authentication') || 
        testName.includes('create') || testName.includes('delete') || 
        error.includes('500') || error.includes('database')) {
      return DefectSeverity.HIGH;
    }
    
    // Medium: Functionality impaired but workarounds exist
    if (testName.includes('search') || testName.includes('filter') || 
        testName.includes('export') || error.includes('404')) {
      return DefectSeverity.MEDIUM;
    }
    
    // Low: Minor issues
    return DefectSeverity.LOW;
  }
  
  /**
   * Determine defect priority based on severity and context
   */
  private determinePriority(failure: TestFailure, severity: DefectSeverity): DefectPriority {
    // Priority mapping based on severity and test context
    if (severity === DefectSeverity.CRITICAL) {
      return DefectPriority.P1;
    }
    
    if (severity === DefectSeverity.HIGH) {
      const testName = failure.testName.toLowerCase();
      // Core user flows get higher priority
      if (testName.includes('login') || testName.includes('create') || testName.includes('dashboard')) {
        return DefectPriority.P1;
      }
      return DefectPriority.P2;
    }
    
    if (severity === DefectSeverity.MEDIUM) {
      return DefectPriority.P2;
    }
    
    return DefectPriority.P3;
  }
  
  /**
   * Generate defect summary statistics
   */
  private generateDefectSummary(testResults: any, failures: TestFailure[]): DefectSummary {
    const totalTests = this.countTotalTests(testResults);
    const failedTests = failures.length;
    const passedTests = totalTests - failedTests - this.countSkippedTests(testResults);
    const skippedTests = this.countSkippedTests(testResults);
    const testDuration = this.calculateTotalDuration(testResults);
    
    // Count by category
    const categories = Object.values(DefectCategory).reduce((acc, category) => {
      acc[category] = failures.filter(f => f.category === category).length;
      return acc;
    }, {} as Record<DefectCategory, number>);
    
    // Count by severity
    const severities = Object.values(DefectSeverity).reduce((acc, severity) => {
      acc[severity] = failures.filter(f => f.severity === severity).length;
      return acc;
    }, {} as Record<DefectSeverity, number>);
    
    // Count by priority
    const priorities = Object.values(DefectPriority).reduce((acc, priority) => {
      acc[priority] = failures.filter(f => f.priority === priority).length;
      return acc;
    }, {} as Record<DefectPriority, number>);
    
    return {
      totalTests,
      failedTests,
      passedTests,
      skippedTests,
      testDuration,
      categories,
      severities,
      priorities,
    };
  }
  
  /**
   * Generate comprehensive markdown report
   */
  private generateMarkdownReport(summary: DefectSummary, failures: TestFailure[]): string {
    const timestamp = new Date().toISOString();
    
    let markdown = '# 🐛 TalentForge Pro Defect Report\n\n';
    markdown += `**Generated:** ${timestamp}\n`;
    markdown += `**Environment:** Development (localhost:8088)\n`;
    markdown += `**Test Framework:** Playwright\n\n`;
    
    // Executive Summary
    markdown += '## 📊 Executive Summary\n\n';
    markdown += `- **Total Tests:** ${summary.totalTests}\n`;
    markdown += `- **Failed Tests:** ${summary.failedTests}\n`;
    markdown += `- **Passed Tests:** ${summary.passedTests}\n`;
    markdown += `- **Skipped Tests:** ${summary.skippedTests}\n`;
    markdown += `- **Success Rate:** ${((summary.passedTests / summary.totalTests) * 100).toFixed(1)}%\n`;
    markdown += `- **Test Duration:** ${Math.round(summary.testDuration / 1000)}s\n\n`;
    
    // Severity Breakdown
    markdown += '## 🚨 Severity Breakdown\n\n';
    Object.entries(summary.severities).forEach(([severity, count]) => {
      if (count > 0) {
        const emoji = this.getSeverityEmoji(severity as DefectSeverity);
        markdown += `- **${emoji} ${severity}:** ${count} issues\n`;
      }
    });
    markdown += '\n';
    
    // Category Breakdown
    markdown += '## 📈 Defect Categories\n\n';
    Object.entries(summary.categories).forEach(([category, count]) => {
      if (count > 0) {
        const emoji = this.getCategoryEmoji(category as DefectCategory);
        markdown += `- **${emoji} ${category}:** ${count} issues\n`;
      }
    });
    markdown += '\n';
    
    // Priority Breakdown
    markdown += '## 🎯 Priority Distribution\n\n';
    Object.entries(summary.priorities).forEach(([priority, count]) => {
      if (count > 0) {
        const emoji = this.getPriorityEmoji(priority as DefectPriority);
        markdown += `- **${emoji} ${priority}:** ${count} issues\n`;
      }
    });
    markdown += '\n';
    
    // Detailed Defect List
    if (failures.length > 0) {
      markdown += '## 🔍 Detailed Defect Analysis\n\n';
      
      // Group by priority
      const priorityGroups = Object.values(DefectPriority).map(priority => ({
        priority,
        failures: failures.filter(f => f.priority === priority),
      })).filter(group => group.failures.length > 0);
      
      priorityGroups.forEach(group => {
        markdown += `### ${this.getPriorityEmoji(group.priority)} ${group.priority} Issues\n\n`;
        
        group.failures.forEach((failure, index) => {
          markdown += `#### ${index + 1}. ${failure.testName}\n\n`;
          markdown += `- **File:** \`${failure.testFile}\`\n`;
          markdown += `- **Browser:** ${failure.browser}\n`;
          markdown += `- **Category:** ${this.getCategoryEmoji(failure.category!)} ${failure.category}\n`;
          markdown += `- **Severity:** ${this.getSeverityEmoji(failure.severity!)} ${failure.severity}\n`;
          markdown += `- **Duration:** ${failure.duration}ms\n\n`;
          
          markdown += '**Error Details:**\n';
          markdown += '```\n';
          markdown += failure.error;
          markdown += '\n```\n\n';
          
          if (failure.screenshots && failure.screenshots.length > 0) {
            markdown += '**Screenshots:**\n';
            failure.screenshots.forEach(screenshot => {
              markdown += `- ![Screenshot](${screenshot})\n`;
            });
            markdown += '\n';
          }
          
          if (failure.trace) {
            markdown += `**Trace:** [View Trace](${failure.trace})\n\n`;
          }
          
          markdown += '**Recommended Actions:**\n';
          markdown += this.generateRecommendedActions(failure);
          markdown += '\n';
          
          markdown += '---\n\n';
        });
      });
    }
    
    // LLM-Friendly Analysis Section
    markdown += '## 🤖 LLM Analysis Context\n\n';
    markdown += 'This section provides structured information for automated analysis:\n\n';
    
    markdown += '### Error Patterns\n\n';
    const errorPatterns = this.analyzeErrorPatterns(failures);
    errorPatterns.forEach(pattern => {
      markdown += `- **${pattern.pattern}:** ${pattern.count} occurrences\n`;
    });
    markdown += '\n';
    
    markdown += '### Test Files with Issues\n\n';
    const fileIssues = this.analyzeFileIssues(failures);
    fileIssues.forEach(file => {
      markdown += `- **${file.file}:** ${file.issues} issues (${file.categories.join(', ')})\n`;
    });
    markdown += '\n';
    
    markdown += '### Browser Compatibility Issues\n\n';
    const browserIssues = this.analyzeBrowserIssues(failures);
    browserIssues.forEach(browser => {
      markdown += `- **${browser.browser}:** ${browser.issues} issues\n`;
    });
    markdown += '\n';
    
    // Footer
    markdown += '---\n\n';
    markdown += '**Report Generated By:** TalentForge Pro Playwright Test Suite\n';
    markdown += `**Report Version:** 1.0\n`;
    markdown += `**Framework Version:** Playwright\n`;
    
    return markdown;
  }
  
  /**
   * Generate recommended actions for a defect
   */
  private generateRecommendedActions(failure: TestFailure): string {
    let actions = '';
    
    switch (failure.category) {
      case DefectCategory.AUTHENTICATION:
        actions += '1. Verify authentication token validity\n';
        actions += '2. Check API authentication endpoints\n';
        actions += '3. Review session management logic\n';
        break;
        
      case DefectCategory.UI_INTERACTION:
        actions += '1. Verify element selectors are correct\n';
        actions += '2. Check for timing issues with page loads\n';
        actions += '3. Review UI component rendering logic\n';
        break;
        
      case DefectCategory.API_FAILURE:
        actions += '1. Check API endpoint availability\n';
        actions += '2. Verify request/response formats\n';
        actions += '3. Review server logs for errors\n';
        break;
        
      case DefectCategory.DATA_VALIDATION:
        actions += '1. Review data validation rules\n';
        actions += '2. Check input sanitization\n';
        actions += '3. Verify database constraints\n';
        break;
        
      case DefectCategory.PERFORMANCE:
        actions += '1. Optimize database queries\n';
        actions += '2. Review timeout configurations\n';
        actions += '3. Check for memory leaks\n';
        break;
        
      default:
        actions += '1. Investigate error logs\n';
        actions += '2. Reproduce issue manually\n';
        actions += '3. Review related code changes\n';
    }
    
    return actions;
  }
  
  // Helper methods for emojis
  private getSeverityEmoji(severity: DefectSeverity): string {
    const emojis = {
      [DefectSeverity.CRITICAL]: '🔴',
      [DefectSeverity.HIGH]: '🟠',
      [DefectSeverity.MEDIUM]: '🟡',
      [DefectSeverity.LOW]: '🟢',
    };
    return emojis[severity];
  }
  
  private getCategoryEmoji(category: DefectCategory): string {
    const emojis = {
      [DefectCategory.AUTHENTICATION]: '🔐',
      [DefectCategory.UI_INTERACTION]: '🖱️',
      [DefectCategory.API_FAILURE]: '🌐',
      [DefectCategory.DATA_VALIDATION]: '📝',
      [DefectCategory.NAVIGATION]: '🧭',
      [DefectCategory.PERFORMANCE]: '⚡',
      [DefectCategory.BROWSER_COMPATIBILITY]: '🌍',
      [DefectCategory.ACCESSIBILITY]: '♿',
      [DefectCategory.UNKNOWN]: '❓',
    };
    return emojis[category];
  }
  
  private getPriorityEmoji(priority: DefectPriority): string {
    const emojis = {
      [DefectPriority.P1]: '🚨',
      [DefectPriority.P2]: '⚠️',
      [DefectPriority.P3]: '📋',
      [DefectPriority.P4]: '📝',
    };
    return emojis[priority];
  }
  
  // Analysis helper methods
  private analyzeErrorPatterns(failures: TestFailure[]): Array<{pattern: string; count: number}> {
    const patterns = new Map<string, number>();
    
    failures.forEach(failure => {
      const error = failure.error.toLowerCase();
      
      if (error.includes('timeout')) {
        patterns.set('Timeout errors', (patterns.get('Timeout errors') || 0) + 1);
      }
      if (error.includes('element not found')) {
        patterns.set('Element not found', (patterns.get('Element not found') || 0) + 1);
      }
      if (error.includes('api') || error.includes('request')) {
        patterns.set('API/Request failures', (patterns.get('API/Request failures') || 0) + 1);
      }
      if (error.includes('navigation')) {
        patterns.set('Navigation issues', (patterns.get('Navigation issues') || 0) + 1);
      }
    });
    
    return Array.from(patterns.entries())
      .map(([pattern, count]) => ({ pattern, count }))
      .sort((a, b) => b.count - a.count);
  }
  
  private analyzeFileIssues(failures: TestFailure[]): Array<{file: string; issues: number; categories: string[]}> {
    const fileIssues = new Map<string, {count: number; categories: Set<string>}>();
    
    failures.forEach(failure => {
      const file = failure.testFile;
      if (!fileIssues.has(file)) {
        fileIssues.set(file, { count: 0, categories: new Set() });
      }
      
      const fileData = fileIssues.get(file)!;
      fileData.count++;
      if (failure.category) {
        fileData.categories.add(failure.category);
      }
    });
    
    return Array.from(fileIssues.entries())
      .map(([file, data]) => ({
        file,
        issues: data.count,
        categories: Array.from(data.categories),
      }))
      .sort((a, b) => b.issues - a.issues);
  }
  
  private analyzeBrowserIssues(failures: TestFailure[]): Array<{browser: string; issues: number}> {
    const browserIssues = new Map<string, number>();
    
    failures.forEach(failure => {
      const browser = failure.browser;
      browserIssues.set(browser, (browserIssues.get(browser) || 0) + 1);
    });
    
    return Array.from(browserIssues.entries())
      .map(([browser, issues]) => ({ browser, issues }))
      .sort((a, b) => b.issues - a.issues);
  }
  
  // Helper methods for test result parsing
  private countTotalTests(testResults: any): number {
    if (!testResults.suites) return 0;
    
    return testResults.suites.reduce((total: number, suite: any) => {
      return total + (suite.tests?.length || 0);
    }, 0);
  }
  
  private countSkippedTests(testResults: any): number {
    if (!testResults.suites) return 0;
    
    return testResults.suites.reduce((total: number, suite: any) => {
      if (!suite.tests) return total;
      
      return total + suite.tests.filter((test: any) => test.outcome === 'skipped').length;
    }, 0);
  }
  
  private calculateTotalDuration(testResults: any): number {
    if (!testResults.suites) return 0;
    
    return testResults.suites.reduce((total: number, suite: any) => {
      if (!suite.tests) return total;
      
      return total + suite.tests.reduce((suiteTotal: number, test: any) => {
        const duration = test.results?.[0]?.duration || 0;
        return suiteTotal + duration;
      }, 0);
    }, 0);
  }
  
  /**
   * Log defect summary to console
   */
  private logDefectSummary(summary: DefectSummary, failures: TestFailure[]): void {
    console.log('\n🐛 Defect Report Summary:');
    console.log('━'.repeat(50));
    console.log(`Total Tests: ${summary.totalTests}`);
    console.log(`Failed: ${summary.failedTests} | Passed: ${summary.passedTests} | Skipped: ${summary.skippedTests}`);
    console.log(`Success Rate: ${((summary.passedTests / summary.totalTests) * 100).toFixed(1)}%`);
    
    if (summary.failedTests > 0) {
      console.log('\nTop Issues by Priority:');
      
      Object.values(DefectPriority).forEach(priority => {
        const count = summary.priorities[priority];
        if (count > 0) {
          console.log(`  ${this.getPriorityEmoji(priority)} ${priority}: ${count} issues`);
        }
      });
      
      console.log('\nTop Categories:');
      Object.entries(summary.categories)
        .filter(([_, count]) => count > 0)
        .sort(([_, a], [__, b]) => b - a)
        .slice(0, 3)
        .forEach(([category, count]) => {
          console.log(`  ${this.getCategoryEmoji(category as DefectCategory)} ${category}: ${count} issues`);
        });
    }
    
    console.log('━'.repeat(50));
  }
}