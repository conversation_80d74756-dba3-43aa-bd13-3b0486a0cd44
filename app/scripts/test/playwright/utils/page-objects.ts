/**
 * Page Object Models for TalentForge Pro E2E Tests
 * 
 * Centralized page interaction patterns:
 * - Reusable page components
 * - Business logic abstraction
 * - Consistent interaction patterns
 * - Cross-browser compatibility
 */

import { Page, expect, Locator } from '@playwright/test';
import { TalentForgeSelectors } from './selectors';
import { AuthHelper } from './auth-helper';

export abstract class BasePage {
  protected page: Page;
  protected authHelper: AuthHelper;
  
  constructor(page: Page) {
    this.page = page;
    this.authHelper = new AuthHelper(page);
  }
  
  /**
   * Navigate to page and wait for load
   */
  abstract navigate(): Promise<void>;
  
  /**
   * Verify page is loaded correctly
   */
  abstract isLoaded(): Promise<boolean>;
  
  /**
   * Get page title
   */
  async getTitle(): Promise<string> {
    return await this.page.title();
  }
  
  /**
   * Wait for page to be in ready state
   */
  async waitForReady(): Promise<void> {
    await this.page.waitForLoadState('networkidle');
  }
  
  /**
   * Take screenshot for debugging
   */
  async takeScreenshot(name?: string): Promise<void> {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = name ? `${name}-${timestamp}.png` : `screenshot-${timestamp}.png`;
    await this.page.screenshot({ path: filename, fullPage: true });
  }
  
  /**
   * Check if user is authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    return await this.authHelper.isAuthenticated();
  }
  
  /**
   * Wait for element to be visible using smart selector
   */
  async waitForElement(selector: any, timeout: number = 10000): Promise<Locator | null> {
    if (typeof selector === 'object' && selector.findElement) {
      return await selector.findElement(this.page, timeout);
    }
    
    const element = this.page.locator(selector);
    try {
      await element.waitFor({ timeout });
      return element;
    } catch {
      return null;
    }
  }
  
  /**
   * Click element with retry logic
   */
  async clickElement(selector: any, timeout: number = 10000): Promise<boolean> {
    const element = await this.waitForElement(selector, timeout);
    if (element) {
      await element.click();
      return true;
    }
    return false;
  }
  
  /**
   * Fill input field with retry logic
   */
  async fillField(selector: any, value: string, timeout: number = 10000): Promise<boolean> {
    const element = await this.waitForElement(selector, timeout);
    if (element) {
      await element.fill(value);
      return true;
    }
    return false;
  }
  
  /**
   * Select option from dropdown
   */
  async selectOption(selector: any, option: string | { value?: string; label?: string; index?: number }, timeout: number = 10000): Promise<boolean> {
    const element = await this.waitForElement(selector, timeout);
    if (element) {
      if (typeof option === 'string') {
        await element.selectOption(option);
      } else if (option.value) {
        await element.selectOption({ value: option.value });
      } else if (option.label) {
        await element.selectOption({ label: option.label });
      } else if (option.index !== undefined) {
        await element.selectOption({ index: option.index });
      }
      return true;
    }
    return false;
  }
}

export class LoginPage extends BasePage {
  async navigate(): Promise<void> {
    await this.page.goto('/login');
    await this.waitForReady();
  }
  
  async isLoaded(): Promise<boolean> {
    return await TalentForgeSelectors.AUTH.loginForm.isVisible(this.page, 5000);
  }
  
  async login(email: string, password: string): Promise<boolean> {
    try {
      await this.navigate();
      
      // Fill email
      const emailFilled = await this.fillField(TalentForgeSelectors.AUTH.emailInput, email);
      if (!emailFilled) return false;
      
      // Fill password
      const passwordFilled = await this.fillField(TalentForgeSelectors.AUTH.passwordInput, password);
      if (!passwordFilled) return false;
      
      // Click login button
      const loginClicked = await this.clickElement(TalentForgeSelectors.AUTH.loginButton);
      if (!loginClicked) return false;
      
      // Wait for redirect or dashboard
      await Promise.race([
        this.page.waitForURL('/dashboard', { timeout: 15000 }),
        this.page.waitForURL('/', { timeout: 15000 }),
        this.page.waitForSelector('[data-testid="user-menu"], .user-avatar', { timeout: 15000 })
      ]);
      
      return true;
    } catch (error) {
      console.error('Login failed:', error);
      return false;
    }
  }
  
  async loginWithDevToken(): Promise<boolean> {
    try {
      await this.authHelper.loginWithDevToken();
      return true;
    } catch (error) {
      console.error('Dev token login failed:', error);
      return false;
    }
  }
  
  async getLoginError(): Promise<string | null> {
    const errorElement = await this.waitForElement(TalentForgeSelectors.STATUS.errorMessage, 3000);
    if (errorElement) {
      return await errorElement.textContent();
    }
    return null;
  }
}

export class DashboardPage extends BasePage {
  async navigate(): Promise<void> {
    await this.page.goto('/dashboard');
    await this.waitForReady();
  }
  
  async isLoaded(): Promise<boolean> {
    return await TalentForgeSelectors.DASHBOARD.container.isVisible(this.page, 10000);
  }
  
  async getMetricCards(): Promise<{ title: string; value: string }[]> {
    const cards = await TalentForgeSelectors.DASHBOARD.metricCards.findElements(this.page);
    const metrics: { title: string; value: string }[] = [];
    
    for (const card of cards) {
      const title = await card.locator('.metric-title, .card-title, h3, h4').textContent() || '';
      const value = await card.locator('.metric-value, .card-value, .stat-number').textContent() || '';
      metrics.push({ title: title.trim(), value: value.trim() });
    }
    
    return metrics;
  }
  
  async refreshDashboard(): Promise<boolean> {
    return await this.clickElement(TalentForgeSelectors.DASHBOARD.refreshButton);
  }
  
  async waitForChartsToLoad(): Promise<boolean> {
    const charts = await TalentForgeSelectors.DASHBOARD.charts.findElements(this.page);
    return charts.length > 0;
  }
  
  async hasLoadingError(): Promise<boolean> {
    return await TalentForgeSelectors.STATUS.errorMessage.isVisible(this.page, 3000);
  }
}

export class CandidatesPage extends BasePage {
  async navigate(): Promise<void> {
    await this.page.goto('/candidates');
    await this.waitForReady();
  }
  
  async isLoaded(): Promise<boolean> {
    return await TalentForgeSelectors.CANDIDATES.listContainer.isVisible(this.page, 10000);
  }
  
  async getCandidateCount(): Promise<number> {
    const rows = await TalentForgeSelectors.CANDIDATES.candidateRows.findElements(this.page);
    return rows.length;
  }
  
  async searchCandidates(query: string): Promise<boolean> {
    const searchFilled = await this.fillField(TalentForgeSelectors.CANDIDATES.searchInput, query);
    if (searchFilled) {
      await this.page.waitForTimeout(1000); // Allow search to process
    }
    return searchFilled;
  }
  
  async createCandidate(candidateData: {
    name: string;
    email: string;
    phone?: string;
    skills?: string;
  }): Promise<boolean> {
    try {
      // Click create button
      const createClicked = await this.clickElement(TalentForgeSelectors.CANDIDATES.createButton);
      if (!createClicked) return false;
      
      await this.waitForReady();
      
      // Fill form fields
      await this.fillField(TalentForgeSelectors.CANDIDATES.nameField, candidateData.name);
      await this.fillField(TalentForgeSelectors.CANDIDATES.emailField, candidateData.email);
      
      if (candidateData.phone) {
        await this.fillField('[name="phone"], input[placeholder*="phone"]', candidateData.phone);
      }
      
      if (candidateData.skills) {
        await this.fillField('[name="skills"], textarea[placeholder*="skill"]', candidateData.skills);
      }
      
      // Submit form
      const submitClicked = await this.clickElement(TalentForgeSelectors.FORMS.submitButton);
      if (!submitClicked) return false;
      
      // Wait for success or redirect
      await Promise.race([
        this.waitForElement(TalentForgeSelectors.FORMS.successMessage, 10000),
        this.page.waitForURL(/candidates/, { timeout: 10000 })
      ]);
      
      return true;
    } catch (error) {
      console.error('Create candidate failed:', error);
      return false;
    }
  }
  
  async selectFirstCandidate(): Promise<boolean> {
    const candidateRows = await TalentForgeSelectors.CANDIDATES.candidateRows.findElements(this.page);
    if (candidateRows.length > 0) {
      await candidateRows[0].click();
      await this.waitForReady();
      return true;
    }
    return false;
  }
  
  async getValidationErrors(): Promise<string[]> {
    const errors = await TalentForgeSelectors.FORMS.validationErrors.findElements(this.page);
    const errorTexts: string[] = [];
    
    for (const error of errors) {
      const text = await error.textContent();
      if (text) errorTexts.push(text.trim());
    }
    
    return errorTexts;
  }
}

export class JobsPage extends BasePage {
  async navigate(): Promise<void> {
    await this.page.goto('/jobs');
    await this.waitForReady();
  }
  
  async isLoaded(): Promise<boolean> {
    return await TalentForgeSelectors.JOBS.listContainer.isVisible(this.page, 10000);
  }
  
  async getJobCount(): Promise<number> {
    const rows = await TalentForgeSelectors.JOBS.jobRows.findElements(this.page);
    return rows.length;
  }
  
  async createJob(jobData: {
    title: string;
    department: string;
    location: string;
    description?: string;
  }): Promise<boolean> {
    try {
      // Click create button
      const createClicked = await this.clickElement(TalentForgeSelectors.JOBS.createButton);
      if (!createClicked) return false;
      
      await this.waitForReady();
      
      // Fill form fields
      await this.fillField(TalentForgeSelectors.JOBS.titleField, jobData.title);
      await this.fillField(TalentForgeSelectors.JOBS.departmentField, jobData.department);
      await this.fillField('[name="location"], [data-testid="location"]', jobData.location);
      
      if (jobData.description) {
        await this.fillField('[name="description"], textarea[placeholder*="description"]', jobData.description);
      }
      
      // Submit form
      const submitClicked = await this.clickElement(TalentForgeSelectors.FORMS.submitButton);
      if (!submitClicked) return false;
      
      // Wait for success or redirect
      await Promise.race([
        this.waitForElement(TalentForgeSelectors.FORMS.successMessage, 10000),
        this.page.waitForURL(/jobs/, { timeout: 10000 })
      ]);
      
      return true;
    } catch (error) {
      console.error('Create job failed:', error);
      return false;
    }
  }
  
  async selectFirstJob(): Promise<boolean> {
    const jobRows = await TalentForgeSelectors.JOBS.jobRows.findElements(this.page);
    if (jobRows.length > 0) {
      await jobRows[0].click();
      await this.waitForReady();
      return true;
    }
    return false;
  }
  
  async filterByDepartment(department: string): Promise<boolean> {
    return await this.selectOption('[data-testid="department-filter"], select[name*="department"]', department);
  }
  
  async filterByLocation(location: string): Promise<boolean> {
    return await this.selectOption('[data-testid="location-filter"], select[name*="location"]', location);
  }
}

export class AdminPage extends BasePage {
  async navigate(): Promise<void> {
    await this.page.goto('/admin');
    await this.waitForReady();
  }
  
  async navigateToDashboard(): Promise<void> {
    await this.page.goto('/admin/dashboard');
    await this.waitForReady();
  }
  
  async navigateToUsers(): Promise<void> {
    await this.page.goto('/admin/users');
    await this.waitForReady();
  }
  
  async navigateToMonitoring(): Promise<void> {
    await this.page.goto('/admin/monitoring');
    await this.waitForReady();
  }
  
  async isLoaded(): Promise<boolean> {
    return await TalentForgeSelectors.ADMIN.dashboard.isVisible(this.page, 10000);
  }
  
  async accessUserManagement(): Promise<boolean> {
    return await this.clickElement(TalentForgeSelectors.ADMIN.userManagement);
  }
  
  async accessMonitoring(): Promise<boolean> {
    return await this.clickElement(TalentForgeSelectors.ADMIN.monitoring);
  }
  
  async getHealthStatus(): Promise<{ service: string; status: string }[]> {
    const healthStatuses: { service: string; status: string }[] = [];
    
    // Look for health indicators
    const healthElements = await this.page.locator('.health-indicator, .status-indicator, [data-testid*="health"]').all();
    
    for (const element of healthElements) {
      const service = await element.locator('.service-name, .health-service').textContent() || 'Unknown Service';
      const status = await element.locator('.status-text, .health-status').textContent() || 'Unknown Status';
      healthStatuses.push({ service: service.trim(), status: status.trim() });
    }
    
    return healthStatuses;
  }
}

export class AIQuestionnairePage extends BasePage {
  async navigate(): Promise<void> {
    // Try multiple possible paths
    const paths = ['/ai-questionnaire', '/questionnaires', '/ai'];
    
    for (const path of paths) {
      try {
        await this.page.goto(path);
        await this.waitForReady();
        if (await this.isLoaded()) {
          return;
        }
      } catch (error) {
        console.log(`Path ${path} not accessible`);
      }
    }
    
    // Fallback to dashboard and look for AI navigation
    await this.page.goto('/dashboard');
    await this.waitForReady();
  }
  
  async isLoaded(): Promise<boolean> {
    return await TalentForgeSelectors.AI.questionnaire.isVisible(this.page, 5000);
  }
  
  async generateQuestionnaire(parameters?: {
    questionCount?: number;
    difficulty?: string;
    category?: string;
  }): Promise<boolean> {
    try {
      // Set parameters if provided
      if (parameters?.questionCount) {
        await this.fillField('[data-testid="question-count"], input[name*="count"]', parameters.questionCount.toString());
      }
      
      if (parameters?.difficulty) {
        await this.selectOption('select[name*="difficulty"]', parameters.difficulty);
      }
      
      if (parameters?.category) {
        await this.selectOption('select[name*="category"]', parameters.category);
      }
      
      // Click generate button
      const generateClicked = await this.clickElement(TalentForgeSelectors.AI.generateButton);
      if (!generateClicked) return false;
      
      // Wait for generation to complete
      await this.page.waitForSelector('text=Generating', { timeout: 5000 }).catch(() => {});
      await this.page.waitForSelector('text=Generating', { state: 'hidden', timeout: 30000 }).catch(() => {});
      
      return true;
    } catch (error) {
      console.error('Generate questionnaire failed:', error);
      return false;
    }
  }
  
  async getGeneratedQuestions(): Promise<string[]> {
    const questions: string[] = [];
    const questionElements = await this.page.locator('.question-item, .generated-question, [data-testid*="question"]').all();
    
    for (const element of questionElements) {
      const text = await element.textContent();
      if (text) questions.push(text.trim());
    }
    
    return questions;
  }
  
  async getAIScore(): Promise<number | null> {
    const scoreElement = await TalentForgeSelectors.AI.scoreDisplay.findElement(this.page, 5000);
    if (scoreElement) {
      const scoreText = await scoreElement.textContent();
      const scoreMatch = scoreText?.match(/(\d+(?:\.\d+)?)/);
      return scoreMatch ? parseFloat(scoreMatch[1]) : null;
    }
    return null;
  }
}

export class NavigationHelper {
  constructor(private page: Page) {}
  
  async navigateToSection(section: 'dashboard' | 'candidates' | 'jobs' | 'admin'): Promise<boolean> {
    const selectors = {
      dashboard: TalentForgeSelectors.NAVIGATION.dashboardLink,
      candidates: TalentForgeSelectors.NAVIGATION.candidatesLink,
      jobs: TalentForgeSelectors.NAVIGATION.jobsLink,
      admin: TalentForgeSelectors.NAVIGATION.adminLink
    };
    
    const link = await selectors[section].findElement(this.page, 5000);
    if (link) {
      await link.click();
      await this.page.waitForLoadState('networkidle');
      return true;
    }
    
    return false;
  }
  
  async toggleMobileMenu(): Promise<boolean> {
    const toggleButton = await TalentForgeSelectors.NAVIGATION.mobileMenuToggle.findElement(this.page, 3000);
    if (toggleButton) {
      await toggleButton.click();
      return true;
    }
    return false;
  }
  
  async logout(): Promise<boolean> {
    const logoutButton = await TalentForgeSelectors.AUTH.logoutButton.findElement(this.page, 5000);
    if (logoutButton) {
      await logoutButton.click();
      await this.page.waitForURL('/login', { timeout: 10000 });
      return true;
    }
    return false;
  }
}

export class LanguageHelper {
  constructor(private page: Page) {}
  
  async switchLanguage(language: 'en' | 'zh'): Promise<boolean> {
    const switcher = await TalentForgeSelectors.LANGUAGE.languageSwitcher.findElement(this.page, 5000);
    if (!switcher) return false;
    
    // Try select dropdown first
    try {
      await switcher.selectOption(language);
      await this.page.waitForTimeout(1000);
      return true;
    } catch {
      // Try clicking language option
      const option = language === 'zh' 
        ? TalentForgeSelectors.LANGUAGE.chineseOption
        : TalentForgeSelectors.LANGUAGE.englishOption;
      
      const optionElement = await option.findElement(this.page, 3000);
      if (optionElement) {
        await optionElement.click();
        await this.page.waitForTimeout(1000);
        return true;
      }
    }
    
    return false;
  }
  
  async getCurrentLanguage(): Promise<string | null> {
    // Check for language indicator elements
    const langIndicators = [
      '[data-current-lang]',
      '.current-language',
      '.active-language'
    ];
    
    for (const selector of langIndicators) {
      const element = this.page.locator(selector);
      if (await element.isVisible({ timeout: 2000 })) {
        return await element.getAttribute('data-current-lang') || 
               await element.textContent() || 
               null;
      }
    }
    
    return null;
  }
  
  async verifyLanguageChange(expectedLanguage: 'en' | 'zh'): Promise<boolean> {
    // Check for language-specific content
    if (expectedLanguage === 'zh') {
      // Look for Chinese characters
      const chineseContent = this.page.locator('text=/[\u4e00-\u9fff]/');
      return await chineseContent.count() > 0;
    } else {
      // Look for English content (less reliable but can check for common English words)
      const englishContent = this.page.locator('text=/Dashboard|Candidates|Jobs|Admin/');
      return await englishContent.count() > 0;
    }
  }
}