/**
 * Authentication Helper for TalentForge Pro E2E Tests
 * 
 * Provides comprehensive authentication utilities:
 * - Dev token authentication
 * - Regular login flows
 * - Session management
 * - Auth state persistence
 * - API endpoint validation
 */

import { Page, expect } from '@playwright/test';
import { TestConfig } from '../config/test-config';
import fs from 'fs/promises';
import path from 'path';

export class AuthHelper {
  private page: Page;
  private readonly config: TestConfig;
  private readonly devToken = 'dev_bypass_token_2025_talentforge';
  
  constructor(page: Page) {
    this.page = page;
    this.config = new TestConfig();
  }
  
  /**
   * Login using development bypass token
   * Fast authentication for development environment
   */
  async loginWithDevToken(): Promise<void> {
    console.log('🔐 Authenticating with dev token...');
    
    try {
      // Navigate to main page first
      await this.page.goto('/');
      
      // Set authentication token in localStorage
      await this.page.evaluate((token) => {
        localStorage.setItem('access_token', token);
        localStorage.setItem('token_type', 'bearer');
      }, this.devToken);
      
      // Also set in cookies for SSR compatibility
      await this.page.context().addCookies([
        {
          name: 'access_token',
          value: this.devToken,
          domain: 'localhost',
          path: '/',
          expires: Math.floor(Date.now() / 1000) + 86400, // 1 day
        }
      ]);
      
      // Verify authentication by checking API
      await this.validateAuthToken();
      
      console.log('✅ Dev token authentication successful');
      
    } catch (error) {
      console.error('❌ Dev token authentication failed:', error);
      throw new Error(`Dev token authentication failed: ${error}`);
    }
  }
  
  /**
   * Login using regular credentials
   * For testing actual login flow
   */
  async loginWithCredentials(
    email: string = '<EMAIL>', 
    password: string = 'test123'
  ): Promise<void> {
    console.log(`🔐 Logging in with credentials: ${email}`);
    
    try {
      // Navigate to login page
      await this.page.goto('/login');
      
      // Wait for login form
      await this.page.waitForSelector('form', { timeout: 10000 });
      
      // Fill login form
      await this.page.fill('input[name="email"], input[type="email"]', email);
      await this.page.fill('input[name="password"], input[type="password"]', password);
      
      // Submit form
      await this.page.click('button[type="submit"], .login-button, [data-testid="login-button"]');
      
      // Wait for successful login (redirect or dashboard)
      await Promise.race([
        this.page.waitForURL('/dashboard', { timeout: 15000 }),
        this.page.waitForURL('/', { timeout: 15000 }),
        this.page.waitForSelector('[data-testid="user-menu"], .user-avatar', { timeout: 15000 })
      ]);
      
      // Verify authentication
      await this.validateAuthToken();
      
      console.log('✅ Credentials authentication successful');
      
    } catch (error) {
      console.error('❌ Credentials authentication failed:', error);
      
      // Capture login error details
      const loginError = await this.page.textContent('.error, .alert-error, [role="alert"]');
      if (loginError) {
        console.error('Login error message:', loginError);
      }
      
      throw new Error(`Credentials authentication failed: ${error}`);
    }
  }
  
  /**
   * Validate authentication token by calling API
   */
  async validateAuthToken(): Promise<void> {
    try {
      const token = await this.getStoredToken();
      
      const response = await this.page.request.get('/api/v1/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      if (!response.ok()) {
        throw new Error(`Auth validation failed: ${response.status()} ${response.statusText()}`);
      }
      
      const userData = await response.json();
      console.log(`✅ Authenticated as: ${userData.email}`);
      
    } catch (error) {
      throw new Error(`Token validation failed: ${error}`);
    }
  }
  
  /**
   * Validate specific API endpoint with authentication
   */
  async validateApiEndpoint(endpoint: string): Promise<any> {
    try {
      const token = await this.getStoredToken();
      
      const response = await this.page.request.get(`/api/v1${endpoint}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });
      
      if (!response.ok()) {
        throw new Error(`API endpoint ${endpoint} validation failed: ${response.status()}`);
      }
      
      const data = await response.json();
      console.log(`✅ API endpoint ${endpoint} validated`);
      
      return data;
      
    } catch (error) {
      console.error(`❌ API endpoint ${endpoint} validation failed:`, error);
      throw error;
    }
  }
  
  /**
   * Get stored authentication token
   */
  async getStoredToken(): Promise<string> {
    const token = await this.page.evaluate(() => {
      return localStorage.getItem('access_token') || '';
    });
    
    if (!token) {
      throw new Error('No authentication token found in storage');
    }
    
    return token;
  }
  
  /**
   * Save authentication state for reuse
   */
  async saveAuthState(filePath: string): Promise<void> {
    try {
      await this.page.context().storageState({ path: filePath });
      console.log(`✅ Authentication state saved to: ${filePath}`);
    } catch (error) {
      throw new Error(`Failed to save auth state: ${error}`);
    }
  }
  
  /**
   * Load authentication state from file
   */
  async loadAuthState(filePath: string): Promise<void> {
    try {
      await fs.access(filePath);
      console.log(`✅ Authentication state loaded from: ${filePath}`);
    } catch (error) {
      throw new Error(`Failed to load auth state: ${error}`);
    }
  }
  
  /**
   * Logout and clear authentication
   */
  async logout(): Promise<void> {
    try {
      // Try to click logout button if available
      try {
        await this.page.click('[data-testid="logout-button"], .logout-button', { timeout: 5000 });
      } catch {
        // If no logout button, manually clear auth
        await this.clearAuthState();
      }
      
      // Verify logout by checking redirect to login
      await this.page.waitForURL('/login', { timeout: 10000 });
      
      console.log('✅ Logout successful');
      
    } catch (error) {
      console.error('❌ Logout failed:', error);
      throw error;
    }
  }
  
  /**
   * Clear authentication state manually
   */
  async clearAuthState(): Promise<void> {
    // Clear localStorage
    await this.page.evaluate(() => {
      localStorage.removeItem('access_token');
      localStorage.removeItem('token_type');
      localStorage.removeItem('refresh_token');
    });
    
    // Clear cookies
    await this.page.context().clearCookies();
    
    console.log('✅ Authentication state cleared');
  }
  
  /**
   * Check if user is currently authenticated
   */
  async isAuthenticated(): Promise<boolean> {
    try {
      await this.validateAuthToken();
      return true;
    } catch {
      return false;
    }
  }
  
  /**
   * Get current user information
   */
  async getCurrentUser(): Promise<any> {
    try {
      const response = await this.validateApiEndpoint('/auth/me');
      return response;
    } catch (error) {
      throw new Error(`Failed to get current user: ${error}`);
    }
  }
  
  /**
   * Switch to different user role (if supported)
   */
  async switchUserRole(role: string): Promise<void> {
    console.log(`🔄 Switching to role: ${role}`);
    
    // This would depend on the application's role switching mechanism
    // For now, we'll implement basic role-based login
    const roleCredentials = {
      'admin': { email: '<EMAIL>', password: 'test123' },
      'recruiter': { email: '<EMAIL>', password: 'test123' },
      'hr': { email: '<EMAIL>', password: 'test123' },
    };
    
    const credentials = roleCredentials[role as keyof typeof roleCredentials];
    if (!credentials) {
      throw new Error(`Unknown role: ${role}`);
    }
    
    await this.clearAuthState();
    await this.loginWithCredentials(credentials.email, credentials.password);
  }
}