/**
 * API Validator for TalentForge Pro E2E Tests
 * 
 * Comprehensive API endpoint validation:
 * - HTTP status code validation
 * - Response schema validation
 * - Performance monitoring
 * - Error detection and reporting
 */

import { APIRequestContext, expect } from '@playwright/test';
import { TestConfig } from './test-config';

interface ApiEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  description: string;
  requiresAuth: boolean;
  expectedStatus?: number;
  expectedFields?: string[];
  testData?: any;
  headers?: Record<string, string>;
}

interface ValidationResult {
  endpoint: string;
  method: string;
  success: boolean;
  status: number;
  responseTime: number;
  error?: string;
  data?: any;
}

interface ValidationReport {
  summary: {
    total: number;
    passed: number;
    failed: number;
    averageResponseTime: number;
  };
  results: ValidationResult[];
  failedEndpoints: string[];
}

export class ApiValidator {
  private config: TestConfig;
  private apiContext: APIRequestContext | null = null;
  private authToken: string = '';
  
  constructor(config: TestConfig) {
    this.config = config;
  }
  
  /**
   * Initialize API context with authentication
   */
  async initialize(apiContext: APIRequestContext, authToken?: string): Promise<void> {
    this.apiContext = apiContext;
    this.authToken = authToken || this.config.devToken;
  }
  
  /**
   * Get all critical API endpoints to validate
   */
  getCriticalEndpoints(): ApiEndpoint[] {
    return [
      // Authentication endpoints
      {
        method: 'GET',
        path: '/auth/me',
        description: 'Get current user profile',
        requiresAuth: true,
        expectedFields: ['id', 'email', 'username'],
      },
      {
        method: 'POST',
        path: '/auth/refresh',
        description: 'Refresh authentication token',
        requiresAuth: true,
        testData: { refresh_token: 'mock_refresh_token' },
      },
      
      // User management endpoints
      {
        method: 'GET',
        path: '/users/',
        description: 'Get users list with pagination',
        requiresAuth: true,
        expectedFields: ['items', 'total', 'skip', 'limit'],
      },
      {
        method: 'POST',
        path: '/users/',
        description: 'Create new user',
        requiresAuth: true,
        testData: {
          email: '<EMAIL>',
          username: 'test-api-user',
          password: 'testpassword123',
          full_name: 'Test API User',
        },
      },
      
      // Candidate management endpoints
      {
        method: 'GET',
        path: '/candidates/',
        description: 'Get candidates list with pagination',
        requiresAuth: true,
        expectedFields: ['items', 'total', 'skip', 'limit'],
      },
      {
        method: 'POST',
        path: '/candidates/',
        description: 'Create new candidate',
        requiresAuth: true,
        testData: {
          name: 'Test API Candidate',
          email: '<EMAIL>',
          phone: '******-TEST',
          experience_years: 3,
          skills: ['JavaScript', 'Testing'],
        },
      },
      {
        method: 'GET',
        path: '/candidates/search',
        description: 'Search candidates',
        requiresAuth: true,
        expectedFields: ['items', 'total'],
      },
      
      // Position management endpoints
      {
        method: 'GET',
        path: '/positions/',
        description: 'Get positions list with pagination',
        requiresAuth: true,
        expectedFields: ['items', 'total', 'skip', 'limit'],
      },
      {
        method: 'POST',
        path: '/positions/',
        description: 'Create new position',
        requiresAuth: true,
        testData: {
          title: 'Test API Position',
          department: 'Engineering',
          location: 'Remote',
          employment_type: 'full-time',
          description: 'Test position created via API validation',
          requirements: ['API Testing', 'Quality Assurance'],
        },
      },
      
      // Dashboard endpoints
      {
        method: 'GET',
        path: '/dashboard/stats',
        description: 'Get dashboard statistics',
        requiresAuth: true,
        expectedFields: ['candidates_count', 'positions_count', 'applications_count'],
      },
      {
        method: 'GET',
        path: '/dashboard/recent-activity',
        description: 'Get recent activity',
        requiresAuth: true,
        expectedFields: ['items'],
      },
      
      // AI Questionnaire endpoints
      {
        method: 'POST',
        path: '/ai-questionnaire/generate',
        description: 'Generate AI questionnaire',
        requiresAuth: true,
        testData: {
          position_id: 'test-position-id',
          candidate_id: 'test-candidate-id',
          question_count: 5,
        },
      },
      {
        method: 'GET',
        path: '/ai-questionnaire/templates',
        description: 'Get questionnaire templates',
        requiresAuth: true,
        expectedFields: ['items'],
      },
      
      // Admin endpoints
      {
        method: 'GET',
        path: '/admin/monitoring/health',
        description: 'Get system health status',
        requiresAuth: true,
        expectedFields: ['status'],
      },
      {
        method: 'GET',
        path: '/admin/users/',
        description: 'Get all users (admin only)',
        requiresAuth: true,
        expectedFields: ['items', 'total'],
      },
      
      // Health check endpoints
      {
        method: 'GET',
        path: '/health',
        description: 'Basic health check',
        requiresAuth: false,
        expectedStatus: 200,
      },
      {
        method: 'GET',
        path: '/health/db',
        description: 'Database health check',
        requiresAuth: false,
        expectedFields: ['status'],
      },
      {
        method: 'GET',
        path: '/health/redis',
        description: 'Redis cache health check',
        requiresAuth: false,
        expectedFields: ['status'],
      },
    ];
  }
  
  /**
   * Validate all critical endpoints
   */
  async validateAllEndpoints(): Promise<ValidationReport> {
    if (!this.apiContext) {
      throw new Error('API context not initialized');
    }
    
    console.log('🔍 Starting comprehensive API validation...');
    
    const endpoints = this.getCriticalEndpoints();
    const results: ValidationResult[] = [];
    
    for (const endpoint of endpoints) {
      const result = await this.validateEndpoint(endpoint);
      results.push(result);
      
      // Log progress
      const status = result.success ? '✅' : '❌';
      console.log(`${status} ${endpoint.method} ${endpoint.path} (${result.responseTime}ms)`);
    }
    
    const report = this.generateValidationReport(results);
    console.log(`\n📊 API Validation Summary: ${report.summary.passed}/${report.summary.total} passed`);
    
    return report;
  }
  
  /**
   * Validate a single endpoint
   */
  async validateEndpoint(endpoint: ApiEndpoint): Promise<ValidationResult> {
    const startTime = Date.now();
    
    try {
      if (!this.apiContext) {
        throw new Error('API context not initialized');
      }
      
      const headers: Record<string, string> = {
        'Content-Type': 'application/json',
        ...endpoint.headers,
      };
      
      if (endpoint.requiresAuth) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
      }
      
      let response;
      const fullPath = `${this.config.apiBaseUrl}${endpoint.path}`;
      
      switch (endpoint.method) {
        case 'GET':
          response = await this.apiContext.get(fullPath, { headers });
          break;
        case 'POST':
          response = await this.apiContext.post(fullPath, {
            headers,
            data: endpoint.testData,
          });
          break;
        case 'PUT':
          response = await this.apiContext.put(fullPath, {
            headers,
            data: endpoint.testData,
          });
          break;
        case 'DELETE':
          response = await this.apiContext.delete(fullPath, { headers });
          break;
        case 'PATCH':
          response = await this.apiContext.patch(fullPath, {
            headers,
            data: endpoint.testData,
          });
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${endpoint.method}`);
      }
      
      const responseTime = Date.now() - startTime;
      const expectedStatus = endpoint.expectedStatus || (endpoint.method === 'POST' ? 201 : 200);
      
      // Check if response status is acceptable
      const isSuccessStatus = response.status() >= 200 && response.status() < 300;
      const isExpectedStatus = response.status() === expectedStatus;
      
      if (!isSuccessStatus) {
        const errorBody = await response.text().catch(() => 'Could not read response body');
        return {
          endpoint: endpoint.path,
          method: endpoint.method,
          success: false,
          status: response.status(),
          responseTime,
          error: `HTTP ${response.status()}: ${errorBody}`,
        };
      }
      
      let responseData = null;
      try {
        responseData = await response.json();
      } catch (error) {
        // Some endpoints might not return JSON
        responseData = await response.text();
      }
      
      // Validate expected fields if specified
      if (endpoint.expectedFields && typeof responseData === 'object' && responseData !== null) {
        const missingFields = endpoint.expectedFields.filter(field => 
          !(field in responseData)
        );
        
        if (missingFields.length > 0) {
          return {
            endpoint: endpoint.path,
            method: endpoint.method,
            success: false,
            status: response.status(),
            responseTime,
            error: `Missing expected fields: ${missingFields.join(', ')}`,
            data: responseData,
          };
        }
      }
      
      return {
        endpoint: endpoint.path,
        method: endpoint.method,
        success: true,
        status: response.status(),
        responseTime,
        data: responseData,
      };
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        endpoint: endpoint.path,
        method: endpoint.method,
        success: false,
        status: 0,
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
  
  /**
   * Validate specific endpoint by path
   */
  async validateSpecificEndpoint(path: string): Promise<ValidationResult | null> {
    const endpoint = this.getCriticalEndpoints().find(e => e.path === path);
    
    if (!endpoint) {
      throw new Error(`Endpoint '${path}' not found in critical endpoints list`);
    }
    
    return await this.validateEndpoint(endpoint);
  }
  
  /**
   * Generate validation report
   */
  private generateValidationReport(results: ValidationResult[]): ValidationReport {
    const total = results.length;
    const passed = results.filter(r => r.success).length;
    const failed = total - passed;
    const averageResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / total;
    const failedEndpoints = results.filter(r => !r.success).map(r => `${r.method} ${r.endpoint}`);
    
    return {
      summary: {
        total,
        passed,
        failed,
        averageResponseTime: Math.round(averageResponseTime),
      },
      results,
      failedEndpoints,
    };
  }
  
  /**
   * Generate detailed API validation report in Markdown format
   */
  generateMarkdownReport(report: ValidationReport): string {
    let markdown = '# API Validation Report\n\n';
    markdown += `**Generated:** ${new Date().toISOString()}\n\n`;
    
    // Summary
    markdown += '## Summary\n\n';
    markdown += `- **Total Endpoints:** ${report.summary.total}\n`;
    markdown += `- **Passed:** ${report.summary.passed}\n`;
    markdown += `- **Failed:** ${report.summary.failed}\n`;
    markdown += `- **Success Rate:** ${((report.summary.passed / report.summary.total) * 100).toFixed(1)}%\n`;
    markdown += `- **Average Response Time:** ${report.summary.averageResponseTime}ms\n\n`;
    
    // Failed endpoints
    if (report.failedEndpoints.length > 0) {
      markdown += '## Failed Endpoints\n\n';
      report.failedEndpoints.forEach(endpoint => {
        const result = report.results.find(r => `${r.method} ${r.endpoint}` === endpoint);
        markdown += `- **${endpoint}** - ${result?.error || 'Unknown error'}\n`;
      });
      markdown += '\n';
    }
    
    // Detailed results
    markdown += '## Detailed Results\n\n';
    markdown += '| Endpoint | Method | Status | Response Time | Result |\n';
    markdown += '|----------|--------|--------|---------------|--------|\n';
    
    report.results.forEach(result => {
      const status = result.success ? '✅ Pass' : '❌ Fail';
      const httpStatus = result.status || 'N/A';
      const responseTime = `${result.responseTime}ms`;
      const error = result.error ? ` (${result.error})` : '';
      
      markdown += `| ${result.endpoint} | ${result.method} | ${httpStatus} | ${responseTime} | ${status}${error} |\n`;
    });
    
    markdown += '\n';
    
    // Performance analysis
    markdown += '## Performance Analysis\n\n';
    
    const sortedByResponseTime = [...report.results].sort((a, b) => b.responseTime - a.responseTime);
    const slowestEndpoints = sortedByResponseTime.slice(0, 5);
    
    markdown += '### Slowest Endpoints\n\n';
    slowestEndpoints.forEach((result, index) => {
      markdown += `${index + 1}. **${result.method} ${result.endpoint}** - ${result.responseTime}ms\n`;
    });
    
    markdown += '\n---\n';
    markdown += '*This report was generated automatically by the Playwright API validator*\n';
    
    return markdown;
  }
}