/**
 * Test Data Manager for TalentForge Pro E2E Tests
 * 
 * Manages test data lifecycle:
 * - Test data initialization
 * - Cleanup operations
 * - Performance metrics collection
 * - Setup metadata management
 */

import fs from 'fs/promises';
import path from 'path';

interface SetupMetadata {
  setupTime: number;
  timestamp: string;
  servicesHealth: any;
  endpointsValidated: string[];
}

interface PerformanceMetrics {
  testDuration: number;
  averageResponseTime: number;
  slowestEndpoints: Array<{endpoint: string; responseTime: number}>;
  errorRate: number;
  memoryUsage?: NodeJS.MemoryUsage;
}

export class TestDataManager {
  private readonly reportsDir: string;
  private readonly dataDir: string;
  
  constructor() {
    this.reportsDir = path.join(__dirname, '../reports');
    this.dataDir = path.join(__dirname, '../data');
  }
  
  /**
   * Initialize test data and directories
   */
  async initializeTestData(): Promise<void> {
    // Ensure required directories exist
    await this.ensureDirectories();
    
    // Initialize test fixtures
    await this.createTestFixtures();
    
    console.log('✅ Test data initialized');
  }
  
  /**
   * Ensure required directories exist
   */
  private async ensureDirectories(): Promise<void> {
    const directories = [
      this.reportsDir,
      this.dataDir,
      path.join(this.reportsDir, 'screenshots'),
      path.join(this.reportsDir, 'videos'),
      path.join(this.reportsDir, 'traces'),
    ];
    
    for (const dir of directories) {
      try {
        await fs.mkdir(dir, { recursive: true });
      } catch (error) {
        // Directory might already exist, ignore error
      }
    }
  }
  
  /**
   * Create test fixtures and sample data
   */
  private async createTestFixtures(): Promise<void> {
    const fixtures = {
      testCandidates: [
        {
          id: 'test-candidate-1',
          name: 'John Doe (Test)',
          email: '<EMAIL>',
          phone: '******-0101',
          position: 'Software Engineer',
          experience: 5,
          skills: ['JavaScript', 'Python', 'React'],
          status: 'active',
        },
        {
          id: 'test-candidate-2',
          name: 'Jane Smith (Test)',
          email: '<EMAIL>',
          phone: '******-0102',
          position: 'Senior Developer',
          experience: 8,
          skills: ['TypeScript', 'Node.js', 'PostgreSQL'],
          status: 'pending',
        },
      ],
      testPositions: [
        {
          id: 'test-position-1',
          title: 'Frontend Developer (Test)',
          department: 'Engineering',
          location: 'Remote',
          type: 'full-time',
          status: 'active',
          requirements: ['React', 'TypeScript', '3+ years experience'],
        },
        {
          id: 'test-position-2',
          title: 'Backend Engineer (Test)',
          department: 'Engineering',
          location: 'San Francisco',
          type: 'full-time',
          status: 'draft',
          requirements: ['Python', 'FastAPI', '5+ years experience'],
        },
      ],
      testUsers: [
        {
          id: 'test-admin',
          email: '<EMAIL>',
          role: 'admin',
          permissions: ['read', 'write', 'delete', 'admin'],
        },
        {
          id: 'test-recruiter',
          email: '<EMAIL>',
          role: 'recruiter',
          permissions: ['read', 'write'],
        },
      ],
      searchQueries: {
        byName: 'John Doe',
        bySkill: 'JavaScript',
        byPosition: 'Software Engineer',
        noResults: 'NonExistentCandidate12345',
      },
      filterOptions: {
        status: {
          active: 'active',
          pending: 'pending',
          inactive: 'inactive',
        },
        experience: {
          min: 2,
          max: 10,
        },
        skills: ['JavaScript', 'Python', 'React', 'TypeScript'],
      },
    };
    
    await fs.writeFile(
      path.join(this.dataDir, 'test-fixtures.json'),
      JSON.stringify(fixtures, null, 2)
    );
  }
  
  /**
   * Get test fixtures
   */
  async getTestFixtures(): Promise<any> {
    try {
      const fixturesPath = path.join(this.dataDir, 'test-fixtures.json');
      const data = await fs.readFile(fixturesPath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      console.warn('⚠️ Could not load test fixtures:', error);
      return {};
    }
  }
  
  /**
   * Save setup metadata
   */
  async saveSetupMetadata(metadata: SetupMetadata): Promise<void> {
    const filePath = path.join(this.reportsDir, 'setup-metadata.json');
    await fs.writeFile(filePath, JSON.stringify(metadata, null, 2));
  }
  
  /**
   * Get setup metadata
   */
  async getSetupMetadata(): Promise<SetupMetadata | null> {
    try {
      const filePath = path.join(this.reportsDir, 'setup-metadata.json');
      const data = await fs.readFile(filePath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      return null;
    }
  }
  
  /**
   * Collect performance metrics
   */
  async collectPerformanceMetrics(): Promise<PerformanceMetrics> {
    const setupMetadata = await this.getSetupMetadata();
    
    // Calculate test duration
    const testDuration = setupMetadata 
      ? Date.now() - new Date(setupMetadata.timestamp).getTime()
      : 0;
    
    // Get memory usage
    const memoryUsage = process.memoryUsage();
    
    // Mock performance metrics (in a real implementation, these would be collected during test runs)
    const metrics: PerformanceMetrics = {
      testDuration,
      averageResponseTime: 150, // Mock value
      slowestEndpoints: [
        { endpoint: '/api/v1/candidates/', responseTime: 300 },
        { endpoint: '/api/v1/dashboard/stats', responseTime: 250 },
      ],
      errorRate: 0, // Mock value
      memoryUsage,
    };
    
    // Save metrics
    await fs.writeFile(
      path.join(this.reportsDir, 'performance-metrics.json'),
      JSON.stringify(metrics, null, 2)
    );
    
    return metrics;
  }
  
  /**
   * Clean up test data and temporary files
   */
  async cleanupTestData(): Promise<void> {
    try {
      // Clean up temporary test files
      const tempFiles = [
        'auth-state.json',
        path.join(this.dataDir, 'temp-*.json'),
      ];
      
      for (const filePattern of tempFiles) {
        try {
          if (filePattern.includes('*')) {
            // Handle glob patterns (simple implementation)
            const dir = path.dirname(filePattern);
            const pattern = path.basename(filePattern);
            const files = await fs.readdir(dir);
            
            for (const file of files) {
              if (file.match(pattern.replace('*', '.*'))) {
                await fs.unlink(path.join(dir, file));
              }
            }
          } else {
            await fs.unlink(filePattern);
          }
        } catch (error) {
          // Ignore errors for files that don't exist
        }
      }
      
      // Clean up old report files (keep last 5)
      await this.cleanupOldReports();
      
      console.log('✅ Test data cleanup completed');
      
    } catch (error) {
      console.warn('⚠️ Test data cleanup failed:', error);
    }
  }
  
  /**
   * Clean up old report files
   */
  private async cleanupOldReports(): Promise<void> {
    try {
      const reportFiles = await fs.readdir(this.reportsDir);
      
      // Filter for dated report files
      const datedReports = reportFiles
        .filter(file => file.match(/^\d{4}-\d{2}-\d{2}-.*\.(json|md)$/))
        .sort()
        .reverse();
      
      // Keep only the last 5 reports
      const filesToDelete = datedReports.slice(5);
      
      for (const file of filesToDelete) {
        await fs.unlink(path.join(this.reportsDir, file));
      }
      
      if (filesToDelete.length > 0) {
        console.log(`🗑️ Cleaned up ${filesToDelete.length} old report files`);
      }
      
    } catch (error) {
      console.warn('⚠️ Could not clean up old reports:', error);
    }
  }
  
  /**
   * Generate test data summary
   */
  async generateDataSummary(): Promise<string> {
    const fixtures = await this.getTestFixtures();
    const setupMetadata = await this.getSetupMetadata();
    const performanceMetrics = await this.collectPerformanceMetrics();
    
    let summary = '# Test Data Summary\n\n';
    summary += `**Generated:** ${new Date().toISOString()}\n\n`;
    
    if (setupMetadata) {
      summary += '## Setup Information\n\n';
      summary += `- **Setup Time:** ${setupMetadata.setupTime}ms\n`;
      summary += `- **Timestamp:** ${setupMetadata.timestamp}\n`;
      summary += `- **Validated Endpoints:** ${setupMetadata.endpointsValidated.length}\n\n`;
    }
    
    if (fixtures.testCandidates) {
      summary += '## Test Candidates\n\n';
      summary += `- **Total:** ${fixtures.testCandidates.length}\n`;
      summary += `- **Active:** ${fixtures.testCandidates.filter((c: any) => c.status === 'active').length}\n\n`;
    }
    
    if (fixtures.testPositions) {
      summary += '## Test Positions\n\n';
      summary += `- **Total:** ${fixtures.testPositions.length}\n`;
      summary += `- **Active:** ${fixtures.testPositions.filter((p: any) => p.status === 'active').length}\n\n`;
    }
    
    summary += '## Performance Metrics\n\n';
    summary += `- **Test Duration:** ${performanceMetrics.testDuration}ms\n`;
    summary += `- **Average Response Time:** ${performanceMetrics.averageResponseTime}ms\n`;
    summary += `- **Error Rate:** ${performanceMetrics.errorRate}%\n\n`;
    
    if (performanceMetrics.memoryUsage) {
      summary += '## Memory Usage\n\n';
      summary += `- **Heap Used:** ${Math.round(performanceMetrics.memoryUsage.heapUsed / 1024 / 1024)}MB\n`;
      summary += `- **Heap Total:** ${Math.round(performanceMetrics.memoryUsage.heapTotal / 1024 / 1024)}MB\n\n`;
    }
    
    return summary;
  }
}