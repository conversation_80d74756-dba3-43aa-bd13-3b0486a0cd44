/**
 * Test Configuration for TalentForge Pro E2E Tests
 * 
 * Centralizes test configuration and environment settings
 */

export class TestConfig {
  // Environment URLs
  readonly baseUrl: string;
  readonly apiBaseUrl: string;
  
  // Authentication
  readonly devToken: string;
  readonly defaultCredentials: {
    email: string;
    password: string;
  };
  
  // Timeouts
  readonly defaultTimeout: number;
  readonly navigationTimeout: number;
  readonly apiTimeout: number;
  
  // Test data
  readonly testUserRoles: string[];
  
  // Screenshots and videos
  readonly captureScreenshots: boolean;
  readonly captureVideos: boolean;
  
  constructor() {
    this.baseUrl = process.env.BASE_URL || 'http://localhost:8088';
    this.apiBaseUrl = process.env.API_BASE_URL || 'http://localhost:8088/api/v1';
    
    this.devToken = 'dev_bypass_token_2025_talentforge';
    this.defaultCredentials = {
      email: '<EMAIL>',
      password: 'test123',
    };
    
    this.defaultTimeout = parseInt(process.env.TEST_TIMEOUT || '30000');
    this.navigationTimeout = parseInt(process.env.NAV_TIMEOUT || '15000');
    this.apiTimeout = parseInt(process.env.API_TIMEOUT || '10000');
    
    this.testUserRoles = ['admin', 'recruiter', 'hr'];
    
    this.captureScreenshots = process.env.CAPTURE_SCREENSHOTS !== 'false';
    this.captureVideos = process.env.CAPTURE_VIDEOS !== 'false';
  }
  
  /**
   * Get configuration for specific environment
   */
  getEnvironmentConfig(env: string = 'development') {
    const configs = {
      development: {
        baseUrl: 'http://localhost:8088',
        apiBaseUrl: 'http://localhost:8088/api/v1',
        useDevToken: true,
      },
      staging: {
        baseUrl: process.env.STAGING_URL || 'https://staging.talentforge.pro',
        apiBaseUrl: process.env.STAGING_API_URL || 'https://staging.talentforge.pro/api/v1',
        useDevToken: false,
      },
      production: {
        baseUrl: process.env.PROD_URL || 'https://talentforge.pro',
        apiBaseUrl: process.env.PROD_API_URL || 'https://talentforge.pro/api/v1',
        useDevToken: false,
      },
    };
    
    return configs[env as keyof typeof configs] || configs.development;
  }
  
  /**
   * Get credentials for specific role
   */
  getRoleCredentials(role: string) {
    const credentials = {
      admin: {
        email: '<EMAIL>',
        password: 'test123',
      },
      recruiter: {
        email: '<EMAIL>',
        password: 'test123',
      },
      hr: {
        email: '<EMAIL>',
        password: 'test123',
      },
    };
    
    return credentials[role as keyof typeof credentials] || this.defaultCredentials;
  }
}