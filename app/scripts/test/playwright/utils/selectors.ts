/**
 * Comprehensive Selector Utilities for TalentForge Pro E2E Tests
 * 
 * Centralized selector management for consistent and maintainable test automation:
 * - Dynamic selector strategies
 * - Fallback selector chains
 * - Element discovery patterns
 * - Cross-browser compatibility
 */

import { Page, Locator } from '@playwright/test';

export interface SelectorStrategy {
  primary: string;
  fallbacks: string[];
  description: string;
}

export interface ElementFinder {
  findElement(page: Page, timeout?: number): Promise<Locator | null>;
  findElements(page: Page, timeout?: number): Promise<Locator[]>;
  isVisible(page: Page, timeout?: number): Promise<boolean>;
}

export class SmartSelector implements ElementFinder {
  private strategies: SelectorStrategy[];
  
  constructor(strategies: SelectorStrategy[]) {
    this.strategies = strategies;
  }
  
  async findElement(page: Page, timeout: number = 5000): Promise<Locator | null> {
    for (const strategy of this.strategies) {
      // Try primary selector first
      const primaryLocator = page.locator(strategy.primary);
      if (await primaryLocator.isVisible({ timeout: Math.min(timeout / this.strategies.length, 2000) })) {
        return primaryLocator;
      }
      
      // Try fallback selectors
      for (const fallback of strategy.fallbacks) {
        const fallbackLocator = page.locator(fallback);
        if (await fallbackLocator.isVisible({ timeout: 1000 })) {
          return fallbackLocator;
        }
      }
    }
    
    return null;
  }
  
  async findElements(page: Page, timeout: number = 5000): Promise<Locator[]> {
    const elements: Locator[] = [];
    
    for (const strategy of this.strategies) {
      const allSelectors = [strategy.primary, ...strategy.fallbacks];
      
      for (const selector of allSelectors) {
        const locator = page.locator(selector);
        if (await locator.count() > 0) {
          elements.push(locator);
        }
      }
    }
    
    return elements;
  }
  
  async isVisible(page: Page, timeout: number = 5000): Promise<boolean> {
    const element = await this.findElement(page, timeout);
    return element !== null;
  }
}

export class TalentForgeSelectors {
  // Authentication selectors
  static readonly AUTH = {
    loginForm: new SmartSelector([
      {
        primary: '[data-testid="login-form"]',
        fallbacks: ['form:has-text("Login")', 'form:has(input[type="email"])', '.login-form'],
        description: 'Main login form'
      }
    ]),
    
    emailInput: new SmartSelector([
      {
        primary: '[data-testid="email-input"]',
        fallbacks: ['input[name="email"]', 'input[type="email"]', 'input[placeholder*="email"]'],
        description: 'Email input field'
      }
    ]),
    
    passwordInput: new SmartSelector([
      {
        primary: '[data-testid="password-input"]',
        fallbacks: ['input[name="password"]', 'input[type="password"]', 'input[placeholder*="password"]'],
        description: 'Password input field'
      }
    ]),
    
    loginButton: new SmartSelector([
      {
        primary: '[data-testid="login-button"]',
        fallbacks: ['button[type="submit"]', 'button:has-text("Login")', 'button:has-text("Sign In")', '.login-button'],
        description: 'Login submit button'
      }
    ]),
    
    logoutButton: new SmartSelector([
      {
        primary: '[data-testid="logout-button"]',
        fallbacks: ['button:has-text("Logout")', 'button:has-text("Sign Out")', '.logout-button', '[title="Logout"]'],
        description: 'Logout button'
      }
    ])
  };
  
  // Navigation selectors
  static readonly NAVIGATION = {
    mainMenu: new SmartSelector([
      {
        primary: '[data-testid="main-nav"]',
        fallbacks: ['nav[role="navigation"]', '.main-navigation', '#main-menu', '.navbar'],
        description: 'Main navigation menu'
      }
    ]),
    
    dashboardLink: new SmartSelector([
      {
        primary: '[data-testid="nav-dashboard"]',
        fallbacks: ['a:has-text("Dashboard")', 'a[href*="dashboard"]', '.nav-dashboard'],
        description: 'Dashboard navigation link'
      }
    ]),
    
    candidatesLink: new SmartSelector([
      {
        primary: '[data-testid="nav-candidates"]',
        fallbacks: ['a:has-text("Candidates")', 'a[href*="candidates"]', '.nav-candidates'],
        description: 'Candidates navigation link'
      }
    ]),
    
    jobsLink: new SmartSelector([
      {
        primary: '[data-testid="nav-jobs"]',
        fallbacks: ['a:has-text("Jobs")', 'a:has-text("Positions")', 'a[href*="jobs"]', 'a[href*="positions"]'],
        description: 'Jobs/Positions navigation link'
      }
    ]),
    
    adminLink: new SmartSelector([
      {
        primary: '[data-testid="nav-admin"]',
        fallbacks: ['a:has-text("Admin")', 'a[href*="admin"]', '.admin-nav'],
        description: 'Admin panel navigation link'
      }
    ]),
    
    mobileMenuToggle: new SmartSelector([
      {
        primary: '[data-testid="mobile-menu-toggle"]',
        fallbacks: ['.hamburger-menu', '.mobile-nav-toggle', 'button:has-text("Menu")', '[aria-label="Toggle navigation"]'],
        description: 'Mobile menu toggle button'
      }
    ])
  };
  
  // Dashboard selectors
  static readonly DASHBOARD = {
    container: new SmartSelector([
      {
        primary: '[data-testid="dashboard"]',
        fallbacks: ['.dashboard-container', '.main-dashboard', '#dashboard', '.dashboard-content'],
        description: 'Main dashboard container'
      }
    ]),
    
    metricCards: new SmartSelector([
      {
        primary: '[data-testid*="metric-card"]',
        fallbacks: ['.metric-card', '.dashboard-card', '.stat-card', '.kpi-card'],
        description: 'Dashboard metric cards'
      }
    ]),
    
    charts: new SmartSelector([
      {
        primary: '[data-testid*="chart"]',
        fallbacks: ['canvas', 'svg', '.chart', '.graph', '.recharts-container', '.apexcharts-canvas'],
        description: 'Dashboard charts and visualizations'
      }
    ]),
    
    refreshButton: new SmartSelector([
      {
        primary: '[data-testid="refresh-dashboard"]',
        fallbacks: ['button:has-text("Refresh")', '.refresh-button', '[title="Refresh"]', '.reload-data'],
        description: 'Dashboard refresh button'
      }
    ])
  };
  
  // Candidate selectors
  static readonly CANDIDATES = {
    listContainer: new SmartSelector([
      {
        primary: '[data-testid="candidates-list"]',
        fallbacks: ['.candidates-container', '.candidates-grid', 'table:has-text("Candidate")', '.candidate-table'],
        description: 'Candidates list container'
      }
    ]),
    
    candidateRows: new SmartSelector([
      {
        primary: '[data-testid*="candidate-row"]',
        fallbacks: ['tbody tr', '.candidate-item', '.candidate-card', '.candidate-row'],
        description: 'Individual candidate rows/items'
      }
    ]),
    
    createButton: new SmartSelector([
      {
        primary: '[data-testid="create-candidate-btn"]',
        fallbacks: ['button:has-text("Create Candidate")', 'button:has-text("Add Candidate")', '.create-candidate-button', 'a[href*="create"]'],
        description: 'Create candidate button'
      }
    ]),
    
    searchInput: new SmartSelector([
      {
        primary: '[data-testid="candidate-search"]',
        fallbacks: ['input[placeholder*="search"]', '.search-input', '[name="search"]'],
        description: 'Candidate search input'
      }
    ]),
    
    nameField: new SmartSelector([
      {
        primary: '[data-testid="candidate-name"]',
        fallbacks: ['input[name="name"]', 'input[placeholder*="name"]', '[name="full_name"]'],
        description: 'Candidate name input field'
      }
    ]),
    
    emailField: new SmartSelector([
      {
        primary: '[data-testid="candidate-email"]',
        fallbacks: ['input[name="email"]', 'input[type="email"]', 'input[placeholder*="email"]'],
        description: 'Candidate email input field'
      }
    ])
  };
  
  // Job selectors
  static readonly JOBS = {
    listContainer: new SmartSelector([
      {
        primary: '[data-testid="jobs-list"]',
        fallbacks: ['.jobs-container', '.positions-list', 'table:has-text("Job")', 'table:has-text("Position")'],
        description: 'Jobs list container'
      }
    ]),
    
    jobRows: new SmartSelector([
      {
        primary: '[data-testid*="job-row"]',
        fallbacks: ['tbody tr', '.job-item', '.position-item', '.job-card'],
        description: 'Individual job rows/items'
      }
    ]),
    
    createButton: new SmartSelector([
      {
        primary: '[data-testid="create-job-btn"]',
        fallbacks: ['button:has-text("Create Job")', 'button:has-text("Post Job")', 'button:has-text("Add Job")', '.create-job-button'],
        description: 'Create job button'
      }
    ]),
    
    titleField: new SmartSelector([
      {
        primary: '[data-testid="job-title"]',
        fallbacks: ['input[name="title"]', 'input[placeholder*="title"]', '[name="position_title"]'],
        description: 'Job title input field'
      }
    ]),
    
    departmentField: new SmartSelector([
      {
        primary: '[data-testid="department"]',
        fallbacks: ['select[name="department"]', 'input[name="department"]', '[name="dept"]'],
        description: 'Department field'
      }
    ])
  };
  
  // Form selectors
  static readonly FORMS = {
    submitButton: new SmartSelector([
      {
        primary: 'button[type="submit"]',
        fallbacks: ['[data-testid*="submit"]', 'button:has-text("Save")', 'button:has-text("Create")', '.submit-button'],
        description: 'Form submit button'
      }
    ]),
    
    cancelButton: new SmartSelector([
      {
        primary: 'button:has-text("Cancel")',
        fallbacks: ['[data-testid="cancel"]', '.cancel-button', 'button:has-text("Close")'],
        description: 'Form cancel button'
      }
    ]),
    
    validationErrors: new SmartSelector([
      {
        primary: '.validation-error',
        fallbacks: ['.field-error', '.error-message', '[role="alert"]', '.invalid-feedback'],
        description: 'Form validation error messages'
      }
    ]),
    
    successMessage: new SmartSelector([
      {
        primary: '.success-message',
        fallbacks: ['.alert-success', '.notification-success', 'text=successfully', '[role="status"]'],
        description: 'Success feedback messages'
      }
    ])
  };
  
  // Loading and status selectors
  static readonly STATUS = {
    loadingIndicator: new SmartSelector([
      {
        primary: '[data-testid="loading"]',
        fallbacks: ['.loading', '.spinner', 'text=Loading...', '.skeleton-loader'],
        description: 'Loading indicator'
      }
    ]),
    
    errorMessage: new SmartSelector([
      {
        primary: '[data-testid="error-message"]',
        fallbacks: ['.error-message', '.alert-error', '.notification-error', '[role="alert"]:has-text("error")'],
        description: 'Error message display'
      }
    ]),
    
    emptyState: new SmartSelector([
      {
        primary: '[data-testid="empty-state"]',
        fallbacks: ['.empty-state', '.no-data', 'text=No data available', 'text=No results found'],
        description: 'Empty state indicator'
      }
    ])
  };
  
  // Language and accessibility selectors
  static readonly LANGUAGE = {
    languageSwitcher: new SmartSelector([
      {
        primary: '[data-testid="language-switcher"]',
        fallbacks: ['.language-selector', 'select[name*="language"]', '.locale-switcher'],
        description: 'Language switcher component'
      }
    ]),
    
    chineseOption: new SmartSelector([
      {
        primary: '[data-value="zh"]',
        fallbacks: ['text=中文', 'text=Chinese', 'option[value="zh"]'],
        description: 'Chinese language option'
      }
    ]),
    
    englishOption: new SmartSelector([
      {
        primary: '[data-value="en"]',
        fallbacks: ['text=English', 'text=EN', 'option[value="en"]'],
        description: 'English language option'
      }
    ])
  };
  
  // Admin panel selectors
  static readonly ADMIN = {
    dashboard: new SmartSelector([
      {
        primary: '[data-testid="admin-dashboard"]',
        fallbacks: ['.admin-dashboard', '.admin-panel', '#admin-dashboard'],
        description: 'Admin dashboard container'
      }
    ]),
    
    userManagement: new SmartSelector([
      {
        primary: '[data-testid="admin-users"]',
        fallbacks: ['a:has-text("User Management")', 'a[href*="admin/users"]', '.user-management'],
        description: 'User management section'
      }
    ]),
    
    monitoring: new SmartSelector([
      {
        primary: '[data-testid="admin-monitoring"]',
        fallbacks: ['a:has-text("Monitoring")', 'a[href*="monitoring"]', '.system-monitoring'],
        description: 'System monitoring section'
      }
    ])
  };
  
  // AI and ML selectors
  static readonly AI = {
    questionnaire: new SmartSelector([
      {
        primary: '[data-testid="ai-questionnaire"]',
        fallbacks: ['text=AI Questionnaire', 'a:has-text("AI")', '.ai-questionnaire'],
        description: 'AI questionnaire interface'
      }
    ]),
    
    generateButton: new SmartSelector([
      {
        primary: '[data-testid="generate-ai"]',
        fallbacks: ['button:has-text("Generate")', 'button:has-text("AI Generate")', '.ai-generate-button'],
        description: 'AI generation trigger button'
      }
    ]),
    
    scoreDisplay: new SmartSelector([
      {
        primary: '[data-testid*="ai-score"]',
        fallbacks: ['.ai-score', '.score-result', 'text=Score:', '.ml-score'],
        description: 'AI score display elements'
      }
    ])
  };
  
  /**
   * Utility method to wait for any element from multiple selectors
   */
  static async waitForAnyElement(page: Page, selectors: SmartSelector[], timeout: number = 10000): Promise<Locator | null> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      for (const selector of selectors) {
        const element = await selector.findElement(page, 1000);
        if (element) {
          return element;
        }
      }
      await page.waitForTimeout(500);
    }
    
    return null;
  }
  
  /**
   * Utility method to click the first available element from multiple selectors
   */
  static async clickFirstAvailable(page: Page, selectors: SmartSelector[], timeout: number = 10000): Promise<boolean> {
    const element = await this.waitForAnyElement(page, selectors, timeout);
    if (element) {
      await element.click();
      return true;
    }
    return false;
  }
  
  /**
   * Utility method to fill the first available input from multiple selectors
   */
  static async fillFirstAvailable(page: Page, selectors: SmartSelector[], value: string, timeout: number = 10000): Promise<boolean> {
    const element = await this.waitForAnyElement(page, selectors, timeout);
    if (element) {
      await element.fill(value);
      return true;
    }
    return false;
  }
}