/**
 * Health Checker for TalentForge Pro Docker Services
 * 
 * Validates that all required Docker services are running and healthy
 * before starting E2E tests.
 */

import { request } from '@playwright/test';

interface ServiceHealth {
  name: string;
  url: string;
  healthy: boolean;
  responseTime: number;
  error?: string;
}

interface HealthStatus {
  allHealthy: boolean;
  services: ServiceHealth[];
  failedServices: string[];
  totalResponseTime: number;
}

export class HealthChecker {
  private readonly services = [
    {
      name: 'Frontend',
      url: 'http://localhost:8088',
      healthEndpoint: '/',
    },
    {
      name: 'Backend API',
      url: 'http://localhost:8088/api/v1',
      healthEndpoint: '/health',
    },
    {
      name: 'PostgreSQL Database',
      url: 'http://localhost:8088/api/v1',
      healthEndpoint: '/health/db',
    },
    {
      name: 'Redis Cache',
      url: 'http://localhost:8088/api/v1',
      healthEndpoint: '/health/redis',
    },
    {
      name: 'MinIO Storage',
      url: 'http://localhost:8088/api/v1',
      healthEndpoint: '/health/storage',
    },
  ];
  
  /**
   * Check health of all services
   */
  async checkAllServices(): Promise<HealthStatus> {
    console.log('🏥 Checking health of all services...');
    
    const healthPromises = this.services.map(service => this.checkServiceHealth(service));
    const serviceHealthResults = await Promise.all(healthPromises);
    
    const allHealthy = serviceHealthResults.every(result => result.healthy);
    const failedServices = serviceHealthResults
      .filter(result => !result.healthy)
      .map(result => result.name);
    
    const totalResponseTime = serviceHealthResults.reduce((sum, result) => sum + result.responseTime, 0);
    
    const healthStatus: HealthStatus = {
      allHealthy,
      services: serviceHealthResults,
      failedServices,
      totalResponseTime,
    };
    
    this.logHealthStatus(healthStatus);
    
    return healthStatus;
  }
  
  /**
   * Check health of a specific service
   */
  private async checkServiceHealth(service: {
    name: string;
    url: string;
    healthEndpoint: string;
  }): Promise<ServiceHealth> {
    const startTime = Date.now();
    
    try {
      const apiRequestContext = await request.newContext();
      const fullUrl = `${service.url}${service.healthEndpoint}`;
      
      const response = await apiRequestContext.get(fullUrl, {
        timeout: 10000,
        headers: {
          'Accept': 'application/json',
        },
      });
      
      const responseTime = Date.now() - startTime;
      
      await apiRequestContext.dispose();
      
      if (response.ok()) {
        return {
          name: service.name,
          url: service.url,
          healthy: true,
          responseTime,
        };
      } else {
        return {
          name: service.name,
          url: service.url,
          healthy: false,
          responseTime,
          error: `HTTP ${response.status()}: ${response.statusText()}`,
        };
      }
      
    } catch (error) {
      const responseTime = Date.now() - startTime;
      
      return {
        name: service.name,
        url: service.url,
        healthy: false,
        responseTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
  
  /**
   * Check specific service by name
   */
  async checkService(serviceName: string): Promise<ServiceHealth | null> {
    const service = this.services.find(s => 
      s.name.toLowerCase() === serviceName.toLowerCase()
    );
    
    if (!service) {
      throw new Error(`Service '${serviceName}' not found`);
    }
    
    return await this.checkServiceHealth(service);
  }
  
  /**
   * Wait for all services to be healthy
   */
  async waitForHealthy(timeoutMs: number = 60000, checkIntervalMs: number = 5000): Promise<boolean> {
    console.log(`⏳ Waiting for services to be healthy (timeout: ${timeoutMs}ms)...`);
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      const healthStatus = await this.checkAllServices();
      
      if (healthStatus.allHealthy) {
        console.log('✅ All services are healthy!');
        return true;
      }
      
      console.log(`⚠️ Services still unhealthy: ${healthStatus.failedServices.join(', ')}`);
      console.log(`⏳ Waiting ${checkIntervalMs}ms before next check...`);
      
      await new Promise(resolve => setTimeout(resolve, checkIntervalMs));
    }
    
    console.log('❌ Timeout waiting for services to be healthy');
    return false;
  }
  
  /**
   * Get system resource usage (if available)
   */
  async getSystemStats(): Promise<any> {
    try {
      const apiRequestContext = await request.newContext();
      
      const response = await apiRequestContext.get('http://localhost:8088/api/v1/health/system', {
        timeout: 5000,
      });
      
      await apiRequestContext.dispose();
      
      if (response.ok()) {
        return await response.json();
      } else {
        return null;
      }
      
    } catch (error) {
      console.warn('⚠️ Could not fetch system stats:', error);
      return null;
    }
  }
  
  /**
   * Log health status in a formatted way
   */
  private logHealthStatus(healthStatus: HealthStatus): void {
    console.log('\n📊 Service Health Report:');
    console.log('━'.repeat(60));
    
    healthStatus.services.forEach(service => {
      const status = service.healthy ? '✅' : '❌';
      const responseTime = `${service.responseTime}ms`;
      const error = service.error ? ` (${service.error})` : '';
      
      console.log(`${status} ${service.name.padEnd(20)} ${responseTime.padStart(8)}${error}`);
    });
    
    console.log('━'.repeat(60));
    console.log(`Overall Status: ${healthStatus.allHealthy ? '✅ HEALTHY' : '❌ UNHEALTHY'}`);
    console.log(`Total Response Time: ${healthStatus.totalResponseTime}ms`);
    
    if (!healthStatus.allHealthy) {
      console.log(`Failed Services: ${healthStatus.failedServices.join(', ')}`);
    }
    
    console.log();
  }
  
  /**
   * Generate health report for documentation
   */
  async generateHealthReport(): Promise<string> {
    const healthStatus = await this.checkAllServices();
    const systemStats = await this.getSystemStats();
    
    let report = '# TalentForge Pro Service Health Report\n\n';
    report += `**Generated:** ${new Date().toISOString()}\n\n`;
    report += `**Overall Status:** ${healthStatus.allHealthy ? '✅ HEALTHY' : '❌ UNHEALTHY'}\n\n`;
    
    report += '## Service Details\n\n';
    report += '| Service | Status | Response Time | Error |\n';
    report += '|---------|--------|---------------|-------|\n';
    
    healthStatus.services.forEach(service => {
      const status = service.healthy ? '✅ Healthy' : '❌ Unhealthy';
      const responseTime = `${service.responseTime}ms`;
      const error = service.error || '-';
      
      report += `| ${service.name} | ${status} | ${responseTime} | ${error} |\n`;
    });
    
    report += '\n';
    
    if (systemStats) {
      report += '## System Statistics\n\n';
      report += '```json\n';
      report += JSON.stringify(systemStats, null, 2);
      report += '\n```\n\n';
    }
    
    if (!healthStatus.allHealthy) {
      report += '## Failed Services\n\n';
      healthStatus.failedServices.forEach(service => {
        report += `- **${service}**\n`;
      });
      report += '\n';
    }
    
    report += '---\n';
    report += '*This report was generated automatically by the Playwright health checker*\n';
    
    return report;
  }
}