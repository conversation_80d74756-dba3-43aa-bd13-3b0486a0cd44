/**
 * Playwright Global Teardown
 * 
 * Handles post-test cleanup and reporting:
 * - Test results aggregation
 * - Defect report generation
 * - Cleanup of test artifacts
 * - Performance metrics collection
 */

import { FullConfig } from '@playwright/test';
import { TestDataManager } from '../utils/test-data-manager';
import { DefectReporter } from '../utils/defect-reporter';
import fs from 'fs/promises';
import path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting TalentForge Pro E2E Test Teardown...');
  
  const teardownStartTime = Date.now();
  
  try {
    // 1. Aggregate test results
    console.log('1️⃣ Aggregating test results...');
    const testDataManager = new TestDataManager();
    
    const resultsPath = path.join(__dirname, '../reports/playwright-results.json');
    let testResults = null;
    
    try {
      const resultsData = await fs.readFile(resultsPath, 'utf-8');
      testResults = JSON.parse(resultsData);
    } catch (error) {
      console.warn('⚠️ Could not read test results file:', error);
    }
    
    // 2. Generate comprehensive defect report
    console.log('2️⃣ Generating defect report...');
    const defectReporter = new DefectReporter();
    
    if (testResults) {
      await defectReporter.generateReport(testResults);
      console.log('✅ Defect report generated');
    }
    
    // 3. Collect performance metrics
    console.log('3️⃣ Collecting performance metrics...');
    const performanceMetrics = await testDataManager.collectPerformanceMetrics();
    
    // 4. Generate summary report
    console.log('4️⃣ Generating test summary...');
    const summaryReport = {
      timestamp: new Date().toISOString(),
      testResults: testResults ? {
        total: testResults.suites?.reduce((acc: number, suite: any) => acc + suite.tests?.length || 0, 0) || 0,
        passed: testResults.suites?.reduce((acc: number, suite: any) => 
          acc + (suite.tests?.filter((test: any) => test.outcome === 'expected')?.length || 0), 0) || 0,
        failed: testResults.suites?.reduce((acc: number, suite: any) => 
          acc + (suite.tests?.filter((test: any) => test.outcome === 'unexpected')?.length || 0), 0) || 0,
        skipped: testResults.suites?.reduce((acc: number, suite: any) => 
          acc + (suite.tests?.filter((test: any) => test.outcome === 'skipped')?.length || 0), 0) || 0,
      } : null,
      performanceMetrics,
      teardownTime: 0, // Will be set below
    };
    
    // 5. Cleanup temporary files
    console.log('5️⃣ Cleaning up temporary files...');
    await testDataManager.cleanupTestData();
    
    // Clean up auth state
    try {
      await fs.unlink('./auth-state.json');
    } catch (error) {
      // Ignore if file doesn't exist
    }
    
    const teardownTime = Date.now() - teardownStartTime;
    summaryReport.teardownTime = teardownTime;
    
    // Save final summary
    await fs.writeFile(
      path.join(__dirname, '../reports/test-summary.json'),
      JSON.stringify(summaryReport, null, 2)
    );
    
    console.log(`🎉 Global teardown completed in ${teardownTime}ms`);
    
    // Print test summary
    if (summaryReport.testResults) {
      console.log('\n📊 Test Summary:');
      console.log(`   Total: ${summaryReport.testResults.total}`);
      console.log(`   Passed: ${summaryReport.testResults.passed}`);
      console.log(`   Failed: ${summaryReport.testResults.failed}`);
      console.log(`   Skipped: ${summaryReport.testResults.skipped}`);
      console.log(`   Success Rate: ${((summaryReport.testResults.passed / summaryReport.testResults.total) * 100).toFixed(1)}%`);
    }
    
  } catch (error) {
    console.error('❌ Global teardown failed:', error);
    // Don't throw error in teardown to avoid masking test failures
  }
}

export default globalTeardown;