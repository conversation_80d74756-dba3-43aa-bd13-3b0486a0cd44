/**
 * Playwright Configuration for TalentForge Pro Black-box Testing
 * 
 * Comprehensive E2E testing configuration with:
 * - Docker environment targeting
 * - Multi-browser support
 * - Authentication state management
 * - Defect reporting integration
 */

import { defineConfig, devices } from '@playwright/test';
import path from 'path';

const BASE_URL = process.env.BASE_URL || 'http://localhost:8088';
const API_BASE_URL = process.env.API_BASE_URL || 'http://localhost:8088/api/v1';

export default defineConfig({
  // Test directory configuration
  testDir: '../tests',
  
  // Global test timeout
  timeout: 60000,
  
  // Expect timeout for assertions
  expect: {
    timeout: 10000,
  },
  
  // Parallel execution configuration
  fullyParallel: false, // Sequential for black-box testing consistency
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1,
  workers: process.env.CI ? 1 : 2,
  
  // Reporter configuration with defect reporting
  reporter: [
    ['list'],
    ['json', { outputFile: '../reports/playwright-results.json' }],
    ['html', { outputFolder: '../reports/html-report' }],
    ['./reporters/defect-reporter.ts', { outputFile: '../reports/defects-report.md' }],
  ],
  
  // Global test configuration
  use: {
    // Base URL for navigation
    baseURL: BASE_URL,
    
    // Browser configuration
    headless: true,
    viewport: { width: 1280, height: 720 },
    ignoreHTTPSErrors: true,
    
    // Request configuration
    extraHTTPHeaders: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
    
    // Media configuration
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
    trace: 'retain-on-failure',
    
    // Storage state for authenticated tests
    storageState: undefined, // Set dynamically by tests
  },
  
  // Project configurations for different test types
  projects: [
    // Authentication setup
    {
      name: 'setup',
      testMatch: /.*\.setup\.ts/,
      use: {
        ...devices['Desktop Chrome'],
      },
    },
    
    // Main test suite - Chrome
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        storageState: 'auth-state.json',
      },
      dependencies: ['setup'],
    },
    
    // Cross-browser validation - Firefox
    {
      name: 'firefox',
      use: {
        ...devices['Desktop Firefox'],
        storageState: 'auth-state.json',
      },
      dependencies: ['setup'],
      testIgnore: [
        /.*\/(admin|candidates|dashboard|jobs|ai-questionnaire)\/.*\.spec\.ts/
      ], // Only run critical tests on Firefox
    },
    
    // Mobile testing - Critical flows only
    {
      name: 'mobile-chrome',
      use: {
        ...devices['Pixel 5'],
        storageState: 'auth-state.json',
      },
      dependencies: ['setup'],
      testMatch: [
        /.*\/critical\/.*\.spec\.ts/
      ],
    },
    
    // API-only tests (no browser)
    {
      name: 'api-validation',
      testMatch: /.*\/api\/.*\.spec\.ts/,
      use: {
        // API tests don't need browser
        baseURL: API_BASE_URL,
      },
    },
  ],
  
  // Development server configuration
  webServer: undefined, // Assume Docker services are already running
  
  // Output directories
  outputDir: '../test-results',
  
  // Global setup and teardown
  globalSetup: './global-setup.ts',
  globalTeardown: './global-teardown.ts',
});