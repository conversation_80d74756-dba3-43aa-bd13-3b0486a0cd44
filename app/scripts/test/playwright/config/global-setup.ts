/**
 * Playwright Global Setup
 * 
 * Handles pre-test environment validation and setup:
 * - Docker service health checks
 * - Database connection validation
 * - Authentication token generation
 * - Test environment preparation
 */

import { chromium, FullConfig } from '@playwright/test';
import { AuthHelper } from '../utils/auth-helper';
import { HealthChecker } from '../utils/health-checker';
import { TestDataManager } from '../utils/test-data-manager';

async function globalSetup(config: FullConfig) {
  console.log('🚀 Starting TalentForge Pro E2E Test Setup...');
  
  const setupStartTime = Date.now();
  
  try {
    // 1. Validate Docker environment
    console.log('1️⃣ Checking Docker services health...');
    const healthChecker = new HealthChecker();
    const healthStatus = await healthChecker.checkAllServices();
    
    if (!healthStatus.allHealthy) {
      throw new Error(`Docker services not healthy: ${healthStatus.failedServices.join(', ')}`);
    }
    
    console.log('✅ All Docker services are healthy');
    
    // 2. Setup authentication
    console.log('2️⃣ Setting up authentication...');
    const browser = await chromium.launch();
    const context = await browser.newContext();
    const page = await context.newPage();
    
    const authHelper = new AuthHelper(page);
    
    // Login and save authentication state
    await authHelper.loginWithDevToken();
    await authHelper.saveAuthState('./auth-state.json');
    
    console.log('✅ Authentication state saved');
    
    // 3. Prepare test data
    console.log('3️⃣ Preparing test data...');
    const testDataManager = new TestDataManager();
    await testDataManager.initializeTestData();
    
    console.log('✅ Test data initialized');
    
    // 4. Validate critical API endpoints
    console.log('4️⃣ Validating critical API endpoints...');
    const criticalEndpoints = [
      '/auth/me',
      '/users/',
      '/candidates/',
      '/positions/',
      '/dashboard/stats',
    ];
    
    for (const endpoint of criticalEndpoints) {
      await authHelper.validateApiEndpoint(endpoint);
    }
    
    console.log('✅ Critical API endpoints validated');
    
    await browser.close();
    
    const setupTime = Date.now() - setupStartTime;
    console.log(`🎉 Global setup completed in ${setupTime}ms`);
    
    // Store setup metadata
    await testDataManager.saveSetupMetadata({
      setupTime,
      timestamp: new Date().toISOString(),
      servicesHealth: healthStatus,
      endpointsValidated: criticalEndpoints,
    });
    
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    throw error;
  }
}

export default globalSetup;