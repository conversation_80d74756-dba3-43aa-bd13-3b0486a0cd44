/**
 * <PERSON><PERSON> Custom Defect Reporter
 * 
 * Custom reporter that integrates with the DefectReporter utility
 * to generate real-time defect reports during test execution.
 */

import {
  FullConfig,
  FullResult,
  Reporter,
  Suite,
  TestCase,
  TestResult,
} from '@playwright/test/reporter';
import { DefectReporter } from '../utils/defect-reporter';
import fs from 'fs/promises';
import path from 'path';

interface ReporterOptions {
  outputFile?: string;
}

export default class CustomDefectReporter implements Reporter {
  private defectReporter: DefectReporter;
  private outputFile: string;
  private testResults: any;
  
  constructor(options: ReporterOptions = {}) {
    this.defectReporter = new DefectReporter();
    this.outputFile = options.outputFile || path.join(__dirname, '../reports/defects-report.md');
    this.testResults = {
      config: null,
      suites: [],
      errors: [],
      stats: {
        startTime: Date.now(),
        duration: 0,
      },
    };
  }
  
  onBegin(config: FullConfig, suite: Suite): void {
    console.log('🚀 Starting TalentForge Pro E2E test execution...');
    
    this.testResults.config = {
      rootDir: config.rootDir,
      testDir: config.testDir,
      timeout: config.timeout,
      workers: config.workers,
    };
    
    this.testResults.stats.startTime = Date.now();
  }
  
  onTestBegin(test: TestCase, result: TestResult): void {
    // Log test start for debugging
    const testPath = test.location?.file ? path.basename(test.location.file) : 'unknown';
    console.log(`▶️ Starting: ${test.title} (${testPath})`);
  }
  
  onTestEnd(test: TestCase, result: TestResult): void {
    // Log test completion
    const status = result.status;
    const duration = result.duration;
    const testPath = test.location?.file ? path.basename(test.location.file) : 'unknown';
    
    const statusEmoji = {
      passed: '✅',
      failed: '❌',
      timedOut: '⏱️',
      skipped: '⏭️',
      interrupted: '🛑',
    }[status] || '❓';
    
    console.log(`${statusEmoji} ${test.title} (${duration}ms) - ${testPath}`);
    
    // If test failed, show error preview
    if (status === 'failed' || status === 'timedOut') {
      if (result.error) {
        console.log(`   Error: ${result.error.message?.slice(0, 100)}...`);
      }
    }
  }
  
  onStdOut(chunk: Buffer, test?: TestCase, result?: TestResult): void {
    // Capture stdout for debugging
    const output = chunk.toString();
    if (output.includes('ERROR') || output.includes('FAIL')) {
      console.log(`📝 Test output: ${output.trim()}`);
    }
  }
  
  onStdErr(chunk: Buffer, test?: TestCase, result?: TestResult): void {
    // Capture stderr for error analysis
    const output = chunk.toString();
    console.error(`🚨 Test error: ${output.trim()}`);
  }
  
  onError(error: TestCase | Error): void {
    console.error('💥 Global test error:', error);
    this.testResults.errors.push({
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      timestamp: new Date().toISOString(),
    });
  }
  
  async onEnd(result: FullResult): void {
    this.testResults.stats.duration = Date.now() - this.testResults.stats.startTime;
    
    console.log('\n🏁 Test execution completed');
    console.log(`⏱️ Total duration: ${Math.round(this.testResults.stats.duration / 1000)}s`);
    
    try {
      // Convert Playwright result to our format
      const convertedResults = this.convertPlaywrightResults(result);
      
      // Generate defect report
      await this.defectReporter.generateReport(convertedResults);
      
      // Log summary
      const summary = this.generateExecutionSummary(result);
      console.log(summary);
      
    } catch (error) {
      console.error('❌ Failed to generate defect report:', error);
    }
  }
  
  /**
   * Convert Playwright FullResult to our test results format
   */
  private convertPlaywrightResults(result: FullResult): any {
    const suites: any[] = [];
    
    // Process each suite recursively
    this.processSuite(result.suites, suites);
    
    return {
      config: this.testResults.config,
      suites,
      errors: this.testResults.errors,
      stats: {
        ...this.testResults.stats,
        status: result.status,
      },
    };
  }
  
  /**
   * Process test suite recursively
   */
  private processSuite(suites: Suite[], results: any[]): void {
    for (const suite of suites) {
      if (suite.suites.length > 0) {
        // Nested suite
        this.processSuite(suite.suites, results);
      }
      
      if (suite.tests.length > 0) {
        // Suite with tests
        const suiteData = {
          title: suite.title,
          file: suite.location?.file,
          tests: suite.tests.map(test => this.convertTest(test)),
        };
        
        results.push(suiteData);
      }
    }
  }
  
  /**
   * Convert TestCase to our format
   */
  private convertTest(test: TestCase): any {
    const lastResult = test.results[test.results.length - 1];
    
    return {
      title: test.title,
      location: test.location,
      outcome: this.mapOutcome(lastResult?.status),
      results: test.results.map(result => ({
        duration: result.duration,
        status: result.status,
        error: result.error ? {
          message: result.error.message,
          stack: result.error.stack,
        } : undefined,
        attachments: result.attachments?.map(attachment => ({
          name: attachment.name,
          path: attachment.path,
          contentType: attachment.contentType,
        })),
        workerIndex: result.workerIndex,
        parallelIndex: result.parallelIndex,
        retry: result.retry,
        startTime: result.startTime,
      })),
      projectName: test.parent.project()?.name,
      annotations: test.annotations,
      tags: test.tags,
    };
  }
  
  /**
   * Map Playwright test status to our outcome format
   */
  private mapOutcome(status?: 'passed' | 'failed' | 'timedOut' | 'skipped' | 'interrupted'): string {
    switch (status) {
      case 'passed':
        return 'expected';
      case 'failed':
      case 'timedOut':
        return 'unexpected';
      case 'skipped':
        return 'skipped';
      case 'interrupted':
        return 'interrupted';
      default:
        return 'unknown';
    }
  }
  
  /**
   * Generate execution summary for console output
   */
  private generateExecutionSummary(result: FullResult): string {
    const stats = {
      total: 0,
      passed: 0,
      failed: 0,
      skipped: 0,
      timedOut: 0,
    };
    
    // Count tests from all suites
    this.countTests(result.suites, stats);
    
    const duration = Math.round(this.testResults.stats.duration / 1000);
    const successRate = stats.total > 0 ? ((stats.passed / stats.total) * 100).toFixed(1) : '0.0';
    
    let summary = '\n📊 Test Execution Summary:\n';
    summary += '━'.repeat(50) + '\n';
    summary += `Total Tests: ${stats.total}\n`;
    summary += `✅ Passed: ${stats.passed}\n`;
    summary += `❌ Failed: ${stats.failed}\n`;
    summary += `⏭️ Skipped: ${stats.skipped}\n`;
    summary += `⏱️ Timed Out: ${stats.timedOut}\n`;
    summary += `🎯 Success Rate: ${successRate}%\n`;
    summary += `⏱️ Duration: ${duration}s\n`;
    
    if (stats.failed > 0) {
      summary += `\n🐛 Defect report generated for failed tests\n`;
      summary += `📄 Report location: ${this.outputFile}\n`;
    }
    
    summary += '━'.repeat(50) + '\n';
    
    return summary;
  }
  
  /**
   * Count tests recursively
   */
  private countTests(suites: Suite[], stats: any): void {
    for (const suite of suites) {
      if (suite.suites.length > 0) {
        this.countTests(suite.suites, stats);
      }
      
      for (const test of suite.tests) {
        stats.total++;
        
        const lastResult = test.results[test.results.length - 1];
        if (lastResult) {
          switch (lastResult.status) {
            case 'passed':
              stats.passed++;
              break;
            case 'failed':
              stats.failed++;
              break;
            case 'skipped':
              stats.skipped++;
              break;
            case 'timedOut':
              stats.timedOut++;
              break;
          }
        }
      }
    }
  }
}