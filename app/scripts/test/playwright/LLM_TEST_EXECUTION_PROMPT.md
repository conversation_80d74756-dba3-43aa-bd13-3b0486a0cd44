# LLM Playwright 测试执行提示词

## 完整提示词模板

```
你是 TalentForge Pro 项目的自动化测试专家。请使用 Playwright MCP 工具执行完整的黑盒测试，并生成详细的功能缺陷报告。

## 测试环境信息
- **目标环境**: http://localhost:8088
- **认证信息**: <EMAIL> / test123 或使用开发令牌 `dev_bypass_token_2025_talentforge`
- **浏览器**: Chrome (Playwright MCP 默认)
- **测试范围**: 完整的用户流程黑盒测试

## 测试执行要求

### 1. 环境检查
首先验证测试环境：
```bash
cd /home/<USER>/source_code/talent_forge_pro
make status  # 检查 Docker 服务状态
curl -I http://localhost:8088  # 验证前端可访问
curl -I http://localhost:8088/api/v1/health  # 验证 API 健康状态
```

### 2. 执行测试套件
按优先级顺序执行以下测试流程：

#### Phase 1: 管理面板测试 (Admin Panel)
- 导航到 http://localhost:8088/login
- 测试用户登录功能 (<EMAIL> / test123)
- 验证管理面板访问权限
- 测试用户管理功能
- 检查系统设置功能

#### Phase 2: 候选人评估测试 (Candidate Assessment)  
- 测试候选人 CRUD 操作
- 验证简历上传功能
- 测试 ML 评分系统
- 检查候选人搜索和过滤
- 验证候选人详情页面

#### Phase 3: 仪表盘测试 (Dashboard)
- 测试数据可视化组件
- 验证分析图表显示
- 检查实时数据更新
- 测试筛选和排序功能
- 验证导出功能

#### Phase 4: 职位管理测试 (Job Management)
- 测试职位发布功能
- 验证候选人匹配算法
- 检查职位生命周期管理
- 测试职位搜索功能
- 验证职位详情编辑

#### Phase 5: AI问卷测试 (AI-Questionnaire)
- 测试 AI 问卷生成功能
- 验证 ML 模型集成
- 检查问卷模板管理
- 测试评分算法
- 验证结果分析

### 3. API 验证要求
对以下关键 API 端点进行验证：
- `/api/v1/health` - 系统健康检查
- `/api/v1/auth/me` - 用户认证状态
- `/api/v1/users/` - 用户管理
- `/api/v1/candidates/` - 候选人管理
- `/api/v1/positions/` - 职位管理
- `/api/v1/dashboard/stats` - 仪表盘数据
- `/api/v1/ai-questionnaire/generate` - AI 问卷生成
- `/api/v1/admin/monitoring/health` - 系统监控

### 4. 成功标准
验证以下条件全部满足：
- ✅ 所有按钮都可以点击并产生相应事件
- ✅ 所有 API 接口都能正常返回响应
- ✅ 页面导航流程完整无阻断
- ✅ 表单提交和验证功能正常
- ✅ 动态内容加载正常显示
- ✅ 错误处理机制工作正常

### 5. 缺陷报告格式
生成 Markdown 格式的缺陷报告，包含：

```markdown
# TalentForge Pro 黑盒测试缺陷报告

## 测试执行概览
- **测试时间**: [自动填入]
- **测试环境**: http://localhost:8088
- **浏览器**: Chrome (Playwright MCP)
- **测试范围**: 完整用户流程

## 测试结果汇总
- **总测试用例**: X 个
- **通过用例**: X 个  
- **失败用例**: X 个
- **成功率**: XX%

## 缺陷详情

### 1. UI交互类缺陷 (UI_INTERACTION)

#### 缺陷 #001 - [严重程度: Critical/High/Medium/Low]
- **页面**: [页面名称]
- **元素**: [具体元素]
- **期望行为**: [预期结果]
- **实际行为**: [实际结果]
- **复现步骤**: 
  1. [步骤1]
  2. [步骤2]
  3. [步骤3]
- **修复建议**: [具体建议]

### 2. API响应类缺陷 (API_FAILURE)

#### 缺陷 #002 - [严重程度]
- **端点**: [API路径]
- **请求方法**: [GET/POST/PUT/DELETE]
- **期望响应**: [期望状态码和数据格式]
- **实际响应**: [实际状态码和错误信息]
- **修复建议**: [具体建议]

### 3. ML功能类缺陷 (ML_INTEGRATION)

#### 缺陷 #003 - [严重程度]
- **功能模块**: [AI问卷/评分系统等]
- **输入数据**: [测试数据]
- **期望输出**: [预期ML结果]
- **实际输出**: [实际结果或错误]
- **修复建议**: [具体建议]

## 修复优先级建议
1. **Critical**: 阻止核心功能使用的缺陷
2. **High**: 影响主要功能的缺陷  
3. **Medium**: 影响用户体验的缺陷
4. **Low**: 次要功能问题

## 总体评估和建议
[整体系统质量评估和改进建议]
```

## 执行指令

现在请开始执行测试：

1. **导航到测试环境**: 使用 Playwright MCP 工具打开 http://localhost:8088
2. **按序执行测试**: 严格按照 Admin Panel → Candidate Assessment → Dashboard → Job Management → AI-Questionnaire 的顺序
3. **详细记录问题**: 对每个发现的问题进行详细记录
4. **生成完整报告**: 按照上述格式生成 LLM 友好的 Markdown 缺陷报告

开始测试执行！
```

## 简化版快速启动提示词

如果需要更简洁的提示词：

```
使用 Playwright MCP 工具对 http://localhost:8088 进行完整黑盒测试。

测试流程：登录(<EMAIL>/test123) → 管理面板 → 候选人管理 → 仪表盘 → 职位管理 → AI问卷

验证要求：所有按钮可点击+所有API正常响应

生成 Markdown 格式缺陷报告，包含问题分类(UI_INTERACTION/API_FAILURE/ML_INTEGRATION)和修复建议。
```

## 高级测试提示词 (包含性能监控)

```
执行 TalentForge Pro 生产级黑盒测试，使用 Playwright MCP 工具：

环境：http://localhost:8088
认证：<EMAIL>/test123

测试矩阵：
- 功能测试：完整用户流程 (5个模块)
- 性能测试：页面加载<3秒，API响应<200ms
- 兼容性：Chrome 浏览器全功能验证
- 错误处理：异常场景和边界条件

输出：结构化 Markdown 缺陷报告 + 性能指标 + 修复优先级
```