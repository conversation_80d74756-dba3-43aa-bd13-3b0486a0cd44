#!/bin/bash

# TalentForge Pro E2E Test Execution Script
# Comprehensive test execution with Docker integration and reporting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../../../.." && pwd)"
PLAYWRIGHT_DIR="$PROJECT_ROOT/app/scripts/test/playwright"
BASE_URL="${BASE_URL:-http://localhost:8088}"
HEADLESS="${HEADLESS:-true}"
WORKERS="${WORKERS:-2}"
RETRIES="${RETRIES:-1}"
TIMEOUT="${TIMEOUT:-60000}"

# Test execution options
RUN_ADMIN="${RUN_ADMIN:-true}"
RUN_CANDIDATES="${RUN_CANDIDATES:-true}"
RUN_DASHBOARD="${RUN_DASHBOARD:-true}"
RUN_JOBS="${RUN_JOBS:-true}"
RUN_AI="${RUN_AI:-true}"
RUN_API_VALIDATION="${RUN_API_VALIDATION:-true}"

# Report configuration
GENERATE_HTML_REPORT="${GENERATE_HTML_REPORT:-true}"
GENERATE_MARKDOWN_REPORT="${GENERATE_MARKDOWN_REPORT:-true}"
GENERATE_DEFECT_REPORT="${GENERATE_DEFECT_REPORT:-true}"

echo_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

echo_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

echo_header() {
    echo -e "\n${BLUE}===================================================="
    echo -e "$1"
    echo -e "====================================================${NC}\n"
}

# Function to check if Docker services are running
check_docker_services() {
    echo_header "Checking Docker Services Status"
    
    cd "$PROJECT_ROOT"
    
    # Check if services are running
    echo_info "Checking Docker Compose services..."
    
    if ! command -v docker &> /dev/null; then
        echo_error "Docker is not installed or not in PATH"
        return 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        echo_error "Docker Compose is not installed or not in PATH"
        return 1
    fi
    
    # Use make status to check services
    if command -v make &> /dev/null; then
        echo_info "Using make to check service status..."
        if ! make status; then
            echo_warning "Make status check failed, trying manual Docker check..."
            return 1
        fi
    else
        echo_warning "Make command not available, checking Docker services manually..."
        
        # Check if any containers are running
        local running_containers=$(docker ps --format "table {{.Names}}\t{{.Status}}" | grep -v "NAMES" || true)
        if [ -z "$running_containers" ]; then
            echo_error "No Docker containers are running"
            return 1
        fi
        
        echo_info "Running containers:"
        echo "$running_containers"
    fi
    
    echo_success "Docker services check completed"
    return 0
}

# Function to wait for services to be ready
wait_for_services() {
    echo_header "Waiting for Services to be Ready"
    
    local max_attempts=30
    local attempt=1
    local services_ready=false
    
    echo_info "Waiting for TalentForge Pro services to be available..."
    echo_info "Base URL: $BASE_URL"
    
    while [ $attempt -le $max_attempts ]; do
        echo_info "Attempt $attempt/$max_attempts: Checking service availability..."
        
        # Check frontend availability
        if curl -sSf "$BASE_URL" > /dev/null 2>&1; then
            echo_success "Frontend service is available at $BASE_URL"
            
            # Check backend API availability
            if curl -sSf "$BASE_URL/api/v1/health" > /dev/null 2>&1; then
                echo_success "Backend API is available at $BASE_URL/api/v1/"
                services_ready=true
                break
            else
                echo_warning "Backend API not yet available at $BASE_URL/api/v1/health"
            fi
        else
            echo_warning "Frontend service not yet available at $BASE_URL"
        fi
        
        echo_info "Services not ready yet, waiting 10 seconds..."
        sleep 10
        ((attempt++))
    done
    
    if [ "$services_ready" = true ]; then
        echo_success "All services are ready for testing"
        return 0
    else
        echo_error "Services failed to become ready within timeout"
        return 1
    fi
}

# Function to setup Playwright environment
setup_playwright() {
    echo_header "Setting up Playwright Environment"
    
    cd "$PLAYWRIGHT_DIR"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        echo_error "package.json not found in $PLAYWRIGHT_DIR"
        return 1
    fi
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        echo_info "Installing npm dependencies..."
        if command -v npm &> /dev/null; then
            npm install
        else
            echo_error "npm is not installed or not in PATH"
            return 1
        fi
    else
        echo_info "Dependencies already installed"
    fi
    
    # Install Playwright browsers
    echo_info "Installing Playwright browsers..."
    npx playwright install chromium
    
    # Install system dependencies if needed
    if command -v apt-get &> /dev/null; then
        echo_info "Installing system dependencies for Playwright..."
        npx playwright install-deps chromium || echo_warning "Could not install system dependencies (may require sudo)"
    fi
    
    echo_success "Playwright environment setup completed"
    return 0
}

# Function to run health check
run_health_check() {
    echo_header "Running Pre-Test Health Check"
    
    cd "$PLAYWRIGHT_DIR"
    
    # Check if health check script exists
    if [ -f "scripts/health-check.js" ]; then
        echo_info "Running custom health check..."
        node scripts/health-check.js
    else
        echo_info "Running basic health check..."
        
        # Basic health check using curl
        local health_endpoints=(
            "$BASE_URL"
            "$BASE_URL/api/v1/health"
        )
        
        for endpoint in "${health_endpoints[@]}"; do
            echo_info "Checking: $endpoint"
            if curl -sSf "$endpoint" > /dev/null 2>&1; then
                echo_success "✓ $endpoint is accessible"
            else
                echo_warning "✗ $endpoint is not accessible"
            fi
        done
    fi
    
    echo_success "Health check completed"
}

# Function to create reports directory
setup_reports() {
    echo_info "Setting up reports directory..."
    
    cd "$PLAYWRIGHT_DIR"
    
    # Create reports directories
    mkdir -p reports/{html-report,json,markdown,defects}
    mkdir -p test-results
    
    # Copy any existing reports to timestamped backup
    local timestamp=$(date +%Y%m%d_%H%M%S)
    if [ -d "reports/html-report" ] && [ "$(ls -A reports/html-report)" ]; then
        echo_info "Backing up previous HTML report..."
        mv reports/html-report "reports/html-report-backup-$timestamp" || true
    fi
    
    echo_success "Reports directory setup completed"
}

# Function to run specific test suite
run_test_suite() {
    local suite_name="$1"
    local test_path="$2"
    local suite_enabled="$3"
    
    if [ "$suite_enabled" = "true" ]; then
        echo_header "Running $suite_name Tests"
        echo_info "Test path: $test_path"
        
        local test_cmd="npx playwright test \"$test_path\" \
            --reporter=list,json,html \
            --output-dir=test-results \
            --timeout=$TIMEOUT \
            --retries=$RETRIES \
            --workers=$WORKERS"
        
        if [ "$HEADLESS" = "false" ]; then
            test_cmd="$test_cmd --headed"
        fi
        
        echo_info "Executing: $test_cmd"
        
        if eval $test_cmd; then
            echo_success "$suite_name tests completed successfully"
            return 0
        else
            echo_error "$suite_name tests failed"
            return 1
        fi
    else
        echo_info "Skipping $suite_name tests (disabled)"
        return 0
    fi
}

# Function to run all test suites
run_tests() {
    echo_header "Executing Test Suites"
    
    cd "$PLAYWRIGHT_DIR"
    
    local total_failures=0
    
    # Test suite configuration
    local test_suites=(
        "Admin Panel:tests/1-admin/:$RUN_ADMIN"
        "Candidate Assessment:tests/2-candidate-assessment/:$RUN_CANDIDATES"
        "Dashboard:tests/3-dashboard/:$RUN_DASHBOARD"
        "Job Management:tests/4-job-management/:$RUN_JOBS"
        "AI Questionnaire:tests/5-ai-questionnaire/:$RUN_AI"
    )
    
    # Run each test suite
    for suite_config in "${test_suites[@]}"; do
        IFS=':' read -r suite_name test_path suite_enabled <<< "$suite_config"
        
        if ! run_test_suite "$suite_name" "$test_path" "$suite_enabled"; then
            ((total_failures++))
        fi
        
        # Brief pause between suites
        sleep 2
    done
    
    # Run API validation tests if enabled
    if [ "$RUN_API_VALIDATION" = "true" ]; then
        echo_header "Running API Validation Tests"
        
        local api_test_cmd="npx playwright test --grep 'API' \
            --reporter=list,json \
            --timeout=$TIMEOUT \
            --workers=1"
        
        if eval $api_test_cmd; then
            echo_success "API validation tests completed successfully"
        else
            echo_error "API validation tests failed"
            ((total_failures++))
        fi
    fi
    
    return $total_failures
}

# Function to generate reports
generate_reports() {
    echo_header "Generating Test Reports"
    
    cd "$PLAYWRIGHT_DIR"
    
    local reports_generated=0
    
    # Generate HTML report
    if [ "$GENERATE_HTML_REPORT" = "true" ]; then
        echo_info "Generating HTML report..."
        if npx playwright show-report --host=0.0.0.0 --port=9323 > /dev/null 2>&1 &; then
            echo_success "HTML report will be available at http://localhost:9323"
            echo_info "HTML report files saved to: reports/html-report/"
            ((reports_generated++))
        else
            echo_warning "Failed to generate HTML report"
        fi
    fi
    
    # Generate custom markdown report
    if [ "$GENERATE_MARKDOWN_REPORT" = "true" ]; then
        echo_info "Generating custom markdown report..."
        if [ -f "scripts/generate-report.js" ]; then
            if node scripts/generate-report.js; then
                echo_success "Markdown report generated"
                ((reports_generated++))
            else
                echo_warning "Failed to generate markdown report"
            fi
        else
            echo_warning "Markdown report generator not found"
        fi
    fi
    
    # Generate defect report
    if [ "$GENERATE_DEFECT_REPORT" = "true" ]; then
        echo_info "Generating defect analysis report..."
        if [ -f "scripts/generate-defect-report.js" ]; then
            if node scripts/generate-defect-report.js; then
                echo_success "Defect report generated"
                ((reports_generated++))
            else
                echo_warning "Failed to generate defect report"
            fi
        else
            echo_info "Using built-in defect reporting from test results"
        fi
    fi
    
    # Copy reports to project docs directory
    local docs_reports_dir="$PROJECT_ROOT/docs/reports/playwright"
    mkdir -p "$docs_reports_dir"
    
    local timestamp=$(date +%Y%m%d_%H%M%S)
    
    # Copy markdown reports if they exist
    if ls reports/markdown/*.md 1> /dev/null 2>&1; then
        echo_info "Copying markdown reports to docs..."
        cp reports/markdown/*.md "$docs_reports_dir/" || true
    fi
    
    # Copy defect reports if they exist
    if ls reports/defects/*.md 1> /dev/null 2>&1; then
        echo_info "Copying defect reports to docs..."
        cp reports/defects/*.md "$docs_reports_dir/" || true
    fi
    
    echo_success "Generated $reports_generated report types"
    
    # Print report locations
    echo_info "Report locations:"
    echo "  - HTML: $PLAYWRIGHT_DIR/reports/html-report/"
    echo "  - JSON: $PLAYWRIGHT_DIR/reports/json/"
    echo "  - Docs: $docs_reports_dir/"
    
    return 0
}

# Function to cleanup and summary
cleanup_and_summary() {
    local exit_code=$1
    
    echo_header "Test Execution Summary"
    
    cd "$PLAYWRIGHT_DIR"
    
    # Print summary
    if [ $exit_code -eq 0 ]; then
        echo_success "All test suites completed successfully!"
        echo_info "✓ Admin Panel Tests"
        echo_info "✓ Candidate Assessment Tests" 
        echo_info "✓ Dashboard Tests"
        echo_info "✓ Job Management Tests"
        echo_info "✓ AI Questionnaire Tests"
    else
        echo_warning "Test execution completed with $exit_code failed test suite(s)"
    fi
    
    # Print test results summary if available
    if [ -f "test-results/results.json" ]; then
        echo_info "Detailed results available in: test-results/results.json"
    fi
    
    # Show report access information
    echo_info "View reports:"
    echo "  HTML Report: npx playwright show-report"
    echo "  Or visit: http://localhost:9323 (if report server is running)"
    
    # Cleanup temporary files
    echo_info "Cleaning up temporary files..."
    find test-results -name "*.tmp" -delete 2>/dev/null || true
    find reports -name "*.tmp" -delete 2>/dev/null || true
    
    echo_success "Cleanup completed"
}

# Main execution function
main() {
    echo_header "TalentForge Pro E2E Test Suite Execution"
    echo_info "Starting comprehensive black-box testing..."
    echo_info "Base URL: $BASE_URL"
    echo_info "Headless: $HEADLESS"
    echo_info "Workers: $WORKERS"
    echo_info "Retries: $RETRIES"
    
    local start_time=$(date +%s)
    local exit_code=0
    
    # Pre-flight checks
    if ! check_docker_services; then
        echo_error "Docker services check failed"
        exit 1
    fi
    
    if ! wait_for_services; then
        echo_error "Services readiness check failed"
        exit 1
    fi
    
    if ! setup_playwright; then
        echo_error "Playwright setup failed"
        exit 1
    fi
    
    run_health_check
    setup_reports
    
    # Run tests
    if ! run_tests; then
        exit_code=$?
        echo_warning "Some tests failed (exit code: $exit_code)"
    fi
    
    # Generate reports regardless of test results
    generate_reports
    
    # Calculate execution time
    local end_time=$(date +%s)
    local execution_time=$((end_time - start_time))
    echo_info "Total execution time: ${execution_time}s"
    
    cleanup_and_summary $exit_code
    
    echo_header "Test Execution Complete"
    exit $exit_code
}

# Handle command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --headed)
            HEADLESS="false"
            shift
            ;;
        --workers=*)
            WORKERS="${1#*=}"
            shift
            ;;
        --timeout=*)
            TIMEOUT="${1#*=}"
            shift
            ;;
        --base-url=*)
            BASE_URL="${1#*=}"
            shift
            ;;
        --skip-admin)
            RUN_ADMIN="false"
            shift
            ;;
        --skip-candidates)
            RUN_CANDIDATES="false"
            shift
            ;;
        --skip-dashboard)
            RUN_DASHBOARD="false"
            shift
            ;;
        --skip-jobs)
            RUN_JOBS="false"
            shift
            ;;
        --skip-ai)
            RUN_AI="false"
            shift
            ;;
        --skip-api)
            RUN_API_VALIDATION="false"
            shift
            ;;
        --no-reports)
            GENERATE_HTML_REPORT="false"
            GENERATE_MARKDOWN_REPORT="false"
            GENERATE_DEFECT_REPORT="false"
            shift
            ;;
        --help)
            echo "TalentForge Pro E2E Test Runner"
            echo ""
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --headed              Run tests in headed mode (visible browser)"
            echo "  --workers=N           Number of parallel workers (default: 2)"
            echo "  --timeout=MS          Test timeout in milliseconds (default: 60000)"
            echo "  --base-url=URL        Base URL for testing (default: http://localhost:8088)"
            echo "  --skip-admin          Skip admin panel tests"
            echo "  --skip-candidates     Skip candidate assessment tests"
            echo "  --skip-dashboard      Skip dashboard tests"
            echo "  --skip-jobs           Skip job management tests"
            echo "  --skip-ai             Skip AI questionnaire tests"
            echo "  --skip-api            Skip API validation tests"
            echo "  --no-reports          Skip report generation"
            echo "  --help                Show this help message"
            echo ""
            echo "Environment Variables:"
            echo "  BASE_URL              Base URL (default: http://localhost:8088)"
            echo "  HEADLESS              Run headless (default: true)"
            echo "  WORKERS               Number of workers (default: 2)"
            echo "  RETRIES               Number of retries (default: 1)"
            echo "  TIMEOUT               Test timeout (default: 60000)"
            exit 0
            ;;
        *)
            echo_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Execute main function
main