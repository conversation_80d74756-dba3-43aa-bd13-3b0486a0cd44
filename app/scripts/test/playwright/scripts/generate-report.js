/**
 * TalentForge Pro Test Report Generator
 * 
 * Generates comprehensive LLM-friendly Markdown reports:
 * - Test execution summary
 * - Defect analysis
 * - Performance metrics
 * - API validation results
 * - Recommendations for fixes
 */

const fs = require('fs');
const path = require('path');

class TestReportGenerator {
  constructor() {
    this.reportsDir = path.join(__dirname, '../reports');
    this.testResultsDir = path.join(__dirname, '../test-results');
    
    this.report = {
      timestamp: new Date().toISOString(),
      summary: {},
      testSuites: [],
      apiResults: [],
      defects: [],
      performance: {},
      recommendations: []
    };
  }
  
  /**
   * Load test results from Playwright JSON output
   */
  async loadTestResults() {
    try {
      // Look for Playwright JSON results
      const resultsFiles = fs.readdirSync(this.testResultsDir)
        .filter(file => file.endsWith('.json'))
        .map(file => path.join(this.testResultsDir, file));
      
      if (resultsFiles.length === 0) {
        console.log('⚠️ No JSON test results found');
        return null;
      }
      
      // Use the most recent results file
      const latestFile = resultsFiles
        .map(file => ({ file, mtime: fs.statSync(file).mtime }))
        .sort((a, b) => b.mtime - a.mtime)[0].file;
      
      console.log(`📊 Loading test results from: ${path.basename(latestFile)}`);
      
      const resultsData = fs.readFileSync(latestFile, 'utf8');
      return JSON.parse(resultsData);
      
    } catch (error) {
      console.log(`⚠️ Could not load test results: ${error.message}`);
      return null;
    }
  }
  
  /**
   * Load health check results
   */
  async loadHealthResults() {
    try {
      const healthFile = path.join(this.reportsDir, 'health-check.json');
      if (fs.existsSync(healthFile)) {
        const healthData = fs.readFileSync(healthFile, 'utf8');
        return JSON.parse(healthData);
      }
    } catch (error) {
      console.log(`⚠️ Could not load health results: ${error.message}`);
    }
    return null;
  }
  
  /**
   * Parse test results and extract meaningful data
   */
  parseTestResults(playwrightResults) {
    if (!playwrightResults) return;
    
    const summary = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      skippedTests: 0,
      duration: 0,
      startTime: null,
      endTime: null
    };
    
    const testSuites = [];
    const failures = [];
    
    // Parse Playwright results structure
    if (playwrightResults.suites) {
      playwrightResults.suites.forEach(suite => {
        const suiteInfo = {
          name: this.extractSuiteName(suite.file || 'Unknown Suite'),
          file: suite.file,
          tests: [],
          duration: 0,
          passed: 0,
          failed: 0,
          skipped: 0
        };
        
        if (suite.specs) {
          suite.specs.forEach(spec => {
            spec.tests?.forEach(test => {
              summary.totalTests++;
              
              const testInfo = {
                title: test.title || 'Unnamed Test',
                outcome: test.outcome || 'unknown',
                duration: 0,
                error: null
              };
              
              if (test.results && test.results.length > 0) {
                const result = test.results[0];
                testInfo.duration = result.duration || 0;
                suiteInfo.duration += testInfo.duration;
                summary.duration += testInfo.duration;
                
                if (result.error) {
                  testInfo.error = result.error.message || result.error.toString();
                }
                
                // Categorize test outcome
                switch (test.outcome) {
                  case 'expected':
                    summary.passedTests++;
                    suiteInfo.passed++;
                    break;
                  case 'unexpected':
                  case 'flaky':
                    summary.failedTests++;
                    suiteInfo.failed++;
                    failures.push({
                      suite: suiteInfo.name,
                      test: testInfo.title,
                      error: testInfo.error,
                      duration: testInfo.duration,
                      file: suite.file
                    });
                    break;
                  case 'skipped':
                    summary.skippedTests++;
                    suiteInfo.skipped++;
                    break;
                }
              }
              
              suiteInfo.tests.push(testInfo);
            });
          });
        }
        
        testSuites.push(suiteInfo);
      });
    }
    
    this.report.summary = summary;
    this.report.testSuites = testSuites;
    this.report.defects = failures;
    
    return { summary, testSuites, failures };
  }
  
  /**
   * Extract readable suite name from file path
   */
  extractSuiteName(filePath) {
    const basename = path.basename(filePath, '.spec.ts');
    const parts = filePath.split('/');
    
    // Look for descriptive directory names
    const descriptiveParts = parts.filter(part => 
      part.includes('admin') || 
      part.includes('candidate') || 
      part.includes('dashboard') || 
      part.includes('job') || 
      part.includes('ai')
    );
    
    if (descriptiveParts.length > 0) {
      return descriptiveParts[0].replace(/[-_]/g, ' ')
        .replace(/\b\w/g, l => l.toUpperCase());
    }
    
    return basename.replace(/[-_]/g, ' ')
      .replace(/\b\w/g, l => l.toUpperCase());
  }
  
  /**
   * Categorize and analyze defects
   */
  analyzeDefects(failures) {
    const categories = {
      'UI_INTERACTION': [],
      'API_FAILURE': [],
      'AUTHENTICATION': [],
      'DATA_VALIDATION': [],
      'PERFORMANCE': [],
      'NAVIGATION': [],
      'UNKNOWN': []
    };
    
    const priorities = {
      'CRITICAL': [],
      'HIGH': [],
      'MEDIUM': [],
      'LOW': []
    };
    
    failures.forEach(failure => {
      // Categorize by error pattern
      const error = (failure.error || '').toLowerCase();
      let category = 'UNKNOWN';
      let priority = 'MEDIUM';
      
      if (error.includes('element') || error.includes('selector') || error.includes('click')) {
        category = 'UI_INTERACTION';
        priority = 'HIGH';
      } else if (error.includes('api') || error.includes('request') || error.includes('response')) {
        category = 'API_FAILURE';
        priority = 'HIGH';
      } else if (error.includes('auth') || error.includes('login') || error.includes('token')) {
        category = 'AUTHENTICATION';
        priority = 'CRITICAL';
      } else if (error.includes('validation') || error.includes('required')) {
        category = 'DATA_VALIDATION';
        priority = 'MEDIUM';
      } else if (error.includes('timeout') || error.includes('slow')) {
        category = 'PERFORMANCE';
        priority = 'MEDIUM';
      } else if (error.includes('navigation') || error.includes('url')) {
        category = 'NAVIGATION';
        priority = 'HIGH';
      }
      
      // Adjust priority based on test suite
      if (failure.suite.toLowerCase().includes('admin')) {
        priority = priority === 'MEDIUM' ? 'HIGH' : priority;
      }
      
      categories[category].push({ ...failure, category, priority });
      priorities[priority].push({ ...failure, category, priority });
    });
    
    return { categories, priorities };
  }
  
  /**
   * Generate performance analysis
   */
  generatePerformanceAnalysis(testSuites) {
    const performance = {
      totalDuration: 0,
      averageTestDuration: 0,
      slowestSuites: [],
      slowestTests: [],
      thresholds: {
        suiteWarning: 30000, // 30 seconds
        testWarning: 10000   // 10 seconds
      }
    };
    
    let allTests = [];
    
    testSuites.forEach(suite => {
      performance.totalDuration += suite.duration;
      
      suite.tests.forEach(test => {
        allTests.push({
          suite: suite.name,
          test: test.title,
          duration: test.duration
        });
      });
    });
    
    if (allTests.length > 0) {
      performance.averageTestDuration = performance.totalDuration / allTests.length;
      
      // Find slowest suites
      performance.slowestSuites = testSuites
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 5)
        .map(suite => ({
          name: suite.name,
          duration: suite.duration,
          testCount: suite.tests.length
        }));
      
      // Find slowest tests
      performance.slowestTests = allTests
        .sort((a, b) => b.duration - a.duration)
        .slice(0, 10);
    }
    
    this.report.performance = performance;
    return performance;
  }
  
  /**
   * Generate recommendations based on analysis
   */
  generateRecommendations(defectAnalysis, performance) {
    const recommendations = [];
    
    // Defect-based recommendations
    if (defectAnalysis.categories.UI_INTERACTION.length > 0) {
      recommendations.push({
        category: 'UI_INTERACTION',
        priority: 'HIGH',
        issue: `${defectAnalysis.categories.UI_INTERACTION.length} UI interaction failures detected`,
        recommendation: 'Review element selectors and wait strategies. Consider implementing more robust element discovery patterns.',
        actions: [
          'Audit failing selectors for accuracy',
          'Add explicit wait conditions',
          'Implement fallback selector strategies',
          'Review page load timing'
        ]
      });
    }
    
    if (defectAnalysis.categories.API_FAILURE.length > 0) {
      recommendations.push({
        category: 'API_FAILURE',
        priority: 'HIGH',
        issue: `${defectAnalysis.categories.API_FAILURE.length} API failures detected`,
        recommendation: 'Investigate backend API stability and error handling.',
        actions: [
          'Check API endpoint availability',
          'Review server logs for errors',
          'Validate request/response formats',
          'Implement API retry logic'
        ]
      });
    }
    
    if (defectAnalysis.categories.AUTHENTICATION.length > 0) {
      recommendations.push({
        category: 'AUTHENTICATION',
        priority: 'CRITICAL',
        issue: `${defectAnalysis.categories.AUTHENTICATION.length} authentication failures detected`,
        recommendation: 'Critical authentication issues require immediate attention.',
        actions: [
          'Verify dev token configuration',
          'Check authentication service status',
          'Review session management',
          'Test login flow manually'
        ]
      });
    }
    
    // Performance-based recommendations
    if (performance.averageTestDuration > performance.thresholds.testWarning) {
      recommendations.push({
        category: 'PERFORMANCE',
        priority: 'MEDIUM',
        issue: `Average test duration (${Math.round(performance.averageTestDuration)}ms) exceeds recommended threshold`,
        recommendation: 'Optimize test execution speed and application performance.',
        actions: [
          'Profile slow-running tests',
          'Optimize database queries',
          'Implement parallel test execution',
          'Review wait strategies and timeouts'
        ]
      });
    }
    
    // General recommendations
    recommendations.push({
      category: 'MAINTENANCE',
      priority: 'LOW',
      issue: 'Regular test maintenance needed',
      recommendation: 'Maintain test suite health and reliability.',
      actions: [
        'Review and update test selectors regularly',
        'Keep test data fresh and relevant',
        'Monitor test execution trends',
        'Update browser versions regularly'
      ]
    });
    
    this.report.recommendations = recommendations;
    return recommendations;
  }
  
  /**
   * Generate comprehensive Markdown report
   */
  generateMarkdownReport(healthResults = null) {
    const { summary, testSuites } = this.report;
    const defectAnalysis = this.analyzeDefects(this.report.defects);
    const performance = this.generatePerformanceAnalysis(testSuites);
    const recommendations = this.generateRecommendations(defectAnalysis, performance);
    
    let markdown = '';
    
    // Header
    markdown += '# 🎯 TalentForge Pro E2E Test Report\n\n';
    markdown += `**Generated:** ${this.report.timestamp}\n`;
    markdown += `**Environment:** Development (localhost:8088)\n`;
    markdown += `**Test Framework:** Playwright\n`;
    markdown += `**Browser:** Chromium\n\n`;
    
    // Executive Summary
    markdown += '## 📊 Executive Summary\n\n';
    
    if (summary.totalTests > 0) {
      const successRate = ((summary.passedTests / summary.totalTests) * 100).toFixed(1);
      const durationMinutes = (summary.duration / 60000).toFixed(1);
      
      markdown += `- **Total Tests:** ${summary.totalTests}\n`;
      markdown += `- **Passed:** ${summary.passedTests} ✅\n`;
      markdown += `- **Failed:** ${summary.failedTests} ❌\n`;
      markdown += `- **Skipped:** ${summary.skippedTests} ⏭️\n`;
      markdown += `- **Success Rate:** ${successRate}%\n`;
      markdown += `- **Execution Time:** ${durationMinutes} minutes\n\n`;
      
      // Success rate indicator
      if (parseFloat(successRate) >= 95) {
        markdown += '🎉 **Excellent**: Success rate meets quality standards\n\n';
      } else if (parseFloat(successRate) >= 80) {
        markdown += '⚠️ **Good**: Success rate is acceptable but has room for improvement\n\n';
      } else {
        markdown += '🚨 **Needs Attention**: Success rate requires immediate investigation\n\n';
      }
    } else {
      markdown += '⚠️ **No test results found**\n\n';
    }
    
    // Health Check Results
    if (healthResults) {
      markdown += '## 🏥 System Health Status\n\n';
      
      const healthServices = [
        { name: 'Frontend', status: healthResults.results.frontend },
        { name: 'Backend API', status: healthResults.results.backend },
        { name: 'Database', status: healthResults.results.database },
        { name: 'Authentication', status: healthResults.results.auth }
      ];
      
      markdown += '| Service | Status | Details |\n';
      markdown += '|---------|--------|---------|\n';
      
      healthServices.forEach(service => {
        if (service.status) {
          const statusIcon = {
            'healthy': '✅',
            'unhealthy': '❌',
            'error': '❌',
            'token_invalid': '⚠️'
          }[service.status.status] || '❓';
          
          const details = service.status.error || service.status.statusCode || 'OK';
          markdown += `| ${service.name} | ${statusIcon} ${service.status.status} | ${details} |\n`;
        } else {
          markdown += `| ${service.name} | ❓ Not checked | - |\n`;
        }
      });
      
      markdown += '\n';
    }
    
    // Test Suite Results
    if (testSuites.length > 0) {
      markdown += '## 🧪 Test Suite Results\n\n';
      
      testSuites.forEach(suite => {
        const suiteSuccessRate = suite.tests.length > 0 
          ? ((suite.passed / suite.tests.length) * 100).toFixed(1)
          : '0';
        
        const suiteDurationSeconds = (suite.duration / 1000).toFixed(1);
        
        markdown += `### ${suite.name}\n\n`;
        markdown += `- **Tests:** ${suite.tests.length}\n`;
        markdown += `- **Passed:** ${suite.passed} ✅\n`;
        markdown += `- **Failed:** ${suite.failed} ❌\n`;
        markdown += `- **Skipped:** ${suite.skipped} ⏭️\n`;
        markdown += `- **Success Rate:** ${suiteSuccessRate}%\n`;
        markdown += `- **Duration:** ${suiteDurationSeconds}s\n\n`;
        
        // List failed tests
        if (suite.failed > 0) {
          markdown += '**Failed Tests:**\n';
          suite.tests
            .filter(test => test.outcome === 'unexpected' || test.outcome === 'flaky')
            .forEach(test => {
              markdown += `- ❌ ${test.title}\n`;
              if (test.error) {
                markdown += `  - Error: \`${test.error.substring(0, 100)}${test.error.length > 100 ? '...' : ''}\`\n`;
              }
            });
          markdown += '\n';
        }
      });
    }
    
    // Defect Analysis
    if (this.report.defects.length > 0) {
      markdown += '## 🐛 Defect Analysis\n\n';
      
      // Category breakdown
      markdown += '### By Category\n\n';
      Object.entries(defectAnalysis.categories)
        .filter(([_, defects]) => defects.length > 0)
        .sort(([_, a], [__, b]) => b.length - a.length)
        .forEach(([category, defects]) => {
          const categoryIcon = {
            'UI_INTERACTION': '🖱️',
            'API_FAILURE': '🌐',
            'AUTHENTICATION': '🔐',
            'DATA_VALIDATION': '📝',
            'PERFORMANCE': '⚡',
            'NAVIGATION': '🧭',
            'UNKNOWN': '❓'
          }[category] || '❓';
          
          markdown += `- **${categoryIcon} ${category}:** ${defects.length} issues\n`;
        });
      
      markdown += '\n';
      
      // Priority breakdown
      markdown += '### By Priority\n\n';
      Object.entries(defectAnalysis.priorities)
        .filter(([_, defects]) => defects.length > 0)
        .sort(([a], [b]) => {
          const priority = { 'CRITICAL': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3 };
          return priority[a] - priority[b];
        })
        .forEach(([priority, defects]) => {
          const priorityIcon = {
            'CRITICAL': '🚨',
            'HIGH': '⚠️',
            'MEDIUM': '📋',
            'LOW': '📝'
          }[priority] || '📝';
          
          markdown += `- **${priorityIcon} ${priority}:** ${defects.length} issues\n`;
        });
      
      markdown += '\n';
    }
    
    // Performance Analysis
    if (performance.totalDuration > 0) {
      markdown += '## ⚡ Performance Analysis\n\n';
      
      const avgDurationSeconds = (performance.averageTestDuration / 1000).toFixed(1);
      const totalDurationMinutes = (performance.totalDuration / 60000).toFixed(1);
      
      markdown += `- **Total Execution Time:** ${totalDurationMinutes} minutes\n`;
      markdown += `- **Average Test Duration:** ${avgDurationSeconds} seconds\n\n`;
      
      if (performance.slowestSuites.length > 0) {
        markdown += '### Slowest Test Suites\n\n';
        performance.slowestSuites.forEach((suite, index) => {
          const durationSeconds = (suite.duration / 1000).toFixed(1);
          markdown += `${index + 1}. **${suite.name}** - ${durationSeconds}s (${suite.testCount} tests)\n`;
        });
        markdown += '\n';
      }
      
      if (performance.slowestTests.length > 0) {
        markdown += '### Slowest Individual Tests\n\n';
        performance.slowestTests.slice(0, 5).forEach((test, index) => {
          const durationSeconds = (test.duration / 1000).toFixed(1);
          markdown += `${index + 1}. **${test.test}** (${test.suite}) - ${durationSeconds}s\n`;
        });
        markdown += '\n';
      }
    }
    
    // Recommendations
    if (recommendations.length > 0) {
      markdown += '## 💡 Recommendations\n\n';
      
      recommendations
        .sort((a, b) => {
          const priority = { 'CRITICAL': 0, 'HIGH': 1, 'MEDIUM': 2, 'LOW': 3 };
          return priority[a.priority] - priority[b.priority];
        })
        .forEach((rec, index) => {
          const priorityIcon = {
            'CRITICAL': '🚨',
            'HIGH': '⚠️',
            'MEDIUM': '📋',
            'LOW': '💡'
          }[rec.priority] || '💡';
          
          markdown += `### ${index + 1}. ${priorityIcon} ${rec.issue}\n\n`;
          markdown += `**Priority:** ${rec.priority}\n\n`;
          markdown += `**Recommendation:** ${rec.recommendation}\n\n`;
          markdown += '**Action Items:**\n';
          rec.actions.forEach(action => {
            markdown += `- ${action}\n`;
          });
          markdown += '\n';
        });
    }
    
    // Technical Details
    markdown += '## 🔧 Technical Details\n\n';
    markdown += '### Test Configuration\n\n';
    markdown += '- **Framework:** Playwright\n';
    markdown += '- **Browser:** Chromium (latest)\n';
    markdown += '- **Viewport:** 1280x720\n';
    markdown += '- **Timeout:** 60 seconds per test\n';
    markdown += '- **Retries:** 1\n';
    markdown += '- **Parallel Workers:** 2\n\n';
    
    markdown += '### Test Categories\n\n';
    markdown += '1. **Admin Panel Tests** - User management, system monitoring, access control\n';
    markdown += '2. **Candidate Assessment Tests** - CRUD operations, resume upload, ML scoring\n';
    markdown += '3. **Dashboard Tests** - Data visualization, filtering, real-time updates\n';
    markdown += '4. **Job Management Tests** - Job posting, matching algorithms, lifecycle\n';
    markdown += '5. **AI Questionnaire Tests** - ML integration, template management, performance\n\n';
    
    // Footer
    markdown += '---\n\n';
    markdown += '**Report Generated By:** TalentForge Pro Playwright Test Suite\n';
    markdown += `**Framework Version:** Playwright v1.40+\n`;
    markdown += `**Node.js Version:** ${process.version}\n`;
    markdown += `**Generated At:** ${new Date().toLocaleString()}\n`;
    
    return markdown;
  }
  
  /**
   * Save report to file
   */
  async saveReport(content, filename) {
    try {
      // Ensure reports directory exists
      const markdownDir = path.join(this.reportsDir, 'markdown');
      if (!fs.existsSync(markdownDir)) {
        fs.mkdirSync(markdownDir, { recursive: true });
      }
      
      const filepath = path.join(markdownDir, filename);
      fs.writeFileSync(filepath, content, 'utf8');
      
      console.log(`📄 Report saved to: ${filepath}`);
      
      // Also save a timestamped version
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const timestampedFile = filename.replace('.md', `-${timestamp}.md`);
      const timestampedPath = path.join(markdownDir, timestampedFile);
      fs.writeFileSync(timestampedPath, content, 'utf8');
      
      return filepath;
    } catch (error) {
      console.error(`❌ Failed to save report: ${error.message}`);
      throw error;
    }
  }
  
  /**
   * Generate complete test report
   */
  async generateReport() {
    console.log('📊 Generating comprehensive test report...');
    
    try {
      // Load test results
      const playwrightResults = await this.loadTestResults();
      const healthResults = await this.loadHealthResults();
      
      // Parse and analyze results
      this.parseTestResults(playwrightResults);
      
      // Generate markdown report
      const markdownReport = this.generateMarkdownReport(healthResults);
      
      // Save reports
      const reportPath = await this.saveReport(markdownReport, 'test-execution-report.md');
      
      // Print summary
      console.log('\n📋 Report Generation Summary:');
      console.log('━'.repeat(50));
      console.log(`📊 Total Tests: ${this.report.summary.totalTests || 0}`);
      console.log(`✅ Passed: ${this.report.summary.passedTests || 0}`);
      console.log(`❌ Failed: ${this.report.summary.failedTests || 0}`);
      console.log(`📄 Report: ${path.basename(reportPath)}`);
      console.log('━'.repeat(50));
      
      return {
        success: true,
        reportPath,
        summary: this.report.summary
      };
      
    } catch (error) {
      console.error(`💥 Report generation failed: ${error.message}`);
      return {
        success: false,
        error: error.message
      };
    }
  }
}

// Main execution
async function main() {
  const generator = new TestReportGenerator();
  
  const result = await generator.generateReport();
  
  if (result.success) {
    console.log('\n🎉 Test report generation completed successfully!');
    process.exit(0);
  } else {
    console.log('\n❌ Test report generation failed');
    process.exit(1);
  }
}

// Handle command line execution
if (require.main === module) {
  main();
}

module.exports = TestReportGenerator;