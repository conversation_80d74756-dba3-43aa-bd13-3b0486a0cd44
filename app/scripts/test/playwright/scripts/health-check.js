/**
 * TalentForge Pro Health Check Script
 * 
 * Pre-test health validation:
 * - Service availability verification
 * - API endpoint testing
 * - Database connectivity
 * - Authentication system validation
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

class HealthChecker {
  constructor() {
    this.baseUrl = process.env.BASE_URL || 'http://localhost:8088';
    this.apiUrl = `${this.baseUrl}/api/v1`;
    this.devToken = 'dev_bypass_token_2025_talentforge';
    this.timeout = 10000; // 10 seconds
    
    this.results = {
      frontend: null,
      backend: null,
      database: null,
      auth: null,
      overall: false
    };
  }
  
  /**
   * Make HTTP request with timeout
   */
  async makeRequest(url, options = {}) {
    return new Promise((resolve, reject) => {
      const parsedUrl = new URL(url);
      const isHttps = parsedUrl.protocol === 'https:';
      const client = isHttps ? https : http;
      
      const requestOptions = {
        hostname: parsedUrl.hostname,
        port: parsedUrl.port || (isHttps ? 443 : 80),
        path: parsedUrl.pathname + parsedUrl.search,
        method: options.method || 'GET',
        timeout: this.timeout,
        headers: {
          'User-Agent': 'TalentForge-Health-Check/1.0',
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          ...options.headers
        }
      };
      
      const req = client.request(requestOptions, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: data
          });
        });
      });
      
      req.on('error', reject);
      req.on('timeout', () => {
        req.destroy();
        reject(new Error(`Request timeout after ${this.timeout}ms`));
      });
      
      if (options.body) {
        req.write(options.body);
      }
      
      req.end();
    });
  }
  
  /**
   * Check frontend availability
   */
  async checkFrontend() {
    console.log('🌐 Checking frontend availability...');
    
    try {
      const response = await this.makeRequest(this.baseUrl);
      
      if (response.statusCode === 200) {
        console.log('✅ Frontend service is available');
        this.results.frontend = {
          status: 'healthy',
          url: this.baseUrl,
          statusCode: response.statusCode
        };
        return true;
      } else {
        console.log(`❌ Frontend returned status ${response.statusCode}`);
        this.results.frontend = {
          status: 'unhealthy',
          url: this.baseUrl,
          statusCode: response.statusCode,
          error: `HTTP ${response.statusCode}`
        };
        return false;
      }
    } catch (error) {
      console.log(`❌ Frontend check failed: ${error.message}`);
      this.results.frontend = {
        status: 'error',
        url: this.baseUrl,
        error: error.message
      };
      return false;
    }
  }
  
  /**
   * Check backend API availability
   */
  async checkBackend() {
    console.log('🔧 Checking backend API availability...');
    
    const healthUrl = `${this.apiUrl}/health`;
    
    try {
      const response = await this.makeRequest(healthUrl);
      
      if (response.statusCode === 200) {
        console.log('✅ Backend API is available');
        
        let healthData = {};
        try {
          healthData = JSON.parse(response.body);
        } catch (e) {
          healthData = { status: 'ok', raw: response.body };
        }
        
        this.results.backend = {
          status: 'healthy',
          url: healthUrl,
          statusCode: response.statusCode,
          data: healthData
        };
        return true;
      } else {
        console.log(`❌ Backend API returned status ${response.statusCode}`);
        this.results.backend = {
          status: 'unhealthy',
          url: healthUrl,
          statusCode: response.statusCode,
          error: `HTTP ${response.statusCode}`
        };
        return false;
      }
    } catch (error) {
      console.log(`❌ Backend API check failed: ${error.message}`);
      this.results.backend = {
        status: 'error',
        url: healthUrl,
        error: error.message
      };
      return false;
    }
  }
  
  /**
   * Check database connectivity through API
   */
  async checkDatabase() {
    console.log('🗄️ Checking database connectivity...');
    
    const dbHealthUrl = `${this.apiUrl}/health/db`;
    
    try {
      const response = await this.makeRequest(dbHealthUrl);
      
      if (response.statusCode === 200) {
        console.log('✅ Database is connected');
        
        let dbData = {};
        try {
          dbData = JSON.parse(response.body);
        } catch (e) {
          dbData = { status: 'connected', raw: response.body };
        }
        
        this.results.database = {
          status: 'healthy',
          url: dbHealthUrl,
          statusCode: response.statusCode,
          data: dbData
        };
        return true;
      } else {
        console.log(`❌ Database health check returned status ${response.statusCode}`);
        this.results.database = {
          status: 'unhealthy',
          url: dbHealthUrl,
          statusCode: response.statusCode,
          error: `HTTP ${response.statusCode}`
        };
        return false;
      }
    } catch (error) {
      console.log(`⚠️ Database health check failed: ${error.message}`);
      this.results.database = {
        status: 'error',
        url: dbHealthUrl,
        error: error.message
      };
      // Database check failure is not critical for frontend tests
      return true;
    }
  }
  
  /**
   * Check authentication system
   */
  async checkAuthentication() {
    console.log('🔐 Checking authentication system...');
    
    const authUrl = `${this.apiUrl}/auth/me`;
    
    try {
      const response = await this.makeRequest(authUrl, {
        headers: {
          'Authorization': `Bearer ${this.devToken}`
        }
      });
      
      if (response.statusCode === 200) {
        console.log('✅ Authentication system is working');
        
        let authData = {};
        try {
          authData = JSON.parse(response.body);
          console.log(`   Authenticated as: ${authData.email || 'Unknown User'}`);
        } catch (e) {
          authData = { status: 'authenticated', raw: response.body };
        }
        
        this.results.auth = {
          status: 'healthy',
          url: authUrl,
          statusCode: response.statusCode,
          data: authData
        };
        return true;
      } else if (response.statusCode === 401) {
        console.log('⚠️ Dev token authentication failed (401)');
        this.results.auth = {
          status: 'token_invalid',
          url: authUrl,
          statusCode: response.statusCode,
          error: 'Dev token may be invalid or disabled'
        };
        // Auth failure is not critical if we can test login flow
        return true;
      } else {
        console.log(`❌ Authentication check returned status ${response.statusCode}`);
        this.results.auth = {
          status: 'unhealthy',
          url: authUrl,
          statusCode: response.statusCode,
          error: `HTTP ${response.statusCode}`
        };
        return false;
      }
    } catch (error) {
      console.log(`❌ Authentication check failed: ${error.message}`);
      this.results.auth = {
        status: 'error',
        url: authUrl,
        error: error.message
      };
      return false;
    }
  }
  
  /**
   * Check additional API endpoints
   */
  async checkCriticalEndpoints() {
    console.log('🎯 Checking critical API endpoints...');
    
    const endpoints = [
      '/dashboard/stats',
      '/candidates/',
      '/positions/'
    ];
    
    const endpointResults = [];
    
    for (const endpoint of endpoints) {
      const url = `${this.apiUrl}${endpoint}`;
      
      try {
        const response = await this.makeRequest(url, {
          headers: {
            'Authorization': `Bearer ${this.devToken}`
          }
        });
        
        const isHealthy = response.statusCode >= 200 && response.statusCode < 300;
        const status = isHealthy ? '✅' : '❌';
        
        console.log(`   ${status} ${endpoint} (${response.statusCode})`);
        
        endpointResults.push({
          endpoint,
          url,
          statusCode: response.statusCode,
          healthy: isHealthy
        });
        
      } catch (error) {
        console.log(`   ❌ ${endpoint} (${error.message})`);
        endpointResults.push({
          endpoint,
          url,
          error: error.message,
          healthy: false
        });
      }
    }
    
    const healthyCount = endpointResults.filter(r => r.healthy).length;
    const totalCount = endpointResults.length;
    
    console.log(`   Summary: ${healthyCount}/${totalCount} endpoints healthy`);
    
    return endpointResults;
  }
  
  /**
   * Generate health report
   */
  generateReport() {
    const timestamp = new Date().toISOString();
    
    const report = {
      timestamp,
      baseUrl: this.baseUrl,
      results: this.results,
      summary: {
        frontend: this.results.frontend?.status || 'unknown',
        backend: this.results.backend?.status || 'unknown',
        database: this.results.database?.status || 'unknown',
        auth: this.results.auth?.status || 'unknown',
        overall: this.results.overall
      }
    };
    
    return report;
  }
  
  /**
   * Print health summary
   */
  printSummary() {
    console.log('\n📋 Health Check Summary:');
    console.log('━'.repeat(50));
    
    const services = [
      { name: 'Frontend Service', result: this.results.frontend },
      { name: 'Backend API', result: this.results.backend },
      { name: 'Database', result: this.results.database },
      { name: 'Authentication', result: this.results.auth }
    ];
    
    services.forEach(service => {
      if (!service.result) {
        console.log(`❓ ${service.name}: Not checked`);
        return;
      }
      
      const statusIcon = {
        'healthy': '✅',
        'unhealthy': '❌',
        'error': '❌',
        'token_invalid': '⚠️',
        'unknown': '❓'
      }[service.result.status] || '❓';
      
      console.log(`${statusIcon} ${service.name}: ${service.result.status}`);
      
      if (service.result.error) {
        console.log(`   Error: ${service.result.error}`);
      }
    });
    
    console.log('━'.repeat(50));
    
    if (this.results.overall) {
      console.log('🎉 Overall Status: Ready for testing');
    } else {
      console.log('⚠️ Overall Status: Issues detected');
      console.log('   Some services may not be available, but testing can proceed');
    }
  }
  
  /**
   * Run comprehensive health check
   */
  async runHealthCheck() {
    console.log('🏥 Starting TalentForge Pro health check...\n');
    
    const startTime = Date.now();
    
    // Run all checks
    const frontendHealthy = await this.checkFrontend();
    const backendHealthy = await this.checkBackend();
    const databaseHealthy = await this.checkDatabase();
    const authHealthy = await this.checkAuthentication();
    
    // Check additional endpoints if backend is healthy
    let endpointResults = [];
    if (backendHealthy) {
      endpointResults = await this.checkCriticalEndpoints();
    }
    
    // Determine overall health
    // Frontend and backend must be healthy for tests to run
    this.results.overall = frontendHealthy && backendHealthy;
    
    const endTime = Date.now();
    const duration = endTime - startTime;
    
    console.log(`\n⏱️ Health check completed in ${duration}ms`);
    
    this.printSummary();
    
    // Save report
    const report = this.generateReport();
    report.endpointResults = endpointResults;
    report.duration = duration;
    
    try {
      const fs = require('fs');
      const path = require('path');
      
      const reportsDir = path.join(__dirname, '../reports');
      if (!fs.existsSync(reportsDir)) {
        fs.mkdirSync(reportsDir, { recursive: true });
      }
      
      const reportPath = path.join(reportsDir, 'health-check.json');
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
      console.log(`📄 Health report saved to: ${reportPath}`);
      
    } catch (error) {
      console.log(`⚠️ Could not save health report: ${error.message}`);
    }
    
    return this.results.overall;
  }
}

// Main execution
async function main() {
  const healthChecker = new HealthChecker();
  
  try {
    const isHealthy = await healthChecker.runHealthCheck();
    
    if (isHealthy) {
      console.log('\n🚀 System is ready for E2E testing!');
      process.exit(0);
    } else {
      console.log('\n⚠️ System has issues but testing can proceed with caution');
      console.log('   Some tests may fail due to service unavailability');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('\n💥 Health check failed with error:');
    console.error(error.message);
    process.exit(2);
  }
}

// Handle command line execution
if (require.main === module) {
  main();
}

module.exports = HealthChecker;