/**
 * Job Management E2E Tests - Priority 4
 * 
 * Comprehensive black-box testing of job management functionality:
 * - Job posting creation and management
 * - Candidate-job matching algorithms
 * - Job lifecycle management
 * - Job performance analytics
 * - Position requirements and specifications
 */

import { test, expect, Page } from '@playwright/test';
import { AuthHelper } from '../../utils/auth-helper';
import { ApiValidator } from '../../utils/api-validator';
import { TestConfig } from '../../utils/test-config';

test.describe('Job Management - Core Functionality', () => {
  let authHelper: AuthHelper;
  let apiValidator: ApiValidator;
  let config: TestConfig;
  
  test.beforeEach(async ({ page }) => {
    authHelper = new AuthHelper(page);
    config = new TestConfig();
    apiValidator = new ApiValidator(config);
    
    // Initialize API validator
    await apiValidator.initialize(page.request, config.devToken);
    
    // Authenticate as admin
    await authHelper.loginWithDevToken();
  });
  
  test('Job listing should load and display properly', async ({ page }) => {
    await test.step('Navigate to jobs page', async () => {
      await page.goto('/jobs');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Verify job list components', async () => {
      // Check main jobs container
      await expect(page.locator('[data-testid="jobs-list"], .jobs-container, table, .positions-list')).toBeVisible();
      
      // Check essential list elements
      const listElements = [
        'Title',
        'Department',
        'Location',
        'Status',
        'Posted',
        'Applications'
      ];
      
      for (const element of listElements) {
        const headerElement = page.locator(`thead th:has-text("${element}"), th:text-is("${element}"), [data-column="${element.toLowerCase()}"]`);
        if (await headerElement.count() > 0) {
          await expect(headerElement.first()).toBeVisible();
        }
      }
    });
    
    await test.step('Verify job search and filter functionality', async () => {
      // Test job search functionality
      const searchInput = page.locator('[data-testid="job-search"], input[placeholder*="search"], .search-input');
      if (await searchInput.isVisible()) {
        await searchInput.fill('engineer');
        await page.waitForTimeout(1000);
        await expect(page.locator('tbody tr, .job-row, .position-item')).toBeVisible({ timeout: 10000 });
      }
      
      // Test department filter
      const departmentFilter = page.locator('[data-testid="department-filter"], select[name*="department"], .department-filter');
      if (await departmentFilter.isVisible()) {
        const options = ['engineering', 'sales', 'marketing'];
        for (const option of options) {
          if (await departmentFilter.locator(`option[value="${option}"]`).count() > 0) {
            await departmentFilter.selectOption(option);
            await page.waitForTimeout(500);
            break;
          }
        }
      }
      
      // Test location filter
      const locationFilter = page.locator('[data-testid="location-filter"], select[name*="location"]');
      if (await locationFilter.isVisible()) {
        const locations = ['remote', 'onsite', 'hybrid'];
        for (const location of locations) {
          if (await locationFilter.locator(`option[value="${location}"]`).count() > 0) {
            await locationFilter.selectOption(location);
            await page.waitForTimeout(500);
            break;
          }
        }
      }
    });
    
    await test.step('Validate jobs API endpoint', async () => {
      const jobsResult = await apiValidator.validateSpecificEndpoint('/positions/');
      expect(jobsResult?.success).toBe(true);
    });
  });
  
  test('Job creation workflow should work end-to-end', async ({ page }) => {
    await test.step('Navigate to job creation', async () => {
      await page.goto('/jobs');
      await page.waitForLoadState('networkidle');
      
      // Click create job button
      const createButtons = [
        '[data-testid="create-job-btn"]',
        'button:has-text("Create Job")',
        'button:has-text("Add Job")',
        'button:has-text("Post Job")',
        '.create-job-button',
        'a[href*="create"]'
      ];
      
      let clicked = false;
      for (const selector of createButtons) {
        if (await page.locator(selector).isVisible({ timeout: 2000 })) {
          await page.click(selector);
          clicked = true;
          break;
        }
      }
      
      if (!clicked) {
        // Try navigation approach
        await page.goto('/jobs/create');
      }
      
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Fill job creation form', async () => {
      const testJob = {
        title: `Test Job Position ${Date.now()}`,
        department: 'Engineering',
        location: 'Remote',
        employmentType: 'full-time',
        description: 'This is a test job position created by automated testing.',
        requirements: 'JavaScript, Python, Testing experience',
        salary: '75000'
      };
      
      // Fill basic job information
      await page.fill('[name="title"], [data-testid="job-title"], input[placeholder*="title"]', testJob.title);
      
      // Department field (could be input or select)
      const departmentField = page.locator('[name="department"], [data-testid="department"]');
      if (await departmentField.isVisible()) {
        if (await departmentField.tagName() === 'SELECT') {
          await departmentField.selectOption(testJob.department);
        } else {
          await departmentField.fill(testJob.department);
        }
      }
      
      // Location field
      const locationField = page.locator('[name="location"], [data-testid="location"]');
      if (await locationField.isVisible()) {
        if (await locationField.tagName() === 'SELECT') {
          await locationField.selectOption(testJob.location);
        } else {
          await locationField.fill(testJob.location);
        }
      }
      
      // Employment type
      const employmentTypeField = page.locator('[name="employment_type"], [name="employmentType"], [data-testid="employment-type"]');
      if (await employmentTypeField.isVisible()) {
        if (await employmentTypeField.tagName() === 'SELECT') {
          await employmentTypeField.selectOption(testJob.employmentType);
        } else {
          await employmentTypeField.fill(testJob.employmentType);
        }
      }
      
      // Job description
      const descriptionField = page.locator('[name="description"], [data-testid="description"], textarea[placeholder*="description"]');
      if (await descriptionField.isVisible()) {
        await descriptionField.fill(testJob.description);
      }
      
      // Requirements
      const requirementsField = page.locator('[name="requirements"], [data-testid="requirements"], textarea[placeholder*="requirement"]');
      if (await requirementsField.isVisible()) {
        await requirementsField.fill(testJob.requirements);
      }
      
      // Salary
      const salaryField = page.locator('[name="salary"], [name="salary_min"], [data-testid="salary"]');
      if (await salaryField.isVisible()) {
        await salaryField.fill(testJob.salary);
      }
    });
    
    await test.step('Submit job creation form', async () => {
      // Submit the form
      const submitButtons = [
        'button[type="submit"]',
        '[data-testid="submit-job"]',
        'button:has-text("Create")',
        'button:has-text("Post Job")',
        'button:has-text("Publish")',
        '.submit-button'
      ];
      
      for (const selector of submitButtons) {
        if (await page.locator(selector).isVisible({ timeout: 2000 })) {
          await page.click(selector);
          break;
        }
      }
      
      // Wait for success feedback
      const successIndicators = [
        'text=created successfully',
        'text=posted successfully',
        'text=published successfully',
        '.success-message',
        '.alert-success',
        '[data-testid="success-message"]'
      ];
      
      let successFound = false;
      for (const indicator of successIndicators) {
        if (await page.locator(indicator).isVisible({ timeout: 10000 })) {
          await expect(page.locator(indicator)).toBeVisible();
          successFound = true;
          break;
        }
      }
      
      // If no success message, check if we were redirected to jobs list
      if (!successFound) {
        await expect(page).toHaveURL(/jobs/, { timeout: 10000 });
      }
    });
    
    await test.step('Verify job was created via API', async () => {
      const jobsResult = await apiValidator.validateSpecificEndpoint('/positions/');
      expect(jobsResult?.success).toBe(true);
      expect(jobsResult?.data).toBeDefined();
    });
  });
  
  test('Job matching functionality should work with candidate profiles', async ({ page }) => {
    await test.step('Navigate to job matching interface', async () => {
      await page.goto('/jobs');
      await page.waitForLoadState('networkidle');
      
      // Look for job matching functionality
      const matchingButtons = [
        '[data-testid="find-candidates"]',
        'button:has-text("Match Candidates")',
        'button:has-text("Find Match")',
        '.matching-button',
        'a[href*="match"]'
      ];
      
      let matchingFound = false;
      for (const selector of matchingButtons) {
        if (await page.locator(selector).isVisible({ timeout: 3000 })) {
          await page.click(selector);
          matchingFound = true;
          break;
        }
      }
      
      if (!matchingFound) {
        // Look for existing job to view details
        const jobRows = page.locator('tbody tr, .job-row, .position-item');
        if (await jobRows.count() > 0) {
          await jobRows.first().click();
          await page.waitForLoadState('networkidle');
        }
      }
    });
    
    await test.step('Test candidate matching interface', async () => {
      // Look for matching-related components
      const matchingElements = [
        'Candidate Match',
        'Skills Match',
        'Match Score',
        'Compatibility',
        'Recommended Candidates'
      ];
      
      for (const element of matchingElements) {
        const elementLocator = page.locator(`text=${element}, [data-testid*="${element.toLowerCase().replace(/\s+/g, '-')}"]`);
        if (await elementLocator.count() > 0) {
          console.log(`Found matching element: ${element}`);
          break;
        }
      }
    });
    
    await test.step('Test matching criteria configuration', async () => {
      // Look for matching criteria controls
      const matchingControls = [
        '[data-testid="skills-weight"]',
        'input[name*="experience"]',
        'select[name*="priority"]',
        '.matching-criteria',
        '.filter-controls'
      ];
      
      for (const selector of matchingControls) {
        const control = page.locator(selector);
        if (await control.isVisible({ timeout: 3000 })) {
          // Interact with matching controls
          if (await control.getAttribute('type') === 'range') {
            // Adjust slider controls
            await control.fill('70');
          } else if (await control.tagName() === 'SELECT') {
            const options = await control.locator('option').allTextContents();
            if (options.length > 1) {
              await control.selectOption({ index: 1 });
            }
          }
          break;
        }
      }
    });
    
    await test.step('Verify candidate search integration', async () => {
      // Test if candidate search works for job matching
      const candidateSearch = await apiValidator.validateSpecificEndpoint('/candidates/search');
      expect(candidateSearch?.success).toBe(true);
    });
  });
  
  test('Job lifecycle management should work properly', async ({ page }) => {
    await test.step('Navigate to job details for lifecycle testing', async () => {
      await page.goto('/jobs');
      await page.waitForLoadState('networkidle');
      
      // Find and access a job for lifecycle testing
      const jobRows = page.locator('tbody tr, .job-item, .position-row');
      const rowCount = await jobRows.count();
      
      if (rowCount > 0) {
        await jobRows.first().click();
        await page.waitForLoadState('networkidle');
      } else {
        // Skip if no jobs available
        test.skip('No jobs available for lifecycle testing');
        return;
      }
    });
    
    await test.step('Test job status transitions', async () => {
      // Look for status controls
      const statusControls = [
        '[data-testid="job-status"]',
        'select[name*="status"]',
        '.status-dropdown',
        'button:has-text("Status")'
      ];
      
      for (const selector of statusControls) {
        const statusControl = page.locator(selector);
        if (await statusControl.isVisible({ timeout: 3000 })) {
          if (await statusControl.tagName() === 'SELECT') {
            // Test different status options
            const statusOptions = ['draft', 'active', 'paused', 'closed'];
            for (const status of statusOptions) {
              if (await statusControl.locator(`option[value="${status}"]`).count() > 0) {
                await statusControl.selectOption(status);
                await page.waitForTimeout(500);
                break;
              }
            }
          }
          break;
        }
      }
    });
    
    await test.step('Test job editing functionality', async () => {
      // Look for edit buttons
      const editButtons = [
        '[data-testid="edit-job"]',
        'button:has-text("Edit")',
        '.edit-button',
        'a:has-text("Edit")'
      ];
      
      for (const selector of editButtons) {
        const editButton = page.locator(selector);
        if (await editButton.isVisible({ timeout: 3000 })) {
          await editButton.click();
          await page.waitForLoadState('networkidle');
          
          // Verify edit form is accessible
          const editForm = page.locator('form, .edit-form, [data-testid="job-edit-form"]');
          if (await editForm.isVisible({ timeout: 5000 })) {
            await expect(editForm).toBeVisible();
            
            // Make a minor edit
            const titleField = page.locator('[name="title"], input[placeholder*="title"]');
            if (await titleField.isVisible() && await titleField.isEditable()) {
              const currentTitle = await titleField.inputValue();
              await titleField.fill(currentTitle + ' (Updated)');
            }
          }
          break;
        }
      }
    });
    
    await test.step('Test job archiving/deletion', async () => {
      // Look for archive or delete options
      const archiveButtons = [
        '[data-testid="archive-job"]',
        'button:has-text("Archive")',
        'button:has-text("Delete")',
        '.archive-button',
        '.delete-button'
      ];
      
      for (const selector of archiveButtons) {
        const button = page.locator(selector);
        if (await button.isVisible({ timeout: 3000 })) {
          await button.click();
          
          // Look for confirmation dialog
          const confirmDialog = page.locator('[role="dialog"], .modal, .confirmation');
          if (await confirmDialog.isVisible({ timeout: 3000 })) {
            // Cancel the action to avoid actually deleting
            const cancelButton = page.locator('button:has-text("Cancel"), .cancel-button');
            if (await cancelButton.isVisible()) {
              await cancelButton.click();
            }
          }
          break;
        }
      }
    });
  });
  
  test('Job performance analytics should display correctly', async ({ page }) => {
    await test.step('Navigate to job analytics', async () => {
      await page.goto('/jobs');
      await page.waitForLoadState('networkidle');
      
      // Look for analytics or reporting features
      const analyticsLinks = [
        'a:has-text("Analytics")',
        'a:has-text("Reports")',
        '[data-testid="job-analytics"]',
        'button:has-text("Performance")'
      ];
      
      let analyticsFound = false;
      for (const selector of analyticsLinks) {
        if (await page.locator(selector).isVisible({ timeout: 3000 })) {
          await page.click(selector);
          await page.waitForLoadState('networkidle');
          analyticsFound = true;
          break;
        }
      }
      
      if (!analyticsFound) {
        // Try accessing analytics for a specific job
        const jobRows = page.locator('tbody tr, .job-row');
        if (await jobRows.count() > 0) {
          await jobRows.first().click();
          await page.waitForLoadState('networkidle');
        }
      }
    });
    
    await test.step('Verify analytics components', async () => {
      // Look for analytics elements
      const analyticsElements = [
        'Applications Received',
        'View Count',
        'Response Rate',
        'Time to Fill',
        'Cost per Hire',
        'Quality Score'
      ];
      
      let analyticsFound = 0;
      for (const element of analyticsElements) {
        const elementLocator = page.locator(`text=${element}, [data-testid*="${element.toLowerCase().replace(/\s+/g, '-')}"]`);
        if (await elementLocator.isVisible({ timeout: 3000 })) {
          await expect(elementLocator).toBeVisible();
          analyticsFound++;
        }
      }
      
      console.log(`Found ${analyticsFound} analytics elements`);
    });
    
    await test.step('Test analytics charts and visualizations', async () => {
      // Look for chart elements
      const chartElements = [
        'canvas',
        'svg',
        '.chart',
        '.graph',
        '[data-testid*="chart"]',
        '.analytics-chart'
      ];
      
      for (const selector of chartElements) {
        const chartElement = page.locator(selector);
        if (await chartElement.isVisible({ timeout: 3000 })) {
          await expect(chartElement).toBeVisible();
          
          // Test chart interactions
          await chartElement.hover();
          await page.waitForTimeout(500);
          
          // Look for tooltips or chart interactions
          const tooltip = page.locator('.tooltip, .chart-tooltip');
          if (await tooltip.isVisible({ timeout: 2000 })) {
            await expect(tooltip).toBeVisible();
          }
          break;
        }
      }
    });
    
    await test.step('Test performance metrics filtering', async () => {
      // Look for time period filters
      const timeFilters = [
        '[data-testid="time-filter"]',
        'select[name*="period"]',
        '.date-range-picker',
        'input[type="date"]'
      ];
      
      for (const selector of timeFilters) {
        const filter = page.locator(selector);
        if (await filter.isVisible({ timeout: 3000 })) {
          if (await filter.tagName() === 'SELECT') {
            const options = ['30days', '90days', '6months', '1year'];
            for (const option of options) {
              if (await filter.locator(`option[value*="${option}"]`).count() > 0) {
                await filter.selectOption({ value: option });
                await page.waitForTimeout(1000);
                break;
              }
            }
          }
          break;
        }
      }
    });
  });
  
  test('Job application workflow should integrate properly', async ({ page }) => {
    await test.step('Navigate to job with application functionality', async () => {
      await page.goto('/jobs');
      await page.waitForLoadState('networkidle');
      
      // Find a job to test application workflow
      const jobRows = page.locator('tbody tr, .job-item');
      if (await jobRows.count() > 0) {
        await jobRows.first().click();
        await page.waitForLoadState('networkidle');
      }
    });
    
    await test.step('Test application interface', async () => {
      // Look for application-related elements
      const applicationElements = [
        'Apply Now',
        'Submit Application',
        'View Applications',
        'Applications Received',
        'Applicants'
      ];
      
      for (const element of applicationElements) {
        const elementLocator = page.locator(`text=${element}, button:has-text("${element}"), [data-testid*="${element.toLowerCase().replace(/\s+/g, '-')}"]`);
        if (await elementLocator.isVisible({ timeout: 3000 })) {
          await expect(elementLocator).toBeVisible();
          console.log(`Found application element: ${element}`);
          break;
        }
      }
    });
    
    await test.step('Test application list management', async () => {
      // Look for application management interface
      const applicationLists = [
        '.applications-list',
        '[data-testid="applications"]',
        'table:has-text("Application")',
        '.applicants-table'
      ];
      
      for (const selector of applicationLists) {
        const applicationList = page.locator(selector);
        if (await applicationList.isVisible({ timeout: 3000 })) {
          await expect(applicationList).toBeVisible();
          
          // Check for application status controls
          const statusControls = applicationList.locator('select, .status-dropdown');
          if (await statusControls.count() > 0) {
            console.log('Found application status controls');
          }
          break;
        }
      }
    });
  });
  
  test('Job management should handle errors gracefully', async ({ page }) => {
    await test.step('Test API failure handling', async () => {
      await page.goto('/jobs');
      
      // Intercept job API calls and make them fail
      await page.route('**/api/v1/positions/**', route => {
        route.abort('failed');
      });
      
      // Refresh to trigger API calls
      await page.reload();
      
      // Should show appropriate error messages
      const errorMessages = [
        'text=Failed to load jobs',
        'text=Error loading positions',
        '.error-message',
        '.jobs-error',
        'text=Unable to fetch'
      ];
      
      let errorFound = false;
      for (const selector of errorMessages) {
        if (await page.locator(selector).isVisible({ timeout: 10000 })) {
          await expect(page.locator(selector)).toBeVisible();
          errorFound = true;
          break;
        }
      }
      
      // Should provide retry functionality
      const retryButtons = [
        'button:has-text("Retry")',
        'button:has-text("Reload")',
        '.retry-button'
      ];
      
      for (const selector of retryButtons) {
        if (await page.locator(selector).isVisible({ timeout: 3000 })) {
          await expect(page.locator(selector)).toBeVisible();
          break;
        }
      }
      
      // Remove route interception
      await page.unroute('**/api/v1/positions/**');
    });
    
    await test.step('Test form validation errors', async () => {
      await page.goto('/jobs/create');
      await page.waitForLoadState('networkidle');
      
      // Try to submit empty form
      const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Post")');
      if (await submitButton.isVisible()) {
        await submitButton.click();
        
        // Should show validation errors
        const validationErrors = [
          '.field-error',
          '.validation-error',
          'text=required',
          'text=invalid',
          '[role="alert"]'
        ];
        
        for (const selector of validationErrors) {
          if (await page.locator(selector).isVisible({ timeout: 5000 })) {
            await expect(page.locator(selector)).toBeVisible();
            break;
          }
        }
      }
    });
  });
  
  test('Job management should be responsive on different screen sizes', async ({ page }) => {
    await test.step('Test desktop layout', async () => {
      await page.setViewportSize({ width: 1280, height: 720 });
      await page.goto('/jobs');
      await page.waitForLoadState('networkidle');
      
      await expect(page.locator('.jobs-container, [data-testid="jobs-list"], table')).toBeVisible();
    });
    
    await test.step('Test tablet layout', async () => {
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Jobs should still be accessible
      await expect(page.locator('.jobs-container, [data-testid="jobs-list"]')).toBeVisible();
    });
    
    await test.step('Test mobile layout', async () => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Should have mobile-optimized layout
      await expect(page.locator('body')).toBeVisible();
      
      // Check for mobile navigation
      const mobileMenu = page.locator('.mobile-menu, .hamburger-menu');
      if (await mobileMenu.isVisible({ timeout: 3000 })) {
        await expect(mobileMenu).toBeVisible();
      }
    });
  });
});