/**
 * Dashboard E2E Tests - Priority 3
 * 
 * Comprehensive black-box testing of dashboard functionality:
 * - Data loading and visualization
 * - Filtering and search functionality  
 * - Analytics display and real-time updates
 * - Data export functionality
 * - Performance monitoring
 */

import { test, expect, Page } from '@playwright/test';
import { AuthHelper } from '../../utils/auth-helper';
import { ApiValidator } from '../../utils/api-validator';
import { TestConfig } from '../../utils/test-config';

test.describe('Dashboard - Core Functionality', () => {
  let authHelper: AuthHelper;
  let apiValidator: ApiValidator;
  let config: TestConfig;
  
  test.beforeEach(async ({ page }) => {
    authHelper = new AuthHelper(page);
    config = new TestConfig();
    apiValidator = new ApiValidator(config);
    
    // Initialize API validator
    await apiValidator.initialize(page.request, config.devToken);
    
    // Authenticate as admin
    await authHelper.loginWithDevToken();
  });
  
  test('Dashboard should load with all key metrics and visualizations', async ({ page }) => {
    await test.step('Navigate to dashboard', async () => {
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Verify main dashboard components are visible', async () => {
      // Check for main dashboard container
      await expect(page.locator('[data-testid="dashboard"], .dashboard-container, .main-dashboard')).toBeVisible();
      
      // Key metrics cards should be present
      const metricCards = [
        'Total Candidates',
        'Active Positions', 
        'Applications',
        'Interviews',
        'Placements',
        'Success Rate'
      ];
      
      let metricsFound = 0;
      for (const metric of metricCards) {
        const metricLocator = page.locator(`text=${metric}, [data-testid*="${metric.toLowerCase().replace(/\s+/g, '-')}"], .metric:has-text("${metric}")`);
        if (await metricLocator.isVisible({ timeout: 3000 })) {
          await expect(metricLocator).toBeVisible();
          metricsFound++;
        }
      }
      
      // Should have at least 3 metric cards visible
      expect(metricsFound).toBeGreaterThanOrEqual(3);
    });
    
    await test.step('Verify charts and visualizations are rendered', async () => {
      // Look for chart containers
      const chartElements = [
        'canvas',
        'svg',
        '.chart',
        '.graph',
        '[data-testid*="chart"]',
        '.recharts-container',
        '.apexcharts-canvas'
      ];
      
      let chartsFound = 0;
      for (const selector of chartElements) {
        const chartElement = page.locator(selector);
        const count = await chartElement.count();
        if (count > 0) {
          await expect(chartElement.first()).toBeVisible();
          chartsFound += count;
        }
      }
      
      // Should have at least one chart/visualization
      expect(chartsFound).toBeGreaterThanOrEqual(1);
    });
    
    await test.step('Validate dashboard API endpoints', async () => {
      const dashboardStats = await apiValidator.validateSpecificEndpoint('/dashboard/stats');
      expect(dashboardStats?.success).toBe(true);
      
      const recentActivity = await apiValidator.validateSpecificEndpoint('/dashboard/recent-activity');
      expect(recentActivity?.success).toBe(true);
    });
  });
  
  test('Dashboard filtering and search should work properly', async ({ page }) => {
    await test.step('Navigate to dashboard', async () => {
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Test date range filtering', async () => {
      // Look for date picker or filter controls
      const dateFilters = [
        '[data-testid="date-filter"]',
        'input[type="date"]',
        '.date-picker',
        '.date-range-picker',
        'select[name*="period"]'
      ];
      
      for (const selector of dateFilters) {
        const filterElement = page.locator(selector);
        if (await filterElement.isVisible({ timeout: 3000 })) {
          if (await filterElement.getAttribute('type') === 'date') {
            // Set a date range
            await filterElement.fill('2024-01-01');
          } else if (await filterElement.tagName() === 'SELECT') {
            // Select a predefined period
            const options = ['7days', '30days', '90days', 'month', 'quarter'];
            for (const option of options) {
              if (await filterElement.locator(`option[value*="${option}"]`).count() > 0) {
                await filterElement.selectOption({ value: option });
                break;
              }
            }
          }
          
          // Wait for data to update
          await page.waitForTimeout(1000);
          break;
        }
      }
    });
    
    await test.step('Test dashboard search functionality', async () => {
      const searchElements = [
        '[data-testid="dashboard-search"]',
        'input[placeholder*="search"]',
        '.search-input',
        '.global-search'
      ];
      
      for (const selector of searchElements) {
        const searchElement = page.locator(selector);
        if (await searchElement.isVisible({ timeout: 3000 })) {
          await searchElement.fill('test candidate');
          await page.waitForTimeout(1000);
          
          // Check for search results or filtered content
          const searchResults = page.locator('.search-results, .filtered-results, tbody tr');
          if (await searchResults.count() > 0) {
            await expect(searchResults.first()).toBeVisible();
          }
          
          // Clear search
          await searchElement.clear();
          break;
        }
      }
    });
    
    await test.step('Test metric filters and drill-down', async () => {
      // Look for clickable metric cards that might filter the dashboard
      const metricCards = page.locator('.metric-card, .dashboard-card, [data-testid*="metric"]');
      const cardCount = await metricCards.count();
      
      if (cardCount > 0) {
        // Click on first metric card to see if it filters content
        await metricCards.first().click();
        await page.waitForTimeout(1000);
        
        // Check if any filtering occurred
        const filteredContent = page.locator('.filtered, .active-filter, .drill-down');
        if (await filteredContent.count() > 0) {
          await expect(filteredContent.first()).toBeVisible();
        }
      }
    });
  });
  
  test('Dashboard analytics should display and update correctly', async ({ page }) => {
    await test.step('Navigate to dashboard', async () => {
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Verify analytics components are present', async () => {
      // Check for analytics sections
      const analyticsSections = [
        'Performance Metrics',
        'Trends',
        'Analytics', 
        'Statistics',
        'Key Performance',
        'Reports'
      ];
      
      let analyticsFound = 0;
      for (const section of analyticsSections) {
        const sectionLocator = page.locator(`text=${section}, [data-testid*="${section.toLowerCase().replace(/\s+/g, '-')}"], h2:has-text("${section}"), h3:has-text("${section}")`);
        if (await sectionLocator.isVisible({ timeout: 3000 })) {
          await expect(sectionLocator).toBeVisible();
          analyticsFound++;
        }
      }
      
      // Should have at least one analytics section
      expect(analyticsFound).toBeGreaterThanOrEqual(1);
    });
    
    await test.step('Test chart interactions', async () => {
      // Look for interactive chart elements
      const interactiveElements = [
        'canvas',
        '.chart-point',
        '.chart-bar',
        '.legend-item',
        '.tooltip-trigger'
      ];
      
      for (const selector of interactiveElements) {
        const element = page.locator(selector);
        if (await element.isVisible({ timeout: 3000 })) {
          // Try to hover over chart element to trigger tooltip
          await element.first().hover();
          await page.waitForTimeout(500);
          
          // Look for tooltip or hover effects
          const tooltip = page.locator('.tooltip, .chart-tooltip, .hover-info');
          if (await tooltip.isVisible({ timeout: 2000 })) {
            await expect(tooltip).toBeVisible();
          }
          break;
        }
      }
    });
    
    await test.step('Verify data refresh capability', async () => {
      // Look for refresh button
      const refreshButtons = [
        '[data-testid="refresh-dashboard"]',
        'button:has-text("Refresh")',
        '.refresh-button',
        '[title="Refresh"]',
        '.reload-data'
      ];
      
      for (const selector of refreshButtons) {
        const refreshButton = page.locator(selector);
        if (await refreshButton.isVisible({ timeout: 3000 })) {
          await refreshButton.click();
          
          // Wait for loading indicator
          const loadingIndicators = [
            '.loading',
            '.spinner',
            'text=Loading',
            '.refresh-indicator'
          ];
          
          for (const loadingSelector of loadingIndicators) {
            const loading = page.locator(loadingSelector);
            if (await loading.isVisible({ timeout: 2000 })) {
              // Wait for loading to complete
              await expect(loading).not.toBeVisible({ timeout: 10000 });
              break;
            }
          }
          break;
        }
      }
    });
  });
  
  test('Dashboard export functionality should work properly', async ({ page }) => {
    await test.step('Navigate to dashboard', async () => {
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Test data export options', async () => {
      // Look for export buttons
      const exportButtons = [
        '[data-testid="export-dashboard"]',
        'button:has-text("Export")',
        'button:has-text("Download")',
        '.export-button',
        '.download-data'
      ];
      
      let exportButton;
      for (const selector of exportButtons) {
        if (await page.locator(selector).isVisible({ timeout: 3000 })) {
          exportButton = page.locator(selector);
          break;
        }
      }
      
      if (exportButton) {
        // Click export button
        await exportButton.click();
        
        // Look for export options dialog/menu
        const exportOptions = [
          'text=PDF',
          'text=Excel', 
          'text=CSV',
          'text=PNG',
          '.export-option',
          '.download-format'
        ];
        
        for (const option of exportOptions) {
          const optionElement = page.locator(option);
          if (await optionElement.isVisible({ timeout: 5000 })) {
            await expect(optionElement).toBeVisible();
            // Don't actually download in test - just verify the option exists
            break;
          }
        }
      }
    });
    
    await test.step('Test report generation', async () => {
      // Look for report generation functionality  
      const reportButtons = [
        'button:has-text("Generate Report")',
        'button:has-text("Create Report")',
        '[data-testid="generate-report"]',
        '.report-generator'
      ];
      
      for (const selector of reportButtons) {
        const reportButton = page.locator(selector);
        if (await reportButton.isVisible({ timeout: 3000 })) {
          await reportButton.click();
          
          // Look for report configuration options
          const reportOptions = [
            '.report-config',
            '.report-settings',
            'text=Report Type',
            'select[name*="report"]'
          ];
          
          for (const option of reportOptions) {
            const optionElement = page.locator(option);
            if (await optionElement.isVisible({ timeout: 3000 })) {
              await expect(optionElement).toBeVisible();
              break;
            }
          }
          break;
        }
      }
    });
  });
  
  test('Dashboard should handle real-time updates properly', async ({ page }) => {
    await test.step('Navigate to dashboard', async () => {
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Verify real-time indicators', async () => {
      // Look for real-time update indicators
      const realtimeIndicators = [
        'text=Live',
        'text=Real-time',
        '.live-indicator',
        '.real-time-badge',
        '.status-indicator'
      ];
      
      for (const indicator of realtimeIndicators) {
        const element = page.locator(indicator);
        if (await element.isVisible({ timeout: 3000 })) {
          await expect(element).toBeVisible();
          break;
        }
      }
    });
    
    await test.step('Test auto-refresh functionality', async () => {
      // Check if dashboard has auto-refresh
      const autoRefreshControls = [
        '[data-testid="auto-refresh"]',
        'input[type="checkbox"]:has-text("Auto")',
        '.auto-refresh-toggle',
        'text=Auto refresh'
      ];
      
      for (const selector of autoRefreshControls) {
        const control = page.locator(selector);
        if (await control.isVisible({ timeout: 3000 })) {
          // Try to toggle auto-refresh
          if (await control.getAttribute('type') === 'checkbox') {
            await control.check();
          } else {
            await control.click();
          }
          
          // Wait briefly and check for any refresh activity
          await page.waitForTimeout(2000);
          break;
        }
      }
    });
    
    await test.step('Test notification system', async () => {
      // Look for notification areas
      const notificationAreas = [
        '.notifications',
        '.alerts',
        '[data-testid="notifications"]',
        '.notification-center'
      ];
      
      for (const selector of notificationAreas) {
        const notifications = page.locator(selector);
        if (await notifications.isVisible({ timeout: 3000 })) {
          await expect(notifications).toBeVisible();
          
          // Check for individual notification items
          const notificationItems = notifications.locator('.notification-item, .alert-item');
          if (await notificationItems.count() > 0) {
            await expect(notificationItems.first()).toBeVisible();
          }
          break;
        }
      }
    });
  });
  
  test('Dashboard should be responsive on different screen sizes', async ({ page }) => {
    await test.step('Test desktop layout (1280x720)', async () => {
      await page.setViewportSize({ width: 1280, height: 720 });
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
      
      // Dashboard should be fully visible
      await expect(page.locator('.dashboard-container, [data-testid="dashboard"]')).toBeVisible();
      
      // Charts should be properly sized
      const charts = page.locator('canvas, svg, .chart');
      if (await charts.count() > 0) {
        await expect(charts.first()).toBeVisible();
      }
    });
    
    await test.step('Test tablet layout (768x1024)', async () => {
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Dashboard should adapt to tablet size
      await expect(page.locator('.dashboard-container, [data-testid="dashboard"]')).toBeVisible();
      
      // Check for responsive layout adjustments
      const responsiveElements = [
        '.responsive-grid',
        '.tablet-layout',
        '.col-md-'
      ];
      
      // Dashboard should still be functional
      expect(await page.locator('body').isVisible()).toBe(true);
    });
    
    await test.step('Test mobile layout (375x667)', async () => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Dashboard should be mobile-optimized
      await expect(page.locator('.dashboard-container, [data-testid="dashboard"]')).toBeVisible();
      
      // Check for mobile-specific elements
      const mobileElements = [
        '.mobile-dashboard',
        '.mobile-layout',
        '.hamburger-menu'
      ];
      
      // Essential dashboard functionality should remain
      expect(await page.locator('body').isVisible()).toBe(true);
    });
  });
  
  test('Dashboard should handle errors and edge cases gracefully', async ({ page }) => {
    await test.step('Test API failure handling', async () => {
      await page.goto('/dashboard');
      
      // Intercept dashboard API calls and make them fail
      await page.route('**/api/v1/dashboard/**', route => {
        route.abort('failed');
      });
      
      // Refresh to trigger API calls
      await page.reload();
      
      // Should show appropriate error messages
      const errorMessages = [
        'text=Failed to load',
        'text=Error loading dashboard',
        '.error-message',
        '.dashboard-error',
        'text=Unable to fetch data'
      ];
      
      let errorFound = false;
      for (const selector of errorMessages) {
        if (await page.locator(selector).isVisible({ timeout: 10000 })) {
          await expect(page.locator(selector)).toBeVisible();
          errorFound = true;
          break;
        }
      }
      
      // Should provide retry functionality
      const retryButtons = [
        'button:has-text("Retry")',
        'button:has-text("Reload")',
        '.retry-button',
        '[data-testid="retry-dashboard"]'
      ];
      
      for (const selector of retryButtons) {
        if (await page.locator(selector).isVisible({ timeout: 3000 })) {
          await expect(page.locator(selector)).toBeVisible();
          break;
        }
      }
      
      // Remove route interception
      await page.unroute('**/api/v1/dashboard/**');
    });
    
    await test.step('Test empty data state', async () => {
      // Navigate to dashboard
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
      
      // Look for empty state indicators
      const emptyStates = [
        'text=No data available',
        'text=No results found',
        '.empty-state',
        '.no-data',
        'text=Get started'
      ];
      
      // Dashboard might show empty states for certain sections
      for (const selector of emptyStates) {
        const emptyElement = page.locator(selector);
        if (await emptyElement.isVisible({ timeout: 3000 })) {
          await expect(emptyElement).toBeVisible();
          break;
        }
      }
    });
    
    await test.step('Test loading states', async () => {
      // Navigate to dashboard and check for loading indicators
      await page.goto('/dashboard');
      
      // Look for loading states during initial load
      const loadingIndicators = [
        '.loading',
        '.spinner',
        'text=Loading...',
        '.skeleton-loader',
        '.loading-dashboard'
      ];
      
      // Loading indicators might appear briefly
      for (const selector of loadingIndicators) {
        const loading = page.locator(selector);
        if (await loading.isVisible({ timeout: 1000 })) {
          // Loading should eventually disappear
          await expect(loading).not.toBeVisible({ timeout: 15000 });
          break;
        }
      }
      
      // Dashboard should be fully loaded
      await page.waitForLoadState('networkidle');
      await expect(page.locator('body')).toBeVisible();
    });
  });
  
  test('Dashboard performance should meet acceptable thresholds', async ({ page }) => {
    await test.step('Measure dashboard load time', async () => {
      const startTime = Date.now();
      
      await page.goto('/dashboard');
      await page.waitForLoadState('networkidle');
      
      const loadTime = Date.now() - startTime;
      
      // Dashboard should load within 10 seconds (generous for development environment)
      expect(loadTime).toBeLessThan(10000);
      
      console.log(`Dashboard load time: ${loadTime}ms`);
    });
    
    await test.step('Check for performance best practices', async () => {
      // Check for lazy loading indicators
      const lazyElements = page.locator('[loading="lazy"], .lazy-load');
      if (await lazyElements.count() > 0) {
        console.log(`Found ${await lazyElements.count()} lazy-loaded elements`);
      }
      
      // Check for optimized images
      const images = page.locator('img');
      const imageCount = await images.count();
      if (imageCount > 0) {
        // Check if images have proper loading attributes
        const optimizedImages = await images.locator('[loading="lazy"]').count();
        console.log(`Optimized images: ${optimizedImages}/${imageCount}`);
      }
    });
    
    await test.step('Validate API response times', async () => {
      // Test critical dashboard endpoints
      const startTime = Date.now();
      const statsResult = await apiValidator.validateSpecificEndpoint('/dashboard/stats');
      const statsTime = Date.now() - startTime;
      
      expect(statsResult?.success).toBe(true);
      expect(statsTime).toBeLessThan(5000); // API should respond within 5 seconds
      
      console.log(`Dashboard stats API response time: ${statsTime}ms`);
    });
  });
});