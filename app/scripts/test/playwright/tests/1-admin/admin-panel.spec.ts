/**
 * Admin Panel E2E Tests - Priority 1
 * 
 * Comprehensive black-box testing of admin panel functionality:
 * - User management
 * - System monitoring
 * - Configuration management
 * - Access control validation
 */

import { test, expect, Page } from '@playwright/test';
import { AuthHelper } from '../../utils/auth-helper';
import { ApiValidator } from '../../utils/api-validator';
import { TestConfig } from '../../utils/test-config';

test.describe('Admin Panel - Core Functionality', () => {
  let authHelper: AuthHelper;
  let apiValidator: ApiValidator;
  let config: TestConfig;
  
  test.beforeEach(async ({ page }) => {
    authHelper = new AuthHelper(page);
    config = new TestConfig();
    apiValidator = new ApiValidator(config);
    
    // Initialize API validator
    await apiValidator.initialize(page.request, config.devToken);
    
    // Authenticate as admin
    await authHelper.loginWithDevToken();
  });
  
  test('Admin dashboard should load with all key metrics', async ({ page }) => {
    await test.step('Navigate to admin dashboard', async () => {
      await page.goto('/admin/dashboard');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Verify dashboard components are visible', async () => {
      // Check main dashboard elements
      await expect(page.locator('[data-testid="admin-dashboard"], .admin-dashboard')).toBeVisible();
      
      // Key metrics should be displayed
      const metrics = [
        'Users',
        'Candidates', 
        'Positions',
        'Active Sessions',
      ];
      
      for (const metric of metrics) {
        await expect(page.locator(`text=${metric}`)).toBeVisible();
      }
    });
    
    await test.step('Verify navigation menu is accessible', async () => {
      // Check admin navigation menu
      const navItems = [
        'Dashboard',
        'Users',
        'System',
        'Monitoring',
      ];
      
      for (const item of navItems) {
        await expect(page.locator(`[data-testid="nav-${item.toLowerCase()}"], a:has-text("${item}")`)).toBeVisible();
      }
    });
    
    await test.step('Validate API endpoints are responsive', async () => {
      // Test critical admin API endpoints
      const dashboardStats = await apiValidator.validateSpecificEndpoint('/dashboard/stats');
      expect(dashboardStats?.success).toBe(true);
      
      const healthStatus = await apiValidator.validateSpecificEndpoint('/admin/monitoring/health');
      expect(healthStatus?.success).toBe(true);
    });
  });
  
  test('User management should allow CRUD operations', async ({ page }) => {
    await test.step('Navigate to user management', async () => {
      await page.goto('/admin/users');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Verify user list is displayed', async () => {
      await expect(page.locator('table, [data-testid="users-table"]')).toBeVisible();
      await expect(page.locator('thead th:has-text("Email")')).toBeVisible();
      await expect(page.locator('thead th:has-text("Role")')).toBeVisible();
      await expect(page.locator('thead th:has-text("Status")')).toBeVisible();
    });
    
    await test.step('Test user creation flow', async () => {
      // Click create user button
      await page.click('[data-testid="create-user-btn"], button:has-text("Create User"), .create-user-button');
      
      // Fill user form
      const testUser = {
        email: `test-admin-user-${Date.now()}@example.com`,
        username: `test-admin-${Date.now()}`,
        fullName: 'Test Admin User',
        role: 'recruiter',
      };
      
      await page.fill('[name="email"], input[type="email"]', testUser.email);
      await page.fill('[name="username"]', testUser.username);
      await page.fill('[name="full_name"], [name="fullName"]', testUser.fullName);
      
      // Select role if dropdown is available
      const roleSelect = page.locator('[name="role"], select');
      if (await roleSelect.isVisible()) {
        await roleSelect.selectOption(testUser.role);
      }
      
      // Submit form
      await page.click('button[type="submit"], [data-testid="submit-user"]');
      
      // Verify success message
      await expect(page.locator('text=User created successfully, .success-message, .alert-success')).toBeVisible();
    });
    
    await test.step('Test user search and filtering', async () => {
      // Search for users
      const searchInput = page.locator('[data-testid="user-search"], input[placeholder*="search"]');
      if (await searchInput.isVisible()) {
        await searchInput.fill('admin');
        await page.waitForTimeout(1000); // Allow search to process
        
        // Verify search results
        await expect(page.locator('tbody tr')).toBeVisible();
      }
      
      // Test role filter
      const roleFilter = page.locator('[data-testid="role-filter"], select[name*="role"]');
      if (await roleFilter.isVisible()) {
        await roleFilter.selectOption('admin');
        await page.waitForTimeout(1000);
      }
    });
    
    await test.step('Validate user management API', async () => {
      const usersResult = await apiValidator.validateSpecificEndpoint('/admin/users/');
      expect(usersResult?.success).toBe(true);
    });
  });
  
  test('System monitoring should display health status', async ({ page }) => {
    await test.step('Navigate to system monitoring', async () => {
      await page.goto('/admin/monitoring');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Verify monitoring dashboard components', async () => {
      // Check for monitoring sections
      const monitoringSections = [
        'System Health',
        'Database Status',
        'Redis Status',
        'Service Status',
      ];
      
      for (const section of monitoringSections) {
        // Use flexible selectors to find monitoring sections
        const sectionLocator = page.locator(`text=${section}, [data-testid="${section.toLowerCase().replace(/\s+/g, '-')}"]`);
        if (await sectionLocator.isVisible({ timeout: 5000 })) {
          await expect(sectionLocator).toBeVisible();
        }
      }
    });
    
    await test.step('Check service health indicators', async () => {
      // Look for health status indicators
      const healthIndicators = page.locator('.health-indicator, .status-indicator, [data-testid*="health"]');
      const count = await healthIndicators.count();
      
      if (count > 0) {
        // Verify at least one health indicator is present
        expect(count).toBeGreaterThan(0);
        
        // Check for common status texts
        const statusTexts = ['Healthy', 'Online', 'Active', 'Running'];
        let foundStatus = false;
        
        for (const status of statusTexts) {
          if (await page.locator(`text=${status}`).isVisible({ timeout: 2000 })) {
            foundStatus = true;
            break;
          }
        }
        
        // At least one status should be visible
        expect(foundStatus).toBe(true);
      }
    });
    
    await test.step('Validate monitoring API endpoints', async () => {
      const healthResult = await apiValidator.validateSpecificEndpoint('/admin/monitoring/health');
      expect(healthResult?.success).toBe(true);
      
      // Test basic health endpoints
      const dbHealthResult = await apiValidator.validateSpecificEndpoint('/health/db');
      expect(dbHealthResult?.success).toBe(true);
      
      const redisHealthResult = await apiValidator.validateSpecificEndpoint('/health/redis');
      expect(redisHealthResult?.success).toBe(true);
    });
  });
  
  test('Admin panel should enforce proper access control', async ({ page }) => {
    await test.step('Verify admin-only sections are accessible', async () => {
      const adminOnlyPages = [
        '/admin/users',
        '/admin/system',
        '/admin/monitoring',
        '/admin/settings',
      ];
      
      for (const adminPage of adminOnlyPages) {
        await page.goto(adminPage);
        await page.waitForLoadState('networkidle', { timeout: 10000 });
        
        // Should not redirect to login or show access denied
        expect(page.url()).not.toContain('/login');
        
        // Should not show access denied message
        const accessDenied = page.locator('text=Access denied, text=Forbidden, text=Not authorized');
        expect(await accessDenied.isVisible({ timeout: 2000 })).toBe(false);
      }
    });
    
    await test.step('Verify admin navigation is present', async () => {
      await page.goto('/admin');
      
      // Check for admin-specific navigation
      const adminNavItems = [
        'User Management',
        'System Monitoring', 
        'Configuration',
        'Logs',
      ];
      
      for (const navItem of adminNavItems) {
        const navLocator = page.locator(`text=${navItem}, [data-testid*="${navItem.toLowerCase().replace(/\s+/g, '-')}"]`);
        // Check if the navigation item exists (may not be visible depending on UI design)
        if (await navLocator.count() > 0) {
          await expect(navLocator.first()).toBeVisible();
        }
      }
    });
  });
  
  test('Admin panel should handle language switching', async ({ page }) => {
    await test.step('Navigate to admin dashboard', async () => {
      await page.goto('/admin/dashboard');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Test language switching functionality', async () => {
      // Look for language switcher
      const languageSwitcher = page.locator('[data-testid="language-switcher"], .language-selector, select[name*="language"]');
      
      if (await languageSwitcher.isVisible()) {
        // Test switching to Chinese
        if (await languageSwitcher.selectOption) {
          await languageSwitcher.selectOption('zh');
        } else {
          // Try clicking on Chinese option
          await page.click('text=中文, text=Chinese, [data-value="zh"]');
        }
        
        await page.waitForTimeout(1000);
        
        // Verify language change (look for Chinese characters or language indicator)
        const chineseContent = page.locator('text=/[\u4e00-\u9fff]/');
        const languageIndicator = page.locator('[data-current-lang="zh"], .lang-zh');
        
        const hasChineseContent = await chineseContent.count() > 0;
        const hasLanguageIndicator = await languageIndicator.isVisible({ timeout: 2000 });
        
        expect(hasChineseContent || hasLanguageIndicator).toBe(true);
        
        // Switch back to English
        if (await languageSwitcher.isVisible()) {
          if (await languageSwitcher.selectOption) {
            await languageSwitcher.selectOption('en');
          } else {
            await page.click('text=English, text=EN, [data-value="en"]');
          }
        }
      }
    });
  });
  
  test('Admin panel should be responsive on different screen sizes', async ({ page }) => {
    await test.step('Test desktop layout', async () => {
      await page.setViewportSize({ width: 1280, height: 720 });
      await page.goto('/admin/dashboard');
      
      await expect(page.locator('[data-testid="admin-dashboard"], .admin-dashboard')).toBeVisible();
    });
    
    await test.step('Test tablet layout', async () => {
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.reload();
      
      // Admin dashboard should still be accessible
      await expect(page.locator('[data-testid="admin-dashboard"], .admin-dashboard')).toBeVisible();
    });
    
    await test.step('Test mobile layout', async () => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.reload();
      
      // Check for mobile navigation or responsive layout
      const mobileMenu = page.locator('[data-testid="mobile-menu"], .mobile-nav, .hamburger-menu');
      if (await mobileMenu.isVisible()) {
        await expect(mobileMenu).toBeVisible();
      }
    });
  });
  
  test('Admin panel should handle errors gracefully', async ({ page }) => {
    await test.step('Test network error handling', async () => {
      await page.goto('/admin/dashboard');
      
      // Simulate network failure for API calls
      await page.route('**/api/v1/admin/**', route => {
        route.abort('failed');
      });
      
      // Trigger a refresh or action that requires API call
      await page.reload();
      
      // Should show appropriate error message
      const errorMessage = page.locator('text=Error loading data, text=Failed to fetch, .error-message, .alert-error');
      await expect(errorMessage).toBeVisible({ timeout: 10000 });
      
      // Should have retry option
      const retryButton = page.locator('text=Retry, button:has-text("Try Again"), .retry-button');
      if (await retryButton.isVisible({ timeout: 5000 })) {
        await expect(retryButton).toBeVisible();
      }
    });
    
    await test.step('Test invalid data handling', async () => {
      // Remove the network interception
      await page.unroute('**/api/v1/admin/**');
      
      // Test form validation
      await page.goto('/admin/users');
      
      if (await page.locator('[data-testid="create-user-btn"], button:has-text("Create User")').isVisible()) {
        await page.click('[data-testid="create-user-btn"], button:has-text("Create User")');
        
        // Try to submit empty form
        await page.click('button[type="submit"], [data-testid="submit-user"]');
        
        // Should show validation errors
        const validationError = page.locator('text=required, text=invalid, .field-error, .validation-error');
        await expect(validationError).toBeVisible({ timeout: 5000 });
      }
    });
  });
});