/**
 * Candidate Assessment E2E Tests - Priority 2
 * 
 * Comprehensive black-box testing of candidate assessment functionality:
 * - Candidate CRUD operations
 * - Resume upload and processing
 * - Assessment scoring with ML integration
 * - Complete assessment workflow validation
 */

import { test, expect, Page } from '@playwright/test';
import { AuthHelper } from '../../utils/auth-helper';
import { ApiValidator } from '../../utils/api-validator';
import { TestConfig } from '../../utils/test-config';
import path from 'path';

test.describe('Candidate Assessment - Core Functionality', () => {
  let authHelper: AuthHelper;
  let apiValidator: ApiValidator;
  let config: TestConfig;
  
  test.beforeEach(async ({ page }) => {
    authHelper = new AuthHelper(page);
    config = new TestConfig();
    apiValidator = new ApiValidator(config);
    
    // Initialize API validator
    await apiValidator.initialize(page.request, config.devToken);
    
    // Authenticate as admin
    await authHelper.loginWithDevToken();
  });
  
  test('Candidate list should load and display properly', async ({ page }) => {
    await test.step('Navigate to candidates page', async () => {
      await page.goto('/candidates');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Verify candidate list components', async () => {
      // Check main candidates container
      await expect(page.locator('[data-testid="candidates-list"], .candidates-container, table')).toBeVisible();
      
      // Check essential list elements
      const listElements = [
        'Name',
        'Email', 
        'Status',
        'Experience',
        'Skills'
      ];
      
      for (const element of listElements) {
        const headerElement = page.locator(`thead th:has-text("${element}"), th:text-is("${element}"), [data-column="${element.toLowerCase()}"]`);
        if (await headerElement.count() > 0) {
          await expect(headerElement.first()).toBeVisible();
        }
      }
    });
    
    await test.step('Verify search and filter functionality', async () => {
      // Test search functionality
      const searchInput = page.locator('[data-testid="candidate-search"], input[placeholder*="search"], .search-input');
      if (await searchInput.isVisible()) {
        await searchInput.fill('test');
        await page.waitForTimeout(1000);
        await expect(page.locator('tbody tr, .candidate-row')).toBeVisible({ timeout: 10000 });
      }
      
      // Test filter functionality
      const filterDropdown = page.locator('[data-testid="status-filter"], select[name*="status"], .filter-dropdown');
      if (await filterDropdown.isVisible()) {
        // Try different filter options
        const filterOptions = ['active', 'pending', 'all'];
        for (const option of filterOptions) {
          if (await filterDropdown.locator(`option[value="${option}"]`).count() > 0) {
            await filterDropdown.selectOption(option);
            await page.waitForTimeout(500);
            break;
          }
        }
      }
    });
    
    await test.step('Validate candidates API endpoint', async () => {
      const candidatesResult = await apiValidator.validateSpecificEndpoint('/candidates/');
      expect(candidatesResult?.success).toBe(true);
    });
  });
  
  test('Candidate creation workflow should work end-to-end', async ({ page }) => {
    await test.step('Navigate to candidate creation', async () => {
      await page.goto('/candidates');
      await page.waitForLoadState('networkidle');
      
      // Click create candidate button
      const createButtons = [
        '[data-testid="create-candidate-btn"]',
        'button:has-text("Create Candidate")',
        'button:has-text("Add Candidate")',
        '.create-candidate-button',
        'a[href*="create"]'
      ];
      
      let clicked = false;
      for (const selector of createButtons) {
        if (await page.locator(selector).isVisible({ timeout: 2000 })) {
          await page.click(selector);
          clicked = true;
          break;
        }
      }
      
      if (!clicked) {
        // Try navigation approach
        await page.goto('/candidates/create');
      }
      
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Fill candidate creation form', async () => {
      const testCandidate = {
        name: `Test Candidate ${Date.now()}`,
        email: `test-candidate-${Date.now()}@example.com`,
        phone: '******-TEST-001',
        experience: '3',
        skills: 'JavaScript, Python, Testing'
      };
      
      // Fill basic information
      await page.fill('[name="name"], [data-testid="candidate-name"], input[placeholder*="name"]', testCandidate.name);
      await page.fill('[name="email"], [data-testid="candidate-email"], input[type="email"]', testCandidate.email);
      await page.fill('[name="phone"], [data-testid="candidate-phone"], input[placeholder*="phone"]', testCandidate.phone);
      
      // Experience field (could be input or select)
      const experienceField = page.locator('[name="experience"], [name="experience_years"], [data-testid="experience"]');
      if (await experienceField.isVisible()) {
        const fieldType = await experienceField.getAttribute('type');
        if (fieldType === 'number' || await experienceField.tagName() === 'INPUT') {
          await experienceField.fill(testCandidate.experience);
        } else if (await experienceField.tagName() === 'SELECT') {
          await experienceField.selectOption(testCandidate.experience);
        }
      }
      
      // Skills field (could be textarea, input, or multi-select)
      const skillsField = page.locator('[name="skills"], [data-testid="skills"], textarea[placeholder*="skill"]');
      if (await skillsField.isVisible()) {
        await skillsField.fill(testCandidate.skills);
      }
      
      // Additional fields that might be present
      const locationField = page.locator('[name="location"], [data-testid="location"]');
      if (await locationField.isVisible()) {
        await locationField.fill('Remote');
      }
      
      const salaryField = page.locator('[name="expected_salary"], [name="salary"], [data-testid="salary"]');
      if (await salaryField.isVisible()) {
        await salaryField.fill('75000');
      }
    });
    
    await test.step('Submit candidate creation form', async () => {
      // Submit the form
      const submitButtons = [
        'button[type="submit"]',
        '[data-testid="submit-candidate"]',
        'button:has-text("Create")',
        'button:has-text("Save")',
        '.submit-button'
      ];
      
      for (const selector of submitButtons) {
        if (await page.locator(selector).isVisible({ timeout: 2000 })) {
          await page.click(selector);
          break;
        }
      }
      
      // Wait for success feedback
      const successIndicators = [
        'text=created successfully',
        'text=saved successfully',
        '.success-message',
        '.alert-success',
        '[data-testid="success-message"]'
      ];
      
      let successFound = false;
      for (const indicator of successIndicators) {
        if (await page.locator(indicator).isVisible({ timeout: 10000 })) {
          await expect(page.locator(indicator)).toBeVisible();
          successFound = true;
          break;
        }
      }
      
      // If no success message, check if we were redirected to candidate list
      if (!successFound) {
        await expect(page).toHaveURL(/candidates/, { timeout: 10000 });
      }
    });
    
    await test.step('Verify candidate was created via API', async () => {
      const candidatesResult = await apiValidator.validateSpecificEndpoint('/candidates/');
      expect(candidatesResult?.success).toBe(true);
      expect(candidatesResult?.data).toBeDefined();
    });
  });
  
  test('Resume upload functionality should work properly', async ({ page }) => {
    await test.step('Navigate to candidate with upload capability', async () => {
      await page.goto('/candidates');
      await page.waitForLoadState('networkidle');
      
      // Try to find an existing candidate or create one for testing
      const candidateRows = page.locator('tbody tr, .candidate-row');
      const rowCount = await candidateRows.count();
      
      if (rowCount > 0) {
        // Click on first candidate
        await candidateRows.first().click();
      } else {
        // Navigate to create candidate first
        await page.goto('/candidates/create');
        await page.waitForLoadState('networkidle');
      }
    });
    
    await test.step('Test resume upload interface', async () => {
      // Look for upload component
      const uploadElements = [
        '[data-testid="resume-upload"]',
        'input[type="file"]',
        '.file-upload',
        '[accept*="pdf"]',
        '.resume-upload'
      ];
      
      let uploadElement;
      for (const selector of uploadElements) {
        if (await page.locator(selector).isVisible({ timeout: 5000 })) {
          uploadElement = page.locator(selector);
          break;
        }
      }
      
      if (uploadElement) {
        // Create a test file (if we had a real file)
        // For now, just verify the upload interface exists
        await expect(uploadElement).toBeVisible();
        
        // Check for drag-and-drop area
        const dropZone = page.locator('.drop-zone, [data-testid="drop-zone"], .file-drop-area');
        if (await dropZone.isVisible({ timeout: 2000 })) {
          await expect(dropZone).toBeVisible();
        }
        
        // Check for upload instructions
        const uploadInstructions = page.locator('text=drag and drop, text=choose file, text=upload resume');
        if (await uploadInstructions.count() > 0) {
          await expect(uploadInstructions.first()).toBeVisible();
        }
      }
    });
    
    await test.step('Verify file type restrictions', async () => {
      const fileInputs = page.locator('input[type="file"]');
      const fileInputCount = await fileInputs.count();
      
      if (fileInputCount > 0) {
        const firstFileInput = fileInputs.first();
        const acceptAttribute = await firstFileInput.getAttribute('accept');
        
        if (acceptAttribute) {
          // Should accept common resume file types
          const expectedTypes = ['.pdf', '.doc', '.docx', 'application/pdf'];
          const hasExpectedType = expectedTypes.some(type => acceptAttribute.includes(type));
          expect(hasExpectedType).toBe(true);
        }
      }
    });
  });
  
  test('Assessment scoring should integrate with ML services', async ({ page }) => {
    await test.step('Navigate to assessment interface', async () => {
      await page.goto('/candidates');
      await page.waitForLoadState('networkidle');
      
      // Look for assessment-related functionality
      const assessmentButtons = [
        '[data-testid="start-assessment"]',
        'button:has-text("Assess")',
        'button:has-text("Score")',
        '.assessment-button',
        'a[href*="assessment"]'
      ];
      
      for (const selector of assessmentButtons) {
        if (await page.locator(selector).isVisible({ timeout: 3000 })) {
          await page.click(selector);
          break;
        }
      }
    });
    
    await test.step('Verify assessment interface components', async () => {
      // Look for assessment scoring elements
      const assessmentElements = [
        'Skills Assessment',
        'Technical Evaluation',
        'Score',
        'Rating',
        'Competency'
      ];
      
      for (const element of assessmentElements) {
        const elementLocator = page.locator(`text=${element}, [data-testid*="${element.toLowerCase().replace(/\s+/g, '-')}"]`);
        if (await elementLocator.count() > 0) {
          // At least one assessment-related element should be visible
          break;
        }
      }
    });
    
    await test.step('Test ML integration endpoints', async () => {
      // Test AI questionnaire generation
      try {
        const aiQuestionnaireResult = await apiValidator.validateSpecificEndpoint('/ai-questionnaire/templates');
        // AI endpoints might not be fully implemented yet, so we'll allow both success and failure
        console.log('AI Questionnaire endpoint status:', aiQuestionnaireResult?.success ? 'Available' : 'Not available');
      } catch (error) {
        console.log('AI Questionnaire endpoint not available:', error);
      }
      
      // Test basic assessment endpoints if they exist
      try {
        const assessmentSearch = await apiValidator.validateSpecificEndpoint('/candidates/search');
        expect(assessmentSearch?.success).toBe(true);
      } catch (error) {
        console.log('Assessment search endpoint issue:', error);
      }
    });
    
    await test.step('Verify score calculation interface', async () => {
      // Look for scoring elements
      const scoringElements = [
        '.score-display',
        '[data-testid*="score"]',
        'text=Score:',
        'text=Rating:',
        '.rating-component',
        'input[type="range"]',
        '.slider'
      ];
      
      for (const selector of scoringElements) {
        const element = page.locator(selector);
        if (await element.isVisible({ timeout: 3000 })) {
          await expect(element).toBeVisible();
          break;
        }
      }
    });
  });
  
  test('Candidate editing and updating should work properly', async ({ page }) => {
    await test.step('Navigate to existing candidate for editing', async () => {
      await page.goto('/candidates');
      await page.waitForLoadState('networkidle');
      
      // Find and click on a candidate to edit
      const candidateRows = page.locator('tbody tr, .candidate-item, .candidate-card');
      const rowCount = await candidateRows.count();
      
      if (rowCount > 0) {
        // Click on first candidate
        await candidateRows.first().click();
        await page.waitForLoadState('networkidle');
      }
    });
    
    await test.step('Access edit functionality', async () => {
      // Look for edit buttons or edit mode
      const editButtons = [
        '[data-testid="edit-candidate"]',
        'button:has-text("Edit")',
        '.edit-button',
        'a:has-text("Edit")',
        '[title="Edit"]'
      ];
      
      for (const selector of editButtons) {
        if (await page.locator(selector).isVisible({ timeout: 3000 })) {
          await page.click(selector);
          await page.waitForLoadState('networkidle');
          break;
        }
      }
    });
    
    await test.step('Update candidate information', async () => {
      // Try to update basic fields
      const updatedData = {
        phone: '******-UPDATED',
        skills: 'JavaScript, Python, Testing, Updated Skill'
      };
      
      // Update phone if field exists and is editable
      const phoneField = page.locator('[name="phone"], input[placeholder*="phone"]');
      if (await phoneField.isVisible() && await phoneField.isEditable()) {
        await phoneField.clear();
        await phoneField.fill(updatedData.phone);
      }
      
      // Update skills if field exists and is editable
      const skillsField = page.locator('[name="skills"], textarea[placeholder*="skill"]');
      if (await skillsField.isVisible() && await skillsField.isEditable()) {
        await skillsField.clear();
        await skillsField.fill(updatedData.skills);
      }
      
      // Look for save/update button
      const saveButtons = [
        'button[type="submit"]',
        'button:has-text("Save")',
        'button:has-text("Update")',
        '[data-testid="save-candidate"]'
      ];
      
      for (const selector of saveButtons) {
        if (await page.locator(selector).isVisible({ timeout: 3000 })) {
          await page.click(selector);
          break;
        }
      }
      
      // Wait for success indication
      const successIndicators = [
        'text=updated successfully',
        'text=saved successfully',
        '.success-message',
        '.alert-success'
      ];
      
      for (const indicator of successIndicators) {
        if (await page.locator(indicator).isVisible({ timeout: 5000 })) {
          await expect(page.locator(indicator)).toBeVisible();
          break;
        }
      }
    });
  });
  
  test('Candidate deletion should work with proper confirmation', async ({ page }) => {
    await test.step('Navigate to candidate for deletion', async () => {
      await page.goto('/candidates');
      await page.waitForLoadState('networkidle');
      
      // Ensure we have at least one candidate
      const candidateRows = page.locator('tbody tr, .candidate-item');
      const rowCount = await candidateRows.count();
      
      if (rowCount === 0) {
        // Skip this test if no candidates exist
        test.skip('No candidates available for deletion test');
        return;
      }
      
      // Find a test candidate or use the first one
      await candidateRows.first().click();
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Initiate deletion process', async () => {
      // Look for delete buttons
      const deleteButtons = [
        '[data-testid="delete-candidate"]',
        'button:has-text("Delete")',
        '.delete-button',
        '[title="Delete"]',
        'button[class*="danger"]'
      ];
      
      let deleteButton;
      for (const selector of deleteButtons) {
        if (await page.locator(selector).isVisible({ timeout: 3000 })) {
          deleteButton = page.locator(selector);
          break;
        }
      }
      
      if (deleteButton) {
        await deleteButton.click();
      } else {
        // Skip if no delete functionality found
        console.log('Delete functionality not found, skipping deletion test');
        return;
      }
    });
    
    await test.step('Handle deletion confirmation', async () => {
      // Look for confirmation dialog
      const confirmationElements = [
        'text=confirm',
        'text=delete',
        'text=are you sure',
        '[role="dialog"]',
        '.modal',
        '.confirmation-dialog'
      ];
      
      let confirmationFound = false;
      for (const selector of confirmationElements) {
        if (await page.locator(selector).isVisible({ timeout: 5000 })) {
          confirmationFound = true;
          
          // Look for confirm button in the dialog
          const confirmButtons = [
            'button:has-text("Confirm")',
            'button:has-text("Delete")',
            'button:has-text("Yes")',
            '[data-testid="confirm-delete"]'
          ];
          
          for (const confirmSelector of confirmButtons) {
            if (await page.locator(confirmSelector).isVisible({ timeout: 2000 })) {
              await page.click(confirmSelector);
              break;
            }
          }
          break;
        }
      }
      
      if (confirmationFound) {
        // Wait for success message or redirect
        const successIndicators = [
          'text=deleted successfully',
          'text=removed successfully',
          '.success-message'
        ];
        
        for (const indicator of successIndicators) {
          if (await page.locator(indicator).isVisible({ timeout: 5000 })) {
            await expect(page.locator(indicator)).toBeVisible();
            break;
          }
        }
      }
    });
  });
  
  test('Candidate search and filtering should work across different criteria', async ({ page }) => {
    await test.step('Navigate to candidates page', async () => {
      await page.goto('/candidates');
      await page.waitForLoadState('networkidle');
    });
    
    await test.step('Test text-based search', async () => {
      const searchInput = page.locator('[data-testid="search"], input[placeholder*="search"], .search-input');
      
      if (await searchInput.isVisible()) {
        // Test searching by name
        await searchInput.fill('test');
        await page.waitForTimeout(1000);
        
        // Verify results are filtered
        const resultRows = page.locator('tbody tr, .candidate-item');
        const rowCount = await resultRows.count();
        expect(rowCount).toBeGreaterThanOrEqual(0); // Could be 0 if no matches
        
        // Clear search
        await searchInput.clear();
        await page.waitForTimeout(500);
      }
    });
    
    await test.step('Test skill-based filtering', async () => {
      // Look for skills filter
      const skillsFilter = page.locator('[data-testid="skills-filter"], select[name*="skill"], .skills-filter');
      
      if (await skillsFilter.isVisible()) {
        // Try to select a skill
        const options = await skillsFilter.locator('option').allTextContents();
        if (options.length > 1) {
          await skillsFilter.selectOption({ index: 1 });
          await page.waitForTimeout(1000);
        }
      }
    });
    
    await test.step('Test experience level filtering', async () => {
      const experienceFilter = page.locator('[data-testid="experience-filter"], select[name*="experience"]');
      
      if (await experienceFilter.isVisible()) {
        const options = ['junior', 'mid', 'senior'];
        for (const option of options) {
          if (await experienceFilter.locator(`option[value="${option}"]`).count() > 0) {
            await experienceFilter.selectOption(option);
            await page.waitForTimeout(500);
            break;
          }
        }
      }
    });
    
    await test.step('Validate search API endpoint', async () => {
      const searchResult = await apiValidator.validateSpecificEndpoint('/candidates/search');
      expect(searchResult?.success).toBe(true);
    });
  });
  
  test('Candidate assessment workflow should handle errors gracefully', async ({ page }) => {
    await test.step('Test network error handling', async () => {
      await page.goto('/candidates');
      
      // Intercept and fail API requests
      await page.route('**/api/v1/candidates/**', route => {
        route.abort('failed');
      });
      
      // Try to refresh the page
      await page.reload();
      
      // Should show error message
      const errorMessages = [
        'text=failed to load',
        'text=error',
        '.error-message',
        '.alert-error',
        '[data-testid="error"]'
      ];
      
      let errorFound = false;
      for (const selector of errorMessages) {
        if (await page.locator(selector).isVisible({ timeout: 10000 })) {
          await expect(page.locator(selector)).toBeVisible();
          errorFound = true;
          break;
        }
      }
      
      // Should have retry mechanism
      const retryButtons = [
        'button:has-text("Retry")',
        'button:has-text("Try Again")',
        '.retry-button'
      ];
      
      for (const selector of retryButtons) {
        if (await page.locator(selector).isVisible({ timeout: 3000 })) {
          await expect(page.locator(selector)).toBeVisible();
          break;
        }
      }
      
      // Remove the route interception
      await page.unroute('**/api/v1/candidates/**');
    });
    
    await test.step('Test form validation errors', async () => {
      // Try to create candidate with invalid data
      await page.goto('/candidates/create');
      await page.waitForLoadState('networkidle');
      
      // Try to submit empty form
      const submitButton = page.locator('button[type="submit"], button:has-text("Create"), button:has-text("Save")');
      if (await submitButton.isVisible()) {
        await submitButton.click();
        
        // Should show validation errors
        const validationErrors = [
          '.field-error',
          '.validation-error',
          'text=required',
          'text=invalid',
          '[role="alert"]'
        ];
        
        for (const selector of validationErrors) {
          if (await page.locator(selector).isVisible({ timeout: 5000 })) {
            await expect(page.locator(selector)).toBeVisible();
            break;
          }
        }
      }
    });
  });
  
  test('Candidate assessment should work with different device sizes', async ({ page }) => {
    await test.step('Test desktop layout', async () => {
      await page.setViewportSize({ width: 1280, height: 720 });
      await page.goto('/candidates');
      await page.waitForLoadState('networkidle');
      
      await expect(page.locator('.candidates-container, table, [data-testid="candidates-list"]')).toBeVisible();
    });
    
    await test.step('Test tablet layout', async () => {
      await page.setViewportSize({ width: 768, height: 1024 });
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Candidates should still be accessible
      await expect(page.locator('.candidates-container, [data-testid="candidates-list"]')).toBeVisible();
    });
    
    await test.step('Test mobile layout', async () => {
      await page.setViewportSize({ width: 375, height: 667 });
      await page.reload();
      await page.waitForLoadState('networkidle');
      
      // Should have mobile-optimized layout
      const mobileElements = [
        '.mobile-layout',
        '.candidate-card',
        '.mobile-nav'
      ];
      
      // At least the candidates should still be visible even if layout changes
      await expect(page.locator('body')).toBeVisible();
    });
  });
});