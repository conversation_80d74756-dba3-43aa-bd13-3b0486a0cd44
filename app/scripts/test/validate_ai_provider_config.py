#!/usr/bin/env python3
"""
AI Provider Configuration Validator
Validates environment variables and configuration for all 6 AI providers
"""
import os
import sys
from typing import Dict, Any, List, Tuple
from rich.console import Console
from rich.table import Table
from rich.panel import Panel

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../backend'))

from app.core.ai_config import ai_settings
from tests.ai_providers.test_config import PROVIDER_EXPECTATIONS

console = Console()


class ProviderConfigValidator:
    """Validate AI provider configurations"""
    
    def __init__(self):
        self.providers = ["zhipu", "deepseek", "moonshot", "openrouter", "qwen", "ollama"]
        self.expectations = PROVIDER_EXPECTATIONS
    
    def validate_provider_config(self, provider: str) -> Dict[str, Any]:
        """
        Validate configuration for specific provider
        
        Args:
            provider: Provider name
            
        Returns:
            Dict: Validation results
        """
        result = {
            "provider": provider,
            "configured": False,
            "issues": [],
            "warnings": [],
            "config_present": {},
            "models_configured": False,
            "api_accessible": "unknown"
        }
        
        # Check API key
        api_key_var = f"{provider.upper()}_API_KEY"
        api_key = getattr(ai_settings, api_key_var, None)
        
        if provider == "ollama":
            # Ollama doesn't need API key
            result["config_present"]["api_key"] = "not_required"
        else:
            result["config_present"]["api_key"] = "present" if api_key else "missing"
            if not api_key:
                result["issues"].append(f"Missing {api_key_var} environment variable")
        
        # Check API base URL
        api_base_var = f"{provider.upper()}_API_BASE"
        api_base = getattr(ai_settings, api_base_var, None)
        result["config_present"]["api_base"] = "present" if api_base else "missing"
        
        # Check LLM model configuration
        llm_model = ai_settings.get_llm_model(provider)
        result["config_present"]["llm_model"] = "present" if llm_model else "missing"
        if not llm_model:
            result["warnings"].append(f"No LLM model configured for {provider}")
        
        # Check embedding model configuration
        embedding_model = ai_settings.get_embedding_model(provider)
        result["config_present"]["embedding_model"] = "present" if embedding_model else "missing"
        if not embedding_model:
            result["warnings"].append(f"No embedding model configured for {provider}")
        
        # Check if provider is available
        result["configured"] = ai_settings.is_provider_available(provider)
        result["models_configured"] = bool(llm_model or embedding_model)
        
        # Provider-specific validations
        expectations = self.expectations.get(provider, {})
        
        # Check expected models
        expected_models = expectations.get("expected_models", [])
        if expected_models:
            if llm_model and llm_model not in expected_models:
                result["warnings"].append(f"LLM model '{llm_model}' not in expected models: {expected_models}")
        
        # Special requirements check
        special_req = expectations.get("special_requirements", "")
        if special_req and special_req != "None":
            result["warnings"].append(f"Special requirement: {special_req}")
        
        return result
    
    def validate_all_providers(self) -> Dict[str, Any]:
        """Validate all provider configurations"""
        results = {}
        
        for provider in self.providers:
            results[provider] = self.validate_provider_config(provider)
        
        # Calculate summary
        configured_count = sum(1 for r in results.values() if r["configured"])
        issues_count = sum(len(r["issues"]) for r in results.values())
        warnings_count = sum(len(r["warnings"]) for r in results.values())
        
        return {
            "provider_results": results,
            "summary": {
                "total_providers": len(self.providers),
                "configured_providers": configured_count,
                "total_issues": issues_count,
                "total_warnings": warnings_count,
                "configuration_rate": configured_count / len(self.providers)
            }
        }
    
    def check_fallback_chains(self) -> Dict[str, Any]:
        """Check provider fallback chain configurations"""
        llm_chain = ai_settings.get_llm_fallback_chain()
        embedding_chain = ai_settings.get_embedding_fallback_chain()
        
        # Validate providers in chains
        llm_available = []
        llm_unavailable = []
        
        for provider in llm_chain:
            if ai_settings.is_provider_available(provider, "llm"):
                llm_available.append(provider)
            else:
                llm_unavailable.append(provider)
        
        embedding_available = []
        embedding_unavailable = []
        
        for provider in embedding_chain:
            if ai_settings.is_provider_available(provider, "embedding"):
                embedding_available.append(provider)
            else:
                embedding_unavailable.append(provider)
        
        return {
            "llm_fallback": {
                "chain": llm_chain,
                "available": llm_available,
                "unavailable": llm_unavailable,
                "availability_rate": len(llm_available) / len(llm_chain) if llm_chain else 0
            },
            "embedding_fallback": {
                "chain": embedding_chain,
                "available": embedding_available, 
                "unavailable": embedding_unavailable,
                "availability_rate": len(embedding_available) / len(embedding_chain) if embedding_chain else 0
            }
        }
    
    def print_validation_results(self, results: Dict[str, Any]) -> None:
        """Print validation results in a nice format"""
        # Header
        console.print(Panel(
            "[bold blue]AI Provider Configuration Validation[/bold blue]\n" +
            f"Checking configuration for {results['summary']['total_providers']} providers...",
            title="🔧 Configuration Validator",
            border_style="blue"
        ))
        
        # Summary table
        summary = results['summary']
        summary_table = Table(title="Configuration Summary", show_header=False)
        summary_table.add_column("Metric", style="bold")
        summary_table.add_column("Value", justify="right")
        
        summary_table.add_row("Total Providers", str(summary['total_providers']))
        summary_table.add_row("Configured Providers", str(summary['configured_providers']))
        
        config_rate = summary['configuration_rate']
        rate_color = "green" if config_rate > 0.8 else "yellow" if config_rate > 0.5 else "red"
        summary_table.add_row("Configuration Rate", f"[{rate_color}]{config_rate*100:.1f}%[/{rate_color}]")
        
        summary_table.add_row("Total Issues", f"[red]{summary['total_issues']}[/red]" if summary['total_issues'] > 0 else "0")
        summary_table.add_row("Total Warnings", f"[yellow]{summary['total_warnings']}[/yellow]" if summary['total_warnings'] > 0 else "0")
        
        console.print(summary_table)
        
        # Provider details
        console.print("\n[bold]Provider Details:[/bold]")
        
        provider_results = results['provider_results']
        for provider, result in provider_results.items():
            # Status icon
            if result['configured']:
                status_icon = "✅"
                status_color = "green"
            else:
                status_icon = "❌"
                status_color = "red"
            
            console.print(f"\n[{status_color}]{status_icon} {provider.upper()}[/{status_color}]")
            
            # Configuration details
            config = result['config_present']
            console.print(f"   API Key: {self._format_status(config.get('api_key', 'missing'))}")
            console.print(f"   API Base: {self._format_status(config.get('api_base', 'missing'))}")
            console.print(f"   LLM Model: {self._format_status(config.get('llm_model', 'missing'))}")
            console.print(f"   Embedding Model: {self._format_status(config.get('embedding_model', 'missing'))}")
            
            # Issues
            if result['issues']:
                console.print("   [red]Issues:[/red]")
                for issue in result['issues']:
                    console.print(f"     • {issue}")
            
            # Warnings
            if result['warnings']:
                console.print("   [yellow]Warnings:[/yellow]")
                for warning in result['warnings']:
                    console.print(f"     • {warning}")
    
    def print_fallback_chain_results(self, results: Dict[str, Any]) -> None:
        """Print fallback chain validation results"""
        console.print("\n" + "="*60)
        console.print("[bold blue]Fallback Chain Validation[/bold blue]")
        console.print("="*60)
        
        # LLM fallback chain
        llm_fallback = results['llm_fallback']
        console.print(f"\n[bold]LLM Fallback Chain:[/bold]")
        console.print(f"Chain: {' → '.join(llm_fallback['chain'])}")
        
        rate = llm_fallback['availability_rate']
        rate_color = "green" if rate > 0.8 else "yellow" if rate > 0.5 else "red"
        console.print(f"Availability: [{rate_color}]{rate*100:.1f}%[/{rate_color}] ({len(llm_fallback['available'])}/{len(llm_fallback['chain'])})")
        
        if llm_fallback['available']:
            console.print(f"Available: [green]{', '.join(llm_fallback['available'])}[/green]")
        if llm_fallback['unavailable']:
            console.print(f"Unavailable: [red]{', '.join(llm_fallback['unavailable'])}[/red]")
        
        # Embedding fallback chain
        embedding_fallback = results['embedding_fallback']
        console.print(f"\n[bold]Embedding Fallback Chain:[/bold]")
        console.print(f"Chain: {' → '.join(embedding_fallback['chain'])}")
        
        rate = embedding_fallback['availability_rate']
        rate_color = "green" if rate > 0.8 else "yellow" if rate > 0.5 else "red"
        console.print(f"Availability: [{rate_color}]{rate*100:.1f}%[/{rate_color}] ({len(embedding_fallback['available'])}/{len(embedding_fallback['chain'])})")
        
        if embedding_fallback['available']:
            console.print(f"Available: [green]{', '.join(embedding_fallback['available'])}[/green]")
        if embedding_fallback['unavailable']:
            console.print(f"Unavailable: [red]{', '.join(embedding_fallback['unavailable'])}[/red]")
    
    def _format_status(self, status: str) -> str:
        """Format configuration status with colors"""
        if status == "present":
            return "[green]✅[/green]"
        elif status == "missing":
            return "[red]❌[/red]"  
        elif status == "not_required":
            return "[blue]ℹ️ Not Required[/blue]"
        else:
            return f"[yellow]{status}[/yellow]"
    
    def generate_setup_instructions(self, results: Dict[str, Any]) -> List[str]:
        """Generate setup instructions for missing configurations"""
        instructions = []
        
        provider_results = results['provider_results']
        
        for provider, result in provider_results.items():
            if not result['configured'] and result['issues']:
                instructions.append(f"\n# {provider.upper()} Configuration")
                
                for issue in result['issues']:
                    if "API_KEY" in issue:
                        instructions.append(f"export {provider.upper()}_API_KEY='your_api_key_here'")
                    elif "model" in issue.lower():
                        instructions.append(f"# Configure {provider} model in settings")
                
                # Add example based on provider
                if provider == "deepseek":
                    instructions.append("# Get API key from: https://platform.deepseek.com/api-keys")
                elif provider == "moonshot":
                    instructions.append("# Get API key from: https://platform.moonshot.cn/console/api-keys")
                elif provider == "openrouter":
                    instructions.append("# Get API key from: https://openrouter.ai/keys")
                elif provider == "qwen":
                    instructions.append("# Get API key from: https://dashscope.console.aliyun.com/apiKey")
                elif provider == "zhipu":
                    instructions.append("# Get API key from: https://open.bigmodel.cn/usercenter/apikeys")
                elif provider == "ollama":
                    instructions.append("# Install Ollama: https://ollama.ai/download")
                    instructions.append("# Start Ollama service: ollama serve")
        
        return instructions


def main():
    """Main entry point"""
    console.print("[blue]🔍 Starting AI Provider Configuration Validation...[/blue]\n")
    
    validator = ProviderConfigValidator()
    
    # Validate all provider configurations
    results = validator.validate_all_providers()
    validator.print_validation_results(results)
    
    # Check fallback chains
    fallback_results = validator.check_fallback_chains()
    validator.print_fallback_chain_results(fallback_results)
    
    # Generate setup instructions if needed
    if results['summary']['total_issues'] > 0:
        instructions = validator.generate_setup_instructions(results)
        if instructions:
            console.print("\n" + "="*60)
            console.print("[bold yellow]Setup Instructions[/bold yellow]")
            console.print("="*60)
            console.print("[yellow]Add these environment variables to configure missing providers:[/yellow]\n")
            
            for instruction in instructions:
                console.print(instruction)
    
    # Exit with appropriate code
    if results['summary']['total_issues'] > 0:
        console.print(f"\n[red]❌ Configuration validation failed with {results['summary']['total_issues']} issues[/red]")
        sys.exit(1)
    elif results['summary']['total_warnings'] > 0:
        console.print(f"\n[yellow]⚠️ Configuration validation completed with {results['summary']['total_warnings']} warnings[/yellow]")
        sys.exit(0)
    else:
        console.print("\n[green]✅ All provider configurations are valid[/green]")
        sys.exit(0)


if __name__ == "__main__":
    main()