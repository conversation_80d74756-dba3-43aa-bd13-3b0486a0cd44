#!/usr/bin/env python3
"""
Service Consolidation Regression Test Suite
==========================================

Comprehensive testing suite to validate system functionality after
service consolidation on August 27, 2025.

This script tests the 6 consolidated services and their 29 critical API endpoints
to ensure no regression was introduced by the consolidation process.

Usage:
    python service_consolidation_regression_test.py [--base-url URL] [--verbose]
"""

import asyncio
import aiohttp
import json
import sys
import time
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum
import argparse
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestStatus(Enum):
    PASSED = "PASSED"
    FAILED = "FAILED"
    CRITICAL_FAILED = "CRITICAL_FAILED"
    SKIPPED = "SKIPPED"

@dataclass
class TestResult:
    service: str
    endpoint: str
    method: str
    status: TestStatus
    response_code: Optional[int]
    response_time: float
    error_message: Optional[str] = None
    response_data: Optional[Any] = None

class ServiceConsolidationTester:
    """Service consolidation regression test suite"""
    
    def __init__(self, base_url: str = "http://localhost:8088", verbose: bool = False):
        self.base_url = base_url.rstrip('/')
        self.api_url = f"{self.base_url}/api/v1"
        self.verbose = verbose
        self.session: Optional[aiohttp.ClientSession] = None
        self.access_token: Optional[str] = None
        self.test_results: List[TestResult] = []
        
        # Consolidated services to test
        self.consolidated_services = {
            "assessment_service": {
                "endpoints": [
                    {"path": "/assessment/generate", "method": "POST", "critical": True},
                    {"path": "/assessment/jfs/calculate", "method": "POST", "critical": True},
                    {"path": "/assessment/jfs/batch", "method": "POST", "critical": False},
                    {"path": "/assessment/compare", "method": "POST", "critical": False},
                    {"path": "/assessment/recommend", "method": "POST", "critical": False},
                    {"path": "/assessment/batch", "method": "POST", "critical": False},
                    {"path": "/assessment/statistics", "method": "GET", "critical": False}
                ]
            },
            "embedding_service": {
                "endpoints": [
                    {"path": "/embedding/generate", "method": "POST", "critical": True},
                    {"path": "/embedding/batch", "method": "POST", "critical": True},
                    {"path": "/embedding/similarity", "method": "POST", "critical": True}
                ]
            },
            "resume_parser": {
                "endpoints": [
                    {"path": "/resume/parse", "method": "POST", "critical": True},
                    {"path": "/resume/parse-preview", "method": "POST", "critical": False},
                    {"path": "/resume/task-status/test-task-id", "method": "GET", "critical": False}
                ]
            },
            "monitoring_service": {
                "endpoints": [
                    {"path": "/admin/monitoring/health", "method": "GET", "critical": True},
                    {"path": "/admin/monitoring/services", "method": "GET", "critical": True},
                    {"path": "/admin/monitoring/metrics", "method": "GET", "critical": False},
                    {"path": "/admin/monitoring/tasks", "method": "GET", "critical": False},
                    {"path": "/admin/monitoring/cache", "method": "GET", "critical": False},
                    {"path": "/admin/monitoring/performance", "method": "GET", "critical": False}
                ]
            },
            "permission_service": {
                "endpoints": [
                    {"path": "/admin/permissions/", "method": "GET", "critical": True},
                    {"path": "/admin/permissions/", "method": "POST", "critical": False},
                    {"path": "/admin/roles/", "method": "GET", "critical": True},
                    {"path": "/admin/roles/", "method": "POST", "critical": False}
                ]
            },
            "storage_service": {
                "endpoints": [
                    # Storage service is tested indirectly via resume upload
                    {"path": "/candidates/", "method": "GET", "critical": True},
                    {"path": "/positions/", "method": "GET", "critical": True}
                ]
            }
        }

    async def setup(self):
        """Initialize test session and authenticate"""
        connector = aiohttp.TCPConnector(limit=10)
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'Content-Type': 'application/json'}
        )
        
        # Authenticate
        await self.authenticate()

    async def teardown(self):
        """Clean up test session"""
        if self.session:
            await self.session.close()

    async def authenticate(self):
        """Authenticate using admin credentials or dev bypass token"""
        try:
            # Try dev bypass token first
            dev_token = "dev_bypass_token_2025_talentforge"
            headers = {"Authorization": f"Bearer {dev_token}"}
            
            async with self.session.get(f"{self.api_url}/auth/me", headers=headers) as response:
                if response.status == 200:
                    self.access_token = dev_token
                    logger.info("✅ Authenticated with dev bypass token")
                    return
                    
        except Exception as e:
            logger.warning(f"Dev token failed: {e}")
        
        # Fallback to login
        try:
            login_data = {
                "username": "<EMAIL>",
                "password": "test123"
            }
            
            async with self.session.post(f"{self.api_url}/auth/login", json=login_data) as response:
                if response.status == 200:
                    data = await response.json()
                    self.access_token = data.get("access_token")
                    logger.info("✅ Authenticated via login")
                else:
                    logger.error(f"Authentication failed: {response.status}")
                    raise Exception("Authentication failed")
                    
        except Exception as e:
            logger.error(f"❌ Authentication error: {e}")
            raise

    async def test_endpoint(self, service: str, endpoint: dict) -> TestResult:
        """Test individual endpoint"""
        start_time = time.time()
        path = endpoint["path"]
        method = endpoint["method"]
        is_critical = endpoint.get("critical", False)
        
        headers = {"Authorization": f"Bearer {self.access_token}"}
        url = f"{self.api_url}{path}"
        
        try:
            # Prepare test data based on endpoint
            test_data = self.get_test_data(service, path, method)
            
            async with self.session.request(method, url, headers=headers, json=test_data) as response:
                response_time = time.time() - start_time
                
                # Read response data
                try:
                    response_data = await response.json()
                except:
                    response_data = await response.text()
                
                # Determine test status
                if response.status in [200, 201, 204]:
                    status = TestStatus.PASSED
                    error_message = None
                elif response.status == 404 and not is_critical:
                    # Some endpoints may not exist yet, non-critical
                    status = TestStatus.SKIPPED
                    error_message = "Endpoint not implemented"
                else:
                    status = TestStatus.CRITICAL_FAILED if is_critical else TestStatus.FAILED
                    error_message = f"HTTP {response.status}: {response_data}"
                
                return TestResult(
                    service=service,
                    endpoint=path,
                    method=method,
                    status=status,
                    response_code=response.status,
                    response_time=response_time,
                    error_message=error_message,
                    response_data=response_data if self.verbose else None
                )
                
        except Exception as e:
            response_time = time.time() - start_time
            status = TestStatus.CRITICAL_FAILED if is_critical else TestStatus.FAILED
            
            return TestResult(
                service=service,
                endpoint=path,
                method=method,
                status=status,
                response_code=None,
                response_time=response_time,
                error_message=str(e)
            )

    def get_test_data(self, service: str, path: str, method: str) -> Optional[Dict]:
        """Generate appropriate test data for different endpoints"""
        if method == "GET":
            return None
            
        # Assessment service test data
        if "assessment" in path:
            if "generate" in path:
                return {
                    "candidate_id": "1",
                    "assessment_type": "comprehensive"
                }
            elif "jfs" in path:
                return {
                    "candidate_id": "1",
                    "position_id": "1"
                }
            elif "compare" in path:
                return {
                    "candidate_ids": ["1", "2"],
                    "comparison_type": "skills"
                }
                
        # Embedding service test data
        elif "embedding" in path:
            if "generate" in path:
                return {
                    "text": "Senior Python developer with 5 years experience",
                    "model": "bge-m3"
                }
            elif "batch" in path:
                return {
                    "texts": ["Python developer", "Data scientist"],
                    "model": "bge-m3"
                }
            elif "similarity" in path:
                return {
                    "text1": "Python developer",
                    "text2": "Software engineer",
                    "model": "bge-m3"
                }
                
        # Resume parser test data
        elif "resume" in path and "parse" in path:
            return {
                "text": "John Doe\\nSenior Developer\\nPython, FastAPI, PostgreSQL",
                "candidate_id": "1"
            }
            
        # Permission/Role service test data
        elif "permissions" in path and method == "POST":
            return {
                "name": "test_permission",
                "description": "Test permission for regression testing",
                "resource": "test_resource"
            }
        elif "roles" in path and method == "POST":
            return {
                "name": "test_role",
                "description": "Test role for regression testing",
                "permissions": []
            }
            
        return None

    async def test_backward_compatibility(self):
        """Test backward compatibility aliases"""
        logger.info("🔍 Testing backward compatibility aliases...")
        
        compatibility_tests = [
            "from app.services import file_service",  # Should alias to storage_service
            "from app.services import enhanced_assessment_service",  # Should alias to assessment_service
            "from app.services import embedding_service_refactored",  # Should alias to embedding_service
            "from app.services.role import role_service",  # Should work via shim
            "from app.services.permission import permission_service"  # Should work via shim
        ]
        
        passed = 0
        total = len(compatibility_tests)
        
        for import_statement in compatibility_tests:
            try:
                exec(import_statement)
                logger.info(f"✅ {import_statement}")
                passed += 1
            except Exception as e:
                logger.error(f"❌ {import_statement}: {e}")
        
        logger.info(f"Backward compatibility: {passed}/{total} passed")
        return passed == total

    async def run_service_tests(self):
        """Run tests for all consolidated services"""
        logger.info("🧪 Running service consolidation regression tests...")
        
        for service_name, service_config in self.consolidated_services.items():
            logger.info(f"\\n📋 Testing {service_name}...")
            
            for endpoint in service_config["endpoints"]:
                result = await self.test_endpoint(service_name, endpoint)
                self.test_results.append(result)
                
                # Log result
                status_emoji = {
                    TestStatus.PASSED: "✅",
                    TestStatus.FAILED: "❌",
                    TestStatus.CRITICAL_FAILED: "🚨",
                    TestStatus.SKIPPED: "⏭️"
                }
                
                emoji = status_emoji.get(result.status, "❓")
                logger.info(
                    f"  {emoji} {result.method} {result.endpoint} "
                    f"({result.response_code}) {result.response_time:.2f}s"
                )
                
                if result.error_message and self.verbose:
                    logger.info(f"    Error: {result.error_message}")

    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        total_tests = len(self.test_results)
        passed = len([r for r in self.test_results if r.status == TestStatus.PASSED])
        failed = len([r for r in self.test_results if r.status == TestStatus.FAILED])
        critical_failed = len([r for r in self.test_results if r.status == TestStatus.CRITICAL_FAILED])
        skipped = len([r for r in self.test_results if r.status == TestStatus.SKIPPED])
        
        # Group results by service
        service_results = {}
        for result in self.test_results:
            if result.service not in service_results:
                service_results[result.service] = []
            service_results[result.service].append(result)
        
        # Calculate average response time
        response_times = [r.response_time for r in self.test_results if r.response_time > 0]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed": passed,
                "failed": failed,
                "critical_failed": critical_failed,
                "skipped": skipped,
                "success_rate": (passed / total_tests * 100) if total_tests > 0 else 0,
                "avg_response_time": avg_response_time
            },
            "service_results": {}
        }
        
        for service, results in service_results.items():
            service_passed = len([r for r in results if r.status == TestStatus.PASSED])
            service_total = len(results)
            
            report["service_results"][service] = {
                "total_tests": service_total,
                "passed": service_passed,
                "success_rate": (service_passed / service_total * 100) if service_total > 0 else 0,
                "tests": [
                    {
                        "endpoint": r.endpoint,
                        "method": r.method,
                        "status": r.status.value,
                        "response_code": r.response_code,
                        "response_time": r.response_time,
                        "error_message": r.error_message
                    }
                    for r in results
                ]
            }
        
        return report

    async def run_tests(self):
        """Run complete test suite"""
        start_time = time.time()
        
        try:
            await self.setup()
            
            # Test backward compatibility
            compat_passed = await self.test_backward_compatibility()
            
            # Test consolidated services
            await self.run_service_tests()
            
            # Generate report
            report = self.generate_report()
            total_time = time.time() - start_time
            
            # Print summary
            print("\\n" + "="*60)
            print("🧪 SERVICE CONSOLIDATION REGRESSION TEST RESULTS")
            print("="*60)
            print(f"Test Duration: {total_time:.2f}s")
            print(f"Total Tests: {report['summary']['total_tests']}")
            print(f"✅ Passed: {report['summary']['passed']}")
            print(f"❌ Failed: {report['summary']['failed']}")
            print(f"🚨 Critical Failed: {report['summary']['critical_failed']}")
            print(f"⏭️ Skipped: {report['summary']['skipped']}")
            print(f"📊 Success Rate: {report['summary']['success_rate']:.1f}%")
            print(f"⚡ Avg Response Time: {report['summary']['avg_response_time']:.3f}s")
            print(f"🔄 Backward Compatibility: {'✅ PASSED' if compat_passed else '❌ FAILED'}")
            
            # Service breakdown
            print("\\n📋 Service Test Results:")
            for service, results in report["service_results"].items():
                status = "✅" if results["success_rate"] == 100 else "❌"
                print(f"  {status} {service}: {results['passed']}/{results['total_tests']} ({results['success_rate']:.1f}%)")
            
            # Critical failures
            critical_failures = [r for r in self.test_results if r.status == TestStatus.CRITICAL_FAILED]
            if critical_failures:
                print("\\n🚨 CRITICAL FAILURES:")
                for failure in critical_failures:
                    print(f"  ❌ {failure.service}: {failure.method} {failure.endpoint}")
                    print(f"     Error: {failure.error_message}")
            
            # Overall status
            overall_success = (report['summary']['critical_failed'] == 0 and 
                             report['summary']['success_rate'] >= 90 and 
                             compat_passed)
            
            print("\\n" + "="*60)
            if overall_success:
                print("🎉 OVERALL STATUS: ✅ REGRESSION TESTING PASSED")
                print("✅ Service consolidation successful with no critical regressions")
            else:
                print("⚠️  OVERALL STATUS: ❌ REGRESSION TESTING FAILED")
                print("🔧 Critical issues detected - immediate attention required")
            print("="*60)
            
            # Save detailed report
            report_path = Path("service_consolidation_test_report.json")
            with open(report_path, "w") as f:
                json.dump(report, f, indent=2)
            print(f"📄 Detailed report saved: {report_path}")
            
            return overall_success
            
        finally:
            await self.teardown()

async def main():
    parser = argparse.ArgumentParser(
        description="Service Consolidation Regression Test Suite"
    )
    parser.add_argument(
        "--base-url",
        default="http://localhost:8088",
        help="Base URL for the API (default: http://localhost:8088)"
    )
    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose output with detailed error messages"
    )
    
    args = parser.parse_args()
    
    tester = ServiceConsolidationTester(
        base_url=args.base_url,
        verbose=args.verbose
    )
    
    success = await tester.run_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    asyncio.run(main())