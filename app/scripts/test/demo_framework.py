#!/usr/bin/env python3
"""
Standalone demo of the AI Provider Testing Framework
Tests the framework components without requiring full app dependencies
"""
import asyncio
import sys
import os

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../backend'))

# Import only the framework components we need
from tests.fixtures.provider_test_fixtures import (
    MockProviderFactory, 
    ResponseValidator, 
    TestDataGenerator
)


async def demo_response_validation():
    """Demo response validation functionality"""
    print("🔍 Testing Response Validation")
    print("-" * 40)
    
    validator = ResponseValidator()
    
    # Test successful models response validation
    print("Testing Models Response Validation:")
    
    # Test DeepSeek (OpenAI-compatible)
    deepseek_response = MockProviderFactory.create_successful_models_response("deepseek", 3)
    result = validator.validate_models_response(deepseek_response, "deepseek")
    print(f"  DeepSeek: {'✅' if result['valid'] else '❌'} ({result['models_count']} models)")
    
    # Test Ollama
    ollama_response = MockProviderFactory.create_successful_models_response("ollama", 2)
    result = validator.validate_models_response(ollama_response, "ollama")
    print(f"  Ollama:   {'✅' if result['valid'] else '❌'} ({result['models_count']} models)")
    
    print("\nTesting Conversation Response Validation:")
    
    # Test Chinese conversation
    chinese_response = MockProviderFactory.create_successful_conversation_response("deepseek", "你好，你是谁？")
    result = validator.validate_conversation_response(chinese_response, "deepseek", "你好，你是谁？")
    print(f"  Chinese:  {'✅' if result['valid'] else '❌'} (quality: {result['response_quality']})")
    
    # Test English conversation
    english_response = MockProviderFactory.create_successful_conversation_response("moonshot", "Hello, who are you?")
    result = validator.validate_conversation_response(english_response, "moonshot", "Hello, who are you?")
    print(f"  English:  {'✅' if result['valid'] else '❌'} (quality: {result['response_quality']})")


def demo_error_categorization():
    """Demo error categorization functionality"""
    print("\n🏷️  Testing Error Categorization")
    print("-" * 40)
    
    validator = ResponseValidator()
    
    test_cases = [
        ("Invalid API key provided", "auth"),
        ("Connection refused to provider", "network"),
        ("Missing DEEPSEEK_API_KEY environment variable", "config"),
        ("Rate limit exceeded (429)", "api"),
        ("Invalid JSON response format", "response"),
        ("Random unknown error occurred", "unknown")
    ]
    
    print("Error Message → Category:")
    for error_message, expected_category in test_cases:
        category = validator.categorize_error_by_message(error_message)
        status = "✅" if category == expected_category else "❌"
        print(f"  {status} '{error_message[:30]}...' → {category}")


def demo_test_data_generation():
    """Demo test data generation functionality"""
    print("\n📊 Testing Data Generation")
    print("-" * 40)
    
    generator = TestDataGenerator()
    
    # Test prompt generation
    prompts = generator.generate_test_prompts()
    print(f"Generated {len(prompts)} test prompts:")
    for prompt in prompts[:2]:
        print(f"  • {prompt['name']}: '{prompt['prompt'][:30]}...'")
    
    # Test provider config generation
    configs = generator.generate_provider_configs()
    print(f"\nGenerated configs for {len(configs)} providers:")
    for provider, config in list(configs.items())[:3]:
        print(f"  • {provider}: timeout={config['timeout']}s, models={len(config['expected_models'])}")


def demo_mock_responses():
    """Demo mock response generation"""
    print("\n🎭 Testing Mock Response Generation")
    print("-" * 40)
    
    # Test successful responses
    print("Mock Successful Responses:")
    
    # Models response
    models_response = MockProviderFactory.create_successful_models_response("deepseek", 5)
    model_count = len(models_response.data) if hasattr(models_response, 'data') else 0
    print(f"  Models Response: ✅ {model_count} models generated")
    
    # Conversation response  
    conv_response = MockProviderFactory.create_successful_conversation_response("deepseek", "你好，你是谁？")
    has_content = hasattr(conv_response, 'choices') and len(conv_response.choices) > 0
    print(f"  Conversation Response: {'✅' if has_content else '❌'} generated")
    
    # Test error responses
    print("\nMock Error Responses:")
    error_types = ["auth", "network", "config", "api"]
    for error_type in error_types:
        try:
            error = MockProviderFactory.create_error_response(error_type, "test_provider")
            print(f"  {error_type.title()} Error: ✅ {type(error).__name__}")
        except Exception as e:
            print(f"  {error_type.title()} Error: ❌ {e}")


async def demo_framework_components():
    """Demo all framework components"""
    print("🧪 AI Provider Testing Framework Demo")
    print("=" * 50)
    
    try:
        # Run all demo components
        await demo_response_validation()
        demo_error_categorization()
        demo_test_data_generation()
        demo_mock_responses()
        
        print("\n" + "=" * 50)
        print("🎯 Framework Demo Summary")
        print("=" * 50)
        print("✅ Response validation works correctly")
        print("✅ Error categorization functions properly")
        print("✅ Test data generation produces valid data")
        print("✅ Mock factories create realistic responses")
        print("\n🚀 Framework is ready for testing!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Demo failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main entry point"""
    result = asyncio.run(demo_framework_components())
    sys.exit(0 if result else 1)


if __name__ == "__main__":
    main()