#!/bin/bash
# AI Provider Testing Script with Virtual Environment

# Change to backend directory
cd /home/<USER>/source_code/talent_forge_pro/app/backend

# Activate virtual environment
echo "🔧 Activating Python virtual environment..."
source venv/bin/activate

# Check if activation successful
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ Virtual environment activated: $VIRTUAL_ENV"
    echo "🐍 Python path: $(which python)"
else
    echo "⚠️ Failed to activate virtual environment"
    echo "Using system Python instead..."
fi

# Change to test directory
cd /home/<USER>/source_code/talent_forge_pro/app/scripts/test

# Run the test
echo ""
echo "🤖 Starting AI Provider Testing..."
echo "=================================="
echo ""

python run_ai_provider_tests_simple.py "$@"