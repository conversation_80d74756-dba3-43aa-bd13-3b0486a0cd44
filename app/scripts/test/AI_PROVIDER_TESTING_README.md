# AI Provider Testing Framework

Comprehensive real API testing framework for all 6 AI providers in TalentForge Pro.

## Overview

This framework provides comprehensive testing for:
- **Zhipu GLM** (JWT token authentication)
- **DeepSeek** (OpenAI-compatible API)
- **Moonshot** (OpenAI-compatible API)
- **OpenRouter** (Multi-provider proxy)
- **<PERSON>wen** (Alibaba Cloud compatible)
- **Ollama** (Local HTTP API)

## Test Types

### 1. Models Endpoint Test
- Validates `/models` endpoint connectivity
- Checks response format and model availability
- Verifies expected models are present
- Measures response time

### 2. Basic Conversation Test
- Sends test prompt: "你好，你是谁？"
- Validates response format and content quality
- Measures response time
- Checks for meaningful AI responses

### 3. Configuration Validation
- Verifies environment variables are set
- Checks API keys and base URLs
- Validates model configurations
- Tests fallback chain availability

## Quick Start

### 1. Run All Tests (Simple Version)
```bash
cd /home/<USER>/source_code/talent_forge_pro/app/scripts/test
python run_ai_provider_tests_simple.py
```

### 2. Test Specific Providers
```bash
python run_ai_provider_tests_simple.py --providers deepseek,moonshot
```

### 3. Generate JSON Report
```bash
python run_ai_provider_tests_simple.py --output json --output-file ai_test_report.json
```

### 4. Validate Configuration Only
```bash
python validate_ai_provider_config.py
```

## Detailed Usage

### CLI Test Runner Options

```bash
python run_ai_provider_tests_simple.py [options]

Options:
  --providers LIST          Providers to test (comma-separated)
                           Options: zhipu,deepseek,moonshot,openrouter,qwen,ollama
                           Default: all providers

  --tests LIST             Test types to run (comma-separated)
                           Options: models,conversation,fallback,configuration
                           Default: models,conversation

  --timeout SECONDS        Request timeout in seconds (default: 30)
  --max-retries N          Maximum retry attempts (default: 2)
  --output FORMAT          Output format: console,json (default: console)
  --output-file PATH       Output file path (required for json format)
  --api-url URL            Backend API URL (default: http://localhost:8001)
  --skip-connectivity-check Skip initial API connectivity check
```

### Configuration Validator Options

```bash
python validate_ai_provider_config.py

Features:
- Validates all environment variables
- Checks API key presence
- Verifies model configurations
- Analyzes fallback chains
- Generates setup instructions for missing configurations
```

## Example Usage Scenarios

### 1. Development Environment Check
```bash
# Check all provider configurations
python validate_ai_provider_config.py

# Test only configured providers
python run_ai_provider_tests_simple.py --timeout 60
```

### 2. CI/CD Integration
```bash
# Quick test with JSON output
python run_ai_provider_tests_simple.py \
  --providers deepseek,ollama \
  --timeout 45 \
  --output json \
  --output-file ci_test_results.json
```

### 3. Production Readiness Check
```bash
# Comprehensive test of all providers
python run_ai_provider_tests_simple.py \
  --tests models,conversation,configuration \
  --timeout 60 \
  --max-retries 3
```

## Configuration Requirements

### Environment Variables

#### DeepSeek
```bash
export DEEPSEEK_API_KEY="your_api_key"
export DEEPSEEK_API_BASE="https://api.deepseek.com/v1"
```

#### Moonshot
```bash
export MOONSHOT_API_KEY="your_api_key"
export MOONSHOT_API_BASE="https://api.moonshot.cn/v1"
```

#### OpenRouter
```bash
export OPENROUTER_API_KEY="your_api_key"
export OPENROUTER_API_BASE="https://openrouter.ai/api/v1"
```

#### Qwen (Alibaba Cloud)
```bash
export QWEN_API_KEY="your_api_key"
export QWEN_API_BASE="https://dashscope.aliyuncs.com/compatible-mode/v1"
```

#### Zhipu GLM
```bash
export ZHIPU_API_KEY="your_api_key"
export ZHIPU_API_BASE="https://open.bigmodel.cn/api/paas/v4/"
```

#### Ollama (Local)
```bash
export OLLAMA_HOST="http://localhost:11434"
# Ensure Ollama service is running: ollama serve
# Pull required models: ollama pull qwen2.5:14b
```

## Test Output Examples

### Console Output
```
=============================================================
🤖 AI Provider Testing Suite
=============================================================
Testing 6 providers with real API calls
Providers: zhipu, deepseek, moonshot, openrouter, qwen, ollama
Test Types: models, conversation
=============================================================

✅ DEEPSEEK
   └─ Models: ✅ 3 models found
      Sample: deepseek-chat, deepseek-coder
      Time: 850ms
   └─ Conversation: ✅ Response: "你好！我是DeepSeek，一个AI助手..."
      Time: 1200ms

❌ MOONSHOT
   └─ Models: ❌ Authentication failed: invalid API key
   └─ Conversation: ⏸️ Skipped (models unavailable)

⚪ OLLAMA
   └─ Models: ⚪ Not configured
   └─ Conversation: ⚪ Not configured

=============================================================
📊 Test Suite Summary
=============================================================
Total Tests:      12
Successful Tests: 4
Failed Tests:     8
Success Rate:     33.3%
Execution Time:   15.2s
Overall Status:   PARTIAL_FAILURE
Failed Tests:     moonshot:models, moonshot:conversation
=============================================================
```

### JSON Output Structure
```json
{
  "suite_info": {
    "start_time": "2025-01-24T10:30:00Z",
    "providers_tested": ["zhipu", "deepseek", "moonshot"],
    "test_types": ["models", "conversation"],
    "total_providers": 3
  },
  "test_results": {
    "models": {
      "deepseek": {
        "status": "success",
        "provider": "deepseek",
        "test_type": "models",
        "response_time_ms": 850.5,
        "data": {
          "models_count": 3,
          "sample_models": ["deepseek-chat", "deepseek-coder"]
        },
        "timestamp": "2025-01-24T10:30:01Z"
      }
    },
    "conversation": {
      "deepseek": {
        "status": "success",
        "provider": "deepseek",
        "test_type": "conversation",
        "response_time_ms": 1205.3,
        "data": {
          "response": "你好！我是DeepSeek，一个AI助手...",
          "response_length": 45,
          "valid_response": true
        },
        "timestamp": "2025-01-24T10:30:02Z"
      }
    }
  },
  "summary": {
    "end_time": "2025-01-24T10:30:15Z",
    "execution_time_seconds": 15.2,
    "total_tests": 6,
    "successful_tests": 2,
    "failed_tests": 4,
    "success_rate": 0.33,
    "failed_providers": ["moonshot:models", "moonshot:conversation"],
    "overall_status": "partial_failure"
  }
}
```

## Error Categories and Diagnostics

### Configuration Errors
- Missing API keys
- Invalid base URLs
- Provider not enabled

**Solution**: Check environment variables and provider configuration

### Network Errors
- Connection timeouts
- DNS resolution failures
- Firewall/proxy issues

**Solution**: Check network connectivity and firewall settings

### Authentication Errors
- Invalid API keys
- Expired tokens
- Insufficient permissions

**Solution**: Verify API key validity and permissions

### API Errors
- Rate limiting (429)
- Service unavailable (503)
- Invalid requests (400)

**Solution**: Check API quotas and request format

### Response Errors
- Malformed JSON responses
- Unexpected response format
- Missing required fields

**Solution**: Check API version compatibility and provider status

## Framework Architecture

### Core Components

1. **BaseProviderTest** - Base class with common testing patterns
2. **ComprehensiveProviderValidator** - Main test orchestrator
3. **TestResult** - Standardized result container
4. **ProviderTestConfig** - Configuration management
5. **ResponseValidator** - Response format validation
6. **MockProviderFactory** - Test data generation

### Provider-Specific Handling

- **Ollama**: Native API format, different endpoints (`/api/tags` vs `/models`)
- **OpenAI-compatible**: Standard OpenAI format (DeepSeek, Moonshot, OpenRouter, Qwen)
- **Zhipu GLM**: JWT authentication with OpenAI-compatible format
- **Error Handling**: Provider-specific error categorization and diagnostics

## Testing in Development

### Prerequisites
```bash
# Ensure backend service is running
cd /home/<USER>/source_code/talent_forge_pro
make up

# Verify API health
curl http://localhost:8001/api/v1/health
```

### Running Framework Tests
```bash
# Run framework unit tests
cd /home/<USER>/source_code/talent_forge_pro/app/backend
pytest tests/ai_providers/test_framework_demo.py -v

# Test with mock data
python tests/ai_providers/test_framework_demo.py
```

### Debug Mode
```bash
# Enable verbose logging
export PYTHONPATH=/home/<USER>/source_code/talent_forge_pro/app/backend
python -c "
import asyncio
import logging
logging.basicConfig(level=logging.DEBUG)
from tests.ai_providers.test_comprehensive_provider_validation import ComprehensiveProviderValidator
from tests.ai_providers.base_provider_test import ProviderTestConfig

async def debug_test():
    config = ProviderTestConfig(timeout=60, max_retries=1)
    validator = ComprehensiveProviderValidator(config)
    result = await validator.test_models_endpoint('deepseek')
    print(f'Result: {result}')

asyncio.run(debug_test())
"
```

## Integration with CI/CD

### GitHub Actions Example
```yaml
- name: Test AI Providers
  run: |
    cd app/scripts/test
    python run_ai_provider_tests_simple.py \
      --providers deepseek,ollama \
      --output json \
      --output-file ai_test_results.json \
      --timeout 45
      
- name: Upload Test Results
  uses: actions/upload-artifact@v3
  with:
    name: ai-provider-test-results
    path: app/scripts/test/ai_test_results.json
```

## Troubleshooting

### Common Issues

1. **"Cannot connect to API"**
   - Ensure backend service is running: `make up`
   - Check API URL: `curl http://localhost:8001/api/v1/health`

2. **"Provider not configured"**
   - Run configuration validator: `python validate_ai_provider_config.py`
   - Check environment variables are set

3. **"Authentication failed"**
   - Verify API keys are correct and not expired
   - Check API key permissions and quotas

4. **"Ollama connection refused"**
   - Start Ollama service: `ollama serve`
   - Verify Ollama is running: `curl http://localhost:11434/api/tags`
   - Pull required models: `ollama pull qwen2.5:14b`

5. **Import errors**
   - Ensure Python path includes backend: `export PYTHONPATH=/path/to/backend`
   - Check all dependencies are installed

### Getting Support

1. **Check Logs**: Review backend logs for detailed error information
2. **Run Validation**: Use configuration validator to identify setup issues
3. **Test Individual Providers**: Use `--providers` flag to isolate issues
4. **Enable Debug Mode**: Set logging level to DEBUG for detailed tracing

## Performance Considerations

- **Default Timeout**: 30 seconds per provider test
- **Retry Logic**: 2 retries with exponential backoff for transient failures
- **Concurrent Testing**: Providers tested in parallel for efficiency
- **Memory Usage**: Minimal memory footprint, suitable for CI/CD environments
- **Network Optimization**: Connection pooling and keep-alive for multiple requests

## Security Considerations

- **API Keys**: Never log or expose API keys in output
- **Error Messages**: Sanitize error messages to prevent information disclosure
- **Network Isolation**: Tests can run in isolated network environments
- **Rate Limiting**: Respect provider rate limits to avoid service disruption