#!/bin/bash
# AI Provider Testing Script using venv Python directly

echo "🤖 AI Provider Testing (Direct venv execution)"
echo "=============================================="
echo ""

# Set Python path to venv
PYTHON_BIN="/home/<USER>/source_code/talent_forge_pro/app/backend/venv/bin/python"

# Check if venv Python exists
if [ ! -f "$PYTHON_BIN" ]; then
    echo "❌ Virtual environment Python not found at: $PYTHON_BIN"
    echo "Please ensure the virtual environment is properly set up."
    exit 1
fi

echo "🐍 Using Python: $PYTHON_BIN"

# Change to test directory
cd /home/<USER>/source_code/talent_forge_pro/app/scripts/test

# Run the test with venv Python
$PYTHON_BIN run_ai_provider_tests_simple.py "$@"