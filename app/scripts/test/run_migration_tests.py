#!/usr/bin/env python3
"""
Migration tests execution script
Runs the complete migration test suite with proper environment setup
"""
import asyncio
import sys
import os
from pathlib import Path

# Add backend to path
backend_path = Path(__file__).parent.parent.parent / "backend"
sys.path.insert(0, str(backend_path))

# Add tests to path
tests_path = backend_path / "tests"
sys.path.insert(0, str(tests_path))


def check_environment():
    """Check that we're in the correct environment for testing"""
    env = os.environ.get('ENVIRONMENT', 'development')
    if env not in ['development', 'test']:
        print(f"❌ Warning: Running tests in {env} environment")
        print("   Tests should typically run in development or test environment")
        return False
    return True


def check_database_connection():
    """Check that database connection is available"""
    try:
        from app.core.database import async_engine
        return True
    except Exception as e:
        print(f"❌ Database connection check failed: {e}")
        return False


async def run_migration_validation():
    """Run the migration validation script directly"""
    scripts_path = Path(__file__).parent.parent / "database"
    sys.path.insert(0, str(scripts_path))
    
    try:
        import validate_migration
        print("🔍 Running migration validation...")
        result = await validate_migration.main()
        return result == 0
    except ImportError as e:
        print(f"❌ Could not import validation script: {e}")
        return False
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return False
    finally:
        if str(scripts_path) in sys.path:
            sys.path.remove(str(scripts_path))


def run_pytest_migration_tests():
    """Run pytest migration tests"""
    try:
        from test_migration_runner import MigrationTestRunner
        
        runner = MigrationTestRunner()
        results = runner.run_all_tests(include_integration=True)
        success = runner.print_results_summary(results)
        
        return success
        
    except ImportError as e:
        print(f"❌ Could not import test runner: {e}")
        return False


def run_specific_test_category(category: str):
    """Run a specific category of migration tests"""
    try:
        from test_migration_runner import (
            run_validation_only, run_safety_only, run_unit_only, MigrationTestRunner
        )
        
        if category == "validation":
            return run_validation_only() == 0
        elif category == "safety":
            return run_safety_only() == 0
        elif category == "unit":
            return run_unit_only() == 0
        elif category == "integration":
            runner = MigrationTestRunner()
            runner.add_scripts_to_path()
            try:
                return runner.run_integration_tests() == 0
            finally:
                runner.remove_scripts_from_path()
        else:
            print(f"❌ Unknown test category: {category}")
            return False
            
    except ImportError as e:
        print(f"❌ Could not import test functions: {e}")
        return False


async def main():
    """Main test execution function"""
    print("🚀 Migration Fix Test Suite")
    print("=" * 50)
    
    # Check environment
    if not check_environment():
        print("⚠️  Environment warning - continuing anyway")
    
    # Check database
    if not check_database_connection():
        print("❌ Database connection failed - some tests may not work")
    
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Run migration fix tests")
    parser.add_argument("--category", choices=["all", "validation", "safety", "unit", "integration", "quick"],
                       default="quick", help="Category of tests to run")
    parser.add_argument("--validate-only", action="store_true", help="Run only migration validation")
    
    args = parser.parse_args()
    
    success = True
    
    # Run validation if requested or as part of other tests
    if args.validate_only or args.category in ["all", "validation", "quick"]:
        print("\n🔍 Step 1: Migration State Validation")
        print("-" * 30)
        validation_success = await run_migration_validation()
        if not validation_success:
            print("❌ Migration validation failed!")
            success = False
        else:
            print("✅ Migration validation passed!")
        
        if args.validate_only:
            return 0 if validation_success else 1
    
    # Run pytest tests
    if args.category != "validation":
        print(f"\n🧪 Step 2: Running {args.category} tests")
        print("-" * 30)
        
        if args.category == "all":
            pytest_success = run_pytest_migration_tests()
        elif args.category == "quick":
            # Quick tests: unit, validation, safety (no integration)
            from test_migration_runner import MigrationTestRunner
            runner = MigrationTestRunner()
            results = runner.run_quick_tests()
            pytest_success = runner.print_results_summary(results)
        else:
            pytest_success = run_specific_test_category(args.category)
        
        if not pytest_success:
            print("❌ Some pytest tests failed!")
            success = False
        else:
            print("✅ All pytest tests passed!")
    
    # Final summary
    print("\n" + "=" * 50)
    if success:
        print("🎉 ALL MIGRATION TESTS PASSED!")
        print("✅ Migration fix implementation is working correctly")
        print("✅ Database state is validated and consistent")
        print("✅ Safety mechanisms are in place")
        return 0
    else:
        print("❌ SOME MIGRATION TESTS FAILED!")
        print("🔧 Please review the test failures and fix issues")
        print("💡 You can run specific test categories to isolate issues:")
        print("   python run_migration_tests.py --category validation")
        print("   python run_migration_tests.py --category safety")
        print("   python run_migration_tests.py --category unit")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)