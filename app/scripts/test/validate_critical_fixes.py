#!/usr/bin/env python3
"""
Critical Backend Fixes Validation Script

This script validates that all critical fixes implemented for TalentForge Pro
backend issues are working correctly.

Validation Areas:
1. SQLAlchemy Model Relationships (Answer model import)
2. MinIO Bucket Auto-Creation 
3. Storage Service Health Checks
4. Application Startup Validation
5. Authentication API Functionality

Usage:
    python validate_critical_fixes.py
    python validate_critical_fixes.py --verbose
    python validate_critical_fixes.py --fix-mode  # Attempt to fix issues
"""

import asyncio
import sys
import os
import argparse
from datetime import datetime, timezone
from typing import Dict, Any, List
import logging

# Add backend app to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'backend'))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CriticalFixesValidator:
    """Comprehensive validation for critical backend fixes"""
    
    def __init__(self, verbose: bool = False, fix_mode: bool = False):
        self.verbose = verbose
        self.fix_mode = fix_mode
        self.results = {
            "passed": [],
            "failed": [],
            "warnings": [],
            "fixed": [],
            "summary": {}
        }
        
        if verbose:
            logger.setLevel(logging.DEBUG)
    
    async def validate_all(self) -> Dict[str, Any]:
        """Run all validation tests"""
        print("🔍 Starting Critical Backend Fixes Validation")
        print("=" * 60)
        
        # Test categories
        test_categories = [
            ("Model Relationships", self.validate_model_relationships),
            ("Storage Service", self.validate_storage_service),
            ("Health Checks", self.validate_health_checks),
            ("Authentication APIs", self.validate_auth_apis),
            ("Startup Validation", self.validate_startup_process)
        ]
        
        for category_name, test_func in test_categories:
            print(f"\n📋 Testing: {category_name}")
            print("-" * 40)
            
            try:
                await test_func()
            except Exception as e:
                error_msg = f"Critical error in {category_name}: {str(e)}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
                logger.error(f"Test category failed: {category_name}", exc_info=True)
        
        return self.generate_report()
    
    async def validate_model_relationships(self):
        """Validate SQLAlchemy model relationships"""
        try:
            # Test 1: Import all models including Answer
            try:
                from app.models import Answer, Question, QuestionnaireResponse
                self.results["passed"].append("✅ Answer model import successful")
                print("✅ Answer model imported successfully")
            except ImportError as e:
                error_msg = f"Answer model import failed: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
                return
            
            # Test 2: Validate model validation service
            try:
                from app.services.model_validation_service import model_validation_service
                validation_results = model_validation_service.validate_all_models()
                
                if validation_results["status"] in ["PASSED", "PASSED_WITH_WARNINGS"]:
                    self.results["passed"].append("✅ Model relationship validation passed")
                    print(f"✅ Model validation: {validation_results['status']}")
                    
                    if validation_results["status"] == "PASSED_WITH_WARNINGS":
                        warnings = validation_results.get("details", {}).get("warnings", [])
                        for warning in warnings[:3]:  # Show first 3 warnings
                            self.results["warnings"].append(f"Model warning: {warning}")
                            print(f"⚠️ Warning: {warning}")
                else:
                    error_msg = f"Model validation failed: {validation_results['status']}"
                    self.results["failed"].append(error_msg)
                    print(f"❌ {error_msg}")
                    
                    # Show critical errors
                    critical_errors = validation_results.get("details", {}).get("critical_errors", [])
                    for error in critical_errors[:3]:
                        print(f"  🚨 {error}")
                
            except Exception as e:
                error_msg = f"Model validation service error: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
            
            # Test 3: Test Answer-QuestionnaireResponse relationship
            try:
                # Verify the relationship exists and is properly configured
                from sqlalchemy import inspect
                
                # Check QuestionnaireResponse -> Answer relationship
                qr_mapper = inspect(QuestionnaireResponse)
                if 'answers' in qr_mapper.relationships:
                    answer_rel = qr_mapper.relationships['answers']
                    if answer_rel.entity.class_ == Answer:
                        self.results["passed"].append("✅ QuestionnaireResponse -> Answer relationship valid")
                        print("✅ QuestionnaireResponse -> Answer relationship configured correctly")
                    else:
                        error_msg = "QuestionnaireResponse.answers points to wrong model"
                        self.results["failed"].append(error_msg)
                        print(f"❌ {error_msg}")
                else:
                    error_msg = "QuestionnaireResponse.answers relationship missing"
                    self.results["failed"].append(error_msg)
                    print(f"❌ {error_msg}")
                
                # Check Answer -> QuestionnaireResponse relationship
                answer_mapper = inspect(Answer)
                if 'response' in answer_mapper.relationships:
                    response_rel = answer_mapper.relationships['response']
                    if response_rel.entity.class_ == QuestionnaireResponse:
                        self.results["passed"].append("✅ Answer -> QuestionnaireResponse relationship valid")
                        print("✅ Answer -> QuestionnaireResponse relationship configured correctly")
                    else:
                        error_msg = "Answer.response points to wrong model"
                        self.results["failed"].append(error_msg)
                        print(f"❌ {error_msg}")
                else:
                    error_msg = "Answer.response relationship missing"
                    self.results["failed"].append(error_msg)
                    print(f"❌ {error_msg}")
                    
            except Exception as e:
                error_msg = f"Relationship inspection failed: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
                
        except Exception as e:
            error_msg = f"Model relationship validation failed: {e}"
            self.results["failed"].append(error_msg)
            print(f"❌ {error_msg}")
    
    async def validate_storage_service(self):
        """Validate storage service and bucket auto-creation"""
        try:
            # Test 1: Import storage service
            try:
                from app.services.storage_service import storage_service
                self.results["passed"].append("✅ Storage service import successful")
                print("✅ Storage service imported successfully")
            except ImportError as e:
                error_msg = f"Storage service import failed: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
                return
            
            # Test 2: Check storage service health
            try:
                health_result = storage_service.health_check()
                status = health_result.get("status", "unknown")
                
                if status == "healthy":
                    self.results["passed"].append("✅ Storage service healthy")
                    print("✅ Storage service is healthy")
                    
                    # Check bucket status
                    bucket_status = health_result.get("bucket_status", {})
                    buckets_ready = bucket_status.get("buckets_ready", False)
                    
                    if buckets_ready:
                        self.results["passed"].append("✅ All required buckets available")
                        print("✅ All required buckets are available")
                    else:
                        missing_count = bucket_status.get("missing_count", 0)
                        if missing_count > 0:
                            warning_msg = f"Storage service has {missing_count} missing buckets"
                            self.results["warnings"].append(warning_msg)
                            print(f"⚠️ {warning_msg}")
                        
                        if self.fix_mode:
                            print("🔧 Fix mode: Attempting to create missing buckets...")
                            # Bucket creation happens automatically during service init
                            # Re-check after a moment
                            await asyncio.sleep(1)
                            updated_health = storage_service.health_check()
                            updated_bucket_status = updated_health.get("bucket_status", {})
                            if updated_bucket_status.get("buckets_ready", False):
                                self.results["fixed"].append("🔧 Missing buckets created successfully")
                                print("✅ Missing buckets created successfully")
                
                elif status == "degraded":
                    warning_msg = f"Storage service degraded: {health_result.get('details', 'Unknown')}"
                    self.results["warnings"].append(warning_msg)
                    print(f"⚠️ {warning_msg}")
                    
                else:
                    error_msg = f"Storage service unhealthy: {health_result.get('error', 'Unknown')}"
                    self.results["failed"].append(error_msg)
                    print(f"❌ {error_msg}")
                    
            except Exception as e:
                error_msg = f"Storage service health check failed: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
            
            # Test 3: Test bucket auto-creation functionality
            try:
                # Check if the enhanced _ensure_buckets method exists
                if hasattr(storage_service, '_ensure_buckets'):
                    self.results["passed"].append("✅ Enhanced bucket management available")
                    print("✅ Enhanced bucket management functionality available")
                else:
                    error_msg = "Enhanced bucket management method missing"
                    self.results["failed"].append(error_msg)
                    print(f"❌ {error_msg}")
                    
            except Exception as e:
                error_msg = f"Bucket management validation failed: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
                
        except Exception as e:
            error_msg = f"Storage service validation failed: {e}"
            self.results["failed"].append(error_msg)
            print(f"❌ {error_msg}")
    
    async def validate_health_checks(self):
        """Validate enhanced health check functionality"""
        try:
            # Test 1: Import health service
            try:
                from app.services.health import health_service
                self.results["passed"].append("✅ Health service import successful")
                print("✅ Health service imported successfully")
            except ImportError as e:
                error_msg = f"Health service import failed: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
                return
            
            # Test 2: Check comprehensive health check
            try:
                health_results = await health_service.check_all(use_cache=False)
                
                overall_status = health_results.get("status", "unknown")
                if overall_status in ["healthy", "degraded"]:
                    self.results["passed"].append(f"✅ Overall system health: {overall_status}")
                    print(f"✅ Overall system health: {overall_status}")
                    
                    # Check if storage service is included
                    services = health_results.get("services", {})
                    if "storage" in services:
                        storage_status = services["storage"].get("status", "unknown")
                        self.results["passed"].append(f"✅ Storage service in health check: {storage_status}")
                        print(f"✅ Storage service included in health check: {storage_status}")
                    else:
                        warning_msg = "Storage service not included in health check"
                        self.results["warnings"].append(warning_msg)
                        print(f"⚠️ {warning_msg}")
                        
                    # Report service statuses
                    healthy_services = [name for name, status in services.items() 
                                      if status.get("status") == "healthy"]
                    if healthy_services:
                        self.results["passed"].append(f"✅ Healthy services: {', '.join(healthy_services)}")
                        print(f"✅ Healthy services: {', '.join(healthy_services)}")
                
                else:
                    error_msg = f"System health check failed: {overall_status}"
                    self.results["failed"].append(error_msg)
                    print(f"❌ {error_msg}")
                    
            except Exception as e:
                error_msg = f"Health check execution failed: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
                
        except Exception as e:
            error_msg = f"Health check validation failed: {e}"
            self.results["failed"].append(error_msg)
            print(f"❌ {error_msg}")
    
    async def validate_auth_apis(self):
        """Validate that authentication APIs work with fixed model relationships"""
        try:
            # Test 1: Import auth dependencies
            try:
                from app.models import User, QuestionnaireResponse, Answer
                from app.crud import user as user_crud
                self.results["passed"].append("✅ Auth-related models imported successfully")
                print("✅ Auth-related models imported successfully")
            except ImportError as e:
                error_msg = f"Auth model import failed: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
                return
            
            # Test 2: Test database connectivity for auth
            try:
                from app.core.database import AsyncSessionLocal
                
                async with AsyncSessionLocal() as session:
                    # Simple query to test database connection
                    from sqlalchemy import text
                    result = await session.execute(text("SELECT 1"))
                    result.scalar()
                    
                self.results["passed"].append("✅ Database connectivity for auth working")
                print("✅ Database connectivity for authentication working")
                
            except Exception as e:
                error_msg = f"Database connectivity test failed: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
            
            # Test 3: Check if auth endpoints can be imported (model relationship test)
            try:
                from app.api.v1 import auth
                self.results["passed"].append("✅ Auth API endpoints importable")
                print("✅ Auth API endpoints can be imported (model relationships working)")
            except Exception as e:
                error_msg = f"Auth API import failed (possible model relationship issue): {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
                
        except Exception as e:
            error_msg = f"Auth API validation failed: {e}"
            self.results["failed"].append(error_msg)
            print(f"❌ {error_msg}")
    
    async def validate_startup_process(self):
        """Validate that startup process works with all fixes"""
        try:
            # Test 1: Model validation service startup check
            try:
                from app.services.model_validation_service import validate_models_on_startup
                
                models_valid = await validate_models_on_startup()
                if models_valid:
                    self.results["passed"].append("✅ Startup model validation passed")
                    print("✅ Startup model validation passed")
                else:
                    warning_msg = "Startup model validation reported issues"
                    self.results["warnings"].append(warning_msg)
                    print(f"⚠️ {warning_msg}")
                    
            except Exception as e:
                error_msg = f"Startup model validation failed: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
            
            # Test 2: Check that main app components can be imported
            try:
                from app.main import app
                self.results["passed"].append("✅ Main FastAPI app importable")
                print("✅ Main FastAPI application can be imported")
            except Exception as e:
                error_msg = f"Main app import failed: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
                
            # Test 3: Check startup lifespan function
            try:
                from app.main import lifespan
                self.results["passed"].append("✅ Startup lifespan function available")
                print("✅ Startup lifespan function available")
            except Exception as e:
                error_msg = f"Startup lifespan function missing: {e}"
                self.results["failed"].append(error_msg)
                print(f"❌ {error_msg}")
                
        except Exception as e:
            error_msg = f"Startup process validation failed: {e}"
            self.results["failed"].append(error_msg)
            print(f"❌ {error_msg}")
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive validation report"""
        total_tests = (
            len(self.results["passed"]) + 
            len(self.results["failed"]) + 
            len(self.results["warnings"]) +
            len(self.results["fixed"])
        )
        
        success_count = len(self.results["passed"]) + len(self.results["fixed"])
        success_rate = (success_count / total_tests * 100) if total_tests > 0 else 0
        
        # Determine overall status
        if len(self.results["failed"]) > 0:
            if len(self.results["failed"]) > len(self.results["passed"]):
                overall_status = "CRITICAL_FAILED"
                status_emoji = "🚨"
            else:
                overall_status = "FAILED"
                status_emoji = "❌"
        elif len(self.results["warnings"]) > 0:
            overall_status = "PASSED_WITH_WARNINGS"
            status_emoji = "⚠️"
        else:
            overall_status = "PASSED"
            status_emoji = "✅"
        
        report = {
            "status": overall_status,
            "status_emoji": status_emoji,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "summary": {
                "total_tests": total_tests,
                "passed": len(self.results["passed"]),
                "failed": len(self.results["failed"]),
                "warnings": len(self.results["warnings"]),
                "fixed": len(self.results["fixed"]),
                "success_rate": round(success_rate, 1)
            },
            "results": self.results,
            "recommendations": self.generate_recommendations()
        }
        
        # Print summary
        print("\n" + "=" * 60)
        print("📊 CRITICAL FIXES VALIDATION REPORT")
        print("=" * 60)
        print(f"Overall Status: {status_emoji} {overall_status}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"✅ Passed: {len(self.results['passed'])}")
        print(f"❌ Failed: {len(self.results['failed'])}")
        print(f"⚠️ Warnings: {len(self.results['warnings'])}")
        print(f"🔧 Fixed: {len(self.results['fixed'])}")
        
        if self.results["failed"]:
            print(f"\n🚨 CRITICAL ISSUES:")
            for failure in self.results["failed"]:
                print(f"  • {failure}")
        
        if self.results["warnings"]:
            print(f"\n⚠️ WARNINGS:")
            for warning in self.results["warnings"]:
                print(f"  • {warning}")
        
        if self.results["fixed"]:
            print(f"\n🔧 FIXES APPLIED:")
            for fix in self.results["fixed"]:
                print(f"  • {fix}")
        
        print(f"\n💡 RECOMMENDATIONS:")
        for rec in report["recommendations"]:
            print(f"  • {rec}")
        
        print("=" * 60)
        
        return report
    
    def generate_recommendations(self) -> List[str]:
        """Generate recommendations based on validation results"""
        recommendations = []
        
        if self.results["failed"]:
            recommendations.append("🚨 CRITICAL: Fix failed tests immediately before deploying")
            recommendations.append("🔧 Run `make db-migrate` to ensure database schema is current")
            recommendations.append("🐳 Restart services: `make down && make up`")
        
        if self.results["warnings"]:
            recommendations.append("⚠️ Review warnings for potential issues")
            recommendations.append("📊 Monitor service health after deployment")
        
        if not any(self.results.values()):
            recommendations.append("🎉 All critical fixes validated successfully!")
            recommendations.append("✅ System ready for deployment")
        else:
            recommendations.append("🔍 Run validation again after fixes")
            recommendations.append("📝 Document any remaining issues")
        
        return recommendations


async def main():
    """Main validation script entry point"""
    parser = argparse.ArgumentParser(
        description="Validate critical backend fixes for TalentForge Pro"
    )
    parser.add_argument(
        "--verbose", "-v", 
        action="store_true", 
        help="Enable verbose logging"
    )
    parser.add_argument(
        "--fix-mode", "-f",
        action="store_true",
        help="Attempt to fix issues where possible"
    )
    
    args = parser.parse_args()
    
    validator = CriticalFixesValidator(
        verbose=args.verbose,
        fix_mode=args.fix_mode
    )
    
    try:
        results = await validator.validate_all()
        
        # Exit with appropriate code
        if results["status"] in ["PASSED", "PASSED_WITH_WARNINGS"]:
            print(f"\n🎉 Validation completed successfully!")
            sys.exit(0)
        else:
            print(f"\n❌ Validation failed - fix issues before deployment")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n🚨 Critical validation error: {e}")
        logger.error("Validation script failed", exc_info=True)
        sys.exit(2)


if __name__ == "__main__":
    asyncio.run(main())