#!/usr/bin/env python3
"""
Test script to verify compatibility layers work correctly
"""
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../backend'))

def test_imports():
    """Test that all compatibility layers can be imported"""
    results = []
    
    # Test LLM Service
    try:
        from app.services.llm_service import LLMService, llm_service
        results.append(("llm_service", True, "Imported successfully"))
    except Exception as e:
        results.append(("llm_service", False, str(e)))
    
    # Test LLM Provider
    try:
        from app.services.llm_provider import LLMProviderFactory, BaseLLMProvider
        results.append(("llm_provider", True, "Imported successfully"))
    except Exception as e:
        results.append(("llm_provider", False, str(e)))
    
    # Test LLM Service Refactored
    try:
        from app.services.llm_service_refactored import LLMServiceRefactored, llm_service_refactored
        results.append(("llm_service_refactored", True, "Imported successfully"))
    except Exception as e:
        results.append(("llm_service_refactored", False, str(e)))
    
    # Test Health Check Service
    try:
        from app.services.health_check_service import health_service
        results.append(("health_check_service", True, "Imported successfully"))
    except Exception as e:
        results.append(("health_check_service", False, str(e)))
    
    # Test Health Service
    try:
        from app.services.health import health_service
        results.append(("health", True, "Imported successfully"))
    except Exception as e:
        results.append(("health", False, str(e)))
    
    return results


def test_basic_functionality():
    """Test basic functionality of compatibility layers"""
    results = []
    
    # Test LLMService instantiation
    try:
        from app.services.llm_service import LLMService
        service = LLMService()
        results.append(("LLMService instantiation", True, "Created successfully"))
    except Exception as e:
        results.append(("LLMService instantiation", False, str(e)))
    
    # Test LLMProviderFactory
    try:
        from app.services.llm_provider import LLMProviderFactory
        provider = LLMProviderFactory.create_provider("deepseek")
        results.append(("LLMProviderFactory", True, "Provider created"))
    except Exception as e:
        results.append(("LLMProviderFactory", False, str(e)))
    
    return results


def main():
    print("=" * 60)
    print("Testing Compatibility Layers for AI Services")
    print("=" * 60)
    
    # Test imports
    print("\n1. Testing Imports:")
    print("-" * 40)
    import_results = test_imports()
    for name, success, message in import_results:
        status = "✓" if success else "✗"
        print(f"  {status} {name}: {message}")
    
    # Test functionality
    print("\n2. Testing Basic Functionality:")
    print("-" * 40)
    func_results = test_basic_functionality()
    for name, success, message in func_results:
        status = "✓" if success else "✗"
        print(f"  {status} {name}: {message}")
    
    # Summary
    print("\n" + "=" * 60)
    total_tests = len(import_results) + len(func_results)
    passed = sum(1 for _, success, _ in import_results + func_results if success)
    print(f"Summary: {passed}/{total_tests} tests passed")
    
    if passed == total_tests:
        print("✓ All compatibility layers are working correctly!")
        return 0
    else:
        print("✗ Some compatibility layers have issues.")
        return 1


if __name__ == "__main__":
    exit(main())