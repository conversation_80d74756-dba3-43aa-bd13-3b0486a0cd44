#!/usr/bin/env python3
"""
AI Provider Testing CLI Runner
Real API testing for all 6 AI providers with comprehensive reporting
"""
import argparse
import asyncio
import json
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, List, Optional

import httpx
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.panel import Panel
from rich.markdown import Markdown
from rich import print as rprint

# Add backend to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../backend'))

from tests.ai_providers.test_comprehensive_provider_validation import ComprehensiveProviderValidator
from tests.ai_providers.base_provider_test import ProviderTestConfig, TestStatus
from tests.ai_providers.test_config import PROVIDER_EXPECTATIONS

console = Console()

class AIProviderTestRunner:
    """
    CLI test runner for AI provider validation
    """
    
    def __init__(self):
        self.validator = None
        self.results = {}
    
    def setup_validator(self, timeout: int = 30, max_retries: int = 2) -> None:
        """Setup test validator with configuration"""
        config = ProviderTestConfig(
            timeout=timeout,
            max_retries=max_retries,
            conversation_test_prompt="你好，你是谁？",
            expected_response_min_length=10
        )
        self.validator = ComprehensiveProviderValidator(config)
    
    async def check_api_connectivity(self, base_url: str = "http://localhost:8001") -> bool:
        """
        Check if backend API is reachable
        
        Args:
            base_url: Backend API base URL
            
        Returns:
            bool: True if API is reachable
        """
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{base_url}/api/v1/health", timeout=5)
                return response.status_code == 200
        except Exception as e:
            console.print(f"[red]❌ Cannot connect to API at {base_url}[/red]")
            console.print(f"[red]   Error: {e}[/red]")
            return False
    
    def print_header(self, providers: List[str], test_types: List[str]) -> None:
        """Print test suite header"""
        header = f"""
# AI Provider Testing Suite
**Testing {len(providers)} providers with real API calls**

**Providers:** {', '.join(providers)}
**Test Types:** {', '.join(test_types)}
**Timestamp:** {datetime.now().isoformat()}
        """
        console.print(Panel(Markdown(header), title="🤖 AI Provider Test Suite", border_style="blue"))
    
    def print_provider_result(self, provider: str, models_result: Optional[Any], conversation_result: Optional[Any]) -> None:
        """Print individual provider test results"""
        # Determine overall status
        if models_result and models_result.status == TestStatus.SUCCESS:
            if conversation_result and conversation_result.status == TestStatus.SUCCESS:
                status_icon = "✅"
                status_color = "green"
            elif conversation_result and conversation_result.status == TestStatus.FAILED:
                status_icon = "⚠️"
                status_color = "yellow"
            else:
                status_icon = "⚠️"
                status_color = "yellow"
        elif models_result and models_result.status == TestStatus.NOT_CONFIGURED:
            status_icon = "⚪"
            status_color = "white"
        else:
            status_icon = "❌"
            status_color = "red"
        
        console.print(f"\n[{status_color}]{status_icon} {provider.upper()}[/{status_color}]")
        
        # Models test result
        if models_result:
            if models_result.status == TestStatus.SUCCESS:
                models_count = models_result.data.get('models_count', 0) if models_result.data else 0
                sample_models = models_result.data.get('sample_models', []) if models_result.data else []
                console.print(f"   └─ Models: [green]✅ {models_count} models found[/green]")
                if sample_models:
                    console.print(f"      Sample: {', '.join(sample_models[:3])}")
                if models_result.response_time_ms:
                    console.print(f"      Time: {models_result.response_time_ms:.0f}ms")
            elif models_result.status == TestStatus.NOT_CONFIGURED:
                console.print(f"   └─ Models: [white]⚪ Not configured[/white]")
            else:
                console.print(f"   └─ Models: [red]❌ {models_result.error[:60]}...[/red]")
        
        # Conversation test result  
        if conversation_result:
            if conversation_result.status == TestStatus.SUCCESS:
                response = conversation_result.data.get('response', '') if conversation_result.data else ''
                response_preview = response[:50] + '...' if len(response) > 50 else response
                console.print(f"   └─ Conversation: [green]✅ Response: \"{response_preview}\"[/green]")
                if conversation_result.response_time_ms:
                    console.print(f"      Time: {conversation_result.response_time_ms:.0f}ms")
            elif conversation_result.status == TestStatus.SKIPPED:
                console.print(f"   └─ Conversation: [yellow]⏸️ Skipped (models unavailable)[/yellow]")
            elif conversation_result.status == TestStatus.NOT_CONFIGURED:
                console.print(f"   └─ Conversation: [white]⚪ Not configured[/white]")
            else:
                console.print(f"   └─ Conversation: [red]❌ {conversation_result.error[:60]}...[/red]")
    
    def print_summary(self, results: Dict[str, Any]) -> None:
        """Print test suite summary"""
        summary = results.get('summary', {})
        
        # Create summary table
        table = Table(title="Test Suite Summary", show_header=True, header_style="bold magenta")
        table.add_column("Metric", style="dim")
        table.add_column("Value", justify="right")
        
        table.add_row("Total Tests", str(summary.get('total_tests', 0)))
        table.add_row("Successful Tests", str(summary.get('successful_tests', 0)))
        table.add_row("Failed Tests", str(summary.get('failed_tests', 0)))
        
        success_rate = summary.get('success_rate', 0)
        success_color = "green" if success_rate > 0.8 else "yellow" if success_rate > 0.5 else "red"
        table.add_row("Success Rate", f"[{success_color}]{success_rate*100:.1f}%[/{success_color}]")
        
        table.add_row("Execution Time", f"{summary.get('execution_time_seconds', 0):.1f}s")
        table.add_row("Overall Status", f"[bold]{summary.get('overall_status', 'unknown').upper()}[/bold]")
        
        console.print(table)
        
        # Print failed providers if any
        failed_providers = summary.get('failed_providers', [])
        if failed_providers:
            console.print(f"\n[red]Failed Tests:[/red] {', '.join(failed_providers)}")
    
    def generate_json_report(self, results: Dict[str, Any], output_file: str) -> None:
        """Generate JSON format report"""
        # Convert TestResult objects to dictionaries
        json_results = self._convert_results_to_json(results)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_results, f, indent=2, ensure_ascii=False)
        
        console.print(f"[green]✅ JSON report saved to: {output_file}[/green]")
    
    def generate_html_report(self, results: Dict[str, Any], output_file: str) -> None:
        """Generate HTML format report"""
        html_content = self._generate_html_content(results)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        console.print(f"[green]✅ HTML report saved to: {output_file}[/green]")
    
    def _convert_results_to_json(self, results: Dict[str, Any]) -> Dict[str, Any]:
        """Convert TestResult objects to JSON-serializable format"""
        json_results = {}
        
        for key, value in results.items():
            if isinstance(value, dict):
                json_results[key] = {}
                for sub_key, sub_value in value.items():
                    if hasattr(sub_value, '__dict__'):
                        # Convert TestResult to dict
                        json_results[key][sub_key] = {
                            'status': sub_value.status.value if hasattr(sub_value.status, 'value') else str(sub_value.status),
                            'provider': sub_value.provider if hasattr(sub_value, 'provider') else None,
                            'test_type': sub_value.test_type if hasattr(sub_value, 'test_type') else None,
                            'response_time_ms': sub_value.response_time_ms if hasattr(sub_value, 'response_time_ms') else None,
                            'data': sub_value.data if hasattr(sub_value, 'data') else None,
                            'error': sub_value.error if hasattr(sub_value, 'error') else None,
                            'error_category': sub_value.error_category.value if hasattr(sub_value, 'error_category') and hasattr(sub_value.error_category, 'value') else None,
                            'timestamp': sub_value.timestamp if hasattr(sub_value, 'timestamp') else None
                        }
                    else:
                        json_results[key][sub_key] = sub_value
            else:
                json_results[key] = value
        
        return json_results
    
    def _generate_html_content(self, results: Dict[str, Any]) -> str:
        """Generate HTML report content"""
        summary = results.get('summary', {})
        
        html = f"""
<!DOCTYPE html>
<html>
<head>
    <title>AI Provider Test Report</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f5f5f5; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .provider {{ margin: 15px 0; padding: 15px; border-left: 4px solid #ddd; }}
        .success {{ border-color: #28a745; }}
        .failure {{ border-color: #dc3545; }}
        .warning {{ border-color: #ffc107; }}
        .not-configured {{ border-color: #6c757d; }}
        .metric {{ display: inline-block; margin: 10px 20px 10px 0; }}
        .status {{ font-weight: bold; }}
        .error {{ color: #dc3545; font-size: 0.9em; }}
        .response {{ color: #28a745; font-style: italic; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 AI Provider Test Report</h1>
        <p><strong>Generated:</strong> {datetime.now().isoformat()}</p>
        <p><strong>Providers Tested:</strong> {results.get('suite_info', {}).get('providers_tested', [])}</p>
        <p><strong>Execution Time:</strong> {summary.get('execution_time_seconds', 0):.1f} seconds</p>
    </div>
    
    <div class="summary">
        <h2>Summary</h2>
        <div class="metric">📊 <strong>Total Tests:</strong> {summary.get('total_tests', 0)}</div>
        <div class="metric">✅ <strong>Successful:</strong> {summary.get('successful_tests', 0)}</div>
        <div class="metric">❌ <strong>Failed:</strong> {summary.get('failed_tests', 0)}</div>
        <div class="metric">📈 <strong>Success Rate:</strong> {summary.get('success_rate', 0)*100:.1f}%</div>
        <div class="metric status">🎯 <strong>Status:</strong> {summary.get('overall_status', 'unknown').upper()}</div>
    </div>
        """
        
        # Add provider results
        html += "<h2>Provider Results</h2>"
        
        models_results = results.get('test_results', {}).get('models', {})
        conversation_results = results.get('test_results', {}).get('conversation', {})
        
        for provider in results.get('suite_info', {}).get('providers_tested', []):
            models_result = models_results.get(provider)
            conversation_result = conversation_results.get(provider)
            
            # Determine CSS class
            if models_result and models_result.status == TestStatus.SUCCESS:
                css_class = "success"
            elif models_result and models_result.status == TestStatus.NOT_CONFIGURED:
                css_class = "not-configured"
            else:
                css_class = "failure"
            
            html += f'<div class="provider {css_class}">'
            html += f'<h3>{provider.upper()}</h3>'
            
            # Models test
            if models_result:
                if models_result.status == TestStatus.SUCCESS:
                    models_count = models_result.data.get('models_count', 0) if models_result.data else 0
                    html += f'<p>📋 <strong>Models:</strong> ✅ {models_count} models found'
                    if models_result.response_time_ms:
                        html += f' ({models_result.response_time_ms:.0f}ms)'
                    html += '</p>'
                elif models_result.status == TestStatus.NOT_CONFIGURED:
                    html += '<p>📋 <strong>Models:</strong> ⚪ Not configured</p>'
                else:
                    html += f'<p>📋 <strong>Models:</strong> ❌ Failed</p>'
                    if models_result.error:
                        html += f'<p class="error">Error: {models_result.error[:200]}...</p>'
            
            # Conversation test
            if conversation_result:
                if conversation_result.status == TestStatus.SUCCESS:
                    response = conversation_result.data.get('response', '') if conversation_result.data else ''
                    response_preview = response[:100] + '...' if len(response) > 100 else response
                    html += f'<p>💬 <strong>Conversation:</strong> ✅ Success'
                    if conversation_result.response_time_ms:
                        html += f' ({conversation_result.response_time_ms:.0f}ms)'
                    html += '</p>'
                    html += f'<p class="response">Response: "{response_preview}"</p>'
                elif conversation_result.status == TestStatus.SKIPPED:
                    html += '<p>💬 <strong>Conversation:</strong> ⏸️ Skipped (models unavailable)</p>'
                elif conversation_result.status == TestStatus.NOT_CONFIGURED:
                    html += '<p>💬 <strong>Conversation:</strong> ⚪ Not configured</p>'
                else:
                    html += '<p>💬 <strong>Conversation:</strong> ❌ Failed</p>'
                    if conversation_result.error:
                        html += f'<p class="error">Error: {conversation_result.error[:200]}...</p>'
            
            html += '</div>'
        
        html += """
</body>
</html>
        """
        
        return html
    
    async def run_tests(
        self,
        providers: Optional[List[str]] = None,
        test_types: Optional[List[str]] = None,
        timeout: int = 30,
        max_retries: int = 2,
        output_format: str = "console",
        output_file: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Run AI provider tests
        
        Args:
            providers: List of providers to test
            test_types: List of test types to run
            timeout: Request timeout in seconds
            max_retries: Maximum retry attempts
            output_format: Output format (console, json, html)
            output_file: Output file path
            
        Returns:
            Dict: Test results
        """
        # Setup validator
        self.setup_validator(timeout=timeout, max_retries=max_retries)
        
        # Default providers and test types
        all_providers = ["zhipu", "deepseek", "moonshot", "openrouter", "qwen", "ollama"]
        providers = providers or all_providers
        test_types = test_types or ["models", "conversation"]
        
        # Print header
        if output_format == "console":
            self.print_header(providers, test_types)
        
        # Run comprehensive test suite
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=console,
            disable=(output_format != "console")
        ) as progress:
            
            task = progress.add_task("Running AI provider tests...", total=len(providers) * len(test_types))
            
            results = await self.validator.run_comprehensive_test_suite(
                providers=providers,
                test_types=test_types
            )
            
            progress.update(task, completed=len(providers) * len(test_types))
        
        # Output results
        if output_format == "console":
            console.print("\n" + "="*60)
            console.print("[bold blue]Test Results[/bold blue]")
            console.print("="*60)
            
            models_results = results.get('test_results', {}).get('models', {})
            conversation_results = results.get('test_results', {}).get('conversation', {})
            
            for provider in providers:
                models_result = models_results.get(provider)
                conversation_result = conversation_results.get(provider)
                self.print_provider_result(provider, models_result, conversation_result)
            
            console.print("\n" + "="*60)
            self.print_summary(results)
            console.print("="*60)
        
        elif output_format == "json":
            if output_file:
                self.generate_json_report(results, output_file)
            else:
                console.print(json.dumps(self._convert_results_to_json(results), indent=2))
        
        elif output_format == "html":
            if output_file:
                self.generate_html_report(results, output_file)
            else:
                console.print("HTML format requires output file. Use --output-file parameter.")
        
        return results


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="AI Provider Testing CLI - Test all 6 AI providers with real API calls",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                    # Test all providers
  %(prog)s --providers deepseek,moonshot     # Test specific providers
  %(prog)s --tests models                    # Test only models endpoint
  %(prog)s --output json --output-file report.json  # Generate JSON report
  %(prog)s --timeout 60 --max-retries 3     # Custom timeout and retry settings
        """
    )
    
    parser.add_argument(
        '--providers',
        help='Comma-separated list of providers to test (zhipu,deepseek,moonshot,openrouter,qwen,ollama)',
        default=None
    )
    
    parser.add_argument(
        '--tests', 
        help='Comma-separated list of test types (models,conversation,fallback,configuration)',
        default='models,conversation'
    )
    
    parser.add_argument(
        '--timeout',
        type=int,
        default=30,
        help='Request timeout in seconds (default: 30)'
    )
    
    parser.add_argument(
        '--max-retries',
        type=int, 
        default=2,
        help='Maximum retry attempts (default: 2)'
    )
    
    parser.add_argument(
        '--output',
        choices=['console', 'json', 'html'],
        default='console',
        help='Output format (default: console)'
    )
    
    parser.add_argument(
        '--output-file',
        help='Output file path (required for json/html formats)'
    )
    
    parser.add_argument(
        '--api-url',
        default='http://localhost:8001',
        help='Backend API URL (default: http://localhost:8001)'
    )
    
    parser.add_argument(
        '--skip-connectivity-check',
        action='store_true',
        help='Skip API connectivity check'
    )
    
    args = parser.parse_args()
    
    # Parse providers and test types
    providers = [p.strip() for p in args.providers.split(',')] if args.providers else None
    test_types = [t.strip() for t in args.tests.split(',')]
    
    async def run_async():
        runner = AIProviderTestRunner()
        
        # Check API connectivity
        if not args.skip_connectivity_check:
            console.print(f"[blue]🔍 Checking API connectivity at {args.api_url}...[/blue]")
            if not await runner.check_api_connectivity(args.api_url):
                console.print("[red]❌ Backend API is not reachable. Please ensure the service is running.[/red]")
                console.print(f"[red]   Expected URL: {args.api_url}/api/v1/health[/red]")
                sys.exit(1)
            console.print("[green]✅ API connectivity confirmed[/green]")
        
        # Run tests
        try:
            results = await runner.run_tests(
                providers=providers,
                test_types=test_types,
                timeout=args.timeout,
                max_retries=args.max_retries,
                output_format=args.output,
                output_file=args.output_file
            )
            
            # Exit with error code if tests failed
            summary = results.get('summary', {})
            if summary.get('overall_status') == 'failure':
                sys.exit(1)
            elif summary.get('overall_status') == 'partial_failure':
                sys.exit(2)  # Partial failure exit code
                
        except KeyboardInterrupt:
            console.print("\n[yellow]⚠️ Test execution interrupted by user[/yellow]")
            sys.exit(130)
        except Exception as e:
            console.print(f"[red]❌ Test execution failed: {e}[/red]")
            sys.exit(1)
    
    # Run async main function
    asyncio.run(run_async())


if __name__ == "__main__":
    main()