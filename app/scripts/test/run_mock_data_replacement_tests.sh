#!/bin/bash
"""
Mock Data Replacement Test Suite Runner
TalentForge Pro - Comprehensive Test Execution Script

This script coordinates the execution of all test suites for the mock data replacement implementation:
- Unit Tests: Service methods, utilities, data transformation
- Integration Tests: API client, database workflows, component integration
- Performance Tests: Response times, concurrent load, memory usage
- Comprehensive E2E Tests: Complete user workflows and system validation

Usage:
  ./run_mock_data_replacement_tests.sh [--suite=all|unit|integration|performance|comprehensive] [--verbose] [--report]
"""

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "${SCRIPT_DIR}/../../.." && pwd)"
TEST_RESULTS_DIR="${PROJECT_ROOT}/test_results"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
MAGENTA='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Test configuration
SUITE="all"
VERBOSE=false
GENERATE_REPORT=false
CONTINUE_ON_FAILURE=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --suite=*)
      SUITE="${1#*=}"
      shift
      ;;
    --verbose|-v)
      VERBOSE=true
      shift
      ;;
    --report|-r)
      GENERATE_REPORT=true
      shift
      ;;
    --continue-on-failure|-c)
      CONTINUE_ON_FAILURE=true
      shift
      ;;
    --help|-h)
      cat << EOF
Mock Data Replacement Test Suite Runner

Usage: $0 [OPTIONS]

OPTIONS:
  --suite=SUITE         Test suite to run (all|unit|integration|performance|comprehensive)
  --verbose, -v         Enable verbose output
  --report, -r          Generate HTML test report
  --continue-on-failure Continue running tests even if some fail
  --help, -h           Show this help message

Test Suites:
  unit                  Unit tests for services, utilities, and components
  integration          Integration tests for APIs, database, and workflows
  performance          Performance tests for response times and system load
  comprehensive        Complete end-to-end validation tests
  all                  Run all test suites (default)

Examples:
  $0                                    # Run all tests
  $0 --suite=unit --verbose           # Run unit tests with verbose output
  $0 --suite=performance --report     # Run performance tests and generate report
  $0 --suite=all --continue-on-failure --report  # Full test suite with report
EOF
      exit 0
      ;;
    *)
      echo -e "${RED}Error: Unknown option $1${NC}" >&2
      echo "Use --help for usage information" >&2
      exit 1
      ;;
  esac
done

# Logging functions
log_info() {
    echo -e "${CYAN}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_test_start() {
    echo -e "${BLUE}[TEST START]${NC} $1"
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${MAGENTA}===========================================${NC}"
    fi
}

log_test_end() {
    local result=$1
    local test_name=$2
    if [[ "$result" == "0" ]]; then
        log_success "✅ $test_name PASSED"
    else
        log_error "❌ $test_name FAILED (exit code: $result)"
    fi
    if [[ "$VERBOSE" == "true" ]]; then
        echo -e "${MAGENTA}===========================================${NC}"
        echo
    fi
}

# Test result tracking
declare -A test_results
declare -a test_order

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker services are running
    if ! curl -f -s http://localhost:8088/api/v1/health >/dev/null 2>&1; then
        log_error "Backend API not accessible at http://localhost:8088"
        log_info "Please ensure Docker services are running: make up"
        exit 1
    fi
    
    if ! curl -f -s http://localhost:8088 >/dev/null 2>&1; then
        log_error "Frontend not accessible at http://localhost:8088"
        log_info "Please ensure Docker services are running: make up"
        exit 1
    fi
    
    # Check Python dependencies
    if ! python3 -c "import pytest, requests, psycopg2, selenium" 2>/dev/null; then
        log_warning "Some Python dependencies may be missing"
        log_info "Consider installing: pip install pytest requests psycopg2-binary selenium psutil matplotlib"
    fi
    
    # Check if Chrome is available for Selenium tests
    if ! command -v google-chrome >/dev/null && ! command -v chromium >/dev/null; then
        log_warning "Chrome/Chromium not found - frontend E2E tests may fail"
        log_info "Install Chrome: wget -q -O - https://dl.google.com/linux/linux_signing_key.pub | sudo apt-key add -"
    fi
    
    # Create test results directory
    mkdir -p "$TEST_RESULTS_DIR"
    
    log_success "Prerequisites check completed"
}

# Run unit tests
run_unit_tests() {
    log_test_start "Unit Tests - Service Layer and Components"
    
    local test_file="${SCRIPT_DIR}/unit_tests_mock_data_replacement.py"
    local result_file="${TEST_RESULTS_DIR}/unit_tests_${TIMESTAMP}.log"
    
    if [[ "$VERBOSE" == "true" ]]; then
        python3 "$test_file" 2>&1 | tee "$result_file"
        local exit_code=${PIPESTATUS[0]}
    else
        python3 "$test_file" > "$result_file" 2>&1
        local exit_code=$?
    fi
    
    test_results["unit"]=$exit_code
    test_order+=("unit")
    log_test_end $exit_code "Unit Tests"
    
    return $exit_code
}

# Run integration tests
run_integration_tests() {
    log_test_start "Integration Tests - API Client and Database Workflows"
    
    local test_file="${SCRIPT_DIR}/integration_tests_mock_data_replacement.py"
    local result_file="${TEST_RESULTS_DIR}/integration_tests_${TIMESTAMP}.log"
    
    if [[ "$VERBOSE" == "true" ]]; then
        python3 "$test_file" 2>&1 | tee "$result_file"
        local exit_code=${PIPESTATUS[0]}
    else
        python3 "$test_file" > "$result_file" 2>&1
        local exit_code=$?
    fi
    
    test_results["integration"]=$exit_code
    test_order+=("integration")
    log_test_end $exit_code "Integration Tests"
    
    return $exit_code
}

# Run performance tests
run_performance_tests() {
    log_test_start "Performance Tests - Response Times and System Load"
    
    local test_file="${SCRIPT_DIR}/performance_tests_mock_data_replacement.py"
    local result_file="${TEST_RESULTS_DIR}/performance_tests_${TIMESTAMP}.log"
    
    if [[ "$VERBOSE" == "true" ]]; then
        python3 "$test_file" 2>&1 | tee "$result_file"
        local exit_code=${PIPESTATUS[0]}
    else
        python3 "$test_file" > "$result_file" 2>&1
        local exit_code=$?
    fi
    
    test_results["performance"]=$exit_code
    test_order+=("performance")
    log_test_end $exit_code "Performance Tests"
    
    return $exit_code
}

# Run comprehensive tests
run_comprehensive_tests() {
    log_test_start "Comprehensive Tests - Complete E2E Validation"
    
    local test_file="${SCRIPT_DIR}/comprehensive_mock_data_replacement_tests.py"
    local result_file="${TEST_RESULTS_DIR}/comprehensive_tests_${TIMESTAMP}.log"
    
    if [[ "$VERBOSE" == "true" ]]; then
        python3 "$test_file" 2>&1 | tee "$result_file"
        local exit_code=${PIPESTATUS[0]}
    else
        python3 "$test_file" > "$result_file" 2>&1
        local exit_code=$?
    fi
    
    test_results["comprehensive"]=$exit_code
    test_order+=("comprehensive")
    log_test_end $exit_code "Comprehensive Tests"
    
    return $exit_code
}

# Generate HTML report
generate_html_report() {
    log_info "Generating HTML test report..."
    
    local report_file="${TEST_RESULTS_DIR}/test_report_${TIMESTAMP}.html"
    
    # Calculate summary statistics
    local total_tests=0
    local passed_tests=0
    local failed_tests=0
    
    for test_suite in "${test_order[@]}"; do
        total_tests=$((total_tests + 1))
        if [[ "${test_results[$test_suite]}" == "0" ]]; then
            passed_tests=$((passed_tests + 1))
        else
            failed_tests=$((failed_tests + 1))
        fi
    done
    
    local success_rate=0
    if [[ $total_tests -gt 0 ]]; then
        success_rate=$(( (passed_tests * 100) / total_tests ))
    fi
    
    # Generate HTML report
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TalentForge Pro - Mock Data Replacement Test Report</title>
    <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px 8px 0 0; }
        .content { padding: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 8px; text-align: center; border-left: 4px solid #007bff; }
        .stat-number { font-size: 2em; font-weight: bold; color: #333; }
        .stat-label { color: #666; margin-top: 5px; }
        .test-results { margin-bottom: 30px; }
        .test-suite { margin-bottom: 20px; border: 1px solid #e0e0e0; border-radius: 8px; overflow: hidden; }
        .test-suite-header { background: #f8f9fa; padding: 15px; border-bottom: 1px solid #e0e0e0; }
        .test-suite-content { padding: 15px; }
        .status-pass { color: #28a745; font-weight: bold; }
        .status-fail { color: #dc3545; font-weight: bold; }
        .timestamp { color: #666; font-size: 0.9em; }
        .footer { background: #f8f9fa; padding: 20px; border-top: 1px solid #e0e0e0; text-align: center; color: #666; }
        .progress-bar { width: 100%; height: 20px; background: #e0e0e0; border-radius: 10px; overflow: hidden; margin: 10px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #28a745, #20c997); transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 TalentForge Pro</h1>
            <h2>Mock Data Replacement Test Report</h2>
            <p class="timestamp">Generated: $(date '+%Y-%m-%d %H:%M:%S')</p>
        </div>
        
        <div class="content">
            <div class="summary">
                <div class="stat-card">
                    <div class="stat-number">$total_tests</div>
                    <div class="stat-label">Total Test Suites</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number status-pass">$passed_tests</div>
                    <div class="stat-label">Passed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number status-fail">$failed_tests</div>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$success_rate%</div>
                    <div class="stat-label">Success Rate</div>
                </div>
            </div>
            
            <div class="progress-bar">
                <div class="progress-fill" style="width: $success_rate%"></div>
            </div>
            
            <div class="test-results">
                <h3>Test Suite Results</h3>
EOF

    # Add individual test results
    for test_suite in "${test_order[@]}"; do
        local status_class="status-fail"
        local status_text="FAILED"
        local status_icon="❌"
        
        if [[ "${test_results[$test_suite]}" == "0" ]]; then
            status_class="status-pass"
            status_text="PASSED" 
            status_icon="✅"
        fi
        
        # Get test suite description
        local description=""
        case $test_suite in
            "unit") description="Service layer methods, utilities, and data transformation" ;;
            "integration") description="API client integration, database workflows, component integration" ;;
            "performance") description="Response times, concurrent load, memory usage, frontend performance" ;;
            "comprehensive") description="Complete end-to-end validation, user workflows, error handling" ;;
        esac
        
        cat >> "$report_file" << EOF
                <div class="test-suite">
                    <div class="test-suite-header">
                        <strong>$status_icon ${test_suite^} Tests</strong>
                        <span class="$status_class" style="float: right;">$status_text</span>
                    </div>
                    <div class="test-suite-content">
                        <p>$description</p>
                        <p><strong>Exit Code:</strong> ${test_results[$test_suite]}</p>
                        <p><strong>Log File:</strong> ${test_suite}_tests_${TIMESTAMP}.log</p>
                    </div>
                </div>
EOF
    done

    # Complete HTML report
    cat >> "$report_file" << EOF
            </div>
            
            <div class="test-details">
                <h3>Test Suite Details</h3>
                <ul>
                    <li><strong>Unit Tests:</strong> Validate service methods, utilities, and data transformation logic</li>
                    <li><strong>Integration Tests:</strong> Test API client integration, database workflows, and component communication</li>
                    <li><strong>Performance Tests:</strong> Measure response times, concurrent load handling, and memory usage</li>
                    <li><strong>Comprehensive Tests:</strong> End-to-end validation including database seeding, frontend integration, and error handling</li>
                </ul>
            </div>
            
            <div class="recommendations">
                <h3>Quality Assessment</h3>
EOF

    if [[ $success_rate -ge 90 ]]; then
        cat >> "$report_file" << EOF
                <p style="color: #28a745; font-weight: bold;">🎉 Excellent Quality - Production Ready</p>
                <p>All critical systems are functioning correctly. The mock data replacement implementation meets production quality standards.</p>
EOF
    elif [[ $success_rate -ge 80 ]]; then
        cat >> "$report_file" << EOF
                <p style="color: #fd7e14; font-weight: bold;">⚠️ Good Quality - Minor Issues</p>
                <p>Most systems are working well with some minor issues that should be addressed before production deployment.</p>
EOF
    elif [[ $success_rate -ge 60 ]]; then
        cat >> "$report_file" << EOF
                <p style="color: #dc3545; font-weight: bold;">❌ Poor Quality - Significant Issues</p>
                <p>Multiple systems have issues that must be resolved before production deployment. Review failed tests and implement fixes.</p>
EOF
    else
        cat >> "$report_file" << EOF
                <p style="color: #dc3545; font-weight: bold;">🚨 Critical Issues - Not Ready</p>
                <p>Major system failures detected. Immediate attention required to resolve critical issues before any deployment.</p>
EOF
    fi

    cat >> "$report_file" << EOF
            </div>
        </div>
        
        <div class="footer">
            <p>TalentForge Pro Mock Data Replacement Test Suite</p>
            <p>Test execution completed at $(date '+%Y-%m-%d %H:%M:%S')</p>
        </div>
    </div>
</body>
</html>
EOF

    log_success "HTML report generated: $report_file"
}

# Main execution function
main() {
    echo -e "${CYAN}"
    cat << "EOF"
╔══════════════════════════════════════════════════════════════╗
║                  TalentForge Pro Test Suite                 ║
║              Mock Data Replacement Validation               ║
╚══════════════════════════════════════════════════════════════╝
EOF
    echo -e "${NC}"
    
    log_info "Starting test execution with suite: $SUITE"
    log_info "Verbose output: $VERBOSE"
    log_info "Generate report: $GENERATE_REPORT"
    log_info "Continue on failure: $CONTINUE_ON_FAILURE"
    
    # Check prerequisites
    check_prerequisites
    
    # Track overall success
    local overall_success=true
    
    # Run selected test suites
    case $SUITE in
        "unit")
            run_unit_tests || overall_success=false
            ;;
        "integration") 
            run_integration_tests || overall_success=false
            ;;
        "performance")
            run_performance_tests || overall_success=false
            ;;
        "comprehensive")
            run_comprehensive_tests || overall_success=false
            ;;
        "all")
            # Run all test suites in order
            if ! run_unit_tests; then
                overall_success=false
                if [[ "$CONTINUE_ON_FAILURE" == "false" ]]; then
                    log_error "Unit tests failed, stopping execution"
                    exit 1
                fi
            fi
            
            if ! run_integration_tests; then
                overall_success=false
                if [[ "$CONTINUE_ON_FAILURE" == "false" ]]; then
                    log_error "Integration tests failed, stopping execution"
                    exit 1
                fi
            fi
            
            if ! run_performance_tests; then
                overall_success=false
                if [[ "$CONTINUE_ON_FAILURE" == "false" ]]; then
                    log_error "Performance tests failed, stopping execution"
                    exit 1
                fi
            fi
            
            if ! run_comprehensive_tests; then
                overall_success=false
                if [[ "$CONTINUE_ON_FAILURE" == "false" ]]; then
                    log_error "Comprehensive tests failed, stopping execution"
                    exit 1
                fi
            fi
            ;;
        *)
            log_error "Invalid test suite: $SUITE"
            echo "Valid options: all, unit, integration, performance, comprehensive"
            exit 1
            ;;
    esac
    
    # Generate report if requested
    if [[ "$GENERATE_REPORT" == "true" ]]; then
        generate_html_report
    fi
    
    # Final summary
    echo -e "\n${CYAN}===========================================${NC}"
    echo -e "${CYAN}           FINAL TEST SUMMARY${NC}"
    echo -e "${CYAN}===========================================${NC}"
    
    local total_tests=${#test_order[@]}
    local passed_tests=0
    local failed_tests=0
    
    for test_suite in "${test_order[@]}"; do
        if [[ "${test_results[$test_suite]}" == "0" ]]; then
            passed_tests=$((passed_tests + 1))
            log_success "✅ ${test_suite^} Tests: PASSED"
        else
            failed_tests=$((failed_tests + 1))
            log_error "❌ ${test_suite^} Tests: FAILED (${test_results[$test_suite]})"
        fi
    done
    
    echo
    log_info "📊 Test Results Summary:"
    log_info "   Total Test Suites: $total_tests"
    log_success "   Passed: $passed_tests"
    if [[ $failed_tests -gt 0 ]]; then
        log_error "   Failed: $failed_tests"
    else
        log_info "   Failed: $failed_tests"
    fi
    
    local success_rate=0
    if [[ $total_tests -gt 0 ]]; then
        success_rate=$(( (passed_tests * 100) / total_tests ))
    fi
    log_info "   Success Rate: $success_rate%"
    
    echo
    log_info "📁 Test Results Directory: $TEST_RESULTS_DIR"
    if [[ "$GENERATE_REPORT" == "true" ]]; then
        log_info "📄 HTML Report: ${TEST_RESULTS_DIR}/test_report_${TIMESTAMP}.html"
    fi
    
    # Final determination
    echo
    if [[ "$overall_success" == "true" ]]; then
        log_success "🎉 ALL TESTS PASSED - Mock Data Replacement is Production Ready!"
        exit 0
    else
        if [[ $success_rate -ge 80 ]]; then
            log_warning "⚠️ SOME TESTS FAILED - Review issues before production deployment"
            exit 1
        else
            log_error "❌ CRITICAL FAILURES - Immediate attention required"
            exit 2
        fi
    fi
}

# Execute main function
main "$@"