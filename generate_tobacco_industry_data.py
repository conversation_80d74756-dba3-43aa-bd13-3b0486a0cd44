#!/usr/bin/env python3
"""
烟草行业演示数据生成器
Generate tobacco industry specific demo data for TalentForge Pro dashboard
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any
import random

# 添加项目路径到Python路径
sys.path.append('/home/<USER>/source_code/talent_forge_pro/app/backend')

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.user import User
from app.models.candidate import Candidate
from app.models.position import Position
from app.models.assessment import CandidateAssessment
from app.core.id_generator import generate_snowflake_id

# 烟草行业相关数据
TOBACCO_COMPANIES = [
    "中国烟草总公司", "红塔集团", "白沙集团", "黄鹤楼集团", "芙蓉王集团", 
    "云烟集团", "中华集团", "利群集团", "双喜集团", "玉溪集团",
    "帝国烟草", "菲利普莫里斯", "英美烟草", "日本烟草", "雷诺兹烟草"
]

TOBACCO_POSITIONS = [
    # 生产制造类
    {"title": "烟叶种植技术专家", "department": "农业技术部", "level": "高级", "skills": ["烟叶种植", "农业技术", "质量控制", "土壤分析", "气候管理"]},
    {"title": "卷烟生产工程师", "department": "生产制造部", "level": "中级", "skills": ["卷烟工艺", "设备维护", "质量管理", "生产计划", "精益生产"]},
    {"title": "烟丝调配技师", "department": "技术研发部", "level": "中级", "skills": ["烟丝调配", "感官评吸", "工艺优化", "配方设计", "质量检测"]},
    {"title": "包装设计师", "department": "设计部", "level": "中级", "skills": ["包装设计", "平面设计", "品牌视觉", "创意策划", "Adobe设计软件"]},
    
    # 技术研发类
    {"title": "烟草化学分析师", "department": "质量检测部", "level": "中级", "skills": ["化学分析", "仪器检测", "数据分析", "实验室管理", "质量标准"]},
    {"title": "新产品研发经理", "department": "研发中心", "level": "高级", "skills": ["产品研发", "市场调研", "项目管理", "创新管理", "团队领导"]},
    {"title": "设备自动化工程师", "department": "设备部", "level": "中级", "skills": ["PLC编程", "自动化控制", "设备维修", "机械设计", "电气工程"]},
    
    # 营销销售类
    {"title": "区域销售经理", "department": "销售部", "level": "中级", "skills": ["销售管理", "渠道开发", "客户关系", "市场分析", "团队管理"]},
    {"title": "品牌营销专员", "department": "市场部", "level": "初级", "skills": ["品牌管理", "营销策划", "数字营销", "活动执行", "消费者洞察"]},
    {"title": "零售终端督导", "department": "渠道部", "level": "初级", "skills": ["终端管理", "陈列优化", "促销执行", "客户服务", "数据统计"]},
    
    # 管理职能类
    {"title": "供应链管理经理", "department": "供应链部", "level": "高级", "skills": ["供应链管理", "采购谈判", "库存控制", "成本管理", "风险控制"]},
    {"title": "质量管理专员", "department": "质量部", "level": "中级", "skills": ["质量管理体系", "ISO认证", "审核管理", "持续改进", "培训管理"]},
    {"title": "财务分析师", "department": "财务部", "level": "中级", "skills": ["财务分析", "成本核算", "预算管理", "Excel高级应用", "SAP系统"]},
    {"title": "人力资源专员", "department": "人事部", "level": "初级", "skills": ["招聘管理", "薪酬福利", "员工关系", "培训发展", "劳动法规"]},
    
    # 合规监管类
    {"title": "法务合规经理", "department": "法务部", "level": "高级", "skills": ["法律事务", "合规管理", "风险控制", "合同审核", "政策解读"]},
    {"title": "税务筹划专员", "department": "财务部", "level": "中级", "skills": ["税务筹划", "政策解读", "申报管理", "风险评估", "沟通协调"]},
]

CHINESE_NAMES = [
    # 烟草行业常见姓名
    "张烟华", "李叶青", "王丝香", "陈烟锋", "刘香叶", "黄金叶", "林烟云", "周香华",
    "吴叶春", "徐烟辉", "朱香玲", "马烟峰", "胡叶明", "郭香涛", "何烟杰", "高叶军",
    "孙香宇", "邓烟强", "冯叶磊", "曹香斌", "彭烟龙", "苗叶飞", "鲁香鹏", "韩烟波",
    "蒋叶超", "谢香博", "崔烟涛", "杨叶宁", "钟香毅", "汪烟晨", "田叶昊", "任香凯",
    "姜烟帆", "石叶翔", "熊香磊", "金烟浩", "戴叶轩", "夏香勇", "方烟臻", "侯叶峻"
]

def generate_tobacco_skills() -> List[str]:
    """生成烟草行业相关技能"""
    technical_skills = [
        "烟叶分级", "卷烟工艺", "香精香料", "质量检测", "感官评吸",
        "设备维护", "自动化控制", "包装设计", "品牌管理", "市场营销",
        "供应链管理", "质量管理", "合规管理", "成本控制", "数据分析"
    ]
    
    soft_skills = [
        "团队协作", "沟通表达", "问题解决", "创新思维", "学习能力",
        "压力管理", "时间管理", "领导力", "客户服务", "项目管理"
    ]
    
    # 随机选择3-6个技能
    selected_technical = random.sample(technical_skills, random.randint(2, 4))
    selected_soft = random.sample(soft_skills, random.randint(1, 3))
    
    return selected_technical + selected_soft

def calculate_dci_score(experience_years: int, education_level: str, skills: List[str]) -> float:
    """计算DCI分数"""
    # 基础分数
    base_score = 60.0
    
    # 经验加分
    experience_bonus = min(experience_years * 2, 20)  # 最多20分
    
    # 教育加分
    education_bonus = {
        "高中": 0,
        "大专": 5,
        "本科": 10,
        "硕士": 15,
        "博士": 20
    }.get(education_level, 0)
    
    # 技能加分
    skill_bonus = min(len(skills) * 1.5, 15)  # 最多15分
    
    # 随机波动
    random_factor = random.uniform(-5, 5)
    
    total_score = base_score + experience_bonus + education_bonus + skill_bonus + random_factor
    return round(min(max(total_score, 30), 95), 1)

async def generate_tobacco_positions(session: AsyncSession) -> List[Position]:
    """生成烟草行业职位"""
    positions = []
    
    for i, pos_data in enumerate(TOBACCO_POSITIONS):
        company = random.choice(TOBACCO_COMPANIES)
        
        # 根据级别设定薪资范围
        salary_ranges = {
            "初级": (8000, 15000),
            "中级": (15000, 25000),
            "高级": (25000, 40000),
            "总监": (35000, 60000)
        }
        
        min_salary, max_salary = salary_ranges.get(pos_data["level"], (10000, 20000))
        
        position = Position(
            id=generate_snowflake_id(),
            title=pos_data["title"],
            company=company,
            department=pos_data["department"],
            location=random.choice(["北京", "上海", "云南昆明", "湖南长沙", "河南郑州", "安徽合肥"]),
            employment_type="全职",
            salary_min=min_salary,
            salary_max=max_salary,
            experience_required=random.choice(["1-3年", "3-5年", "5-8年", "8年以上"]),
            education_required=random.choice(["大专", "本科", "硕士"]),
            skills_required=pos_data["skills"],
            description=f"负责{pos_data['title']}相关工作，要求具备{pos_data['department']}专业经验。",
            requirements=f"1. 具备{pos_data['level']}水平的专业能力\n2. 熟悉烟草行业相关知识\n3. 有相关工作经验优先",
            benefits=["五险一金", "年终奖金", "带薪年假", "培训发展", "餐补交通"],
            status="active",
            created_by_id=1,  # 假设管理员ID为1
            is_deleted=False
        )
        
        positions.append(position)
        session.add(position)
    
    await session.commit()
    print(f"✅ 已生成 {len(positions)} 个烟草行业职位")
    return positions

async def generate_tobacco_candidates(session: AsyncSession, positions: List[Position]) -> List[Candidate]:
    """生成烟草行业候选人"""
    candidates = []
    
    # 生成50个候选人
    for i in range(50):
        name = random.choice(CHINESE_NAMES)
        
        # 生成邮箱和手机
        email = f"{name.lower().replace(' ', '').encode('ascii', 'ignore').decode()}_{i+1}@tobacco-talent.com"
        phone = f"1{random.randint(30, 89):02d}{random.randint(1000, 9999):04d}{random.randint(1000, 9999):04d}"
        
        # 生成经验和教育
        experience_years = random.randint(1, 15)
        education_level = random.choice(["大专", "本科", "硕士", "博士"])
        
        # 生成技能
        skills = generate_tobacco_skills()
        
        # 生成创建时间（最近30天）
        days_ago = random.randint(0, 30)
        created_at = datetime.now(timezone.utc) - timedelta(days=days_ago)
        
        candidate = Candidate(
            id=generate_snowflake_id(),
            name=name,
            email=email,
            phone=phone,
            position=random.choice(["烟叶技术员", "生产操作员", "质量检测员", "销售代表", "设备维护工", "研发助理"]),
            company=random.choice(TOBACCO_COMPANIES[:10]),  # 当前/上一家公司
            experience_years=experience_years,
            education_level=education_level,
            major=random.choice(["农学", "化学工程", "机械工程", "市场营销", "工商管理", "食品科学"]),
            skills=skills,
            salary_expectation=random.randint(8000, 35000),
            location_preference=random.choice(["北京", "上海", "云南", "湖南", "河南", "安徽"]),
            status=random.choice(["active", "interviewing", "offered", "inactive"]),
            notes=f"具有{experience_years}年烟草行业经验，专业技能：{', '.join(skills[:3])}",
            resume_url=f"/resumes/tobacco_{i+1}.pdf",
            linkedin_url=f"https://linkedin.com/in/tobacco-{i+1}",
            created_at=created_at,
            updated_at=created_at,
            is_deleted=False
        )
        
        candidates.append(candidate)
        session.add(candidate)
    
    await session.commit()
    print(f"✅ 已生成 {len(candidates)} 个烟草行业候选人")
    return candidates

async def generate_tobacco_assessments(session: AsyncSession, candidates: List[Candidate]) -> List[CandidateAssessment]:
    """生成烟草行业候选人评估"""
    assessments = []
    
    # 为70%的候选人生成评估
    assessed_candidates = random.sample(candidates, int(len(candidates) * 0.7))
    
    for candidate in assessed_candidates:
        # 根据候选人经验和教育生成评估分数
        experience_bonus = min(candidate.experience_years * 2, 20)
        education_bonus = {"高中": 0, "大专": 5, "本科": 10, "硕士": 15, "博士": 20}.get(candidate.education_level, 0)
        
        # 五维评估 (数字素养、行业技能、岗位技能、创新能力、学习潜力)
        digital_literacy = max(60, min(95, 70 + experience_bonus/2 + random.randint(-10, 10)))
        industry_skills = max(65, min(98, 75 + experience_bonus/1.5 + education_bonus/2 + random.randint(-8, 12)))
        position_skills = max(60, min(95, 70 + experience_bonus/1.2 + len(candidate.skills) * 2 + random.randint(-15, 15)))
        innovation_ability = max(55, min(90, 65 + education_bonus/1.5 + random.randint(-10, 15)))
        learning_potential = max(60, min(95, 70 + education_bonus/2 + random.randint(-12, 18)))
        
        # 计算DCI分数 (加权平均)
        dci_score = (
            digital_literacy * 0.20 +
            industry_skills * 0.25 +
            position_skills * 0.30 +
            innovation_ability * 0.15 +
            learning_potential * 0.10
        )
        
        # 生成评估时间（候选人创建后1-7天）
        assessed_at = candidate.created_at + timedelta(days=random.randint(1, 7))
        
        assessment = CandidateAssessment(
            id=generate_snowflake_id(),
            candidate_id=candidate.id,
            digital_literacy_score=round(digital_literacy, 1),
            industry_skills_score=round(industry_skills, 1),
            position_skills_score=round(position_skills, 1),
            innovation_ability_score=round(innovation_ability, 1),
            learning_potential_score=round(learning_potential, 1),
            dci_score=round(dci_score, 1),
            jfs_score=round(dci_score + random.uniform(-5, 5), 1),  # JFS与DCI相关但略有差异
            percentile_rank=random.randint(20, 95),
            assessment_type="comprehensive",
            status="completed",
            notes=f"烟草行业专业评估：在{candidate.major}专业背景下，具备{candidate.experience_years}年相关经验",
            assessed_at=assessed_at,
            created_at=assessed_at,
            is_deleted=False
        )
        
        assessments.append(assessment)
        session.add(assessment)
    
    await session.commit()
    print(f"✅ 已生成 {len(assessments)} 个烟草行业评估记录")
    return assessments

async def main():
    """主函数"""
    print("🚀 开始生成烟草行业演示数据...")
    
    # 创建数据库连接
    DATABASE_URL = settings.ASYNC_DATABASE_URL
    engine = create_async_engine(DATABASE_URL)
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as session:
        try:
            # 1. 生成职位
            print("\n📋 生成烟草行业职位...")
            positions = await generate_tobacco_positions(session)
            
            # 2. 生成候选人
            print("\n👥 生成烟草行业候选人...")
            candidates = await generate_tobacco_candidates(session, positions)
            
            # 3. 生成评估
            print("\n📊 生成烟草行业评估...")
            assessments = await generate_tobacco_assessments(session, candidates)
            
            print(f"\n✅ 烟草行业演示数据生成完成!")
            print(f"   - 职位数量: {len(positions)}")
            print(f"   - 候选人数量: {len(candidates)}")
            print(f"   - 评估数量: {len(assessments)}")
            print(f"   - 平均DCI分数: {sum(a.dci_score for a in assessments)/len(assessments):.1f}")
            
        except Exception as e:
            print(f"❌ 生成数据时出错: {e}")
            await session.rollback()
            raise
        finally:
            await engine.dispose()

if __name__ == "__main__":
    asyncio.run(main())