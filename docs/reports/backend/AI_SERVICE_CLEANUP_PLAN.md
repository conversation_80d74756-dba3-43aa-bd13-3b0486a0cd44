# AI服务代码清理计划

## 现状分析

### 代码重复统计
- **总代码行数**: 3,007行
- **重复/冗余代码**: ~1,100行 (36%)
- **实际需要代码**: ~1,900行

### 文件功能分析

| 文件名 | 行数 | 状态 | 依赖情况 | 建议 |
|--------|------|------|---------|------|
| `ai_service_manager.py` | 1,292 | ✅ 主要 | 新的统一管理器 | **保留** |
| `health.py` | 383 | ⚠️ 使用中 | monitoring, API | **保留并修改** |
| `health_check_service.py` | 232 | ❌ 重复 | API端点使用 | **删除** |
| `llm_service.py` | 554 | ❌ 冗余 | 仅测试使用 | **归档** |
| `llm_service_refactored.py` | 397 | ❌ 冗余 | 仅测试使用 | **归档** |
| `llm_provider.py` | 149 | ❌ 冗余 | refactored使用 | **归档** |

## 问题清单

### 1. 健康检查服务冲突
- **问题**: 两个 `HealthCheckService` 类，功能重叠
- **影响**: 
  - `health.py` → monitoring.py, __init__.py
  - `health_check_service.py` → api/v1/health.py
- **解决**: 统一使用 `health.py`，删除 `health_check_service.py`

### 2. LLM服务三重实现
- **问题**: 三个文件做同样的事情
- **影响**: 维护困难，不知道用哪个
- **解决**: 全部迁移到 `ai_service_manager.py`

### 3. 测试文件引用混乱
- **问题**: 测试文件引用不同的服务实现
- **影响**: 测试可能失败或不一致
- **解决**: 统一更新为使用 `ai_service_manager`

## 清理步骤

### Phase 1: 准备工作
```bash
# 1. 创建归档目录
mkdir -p archive/backend/deprecated_services

# 2. 备份当前状态
cp -r app/backend/app/services archive/backend/services_backup_$(date +%Y%m%d)
```

### Phase 2: 健康检查整合
1. **修改 `health.py`**：
   ```python
   # 将 LLM 健康检查改为使用 ai_service_manager
   from app.services.ai_service_manager import ai_service_manager
   
   async def check_llm_provider(self):
       return await ai_service_manager.check_all_providers_health()
   ```

2. **更新 API 端点**：
   ```python
   # app/api/v1/health.py
   from app.services.health import health_service  # 改为使用 health.py
   ```

3. **删除重复文件**：
   ```bash
   mv app/backend/app/services/health_check_service.py archive/backend/deprecated_services/
   ```

### Phase 3: LLM服务清理
1. **归档冗余文件**：
   ```bash
   mv app/backend/app/services/llm_service.py archive/backend/deprecated_services/
   mv app/backend/app/services/llm_service_refactored.py archive/backend/deprecated_services/
   mv app/backend/app/services/llm_provider.py archive/backend/deprecated_services/
   ```

2. **创建兼容层（临时）**：
   ```python
   # app/backend/app/services/llm_service.py (新的兼容文件)
   """LLM Service compatibility layer - redirects to ai_service_manager"""
   from app.services.ai_service_manager import ai_service_manager
   
   class LLMService:
       def __init__(self):
           self.ai_manager = ai_service_manager
       
       async def complete(self, prompt, **kwargs):
           return await self.ai_manager.generate_text(prompt, **kwargs)
   ```

### Phase 4: 测试更新
1. 更新所有测试文件引用
2. 运行测试确保没有破坏
3. 删除兼容层

## 最终架构

```
app/services/
├── ai_service_manager.py  # 统一的AI服务管理（LLM、Embedding、Rerank）
├── health.py              # 基础设施健康检查（DB、Redis、MinIO、AI）
├── vector_service.py      # 向量服务（使用ai_service_manager）
├── resume_parser.py       # 简历解析（使用ai_service_manager）
└── [其他业务服务]
```

## 收益

1. **代码减少**: 3,007行 → ~1,900行 (-36%)
2. **维护简化**: 单一入口，职责清晰
3. **性能提升**: 统一的缓存和连接管理
4. **测试简化**: 一致的接口和行为

## 风险与缓解

| 风险 | 影响 | 缓解措施 |
|-----|------|---------|
| 测试失败 | 高 | 创建兼容层，逐步迁移 |
| API中断 | 中 | 先更新引用，再删除文件 |
| 功能遗漏 | 低 | 详细对比功能，确保覆盖 |

## 执行时间表

- **Day 1**: 备份，创建计划
- **Day 2**: 健康检查整合
- **Day 3**: LLM服务迁移
- **Day 4**: 测试修复
- **Day 5**: 清理和验证

## 验证检查清单

- [ ] 所有测试通过
- [ ] API端点正常工作
- [ ] 健康检查功能完整
- [ ] LLM调用正常
- [ ] 监控服务正常
- [ ] 文档更新完成