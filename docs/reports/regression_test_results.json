{"timestamp": "2025-08-27T16:13:05.397972", "total_tests": 9, "passed": 3, "failed": 6, "skipped": 0, "results": [{"test_name": "Health Endpoints", "category": "Smoke Test", "status": "PASS", "message": "All health endpoints responding", "duration": 0.006325244903564453, "details": null}, {"test_name": "<PERSON> Authentication", "category": "Smoke Test", "status": "FAIL", "message": "Auth failed: Auth failed: 500", "duration": 0.01352834701538086, "details": null}, {"test_name": "Service Import Validation", "category": "Service Integration", "status": "FAIL", "message": "Failed imports: assessment_service: No module named 'redis', embedding_service: No module named 'redis', resume_parser: No module named 'redis', storage_service: No module named 'redis', monitoring: No module named 'redis', permission_service: No module named 'redis'", "duration": 0.008117198944091797, "details": null}, {"test_name": "Backward Compatibility", "category": "Service Integration", "status": "FAIL", "message": "Broken compatibility: role.py: No module named 'redis', permission.py: No module named 'redis'", "duration": 0.0007030963897705078, "details": null}, {"test_name": "Authentication Endpoint", "category": "API Endpoint", "status": "FAIL", "message": "GET /auth/me failed with 500", "duration": 0.005997657775878906, "details": null}, {"test_name": "User Management Endpoint", "category": "API Endpoint", "status": "FAIL", "message": "GET /users/ failed with 500", "duration": 0.006018638610839844, "details": null}, {"test_name": "Candidate Management Endpoint", "category": "API Endpoint", "status": "FAIL", "message": "GET /candidates/ failed with 500", "duration": 0.005400419235229492, "details": null}, {"test_name": "Health Check Endpoint", "category": "API Endpoint", "status": "PASS", "message": "GET /health responds with 200", "duration": 0.001905202865600586, "details": null}, {"test_name": "API Response Time", "category": "Performance", "status": "PASS", "message": "Good performance: avg=1.6ms, max=1.9ms", "duration": 0.008117914199829102, "details": {"response_times": [1.5146732330322266, 1.710653305053711, 1.8930435180664062, 1.4567375183105469, 1.5292167663574219]}}]}