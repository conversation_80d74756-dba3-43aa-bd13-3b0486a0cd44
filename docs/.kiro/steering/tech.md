# TalentForge Pro - Technology Stack

## Core Technology Stack

### Backend
- **Language**: Python 3.12+
- **Framework**: FastAPI 0.110+
- **ORM**: SQLAlchemy 2.0+ with asyncio support
- **Database**: PostgreSQL 17 (primary), Redis 7.4 (cache)
- **Package Manager**: Poetry
- **Server**: uvicorn with async support

### Frontend
- **Framework**: Next.js 15.4.1 with App Router
- **Language**: TypeScript 5.x (strict mode)
- **UI Library**: Tailwind CSS + shadcn/ui components
- **State Management**: Redux Toolkit 2.x
- **Data Fetching**: TanStack React Query 5.x
- **Package Manager**: pnpm
- **Icons**: Lucide React

### Infrastructure
- **Containerization**: Docker + Docker Compose
- **Object Storage**: MinIO (S3-compatible)
- **Vector Database**: ChromaDB 1.0.15
- **Message Queue**: Celery with Redis backend
- **Monitoring**: Prometheus + Grafana
- **Proxy**: Nginx (unified proxy architecture)

### ML/AI Stack
- **Framework**: PyTorch 2.0+
- **NLP**: Transformers 4.36+
- **Models**: BERT+BiLSTM for matching, MLP for assessment
- **Base Model**: bert-base-chinese

## Build System & Commands

### Development Setup
```bash
# First-time setup
make setup              # Create .env and data directories
make up                 # Start all services (unified proxy mode)
make logs               # View real-time logs
make down               # Stop all services

# Quick access
make info               # Show project info and access points
make status             # Show service status
make health             # Check service health
```

### Service Management
```bash
# Individual services
make up-postgres        # Start PostgreSQL
make up-redis          # Start Redis
make up-backend        # Start FastAPI backend
make up-frontend       # Start Next.js frontend
make up-nginx          # Start Nginx proxy

# Development tools
make shell-backend     # Open backend container shell
make shell-frontend    # Open frontend container shell
make shell-postgres    # Open PostgreSQL shell
```

### Database Operations
```bash
make db-init           # Initialize database with migrations
make db-migrate        # Run database migrations
make db-reset          # Reset database (WARNING: destroys data)
```

### Frontend Development
```bash
# Package management (Docker environment)
make frontend-add PACKAGES="package1 package2"
make frontend-remove PACKAGES="package1"
make frontend-deps     # Update dependencies
make frontend-restart  # Restart frontend service
```

### Testing & Validation
```bash
make test              # Run all tests
make test-coverage     # Run tests with coverage
make validate          # Validate project setup
```

### Build & Deployment
```bash
make build             # Build all Docker images
make build-backend     # Build backend only
make build-frontend    # Build frontend only
make clean             # Clean Docker resources
```

## Access Points

### Development Environment
- **Unified Proxy** (recommended): http://localhost:8088
- **API Documentation**: http://localhost:8088/docs
- **MinIO Console**: http://localhost:9001 (admin/Pass1234)
- **Prometheus**: http://localhost:9090

### Default Credentials
- **Application**: <EMAIL> / test123
- **MinIO**: admin / Pass1234
- **Redis**: Pass1234

## Architecture Patterns

### Backend Patterns
- **Layered Architecture**: API → Service → CRUD → Model
- **Async Programming**: All I/O operations use asyncio
- **Dependency Injection**: FastAPI Depends pattern
- **Type Safety**: Pydantic models for validation
- **Permission System**: RBAC with data visibility levels

### Frontend Patterns
- **Component Architecture**: Functional components with hooks
- **State Management**: Redux slices with RTK Query
- **Type Safety**: Strict TypeScript, avoid `any`
- **UI Consistency**: shadcn/ui component library
- **Performance**: React.memo, lazy loading, code splitting

### Data Persistence
- **Location**: `~/dev_data/hephaestus/`
- **Volumes**: postgres, redis, minio, chromadb, prometheus, nginx_logs
- **Cache**: pnpm store/cache for faster frontend builds

## Development Guidelines

### Code Quality
- **Backend**: Use ruff for linting, mypy for type checking
- **Frontend**: ESLint + Prettier, TypeScript strict mode
- **Testing**: pytest (backend), Jest (frontend), Playwright (e2e)
- **Coverage**: Minimum 80% for critical paths

### Performance Requirements
- **API Response**: < 200ms
- **First Load**: < 3 seconds
- **ML Inference**: < 200ms

### Security
- **Authentication**: JWT with refresh tokens
- **Authorization**: RBAC with permission checks
- **Data**: bcrypt password hashing, input validation
- **Transport**: HTTPS in production