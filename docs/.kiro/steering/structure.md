# TalentForge Pro - Project Structure

## Root Directory Organization

```
talent_forge_pro/
├── .claude/                    # Claude AI configuration
│   ├── agents/                 # Multi-agent definitions
│   ├── commands/               # Custom Claude commands
│   └── settings.local.json     # Claude permissions
├── .kiro/                      # Kiro AI steering rules
│   └── steering/               # Project guidance files
├── PRPs/                       # Product Requirements Prompts
│   └── templates/              # PRP templates
├── app/                        # Main application directory
├── docs/                       # Project documentation
├── examples/                   # Code examples and patterns
├── use-cases/                  # Specific use case implementations
├── Makefile                    # Unified build commands
├── CLAUDE.md                   # Main project guidelines
└── README.md                   # Project overview
```

## Application Structure (`app/`)

### Backend (`app/backend/`)
```
backend/
├── app/
│   ├── __init__.py
│   ├── main.py                 # FastAPI application entry
│   ├── api/                    # API layer
│   │   ├── __init__.py
│   │   ├── deps.py             # Dependencies (auth, db)
│   │   └── v1/                 # API version 1
│   │       ├── __init__.py
│   │       ├── auth.py         # Authentication endpoints
│   │       ├── users.py        # User management
│   │       └── candidates.py   # Candidate management
│   ├── core/                   # Core configuration
│   │   ├── __init__.py
│   │   ├── config.py           # Settings and configuration
│   │   ├── database.py         # Database connection
│   │   ├── security.py         # JWT and password handling
│   │   ├── permissions.py      # RBAC permission system
│   │   └── exceptions.py       # Custom exceptions
│   ├── models/                 # SQLAlchemy models
│   │   ├── __init__.py
│   │   ├── base.py             # Base model class
│   │   ├── user.py             # User model
│   │   ├── user_preference.py  # User preferences
│   │   └── candidate.py        # Candidate model
│   ├── schemas/                # Pydantic schemas
│   │   ├── __init__.py
│   │   ├── user.py             # User request/response schemas
│   │   ├── auth.py             # Authentication schemas
│   │   └── candidate.py        # Candidate schemas
│   ├── crud/                   # Data access layer
│   │   ├── __init__.py
│   │   ├── base.py             # Base CRUD operations
│   │   ├── user.py             # User CRUD operations
│   │   └── candidate.py        # Candidate CRUD operations
│   ├── services/               # Business logic layer
│   │   ├── __init__.py
│   │   ├── auth.py             # Authentication service
│   │   ├── user.py             # User service
│   │   └── candidate.py        # Candidate service
│   ├── tasks.py                # Celery tasks
│   └── worker.py               # Celery worker configuration
├── alembic/                    # Database migrations
│   ├── versions/               # Migration files
│   ├── env.py                  # Alembic environment
│   └── script.py.mako          # Migration template
├── scripts/                    # Utility scripts
│   ├── init_db.py              # Database initialization
│   ├── startup.sh              # Container startup script
│   └── celery_dev.py           # Celery development script
├── tests/                      # Test files
│   ├── __init__.py
│   ├── conftest.py             # Test configuration
│   ├── test_auth.py            # Authentication tests
│   └── test_users.py           # User tests
├── pyproject.toml              # Poetry dependencies
├── alembic.ini                 # Alembic configuration
└── Dockerfile                  # Backend container
```

### Frontend (`app/frontend/`)
```
frontend/
├── app/                        # Next.js App Router
│   ├── (auth)/                 # Auth route group
│   │   ├── login/              # Login page
│   │   └── register/           # Registration page
│   ├── (dashboard)/            # Dashboard route group
│   │   ├── users/              # User management
│   │   ├── candidates/         # Candidate management
│   │   └── reports/            # Reports and analytics
│   ├── [locale]/               # Internationalization routes
│   ├── i18n/                   # i18n configuration
│   │   ├── client.tsx          # Client-side i18n
│   │   ├── server.ts           # Server-side i18n
│   │   └── routing.ts          # Route configuration
│   ├── globals.css             # Global styles
│   ├── layout.tsx              # Root layout
│   ├── page.tsx                # Home page
│   └── providers.tsx           # Context providers
├── components/                 # React components
│   ├── ui/                     # shadcn/ui components
│   │   ├── button.tsx
│   │   ├── dialog.tsx
│   │   ├── form.tsx
│   │   └── ...
│   ├── auth/                   # Authentication components
│   │   ├── LoginForm.tsx
│   │   └── RegisterForm.tsx
│   ├── candidates/             # Candidate components
│   │   ├── CandidateList.tsx
│   │   └── CandidateForm.tsx
│   ├── layout/                 # Layout components
│   │   ├── Header.tsx
│   │   ├── Sidebar.tsx
│   │   └── Footer.tsx
│   ├── LanguageSwitcher.tsx    # Language switching
│   └── ThemeToggle.tsx         # Theme switching
├── services/                   # API services
│   ├── auth.ts                 # Authentication API
│   ├── user.ts                 # User API
│   └── candidate.ts            # Candidate API
├── stores/                     # Redux store
│   ├── index.ts                # Store configuration
│   ├── authSlice.ts            # Authentication state
│   └── candidateSlice.ts       # Candidate state
├── hooks/                      # Custom React hooks
│   ├── useAuth.tsx             # Authentication hook
│   └── usePermission.ts        # Permission hook
├── lib/                        # Utility libraries
│   ├── api/                    # API client configuration
│   ├── utils.ts                # General utilities
│   └── date-localization.service.ts
├── messages/                   # i18n translation files
│   ├── en.json                 # English translations
│   └── zh.json                 # Chinese translations
├── types/                      # TypeScript type definitions
│   ├── index.ts                # Main type exports
│   ├── enums.ts                # Enum definitions
│   └── global.d.ts             # Global type declarations
├── __tests__/                  # Test files
│   ├── components/             # Component tests
│   ├── services/               # Service tests
│   └── utils/                  # Utility tests
├── package.json                # Dependencies and scripts
├── next.config.ts              # Next.js configuration
├── tailwind.config.ts          # Tailwind CSS configuration
├── tsconfig.json               # TypeScript configuration
└── Dockerfile                  # Frontend container
```

## Documentation Structure (`docs/`)

```
docs/
├── 01_需求规格说明书.md         # Requirements specification
├── 02_技术架构设计文档.md       # Technical architecture
├── 03_API设计文档.md           # API design document
├── 04_任务分解与开发指南.md     # Development guide
├── 05_LLM开发提示词文档.md     # LLM development prompts
├── auto/                       # Auto-generated documentation
│   ├── BACKEND_DEVELOPMENT_GUIDELINES.md
│   ├── DOCKER_SETUP.md
│   ├── NGINX_SETUP.md
│   └── ...
├── reports/                    # Test and validation reports
└── source_doc/                 # Source documentation
```

## Scripts Structure (`app/scripts/`)

```
scripts/
├── development/                # Development scripts
│   ├── start-dev.sh           # Start development environment
│   ├── start-unified.sh       # Start with unified proxy
│   ├── stop-dev.sh            # Stop development environment
│   └── logs.sh                # View logs
├── docker/                    # Docker management scripts
│   ├── rebuild.sh             # Rebuild containers
│   └── verify_setup.sh        # Verify Docker setup
├── test/                      # Testing scripts
│   ├── run_coverage.py        # Coverage reports
│   └── comprehensive_api_test.py
└── validation/                # Validation scripts
    └── validate_setup.sh      # Validate project setup
```

## Key Conventions

### File Naming
- **Python**: snake_case for files and functions
- **TypeScript**: PascalCase for components, camelCase for utilities
- **Directories**: kebab-case or snake_case consistently

### Import Patterns
- **Backend**: Absolute imports from `app.` root
- **Frontend**: Relative imports with `@/` alias for app root
- **CRUD**: Import instances directly: `from app.crud import user as user_crud`

### Configuration Files
- **Environment**: `.env` files for environment-specific settings
- **Docker**: `docker-compose.yml` + `docker-compose.override.yml`
- **Package Management**: `pyproject.toml` (backend), `package.json` (frontend)

### Data Persistence
- **Location**: `~/dev_data/hephaestus/`
- **Structure**: Separate directories for each service (postgres, redis, minio, etc.)
- **Volumes**: Named Docker volumes for production, bind mounts for development