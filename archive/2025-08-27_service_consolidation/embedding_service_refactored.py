"""
Embedding Service (Refactored)
使用统一的AIServiceManager管理embedding生成

Author: TalentForge Pro Team
Date: 2025-08-11
"""
import logging
import numpy as np
from typing import List, Optional, Dict, Any
import asyncio
import hashlib
import json

from app.core.ai_config import ai_settings
from app.core.redis import get_redis
from app.services.ai_service_manager import ai_service_manager, AIServiceType
from app.schemas.matching import (
    EmbeddingRequest,
    EmbeddingResponse,
    BatchEmbeddingRequest
)

logger = logging.getLogger(__name__)


class EmbeddingServiceRefactored:
    """重构后的Embedding服务，使用统一的AI服务管理器"""
    
    def __init__(self):
        """初始化embedding服务"""
        # 使用AI服务管理器，不再需要初始化各种客户端！
        self.ai_manager = ai_service_manager
        
        # 缓存配置
        self.cache_ttl = ai_settings.EMBEDDING_CACHE_TTL
        self.batch_size = ai_settings.EMBEDDING_BATCH_SIZE
        
        logger.info("EmbeddingServiceRefactored initialized with AIServiceManager")
    
    async def generate_embedding(
        self,
        request: EmbeddingRequest
    ) -> EmbeddingResponse:
        """
        生成单个文本的embedding
        
        Args:
            request: Embedding请求
            
        Returns:
            Embedding响应
        """
        # 检查缓存
        cache_key = self._get_cache_key(request.text, request.model, request.provider)
        cached = await self._get_cached_embedding(cache_key)
        if cached:
            logger.debug(f"Cache hit for embedding: {cache_key[:20]}...")
            return EmbeddingResponse(
                embedding=cached,
                model=request.model,
                provider=request.provider,
                dimension=len(cached)
            )
        
        # 生成新的embedding
        embedding = await self._generate_with_provider(
            request.text,
            request.provider,
            request.model
        )
        
        # 缓存结果
        await self._cache_embedding(cache_key, embedding)
        
        return EmbeddingResponse(
            embedding=embedding,
            model=request.model or "default",
            provider=request.provider or ai_settings.EMBEDDING_PROVIDER,
            dimension=len(embedding)
        )
    
    async def batch_generate(
        self,
        request: BatchEmbeddingRequest
    ) -> List[EmbeddingResponse]:
        """
        批量生成embeddings
        
        Args:
            request: 批量embedding请求
            
        Returns:
            Embedding响应列表
        """
        results = []
        
        # 分批处理
        for i in range(0, len(request.texts), self.batch_size):
            batch = request.texts[i:i + self.batch_size]
            batch_results = await asyncio.gather(
                *[self.generate_embedding(
                    EmbeddingRequest(
                        text=text,
                        model=request.model,
                        provider=request.provider
                    )
                ) for text in batch]
            )
            results.extend(batch_results)
        
        return results
    
    async def _generate_with_provider(
        self,
        text: str,
        provider: Optional[str] = None,
        model: Optional[str] = None
    ) -> List[float]:
        """
        使用指定provider生成embedding
        
        Args:
            text: 要嵌入的文本
            provider: provider名称
            model: 模型名称
            
        Returns:
            Embedding向量
        """
        try:
            # 获取embedding客户端 - 使用统一的管理器！
            client, config = self.ai_manager.get_embedding_client(provider)
            
            # 根据provider类型调用相应的API
            actual_provider = provider or ai_settings.EMBEDDING_PROVIDER
            
            if actual_provider.lower() == "ollama":
                # Ollama使用同步客户端
                response = await asyncio.to_thread(
                    client.embeddings,
                    model=model or config["embedding_model"],
                    prompt=text
                )
                return response['embedding']
            else:
                # OpenAI兼容的API (DeepSeek, Moonshot, OpenRouter等)
                response = await client.embeddings.create(
                    model=model or config["embedding_model"],
                    input=text
                )
                return response.data[0].embedding
                
        except Exception as e:
            logger.error(f"Failed to generate embedding with {provider}: {e}")
            
            # 尝试降级到其他provider
            fallback_provider = await self.ai_manager.get_fallback_provider(
                AIServiceType.EMBEDDING,
                exclude_providers=[provider] if provider else []
            )
            
            if fallback_provider:
                logger.info(f"Retrying with fallback provider: {fallback_provider}")
                return await self._generate_with_provider(text, fallback_provider, model)
            
            # 如果所有provider都失败，使用哈希降级
            logger.warning("All embedding providers failed, using hash fallback")
            return self._hash_fallback_embedding(text)
    
    def _hash_fallback_embedding(self, text: str) -> List[float]:
        """
        哈希降级方案，当所有provider都不可用时使用
        
        Args:
            text: 输入文本
            
        Returns:
            基于哈希的伪embedding
        """
        # 使用多个哈希函数生成固定维度的向量
        dimension = ai_settings.VECTOR_DIMENSION
        embeddings = []
        
        for i in range(dimension):
            # 使用不同的salt进行哈希
            hash_input = f"{text}_{i}".encode('utf-8')
            hash_value = hashlib.sha256(hash_input).hexdigest()
            # 将哈希值转换为-1到1之间的浮点数
            normalized = (int(hash_value[:8], 16) / 0xFFFFFFFF) * 2 - 1
            embeddings.append(normalized)
        
        # 归一化
        norm = np.linalg.norm(embeddings)
        if norm > 0:
            embeddings = [e / norm for e in embeddings]
        
        return embeddings
    
    def _get_cache_key(
        self,
        text: str,
        model: Optional[str],
        provider: Optional[str]
    ) -> str:
        """生成缓存键"""
        provider = provider or ai_settings.EMBEDDING_PROVIDER
        model = model or "default"
        text_hash = hashlib.md5(text.encode()).hexdigest()
        return f"embedding:{provider}:{model}:{text_hash}"
    
    async def _get_cached_embedding(self, key: str) -> Optional[List[float]]:
        """从缓存获取embedding"""
        try:
            redis = await get_redis()
            cached = await redis.get(key)
            if cached:
                return json.loads(cached)
        except Exception as e:
            logger.warning(f"Failed to get cached embedding: {e}")
        return None
    
    async def _cache_embedding(self, key: str, embedding: List[float]) -> None:
        """缓存embedding"""
        try:
            redis = await get_redis()
            await redis.setex(
                key,
                self.cache_ttl,
                json.dumps(embedding)
            )
        except Exception as e:
            logger.warning(f"Failed to cache embedding: {e}")
    
    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态信息
        """
        # 使用AI管理器的健康检查
        ai_health = await self.ai_manager.check_all_providers_health()
        
        # 只返回embedding相关的健康信息
        embedding_health = {
            "status": ai_health["status"],
            "timestamp": ai_health["timestamp"],
            "embedding_providers": {},
            "default_provider": ai_settings.EMBEDDING_PROVIDER
        }
        
        # 筛选embedding可用的provider
        for provider, status in ai_health["providers"].items():
            config = self.ai_manager.get_provider_config(provider)
            if config and config.get("embedding_model"):
                embedding_health["embedding_providers"][provider] = {
                    "status": status["status"],
                    "model": config["embedding_model"],
                    "dimension": config.get("dimension")
                }
        
        return embedding_health


# 创建服务实例
embedding_service_refactored = EmbeddingServiceRefactored()