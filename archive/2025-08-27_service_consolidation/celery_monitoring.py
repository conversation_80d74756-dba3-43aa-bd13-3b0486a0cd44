"""
Celery Task Monitoring Service
Provides comprehensive monitoring for Celery tasks, queues, and worker health
"""
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, asdict
from collections import defaultdict, deque
from enum import Enum

import redis
from celery import Celery
from celery.events.state import State
from celery.events import EventReceiver
from kombu import Connection

from app.core.config import settings
from app.core.exceptions import service_error
from app.worker import celery_app

logger = logging.getLogger(__name__)


class TaskStatus(str, Enum):
    """Task status enumeration"""
    PENDING = "pending"
    STARTED = "started"
    SUCCESS = "success"
    FAILURE = "failure"
    RETRY = "retry"
    REVOKED = "revoked"


class QueuePriority(str, Enum):
    """Queue priority levels"""
    HIGH = "high"      # matching queue (priority 9)
    MEDIUM = "medium"  # parsing, assessment queues (priority 8, 7)
    LOW = "low"        # general queue (priority 10, but lower volume)


@dataclass
class TaskMetrics:
    """Task execution metrics"""
    task_name: str
    queue: str
    status: TaskStatus
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    execution_time: Optional[float] = None
    retries: int = 0
    error_message: Optional[str] = None
    worker: Optional[str] = None


@dataclass
class QueueMetrics:
    """Queue health metrics"""
    name: str
    priority: QueuePriority
    pending_tasks: int = 0
    active_tasks: int = 0
    completed_tasks_1h: int = 0
    failed_tasks_1h: int = 0
    avg_execution_time: float = 0.0
    max_execution_time: float = 0.0
    throughput_per_minute: float = 0.0
    error_rate: float = 0.0
    last_updated: datetime = None


@dataclass 
class WorkerMetrics:
    """Worker performance metrics"""
    worker_id: str
    hostname: str
    queues: List[str]
    active_tasks: int = 0
    completed_tasks_1h: int = 0
    failed_tasks_1h: int = 0
    avg_task_duration: float = 0.0
    memory_usage_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    last_heartbeat: Optional[datetime] = None
    is_online: bool = False


class CeleryMonitoringService:
    """Comprehensive Celery monitoring service"""
    
    def __init__(self):
        self.redis_client = redis.from_url(settings.redis_url_with_auth, decode_responses=True)
        self.celery_app = celery_app
        
        # In-memory metrics storage
        self.task_metrics = deque(maxlen=10000)  # Last 10k tasks
        self.queue_metrics = {}
        self.worker_metrics = {}
        
        # Monitoring configuration
        self.metrics_ttl = 3600  # 1 hour
        self.monitoring_interval = 30  # 30 seconds
        
        # Queue configurations
        self.queue_configs = {
            'general': QueuePriority.LOW,
            'parsing': QueuePriority.MEDIUM,
            'matching': QueuePriority.HIGH,
            'assessment': QueuePriority.MEDIUM
        }
        
        logger.info("Celery monitoring service initialized")
    
    async def get_overall_health(self) -> Dict[str, Any]:
        """Get overall system health metrics"""
        try:
            # Get active workers
            active_workers = await self._get_active_workers()
            
            # Get queue statistics
            queue_stats = await self._get_all_queue_stats()
            
            # Calculate system-wide metrics
            total_pending = sum(q.pending_tasks for q in queue_stats.values())
            total_active = sum(q.active_tasks for q in queue_stats.values())
            avg_error_rate = sum(q.error_rate for q in queue_stats.values()) / len(queue_stats) if queue_stats else 0
            
            # System health score (0-100)
            health_score = self._calculate_health_score(queue_stats, active_workers)
            
            # Get Redis connection info
            redis_info = await self._get_redis_info()
            
            return {
                "health_score": health_score,
                "status": self._determine_system_status(health_score),
                "timestamp": datetime.utcnow().isoformat(),
                "workers": {
                    "active": len(active_workers),
                    "total_capacity": sum(w.get('max_concurrency', 4) for w in active_workers),
                    "workers": active_workers
                },
                "tasks": {
                    "pending": total_pending,
                    "active": total_active,
                    "avg_error_rate": round(avg_error_rate, 3)
                },
                "queues": {name: asdict(metrics) for name, metrics in queue_stats.items()},
                "redis": redis_info,
                "performance_summary": await self._get_performance_summary()
            }
            
        except Exception as e:
            logger.error(f"Failed to get overall health: {str(e)}")
            raise service_error(f"MONITORING_HEALTH_CHECK_FAILED: {str(e)}")
    
    async def get_queue_metrics(self, queue_name: str) -> QueueMetrics:
        """Get detailed metrics for specific queue"""
        try:
            # Get queue length from Redis
            pending_tasks = await self._get_queue_length(queue_name)
            
            # Get task history for this queue
            recent_tasks = self._get_recent_tasks_by_queue(queue_name, hours=1)
            
            # Calculate metrics
            completed_tasks = len([t for t in recent_tasks if t.status == TaskStatus.SUCCESS])
            failed_tasks = len([t for t in recent_tasks if t.status == TaskStatus.FAILURE])
            
            execution_times = [t.execution_time for t in recent_tasks if t.execution_time is not None]
            avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
            max_execution_time = max(execution_times) if execution_times else 0
            
            # Calculate throughput (tasks per minute)
            throughput = completed_tasks  # Tasks completed in last hour (approximated per minute)
            
            # Calculate error rate
            total_tasks = completed_tasks + failed_tasks
            error_rate = (failed_tasks / total_tasks) * 100 if total_tasks > 0 else 0
            
            # Get active tasks for this queue
            active_tasks = await self._get_active_tasks_by_queue(queue_name)
            
            return QueueMetrics(
                name=queue_name,
                priority=self.queue_configs.get(queue_name, QueuePriority.LOW),
                pending_tasks=pending_tasks,
                active_tasks=len(active_tasks),
                completed_tasks_1h=completed_tasks,
                failed_tasks_1h=failed_tasks,
                avg_execution_time=round(avg_execution_time, 3),
                max_execution_time=round(max_execution_time, 3),
                throughput_per_minute=round(throughput / 60, 2),
                error_rate=round(error_rate, 2),
                last_updated=datetime.utcnow()
            )
            
        except Exception as e:
            logger.error(f"Failed to get queue metrics for {queue_name}: {str(e)}")
            raise service_error(f"MONITORING_QUEUE_METRICS_FAILED: {str(e)}")
    
    async def get_task_history(
        self, 
        hours: int = 24,
        queue: Optional[str] = None,
        status: Optional[TaskStatus] = None
    ) -> List[TaskMetrics]:
        """Get task execution history with optional filtering"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            tasks = []
            for task in self.task_metrics:
                if task.started_at and task.started_at >= cutoff_time:
                    if queue and task.queue != queue:
                        continue
                    if status and task.status != status:
                        continue
                    tasks.append(task)
            
            # Sort by most recent first
            tasks.sort(key=lambda t: t.started_at or datetime.min, reverse=True)
            
            return tasks
            
        except Exception as e:
            logger.error(f"Failed to get task history: {str(e)}")
            raise service_error(f"MONITORING_TASK_HISTORY_FAILED: {str(e)}")
    
    async def get_performance_analytics(self, queue_name: Optional[str] = None) -> Dict[str, Any]:
        """Get detailed performance analytics"""
        try:
            if queue_name:
                recent_tasks = self._get_recent_tasks_by_queue(queue_name, hours=24)
            else:
                recent_tasks = list(self.task_metrics)[-1000:]  # Last 1000 tasks
            
            if not recent_tasks:
                return {"message": "No recent task data available"}
            
            # Analyze execution times
            execution_times = [t.execution_time for t in recent_tasks if t.execution_time is not None]
            
            # Calculate percentiles
            execution_times_sorted = sorted(execution_times)
            n = len(execution_times_sorted)
            
            percentiles = {}
            if n > 0:
                percentiles = {
                    "p50": execution_times_sorted[int(n * 0.5)] if n > 0 else 0,
                    "p90": execution_times_sorted[int(n * 0.9)] if n > 0 else 0,
                    "p95": execution_times_sorted[int(n * 0.95)] if n > 0 else 0,
                    "p99": execution_times_sorted[int(n * 0.99)] if n > 0 else 0
                }
            
            # Task status distribution
            status_counts = defaultdict(int)
            for task in recent_tasks:
                status_counts[task.status.value] += 1
            
            # Error analysis
            failed_tasks = [t for t in recent_tasks if t.status == TaskStatus.FAILURE]
            error_patterns = defaultdict(int)
            for task in failed_tasks:
                if task.error_message:
                    # Extract error type (first word of error message)
                    error_type = task.error_message.split(':')[0] if ':' in task.error_message else task.error_message.split()[0]
                    error_patterns[error_type] += 1
            
            # Throughput analysis (tasks per hour over last 24 hours)
            hourly_throughput = defaultdict(int)
            for task in recent_tasks:
                if task.completed_at:
                    hour = task.completed_at.replace(minute=0, second=0, microsecond=0)
                    hourly_throughput[hour.isoformat()] += 1
            
            return {
                "queue_name": queue_name,
                "analysis_period": "24 hours",
                "task_count": len(recent_tasks),
                "execution_time_stats": {
                    "count": len(execution_times),
                    "avg": round(sum(execution_times) / len(execution_times), 3) if execution_times else 0,
                    "min": min(execution_times) if execution_times else 0,
                    "max": max(execution_times) if execution_times else 0,
                    "percentiles": percentiles
                },
                "status_distribution": dict(status_counts),
                "error_rate": round((status_counts['failure'] / len(recent_tasks)) * 100, 2) if recent_tasks else 0,
                "top_error_patterns": dict(sorted(error_patterns.items(), key=lambda x: x[1], reverse=True)[:10]),
                "hourly_throughput": dict(sorted(hourly_throughput.items())),
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get performance analytics: {str(e)}")
            raise service_error(f"MONITORING_ANALYTICS_FAILED: {str(e)}")
    
    async def record_task_event(
        self,
        task_id: str,
        event_type: str,
        task_name: str,
        queue: str,
        worker: Optional[str] = None,
        **kwargs
    ):
        """Record task execution event"""
        try:
            # Find existing task metrics or create new
            task_metric = None
            for metric in reversed(self.task_metrics):
                if hasattr(metric, 'task_id') and metric.task_id == task_id:
                    task_metric = metric
                    break
            
            if not task_metric:
                task_metric = TaskMetrics(
                    task_name=task_name,
                    queue=queue,
                    status=TaskStatus.PENDING
                )
                # Add task_id for tracking
                task_metric.task_id = task_id
                self.task_metrics.append(task_metric)
            
            # Update based on event type
            if event_type == 'task-started':
                task_metric.status = TaskStatus.STARTED
                task_metric.started_at = datetime.utcnow()
                task_metric.worker = worker
            
            elif event_type == 'task-succeeded':
                task_metric.status = TaskStatus.SUCCESS
                task_metric.completed_at = datetime.utcnow()
                if task_metric.started_at:
                    task_metric.execution_time = (task_metric.completed_at - task_metric.started_at).total_seconds()
            
            elif event_type == 'task-failed':
                task_metric.status = TaskStatus.FAILURE
                task_metric.completed_at = datetime.utcnow()
                task_metric.error_message = kwargs.get('exception', 'Unknown error')
                if task_metric.started_at:
                    task_metric.execution_time = (task_metric.completed_at - task_metric.started_at).total_seconds()
            
            elif event_type == 'task-retry':
                task_metric.status = TaskStatus.RETRY
                task_metric.retries += 1
            
            # Store in Redis for persistence
            await self._store_task_metric(task_id, task_metric)
            
        except Exception as e:
            logger.error(f"Failed to record task event: {str(e)}")
            # Don't raise exception to avoid disrupting task execution
    
    # Helper methods
    async def _get_active_workers(self) -> List[Dict[str, Any]]:
        """Get list of active Celery workers"""
        try:
            inspect = self.celery_app.control.inspect()
            stats = inspect.stats()
            
            workers = []
            if stats:
                for worker_name, worker_stats in stats.items():
                    workers.append({
                        "name": worker_name,
                        "status": "online",
                        "processed_tasks": worker_stats.get('total', {}),
                        "active_tasks": len(inspect.active().get(worker_name, [])),
                        "max_concurrency": worker_stats.get('pool', {}).get('max-concurrency', 4),
                        "current_load": worker_stats.get('rusage', {})
                    })
            
            return workers
            
        except Exception as e:
            logger.warning(f"Failed to get active workers: {str(e)}")
            return []
    
    async def _get_all_queue_stats(self) -> Dict[str, QueueMetrics]:
        """Get statistics for all queues"""
        stats = {}
        for queue_name in self.queue_configs.keys():
            try:
                stats[queue_name] = await self.get_queue_metrics(queue_name)
            except Exception as e:
                logger.error(f"Failed to get stats for queue {queue_name}: {str(e)}")
        
        return stats
    
    async def _get_queue_length(self, queue_name: str) -> int:
        """Get number of pending tasks in queue"""
        try:
            # Use Redis to get queue length
            queue_key = f"celery.{queue_name}"
            return self.redis_client.llen(queue_key)
        except Exception as e:
            logger.warning(f"Failed to get queue length for {queue_name}: {str(e)}")
            return 0
    
    async def _get_active_tasks_by_queue(self, queue_name: str) -> List[Dict[str, Any]]:
        """Get active tasks for specific queue"""
        try:
            inspect = self.celery_app.control.inspect()
            active_tasks = inspect.active()
            
            queue_tasks = []
            if active_tasks:
                for worker_name, tasks in active_tasks.items():
                    for task in tasks:
                        if task.get('delivery_info', {}).get('routing_key') == queue_name:
                            queue_tasks.append(task)
            
            return queue_tasks
            
        except Exception as e:
            logger.warning(f"Failed to get active tasks for queue {queue_name}: {str(e)}")
            return []
    
    def _get_recent_tasks_by_queue(self, queue_name: str, hours: int = 1) -> List[TaskMetrics]:
        """Get recent tasks for specific queue"""
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        return [
            task for task in self.task_metrics
            if task.queue == queue_name and 
               task.started_at and task.started_at >= cutoff_time
        ]
    
    async def _get_redis_info(self) -> Dict[str, Any]:
        """Get Redis server information"""
        try:
            info = self.redis_client.info()
            return {
                "version": info.get("redis_version"),
                "connected_clients": info.get("connected_clients"),
                "used_memory_mb": round(info.get("used_memory", 0) / 1024 / 1024, 2),
                "total_commands_processed": info.get("total_commands_processed"),
                "keyspace_hits": info.get("keyspace_hits"),
                "keyspace_misses": info.get("keyspace_misses")
            }
        except Exception as e:
            logger.warning(f"Failed to get Redis info: {str(e)}")
            return {"error": str(e)}
    
    def _calculate_health_score(self, queue_stats: Dict[str, QueueMetrics], workers: List[Dict[str, Any]]) -> int:
        """Calculate system health score (0-100)"""
        score = 100
        
        # Deduct points for high error rates
        for queue_metric in queue_stats.values():
            if queue_metric.error_rate > 10:  # >10% error rate
                score -= min(20, queue_metric.error_rate)
        
        # Deduct points for long queues
        total_pending = sum(q.pending_tasks for q in queue_stats.values())
        if total_pending > 100:
            score -= min(30, total_pending // 10)
        
        # Deduct points for no active workers
        if len(workers) == 0:
            score -= 50
        
        return max(0, score)
    
    def _determine_system_status(self, health_score: int) -> str:
        """Determine system status based on health score"""
        if health_score >= 90:
            return "healthy"
        elif health_score >= 70:
            return "warning"
        elif health_score >= 50:
            return "degraded"
        else:
            return "critical"
    
    async def _get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for last hour"""
        recent_tasks = list(self.task_metrics)[-500:]  # Last 500 tasks
        
        if not recent_tasks:
            return {"message": "No recent data"}
        
        completed_tasks = [t for t in recent_tasks if t.status == TaskStatus.SUCCESS]
        failed_tasks = [t for t in recent_tasks if t.status == TaskStatus.FAILURE]
        
        total_tasks = len(recent_tasks)
        success_rate = (len(completed_tasks) / total_tasks * 100) if total_tasks > 0 else 0
        
        execution_times = [t.execution_time for t in completed_tasks if t.execution_time is not None]
        avg_execution_time = sum(execution_times) / len(execution_times) if execution_times else 0
        
        return {
            "total_tasks_recent": total_tasks,
            "success_rate": round(success_rate, 2),
            "avg_execution_time": round(avg_execution_time, 3),
            "failed_tasks": len(failed_tasks)
        }
    
    async def _store_task_metric(self, task_id: str, metric: TaskMetrics):
        """Store task metric in Redis for persistence"""
        try:
            key = f"task_metric:{task_id}"
            data = asdict(metric)
            # Convert datetime objects to ISO strings
            if data.get('started_at'):
                data['started_at'] = data['started_at'].isoformat()
            if data.get('completed_at'):
                data['completed_at'] = data['completed_at'].isoformat()
            
            self.redis_client.setex(key, self.metrics_ttl, json.dumps(data))
        except Exception as e:
            logger.error(f"Failed to store task metric: {str(e)}")


# Global monitoring service instance
celery_monitoring = CeleryMonitoringService()