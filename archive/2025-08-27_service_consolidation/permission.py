"""
Permission service for managing system permissions
"""
from typing import Any, Dict, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.exceptions import ConflictError, NotFoundError, PermissionError
from app.crud.permission import permission_crud
from app.models.permission import Permission
from app.schemas.permission import (
    PermissionCreate,
    PermissionFilter,
    PermissionListResponse,
    PermissionResponse,
    PermissionUpdate,
)


class PermissionService:
    """Service for handling permission business logic"""

    async def get_permission(
        self,
        db: AsyncSession,
        permission_id: int
    ) -> PermissionResponse:
        """
        Get a permission by ID
        
        Args:
            db: Database session
            permission_id: Permission ID
            
        Returns:
            Permission response object
            
        Raises:
            NotFoundError: If permission not found
        """
        permission = await permission_crud.get(db, id=permission_id)
        if not permission:
            raise NotFoundError(f"Permission with id {permission_id} not found")

        return PermissionResponse.from_orm_with_str_id(permission)

    async def get_permission_by_code(
        self,
        db: AsyncSession,
        code: str
    ) -> PermissionResponse:
        """
        Get a permission by code
        
        Args:
            db: Database session
            code: Permission code
            
        Returns:
            Permission response object
            
        Raises:
            NotFoundError: If permission not found
        """
        permission = await permission_crud.get_by_code(db, code=code)
        if not permission:
            raise NotFoundError(f"Permission with code {code} not found")

        return PermissionResponse.from_orm_with_str_id(permission)

    async def list_permissions(
        self,
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        filters: PermissionFilter | None = None
    ) -> PermissionListResponse:
        """
        List permissions with filtering and pagination
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Optional filter parameters
            
        Returns:
            Paginated list of permissions
        """
        permissions = await permission_crud.get_multi_filtered(
            db,
            skip=skip,
            limit=limit,
            filters=filters
        )

        total = await permission_crud.count_filtered(db, filters=filters)

        items = [
            PermissionResponse.from_orm_with_str_id(permission)
            for permission in permissions
        ]

        return PermissionListResponse(
            items=items,
            total=total,
            skip=skip,
            limit=limit
        )

    async def create_permission(
        self,
        db: AsyncSession,
        permission_in: PermissionCreate
    ) -> PermissionResponse:
        """
        Create a new permission
        
        Args:
            db: Database session
            permission_in: Permission creation data
            
        Returns:
            Created permission
            
        Raises:
            ConflictError: If permission code already exists
        """
        # Check if permission code already exists
        existing = await permission_crud.get_by_code(db, code=permission_in.code)
        if existing:
            raise ConflictError(f"Permission with code {permission_in.code} already exists")

        permission = await permission_crud.create(db, obj_in=permission_in)
        return PermissionResponse.from_orm_with_str_id(permission)

    async def update_permission(
        self,
        db: AsyncSession,
        permission_id: int,
        permission_in: PermissionUpdate
    ) -> PermissionResponse:
        """
        Update a permission
        
        Args:
            db: Database session
            permission_id: Permission ID
            permission_in: Permission update data
            
        Returns:
            Updated permission
            
        Raises:
            NotFoundError: If permission not found
            PermissionError: If attempting to modify a system permission
            ConflictError: If new code already exists
        """
        permission = await permission_crud.get(db, id=permission_id)
        if not permission:
            raise NotFoundError(f"Permission with id {permission_id} not found")

        # Check if it's a system permission
        if permission.is_system:
            raise PermissionError("Cannot modify system permissions")

        # Check if new code conflicts with existing permission
        if permission_in.code and permission_in.code != permission.code:
            existing = await permission_crud.get_by_code(db, code=permission_in.code)
            if existing:
                raise ConflictError(f"Permission with code {permission_in.code} already exists")

        permission = await permission_crud.update(db, db_obj=permission, obj_in=permission_in)
        return PermissionResponse.from_orm_with_str_id(permission)

    async def delete_permission(
        self,
        db: AsyncSession,
        permission_id: int
    ) -> bool:
        """
        Delete a permission
        
        Args:
            db: Database session
            permission_id: Permission ID
            
        Returns:
            True if deleted successfully
            
        Raises:
            NotFoundError: If permission not found
            PermissionError: If attempting to delete a system permission
        """
        permission = await permission_crud.get(db, id=permission_id)
        if not permission:
            raise NotFoundError(f"Permission with id {permission_id} not found")

        # Check if it's a system permission
        if permission.is_system:
            raise PermissionError("Cannot delete system permissions")

        await permission_crud.remove(db, id=permission_id)
        return True

    async def bulk_create_permissions(
        self,
        db: AsyncSession,
        permissions: list[PermissionCreate]
    ) -> list[PermissionResponse]:
        """
        Create multiple permissions at once
        
        Args:
            db: Database session
            permissions: List of permissions to create
            
        Returns:
            List of created permissions
        """
        created = await permission_crud.bulk_create(db, permissions=permissions)
        return [
            PermissionResponse.from_orm_with_str_id(permission)
            for permission in created
        ]

    async def get_permissions_by_module(
        self,
        db: AsyncSession,
        module: str
    ) -> list[PermissionResponse]:
        """
        Get all permissions for a specific module
        
        Args:
            db: Database session
            module: Module name
            
        Returns:
            List of permissions for the module
        """
        permissions = await permission_crud.get_by_module(db, module=module)
        return [
            PermissionResponse.from_orm_with_str_id(permission)
            for permission in permissions
        ]

    async def get_active_permissions(
        self,
        db: AsyncSession
    ) -> list[PermissionResponse]:
        """
        Get all active permissions
        
        Args:
            db: Database session
            
        Returns:
            List of active permissions
        """
        permissions = await permission_crud.get_active_permissions(db)
        return [
            PermissionResponse.from_orm_with_str_id(permission)
            for permission in permissions
        ]

    async def group_permissions_by_module(
        self,
        db: AsyncSession
    ) -> dict[str, list[PermissionResponse]]:
        """
        Get permissions grouped by module
        
        Args:
            db: Database session
            
        Returns:
            Dictionary with module names as keys and permission lists as values
        """
        all_permissions = await permission_crud.get_active_permissions(db)

        grouped: dict[str, list[PermissionResponse]] = {}
        for permission in all_permissions:
            if permission.module not in grouped:
                grouped[permission.module] = []
            grouped[permission.module].append(
                PermissionResponse.from_orm_with_str_id(permission)
            )

        # Sort permissions within each module
        for module in grouped:
            grouped[module].sort(key=lambda p: (p.resource, p.action))

        return grouped

    async def check_permission_exists(
        self,
        db: AsyncSession,
        code: str
    ) -> bool:
        """
        Check if a permission exists by code
        
        Args:
            db: Database session
            code: Permission code
            
        Returns:
            True if permission exists
        """
        permission = await permission_crud.get_by_code(db, code=code)
        return permission is not None

    async def initialize_default_permissions(
        self,
        db: AsyncSession
    ) -> list[PermissionResponse]:
        """
        Initialize default system permissions
        
        Args:
            db: Database session
            
        Returns:
            List of created permissions
        """
        default_permissions = [
            # User management permissions
            PermissionCreate(
                code="users.view",
                name="View Users",
                module="users",
                resource="users",
                action="view",
                access_level="all",
                description="View user list and details",
                is_system=True
            ),
            PermissionCreate(
                code="users.create",
                name="Create Users",
                module="users",
                resource="users",
                action="create",
                access_level="all",
                description="Create new users",
                is_system=True
            ),
            PermissionCreate(
                code="users.update",
                name="Update Users",
                module="users",
                resource="users",
                action="update",
                access_level="all",
                description="Update user information",
                is_system=True
            ),
            PermissionCreate(
                code="users.delete",
                name="Delete Users",
                module="users",
                resource="users",
                action="delete",
                access_level="all",
                description="Delete users",
                is_system=True
            ),

            # Role management permissions
            PermissionCreate(
                code="roles.view",
                name="View Roles",
                module="roles",
                resource="roles",
                action="view",
                access_level="all",
                description="View role list and details",
                is_system=True
            ),
            PermissionCreate(
                code="roles.create",
                name="Create Roles",
                module="roles",
                resource="roles",
                action="create",
                access_level="all",
                description="Create new roles",
                is_system=True
            ),
            PermissionCreate(
                code="roles.update",
                name="Update Roles",
                module="roles",
                resource="roles",
                action="update",
                access_level="all",
                description="Update role information",
                is_system=True
            ),
            PermissionCreate(
                code="roles.delete",
                name="Delete Roles",
                module="roles",
                resource="roles",
                action="delete",
                access_level="all",
                description="Delete roles",
                is_system=True
            ),

            # Permission management permissions
            PermissionCreate(
                code="permissions.view",
                name="View Permissions",
                module="permissions",
                resource="permissions",
                action="view",
                access_level="all",
                description="View permission list and details",
                is_system=True
            ),
            PermissionCreate(
                code="permissions.manage",
                name="Manage Permissions",
                module="permissions",
                resource="permissions",
                action="manage",
                access_level="all",
                description="Create, update, and delete permissions",
                is_system=True
            ),

            # Candidate management permissions
            PermissionCreate(
                code="candidates.view",
                name="View Candidates",
                module="candidates",
                resource="candidates",
                action="view",
                access_level="all",
                description="View candidate list and details",
                is_system=True
            ),
            PermissionCreate(
                code="candidates.create",
                name="Create Candidates",
                module="candidates",
                resource="candidates",
                action="create",
                access_level="all",
                description="Create new candidates",
                is_system=True
            ),
            PermissionCreate(
                code="candidates.update",
                name="Update Candidates",
                module="candidates",
                resource="candidates",
                action="update",
                access_level="all",
                description="Update candidate information",
                is_system=True
            ),
            PermissionCreate(
                code="candidates.delete",
                name="Delete Candidates",
                module="candidates",
                resource="candidates",
                action="delete",
                access_level="all",
                description="Delete candidates",
                is_system=True
            ),

            # Position management permissions
            PermissionCreate(
                code="positions.view",
                name="View Positions",
                module="positions",
                resource="positions",
                action="view",
                access_level="all",
                description="View position list and details",
                is_system=True
            ),
            PermissionCreate(
                code="positions.create",
                name="Create Positions",
                module="positions",
                resource="positions",
                action="create",
                access_level="all",
                description="Create new positions",
                is_system=True
            ),
            PermissionCreate(
                code="positions.update",
                name="Update Positions",
                module="positions",
                resource="positions",
                action="update",
                access_level="all",
                description="Update position information",
                is_system=True
            ),
            PermissionCreate(
                code="positions.delete",
                name="Delete Positions",
                module="positions",
                resource="positions",
                action="delete",
                access_level="all",
                description="Delete positions",
                is_system=True
            ),

            # System monitoring permissions
            PermissionCreate(
                code="monitoring.view",
                name="View System Monitoring",
                module="monitoring",
                resource="monitoring",
                action="view",
                access_level="all",
                description="View system health and metrics",
                is_system=True
            ),

            # Settings permissions
            PermissionCreate(
                code="settings.view",
                name="View Settings",
                module="settings",
                resource="settings",
                action="view",
                access_level="all",
                description="View system settings",
                is_system=True
            ),
            PermissionCreate(
                code="settings.manage",
                name="Manage Settings",
                module="settings",
                resource="settings",
                action="manage",
                access_level="all",
                description="Modify system settings",
                is_system=True
            ),
        ]

        created_permissions = await self.bulk_create_permissions(db, default_permissions)
        return created_permissions


# Create a singleton instance
permission_service = PermissionService()
