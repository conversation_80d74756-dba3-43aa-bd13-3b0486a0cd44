"""
Enhanced Assessment Service with AI Integration
Migrated to use AIServiceManager for unified provider management
"""
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from uuid import UUID
import hashlib

from app.services.ai_service_manager import ai_service_manager
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from pydantic import ValidationError

from app.core.config import settings
from app.core.redis import redis_client
from app.models.candidate import Candidate
from app.models.position import Position
from app.schemas.assessment import (
    AssessmentCreateRequest,
    AssessmentResponse,
    DimensionScore,
    JFSCalculationRequest,
    JFSCalculationResponse,
    ComparisonRequest,
    ComparisonResponse,
    CandidateComparison,
    RecommendationRequest,
    RecommendationResponse
)

logger = logging.getLogger(__name__)


class EnhancedAssessmentService:
    """Enhanced assessment service with DeepSeek AI integration"""
    
    def __init__(self):
        """Initialize service with unified AI service manager"""
        self.cache_ttl = 3600  # 1 hour cache
        self.ai_manager = ai_service_manager
        logger.info(f"Enhanced Assessment Service initialized with AI Service Manager, provider: {self.ai_manager.llm_provider}")
    
    async def generate_assessment(
        self,
        request: AssessmentCreateRequest,
        db: AsyncSession
    ) -> AssessmentResponse:
        """Generate AI-powered assessment for candidate"""
        
        # Check cache first
        cache_key = f"assessment:{request.candidate_id}:{request.assessment_type}"
        cached = await self._get_cached(cache_key)
        if cached and not request.position_id:  # Only use cache if not comparing with position
            logger.info(f"Returning cached assessment for candidate {request.candidate_id}")
            return AssessmentResponse(**cached)
        
        # Fetch candidate data
        candidate = await db.get(Candidate, request.candidate_id)
        if not candidate:
            raise ValueError(f"Candidate {request.candidate_id} not found")
        
        # Generate assessment using AI provider
        if request.use_deepseek:
            assessment_data = await self._generate_with_ai(
                candidate, 
                request.assessment_type,
                request.include_ai_insights
            )
        else:
            # Use rule-based assessment if AI not requested
            assessment_data = await self._generate_rule_based(candidate)
        
        # Calculate JFS if position provided
        if request.position_id:
            position = await db.get(Position, request.position_id)
            if position:
                jfs_score = await self._calculate_jfs(
                    candidate, position, assessment_data['dimensions']
                )
                assessment_data['jfs_score'] = jfs_score
        
        # Create response
        response = AssessmentResponse(
            candidate_id=request.candidate_id,
            dimensions=assessment_data['dimensions'],
            dci_score=assessment_data['dci_score'],
            jfs_score=assessment_data.get('jfs_score'),
            percentile=assessment_data['percentile'],
            recommendations=assessment_data['recommendations'],
            strengths=assessment_data['strengths'],
            improvement_areas=assessment_data['improvement_areas'],
            radar_chart_data=assessment_data['radar_chart_data'],
            assessed_at=datetime.utcnow()
        )
        
        # Cache the result
        await self._set_cached(cache_key, response.model_dump(), self.cache_ttl)
        
        return response
    
    async def _generate_with_ai(
        self,
        candidate: Candidate,
        assessment_type: str,
        include_insights: bool
    ) -> Dict[str, Any]:
        """Generate assessment using AI via AIServiceManager"""
        
        # Prepare candidate profile
        profile = {
            "name": candidate.name,
            "email": candidate.email,
            "experience_years": getattr(candidate, 'experience_years', 0),
            "skills": getattr(candidate, 'skills', []),
            "education": getattr(candidate, 'education', ''),
            "resume_text": getattr(candidate, 'resume_text', '')
        }
        
        # Create assessment prompt
        prompt = self._create_assessment_prompt(profile, assessment_type)
        
        try:
            # Get AI client from manager
            client, config = self.ai_manager.get_llm_client()
            
            if not client:
                # Try fallback provider
                fallback_provider = await self.ai_manager.get_fallback_provider("llm")
                if fallback_provider:
                    client, config = self.ai_manager.get_llm_client(fallback_provider)
                    logger.info(f"Using fallback provider for assessment: {fallback_provider}")
                else:
                    logger.warning("No AI provider available, falling back to rule-based")
                    return await self._generate_rule_based(candidate)
            
            # Call AI API
            response = await client.chat.completions.create(
                model=config["llm_model"],
                messages=[
                    {
                        "role": "system",
                        "content": "你是一位专业的人才评估专家，擅长进行多维度能力评估和职业发展建议。"
                    },
                    {"role": "user", "content": prompt}
                ],
                temperature=config.get("temperature", 0.1),
                max_tokens=config.get("max_tokens", 2000)
            )
            
            # Parse response
            result_text = response.choices[0].message.content
            
            # Extract JSON from response
            import re
            json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
            if json_match:
                assessment_data = json.loads(json_match.group())
            else:
                # Fallback if JSON extraction fails
                assessment_data = self._parse_text_response(result_text)
            
            # Ensure all required fields
            return self._validate_assessment_data(assessment_data, include_insights)
            
        except Exception as e:
            logger.error(f"AI assessment generation failed: {str(e)}")
            # Try fallback provider
            fallback_provider = await self.ai_manager.get_fallback_provider("llm")
            if fallback_provider:
                try:
                    logger.info(f"Attempting with fallback provider: {fallback_provider}")
                    client, config = self.ai_manager.get_llm_client(fallback_provider)
                    if client:
                        # Simplified retry with fallback
                        return await self._generate_rule_based(candidate)
                except Exception as fallback_e:
                    logger.error(f"Fallback provider also failed: {fallback_e}")
            # Final fallback to rule-based
            return await self._generate_rule_based(candidate)
    
    def _create_assessment_prompt(self, profile: Dict, assessment_type: str) -> str:
        """Create prompt for AI assessment"""
        
        if assessment_type == "comprehensive":
            depth = "详细深入的"
            detail_level = "包括具体例证和改进建议"
        elif assessment_type == "deep":
            depth = "极其深入和全面的"
            detail_level = "包括详细分析、案例对比和发展路径"
        else:  # quick
            depth = "快速概要的"
            detail_level = "简洁明了"
        
        return f"""
        请对以下候选人进行{depth}五维度能力评估：
        
        候选人信息：
        - 姓名：{profile['name']}
        - 工作年限：{profile.get('experience_years', 0)}年
        - 技能：{', '.join(profile.get('skills', []))}
        - 教育背景：{profile.get('education', '未知')}
        - 简历摘要：{profile.get('resume_text', '')[:500]}
        
        请返回JSON格式的评估结果，{detail_level}：
        {{
            "digital_literacy_score": <0-100分>,
            "industry_skills_score": <0-100分>,
            "position_skills_score": <0-100分>,
            "innovation_score": <0-100分>,
            "learning_potential_score": <0-100分>,
            "dci_score": <综合得分0-100>,
            "percentile": <在所有候选人中的百分位>,
            "strengths": ["优势1", "优势2", "优势3"],
            "improvement_areas": ["改进点1", "改进点2"],
            "recommendations": ["建议1", "建议2", "建议3"],
            "ai_insights": "深度洞察和分析"
        }}
        
        评分标准：
        - 数字素养(20%)：数字工具使用、数据分析、AI理解
        - 行业技能(25%)：行业知识、专业技能、经验深度
        - 岗位技能(30%)：具体岗位所需技能匹配度
        - 创新能力(15%)：创新思维、问题解决、适应性
        - 学习潜力(10%)：学习能力、成长潜力、知识更新
        """
    
    def _validate_assessment_data(
        self,
        data: Dict[str, Any],
        include_insights: bool
    ) -> Dict[str, Any]:
        """Validate and normalize assessment data"""
        
        # Create dimension scores
        dimensions = {}
        
        dimension_mapping = {
            'digital_literacy': 'digital_literacy_score',
            'industry_skills': 'industry_skills_score',
            'position_skills': 'position_skills_score',
            'innovation': 'innovation_score',
            'learning_potential': 'learning_potential_score'
        }
        
        for dim_name, score_key in dimension_mapping.items():
            score = data.get(score_key, 70.0)  # Default score
            dimensions[dim_name] = DimensionScore(
                dimension=dim_name,
                score=min(100.0, max(0.0, float(score))),
                confidence=0.85,
                evidence=[]
            )
        
        # Calculate DCI if not provided
        if 'dci_score' not in data:
            weights = {
                'digital_literacy': 0.20,
                'industry_skills': 0.25,
                'position_skills': 0.30,
                'innovation': 0.15,
                'learning_potential': 0.10
            }
            dci_score = sum(
                dimensions[k].score * w 
                for k, w in weights.items()
            )
        else:
            dci_score = float(data['dci_score'])
        
        # Create radar chart data
        radar_chart_data = {
            dim: dimensions[dim].score 
            for dim in dimensions
        }
        
        return {
            'dimensions': dimensions,
            'dci_score': dci_score,
            'percentile': float(data.get('percentile', 75.0)),
            'strengths': data.get('strengths', []),
            'improvement_areas': data.get('improvement_areas', []),
            'recommendations': data.get('recommendations', []),
            'radar_chart_data': radar_chart_data
        }
    
    async def _generate_rule_based(self, candidate: Candidate) -> Dict[str, Any]:
        """Fallback rule-based assessment generation"""
        
        # Simple rule-based scoring
        base_score = 70.0
        experience_bonus = min(20.0, getattr(candidate, 'experience_years', 0) * 2)
        
        dimensions = {
            'digital_literacy': DimensionScore(
                dimension='digital_literacy',
                score=base_score + experience_bonus * 0.5,
                confidence=0.7,
                evidence=["基于经验年限评估"]
            ),
            'industry_skills': DimensionScore(
                dimension='industry_skills',
                score=base_score + experience_bonus * 0.8,
                confidence=0.7,
                evidence=["基于行业经验评估"]
            ),
            'position_skills': DimensionScore(
                dimension='position_skills',
                score=base_score + experience_bonus * 0.6,
                confidence=0.7,
                evidence=["基于技能匹配评估"]
            ),
            'innovation': DimensionScore(
                dimension='innovation',
                score=base_score,
                confidence=0.6,
                evidence=["基于标准评估"]
            ),
            'learning_potential': DimensionScore(
                dimension='learning_potential',
                score=base_score + 10.0,
                confidence=0.6,
                evidence=["基于潜力评估"]
            )
        }
        
        # Calculate DCI score
        weights = {
            'digital_literacy': 0.20,
            'industry_skills': 0.25,
            'position_skills': 0.30,
            'innovation': 0.15,
            'learning_potential': 0.10
        }
        
        dci_score = sum(
            dimensions[k].score * w 
            for k, w in weights.items()
        )
        
        return {
            'dimensions': dimensions,
            'dci_score': dci_score,
            'percentile': 70.0,
            'strengths': ["工作经验丰富", "技能扎实"],
            'improvement_areas': ["创新能力", "数字素养"],
            'recommendations': ["建议参加技能培训", "增强项目经验"],
            'radar_chart_data': {k: v.score for k, v in dimensions.items()}
        }
    
    async def _calculate_jfs(
        self,
        candidate: Candidate,
        position: Position,
        dimensions: Dict[str, DimensionScore]
    ) -> float:
        """Calculate Job Fit Score"""
        
        # Simple JFS calculation based on dimension match
        # In real implementation, this would use position requirements
        position_weights = {
            'digital_literacy': 0.15,
            'industry_skills': 0.25,
            'position_skills': 0.35,
            'innovation': 0.15,
            'learning_potential': 0.10
        }
        
        jfs_score = sum(
            dimensions[k].score * w 
            for k, w in position_weights.items()
            if k in dimensions
        )
        
        return min(100.0, jfs_score)
    
    async def _get_cached(self, key: str) -> Optional[Dict]:
        """Get cached data"""
        try:
            data = await redis_client.get(key)
            if data:
                return json.loads(data)
        except Exception as e:
            logger.warning(f"Cache get failed: {str(e)}")
        return None
    
    async def _set_cached(self, key: str, value: Dict, ttl: int):
        """Set cached data"""
        try:
            await redis_client.setex(
                key,
                ttl,
                json.dumps(value, default=str)
            )
        except Exception as e:
            logger.warning(f"Cache set failed: {str(e)}")
    
    async def calculate_jfs(
        self,
        request: JFSCalculationRequest,
        db: AsyncSession
    ) -> JFSCalculationResponse:
        """Calculate Job Fit Score for candidate-position pair"""
        
        # Get candidate assessment
        assessment_req = AssessmentCreateRequest(
            candidate_id=request.candidate_id,
            position_id=request.position_id,
            assessment_type="quick"
        )
        assessment = await self.generate_assessment(assessment_req, db)
        
        # Get position details
        position = await db.get(Position, request.position_id)
        if not position:
            raise ValueError(f"Position {request.position_id} not found")
        
        # Calculate dimension matches
        dimension_matches = {
            dim: score.score 
            for dim, score in assessment.dimensions.items()
        }
        
        # Generate match summary
        match_summary = f"候选人与{position.title}岗位的匹配度为{assessment.jfs_score:.1f}%"
        
        # Generate recommendations
        recommendations = []
        if assessment.jfs_score >= 80:
            recommendations.append("强烈推荐面试")
        elif assessment.jfs_score >= 60:
            recommendations.append("建议进一步评估")
        else:
            recommendations.append("需要提升相关技能")
        
        return JFSCalculationResponse(
            candidate_id=request.candidate_id,
            position_id=request.position_id,
            jfs_score=assessment.jfs_score or 0.0,
            dimension_matches=dimension_matches,
            match_summary=match_summary,
            recommendations=recommendations
        )
    
    async def compare_candidates(
        self,
        request: ComparisonRequest,
        db: AsyncSession
    ) -> ComparisonResponse:
        """Compare multiple candidates"""
        
        candidates_data = []
        
        for candidate_id in request.candidate_ids:
            # Generate assessment for each candidate
            assessment_req = AssessmentCreateRequest(
                candidate_id=candidate_id,
                position_id=request.position_id,
                assessment_type="quick"
            )
            assessment = await self.generate_assessment(assessment_req, db)
            
            # Get candidate info
            candidate = await db.get(Candidate, candidate_id)
            
            candidates_data.append(
                CandidateComparison(
                    candidate_id=candidate_id,
                    candidate_name=candidate.name if candidate else "Unknown",
                    dci_score=assessment.dci_score,
                    percentile=assessment.percentile,
                    dimension_scores={
                        dim: score.score 
                        for dim, score in assessment.dimensions.items()
                    },
                    strengths=assessment.strengths[:3],
                    rank=0  # Will be set after sorting
                )
            )
        
        # Sort by DCI score and assign ranks
        candidates_data.sort(key=lambda x: x.dci_score, reverse=True)
        for i, candidate in enumerate(candidates_data, 1):
            candidate.rank = i
        
        # Determine winner
        winner_id = candidates_data[0].candidate_id if candidates_data else None
        
        # Create comparison chart data
        comparison_chart_data = {
            "candidates": [c.candidate_name for c in candidates_data],
            "scores": {
                dim: [c.dimension_scores.get(dim, 0) for c in candidates_data]
                for dim in ['digital_literacy', 'industry_skills', 'position_skills', 'innovation', 'learning_potential']
            }
        }
        
        return ComparisonResponse(
            candidates=candidates_data,
            winner_id=winner_id,
            comparison_chart_data=comparison_chart_data
        )
    
    async def generate_recommendations(
        self,
        request: RecommendationRequest,
        db: AsyncSession
    ) -> RecommendationResponse:
        """Generate personalized recommendations using AI"""
        
        # Get candidate assessment
        assessment_req = AssessmentCreateRequest(
            candidate_id=request.candidate_id,
            assessment_type="comprehensive",
            include_ai_insights=True
        )
        assessment = await self.generate_assessment(assessment_req, db)
        
        # Generate recommendations based on weak areas
        recommendations = []
        learning_path = []
        
        # Identify weak dimensions (score < 70)
        weak_areas = [
            (dim, score.score) 
            for dim, score in assessment.dimensions.items() 
            if score.score < 70
        ]
        
        for dim, score in weak_areas:
            if dim == 'digital_literacy':
                recommendations.append({
                    "area": "数字素养",
                    "priority": "高",
                    "actions": ["参加数据分析培训", "学习AI工具使用", "提升数字化办公技能"]
                })
                learning_path.append({
                    "step": 1,
                    "skill": "数据分析基础",
                    "duration": "2周",
                    "resources": ["Coursera数据分析课程", "Excel高级技巧"]
                })
            elif dim == 'innovation':
                recommendations.append({
                    "area": "创新能力",
                    "priority": "中",
                    "actions": ["参与创新项目", "学习设计思维", "跨部门交流"]
                })
        
        # Estimate improvement potential
        estimated_improvement = {
            dim: min(95.0, score.score + 15.0)
            for dim, score in assessment.dimensions.items()
        }
        
        # Resources
        resources = [
            {
                "type": "课程",
                "title": "数字化转型与创新",
                "provider": "Coursera",
                "url": "https://coursera.org/digital"
            },
            {
                "type": "书籍",
                "title": "创新者的窘境",
                "author": "Clayton Christensen",
                "relevance": "创新思维"
            }
        ]
        
        return RecommendationResponse(
            candidate_id=request.candidate_id,
            recommendations=recommendations,
            learning_path=learning_path,
            estimated_improvement=estimated_improvement,
            resources=resources
        )


# Service instance
enhanced_assessment_service = EnhancedAssessmentService()