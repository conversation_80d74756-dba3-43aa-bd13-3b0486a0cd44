"""
File storage service using Min<PERSON> for resume and document management
"""
import os
import uuid
from typing import Optional, BinaryIO, Dict, Any

# Try to import magic, but make it optional
try:
    import magic
    HAS_MAGIC = True
except ImportError:
    HAS_MAGIC = False
    import logging as _logging
    _logging.getLogger(__name__).warning("python-magic not available, falling back to basic MIME type detection")
from datetime import datetime, timedelta
from minio import Minio
from minio.error import S3Error, InvalidResponseError
from fastapi import UploadFile, HTTPException
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class FileService:
    """文件存储服务（MinIO集成）"""
    
    # Allowed file types for security
    ALLOWED_MIME_TYPES = {
        'application/pdf': '.pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': '.docx',
        'application/msword': '.doc',
        'text/plain': '.txt',
    }
    
    # Maximum file size (10MB)
    MAX_FILE_SIZE = 10 * 1024 * 1024
    
    def __init__(self):
        """Initialize MinIO client"""
        try:
            self.client = Minio(
                settings.MINIO_ENDPOINT,
                access_key=settings.MINIO_ACCESS_KEY,
                secret_key=settings.MINIO_SECRET_KEY,
                secure=settings.MINIO_USE_SSL,
                region="us-east-1"  # Add explicit region for compatibility
            )
            self.bucket = settings.MINIO_BUCKET
            self.resume_bucket = settings.MINIO_BUCKET_RESUMES
            self._ensure_buckets()
            
        except Exception as e:
            logger.error(f"Failed to initialize MinIO client: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"File storage initialization failed: {str(e)}"
            )
    
    def _ensure_buckets(self):
        """确保所需的存储桶存在"""
        # Skip bucket creation/checking - buckets are auto-created by MinIO via docker-compose.yml
        # This avoids Python MinIO client compatibility issues in development
        logger.info(f"MinIO buckets configured for auto-creation: {self.resume_bucket}")
        logger.info("Skipping manual bucket creation - using MinIO auto-created buckets")
    
    async def upload_resume(
        self,
        file: UploadFile,
        user_id: int,
        candidate_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """上传简历文件"""
        # Validate file
        await self._validate_file(file)
        
        # Generate unique filename
        file_ext = self._get_file_extension(file.filename)
        unique_filename = f"{uuid.uuid4()}{file_ext}"
        object_name = f"resumes/{user_id}/{unique_filename}"
        
        # Prepare metadata
        metadata = {
            "uploaded_by": str(user_id),
            "candidate_id": str(candidate_id) if candidate_id else "",
            "original_name": file.filename or "unknown",
            "upload_time": datetime.utcnow().isoformat(),
            "content_type": file.content_type or "application/octet-stream"
        }
        
        try:
            # Reset file pointer
            await file.seek(0)
            
            # Upload to MinIO
            result = self.client.put_object(
                self.resume_bucket,
                object_name,
                file.file,
                length=file.size,
                content_type=file.content_type,
                metadata=metadata
            )
            
            # Generate presigned URL for access
            presigned_url = self.get_presigned_url(
                self.resume_bucket,
                object_name,
                expires_days=30  # Resume URLs valid for 30 days
            )
            
            return {
                "object_name": object_name,
                "presigned_url": presigned_url,
                "file_size": file.size,
                "content_type": file.content_type,
                "original_filename": file.filename,
                "upload_time": datetime.utcnow(),
                "etag": result.etag
            }
            
        except S3Error as e:
            logger.error(f"MinIO upload failed: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"File upload failed: {str(e)}"
            )
        except Exception as e:
            logger.error(f"Unexpected upload error: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Upload processing failed: {str(e)}"
            )
    
    def get_presigned_url(
        self,
        bucket_name: str,
        object_name: str,
        expires_days: int = 7
    ) -> str:
        """获取预签名URL用于安全访问"""
        try:
            url = self.client.presigned_get_object(
                bucket_name,
                object_name,
                expires=timedelta(days=expires_days)
            )
            return url
            
        except S3Error as e:
            logger.error(f"Failed to generate presigned URL: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"URL generation failed: {str(e)}"
            )
    
    def get_presigned_upload_url(
        self,
        bucket_name: str,
        object_name: str,
        expires_minutes: int = 15
    ) -> str:
        """获取预签名上传URL（用于前端直接上传）"""
        try:
            url = self.client.presigned_put_object(
                bucket_name,
                object_name,
                expires=timedelta(minutes=expires_minutes)
            )
            return url
            
        except S3Error as e:
            logger.error(f"Failed to generate upload URL: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Upload URL generation failed: {str(e)}"
            )
    
    def download_file(
        self,
        bucket_name: str,
        object_name: str
    ) -> bytes:
        """下载文件内容"""
        try:
            response = self.client.get_object(bucket_name, object_name)
            return response.read()
            
        except S3Error as e:
            logger.error(f"Failed to download file: {str(e)}")
            raise HTTPException(
                status_code=404,
                detail="File not found or access denied"
            )
        finally:
            if 'response' in locals():
                response.close()
                response.release_conn()
    
    def delete_file(
        self,
        bucket_name: str,
        object_name: str
    ) -> bool:
        """删除文件"""
        try:
            self.client.remove_object(bucket_name, object_name)
            logger.info(f"Deleted file: {bucket_name}/{object_name}")
            return True
            
        except S3Error as e:
            logger.error(f"Failed to delete file: {str(e)}")
            return False
    
    def get_file_info(
        self,
        bucket_name: str,
        object_name: str
    ) -> Optional[Dict[str, Any]]:
        """获取文件信息"""
        try:
            stat = self.client.stat_object(bucket_name, object_name)
            return {
                "size": stat.size,
                "last_modified": stat.last_modified,
                "etag": stat.etag,
                "content_type": stat.content_type,
                "metadata": stat.metadata
            }
            
        except S3Error as e:
            logger.error(f"Failed to get file info: {str(e)}")
            return None
    
    async def _validate_file(self, file: UploadFile) -> None:
        """验证上传文件的安全性和合规性"""
        if not file:
            raise HTTPException(
                status_code=400,
                detail="No file provided"
            )
        
        # Check file size
        if file.size and file.size > self.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"File too large. Maximum size: {self.MAX_FILE_SIZE // (1024*1024)}MB"
            )
        
        # Read first 2KB for MIME type detection
        await file.seek(0)
        file_header = await file.read(2048)
        await file.seek(0)
        
        # Detect actual MIME type using python-magic if available
        if HAS_MAGIC:
            try:
                detected_mime = magic.from_buffer(file_header, mime=True)
            except Exception as e:
                logger.warning(f"MIME detection failed: {str(e)}")
                detected_mime = file.content_type
        else:
            # Fallback to content_type from upload
            detected_mime = file.content_type
            logger.info(f"Using content_type from upload: {detected_mime}")
        
        # Validate MIME type
        if detected_mime not in self.ALLOWED_MIME_TYPES:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported file type: {detected_mime}. "
                       f"Allowed types: {', '.join(self.ALLOWED_MIME_TYPES.keys())}"
            )
        
        # Additional security checks
        if file.filename:
            # Check for dangerous file extensions
            dangerous_extensions = ['.exe', '.bat', '.cmd', '.scr', '.pif', '.com']
            file_lower = file.filename.lower()
            
            if any(file_lower.endswith(ext) for ext in dangerous_extensions):
                raise HTTPException(
                    status_code=400,
                    detail="Dangerous file type detected"
                )
    
    def _get_file_extension(self, filename: Optional[str]) -> str:
        """获取文件扩展名"""
        if not filename:
            return '.bin'
        
        return os.path.splitext(filename)[1].lower() or '.bin'
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # Test bucket listing
            buckets = self.client.list_buckets()
            bucket_names = [bucket.name for bucket in buckets]
            
            return {
                "status": "healthy",
                "endpoint": settings.MINIO_ENDPOINT,
                "available_buckets": bucket_names,
                "required_buckets": [self.bucket, self.resume_bucket],
                "buckets_ready": all(
                    bucket in bucket_names 
                    for bucket in [self.bucket, self.resume_bucket]
                )
            }
            
        except Exception as e:
            logger.error(f"MinIO health check failed: {str(e)}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "endpoint": settings.MINIO_ENDPOINT
            }


# Global file service instance
file_service = FileService()