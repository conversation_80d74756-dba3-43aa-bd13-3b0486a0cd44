"""
Resume Parsing Service - Main service for resume parsing operations
Integrates LLM-enhanced parser with vector embeddings and database operations
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple, Union
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from app.models import Candidate, ResumeVector
from app.schemas.candidate import CandidateCreate, CandidateUpdate
from app.services.parser import enhanced_resume_parser, ParsedResume
from app.services.embedding_service import embedding_service
from app.crud import candidate as candidate_crud, resume_vector as resume_vector_crud
from app.core.exceptions import service_error, not_found, bad_request
# Remove PermissionChecker import as it's handled at API layer

logger = logging.getLogger(__name__)


class ResumeParsingService:
    """Comprehensive resume parsing service with LLM enhancement and vector storage"""
    
    def __init__(self):
        self.parser_service = enhanced_resume_parser
        self.embedding_service = embedding_service
    
    async def parse_and_store_resume(
        self,
        db: AsyncSession,
        file_content: bytes,
        filename: str,
        user_id: int,
        candidate_id: Optional[int] = None,
        generate_embeddings: bool = True,
        embedding_provider: Optional[str] = None
    ) -> Dict[str, Any]:
        """Parse resume and store candidate data with embeddings"""
        try:
            # Parse resume using enhanced parser
            logger.info(f"Starting resume parsing for file: {filename}")
            parsed_resume = await self.parser_service.parse_resume(file_content, filename)
            
            if parsed_resume.confidence_score < 0.3:
                logger.warning(f"Low confidence parsing result for {filename}: {parsed_resume.confidence_score}")
            
            # Create or update candidate record
            if candidate_id:
                # Update existing candidate
                candidate = await candidate_crud.get(db, id=candidate_id)
                if not candidate:
                    raise not_found(f"CANDIDATE_NOT_FOUND: {candidate_id}")
                
                # Permission checking is handled at API layer
                
                # Update candidate with parsed data
                update_data = self._create_candidate_update_data(parsed_resume)
                candidate = await candidate_crud.update(db, db_obj=candidate, obj_in=update_data)
                action = "updated"
            else:
                # Create new candidate
                create_data = self._create_candidate_create_data(parsed_resume, user_id)
                candidate = await candidate_crud.create(db, obj_in=create_data)
                candidate_id = candidate.id
                action = "created"
            
            # Generate and store embeddings if requested
            embeddings_result = {}
            if generate_embeddings and parsed_resume.raw_text:
                try:
                    embeddings_result = await self._generate_and_store_embeddings(
                        db, candidate_id, parsed_resume, embedding_provider
                    )
                except Exception as e:
                    logger.error(f"Failed to generate embeddings for candidate {candidate_id}: {str(e)}")
                    embeddings_result = {"error": str(e), "embeddings_generated": False}
            
            # Prepare response
            result = {
                "candidate_id": candidate_id,
                "action": action,
                "parsing_result": {
                    "confidence_score": parsed_resume.confidence_score,
                    "parser_version": parsed_resume.parser_version,
                    "parsing_errors": parsed_resume.parsing_errors,
                    "fields_extracted": self._count_extracted_fields(parsed_resume)
                },
                "candidate_data": {
                    "name": candidate.name,
                    "email": candidate.email,
                    "phone": candidate.phone,
                    "current_position": candidate.current_position,
                    "current_company": candidate.current_company,
                    "years_of_experience": candidate.years_of_experience,
                    "skills_count": len(parsed_resume.skills) if parsed_resume.skills else 0,
                    "education_count": len(parsed_resume.education) if parsed_resume.education else 0,
                    "experience_count": len(parsed_resume.work_experience) if parsed_resume.work_experience else 0
                },
                "embeddings": embeddings_result,
                "processed_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"Successfully processed resume {filename} for candidate {candidate_id}")
            return result
            
        except Exception as e:
            logger.error(f"Resume parsing and storage failed for {filename}: {str(e)}")
            if "CANDIDATE_" in str(e) or "PARSER_" in str(e) or "EMBEDDING_" in str(e):
                raise  # Re-raise service errors with proper error codes
            else:
                raise service_error(f"RESUME_PROCESSING_FAILED: {str(e)}")
    
    async def parse_resume_only(
        self,
        file_content: bytes,
        filename: str,
        include_raw_text: bool = False
    ) -> Dict[str, Any]:
        """Parse resume without storing to database (for analysis/preview)"""
        try:
            parsed_resume = await self.parser_service.parse_resume(file_content, filename)
            
            result = {
                "parsing_result": {
                    "confidence_score": parsed_resume.confidence_score,
                    "parser_version": parsed_resume.parser_version,
                    "parsing_errors": parsed_resume.parsing_errors,
                    "fields_extracted": self._count_extracted_fields(parsed_resume)
                },
                "extracted_data": {
                    "name": parsed_resume.name,
                    "email": parsed_resume.email,
                    "phone": parsed_resume.phone,
                    "current_position": parsed_resume.current_position,
                    "current_company": parsed_resume.current_company,
                    "years_of_experience": parsed_resume.years_of_experience,
                    "skills": parsed_resume.skills or [],
                    "education": parsed_resume.education or [],
                    "work_experience": parsed_resume.work_experience or []
                },
                "metadata": {
                    "filename": filename,
                    "parsed_at": datetime.utcnow().isoformat()
                }
            }
            
            if include_raw_text:
                result["raw_text"] = parsed_resume.raw_text
            
            return result
            
        except Exception as e:
            logger.error(f"Resume parsing failed for {filename}: {str(e)}")
            if "PARSER_" in str(e):
                raise
            else:
                raise service_error(f"RESUME_PARSING_FAILED: {str(e)}")
    
    async def batch_parse_resumes(
        self,
        db: AsyncSession,
        resume_files: List[Tuple[bytes, str]],
        user_id: int,
        generate_embeddings: bool = True,
        max_concurrent: int = 5
    ) -> List[Dict[str, Any]]:
        """Parse multiple resumes concurrently"""
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def process_single_resume(file_content: bytes, filename: str):
            async with semaphore:
                try:
                    return await self.parse_and_store_resume(
                        db, file_content, filename, user_id, 
                        generate_embeddings=generate_embeddings
                    )
                except Exception as e:
                    logger.error(f"Batch processing failed for {filename}: {str(e)}")
                    return {
                        "filename": filename,
                        "success": False,
                        "error": str(e),
                        "error_code": "BATCH_PROCESSING_ERROR"
                    }
        
        tasks = [process_single_resume(content, name) for content, name in resume_files]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results and handle exceptions
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                filename = resume_files[i][1]
                processed_results.append({
                    "filename": filename,
                    "success": False,
                    "error": str(result),
                    "error_code": "BATCH_PROCESSING_EXCEPTION"
                })
            else:
                result["success"] = "error" not in result
                processed_results.append(result)
        
        return processed_results
    
    async def reparse_candidate_resume(
        self,
        db: AsyncSession,
        candidate_id: int,
        user_id: int,
        file_content: bytes,
        filename: str,
        force_reprocess: bool = False
    ) -> Dict[str, Any]:
        """Reparse an existing candidate's resume with updated parser"""
        # Check candidate exists and user has permission
        candidate = await candidate_crud.get(db, id=candidate_id)
        if not candidate:
            raise not_found(f"CANDIDATE_NOT_FOUND: {candidate_id}")
        
        # Permission checking is handled at API layer
        
        # Parse with enhanced parser
        result = await self.parse_and_store_resume(
            db, file_content, filename, user_id, candidate_id,
            generate_embeddings=True
        )
        result["action"] = "reparsed"
        
        return result
    
    async def _generate_and_store_embeddings(
        self,
        db: AsyncSession,
        candidate_id: int,
        parsed_resume: ParsedResume,
        provider: Optional[str] = None
    ) -> Dict[str, Any]:
        """Generate and store multi-granularity embeddings"""
        try:
            # Prepare texts for embedding
            texts_to_embed = []
            text_types = []
            
            # Full text embedding
            if parsed_resume.raw_text:
                texts_to_embed.append(parsed_resume.raw_text[:8000])  # Truncate if too long
                text_types.append("full_text")
            
            # Experience summary embedding
            if parsed_resume.work_experience:
                experience_text = " | ".join([
                    f"{exp.get('position', '')} at {exp.get('company', '')} - {exp.get('description', '')}"
                    for exp in parsed_resume.work_experience[:3]  # Top 3 experiences
                ])
                texts_to_embed.append(experience_text)
                text_types.append("experience")
            
            # Education summary embedding
            if parsed_resume.education:
                education_text = " | ".join([
                    f"{edu.get('degree', '')} in {edu.get('major', '')} from {edu.get('school', '')}"
                    for edu in parsed_resume.education
                ])
                texts_to_embed.append(education_text)
                text_types.append("education")
            
            # Skills embedding
            if parsed_resume.skills:
                skills_text = ", ".join(parsed_resume.skills[:30])  # Top 30 skills
                texts_to_embed.append(skills_text)
                text_types.append("skills")
            
            if not texts_to_embed:
                return {"embeddings_generated": False, "reason": "No text content for embedding"}
            
            # Generate embeddings
            embeddings, metadata = await self.embedding_service.generate_embeddings(
                texts_to_embed, provider=provider
            )
            
            # Create embedding vectors dictionary
            embedding_vectors = {}
            for i, (embedding, text_type) in enumerate(zip(embeddings, text_types)):
                embedding_vectors[f"{text_type}_vector"] = embedding
            
            # Store in database
            vector_data = {
                "candidate_id": candidate_id,
                "embedding_model": metadata["embedding_model"],
                "embedding_provider": metadata["embedding_provider"],
                "embedding_dimension": metadata["embedding_dimension"],
                **embedding_vectors
            }
            
            await resume_vector_crud.create_or_update_by_candidate(db, **vector_data)
            
            return {
                "embeddings_generated": True,
                "provider": metadata["embedding_provider"],
                "model": metadata["embedding_model"],
                "dimension": metadata["embedding_dimension"],
                "vector_types": text_types,
                "token_count": metadata.get("token_count", 0),
                "generation_time_ms": metadata.get("generation_time_ms", 0)
            }
            
        except Exception as e:
            logger.error(f"Embedding generation failed for candidate {candidate_id}: {str(e)}")
            return {
                "embeddings_generated": False,
                "error": str(e)
            }
    
    def _create_candidate_create_data(self, parsed_resume: ParsedResume, user_id: int) -> CandidateCreate:
        """Create CandidateCreate object from parsed resume data"""
        return CandidateCreate(
            name=parsed_resume.name or "Unknown",
            email=parsed_resume.email,
            phone=parsed_resume.phone,
            current_position=parsed_resume.current_position,
            current_company=parsed_resume.current_company,
            years_of_experience=parsed_resume.years_of_experience,
            skills=parsed_resume.skills or [],
            created_by=user_id,
            source="resume_upload"
        )
    
    def _create_candidate_update_data(self, parsed_resume: ParsedResume) -> CandidateUpdate:
        """Create CandidateUpdate object from parsed resume data"""
        update_data = {}
        
        if parsed_resume.name:
            update_data["name"] = parsed_resume.name
        if parsed_resume.email:
            update_data["email"] = parsed_resume.email
        if parsed_resume.phone:
            update_data["phone"] = parsed_resume.phone
        if parsed_resume.current_position:
            update_data["current_position"] = parsed_resume.current_position
        if parsed_resume.current_company:
            update_data["current_company"] = parsed_resume.current_company
        if parsed_resume.years_of_experience is not None:
            update_data["years_of_experience"] = parsed_resume.years_of_experience
        if parsed_resume.skills:
            update_data["skills"] = parsed_resume.skills
        
        return CandidateUpdate(**update_data)
    
    def _count_extracted_fields(self, parsed_resume: ParsedResume) -> int:
        """Count successfully extracted fields"""
        field_count = 0
        
        if parsed_resume.name:
            field_count += 1
        if parsed_resume.email:
            field_count += 1
        if parsed_resume.phone:
            field_count += 1
        if parsed_resume.current_position:
            field_count += 1
        if parsed_resume.current_company:
            field_count += 1
        if parsed_resume.years_of_experience is not None:
            field_count += 1
        if parsed_resume.skills:
            field_count += 1
        if parsed_resume.education:
            field_count += 1
        if parsed_resume.work_experience:
            field_count += 1
        
        return field_count
    
    async def get_parsing_statistics(self, db: AsyncSession) -> Dict[str, Any]:
        """Get resume parsing statistics and performance metrics"""
        try:
            # Get parser service information
            parser_info = self.parser_service.get_service_info()
            
            # Get embedding service information
            embedding_providers = self.embedding_service.get_supported_providers()
            
            # Query database statistics
            total_candidates = await candidate_crud.count_total(db)
            total_with_vectors = await resume_vector_crud.count_total(db)
            
            return {
                "parser_service": parser_info,
                "embedding_service": {
                    "supported_providers": embedding_providers,
                    "primary_provider": self.embedding_service.get_supported_providers()[0] if embedding_providers else None
                },
                "database_statistics": {
                    "total_candidates": total_candidates,
                    "candidates_with_vectors": total_with_vectors,
                    "vector_coverage_percentage": round((total_with_vectors / total_candidates * 100), 2) if total_candidates > 0 else 0
                },
                "performance_metrics": {
                    "target_parsing_accuracy": ">95%",
                    "target_processing_time": "<5s per resume",
                    "max_concurrent_processing": 5,
                    "supported_file_formats": ["pdf", "docx", "txt"]
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get parsing statistics: {str(e)}")
            raise service_error(f"PARSING_STATISTICS_ERROR: {str(e)}")


# Global service instance
resume_parsing_service = ResumeParsingService()