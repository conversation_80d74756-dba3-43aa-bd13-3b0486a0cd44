import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { analyticsService } from '@/services/analytics.service';
import {
  AnalyticsOverview,
  TrendData,
  DepartmentPerformance,
  SkillsGapAnalysis,
  PerformanceMetrics,
  PredictiveAnalytics,
  ConversionFunnel
} from '@/types/analytics';

interface AnalyticsState {
  overview: AnalyticsOverview | null;
  trends: TrendData | null;
  departments: DepartmentPerformance[];
  skillsGap: SkillsGapAnalysis[];
  performance: PerformanceMetrics | null;
  predictive: PredictiveAnalytics | null;
  funnel: ConversionFunnel | null;
  loading: boolean;
  error: string | null;
  timeRange: string;
  activeTab: string;
  realTimeConfig: {
    wsUrl?: string;
    token?: string;
    metrics?: string[];
    connected: boolean;
  };
}

const initialState: AnalyticsState = {
  overview: null,
  trends: null,
  departments: [],
  skillsGap: [],
  performance: null,
  predictive: null,
  funnel: null,
  loading: false,
  error: null,
  timeRange: 'last30days',
  activeTab: 'overview',
  realTimeConfig: {
    connected: false
  }
};

// Async thunks
export const fetchAnalyticsOverview = createAsyncThunk(
  'analytics/fetchOverview',
  async (timeRange: string) => {
    return await analyticsService.getOverview(timeRange);
  }
);

export const fetchTrends = createAsyncThunk(
  'analytics/fetchTrends',
  async (params: {
    metric: string;
    timeRange: string;
    groupBy?: 'day' | 'week' | 'month';
  }) => {
    return await analyticsService.getTrends(params);
  }
);

export const fetchDepartmentPerformance = createAsyncThunk(
  'analytics/fetchDepartments',
  async (timeRange: string) => {
    return await analyticsService.getDepartmentPerformance(timeRange);
  }
);

export const fetchSkillsGap = createAsyncThunk(
  'analytics/fetchSkillsGap',
  async (params?: {
    department?: string;
    position?: string;
    timeRange?: string;
  }) => {
    return await analyticsService.getSkillsGap(params);
  }
);

export const fetchPerformanceMetrics = createAsyncThunk(
  'analytics/fetchPerformance',
  async (timeRange: string) => {
    return await analyticsService.getPerformanceMetrics(timeRange);
  }
);

export const fetchPredictiveAnalytics = createAsyncThunk(
  'analytics/fetchPredictive',
  async (horizon: 'quarter' | 'year' = 'quarter') => {
    return await analyticsService.getPredictiveAnalytics(horizon);
  }
);

export const fetchConversionFunnel = createAsyncThunk(
  'analytics/fetchFunnel',
  async (params?: {
    department?: string;
    position?: string;
    timeRange?: string;
  }) => {
    return await analyticsService.getConversionFunnel(params);
  }
);

export const exportAnalyticsData = createAsyncThunk(
  'analytics/export',
  async (params: {
    type: 'overview' | 'trends' | 'performance' | 'all';
    format: 'csv' | 'excel' | 'pdf';
    timeRange: string;
  }) => {
    const blob = await analyticsService.exportData(params);
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `analytics-${params.type}-${new Date().toISOString()}.${params.format}`;
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    return true;
  }
);

export const fetchRealTimeConfig = createAsyncThunk(
  'analytics/fetchRealTimeConfig',
  async () => {
    return await analyticsService.getRealTimeConfig();
  }
);

export const comparePeriods = createAsyncThunk(
  'analytics/comparePeriods',
  async (params: {
    period1Start: string;
    period1End: string;
    period2Start: string;
    period2End: string;
    metrics: string[];
  }) => {
    return await analyticsService.comparePeriods(params);
  }
);

const analyticsSlice = createSlice({
  name: 'analytics',
  initialState,
  reducers: {
    setTimeRange: (state, action: PayloadAction<string>) => {
      state.timeRange = action.payload;
    },
    setActiveTab: (state, action: PayloadAction<string>) => {
      state.activeTab = action.payload;
    },
    setRealTimeConnected: (state, action: PayloadAction<boolean>) => {
      state.realTimeConfig.connected = action.payload;
    },
    updateMetric: (state, action: PayloadAction<{ metric: string; value: any }>) => {
      // Update specific metric in real-time
      if (state.overview) {
        const metricKey = action.payload.metric as keyof AnalyticsOverview;
        if (metricKey in state.overview) {
          (state.overview[metricKey] as any).value = action.payload.value;
        }
      }
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    // Fetch overview
    builder
      .addCase(fetchAnalyticsOverview.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAnalyticsOverview.fulfilled, (state, action) => {
        state.overview = action.payload;
        state.loading = false;
      })
      .addCase(fetchAnalyticsOverview.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch overview';
      });

    // Fetch trends
    builder
      .addCase(fetchTrends.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTrends.fulfilled, (state, action) => {
        state.trends = action.payload;
        state.loading = false;
      })
      .addCase(fetchTrends.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch trends';
      });

    // Fetch departments
    builder
      .addCase(fetchDepartmentPerformance.fulfilled, (state, action) => {
        state.departments = action.payload;
      });

    // Fetch skills gap
    builder
      .addCase(fetchSkillsGap.fulfilled, (state, action) => {
        state.skillsGap = action.payload;
      });

    // Fetch performance
    builder
      .addCase(fetchPerformanceMetrics.fulfilled, (state, action) => {
        state.performance = action.payload;
      });

    // Fetch predictive
    builder
      .addCase(fetchPredictiveAnalytics.fulfilled, (state, action) => {
        state.predictive = action.payload;
      });

    // Fetch funnel
    builder
      .addCase(fetchConversionFunnel.fulfilled, (state, action) => {
        state.funnel = action.payload;
      });

    // Fetch real-time config
    builder
      .addCase(fetchRealTimeConfig.fulfilled, (state, action) => {
        state.realTimeConfig = {
          ...action.payload,
          connected: false
        };
      });
  }
});

export const {
  setTimeRange,
  setActiveTab,
  setRealTimeConnected,
  updateMetric,
  clearError
} = analyticsSlice.actions;

export default analyticsSlice.reducer;