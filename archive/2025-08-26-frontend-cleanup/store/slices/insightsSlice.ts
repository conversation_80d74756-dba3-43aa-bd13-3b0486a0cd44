import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { insightsService } from '@/services/insights.service';
import {
  Insight,
  BusinessMetrics,
  Recommendation,
  ImplementationRoadmap,
  InsightFeedback
} from '@/types/insights';

interface InsightsState {
  insights: Insight[];
  businessMetrics: BusinessMetrics | null;
  recommendations: Recommendation[];
  roadmap: ImplementationRoadmap | null;
  selectedInsight: Insight | null;
  aiSummary: {
    summary: string;
    keyPoints: string[];
    opportunities: number;
    risks: number;
    improvements: number;
    trends: number;
  } | null;
  loading: boolean;
  error: string | null;
  filters: {
    type: string;
    priority: string;
    category: string;
  };
  pagination: {
    skip: number;
    limit: number;
    total: number;
  };
  bookmarkedInsights: string[];
  generationTask: {
    taskId?: string;
    status?: 'pending' | 'processing' | 'completed' | 'failed';
    progress?: number;
  };
}

const initialState: InsightsState = {
  insights: [],
  businessMetrics: null,
  recommendations: [],
  roadmap: null,
  selectedInsight: null,
  aiSummary: null,
  loading: false,
  error: null,
  filters: {
    type: 'all',
    priority: 'all',
    category: 'all'
  },
  pagination: {
    skip: 0,
    limit: 10,
    total: 0
  },
  bookmarkedInsights: [],
  generationTask: {}
};

// Async thunks
export const fetchInsights = createAsyncThunk(
  'insights/fetchInsights',
  async (params?: {
    type?: string;
    priority?: string;
    category?: string;
    timeRange?: string;
    skip?: number;
    limit?: number;
  }) => {
    return await insightsService.getInsights(params as any);
  }
);

export const fetchBusinessMetrics = createAsyncThunk(
  'insights/fetchBusinessMetrics',
  async (timeRange: string = 'last30days') => {
    return await insightsService.getBusinessMetrics(timeRange);
  }
);

export const fetchRecommendations = createAsyncThunk(
  'insights/fetchRecommendations',
  async (params?: {
    status?: 'new' | 'in_progress' | 'completed' | 'planned';
    impact?: 'high' | 'medium' | 'low';
    effort?: 'high' | 'medium' | 'low';
  }) => {
    return await insightsService.getRecommendations(params);
  }
);

export const fetchImplementationRoadmap = createAsyncThunk(
  'insights/fetchRoadmap',
  async () => {
    return await insightsService.getImplementationRoadmap();
  }
);

export const fetchAISummary = createAsyncThunk(
  'insights/fetchAISummary',
  async (timeRange: string = 'last30days') => {
    return await insightsService.getAISummary(timeRange);
  }
);

export const submitInsightFeedback = createAsyncThunk(
  'insights/submitFeedback',
  async (params: { insightId: string; feedback: InsightFeedback }) => {
    await insightsService.submitFeedback(params.insightId, params.feedback);
    return { insightId: params.insightId, feedback: params.feedback };
  }
);

export const toggleBookmark = createAsyncThunk(
  'insights/toggleBookmark',
  async (insightId: string) => {
    const result = await insightsService.toggleBookmark(insightId);
    return { insightId, bookmarked: result.bookmarked };
  }
);

export const shareInsight = createAsyncThunk(
  'insights/share',
  async (params: {
    insightId: string;
    recipients: string[];
    message?: string;
  }) => {
    await insightsService.shareInsight(params.insightId, {
      recipients: params.recipients,
      message: params.message
    });
    return params.insightId;
  }
);

export const takeInsightAction = createAsyncThunk(
  'insights/takeAction',
  async (params: {
    insightId: string;
    action: {
      type: 'task' | 'investigation' | 'implementation' | 'monitoring';
      title: string;
      description: string;
      assignee?: string;
      dueDate?: string;
    };
  }) => {
    await insightsService.takeAction(params.insightId, params.action as any);
    return params.insightId;
  }
);

export const startRecommendationImplementation = createAsyncThunk(
  'insights/startImplementation',
  async (params: {
    recommendationId: string;
    assignee?: string;
    dueDate?: string;
    notes?: string;
  }) => {
    await insightsService.startImplementation(params.recommendationId, {
      assignee: params.assignee,
      dueDate: params.dueDate,
      notes: params.notes
    });
    return params.recommendationId;
  }
);

export const updateRecommendationStatus = createAsyncThunk(
  'insights/updateRecommendationStatus',
  async (params: {
    recommendationId: string;
    status: 'in_progress' | 'completed' | 'cancelled';
  }) => {
    await insightsService.updateRecommendationStatus(
      params.recommendationId,
      params.status
    );
    return params;
  }
);

export const generateInsights = createAsyncThunk(
  'insights/generate',
  async (params?: {
    focus?: string[];
    depth?: 'quick' | 'standard' | 'deep';
  }) => {
    return await insightsService.generateInsights(params);
  }
);

export const checkGenerationStatus = createAsyncThunk(
  'insights/checkGenerationStatus',
  async (taskId: string) => {
    return await insightsService.getGenerationStatus(taskId);
  }
);

export const exportInsights = createAsyncThunk(
  'insights/export',
  async (params: {
    type?: string;
    format: 'pdf' | 'excel' | 'ppt';
    timeRange: string;
  }) => {
    const blob = await insightsService.exportInsights(params);
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `insights-${new Date().toISOString()}.${params.format}`;
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
    return true;
  }
);

const insightsSlice = createSlice({
  name: 'insights',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<InsightsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setPagination: (state, action: PayloadAction<Partial<InsightsState['pagination']>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    setSelectedInsight: (state, action: PayloadAction<Insight | null>) => {
      state.selectedInsight = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    }
  },
  extraReducers: (builder) => {
    // Fetch insights
    builder
      .addCase(fetchInsights.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchInsights.fulfilled, (state, action) => {
        state.insights = action.payload.items;
        state.pagination.total = action.payload.total;
        state.loading = false;
      })
      .addCase(fetchInsights.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message || 'Failed to fetch insights';
      });

    // Fetch business metrics
    builder
      .addCase(fetchBusinessMetrics.fulfilled, (state, action) => {
        state.businessMetrics = action.payload;
      });

    // Fetch recommendations
    builder
      .addCase(fetchRecommendations.fulfilled, (state, action) => {
        state.recommendations = action.payload;
      });

    // Fetch roadmap
    builder
      .addCase(fetchImplementationRoadmap.fulfilled, (state, action) => {
        state.roadmap = action.payload;
      });

    // Fetch AI summary
    builder
      .addCase(fetchAISummary.fulfilled, (state, action) => {
        state.aiSummary = action.payload;
      });

    // Submit feedback
    builder
      .addCase(submitInsightFeedback.fulfilled, (state, action) => {
        const insight = state.insights.find(i => i.id === action.payload.insightId);
        if (insight) {
          if (!insight.feedback) {
            insight.feedback = { helpful: 0, notHelpful: 0 };
          }
          if (action.payload.feedback.helpful) {
            insight.feedback.helpful++;
            insight.feedback.userFeedback = 'helpful';
          } else {
            insight.feedback.notHelpful++;
            insight.feedback.userFeedback = 'not_helpful';
          }
        }
      });

    // Toggle bookmark
    builder
      .addCase(toggleBookmark.fulfilled, (state, action) => {
        const insight = state.insights.find(i => i.id === action.payload.insightId);
        if (insight) {
          insight.bookmarked = action.payload.bookmarked;
        }
        if (action.payload.bookmarked) {
          state.bookmarkedInsights.push(action.payload.insightId);
        } else {
          state.bookmarkedInsights = state.bookmarkedInsights.filter(
            id => id !== action.payload.insightId
          );
        }
      });

    // Update recommendation status
    builder
      .addCase(updateRecommendationStatus.fulfilled, (state, action) => {
        const recommendation = state.recommendations.find(
          r => r.id === action.payload.recommendationId
        );
        if (recommendation) {
          recommendation.status = action.payload.status;
        }
      });

    // Generate insights
    builder
      .addCase(generateInsights.pending, (state) => {
        state.generationTask = { status: 'pending', progress: 0 };
      })
      .addCase(generateInsights.fulfilled, (state, action) => {
        state.generationTask = {
          taskId: action.payload.taskId,
          status: 'processing',
          progress: 0
        };
      });

    // Check generation status
    builder
      .addCase(checkGenerationStatus.fulfilled, (state, action) => {
        state.generationTask = {
          ...state.generationTask,
          status: action.payload.status,
          progress: action.payload.progress
        };
        if (action.payload.status === 'completed' && action.payload.result) {
          state.insights = [...action.payload.result, ...state.insights];
        }
      });
  }
});

export const {
  setFilters,
  setPagination,
  setSelectedInsight,
  clearError
} = insightsSlice.actions;

export default insightsSlice.reducer;