/**
 * Unit tests for candidateSlice Redux store
 */
import { configureStore } from '@reduxjs/toolkit'
import candidateSlice, {
  fetchCandidates,
  fetchCandidateById,
  createCandidate,
  createCandidateWithResume,
  updateCandidate,
  deleteCandidate,
  uploadResume,
  shareCandidate,
  bulkExportCandidates,
  bulkImportCandidates,
  checkDuplicateCandidate,
  fetchCandidateStats,
  setFilters,
  clearFilters,
  setPagination,
  setCurrentCandidate,
  clearCurrentCandidate,
  toggleCandidateSelection,
  selectAllCandidates,
  clearCandidateSelection,
  setBulkOperation,
  setBulkProgress,
  clearError,
  clearAllErrors,
  invalidateCache,
  updateCandidateLocal,
  addCandidateLocal,
  removeCandidateLocal,
} from '../candidateSlice'
import { candidateService } from '@/services/candidate'
import {
  mockCandidates,
  mockCandidate,
  mockCandidateCreate,
  mockCandidateUpdate,
  mockCandidateListResponse,
  mockCandidateStats,
  mockResumeUploadResponse,
} from '@/test/mock-data'
import { CandidateStatus, DataPermission } from '@/types'

// Mock the candidate service
jest.mock('@/services/candidate')
const mockCandidateService = candidateService as jest.Mocked<typeof candidateService>

// Helper to create store with initial state
const createStore = (preloadedState = {}) =>
  configureStore({
    reducer: {
      candidates: candidateSlice,
    },
    preloadedState: {
      candidates: {
        candidates: [],
        currentCandidate: null,
        pagination: {
          page: 1,
          pageSize: 20,
          total: 0,
          totalPages: 0,
        },
        filters: {
          search: '',
          status: undefined,
          skills: [],
          min_experience: undefined,
          max_experience: undefined,
          min_salary: undefined,
          max_salary: undefined,
          data_permission: undefined,
          source: undefined,
          tags: [],
          created_after: undefined,
          created_before: undefined,
          order_by: 'created_at',
          order_desc: true,
        },
        bulkOperations: {
          selectedIds: [],
          operation: null,
          progress: 0,
        },
        loading: {
          list: false,
          detail: false,
          create: false,
          update: false,
          delete: false,
          uploadResume: false,
          share: false,
          bulkImport: false,
          bulkExport: false,
        },
        errors: {
          list: null,
          detail: null,
          create: null,
          update: null,
          delete: null,
          uploadResume: null,
          share: null,
          bulkImport: null,
          bulkExport: null,
        },
        lastFetch: null,
        cacheValid: false,
        ...preloadedState,
      },
    },
  })

describe('candidateSlice', () => {
  let store: ReturnType<typeof createStore>

  beforeEach(() => {
    store = createStore()
    jest.clearAllMocks()
  })

  describe('Synchronous Actions', () => {
    describe('setFilters', () => {
      it('should update filters and invalidate cache', () => {
        const newFilters = { search: 'John', status: CandidateStatus.NEW }

        store.dispatch(setFilters(newFilters))

        const state = store.getState().candidates
        expect(state.filters.search).toBe('John')
        expect(state.filters.status).toBe(CandidateStatus.NEW)
        expect(state.cacheValid).toBe(false)
      })

      it('should merge with existing filters', () => {
        // Set initial filters
        store.dispatch(setFilters({ search: 'Initial' }))
        
        // Update with new filters
        store.dispatch(setFilters({ status: CandidateStatus.SCREENING }))

        const state = store.getState().candidates
        expect(state.filters.search).toBe('Initial')
        expect(state.filters.status).toBe(CandidateStatus.SCREENING)
      })
    })

    describe('clearFilters', () => {
      it('should reset filters to initial state', () => {
        // Set some filters first
        store.dispatch(setFilters({ 
          search: 'John', 
          status: CandidateStatus.NEW,
          min_experience: 5 
        }))

        // Clear filters
        store.dispatch(clearFilters())

        const state = store.getState().candidates
        expect(state.filters.search).toBe('')
        expect(state.filters.status).toBeUndefined()
        expect(state.filters.min_experience).toBeUndefined()
        expect(state.cacheValid).toBe(false)
      })
    })

    describe('setPagination', () => {
      it('should update pagination', () => {
        store.dispatch(setPagination({ page: 2, pageSize: 50 }))

        const state = store.getState().candidates
        expect(state.pagination.page).toBe(2)
        expect(state.pagination.pageSize).toBe(50)
      })
    })

    describe('setCurrentCandidate', () => {
      it('should set current candidate', () => {
        store.dispatch(setCurrentCandidate(mockCandidate))

        const state = store.getState().candidates
        expect(state.currentCandidate).toEqual(mockCandidate)
      })

      it('should clear current candidate', () => {
        store.dispatch(setCurrentCandidate(mockCandidate))
        store.dispatch(clearCurrentCandidate())

        const state = store.getState().candidates
        expect(state.currentCandidate).toBeNull()
      })
    })

    describe('Bulk Operations', () => {
      beforeEach(() => {
        // Set up candidates in store
        store = createStore({
          candidates: mockCandidates,
        })
      })

      it('should toggle candidate selection', () => {
        store.dispatch(toggleCandidateSelection('1'))

        let state = store.getState().candidates
        expect(state.bulkOperations.selectedIds).toContain('1')

        // Toggle again to deselect
        store.dispatch(toggleCandidateSelection('1'))

        state = store.getState().candidates
        expect(state.bulkOperations.selectedIds).not.toContain('1')
      })

      it('should select all candidates', () => {
        store.dispatch(selectAllCandidates())

        const state = store.getState().candidates
        expect(state.bulkOperations.selectedIds).toEqual(['1', '2', '3'])
      })

      it('should clear selection', () => {
        // Select some candidates first
        store.dispatch(toggleCandidateSelection('1'))
        store.dispatch(toggleCandidateSelection('2'))

        // Clear selection
        store.dispatch(clearCandidateSelection())

        const state = store.getState().candidates
        expect(state.bulkOperations.selectedIds).toEqual([])
      })

      it('should set bulk operation and progress', () => {
        store.dispatch(setBulkOperation('export'))
        store.dispatch(setBulkProgress(50))

        const state = store.getState().candidates
        expect(state.bulkOperations.operation).toBe('export')
        expect(state.bulkOperations.progress).toBe(50)
      })
    })

    describe('Error Management', () => {
      it('should clear specific error', () => {
        // Set up error state
        store = createStore({
          errors: { list: 'List error', create: 'Create error' },
        })

        store.dispatch(clearError('list'))

        const state = store.getState().candidates
        expect(state.errors.list).toBeNull()
        expect(state.errors.create).toBe('Create error')
      })

      it('should clear all errors', () => {
        // Set up error state
        store = createStore({
          errors: { 
            list: 'List error', 
            create: 'Create error',
            update: 'Update error'
          },
        })

        store.dispatch(clearAllErrors())

        const state = store.getState().candidates
        expect(state.errors.list).toBeNull()
        expect(state.errors.create).toBeNull()
        expect(state.errors.update).toBeNull()
      })
    })

    describe('Local Updates', () => {
      beforeEach(() => {
        store = createStore({
          candidates: mockCandidates,
          currentCandidate: mockCandidate,
        })
      })

      it('should update candidate locally', () => {
        const updatedCandidate = { ...mockCandidate, name: 'Updated Name' }

        store.dispatch(updateCandidateLocal(updatedCandidate))

        const state = store.getState().candidates
        const candidate = state.candidates.find(c => c.id === '1')
        expect(candidate?.name).toBe('Updated Name')
        expect(state.currentCandidate?.name).toBe('Updated Name')
      })

      it('should add candidate locally', () => {
        const newCandidate = { ...mockCandidate, id: '4', name: 'New Candidate' }

        store.dispatch(addCandidateLocal(newCandidate))

        const state = store.getState().candidates
        expect(state.candidates[0]).toEqual(newCandidate) // Added at beginning
        expect(state.pagination.total).toBe(1) // Incremented
      })

      it('should remove candidate locally', () => {
        store.dispatch(removeCandidateLocal('1'))

        const state = store.getState().candidates
        expect(state.candidates.find(c => c.id === '1')).toBeUndefined()
        expect(state.currentCandidate).toBeNull() // Cleared if it was current
        expect(state.pagination.total).toBe(-1) // Decremented
      })
    })
  })

  describe('Async Actions', () => {
    describe('fetchCandidates', () => {
      it('should handle successful fetch', async () => {
        mockCandidateService.getList.mockResolvedValue(mockCandidateListResponse)

        const params = { skip: 0, limit: 20 }
        await store.dispatch(fetchCandidates(params))

        const state = store.getState().candidates
        expect(state.loading.list).toBe(false)
        expect(state.candidates).toEqual(mockCandidates)
        expect(state.pagination.total).toBe(3)
        expect(state.errors.list).toBeNull()
        expect(state.cacheValid).toBe(true)
        expect(state.lastFetch).toBeTruthy()
      })

      it('should handle fetch error', async () => {
        const errorMessage = 'Network error'
        mockCandidateService.getList.mockRejectedValue(new Error(errorMessage))

        await store.dispatch(fetchCandidates())

        const state = store.getState().candidates
        expect(state.loading.list).toBe(false)
        expect(state.errors.list).toBe(errorMessage)
      })

      it('should set loading state during fetch', () => {
        mockCandidateService.getList.mockImplementation(
          () => new Promise(resolve => setTimeout(resolve, 100))
        )

        store.dispatch(fetchCandidates())

        const state = store.getState().candidates
        expect(state.loading.list).toBe(true)
        expect(state.errors.list).toBeNull()
      })
    })

    describe('fetchCandidateById', () => {
      it('should handle successful fetch by ID', async () => {
        mockCandidateService.getById.mockResolvedValue(mockCandidate)

        await store.dispatch(fetchCandidateById('1'))

        const state = store.getState().candidates
        expect(state.loading.detail).toBe(false)
        expect(state.currentCandidate).toEqual(mockCandidate)
        expect(state.errors.detail).toBeNull()
      })

      it('should update candidate in list if exists', async () => {
        // Set up store with candidates
        store = createStore({ candidates: mockCandidates })
        
        const updatedCandidate = { ...mockCandidate, name: 'Updated Name' }
        mockCandidateService.getById.mockResolvedValue(updatedCandidate)

        await store.dispatch(fetchCandidateById('1'))

        const state = store.getState().candidates
        const candidate = state.candidates.find(c => c.id === '1')
        expect(candidate?.name).toBe('Updated Name')
      })

      it('should handle fetch by ID error', async () => {
        const errorMessage = 'Candidate not found'
        mockCandidateService.getById.mockRejectedValue(new Error(errorMessage))

        await store.dispatch(fetchCandidateById('999'))

        const state = store.getState().candidates
        expect(state.loading.detail).toBe(false)
        expect(state.errors.detail).toBe(errorMessage)
      })
    })

    describe('createCandidate', () => {
      it('should handle successful creation', async () => {
        const createdCandidate = { ...mockCandidate, ...mockCandidateCreate }
        mockCandidateService.create.mockResolvedValue(createdCandidate)

        await store.dispatch(createCandidate(mockCandidateCreate))

        const state = store.getState().candidates
        expect(state.loading.create).toBe(false)
        expect(state.candidates[0]).toEqual(createdCandidate)
        expect(state.currentCandidate).toEqual(createdCandidate)
        expect(state.pagination.total).toBe(1)
        expect(state.errors.create).toBeNull()
      })

      it('should handle creation error', async () => {
        const errorMessage = 'Validation failed'
        mockCandidateService.create.mockRejectedValue(new Error(errorMessage))

        await store.dispatch(createCandidate(mockCandidateCreate))

        const state = store.getState().candidates
        expect(state.loading.create).toBe(false)
        expect(state.errors.create).toBe(errorMessage)
      })
    })

    describe('createCandidateWithResume', () => {
      it('should handle successful creation with resume', async () => {
        const file = new File(['resume'], 'resume.pdf', { type: 'application/pdf' })
        const createdCandidate = { 
          ...mockCandidate, 
          ...mockCandidateCreate,
          resume_url: 'https://example.com/resume.pdf'
        }
        mockCandidateService.createWithResume.mockResolvedValue(createdCandidate)

        await store.dispatch(createCandidateWithResume({ 
          data: mockCandidateCreate, 
          resumeFile: file 
        }))

        const state = store.getState().candidates
        expect(state.loading.create).toBe(false)
        expect(state.candidates[0]).toEqual(createdCandidate)
        expect(state.currentCandidate).toEqual(createdCandidate)
        expect(state.errors.create).toBeNull()
      })
    })

    describe('updateCandidate', () => {
      beforeEach(() => {
        store = createStore({
          candidates: mockCandidates,
          currentCandidate: mockCandidate,
        })
      })

      it('should handle successful update', async () => {
        const updatedCandidate = { ...mockCandidate, ...mockCandidateUpdate }
        mockCandidateService.update.mockResolvedValue(updatedCandidate)

        await store.dispatch(updateCandidate({ 
          id: '1', 
          data: mockCandidateUpdate 
        }))

        const state = store.getState().candidates
        expect(state.loading.update).toBe(false)
        expect(state.candidates[0]).toEqual(updatedCandidate)
        expect(state.currentCandidate).toEqual(updatedCandidate)
        expect(state.errors.update).toBeNull()
      })

      it('should handle update error', async () => {
        const errorMessage = 'Update failed'
        mockCandidateService.update.mockRejectedValue(new Error(errorMessage))

        await store.dispatch(updateCandidate({ 
          id: '1', 
          data: mockCandidateUpdate 
        }))

        const state = store.getState().candidates
        expect(state.loading.update).toBe(false)
        expect(state.errors.update).toBe(errorMessage)
      })
    })

    describe('deleteCandidate', () => {
      beforeEach(() => {
        store = createStore({
          candidates: mockCandidates,
          currentCandidate: mockCandidate,
          bulkOperations: { selectedIds: ['1', '2'], operation: null, progress: 0 },
        })
      })

      it('should handle successful deletion', async () => {
        mockCandidateService.delete.mockResolvedValue({ message: 'Deleted' })

        await store.dispatch(deleteCandidate('1'))

        const state = store.getState().candidates
        expect(state.loading.delete).toBe(false)
        expect(state.candidates.find(c => c.id === '1')).toBeUndefined()
        expect(state.currentCandidate).toBeNull()
        expect(state.bulkOperations.selectedIds).not.toContain('1')
        expect(state.pagination.total).toBe(-1)
        expect(state.errors.delete).toBeNull()
      })

      it('should handle delete error', async () => {
        const errorMessage = 'Delete failed'
        mockCandidateService.delete.mockRejectedValue(new Error(errorMessage))

        await store.dispatch(deleteCandidate('1'))

        const state = store.getState().candidates
        expect(state.loading.delete).toBe(false)
        expect(state.errors.delete).toBe(errorMessage)
      })
    })

    describe('uploadResume', () => {
      beforeEach(() => {
        store = createStore({
          candidates: mockCandidates,
          currentCandidate: mockCandidate,
        })
      })

      it('should handle successful resume upload', async () => {
        const file = new File(['resume'], 'resume.pdf', { type: 'application/pdf' })
        mockCandidateService.uploadResume.mockResolvedValue(mockResumeUploadResponse)

        await store.dispatch(uploadResume({ 
          candidateId: '1', 
          resumeFile: file 
        }))

        const state = store.getState().candidates
        expect(state.loading.uploadResume).toBe(false)
        expect(state.candidates[0].resume_url).toBe(mockResumeUploadResponse.resume_url)
        expect(state.currentCandidate?.resume_url).toBe(mockResumeUploadResponse.resume_url)
        expect(state.errors.uploadResume).toBeNull()
      })
    })

    describe('shareCandidate', () => {
      beforeEach(() => {
        store = createStore({
          candidates: mockCandidates,
          currentCandidate: mockCandidate,
        })
      })

      it('should handle successful candidate sharing', async () => {
        const shareParams = { user_ids: ['user-2', 'user-3'] }
        mockCandidateService.shareCandidate.mockResolvedValue({ 
          message: 'Shared successfully' 
        })

        await store.dispatch(shareCandidate({ 
          candidateId: '1', 
          params: shareParams 
        }))

        const state = store.getState().candidates
        expect(state.loading.share).toBe(false)
        expect(state.candidates[0].shared_with).toContain('user-2')
        expect(state.candidates[0].shared_with).toContain('user-3')
        expect(state.errors.share).toBeNull()
      })
    })

    describe('bulkExportCandidates', () => {
      it('should handle successful bulk export', async () => {
        const exportParams = { candidate_ids: ['1', '2'], format: 'excel' as const }
        mockCandidateService.bulkExport.mockResolvedValue({
          download_url: 'https://example.com/export.xlsx'
        })

        await store.dispatch(bulkExportCandidates(exportParams))

        const state = store.getState().candidates
        expect(state.loading.bulkExport).toBe(false)
        expect(state.bulkOperations.operation).toBeNull()
        expect(state.bulkOperations.progress).toBe(100)
        expect(state.errors.bulkExport).toBeNull()
      })

      it('should handle bulk export error', async () => {
        const errorMessage = 'Export failed'
        mockCandidateService.bulkExport.mockRejectedValue(new Error(errorMessage))

        await store.dispatch(bulkExportCandidates({ 
          candidate_ids: ['1'], 
          format: 'excel' 
        }))

        const state = store.getState().candidates
        expect(state.loading.bulkExport).toBe(false)
        expect(state.errors.bulkExport).toBe(errorMessage)
        expect(state.bulkOperations.operation).toBeNull()
      })
    })

    describe('bulkImportCandidates', () => {
      it('should handle successful bulk import', async () => {
        const file = new File(['csv content'], 'import.csv', { type: 'text/csv' })
        mockCandidateService.bulkImport.mockResolvedValue({
          message: 'Import started',
          task_id: 'import-123'
        })

        await store.dispatch(bulkImportCandidates(file))

        const state = store.getState().candidates
        expect(state.loading.bulkImport).toBe(false)
        expect(state.bulkOperations.operation).toBeNull()
        expect(state.bulkOperations.progress).toBe(100)
        expect(state.cacheValid).toBe(false) // Cache invalidated
        expect(state.errors.bulkImport).toBeNull()
      })
    })

    describe('fetchCandidateStats', () => {
      it('should handle successful stats fetch', async () => {
        mockCandidateService.getStats.mockResolvedValue(mockCandidateStats)

        const result = await store.dispatch(fetchCandidateStats())

        expect(result.type).toBe('candidates/fetchStats/fulfilled')
        expect(result.payload).toEqual(mockCandidateStats)
      })
    })

    describe('checkDuplicateCandidate', () => {
      it('should handle successful duplicate check', async () => {
        const checkParams = {
          name: 'John Doe',
          email: '<EMAIL>',
        }
        const checkResponse = { exists: false, candidate: null }
        
        mockCandidateService.checkDuplicate.mockResolvedValue(checkResponse)

        const result = await store.dispatch(checkDuplicateCandidate(checkParams))

        expect(result.type).toBe('candidates/checkDuplicate/fulfilled')
        expect(result.payload).toEqual(checkResponse)
      })
    })
  })

  describe('Loading States', () => {
    it('should set loading state for async actions', () => {
      // Mock a pending promise
      mockCandidateService.getList.mockImplementation(
        () => new Promise(() => {}) // Never resolves
      )

      store.dispatch(fetchCandidates())

      const state = store.getState().candidates
      expect(state.loading.list).toBe(true)
    })
  })

  describe('Cache Management', () => {
    it('should invalidate cache', () => {
      // Set cache as valid first
      store = createStore({ cacheValid: true })

      store.dispatch(invalidateCache())

      const state = store.getState().candidates
      expect(state.cacheValid).toBe(false)
    })
  })
})