import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import authReducer from './authSlice';
import candidateReducer from './candidateSlice';
import positionReducer from './positionSlice';
import vectorReducer from './vectorSlice';
import reportsReducer from '../store/slices/reportsSlice';
import analyticsReducer from '../store/slices/analyticsSlice';
import insightsReducer from '../store/slices/insightsSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    candidates: candidateReducer,
    positions: positionReducer,
    vectors: vectorReducer,
    reports: reportsReducer,
    analytics: analyticsReducer,
    insights: insightsReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        // Ignore these action types
        ignoredActions: [
          'auth/setUser',
          'candidates/fetchList/fulfilled',
          'candidates/fetchById/fulfilled',
          'positions/fetchList/fulfilled',
          'positions/fetchById/fulfilled',
          'vectors/fetchByPosition/fulfilled',
          'vectors/fetchList/fulfilled',
        ],
        // Ignore these paths in the state
        ignoredPaths: [
          'auth.user.created_at', 
          'auth.user.updated_at',
          'candidates.candidates.0.created_at',
          'candidates.candidates.0.updated_at',
          'candidates.currentCandidate.created_at',
          'candidates.currentCandidate.updated_at',
          'positions.positions.0.created_at',
          'positions.positions.0.updated_at',
          'positions.currentPosition.created_at',
          'positions.currentPosition.updated_at',
          'vectors.vectors.0.created_at',
          'vectors.vectors.0.updated_at',
          'vectors.currentVector.created_at',
          'vectors.currentVector.updated_at',
        ],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Use throughout your app instead of plain `useDispatch` and `useSelector`
export const useAppDispatch: () => AppDispatch = useDispatch;
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;