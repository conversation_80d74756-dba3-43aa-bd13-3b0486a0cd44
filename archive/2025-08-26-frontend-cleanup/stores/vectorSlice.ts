import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { vectorService, RegenerateParams, SearchParams } from '@/services/vector';
import { 
  JobVector,
  VectorStats,
  ProviderInfo,
  SearchResult,
  TestResult,
  VectorActivity,
  PerformanceMetrics,
  TaskInfo
} from '@/types';

// Loading states interface
interface LoadingStates {
  detail: boolean;
  list: boolean;
  generate: boolean;
  batchGenerate: boolean;
  regenerate: boolean;
  delete: boolean;
  search: boolean;
  stats: boolean;
  providers: boolean;
  testEmbedding: boolean;
  activities: boolean;
  performance: boolean;
  health: boolean;
  textEmbedding: boolean;
  compare: boolean;
}

// Error states interface
interface ErrorStates {
  detail: string | null;
  list: string | null;
  generate: string | null;
  batchGenerate: string | null;
  regenerate: string | null;
  delete: string | null;
  search: string | null;
  stats: string | null;
  providers: string | null;
  testEmbedding: string | null;
  activities: string | null;
  performance: string | null;
  health: string | null;
  textEmbedding: string | null;
  compare: string | null;
}

// Search state
interface SearchState {
  query: string;
  results: SearchResult[];
  lastParams: SearchParams | null;
  loading: boolean;
  error: string | null;
}

// Generation queue state
interface GenerationQueueState {
  positionIds: string[];
  completed: string[];
  failed: string[];
  progress: number;
  currentProvider: string | null;
}

// Health monitoring state
interface HealthState {
  ollama: {
    status: 'healthy' | 'degraded' | 'down';
    latency?: number;
    last_check: string;
    error?: string;
  };
  openai: {
    status: 'healthy' | 'degraded' | 'down';
    latency?: number;
    last_check: string;
    error?: string;
  };
}

// Main vector state interface
interface VectorState {
  // Data
  vectors: JobVector[];
  currentVector: JobVector | null;
  stats: VectorStats | null;
  providers: ProviderInfo | null;
  activities: VectorActivity[];
  performance: PerformanceMetrics | null;
  
  // Search functionality
  search: SearchState;
  
  // Generation tracking
  generationQueue: GenerationQueueState;
  
  // Health monitoring
  health: HealthState;
  
  // Loading states
  loading: LoadingStates;
  
  // Error states
  errors: ErrorStates;
  
  // Background tasks
  backgroundTasks: Record<string, TaskInfo>;
  
  // Test results
  lastTestResult: TestResult | null;
  
  // Cache metadata
  lastFetch: number | null;
  cacheValid: boolean;
}

// Initial state
const initialState: VectorState = {
  // Data
  vectors: [],
  currentVector: null,
  stats: null,
  providers: null,
  activities: [],
  performance: null,
  
  // Search functionality
  search: {
    query: '',
    results: [],
    lastParams: null,
    loading: false,
    error: null,
  },
  
  // Generation tracking
  generationQueue: {
    positionIds: [],
    completed: [],
    failed: [],
    progress: 0,
    currentProvider: null,
  },
  
  // Health monitoring
  health: {
    ollama: {
      status: 'down',
      last_check: new Date().toISOString(),
    },
    openai: {
      status: 'down',
      last_check: new Date().toISOString(),
    },
  },
  
  // Loading states
  loading: {
    detail: false,
    list: false,
    generate: false,
    batchGenerate: false,
    regenerate: false,
    delete: false,
    search: false,
    stats: false,
    providers: false,
    testEmbedding: false,
    activities: false,
    performance: false,
    health: false,
    textEmbedding: false,
    compare: false,
  },
  
  // Error states
  errors: {
    detail: null,
    list: null,
    generate: null,
    batchGenerate: null,
    regenerate: null,
    delete: null,
    search: null,
    stats: null,
    providers: null,
    testEmbedding: null,
    activities: null,
    performance: null,
    health: null,
    textEmbedding: null,
    compare: null,
  },
  
  // Background tasks
  backgroundTasks: {},
  
  // Test results
  lastTestResult: null,
  
  // Cache metadata
  lastFetch: null,
  cacheValid: false,
};

// Async thunks
export const fetchVectorByPosition = createAsyncThunk(
  'vectors/fetchByPosition',
  async (positionId: string) => {
    const vector = await vectorService.getByPositionId(positionId);
    return vector;
  }
);

export const fetchVectorList = createAsyncThunk(
  'vectors/fetchList',
  async (params: {
    skip?: number;
    limit?: number;
    has_embeddings?: boolean;
    provider?: string;
  } = {}) => {
    const response = await vectorService.getList(params);
    return { response, params };
  }
);

export const generateVectorEmbedding = createAsyncThunk(
  'vectors/generate',
  async ({ positionId, provider }: { positionId: string; provider?: string }) => {
    const task = await vectorService.generateEmbedding(positionId, provider);
    return { positionId, task };
  }
);

export const batchGenerateEmbeddings = createAsyncThunk(
  'vectors/batchGenerate',
  async (params: RegenerateParams) => {
    const task = await vectorService.batchGenerate(params);
    return { params, task };
  }
);

export const regenerateEmbedding = createAsyncThunk(
  'vectors/regenerate',
  async ({ positionId, provider }: { positionId: string; provider?: string }) => {
    const task = await vectorService.regenerateEmbedding(positionId, provider);
    return { positionId, task };
  }
);

export const deleteVector = createAsyncThunk(
  'vectors/delete',
  async (positionId: string) => {
    await vectorService.delete(positionId);
    return positionId;
  }
);

export const searchVectors = createAsyncThunk(
  'vectors/search',
  async (params: SearchParams) => {
    const results = await vectorService.search(params);
    return { results, params };
  }
);

export const fetchVectorStats = createAsyncThunk(
  'vectors/fetchStats',
  async () => {
    const stats = await vectorService.getStats();
    return stats;
  }
);

export const fetchProviderInfo = createAsyncThunk(
  'vectors/fetchProviders',
  async () => {
    const providers = await vectorService.getProviderInfo();
    return providers;
  }
);

export const testEmbedding = createAsyncThunk(
  'vectors/testEmbedding',
  async ({ text, provider }: { text: string; provider?: string }) => {
    const result = await vectorService.testEmbedding(text, provider);
    return result;
  }
);

export const fetchVectorActivities = createAsyncThunk(
  'vectors/fetchActivities',
  async (params: {
    skip?: number;
    limit?: number;
    position_id?: string;
    action?: string;
    start_date?: string;
    end_date?: string;
  } = {}) => {
    const response = await vectorService.getActivities(params);
    return response;
  }
);

export const fetchPerformanceMetrics = createAsyncThunk(
  'vectors/fetchPerformance',
  async (days: number = 30) => {
    const metrics = await vectorService.getPerformanceMetrics(days);
    return metrics;
  }
);

export const checkProviderHealth = createAsyncThunk(
  'vectors/checkHealth',
  async (provider?: string) => {
    const health = await vectorService.checkProviderHealth(provider);
    return { provider, health };
  }
);

export const getTextEmbedding = createAsyncThunk(
  'vectors/getTextEmbedding',
  async ({ text, provider }: { text: string; provider?: string }) => {
    const result = await vectorService.getTextEmbedding(text, provider);
    return result;
  }
);

export const comparePositions = createAsyncThunk(
  'vectors/compare',
  async ({ positionId1, positionId2 }: { positionId1: string; positionId2: string }) => {
    const result = await vectorService.comparePositions(positionId1, positionId2);
    return result;
  }
);

// Vector slice
const vectorSlice = createSlice({
  name: 'vectors',
  initialState,
  reducers: {
    // Search actions
    setSearchQuery: (state, action: PayloadAction<string>) => {
      state.search.query = action.payload;
    },
    clearSearchResults: (state) => {
      state.search.results = [];
      state.search.error = null;
      state.search.lastParams = null;
    },
    
    // Generation queue actions
    addToGenerationQueue: (state, action: PayloadAction<string[]>) => {
      state.generationQueue.positionIds = [
        ...state.generationQueue.positionIds,
        ...action.payload.filter(id => !state.generationQueue.positionIds.includes(id))
      ];
    },
    removeFromGenerationQueue: (state, action: PayloadAction<string>) => {
      state.generationQueue.positionIds = state.generationQueue.positionIds.filter(
        id => id !== action.payload
      );
    },
    markGenerationCompleted: (state, action: PayloadAction<string>) => {
      const positionId = action.payload;
      state.generationQueue.completed.push(positionId);
      state.generationQueue.positionIds = state.generationQueue.positionIds.filter(
        id => id !== positionId
      );
      state.generationQueue.progress = 
        (state.generationQueue.completed.length / 
         (state.generationQueue.completed.length + state.generationQueue.positionIds.length + state.generationQueue.failed.length)) * 100;
    },
    markGenerationFailed: (state, action: PayloadAction<string>) => {
      const positionId = action.payload;
      state.generationQueue.failed.push(positionId);
      state.generationQueue.positionIds = state.generationQueue.positionIds.filter(
        id => id !== positionId
      );
    },
    clearGenerationQueue: (state) => {
      state.generationQueue = initialState.generationQueue;
    },
    setCurrentProvider: (state, action: PayloadAction<string | null>) => {
      state.generationQueue.currentProvider = action.payload;
    },
    
    // Error actions
    clearError: (state, action: PayloadAction<keyof ErrorStates>) => {
      state.errors[action.payload] = null;
    },
    clearAllErrors: (state) => {
      Object.keys(state.errors).forEach(key => {
        state.errors[key as keyof ErrorStates] = null;
      });
    },
    
    // Cache actions
    invalidateCache: (state) => {
      state.cacheValid = false;
    },
    
    // Background task management
    updateBackgroundTask: (state, action: PayloadAction<TaskInfo>) => {
      state.backgroundTasks[action.payload.task_id] = action.payload;
    },
    clearBackgroundTask: (state, action: PayloadAction<string>) => {
      delete state.backgroundTasks[action.payload];
    },
    
    // Local vector updates
    updateVectorLocal: (state, action: PayloadAction<JobVector>) => {
      const index = state.vectors.findIndex(v => v.position_id === action.payload.position_id);
      if (index >= 0) {
        state.vectors[index] = action.payload;
      } else {
        state.vectors.push(action.payload);
      }
      if (state.currentVector?.position_id === action.payload.position_id) {
        state.currentVector = action.payload;
      }
    },
    
    // Remove vector locally
    removeVectorLocal: (state, action: PayloadAction<string>) => {
      state.vectors = state.vectors.filter(v => v.position_id !== action.payload);
      if (state.currentVector?.position_id === action.payload) {
        state.currentVector = null;
      }
    },
    
    // Health updates
    updateProviderHealth: (state, action: PayloadAction<{
      provider: string;
      health: {
        status: 'healthy' | 'degraded' | 'down';
        latency?: number;
        last_check: string;
        error?: string;
      };
    }>) => {
      const { provider, health } = action.payload;
      if (provider === 'ollama' || provider === 'openai') {
        state.health[provider] = health;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch vector by position
    builder
      .addCase(fetchVectorByPosition.pending, (state) => {
        state.loading.detail = true;
        state.errors.detail = null;
      })
      .addCase(fetchVectorByPosition.fulfilled, (state, action) => {
        state.loading.detail = false;
        state.currentVector = action.payload;
        
        // Update in list if exists
        const index = state.vectors.findIndex(v => v.position_id === action.payload.position_id);
        if (index >= 0) {
          state.vectors[index] = action.payload;
        }
      })
      .addCase(fetchVectorByPosition.rejected, (state, action) => {
        state.loading.detail = false;
        state.errors.detail = action.error.message || 'Failed to fetch vector';
      });

    // Fetch vector list
    builder
      .addCase(fetchVectorList.pending, (state) => {
        state.loading.list = true;
        state.errors.list = null;
      })
      .addCase(fetchVectorList.fulfilled, (state, action) => {
        state.loading.list = false;
        state.vectors = action.payload.response.items;
        state.lastFetch = Date.now();
        state.cacheValid = true;
      })
      .addCase(fetchVectorList.rejected, (state, action) => {
        state.loading.list = false;
        state.errors.list = action.error.message || 'Failed to fetch vectors';
      });

    // Generate embedding
    builder
      .addCase(generateVectorEmbedding.pending, (state) => {
        state.loading.generate = true;
        state.errors.generate = null;
      })
      .addCase(generateVectorEmbedding.fulfilled, (state, action) => {
        state.loading.generate = false;
        state.backgroundTasks[action.payload.task.task_id] = action.payload.task;
        state.generationQueue.positionIds.push(action.payload.positionId);
      })
      .addCase(generateVectorEmbedding.rejected, (state, action) => {
        state.loading.generate = false;
        state.errors.generate = action.error.message || 'Failed to generate embedding';
      });

    // Batch generate embeddings
    builder
      .addCase(batchGenerateEmbeddings.pending, (state) => {
        state.loading.batchGenerate = true;
        state.errors.batchGenerate = null;
      })
      .addCase(batchGenerateEmbeddings.fulfilled, (state, action) => {
        state.loading.batchGenerate = false;
        state.backgroundTasks[action.payload.task.task_id] = action.payload.task;
        state.generationQueue.positionIds = [
          ...state.generationQueue.positionIds,
          ...action.payload.params.position_ids
        ];
      })
      .addCase(batchGenerateEmbeddings.rejected, (state, action) => {
        state.loading.batchGenerate = false;
        state.errors.batchGenerate = action.error.message || 'Failed to batch generate embeddings';
      });

    // Delete vector
    builder
      .addCase(deleteVector.pending, (state) => {
        state.loading.delete = true;
        state.errors.delete = null;
      })
      .addCase(deleteVector.fulfilled, (state, action) => {
        state.loading.delete = false;
        state.vectors = state.vectors.filter(v => v.position_id !== action.payload);
        if (state.currentVector?.position_id === action.payload) {
          state.currentVector = null;
        }
      })
      .addCase(deleteVector.rejected, (state, action) => {
        state.loading.delete = false;
        state.errors.delete = action.error.message || 'Failed to delete vector';
      });

    // Search vectors
    builder
      .addCase(searchVectors.pending, (state) => {
        state.search.loading = true;
        state.search.error = null;
      })
      .addCase(searchVectors.fulfilled, (state, action) => {
        state.search.loading = false;
        state.search.results = action.payload.results;
        state.search.lastParams = action.payload.params;
      })
      .addCase(searchVectors.rejected, (state, action) => {
        state.search.loading = false;
        state.search.error = action.error.message || 'Search failed';
      });

    // Fetch stats
    builder
      .addCase(fetchVectorStats.pending, (state) => {
        state.loading.stats = true;
        state.errors.stats = null;
      })
      .addCase(fetchVectorStats.fulfilled, (state, action) => {
        state.loading.stats = false;
        state.stats = action.payload;
      })
      .addCase(fetchVectorStats.rejected, (state, action) => {
        state.loading.stats = false;
        state.errors.stats = action.error.message || 'Failed to fetch stats';
      });

    // Fetch provider info
    builder
      .addCase(fetchProviderInfo.pending, (state) => {
        state.loading.providers = true;
        state.errors.providers = null;
      })
      .addCase(fetchProviderInfo.fulfilled, (state, action) => {
        state.loading.providers = false;
        state.providers = action.payload;
      })
      .addCase(fetchProviderInfo.rejected, (state, action) => {
        state.loading.providers = false;
        state.errors.providers = action.error.message || 'Failed to fetch provider info';
      });

    // Test embedding
    builder
      .addCase(testEmbedding.pending, (state) => {
        state.loading.testEmbedding = true;
        state.errors.testEmbedding = null;
      })
      .addCase(testEmbedding.fulfilled, (state, action) => {
        state.loading.testEmbedding = false;
        state.lastTestResult = action.payload;
      })
      .addCase(testEmbedding.rejected, (state, action) => {
        state.loading.testEmbedding = false;
        state.errors.testEmbedding = action.error.message || 'Test embedding failed';
      });

    // Fetch activities
    builder
      .addCase(fetchVectorActivities.pending, (state) => {
        state.loading.activities = true;
        state.errors.activities = null;
      })
      .addCase(fetchVectorActivities.fulfilled, (state, action) => {
        state.loading.activities = false;
        state.activities = action.payload.items;
      })
      .addCase(fetchVectorActivities.rejected, (state, action) => {
        state.loading.activities = false;
        state.errors.activities = action.error.message || 'Failed to fetch activities';
      });

    // Fetch performance metrics
    builder
      .addCase(fetchPerformanceMetrics.pending, (state) => {
        state.loading.performance = true;
        state.errors.performance = null;
      })
      .addCase(fetchPerformanceMetrics.fulfilled, (state, action) => {
        state.loading.performance = false;
        state.performance = action.payload;
      })
      .addCase(fetchPerformanceMetrics.rejected, (state, action) => {
        state.loading.performance = false;
        state.errors.performance = action.error.message || 'Failed to fetch performance metrics';
      });

    // Check health
    builder
      .addCase(checkProviderHealth.pending, (state) => {
        state.loading.health = true;
        state.errors.health = null;
      })
      .addCase(checkProviderHealth.fulfilled, (state, action) => {
        state.loading.health = false;
        const { provider, health } = action.payload;
        if (provider === 'ollama' || provider === 'openai') {
          state.health[provider] = health;
        } else {
          // Update all providers if no specific provider
          state.health.ollama = health;
          state.health.openai = health;
        }
      })
      .addCase(checkProviderHealth.rejected, (state, action) => {
        state.loading.health = false;
        state.errors.health = action.error.message || 'Health check failed';
      });
  },
});

// Export actions
export const {
  setSearchQuery,
  clearSearchResults,
  addToGenerationQueue,
  removeFromGenerationQueue,
  markGenerationCompleted,
  markGenerationFailed,
  clearGenerationQueue,
  setCurrentProvider,
  clearError,
  clearAllErrors,
  invalidateCache,
  updateBackgroundTask,
  clearBackgroundTask,
  updateVectorLocal,
  removeVectorLocal,
  updateProviderHealth,
} = vectorSlice.actions;

// Export reducer
export default vectorSlice.reducer;