import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { positionService } from '@/services/position';
import { 
  Position, 
  PositionCreate, 
  PositionUpdate, 
  PositionListResponse,
  PositionListParams,
  MatchParams,
  CandidateMatch,
  SharePositionParams,
  BulkPositionAction,
  ExportParams,
  PositionAnalytics,
  TaskInfo,
  PositionStatus,
  PositionUrgency,
  DataPermission
} from '@/types';

// Loading states interface
interface LoadingStates {
  list: boolean;
  detail: boolean;
  create: boolean;
  update: boolean;
  delete: boolean;
  share: boolean;
  generateEmbeddings: boolean;
  matchCandidates: boolean;
  findSimilar: boolean;
  bulkAction: boolean;
  export: boolean;
  analytics: boolean;
}

// Error states interface
interface ErrorStates {
  list: string | null;
  detail: string | null;
  create: string | null;
  update: string | null;
  delete: string | null;
  share: string | null;
  generateEmbeddings: string | null;
  matchCandidates: string | null;
  findSimilar: string | null;
  bulkAction: string | null;
  export: string | null;
  analytics: string | null;
}

// Pagination state - Following CLAUDE.md standard: {items, total, skip, limit}
interface PaginationState {
  skip: number;
  limit: number;
  total: number;
}

// Search filters state
interface SearchFiltersState extends PositionListParams {
  order_by?: string;
  order_desc?: boolean;
}

// Bulk operations state
interface BulkOperationsState {
  selectedIds: string[];
  operation: string | null;
  progress: number;
  taskId: string | null;
}

// Matching state
interface MatchingState {
  results: CandidateMatch | null;
  loading: boolean;
  error: string | null;
  lastParams: MatchParams | null;
}

// Similar positions state
interface SimilarPositionsState {
  [positionId: string]: Position[];
}

// Main position state interface
interface PositionState {
  // Data
  positions: Position[];
  currentPosition: Position | null;
  similarPositions: SimilarPositionsState;
  
  // UI state
  pagination: PaginationState;
  filters: SearchFiltersState;
  bulkOperations: BulkOperationsState;
  matching: MatchingState;
  
  // Loading states
  loading: LoadingStates;
  
  // Error states
  errors: ErrorStates;
  
  // Analytics data
  analytics: PositionAnalytics | null;
  
  // Background tasks
  backgroundTasks: Record<string, TaskInfo>;
  
  // Cache metadata
  lastFetch: number | null;
  cacheValid: boolean;
}

// Initial state
const initialState: PositionState = {
  // Data
  positions: [],
  currentPosition: null,
  similarPositions: {},
  
  // UI state
  pagination: {
    skip: 0,
    limit: 20,
    total: 0,
  },
  filters: {
    search: '',
    department: undefined,
    location: undefined,
    status: undefined,
    urgency: undefined,
    skills: [],
    salary_min: undefined,
    salary_max: undefined,
    data_permission: undefined,
    created_after: undefined,
    created_before: undefined,
    order_by: 'created_at',
    order_desc: true,
  },
  bulkOperations: {
    selectedIds: [],
    operation: null,
    progress: 0,
    taskId: null,
  },
  matching: {
    results: null,
    loading: false,
    error: null,
    lastParams: null,
  },
  
  // Loading states
  loading: {
    list: false,
    detail: false,
    create: false,
    update: false,
    delete: false,
    share: false,
    generateEmbeddings: false,
    matchCandidates: false,
    findSimilar: false,
    bulkAction: false,
    export: false,
    analytics: false,
  },
  
  // Error states
  errors: {
    list: null,
    detail: null,
    create: null,
    update: null,
    delete: null,
    share: null,
    generateEmbeddings: null,
    matchCandidates: null,
    findSimilar: null,
    bulkAction: null,
    export: null,
    analytics: null,
  },
  
  // Analytics data
  analytics: null,
  
  // Background tasks
  backgroundTasks: {},
  
  // Cache metadata
  lastFetch: null,
  cacheValid: false,
};

// Async thunks
export const fetchPositions = createAsyncThunk(
  'positions/fetchList',
  async (params: PositionListParams = {}) => {
    const response = await positionService.getList(params);
    return { response, params };
  }
);

export const fetchPositionById = createAsyncThunk(
  'positions/fetchById',
  async (id: string) => {
    const position = await positionService.getById(id);
    return position;
  }
);

export const createPosition = createAsyncThunk(
  'positions/create',
  async (data: PositionCreate) => {
    const position = await positionService.create(data);
    return position;
  }
);

export const updatePosition = createAsyncThunk(
  'positions/update',
  async ({ id, data }: { id: string; data: PositionUpdate }) => {
    const position = await positionService.update(id, data);
    return position;
  }
);

export const deletePosition = createAsyncThunk(
  'positions/delete',
  async (id: string) => {
    await positionService.delete(id);
    return id;
  }
);

export const sharePosition = createAsyncThunk(
  'positions/share',
  async ({ id, params }: { id: string; params: SharePositionParams }) => {
    const position = await positionService.share(id, params);
    return { id, position };
  }
);

export const unsharePosition = createAsyncThunk(
  'positions/unshare',
  async ({ id, userIds }: { id: string; userIds: string[] }) => {
    const position = await positionService.unshare(id, userIds);
    return { id, position };
  }
);

export const generateEmbeddings = createAsyncThunk(
  'positions/generateEmbeddings',
  async ({ id, provider }: { id: string; provider?: string }) => {
    const task = await positionService.generateEmbeddings(id, provider);
    return { positionId: id, task };
  }
);

export const matchCandidates = createAsyncThunk(
  'positions/matchCandidates',
  async ({ id, params }: { id: string; params: MatchParams }) => {
    const results = await positionService.matchCandidates(id, params);
    return { positionId: id, results, params };
  }
);

export const findSimilarPositions = createAsyncThunk(
  'positions/findSimilar',
  async ({ id, limit }: { id: string; limit?: number }) => {
    const similar = await positionService.findSimilar(id, limit);
    return { positionId: id, similar };
  }
);

export const fetchPositionAnalytics = createAsyncThunk(
  'positions/fetchAnalytics',
  async (id: string) => {
    const analytics = await positionService.getAnalytics(id);
    return { positionId: id, analytics };
  }
);

export const bulkActionPositions = createAsyncThunk(
  'positions/bulkAction',
  async (action: BulkPositionAction) => {
    const task = await positionService.bulkAction(action);
    return { action, task };
  }
);

export const exportPositions = createAsyncThunk(
  'positions/export',
  async ({ positionIds, params }: { positionIds: string[]; params: ExportParams }) => {
    const result = await positionService.export(positionIds, params);
    return result;
  }
);

export const checkDuplicatePosition = createAsyncThunk(
  'positions/checkDuplicate',
  async (params: {
    title: string;
    department: string;
    exclude_id?: string;
  }) => {
    const response = await positionService.checkDuplicate(params);
    return response;
  }
);

export const fetchPositionStats = createAsyncThunk(
  'positions/fetchStats',
  async () => {
    const stats = await positionService.getStats();
    return stats;
  }
);

export const fetchTaskStatus = createAsyncThunk(
  'positions/fetchTaskStatus',
  async (taskId: string) => {
    const task = await positionService.getTaskStatus(taskId);
    return task;
  }
);

// Position slice
const positionSlice = createSlice({
  name: 'positions',
  initialState,
  reducers: {
    // Filter actions
    setFilters: (state, action: PayloadAction<Partial<SearchFiltersState>>) => {
      state.filters = { ...state.filters, ...action.payload };
      state.cacheValid = false;
    },
    clearFilters: (state) => {
      state.filters = initialState.filters;
      state.cacheValid = false;
    },
    
    // Pagination actions
    setPagination: (state, action: PayloadAction<Partial<PaginationState>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    
    // Current position actions
    setCurrentPosition: (state, action: PayloadAction<Position | null>) => {
      state.currentPosition = action.payload;
    },
    clearCurrentPosition: (state) => {
      state.currentPosition = null;
    },
    
    // Bulk operations actions
    togglePositionSelection: (state, action: PayloadAction<string>) => {
      const id = action.payload;
      const index = state.bulkOperations.selectedIds.indexOf(id);
      if (index >= 0) {
        state.bulkOperations.selectedIds.splice(index, 1);
      } else {
        state.bulkOperations.selectedIds.push(id);
      }
    },
    selectAllPositions: (state) => {
      state.bulkOperations.selectedIds = state.positions.map(p => p.id);
    },
    clearPositionSelection: (state) => {
      state.bulkOperations.selectedIds = [];
    },
    setBulkOperation: (state, action: PayloadAction<string | null>) => {
      state.bulkOperations.operation = action.payload;
      state.bulkOperations.progress = 0;
    },
    setBulkProgress: (state, action: PayloadAction<number>) => {
      state.bulkOperations.progress = action.payload;
    },
    
    // Error actions
    clearError: (state, action: PayloadAction<keyof ErrorStates>) => {
      state.errors[action.payload] = null;
    },
    clearAllErrors: (state) => {
      Object.keys(state.errors).forEach(key => {
        state.errors[key as keyof ErrorStates] = null;
      });
    },
    
    // Cache actions
    invalidateCache: (state) => {
      state.cacheValid = false;
    },
    
    // Local position updates (optimistic updates)
    updatePositionLocal: (state, action: PayloadAction<Position>) => {
      const index = state.positions.findIndex(p => p.id === action.payload.id);
      if (index >= 0) {
        state.positions[index] = action.payload;
      }
      if (state.currentPosition?.id === action.payload.id) {
        state.currentPosition = action.payload;
      }
    },
    
    // Add position locally (optimistic create)
    addPositionLocal: (state, action: PayloadAction<Position>) => {
      state.positions.unshift(action.payload);
      state.pagination.total += 1;
    },
    
    // Remove position locally (optimistic delete)
    removePositionLocal: (state, action: PayloadAction<string>) => {
      state.positions = state.positions.filter(p => p.id !== action.payload);
      state.pagination.total -= 1;
      if (state.currentPosition?.id === action.payload) {
        state.currentPosition = null;
      }
      // Remove from selection
      state.bulkOperations.selectedIds = state.bulkOperations.selectedIds.filter(
        id => id !== action.payload
      );
    },
    
    // Matching actions
    clearMatchingResults: (state) => {
      state.matching.results = null;
      state.matching.error = null;
      state.matching.lastParams = null;
    },
    
    // Background task management
    updateBackgroundTask: (state, action: PayloadAction<TaskInfo>) => {
      state.backgroundTasks[action.payload.task_id] = action.payload;
    },
    clearBackgroundTask: (state, action: PayloadAction<string>) => {
      delete state.backgroundTasks[action.payload];
    },
  },
  extraReducers: (builder) => {
    // Fetch positions list
    builder
      .addCase(fetchPositions.pending, (state) => {
        state.loading.list = true;
        state.errors.list = null;
      })
      .addCase(fetchPositions.fulfilled, (state, action) => {
        state.loading.list = false;
        state.positions = action.payload.response.items;
        state.pagination = {
          skip: action.payload.params.skip || 0,
          limit: action.payload.params.limit || 20,
          total: action.payload.response.total,
        };
        state.lastFetch = Date.now();
        state.cacheValid = true;
      })
      .addCase(fetchPositions.rejected, (state, action) => {
        state.loading.list = false;
        state.errors.list = action.error.message || 'Failed to fetch positions';
      });

    // Fetch position by ID
    builder
      .addCase(fetchPositionById.pending, (state) => {
        state.loading.detail = true;
        state.errors.detail = null;
      })
      .addCase(fetchPositionById.fulfilled, (state, action) => {
        state.loading.detail = false;
        state.currentPosition = action.payload;
        
        // Update in list if exists
        const index = state.positions.findIndex(p => p.id === action.payload.id);
        if (index >= 0) {
          state.positions[index] = action.payload;
        }
      })
      .addCase(fetchPositionById.rejected, (state, action) => {
        state.loading.detail = false;
        state.errors.detail = action.error.message || 'Failed to fetch position';
      });

    // Create position
    builder
      .addCase(createPosition.pending, (state) => {
        state.loading.create = true;
        state.errors.create = null;
      })
      .addCase(createPosition.fulfilled, (state, action) => {
        state.loading.create = false;
        state.positions.unshift(action.payload);
        state.pagination.total += 1;
        state.currentPosition = action.payload;
      })
      .addCase(createPosition.rejected, (state, action) => {
        state.loading.create = false;
        state.errors.create = action.error.message || 'Failed to create position';
      });

    // Update position
    builder
      .addCase(updatePosition.pending, (state) => {
        state.loading.update = true;
        state.errors.update = null;
      })
      .addCase(updatePosition.fulfilled, (state, action) => {
        state.loading.update = false;
        
        // Update in list
        const index = state.positions.findIndex(p => p.id === action.payload.id);
        if (index >= 0) {
          state.positions[index] = action.payload;
        }
        
        // Update current position if it matches
        if (state.currentPosition?.id === action.payload.id) {
          state.currentPosition = action.payload;
        }
      })
      .addCase(updatePosition.rejected, (state, action) => {
        state.loading.update = false;
        state.errors.update = action.error.message || 'Failed to update position';
      });

    // Delete position
    builder
      .addCase(deletePosition.pending, (state) => {
        state.loading.delete = true;
        state.errors.delete = null;
      })
      .addCase(deletePosition.fulfilled, (state, action) => {
        state.loading.delete = false;
        state.positions = state.positions.filter(p => p.id !== action.payload);
        state.pagination.total -= 1;
        
        // Clear current if it was deleted
        if (state.currentPosition?.id === action.payload) {
          state.currentPosition = null;
        }
        
        // Remove from selection
        state.bulkOperations.selectedIds = state.bulkOperations.selectedIds.filter(
          id => id !== action.payload
        );
      })
      .addCase(deletePosition.rejected, (state, action) => {
        state.loading.delete = false;
        state.errors.delete = action.error.message || 'Failed to delete position';
      });

    // Share position
    builder
      .addCase(sharePosition.pending, (state) => {
        state.loading.share = true;
        state.errors.share = null;
      })
      .addCase(sharePosition.fulfilled, (state, action) => {
        state.loading.share = false;
        
        // Update position in list and current
        const updatedPosition = action.payload.position;
        const index = state.positions.findIndex(p => p.id === action.payload.id);
        if (index >= 0) {
          state.positions[index] = updatedPosition;
        }
        if (state.currentPosition?.id === action.payload.id) {
          state.currentPosition = updatedPosition;
        }
      })
      .addCase(sharePosition.rejected, (state, action) => {
        state.loading.share = false;
        state.errors.share = action.error.message || 'Failed to share position';
      });

    // Generate embeddings
    builder
      .addCase(generateEmbeddings.pending, (state) => {
        state.loading.generateEmbeddings = true;
        state.errors.generateEmbeddings = null;
      })
      .addCase(generateEmbeddings.fulfilled, (state, action) => {
        state.loading.generateEmbeddings = false;
        state.backgroundTasks[action.payload.task.task_id] = action.payload.task;
      })
      .addCase(generateEmbeddings.rejected, (state, action) => {
        state.loading.generateEmbeddings = false;
        state.errors.generateEmbeddings = action.error.message || 'Failed to generate embeddings';
      });

    // Match candidates
    builder
      .addCase(matchCandidates.pending, (state) => {
        state.matching.loading = true;
        state.matching.error = null;
      })
      .addCase(matchCandidates.fulfilled, (state, action) => {
        state.matching.loading = false;
        state.matching.results = action.payload.results;
        state.matching.lastParams = action.payload.params;
      })
      .addCase(matchCandidates.rejected, (state, action) => {
        state.matching.loading = false;
        state.matching.error = action.error.message || 'Failed to match candidates';
      });

    // Find similar positions
    builder
      .addCase(findSimilarPositions.pending, (state) => {
        state.loading.findSimilar = true;
        state.errors.findSimilar = null;
      })
      .addCase(findSimilarPositions.fulfilled, (state, action) => {
        state.loading.findSimilar = false;
        state.similarPositions[action.payload.positionId] = action.payload.similar;
      })
      .addCase(findSimilarPositions.rejected, (state, action) => {
        state.loading.findSimilar = false;
        state.errors.findSimilar = action.error.message || 'Failed to find similar positions';
      });

    // Fetch analytics
    builder
      .addCase(fetchPositionAnalytics.pending, (state) => {
        state.loading.analytics = true;
        state.errors.analytics = null;
      })
      .addCase(fetchPositionAnalytics.fulfilled, (state, action) => {
        state.loading.analytics = false;
        state.analytics = action.payload.analytics;
      })
      .addCase(fetchPositionAnalytics.rejected, (state, action) => {
        state.loading.analytics = false;
        state.errors.analytics = action.error.message || 'Failed to fetch analytics';
      });

    // Bulk actions
    builder
      .addCase(bulkActionPositions.pending, (state) => {
        state.loading.bulkAction = true;
        state.errors.bulkAction = null;
        state.bulkOperations.operation = 'processing';
        state.bulkOperations.progress = 0;
      })
      .addCase(bulkActionPositions.fulfilled, (state, action) => {
        state.loading.bulkAction = false;
        state.bulkOperations.taskId = action.payload.task.task_id;
        state.backgroundTasks[action.payload.task.task_id] = action.payload.task;
      })
      .addCase(bulkActionPositions.rejected, (state, action) => {
        state.loading.bulkAction = false;
        state.errors.bulkAction = action.error.message || 'Bulk action failed';
        state.bulkOperations.operation = null;
      });

    // Export positions
    builder
      .addCase(exportPositions.pending, (state) => {
        state.loading.export = true;
        state.errors.export = null;
      })
      .addCase(exportPositions.fulfilled, (state) => {
        state.loading.export = false;
        // Export success - download URL is handled in component
      })
      .addCase(exportPositions.rejected, (state, action) => {
        state.loading.export = false;
        state.errors.export = action.error.message || 'Failed to export positions';
      });

    // Task status updates
    builder
      .addCase(fetchTaskStatus.fulfilled, (state, action) => {
        state.backgroundTasks[action.payload.task_id] = action.payload;
      });
  },
});

// Export actions
export const {
  setFilters,
  clearFilters,
  setPagination,
  setCurrentPosition,
  clearCurrentPosition,
  togglePositionSelection,
  selectAllPositions,
  clearPositionSelection,
  setBulkOperation,
  setBulkProgress,
  clearError,
  clearAllErrors,
  invalidateCache,
  updatePositionLocal,
  addPositionLocal,
  removePositionLocal,
  clearMatchingResults,
  updateBackgroundTask,
  clearBackgroundTask,
} = positionSlice.actions;

// Export reducer
export default positionSlice.reducer;