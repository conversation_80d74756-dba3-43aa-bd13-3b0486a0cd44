# Mock Data Implementation for Talent Pool Module

## Overview
This document describes the mock data implementation for the talent pool module, which handles all API calls that currently return 404 from the backend.

## Architecture

### 1. Mock Service (`talentPoolMock.ts`)
- Provides mock data for all talent pool operations
- Simulates network delays for realistic behavior
- Maintains state for CRUD operations during the session
- Generates realistic candidate data with proper relationships

### 2. Service Wrapper (`talentPoolWithMock.ts`)
- Wraps the original `TalentPoolService`
- Automatically catches 404 errors and falls back to mock data
- Logs when mock data is being used
- Preserves original API structure for easy migration

### 3. Mock Data Features

#### Candidates
- 50 mock candidates with realistic data
- Random distribution across pipeline stages
- Varied engagement levels and availability
- Skills, experience, and salary data
- Contact information and LinkedIn profiles

#### Filter Sets
- 3 pre-configured filter sets
- Senior Engineers filter
- Product Managers filter
- High Priority follow-up filter

#### Campaigns
- 2 mock campaigns with different statuses
- Recruitment metrics and conversion data
- Start/end dates and progress tracking

#### Analytics
- Pipeline distribution across all stages
- Engagement metrics
- Source distribution
- Conversion rates
- Time in stage metrics
- Top skills analysis
- Location distribution
- Monthly trends

#### Reports
- Monthly pipeline analysis report
- Engagement metrics report
- Export functionality (mock URLs)

## Usage

### Switching Between Real and Mock APIs

#### Current Setup (With Mock Fallback)
```typescript
// In page components
import { talentPoolService } from '@/services/talentPoolWithMock';
```

#### Future Setup (When Backend is Ready)
```typescript
// Switch back to real service
import { talentPoolService } from '@/services/talentPool';
```

### How It Works

1. **API Call Attempt**: First tries the real backend API
2. **404 Detection**: If backend returns 404, catches the error
3. **Mock Fallback**: Automatically returns mock data instead
4. **Console Logging**: Logs when using mock data (not errors)
5. **Seamless Experience**: User sees no error messages

### Example Console Output
```
📝 Using mock data for saved filter sets (backend not implemented)
📝 Using mock data for campaigns (backend not implemented)
📝 Using mock data for analytics (backend not implemented)
```

## Mock Data Capabilities

### Supported Operations
- ✅ Get candidates (with filtering and pagination)
- ✅ Get/save filter sets
- ✅ Get/create campaigns
- ✅ Get reports
- ✅ Get analytics
- ✅ Bulk operations
- ✅ Export data
- ✅ Import data
- ✅ Move candidates between stages
- ✅ Archive/unarchive candidates

### Data Persistence
- Mock data persists during the browser session
- Changes (like saving filter sets) are maintained in memory
- Data resets on page refresh

## Files Modified

### Core Files
1. `/services/talentPoolMock.ts` - Mock data service
2. `/services/talentPoolWithMock.ts` - Service wrapper with fallback
3. `/app/(dashboard)/recruitment/talent-pool/page.tsx` - Updated imports

### Documentation
1. `/TODO_BACKEND_APIS.md` - List of required backend APIs
2. `/MOCK_DATA_IMPLEMENTATION.md` - This file

## Migration Path

When backend APIs are ready:

1. **Test Backend APIs**: Ensure all endpoints work correctly
2. **Update Imports**: Change from `talentPoolWithMock` to `talentPool`
3. **Remove Mock Files**: Delete mock services after successful migration
4. **Update Documentation**: Remove TODO comments

## Benefits

1. **No Console Errors**: 404s are handled gracefully
2. **Functional UI**: All features work with mock data
3. **Realistic Experience**: Mock data simulates real scenarios
4. **Easy Migration**: Simple import change when backend is ready
5. **Development Continues**: Frontend team can work independently

## Testing

### Verify Mock Data is Working
1. Open talent pool page: http://localhost:8088/recruitment/talent-pool
2. Check console for "📝 Using mock data" messages (not errors)
3. Test filter sets dropdown - should show 3 mock filters
4. Test pipeline view - should show candidates in different stages
5. Test analytics - should show charts and metrics

### Performance
- Mock API calls include artificial delays (200-1500ms)
- Simulates realistic network behavior
- Helps identify potential loading state issues

## Notes for Backend Team

The mock service implementation shows exactly what data structure the frontend expects. Use the mock data structures as a reference when implementing the real APIs:

- Response format for list endpoints
- Pagination structure
- Filter parameter handling
- Analytics data structure
- Bulk operation responses

All mock data follows the TypeScript interfaces defined in `/types/recruitment.ts`.