# Testing Strategy & Documentation

## Overview

This document outlines the comprehensive testing strategy for the TalentForge Pro frontend, covering unit tests, integration tests, E2E tests, accessibility tests, and internationalization tests.

## 📊 Testing Pyramid

```
    /\
   /  \  E2E Tests (Critical User Journeys)
  /____\
 /      \  Integration Tests (Component + API)
/__________\
\          / Unit Tests (Services, Utils, Hooks)
 \________/
```

## 🎯 Coverage Goals

- **Overall Coverage**: >80%
- **Service Layer**: >90% (business logic critical)
- **Redux Slices**: >90% (state management critical)
- **React Components**: >80% (UI behavior)
- **E2E Tests**: 100% of critical user paths

## 🛠️ Testing Infrastructure

### Technologies Used

- **Jest**: Unit testing framework
- **React Testing Library**: Component testing with user-centric approach
- **Playwright**: E2E testing with cross-browser support
- **MSW (Mock Service Worker)**: API mocking
- **jest-axe**: Accessibility testing
- **TypeScript**: Type-safe tests

### Configuration Files

- `jest.config.js` - Jest configuration with coverage thresholds
- `jest.setup.js` - Global test setup and mocks
- `playwright.config.ts` - Playwright E2E configuration
- `__mocks__/` - Mock implementations and test data

## 📂 Test Organization

```
app/frontend/
├── __tests__/
│   ├── utils/
│   │   ├── test-utils.tsx     # RTL helpers and providers
│   │   └── mock-data.ts       # Reusable test data
│   ├── accessibility/
│   │   └── *.a11y.test.tsx    # Accessibility tests
│   └── i18n/
│       └── *.i18n.test.tsx    # Internationalization tests
├── services/
│   └── __tests__/
│       └── *.test.ts          # Service layer tests
├── stores/
│   └── __tests__/
│       └── *.test.ts          # Redux slice tests
├── app/
│   └── (dashboard)/
│       └── candidates/
│           └── __tests__/
│               └── *.test.tsx # Page component tests
└── e2e/
    ├── utils/
    │   ├── auth-helper.ts     # E2E authentication utilities
    │   └── candidate-helper.ts # E2E candidate operations
    ├── fixtures/
    │   └── test-data.ts       # E2E test data
    └── *.spec.ts              # E2E test suites
```

## 🧪 Unit Tests

### Service Layer Tests (`services/__tests__/`)

**Purpose**: Test API interactions and business logic

**Examples**:
- API method calls and responses
- Validation functions
- Error handling
- Data transformation

```typescript
// Example: Testing candidate service
describe('CandidateService', () => {
  it('should fetch candidate list with correct parameters', async () => {
    mockApiClient.get.mockResolvedValue(mockCandidateListResponse)
    
    const result = await candidateService.getList({ skip: 20, limit: 50 })
    
    expect(mockApiClient.get).toHaveBeenCalledWith('/candidates/', {
      params: { skip: 20, limit: 50 }
    })
    expect(result).toEqual(mockCandidateListResponse)
  })
})
```

### Redux Store Tests (`stores/__tests__/`)

**Purpose**: Test state management logic

**Examples**:
- Action creators
- Reducers
- Async thunks
- State updates

```typescript
// Example: Testing Redux slice
describe('candidateSlice', () => {
  it('should handle successful candidate creation', async () => {
    await store.dispatch(createCandidate(mockCandidateCreate))
    
    const state = store.getState().candidates
    expect(state.loading.create).toBe(false)
    expect(state.candidates[0]).toEqual(expect.objectContaining(mockCandidateCreate))
  })
})
```

### Component Tests (`app/**/__tests__/`)

**Purpose**: Test user interactions and UI behavior

**Examples**:
- User interactions (clicks, form inputs)
- Conditional rendering
- Props handling
- Event handling

```typescript
// Example: Testing component interaction
describe('CandidatesPage', () => {
  it('should navigate to create page when add button is clicked', async () => {
    renderWithProviders(<CandidatesPage />)
    
    const addButton = screen.getByText('candidates.addCandidate')
    await user.click(addButton)
    
    expect(mockPush).toHaveBeenCalledWith('/candidates/new')
  })
})
```

## 🔗 Integration Tests

### API Integration

**Purpose**: Test component-API interactions with MSW

```typescript
// Example: Testing API integration
test('should display candidates from API', async () => {
  // MSW will intercept and mock the API call
  renderWithProviders(<CandidatesPage />)
  
  await waitFor(() => {
    expect(screen.getByText('John Doe')).toBeInTheDocument()
  })
})
```

### State Integration

**Purpose**: Test component-Redux integration

```typescript
// Example: Testing Redux integration
test('should update filters in store when search is performed', async () => {
  const { store } = renderWithProviders(<CandidatesPage />)
  
  const searchInput = screen.getByPlaceholderText('Search...')
  await user.type(searchInput, 'John')
  
  expect(store.getState().candidates.filters.search).toBe('John')
})
```

## 🎭 E2E Tests

### Critical User Journeys

**1. Browse and View Candidates**
- Login → Navigate to candidates → Search/filter → View details

**2. Create New Candidate**
- Navigate to create form → Fill information → Upload resume → Submit

**3. Edit Existing Candidate**
- Find candidate → Edit information → Save → Verify changes

**4. Bulk Operations**
- Select candidates → Export → Verify download

### E2E Test Structure

```typescript
// Example: E2E test with helpers
test('should complete candidate creation journey', async ({ page }) => {
  const auth = new AuthHelper(page)
  const candidate = new CandidateHelper(page)
  
  await auth.login('admin')
  await candidate.createCandidate(testCandidates.newCandidate)
  
  expect(await candidate.verifyCandidateExists('Test Candidate')).toBe(true)
})
```

## ♿ Accessibility Tests

### Test Categories

**1. Automated Accessibility**
- WCAG compliance with jest-axe
- Color contrast ratios
- Keyboard navigation
- ARIA attributes

**2. Screen Reader Support**
- Semantic HTML structure
- Proper headings hierarchy
- Form labels and descriptions

**3. Keyboard Navigation**
- Tab order
- Focus management
- Keyboard shortcuts

```typescript
// Example: Accessibility test
test('should not have accessibility violations', async () => {
  const { container } = renderWithProviders(<CandidatesPage />)
  const results = await axe(container)
  expect(results).toHaveNoViolations()
})
```

## 🌍 Internationalization Tests

### Test Categories

**1. Text Localization**
- English and Chinese translations
- Missing translation fallbacks
- Pluralization rules

**2. Cultural Adaptation**
- Date/number formatting
- RTL language support
- Context-aware translations

```typescript
// Example: I18n test
test('should display Chinese text correctly', () => {
  // Mock Chinese locale
  const { getByText } = renderWithProviders(<CandidatesPage />)
  expect(getByText('候选人')).toBeInTheDocument()
})
```

## 🚀 Running Tests

### Development Commands

```bash
# Unit tests
pnpm test                    # Run all tests
pnpm test:watch             # Watch mode
pnpm test:coverage          # With coverage report
pnpm test:ui                # UI mode with verbose output

# E2E tests
pnpm test:e2e               # Headless mode
pnpm test:e2e:ui            # Interactive UI mode
pnpm test:e2e:headed        # Headed browser mode

# Specific test types
pnpm test services/         # Service tests only
pnpm test stores/          # Redux tests only
pnpm test __tests__/accessibility/  # A11y tests only
pnpm test __tests__/i18n/          # I18n tests only
```

### CI/CD Integration

**GitHub Actions Workflow** (`.github/workflows/test.yml`):

1. **Lint and Type Check** - Code quality validation
2. **Unit Tests** - All unit and integration tests with coverage
3. **Accessibility Tests** - WCAG compliance verification
4. **I18n Tests** - Localization validation
5. **E2E Tests** - Critical user journey validation
6. **Performance Tests** - Lighthouse audits
7. **Security Scan** - Dependency vulnerabilities

### Coverage Reports

- **HTML Report**: `coverage/lcov-report/index.html`
- **LCOV Format**: `coverage/lcov.info` (for CI integration)
- **Text Summary**: Console output during test runs

## 🎯 Best Practices

### Unit Testing

1. **User-Centric Approach**: Test behavior, not implementation
2. **Mock External Dependencies**: Use MSW for API, mock complex services
3. **Test Edge Cases**: Error states, empty data, validation failures
4. **Descriptive Names**: Test names should explain the scenario

### E2E Testing

1. **Critical Paths Only**: Focus on business-critical user journeys
2. **Stable Selectors**: Use `data-testid` for reliable element selection
3. **Helper Functions**: Reusable actions (login, navigation, forms)
4. **Parallel Execution**: Run tests across multiple browsers

### Accessibility Testing

1. **Automated + Manual**: Combine jest-axe with manual testing
2. **Real Users**: Include users with disabilities in testing
3. **Multiple Devices**: Test on various screen sizes and input methods
4. **Color Blindness**: Verify information isn't color-dependent only

### Performance Considerations

1. **Test Isolation**: Each test should be independent
2. **Cleanup**: Properly cleanup after tests (DOM, timers, listeners)
3. **Parallel Execution**: Use Jest's parallel capabilities
4. **Mock Heavy Operations**: Avoid real network calls in unit tests

## 🔧 Troubleshooting

### Common Issues

**1. Tests Timing Out**
```typescript
// Increase timeout for slow operations
test('slow operation', async () => {
  // ...
}, 10000) // 10 second timeout
```

**2. MSW Not Working**
```typescript
// Ensure MSW server is properly set up in jest.setup.js
beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())
```

**3. Component Not Rendering**
```typescript
// Use renderWithProviders for components that need Redux/QueryClient
const { getByText } = renderWithProviders(<MyComponent />)
```

**4. E2E Tests Flaky**
```typescript
// Use proper waiting strategies
await expect(page.locator('text=Expected')).toBeVisible()
// Instead of page.waitForTimeout()
```

### Debug Tips

1. **Console Logs**: Use `screen.debug()` to see current DOM state
2. **Test Names**: Run specific tests with `pnpm test -- --testNamePattern="pattern"`
3. **Coverage Reports**: Check which lines aren't covered
4. **E2E Screenshots**: Playwright captures screenshots on failure

## 📈 Metrics and Monitoring

### Test Metrics

- **Coverage Percentage**: Track over time
- **Test Execution Time**: Monitor for performance regressions
- **Flaky Test Rate**: Identify unstable tests
- **Accessibility Violation Count**: Zero tolerance goal

### Quality Gates

- All tests must pass before merge
- Coverage must meet >80% threshold
- No accessibility violations allowed
- E2E tests must pass on all target browsers

### Reporting

- **PR Comments**: Automatic coverage reports
- **Dashboard**: Test results visualization
- **Alerts**: Notify team of test failures
- **Trends**: Track testing metrics over time

## 🔄 Maintenance

### Regular Tasks

1. **Update Dependencies**: Keep testing libraries current
2. **Review Test Coverage**: Identify gaps and add tests
3. **Refactor Tests**: Remove obsolete tests, improve clarity
4. **Performance Monitoring**: Optimize slow tests

### Test Review Process

1. **New Features**: Require tests for all new functionality
2. **Bug Fixes**: Add regression tests
3. **Code Review**: Include test quality in reviews
4. **Documentation**: Update this guide as tests evolve

---

## 📚 Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library Guide](https://testing-library.com/docs/react-testing-library/intro/)
- [Playwright Documentation](https://playwright.dev/docs/intro)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)
- [MSW Documentation](https://mswjs.io/docs/)

For questions or improvements to this testing strategy, please reach out to the development team or create an issue in the project repository.