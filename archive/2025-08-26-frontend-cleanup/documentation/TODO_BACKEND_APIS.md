# TODO: Backend APIs Required for Frontend Features

## Talent Pool Module (/api/v1/talent-pool/)

### Status: ❌ Not Implemented
All talent pool endpoints currently return 404. Frontend is using mock data as fallback.

### Required Endpoints:

#### Core CRUD Operations
- [ ] `GET    /api/v1/talent-pool/` - List candidates with filters
- [ ] `POST   /api/v1/talent-pool/` - Add new candidate
- [ ] `GET    /api/v1/talent-pool/{id}/` - Get candidate details
- [ ] `PUT    /api/v1/talent-pool/{id}/` - Update candidate
- [ ] `DELETE /api/v1/talent-pool/{id}/` - Remove candidate

#### Pipeline Management
- [ ] `POST   /api/v1/talent-pool/batch-move-stage/` - Bulk move candidates between pipeline stages
- [ ] `GET    /api/v1/talent-pool/{id}/pipeline-history/` - Get candidate's pipeline history

#### Bulk Operations
- [ ] `POST   /api/v1/talent-pool/bulk-operation/` - Execute bulk operations (tag, assign, etc.)
- [ ] `POST   /api/v1/talent-pool/archive/` - Archive multiple candidates
- [ ] `POST   /api/v1/talent-pool/unarchive/` - Unarchive multiple candidates

#### Filter Management
- [ ] `GET    /api/v1/talent-pool/filter-sets/` - Get saved filter sets
- [ ] `POST   /api/v1/talent-pool/filter-sets/` - Save new filter set
- [ ] `PUT    /api/v1/talent-pool/filter-sets/{id}/` - Update filter set
- [ ] `DELETE /api/v1/talent-pool/filter-sets/{id}/` - Delete filter set

#### Campaign Management
- [ ] `GET    /api/v1/talent-pool/campaigns/` - List talent campaigns
- [ ] `POST   /api/v1/talent-pool/campaigns/` - Create new campaign
- [ ] `GET    /api/v1/talent-pool/campaigns/{id}/` - Get campaign details
- [ ] `PUT    /api/v1/talent-pool/campaigns/{id}/` - Update campaign
- [ ] `DELETE /api/v1/talent-pool/campaigns/{id}/` - Delete campaign

#### Reporting & Analytics
- [ ] `GET    /api/v1/talent-pool/reports/` - List available reports
- [ ] `POST   /api/v1/talent-pool/reports/` - Generate new report
- [ ] `GET    /api/v1/talent-pool/analytics/` - Get analytics data
- [ ] `GET    /api/v1/talent-pool/health-metrics/` - System health metrics

#### Import/Export
- [ ] `POST   /api/v1/talent-pool/export/` - Export candidates data
- [ ] `POST   /api/v1/talent-pool/import/` - Import candidates from file

#### Market Intelligence
- [ ] `POST   /api/v1/talent-pool/market-intelligence/` - Get market insights

### Frontend Files Affected:
- `/app/frontend/services/talentPool.ts` - Service layer with all API calls
- `/app/frontend/app/(dashboard)/recruitment/talent-pool/page.tsx` - Main page using the APIs
- `/app/frontend/hooks/useMockData.ts` - Currently providing mock data

### Temporary Workaround:
Frontend is configured to:
1. Catch 404 errors and use empty arrays/default values
2. Use mock data from `useMockData` hooks
3. Show no error messages to users for missing APIs

### Priority:
🔴 **High** - These are core features for the talent pool module

---

## Position Management Enums

### Status: ❌ Missing Type Definitions

The following enums are referenced but not defined:
- [ ] `PositionStatus` - Status of job positions (open, closed, paused, etc.)
- [ ] `PositionUrgency` - Urgency levels (critical, high, normal, low)
- [ ] `JobLevel` - Job seniority levels (entry, junior, mid, senior, lead, principal)
- [ ] `JobType` - Employment types (full_time, part_time, contract, freelance)

### Affected Files:
- `/app/frontend/services/mockDataService.ts` - Currently using hardcoded values
- `/app/frontend/types/recruitment.ts` - Should define these enums

---

## Notes for Backend Team:
1. All endpoints should follow RESTful conventions with trailing slashes
2. List endpoints should return paginated responses with `{ items: [], total: number, skip: number, limit: number }`
3. Error responses should include `error_code` field for i18n translation
4. Consider implementing WebSocket support for real-time pipeline updates