'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Activity,
  RefreshCw,
  Settings,
  Clock,
  TrendingUp
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslations } from '@/app/i18n/client';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

// Import monitoring services and types
import { monitoringService } from '@/services/monitoring';
import { SystemOverviewCard } from '@/components/monitoring/SystemOverviewCard';
import { ServiceCategoryGroup } from '@/components/monitoring/ServiceCategoryGroup';
import { 
  ServiceMetrics, 
  SystemMetricsResponse, 
  ServiceStatus, 
  SystemHealthResponse
} from '@/types/monitoring';

// Service categories configuration
const SERVICE_CATEGORIES = {
  infrastructure: {
    id: 'infrastructure',
    name: '核心基础设施',
    critical: true,
    services: ['database', 'redis', 'celery']
  },
  ai_services: {
    id: 'ai_services', 
    name: 'AI & 机器学习',
    critical: false,
    services: ['deepseek_api', 'ollama', 'ml_service']
  },
  storage: {
    id: 'storage',
    name: '存储服务',
    critical: false,
    services: ['minio']
  }
};

// Main monitoring page
const SystemMonitoringPageImproved: React.FC = () => {
  const t = useTranslations();
  const { toast } = useToast();

  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [systemHealth, setSystemHealth] = useState<SystemHealthResponse | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  
  const maxRetries = 3;

  // Load monitoring data with retry mechanism
  const loadMonitoringData = useCallback(async (isRetry = false) => {
    try {
      if (!isRetry) {
        setLoading(true);
        setError(null);
      }
      
      // Fetch system health data from API
      const systemHealthData = await monitoringService.getSystemHealth();
      
      setSystemHealth(systemHealthData);
      setLastRefresh(new Date());
      setRetryCount(0);
      
    } catch (error: any) {
      console.error('Failed to load monitoring data:', error);
      
      const errorCode = error.message || 'MONITORING_GENERIC_ERROR';
      const errorMessage = t(`errors.${errorCode}`, {
        defaultValue: t('monitoring.errors.loadFailed')
      });
      
      setError(errorMessage);
      
      // Retry logic
      if (retryCount < maxRetries && !isRetry) {
        const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000);
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          loadMonitoringData(true);
        }, retryDelay);
      } else if (!isRetry) {
        toast({
          title: t('monitoring.errors.loadFailedTitle'),
          description: errorMessage,
          variant: 'destructive',
          action: (
            <Button variant="outline" size="sm" onClick={() => loadMonitoringData()}>
              {t('monitoring.actions.retry')}
            </Button>
          )
        });
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [toast, t, retryCount, maxRetries]);

  // Manual refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadMonitoringData();
  };

  // Run comprehensive health check
  const handleHealthCheck = async () => {
    try {
      setRefreshing(true);
      setError(null);
      
      // Trigger comprehensive health check for all services
      const healthCheckResult = await monitoringService.runHealthCheck();
      
      toast({
        title: t('monitoring.actions.healthCheckComplete'),
        description: t('monitoring.messages.servicesUpdated', {
          total: Object.keys(healthCheckResult.services).length
        }),
      });
      
      // Reload monitoring data after health check
      await loadMonitoringData();
      
    } catch (error: any) {
      console.error('Health check failed:', error);
      
      const errorCode = error.message || 'MONITORING_HEALTH_CHECK_FAILED';
      const errorMessage = t(`errors.${errorCode}`, {
        defaultValue: t('monitoring.errors.healthCheckFailed')
      });
      
      toast({
        title: t('monitoring.errors.healthCheckFailedTitle'),
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setRefreshing(false);
    }
  };

  // Auto refresh
  useEffect(() => {
    loadMonitoringData();
  }, [loadMonitoringData]);

  useEffect(() => {
    if (!autoRefresh) return;
    
    const interval = setInterval(() => {
      loadMonitoringData();
    }, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, [autoRefresh, loadMonitoringData]);

  // Group services by category
  const groupedServices = React.useMemo(() => {
    if (!systemHealth?.services) return [];
    
    return Object.values(SERVICE_CATEGORIES).map(category => ({
      ...category,
      services: systemHealth.services.filter(service => 
        category.services.includes(service.name)
      ).map(service => ({
        ...service,
        metrics: {},
        displayName: service.displayName || service.name
      } as ServiceMetrics))
    })).filter(category => category.services.length > 0);
  }, [systemHealth]);

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <div>
          <h1 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            {t('monitoring.title')}
          </h1>
          <p className="text-muted-foreground mt-1">
            {t('monitoring.subtitle')}
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            {t('monitoring.lastUpdate')}: {lastRefresh ? lastRefresh.toLocaleTimeString() : t('monitoring.notRefreshed')}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={cn(autoRefresh && "bg-green-50 border-green-300 dark:bg-green-950/20")}
          >
            <TrendingUp className="h-4 w-4 mr-2" />
            {autoRefresh ? t('monitoring.actions.autoRefresh') : t('monitoring.actions.manualRefresh')}
          </Button>
          
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
            {t('monitoring.actions.refresh')}
          </Button>
          
          <Button onClick={handleHealthCheck} disabled={refreshing}>
            <Activity className="h-4 w-4 mr-2" />
            {t('monitoring.actions.healthCheck')}
          </Button>
        </div>
      </motion.div>

      {/* System Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1, duration: 0.5 }}
      >
        <SystemOverviewCard 
          systemHealth={systemHealth} 
          className="mb-6"
        />
      </motion.div>

      {/* Error Alert */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <Alert className="border-red-300 bg-red-50 dark:bg-red-950/20">
            <AlertTitle className="text-red-800">
              {t('monitoring.errors.loadFailedTitle')}
            </AlertTitle>
            <AlertDescription className="text-red-700">
              {error} {retryCount > 0 && `(${t('monitoring.retrying')}: ${retryCount}/${maxRetries})`}
            </AlertDescription>
          </Alert>
        </motion.div>
      )}

      {/* Service Categories */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.5 }}
        className="space-y-6"
      >
        {loading ? (
          <Card className="border-0 shadow-sm">
            <CardContent className="flex items-center justify-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground mr-3" />
              <span className="text-muted-foreground">{t('monitoring.loading')}</span>
            </CardContent>
          </Card>
        ) : groupedServices.length === 0 ? (
          <Card className="border-0 shadow-sm">
            <CardContent className="flex items-center justify-center py-12 text-center">
              <div>
                <Activity className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-lg font-medium text-muted-foreground mb-2">
                  {t('monitoring.noData')}
                </p>
                <p className="text-sm text-muted-foreground mb-4">
                  {t('monitoring.noDataDescription')}
                </p>
                <Button onClick={() => loadMonitoringData()} disabled={refreshing}>
                  <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
                  {t('monitoring.actions.retry')}
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <AnimatePresence>
            {groupedServices.map((category, index) => (
              <ServiceCategoryGroup
                key={category.id}
                category={category}
                className="mb-6"
              />
            ))}
          </AnimatePresence>
        )}
      </motion.div>

      {/* Additional Tabs (Logs, Alerts) */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4, duration: 0.5 }}
      >
        <Tabs defaultValue="logs" className="space-y-4">
          <TabsList className="grid w-full grid-cols-2 max-w-[400px]">
            <TabsTrigger value="logs">{t('monitoring.tabs.logs')}</TabsTrigger>
            <TabsTrigger value="alerts">{t('monitoring.tabs.alerts')}</TabsTrigger>
          </TabsList>

          {/* System Logs */}
          <TabsContent value="logs" className="space-y-4">
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>{t('monitoring.logs.title')}</CardTitle>
                <CardDescription>{t('monitoring.logs.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[300px] w-full rounded-md border p-4">
                  <div className="space-y-3 text-sm font-mono">
                    <div className="flex items-start gap-3">
                      <span className="text-muted-foreground text-xs">2024-01-20 15:30:45</span>
                      <Badge className="bg-green-100 text-green-800 text-xs">INFO</Badge>
                      <span className="text-sm">DeepSeek API health check completed successfully</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-muted-foreground text-xs">2024-01-20 15:30:40</span>
                      <Badge className="bg-yellow-100 text-yellow-800 text-xs">WARN</Badge>
                      <span className="text-sm">Ollama response time exceeds threshold (850ms {'>'} 500ms)</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-muted-foreground text-xs">2024-01-20 15:30:30</span>
                      <Badge className="bg-green-100 text-green-800 text-xs">INFO</Badge>
                      <span className="text-sm">Database backup completed successfully</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-muted-foreground text-xs">2024-01-20 15:25:00</span>
                      <Badge className="bg-green-100 text-green-800 text-xs">INFO</Badge>
                      <span className="text-sm">Celery worker task completed: generate_embeddings</span>
                    </div>
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* System Alerts */}
          <TabsContent value="alerts" className="space-y-4">
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>{t('monitoring.alerts.title')}</CardTitle>
                <CardDescription>{t('monitoring.alerts.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Alert className="border-blue-300 bg-blue-50 dark:bg-blue-950/20">
                    <Activity className="h-4 w-4 text-blue-600" />
                    <AlertTitle className="text-blue-800">
                      {t('monitoring.alerts.deepseekApiAdded')}
                    </AlertTitle>
                    <AlertDescription className="text-blue-700">
                      {t('monitoring.alerts.deepseekApiAddedDesc')}
                    </AlertDescription>
                  </Alert>
                  <Alert className="border-green-300 bg-green-50 dark:bg-green-950/20">
                    <Settings className="h-4 w-4 text-green-600" />
                    <AlertTitle className="text-green-800">
                      {t('monitoring.alerts.systemImproved')}
                    </AlertTitle>
                    <AlertDescription className="text-green-700">
                      {t('monitoring.alerts.systemImprovedDesc')}
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  );
};

export default SystemMonitoringPageImproved;