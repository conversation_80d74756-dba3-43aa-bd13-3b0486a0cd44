{"auth": {"alreadyHaveAccount": "Already have an account?", "dontHaveAccount": "Don't have an account?", "emailRequired": "Email is required", "forgotPassword": "Forgot password?", "loginError": "Invalid email or password", "loginSuccess": "Login successful!", "loginTitle": "Login to your account", "passwordMin": "Password must be at least 8 characters", "passwordMismatch": "Passwords do not match", "passwordRequired": "Password is required", "registerError": "Registration failed. Please try again.", "registerSuccess": "Registration successful! Please login.", "registerTitle": "Create a new account", "rememberMe": "Remember me"}, "candidates": {"addCandidate": "Add Candidate", "advancedFilters": "Advanced Filters", "backToCandidates": "Back to List", "backToList": "Back to Candidate List", "bulkDelete": "Bulk Delete", "bulkImport": "Bulk Import", "candidateNotFound": "Candidate Not Found", "candidateNotFoundDesc": "Cannot find this candidate, it may have been deleted.", "clearFilters": "Clear", "clearSelection": "Clear Selection", "confirmDelete": "Confirm Delete", "createCandidate": "Create Candidate", "creating": "Creating...", "deleteConfirm": "Confirm Delete", "deleteConfirmDesc": "Are you sure you want to delete this candidate? This action cannot be undone.", "deleteConfirmName": "Are you sure you want to delete candidate \"{name}\"? This action cannot be undone.", "deleting": "Deleting...", "detail": {"assessment": "Assessment", "createdTime": "Created: {time}", "currentJob": "Current Job", "dataPermissionLabel": "Data Permission", "dciScore": "Digital Competency Index (DCI)", "download": "Download", "editCandidate": "Edit Candidate", "education": "Education", "gpa": "GPA: {gpa}", "jfsScore": "Job Fit Score (JFS)", "metadata": "Additional Information", "noAssessment": "No assessment information available", "noEditPermission": "No Edit Permission", "noEditPermissionDesc": "You don't have permission to edit this candidate.", "noEducation": "No education information available", "noResume": "No resume file available", "noSkills": "No skills information available", "noWorkExperience": "No work experience information available", "notes": "Notes", "parseTime": "Parsed at: {time}", "parsing": "Parsing...", "personalInfo": "Personal Information", "present": "Present", "professionalInfo": "Professional Information", "resume": "Resume", "resumeSummary": "Resume Summary", "skills": "Skills & Tags", "source": "Source Channel", "tags": "Tags", "updateResume": "Update Resume", "updatedTime": "Updated: {time}", "uploadResume": "Upload Resume", "workExperience": "Work Experience"}, "exportSelected": "Export Selected", "filters": {"allPermissions": "All Permissions", "allStatus": "All Status", "dataPermission": "Data Permission", "experience": "Years of Experience", "maxExperience": "Max", "maxSalary": "Max", "minExperience": "Min", "minSalary": "Min", "salary": "Expected Sal<PERSON> (K/year)", "selectPermission": "Select Permission", "selectStatus": "Select Status", "status": "Status"}, "form": {"addEducation": "Add Education", "addWorkExperience": "Add Work Experience", "basicInfo": "Basic Information", "basicInfoDesc": "Basic personal information of the candidate", "birthDate": "Birth Date", "birthDatePlaceholder": "Select birth date", "chooseFile": "Choose <PERSON>", "clearDraft": "Clear Draft", "companyPlaceholder": "e.g., Alibaba Group", "companyRequired": "Please enter company name", "createDesc": "Fill in candidate information to create new profile", "createTitle": "Add Candidate", "currentCompany": "Current Company", "currentCompanyPlaceholder": "e.g., Alibaba Group", "currentJob": "Current Job", "currentPosition": "Current Position", "currentPositionPlaceholder": "e.g., Senior Software Engineer", "currentResumeFile": "Current Resume File", "currentSalary": "Current Salary (K/year)", "currentSalaryLabel": "Current", "currentSalaryPlaceholder": "e.g., 30", "degree": "Degree", "degreePlaceholder": "e.g., Bachelor/Master/PhD", "degreeRequired": "Please enter degree", "description": "Description", "discardDraft": "Discard Draft", "draftCleared": "Draft cleared successfully", "draftLoaded": "Draft loaded successfully", "draftSaved": "Draft saved successfully", "dragDropText": "Drag files here or click to upload", "editDesc": "Edit {name}'s information", "editTitle": "Edit Candidate", "educationDescPlaceholder": "Other relevant information...", "educationItem": "Education {index}", "educationSection": "Education Background", "emailPlaceholder": "Enter email address", "emailValidation": "Please enter a valid email address", "endDate": "End Date", "endDatePlaceholder": "e.g., 2019-07", "endDateRequired": "Please enter end date", "expectedSalary": "Expected Sal<PERSON> (K/year)", "expectedSalaryLabel": "Expected", "expectedSalaryPlaceholder": "e.g., 35", "experienceMaxValidation": "Years of experience cannot exceed 50 years", "experienceValidation": "Years of experience cannot be negative", "female": "Female", "fileSize": "{size} MB", "fileTypeText": "Supports PDF, Word documents, max 10MB", "gender": "Gender", "gpaPlaceholder": "e.g., 3.8", "hasDraft": "Unfinished draft detected, would you like to load it?", "loadDraft": "Load Draft", "loadDraftConfirm": "Load Draft", "major": "Major", "majorPlaceholder": "e.g., Computer Science and Technology", "majorRequired": "Please enter major", "male": "Male", "nameMaxLength": "Name cannot exceed 100 characters", "namePlaceholder": "Enter candidate name", "nameRequired": "Name *", "nameValidation": "Please enter candidate name", "noCreatePermission": "No Access Permission", "noCreatePermissionDesc": "You don't have permission to create candidates.", "noEditPermissionDesc": "You don't have permission to edit this candidate.", "noEditPermissionTitle": "No Edit Permission", "noEducationData": "No education background, click the button above to add", "noViewPermission": "No Access Permission", "noViewPermissionDesc": "You don't have permission to view this candidate.", "noWorkExperienceData": "No work experience, click the button above to add", "notFilled": "Not filled", "notesPlaceholder": "Other notes about the candidate...", "other": "Other", "phonePlaceholder": "Enter phone number", "phoneValidation": "Please enter a valid phone number", "positionPlaceholder": "e.g., Senior Software Engineer", "positionRequired": "Please enter position", "professionalInfo": "Professional Information", "professionalInfoDesc": "Professional background and experience of the candidate", "referrerId": "Referrer ID", "referrerIdPlaceholder": "Enter the referrer's user ID", "referrerIdValidation": "Referrer ID must be a number", "removeFile": "Remove", "replaceFileNote": "Selecting a new file will replace the current resume", "resumeFile": "Resume File", "resumeFileDesc": "Upload candidate's resume file (optional)", "resumeFileUpdateDesc": "Update candidate's resume file", "salaryInfo": "Salary Information", "salaryValidation": "Salary cannot be negative", "saveDraft": "Save Draft", "saveDraftTooltip": "Save current form as draft for later editing", "school": "School", "schoolPlaceholder": "e.g., Tsinghua University", "schoolRequired": "Please enter school name", "selectGender": "Select Gender", "selectPermission": "Select Permission", "selectStatus": "Select Status", "settings": "Settings", "settingsDesc": "Candidate status and data permission settings", "skillsAndTags": "Skills & Tags", "skillsAndTagsDesc": "Candidate's skills and custom tags", "skillsLabel": "Skills", "skillsPlaceholder": "Enter skills separated by commas, e.g., <PERSON>, <PERSON><PERSON>, <PERSON>er", "sourceChannel": "Source Channel", "sourcePlaceholder": "e.g., LinkedIn, Referral, Headhunter", "startDate": "Start Date", "startDatePlaceholder": "e.g., 2015-09", "startDateRequired": "Please enter start date", "tagsLabel": "Tags", "tagsPlaceholder": "Enter tags separated by commas, e.g., Recommended Candidate, Tech Expert", "viewCurrentFile": "View Current File", "viewDetails": "View Details", "workDescPlaceholder": "Describe main job responsibilities and achievements...", "workDescription": "Job Description", "workEndDatePlaceholder": "e.g., 2023-12", "workExperienceItem": "Work Experience {index}", "workExperienceSection": "Work Experience", "workStartDatePlaceholder": "e.g., 2019-07", "years": "{years} years", "yearsOfExperience": "Years of Experience", "yearsOfExperiencePlaceholder": "Enter years of experience"}, "loadingError": "Loading Failed", "noData": "No Candidates", "noDataDesc": "No candidate data yet, add the first candidate to get started.", "noPermission": "No Access Permission", "noPermissionDesc": "You don't have permission to access candidate management.", "pagination": {"next": "Next", "page": "Page {current} of {total}", "previous": "Previous", "showing": "Showing {start} - {end} of {total} records"}, "permission": {"private": "Private", "public": "Public", "shared": "Shared", "team": "Team"}, "saveChanges": "Save Changes", "searchPlaceholder": "Search candidate name, email, phone or company...", "selected": "Selected {count} candidates", "stats": {"title": "Candidate Statistics", "subtitle": "Real-time insights and analytics for your talent pool", "totalCandidates": "Total Candidates", "activeCandidates": "Active Candidates", "newThisWeek": "New This Week", "newThisMonth": "New This Month", "avgMatchScore": "Average Match Score", "thisMonth": "this month", "last30Days": "Last 30 days", "withResume": "With Resume", "pending": "Pending", "vsLastWeek": "vs last week", "skillMatch": "Skill Match", "activeRate": "active rate", "overview": "Overview", "skills": "Skills", "demographics": "Demographics", "statusDistribution": "Status Distribution", "statusDistributionDesc": "Current status of all candidates in the pipeline", "sourceDistribution": "Source Distribution", "sourceDistributionDesc": "Where candidates are coming from", "topSkills": "Top Skills", "topSkillsDesc": "Most common skills among candidates", "experienceDistribution": "Experience Distribution", "experienceDistributionDesc": "Years of experience breakdown", "educationDistribution": "Education Distribution", "educationDistributionDesc": "Educational background of candidates", "salaryDistribution": "Salary Distribution", "salaryDistributionDesc": "Expected salary ranges", "departmentDistribution": "Department Distribution", "departmentDistributionDesc": "Distribution across departments", "activityTrend": "Activity Trend", "activityTrendDesc": "Daily activity over the past week", "noData": "No data available", "unknown": "Unknown", "unassigned": "Unassigned", "new": "new", "active": "active", "loadError": "Failed to load statistics", "errorTitle": "Unable to Load Statistics", "experienceRanges": {"0-2": "0-2 years", "3-5": "3-5 years", "6-10": "6-10 years", "11-15": "11-15 years", "16+": "16+ years"}, "educationLevels": {"highSchool": "High School", "associate": "Associate Degree", "bachelor": "Bachelor's Degree", "master": "Master's Degree", "doctorate": "Doctorate", "other": "Other"}, "salaryRanges": {"0-50k": "$0-50K", "50-75k": "$50-75K", "75-100k": "$75-100K", "100-150k": "$100-150K", "150-200k": "$150-200K", "200k+": "$200K+"}, "sources": {"linkedin": "LinkedIn", "referral": "Referral", "jobBoard": "Job Board", "careerPage": "Career Page", "agency": "Agency", "direct": "Direct Application", "other": "Other"}, "refreshing": "Refreshing...", "exportStats": "Export Statistics", "viewMore": "View More", "lastUpdated": "Last updated: {time}", "compareTimeframes": "Compare Timeframes", "growth": "Growth", "decline": "Decline", "stable": "Stable"}, "status": {"hired": "<PERSON><PERSON>", "interview": "Interview", "new": "New Candidate", "offer": "Offer <PERSON>", "rejected": "Rejected", "screening": "Screening", "withdrawn": "Withdrawn"}, "subtitle": "Manage and evaluate candidate information, track recruitment process.", "table": {"actions": "Actions", "company": "Current Company", "createdAt": "Created", "email": "Email", "experience": "Experience", "name": "Name", "permission": "Permission", "phone": "Phone", "position": "Current Position", "share": "Share", "skills": "Skills", "status": "Status", "viewDetails": "View Details"}, "title": "Candidate Management", "updating": "Updating...", "uploading": "Uploading..."}, "common": {"save": "Save", "cancel": "Cancel", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "minute": "minute", "minutes": "minutes", "hour": "hour", "hours": "hours", "day": "day", "days": "days", "recommended": "Recommended"}, "dashboard": {"actions": {"createAssessment": "Create Assessment", "exportReport": "Export Report", "importCandidates": "Import Candidates", "viewAllActivities": "View All Activities"}, "activitySection": {"activities": {"assessmentCompleted": "Completed skill assessment for {name}", "batchImportCompleted": "Batch import completed, awaiting assessment", "candidatesAdded": "Added {count} candidates", "reportGenerated": "Generated monthly assessment report", "reportStats": "Contains {count} assessment results", "scores": "DCI Score: {dci}, JFS Score: {jfs}"}, "description": "Recent assessment activities and important events in the system.", "timeAgo": {"daysAgo": "{days} days ago", "hoursAgo": "{hours} hours ago", "minutesAgo": "{minutes} minutes ago"}, "title": "Recent Activity"}, "createAssessmentDialog": {"cancel": "Cancel", "create": "Create Task", "description": "Set up a new talent assessment task, the system will automatically perform intelligent matching.", "targetPosition": "Target Position", "targetPositionPlaceholder": "Senior Frontend Engineer", "taskName": "Task Name", "taskNamePlaceholder": "2024 Frontend Engineer Recruitment", "title": "Create New Assessment Task"}, "overview": "Overview", "quickActions": "Quick Actions", "quickStart": {"addCandidate": "Add Candidate", "addCandidateDesc": "Upload resume or create manually", "description": "Choose an action to start using the system", "publishJob": "Publish Job", "publishJobDesc": "Create new recruitment requirements", "startAssessment": "Start Assessment", "startAssessmentDesc": "Evaluate candidate capabilities", "title": "Quick Start"}, "recentActivity": "Recent Activity", "statistics": {"activeJobs": "Active Jobs", "aiMatchAccuracy": "AI Match Accuracy", "assessmentsCompleted": "Assessments Completed", "averageMatchScore": "Average Match Score", "avgJFSScore": "Average Match Score", "comparedToLastMonth": "vs last month", "evaluationRate": "Evaluation Completion Rate", "jfsAverageScore": "JFS Average", "monthlyEvaluation": "This month", "systemAccuracy": "System Accuracy", "totalCandidates": "Total Candidates"}, "subtitle": "This is your intelligent talent assessment system overview.", "toast": {"assessmentCreated": "Assessment task created successfully!", "assessmentStarted": "New assessment task has started running."}, "welcome": "Welcome back, "}, "errors": {"AUTH_LOGIN_INACTIVE_USER": "Your account is inactive. Please contact support.", "AUTH_REFRESH_TOKEN_EXPIRED": "Your session has expired. Please log in again.", "AUTH_REFRESH_TOKEN_INVALID": "Invalid session token. Please log in again.", "AUTH_REFRESH_TOKEN_MISSING": "Authentication token is missing. Please log in again.", "AUTH_REFRESH_TOKEN_NOT_FOUND": "Session token not found. Please log in again.", "AUTH_TOKEN_INVALID_USER_ID": "Invalid user session. Please log in again.", "AUTH_USER_NOT_FOUND": "User account not found. Please contact support.", "EMBEDDING_DIMENSION_MISMATCH": "Embedding dimension mismatch", "EMBEDDING_GENERATION_FAILED": "Failed to generate embeddings", "EMBEDDING_PROVIDER_UNAVAILABLE": "Embedding provider is unavailable", "INSUFFICIENT_VECTOR_DATA": "Insufficient vector data for matching", "MATCHING_FAILED": "Failed to match candidates", "NO_CANDIDATES_FOUND": "No matching candidates found", "OLLAMA_CONNECTION_FAILED": "Failed to connect to Ollama service", "OPENAI_API_ERROR": "OpenAI API error", "POSITION_DELETE_PERMISSION_DENIED": "You don't have permission to delete this position", "POSITION_DUPLICATE": "A similar position already exists", "POSITION_INVALID_DATA": "Invalid position data", "POSITION_NOT_FOUND": "Position not found", "POSITION_PERMISSION_DENIED": "Permission denied for this position", "POSITION_SHARE_PERMISSION_DENIED": "Only the creator can share this position", "POSITION_UPDATE_PERMISSION_DENIED": "You don't have permission to update this position", "VECTOR_GENERATION_FAILED": "Failed to generate embeddings", "VECTOR_NOT_FOUND": "Vector not found for this position", "VECTOR_PERMISSION_DENIED": "Permission denied for vector operations", "VECTOR_SEARCH_FAILED": "Vector search failed", "authenticationError": "Authentication failed", "genericError": "An unexpected error occurred", "networkError": "Network connection error", "permissionDenied": "Permission denied", "validationError": "Please check your input"}, "landing": {"features": {"analytics": {"description": "Get detailed insights with DCI and JFS scores for data-driven decisions", "title": "Comprehensive Analytics"}, "assessment": {"description": "Evaluate candidates across digital literacy, industry skills, job skills, innovation, and learning potential", "title": "Multi-dimensional Assessment"}, "integration": {"description": "Easily integrate with your existing HR systems and workflows", "title": "Seamless Integration"}, "matching": {"description": "Use machine learning to match candidates with the most suitable job positions", "title": "AI-Powered Matching"}, "title": "Key Features"}, "hero": {"getStarted": "Get Started", "learnMore": "Learn More", "subtitle": "Evaluate digital competencies and match talents with perfect job opportunities using AI-powered assessments", "title": "Intelligent Talent Assessment & Job Matching System"}}, "navigation": {"assessments": "Assessments", "candidates": "Candidates", "dashboard": "Dashboard", "help": "Help Center", "home": "Home", "jobs": "Jobs", "profile": "Profile", "reports": "Reports", "analytics": "Analytics", "insights": "Insights", "settings": "Settings", "users": "User Management", "search": "Search", "searchPlaceholder": "Search...", "commandMenu": "Command Menu", "addCandidate": "Add Candidate", "quickActions": "Quick Actions", "systemStatus": "System Status", "systemOnline": "System Online", "allServicesOperational": "All services operational", "pro": "PRO", "new": "New", "main": "Main", "analytics_group": "Analytics", "admin": "Admin", "management": "Management", "userManagement": "User Management", "roles": "Roles", "permissions": "Permissions", "systemSettings": "System Settings", "monitoring": "System Monitoring"}, "permissions": {"private": "Private", "public": "Public", "shared": "Shared", "team": "Team"}, "positions": {"actions": {"archive": "Archive", "bulkDelete": "Delete Selected", "bulkGenerateEmbeddings": "Generate Embeddings", "delete": "Delete Position", "duplicate": "Duplicate", "edit": "Edit Position", "export": "Export", "generateEmbeddings": "Generate Embeddings", "matchCandidates": "Find Matches", "publish": "Publish Position", "save": "Save Position", "share": "Share Position", "unpublish": "Unpublish", "view": "View Details"}, "create": "Create Position", "createDescription": "Fill in the details below to create a new job position", "currency": {"CNY": "CNY (¥)", "EUR": "EUR (€)", "GBP": "GBP (£)", "USD": "USD ($)"}, "delete": "Delete Position", "edit": "Edit Position", "departments": {"engineering": "Engineering", "product": "Product", "design": "Design", "marketing": "Marketing", "sales": "Sales", "operations": "Operations", "finance": "Finance", "hr": "HR", "legal": "Legal"}, "locations": {"remote": "Remote", "newYork": "New York", "sanFrancisco": "San Francisco", "london": "London", "toronto": "Toronto", "berlin": "Berlin", "singapore": "Singapore", "beijing": "Beijing", "shanghai": "Shanghai", "shenzhen": "Shenzhen"}, "empty": {"description": "Create your first job position to start matching candidates", "noPositions": "No positions found", "title": "No positions yet", "tryDifferentFilters": "Try adjusting your filters or search terms"}, "fields": {"benefits": "Benefits", "capabilityWeights": "Capability Weights", "createdAt": "Created", "createdBy": "Created By", "dataPermission": "Data Permission", "data_permission": "Data Permission", "deadline": "Application Deadline", "department": "Department", "headcount": "Headcount", "jobLevel": "Job Level", "jobType": "Job Type", "job_level": "Job Level", "job_type": "Job Type", "location": "Location", "preferredSkills": "Preferred Skills", "preferred_skills": "Preferred Skills", "qualifications": "Qualifications", "requiredSkills": "Required Skills", "required_skills": "Required Skills", "requirements": "Requirements", "responsibilities": "Responsibilities", "salary": "Salary Range", "salaryCurrency": "<PERSON><PERSON><PERSON><PERSON>", "salaryMax": "Maximum Salary", "salaryMin": "Minimum Salary", "salary_currency": "<PERSON><PERSON><PERSON><PERSON>", "salary_max": "Maximum Salary", "salary_min": "Minimum Salary", "sharedWith": "Shared With", "status": "Status", "title": "Position Title", "updatedAt": "Updated", "urgency": "Urgency"}, "filters": {"activeFilters": "Active Filters", "clearAll": "Clear All Filters", "dataPermission": "Data Permission", "dateRange": "Date Range", "department": "Department", "endDate": "End Date", "location": "Location", "permission": "Permission", "salary": "Salary", "salaryRange": "Salary Range", "search": "Search", "searchPlaceholder": "Search positions...", "selectDepartment": "Select Department", "selectLocation": "Select Location", "selectPermission": "Select Permission", "selectStatus": "Select Status", "selectUrgency": "Select Urgency", "skills": "Skills", "skillsPlaceholder": "Enter skill and press Enter", "startDate": "Start Date", "status": "Status", "title": "Filters", "urgency": "Urgency"}, "form": {"basicInfo": "Basic Information", "dataPermission": "Data Permissions", "salaryInfo": "Salary Information", "skills": "Skills Requirements", "statusPriority": "Status & Priority", "title": "Position Details"}, "headcount": "positions", "jobType": {"contract": "Contract", "full-time": "Full-time", "internship": "Internship", "part-time": "Part-time"}, "list": {"title": "Positions", "total": "total"}, "messages": {"createSuccess": "Position created successfully", "deleteSuccess": "Position deleted successfully", "matchingComplete": "Candidate matching completed", "matchingInProgress": "Finding matches...", "shareSuccess": "Position shared successfully", "updateSuccess": "Position updated successfully", "vectorGenerated": "Embeddings generated successfully", "vectorGenerating": "Generating embeddings..."}, "noResults": "No positions found", "pagination": {"showing": "Showing {start}-{end} of {total}"}, "placeholders": {"addBenefit": "Add a benefit and press Enter", "addQualification": "Add a qualification and press Enter", "addResponsibility": "Add a responsibility and press Enter", "addSkill": "Add a skill and press Enter", "deadline": "Select deadline date", "department": "e.g., Engineering", "job_level": "e.g., Senior, Manager, Director", "location": "e.g., San Francisco, CA", "salary_max": "Enter maximum salary", "salary_min": "Enter minimum salary", "title": "e.g., Senior Software Engineer"}, "selected": "selected", "share": "Share Position", "stats": {"active": "active", "andMore": "+{count} more", "byDepartment": "By Department", "byStatus": "By Status", "byUrgency": "By Priority", "lastWeek": "last 7 days", "modelDistribution": "Model Distribution", "ofTotal": "of total", "ollamaVectors": "Ollama", "openPositions": "Open Positions", "openaiVectors": "OpenAI", "recent": "Recent", "total": "Total Positions", "vectorCoverage": "AI Coverage", "vectorStats": "AI Embeddings", "withEmbeddings": "with AI embeddings", "withVectors": "With Embeddings", "withoutVectors": "Without Embeddings"}, "status": {"closed": "Closed", "draft": "Draft", "open": "Open", "paused": "Paused", "reviewing": "Under Review"}, "subtitle": "Manage and create job positions with AI-powered matching", "title": "Job Positions", "urgency": {"critical": "Critical", "high": "High Priority", "low": "Low Priority", "normal": "Normal"}, "view": {"cards": "Cards", "table": "Table"}}, "settings": {"stats": {"title": "Statistics Display", "description": "Configure how statistics are displayed in the sidebar and dashboard", "displayOptions": "Display Options", "showDashboard": "Show Dashboard Statistics", "showDashboardDesc": "Display notification counts and new items in the dashboard section", "showCandidates": "Show Candidate Statistics", "showCandidatesDesc": "Display candidate counts and pending reviews in the sidebar", "showUserManagement": "Show User Management Statistics", "showUserManagementDesc": "Display user counts and pending verifications (admin only)", "showBadges": "Show Notification Badges", "showBadgesDesc": "Display count badges and 'new' indicators on menu items", "performanceOptions": "Performance Options", "refreshInterval": "Refresh Interval", "refreshIntervalDesc": "How often statistics are refreshed automatically", "selectInterval": "Select refresh interval", "displayMode": "Display Mode", "displayModeDesc": "Choose how statistics are displayed", "selectMode": "Select display mode", "modeAuto": "Auto", "modeCompact": "Compact", "modeDetailed": "Detailed", "refreshCache": "Refresh Now"}}, "theme": {"dark": "Dark", "light": "Light", "system": "System", "toggle": "Toggle theme"}, "users": {"actions": {"activate": "Activate", "changePassword": "Change Password", "deactivate": "Deactivate", "edit": "Edit", "unlock": "Unlock"}, "addUser": "Add User", "form": {"active": "Active", "addDesc": "Create a new user account.", "addTitle": "Add New User", "create": "Create", "department": "Department", "editDesc": "Update user information and permissions.", "editTitle": "Edit User", "email": "Email", "emailInvalid": "Invalid email format", "emailRequired": "Email is required", "fullName": "Full Name", "fullNameRequired": "Full name is required", "phone": "Phone", "phoneInvalid": "Invalid phone number format", "position": "Position", "role": "Role", "saving": "Saving...", "selectRole": "Select a role", "update": "Update", "username": "Username", "usernameRequired": "Username is required"}, "noData": "No Users", "noDataDesc": "No user data yet, add the first user to get started.", "passwordModal": {"changeFailed": "Failed to change password", "changePassword": "Change Password", "changeSuccess": "Password changed successfully", "changing": "Changing...", "confirmPassword": "Confirm New Password", "confirmPasswordRequired": "Please confirm your new password", "currentPassword": "Current Password", "currentPasswordRequired": "Current password is required", "description": "Enter your current password and choose a new password.", "newPassword": "New Password", "newPasswordRequired": "New password is required", "passwordHint": "At least 8 characters with uppercase, lowercase, numbers, and special characters", "passwordMismatch": "Passwords do not match", "passwordSame": "New password must be different from current password", "title": "Change Password"}, "permissions": {"changePassword": "Change Password", "create": "Create Users", "edit": "Edit Users", "manageRoles": "Manage Roles", "viewAll": "View All Users"}, "roles": {"employee": "Employee", "hr_admin": "HR Admin", "hr_specialist": "HR Specialist", "interviewer": "Interviewer", "recruiter": "Rec<PERSON>er", "super_admin": "Super Admin"}, "searchPlaceholder": "Search username, email or name...", "status": {"active": "Active", "inactive": "Inactive", "locked": "Locked"}, "subtitle": "Manage system users and their permissions", "table": {"actions": "Actions", "department": "Department", "email": "Email", "fullName": "Full Name", "lastLogin": "Last Login", "role": "Role", "status": "Status", "username": "Username"}, "title": "User Management", "toast": {"activateSuccess": "User activated successfully", "createSuccess": "User created successfully", "deactivateSuccess": "User deactivated successfully", "deleteSuccess": "User deleted successfully", "loadError": "Failed to load users", "unlockSuccess": "User unlocked successfully", "updateSuccess": "User updated successfully"}}, "monitoring": {"title": "System Monitoring", "subtitle": "Real-time monitoring of system health and performance metrics", "services": {"title": "Service Status", "description": "Real-time health status of all system services", "database": "Database", "postgres": "PostgreSQL Database", "redis": "<PERSON><PERSON>", "minio": "MinIO Storage", "ollama": "Ollama AI Service", "celery": "Celery Workers", "nginx": "Nginx Proxy", "backend": "Backend API", "frontend": "Frontend App", "ml_service": "ML Service", "deepseek_api": "DeepSeek API"}, "statusPage": {"title": "Service Status & Metrics", "description": "Comprehensive service monitoring with historical uptime trends and real-time metrics", "allSystemsOperational": "All Systems Operational", "minorServiceIssues": "Minor Service Issues", "serviceOutage": "Service Outage", "healthScore": "Health Score", "lastUpdated": "Last updated", "daysUptime": "{{days}}-day uptime", "serviceStatusHistory": "Service Status History", "uptime": "Uptime", "response": "Response", "keyMetrics": "Key Metrics", "lastCheck": "Last check", "daysStatusHistory": "{{days}}-day status history", "systemMetrics": "System Metrics", "systemUptime": "System Uptime", "operational": "Operational", "degraded": "Degraded", "majorIssues": "Major Issues", "noData": "No Data", "loading": "Loading Status Page...", "loadError": "Failed to Load Status Page", "serviceHistoryDescription": "Current status and {{days}}-day uptime history for all services", "noServicesData": "No services data available", "systemResourceDescription": "Current system resource utilization", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "diskUsage": "Disk Usage", "moreMetrics": "+{{count}} more metrics available", "timelineRange": "{{days}} days ago ← → Today", "days30": "30 days", "days90": "90 days", "days180": "180 days", "year1": "1 year", "incidents": "{{count}} incidents"}, "systemOverview": "System Overview", "healthScore": "Health Score", "overallHealth": "Overall Health", "uptime": "Uptime", "servicesHealthy": "Services Healthy", "critical": "Critical", "lastUpdate": "Last Updated", "lastCheck": "Last Check", "notRefreshed": "Not refreshed", "loading": "Loading monitoring data...", "noData": "No monitoring data available", "moreMetrics": "more metrics", "showLess": "Show less", "hoverToExpand": "Hover to expand", "noMetrics": "No metrics available", "performanceMetrics": "Performance Metrics", "retrying": "Retrying", "systemStatus": "System Status", "systemLoad": "System Load", "memory": "Memory", "disk": "Disk", "hostSystemMetrics": "Host System Metrics (Real-time)", "hostMetricsTooltip": "These metrics are collected from the host machine using psutil library, showing real system resource usage. Container-specific metrics are shown in individual service cards.", "cpuTooltip": "Host machine CPU usage", "memoryTooltip": "Host machine RAM usage", "diskTooltip": "Host machine disk usage (/)", "actions": {"auto": "Auto", "manual": "Manual", "autoRefresh": "Auto Refresh", "manualRefresh": "Manual Refresh", "refresh": "Refresh", "healthCheck": "Health Check", "retry": "Retry", "healthCheckComplete": "Health check completed"}, "messages": {"servicesRunning": "{healthy}/{total} services running normally", "servicesDegraded": "{count} services degraded", "servicesUnhealthy": "{count} services unhealthy", "servicesUpdated": "All {total} services status updated"}, "tabs": {"services": "Service Status", "logs": "System Logs", "alerts": "<PERSON><PERSON><PERSON>"}, "serviceHealth": {"title": "Service Health Status", "description": "Real-time operational status of TalentForge Pro services"}, "logs": {"title": "System Logs", "description": "Recent system events and operation logs"}, "alerts": {"title": "System Alerts", "description": "Current system alerts and notifications", "systemHealthy": "All systems operational", "systemDegraded": "System performance degraded", "systemUnhealthy": "System issues detected", "serviceUnhealthy": "{service} service is unhealthy and requires immediate attention", "serviceDegraded": "{service} service is experiencing degraded performance", "allServicesHealthy": "All Services Healthy", "allServicesHealthyDesc": "All system components are running normally. No action needed.", "serviceDown": "Service {{service}} is down", "highCpuUsage": "High CPU usage detected", "highMemoryUsage": "High memory usage detected", "highDiskUsage": "High disk usage detected", "maintenanceReminder": "Scheduled Maintenance Reminder", "scheduledMaintenance": "Next scheduled maintenance: {{time}}", "deepseekApiAdded": "DeepSeek API Integration Added", "deepseekApiAddedDesc": "DeepSeek API has been successfully integrated for enhanced AI capabilities", "systemImproved": "System Performance Improved", "systemImprovedDesc": "Recent optimizations have improved system response time by 30%"}, "metrics": {"connections": "Connections", "memory_usage": "Memory Usage", "cpu_usage": "CPU Usage", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "disk_usage": "Disk Usage", "diskUsage": "Disk Usage", "networkTraffic": "Network Traffic", "averageResponseTime": "Average Response Time", "cache_hit_ratio": "<PERSON><PERSON>", "vector_index_size": "Vector Index Size", "query_latency": "Query Latency (P99)", "connected_clients": "Connected Clients", "ops_per_sec": "Operations/sec", "evicted_keys": "Evicted <PERSON>", "bucket_count": "Bucket Count", "total_objects": "Total Objects", "bandwidth_in": "Bandwidth In", "bandwidth_out": "Bandwidth Out", "model_count": "Model Count", "loaded_models": "Loaded Models", "inference_latency": "Inference Latency", "gpu_memory": "GPU Memory", "active_workers": "Active Workers", "queued_tasks": "Queued Tasks", "completed_tasks": "Completed Tasks", "failed_tasks": "Failed Tasks", "task_success_rate": "Task Success Rate", "active_connections": "Active Connections", "requests_per_sec": "Requests/sec", "error_rate": "Error Rate", "upstream_response_time": "Upstream Response Time", "response_time": "Response Time", "uptime": "Uptime", "version": "Version", "container_cpu": "Container CPU", "container_memory": "Container Memory", "tables": "Tables", "clients": "Clients", "memory": "Memory", "workers": "Workers", "tasks": "Tasks", "buckets": "Buckets", "objects": "Objects", "models": "Models"}, "status": {"healthy": "Healthy", "degraded": "Degraded", "unhealthy": "Unhealthy", "unknown": "Unknown", "running": "Running", "stopped": "Stopped"}, "severity": {"normal": "Normal", "warning": "Warning", "critical": "Critical"}, "errors": {"MONITORING_SERVICE_UNAVAILABLE": "Service is unavailable", "loadFailed": "Failed to load monitoring data, please try again later", "loadFailedTitle": "Loading Failed", "healthCheckFailed": "Unable to perform health check, please try again later", "healthCheckFailedTitle": "Health Check Failed", "authRequired": "Please login to view monitoring data", "configMissing": "Configuration missing", "connectionFailed": "Connection failed", "timeout": "Request timeout", "MONITORING_SERVICE_TIMEOUT": "Service request timed out", "MONITORING_SERVICE_CONNECTION_FAILED": "Failed to connect to service", "MONITORING_HEALTH_CHECK_FAILED": "Health check failed", "MONITORING_HEALTH_CHECK_TIMEOUT": "Health check timed out", "MONITORING_METRIC_COLLECTION_ERROR": "Failed to collect metrics", "MONITORING_METRIC_PARSE_ERROR": "Failed to parse metrics", "MONITORING_PERMISSION_DENIED": "Permission denied", "MONITORING_RATE_LIMIT_EXCEEDED": "Too many requests", "MONITORING_DATA_NOT_FOUND": "Monitoring data not found", "MONITORING_INVALID_TIME_RANGE": "Invalid time range specified"}}, "validation": {"email": "Please enter a valid email address", "max": "Must be no more than {max} characters", "min": "Must be at least {min} characters", "number": "Must be a number", "positive": "Must be a positive number", "required": "This field is required"}}