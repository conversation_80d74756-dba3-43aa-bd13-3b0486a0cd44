'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Activity,
  AlertCircle,
  CheckCircle2,
  Clock,
  Cpu,
  Database,
  HardDrive,
  RefreshCw,
  Server,
  Wifi,
  Monitor,
  Zap,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Package,
  Brain,
  Layers,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useTranslations } from '@/app/i18n/client';
import { useToast } from '@/hooks/use-toast';
import { cn } from '@/lib/utils';

// Import monitoring services and types
import { monitoringService } from '@/services/monitoring';
import { ServiceCard } from '@/components/monitoring/ServiceCard';
import { 
  ServiceMetrics, 
  SystemMetricsResponse, 
  ServiceStatus, 
  ServiceListResponse,
  SystemHealthResponse
} from '@/types/monitoring';

// Using imported monitoring types instead of local definitions

// Using imported ServiceCard component with dynamic metrics rendering

// Metric card component
const MetricCard: React.FC<{
  title: string;
  value: number;
  unit: string;
  icon: React.ReactNode;
  status: 'good' | 'warning' | 'critical';
  trend?: 'up' | 'down' | 'stable';
}> = ({ title, value, unit, icon, status, trend }) => {
  const getStatusColor = () => {
    switch (status) {
      case 'good': return 'text-green-600 bg-green-50 dark:bg-green-950/20';
      case 'warning': return 'text-yellow-600 bg-yellow-50 dark:bg-yellow-950/20';
      case 'critical': return 'text-red-600 bg-red-50 dark:bg-red-950/20';
    }
  };

  const getTrendIcon = () => {
    if (!trend) return null;
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-600" />;
      case 'stable': return <div className="h-4 w-4 text-gray-600">—</div>;
    }
  };

  return (
    <Card className="border-0 shadow-sm">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-2">
          <div className={cn('h-10 w-10 rounded-lg flex items-center justify-center', getStatusColor())}>
            {icon}
          </div>
          {getTrendIcon()}
        </div>
        <p className="text-sm text-muted-foreground mb-1">{title}</p>
        <div className="flex items-baseline gap-1">
          <p className="text-2xl font-bold">{value}</p>
          <span className="text-sm text-muted-foreground">{unit}</span>
        </div>
        <Progress 
          value={value} 
          className={cn(
            "mt-2 h-2",
            status === 'good' && "bg-green-100",
            status === 'warning' && "bg-yellow-100",
            status === 'critical' && "bg-red-100"
          )} 
        />
      </CardContent>
    </Card>
  );
};

// Main monitoring page
const SystemMonitoringPage: React.FC = () => {
  const t = useTranslations();
  const { toast } = useToast();

  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const [services, setServices] = useState<ServiceMetrics[]>([]);
  const [systemMetrics, setSystemMetrics] = useState<SystemMetricsResponse | null>(null);
  const [systemHealth, setSystemHealth] = useState<SystemHealthResponse | null>(null);
  const [lastRefresh, setLastRefresh] = useState<Date | null>(null);
  
  const maxRetries = 3;

  // System status calculation based on real data

  // Load monitoring data with retry mechanism
  const loadMonitoringData = useCallback(async (isRetry = false) => {
    try {
      if (!isRetry) {
        setLoading(true);
        setError(null);
      }
      
      // Fetch real monitoring data from APIs
      const [systemHealthData, systemMetricsData] = await Promise.all([
        monitoringService.getSystemHealth(),
        monitoringService.getSystemMetrics(),
      ]);
      
      // Transform and set data
      const transformedServices = systemHealthData.services.map(service => 
        monitoringService.transformServiceData(service)
      );
      
      setServices(transformedServices);
      setSystemMetrics(systemMetricsData);
      setSystemHealth(systemHealthData);
      setLastRefresh(new Date());
      setRetryCount(0);
      
    } catch (error: any) {
      console.error('Failed to load monitoring data:', error);
      
      const errorCode = error.message || 'MONITORING_GENERIC_ERROR';
      const errorMessage = t(`errors.${errorCode}`, {
        defaultValue: t('monitoring.errors.loadFailed')
      });
      
      setError(errorMessage);
      
      // Retry logic
      if (retryCount < maxRetries && !isRetry) {
        const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 10000);
        setTimeout(() => {
          setRetryCount(prev => prev + 1);
          loadMonitoringData(true);
        }, retryDelay);
      } else if (!isRetry) {
        // Show toast on final failure
        toast({
          title: t('monitoring.errors.loadFailedTitle'),
          description: errorMessage,
          variant: 'destructive',
          action: (
            <Button variant="outline" size="sm" onClick={() => loadMonitoringData()}>
              {t('monitoring.actions.retry')}
            </Button>
          )
        });
        
        // Use fallback mock data if available
        const mockServices = monitoringService.getMockServicesData();
        setServices(mockServices);
      }
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  }, [toast, t, retryCount, maxRetries]);

  // Manual refresh
  const handleRefresh = async () => {
    setRefreshing(true);
    await loadMonitoringData();
  };

  // Run comprehensive health check
  const handleHealthCheck = async () => {
    try {
      setRefreshing(true);
      setError(null);
      
      // Trigger comprehensive health check for all services
      const healthCheckResult = await monitoringService.runHealthCheck();
      
      toast({
        title: t('monitoring.actions.healthCheckComplete'),
        description: t('monitoring.messages.servicesUpdated', {
          total: Object.keys(healthCheckResult.services).length
        }),
      });
      
      // Reload monitoring data after health check
      await loadMonitoringData();
      
    } catch (error: any) {
      console.error('Health check failed:', error);
      
      const errorCode = error.message || 'MONITORING_HEALTH_CHECK_FAILED';
      const errorMessage = t(`errors.${errorCode}`, {
        defaultValue: t('monitoring.errors.healthCheckFailed')
      });
      
      toast({
        title: t('monitoring.errors.healthCheckFailedTitle'),
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setRefreshing(false);
    }
  };

  // Auto refresh
  useEffect(() => {
    loadMonitoringData();
  }, [loadMonitoringData]);

  useEffect(() => {
    if (!autoRefresh) return;
    
    const interval = setInterval(() => {
      loadMonitoringData();
    }, 30000); // Refresh every 30 seconds
    
    return () => clearInterval(interval);
  }, [autoRefresh, loadMonitoringData]);

  // Calculate overall system status from real data
  const getSystemStatus = (): ServiceStatus => {
    if (systemHealth?.overallStatus) {
      return systemHealth.overallStatus as ServiceStatus;
    }
    
    if (!services.length) return ServiceStatus.UNKNOWN;
    
    const statusCounts = services.reduce((counts, service) => {
      const status = service.status as ServiceStatus;
      counts[status] = (counts[status] || 0) + 1;
      return counts;
    }, {} as Record<ServiceStatus, number>);
    
    if (statusCounts[ServiceStatus.UNHEALTHY] > 0) return ServiceStatus.UNHEALTHY;
    if (statusCounts[ServiceStatus.DEGRADED] > 1) return ServiceStatus.DEGRADED;
    if (statusCounts[ServiceStatus.HEALTHY] === services.length) return ServiceStatus.HEALTHY;
    
    return ServiceStatus.UNKNOWN;
  };
  
  const systemStatus = getSystemStatus();
  
  // Calculate service statistics for display
  const serviceStats = services.reduce((stats, service) => {
    const status = service.status as ServiceStatus;
    stats.total += 1;
    
    switch (status) {
      case ServiceStatus.HEALTHY:
      case 'healthy':
        stats.healthy += 1;
        break;
      case ServiceStatus.DEGRADED:
      case 'degraded':
        stats.degraded += 1;
        break;
      case ServiceStatus.UNHEALTHY:
      case 'unhealthy':
        stats.unhealthy += 1;
        break;
    }
    
    return stats;
  }, { total: 0, healthy: 0, degraded: 0, unhealthy: 0 });

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="flex items-center justify-between"
      >
        <div>
          <h2 className="text-3xl font-bold tracking-tight bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
            {t('monitoring.title')}
          </h2>
          <p className="text-muted-foreground mt-1">
            {t('monitoring.subtitle')}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Clock className="h-4 w-4" />
            {t('monitoring.lastUpdate')}: {lastRefresh ? lastRefresh.toLocaleTimeString() : t('monitoring.notRefreshed')}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={cn(autoRefresh && "bg-green-50 border-green-300 dark:bg-green-950/20")}
          >
            {autoRefresh ? t('monitoring.actions.autoRefresh') : t('monitoring.actions.manualRefresh')}
          </Button>
          <Button
            variant="outline"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
            {t('monitoring.actions.refresh')}
          </Button>
          <Button onClick={handleHealthCheck} disabled={refreshing}>
            <Activity className="h-4 w-4 mr-2" />
            {t('monitoring.actions.healthCheck')}
          </Button>
        </div>
      </motion.div>

      {/* System Status Alert */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1, duration: 0.5 }}
      >
        <Alert className={cn(
          "border-0 shadow-sm",
          (systemStatus === ServiceStatus.HEALTHY || systemStatus === 'healthy') && "bg-green-50 dark:bg-green-950/20",
          (systemStatus === ServiceStatus.DEGRADED || systemStatus === 'degraded') && "bg-yellow-50 dark:bg-yellow-950/20",
          (systemStatus === ServiceStatus.UNHEALTHY || systemStatus === 'unhealthy') && "bg-red-50 dark:bg-red-950/20"
        )}>
          <div className="flex items-center gap-3">
            {(systemStatus === ServiceStatus.HEALTHY || systemStatus === 'healthy') && <CheckCircle2 className="h-5 w-5 text-green-600" />}
            {(systemStatus === ServiceStatus.DEGRADED || systemStatus === 'degraded') && <AlertTriangle className="h-5 w-5 text-yellow-600" />}
            {(systemStatus === ServiceStatus.UNHEALTHY || systemStatus === 'unhealthy') && <AlertCircle className="h-5 w-5 text-red-600" />}
            <div className="flex-1">
              <AlertTitle>
                {t('monitoring.systemStatus')}: {t(`monitoring.status.${systemStatus}`)}
              </AlertTitle>
              <AlertDescription>
                <div className="space-y-1">
                  <div>
                    {t('monitoring.messages.servicesRunning', {
                      healthy: serviceStats.healthy,
                      total: serviceStats.total
                    })}
                    {serviceStats.degraded > 0 && ` • ${t('monitoring.messages.servicesDegraded', { count: serviceStats.degraded })}`}
                    {serviceStats.unhealthy > 0 && ` • ${t('monitoring.messages.servicesUnhealthy', { count: serviceStats.unhealthy })}`}
                  </div>
                  {services.length > 0 && (
                    <div>
                      {t('monitoring.metrics.averageResponseTime')}: {Math.round(services.reduce((sum, s) => sum + (s.responseTime || 0), 0) / services.length)}ms
                    </div>
                  )}
                  {error && (
                    <div className="text-red-600 text-sm">
                      ⚠️ {error} {retryCount > 0 && `(${t('monitoring.retrying')}: ${retryCount}/${maxRetries})`}
                    </div>
                  )}
                </div>
              </AlertDescription>
            </div>
            <div className="text-right text-sm">
              <p className="font-medium">{t('monitoring.systemLoad')}</p>
              <p className="text-muted-foreground">
                CPU: {systemMetrics?.cpu?.usage || 0}% • {t('monitoring.memory')}: {systemMetrics?.memory?.usage || 0}% • {t('monitoring.disk')}: {systemMetrics?.disk?.usage || 0}%
              </p>
            </div>
          </div>
        </Alert>
      </motion.div>

      {/* Metrics Overview */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2, duration: 0.5 }}
        className="grid grid-cols-1 md:grid-cols-4 gap-4"
      >
        <MetricCard
          title={t('monitoring.metrics.cpuUsage')}
          value={systemMetrics?.cpu?.usage || 0}
          unit="%"
          icon={<Cpu className="h-5 w-5" />}
          status={(systemMetrics?.cpu?.usage || 0) < 70 ? 'good' : (systemMetrics?.cpu?.usage || 0) < 85 ? 'warning' : 'critical'}
          trend="stable"
        />
        <MetricCard
          title={t('monitoring.metrics.memoryUsage')}
          value={systemMetrics?.memory?.usage || 0}
          unit="%"
          icon={<Monitor className="h-5 w-5" />}
          status={(systemMetrics?.memory?.usage || 0) < 70 ? 'good' : (systemMetrics?.memory?.usage || 0) < 85 ? 'warning' : 'critical'}
          trend="up"
        />
        <MetricCard
          title={t('monitoring.metrics.diskUsage')}
          value={systemMetrics?.disk?.usage || 0}
          unit="%"
          icon={<HardDrive className="h-5 w-5" />}
          status={(systemMetrics?.disk?.usage || 0) < 70 ? 'good' : (systemMetrics?.disk?.usage || 0) < 85 ? 'warning' : 'critical'}
          trend="stable"
        />
        <MetricCard
          title={t('monitoring.metrics.networkTraffic')}
          value={systemMetrics?.network?.in || 0}
          unit="MB/s"
          icon={<Wifi className="h-5 w-5" />}
          status="good"
          trend="down"
        />
      </motion.div>

      {/* Services and Details */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <Tabs defaultValue="services" className="space-y-4">
          <TabsList className="grid w-full grid-cols-3 max-w-[400px]">
            <TabsTrigger value="services">{t('monitoring.tabs.services')}</TabsTrigger>
            <TabsTrigger value="logs">{t('monitoring.tabs.logs')}</TabsTrigger>
            <TabsTrigger value="alerts">{t('monitoring.tabs.alerts')}</TabsTrigger>
          </TabsList>

          {/* Services Tab */}
          <TabsContent value="services" className="space-y-4">
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>{t('monitoring.serviceHealth.title')}</CardTitle>
                <CardDescription>
                  {t('monitoring.serviceHealth.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
                    <span className="ml-2 text-muted-foreground">{t('monitoring.loading')}</span>
                  </div>
                ) : services.length === 0 ? (
                  <div className="flex items-center justify-center py-8 text-muted-foreground">
                    <AlertCircle className="h-8 w-8 mr-2" />
                    <span>{t('monitoring.noData')}</span>
                    <Button 
                      variant="outline" 
                      size="sm" 
                      className="ml-4" 
                      onClick={() => loadMonitoringData()}
                      disabled={refreshing}
                    >
                      <RefreshCw className={cn("h-4 w-4 mr-2", refreshing && "animate-spin")} />
                      {t('monitoring.actions.retry')}
                    </Button>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                    <AnimatePresence>
                      {services.map(service => (
                        <ServiceCard key={service.name} service={service} />
                      ))}
                    </AnimatePresence>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Logs Tab */}
          <TabsContent value="logs" className="space-y-4">
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>{t('monitoring.logs.title')}</CardTitle>
                <CardDescription>{t('monitoring.logs.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px] w-full rounded-md border p-4">
                  <div className="space-y-2 text-sm font-mono">
                    <div className="flex items-start gap-3">
                      <span className="text-muted-foreground">2024-01-20 15:30:45</span>
                      <Badge className="bg-green-100 text-green-800">INFO</Badge>
                      <span>Ollama health check completed successfully</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-muted-foreground">2024-01-20 15:30:40</span>
                      <Badge className="bg-yellow-100 text-yellow-800">WARN</Badge>
                      <span>Ollama response time exceeds threshold (850ms {'>'} 500ms)</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-muted-foreground">2024-01-20 15:30:30</span>
                      <Badge className="bg-green-100 text-green-800">INFO</Badge>
                      <span>Database backup completed successfully</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-muted-foreground">2024-01-20 15:25:00</span>
                      <Badge className="bg-green-100 text-green-800">INFO</Badge>
                      <span>Celery worker task completed: generate_embeddings</span>
                    </div>
                    <div className="flex items-start gap-3">
                      <span className="text-muted-foreground">2024-01-20 15:20:15</span>
                      <Badge className="bg-blue-100 text-blue-800">DEBUG</Badge>
                      <span>MinIO storage usage: 38% (15.2GB / 40GB)</span>
                    </div>
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Alerts Tab */}
          <TabsContent value="alerts" className="space-y-4">
            <Card className="border-0 shadow-sm">
              <CardHeader>
                <CardTitle>{t('monitoring.alerts.title')}</CardTitle>
                <CardDescription>{t('monitoring.alerts.description')}</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Alert className="border-yellow-300 bg-yellow-50 dark:bg-yellow-950/20">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <AlertTitle>{t('monitoring.alerts.ollamaSlowResponse')}</AlertTitle>
                    <AlertDescription>
                      {t('monitoring.alerts.ollamaSlowResponseDesc')}
                    </AlertDescription>
                  </Alert>
                  <Alert className="border-blue-300 bg-blue-50 dark:bg-blue-950/20">
                    <AlertCircle className="h-4 w-4 text-blue-600" />
                    <AlertTitle>{t('monitoring.alerts.maintenanceReminder')}</AlertTitle>
                    <AlertDescription>
                      {t('monitoring.alerts.maintenanceReminderDesc')}
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </div>
  );
};

export default SystemMonitoringPage;