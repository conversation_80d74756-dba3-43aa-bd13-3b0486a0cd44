/**
 * Talent Pool Mock Service
 * Provides mock data for all talent pool APIs until backend is implemented
 * TODO: Remove this file once backend APIs are ready
 */

import {
  TalentPoolCandidate,
  TalentPoolListResponse,
  TalentPoolAnalytics,
  SavedFilterSet,
  TalentCampaign,
  TalentPoolReport,
  BulkOperation,
  TalentPipelineStage,
  EngagementLevel,
  AvailabilityStatus,
  ContactType,
  CampaignStatus,
  CampaignType,
  ReportType,
  BulkOperationType,
} from '@/types/recruitment';

/**
 * Generate mock talent pool candidates
 */
export const generateMockCandidates = (count: number = 20): TalentPoolCandidate[] => {
  const candidates: TalentPoolCandidate[] = [];
  
  const names = [
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
    '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'
  ];
  
  const companies = ['Tech Corp', 'Innovation Labs', 'Digital Solutions', 'Future Systems', 'Cloud Dynamics'];
  const positions = ['Senior Engineer', 'Product Manager', 'Data Scientist', 'UX Designer', 'DevOps Engineer'];
  const skills = ['React', 'Node.js', 'Python', 'AWS', 'Docker', 'Kubernetes', 'TypeScript', 'GraphQL', 'MongoDB', 'PostgreSQL'];
  const locations = ['San Francisco', '<PERSON>', 'Seattle', 'Austin', 'Boston'];
  
  for (let i = 0; i < count; i++) {
    const randomSkills = skills.sort(() => 0.5 - Math.random()).slice(0, Math.floor(Math.random() * 5) + 3);
    
    candidates.push({
      id: `candidate-${i + 1}`,
      name: names[i % names.length],
      email: `${names[i % names.length].toLowerCase().replace(' ', '.')}@example.com`,
      phone: `******-${String(Math.floor(Math.random() * 900) + 100)}-${String(Math.floor(Math.random() * 9000) + 1000)}`,
      current_position: positions[Math.floor(Math.random() * positions.length)],
      current_company: companies[Math.floor(Math.random() * companies.length)],
      location: locations[Math.floor(Math.random() * locations.length)],
      years_of_experience: Math.floor(Math.random() * 15) + 1,
      expected_salary: Math.floor(Math.random() * 100000) + 80000,
      skill_tags: randomSkills,
      overall_talent_score: Math.floor(Math.random() * 30) + 70,
      dci_score: Math.floor(Math.random() * 30) + 70,
      jfs_score: Math.floor(Math.random() * 30) + 70,
      pipeline_stage: Object.values(TalentPipelineStage)[Math.floor(Math.random() * Object.values(TalentPipelineStage).length)],
      engagement_level: Object.values(EngagementLevel)[Math.floor(Math.random() * Object.values(EngagementLevel).length)],
      availability_status: Object.values(AvailabilityStatus)[Math.floor(Math.random() * Object.values(AvailabilityStatus).length)],
      source: 'LinkedIn',
      sourced_by: 'System',
      total_interactions: Math.floor(Math.random() * 20),
      last_contact_date: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
      next_follow_up_date: Math.random() > 0.5 ? new Date(Date.now() + Math.random() * 14 * 24 * 60 * 60 * 1000).toISOString() : undefined,
      relationship_status: 'active',
      notes: 'Mock candidate data',
      linkedin_url: `https://linkedin.com/in/${names[i % names.length].toLowerCase().replace(' ', '-')}`,
      resume_url: undefined,
      tags: randomSkills.slice(0, 2),
      priority_level: Math.random() > 0.7 ? 'high' : Math.random() > 0.4 ? 'medium' : 'low',
      is_archived: false,
      created_at: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString(),
    });
  }
  
  return candidates;
};

/**
 * Generate mock saved filter sets
 */
export const generateMockFilterSets = (): SavedFilterSet[] => {
  return [
    {
      id: 'filter-1',
      name: 'Senior Engineers - Active',
      description: 'Senior level engineers who are actively looking',
      filters: {
        experience_min: 5,
        skills: ['React', 'Node.js'],
        engagement_levels: [EngagementLevel.HOT, EngagementLevel.WARM],
        availability_status: [AvailabilityStatus.ACTIVELY_LOOKING],
      },
      created_by: 'Admin',
      created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      is_default: true,
      usage_count: 45,
    },
    {
      id: 'filter-2',
      name: 'Product Managers - Bay Area',
      description: 'Product managers located in San Francisco Bay Area',
      filters: {
        search: 'product manager',
        locations: ['San Francisco', 'San Jose', 'Oakland'],
        experience_min: 3,
      },
      created_by: 'Admin',
      created_at: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
      is_default: false,
      usage_count: 23,
    },
    {
      id: 'filter-3',
      name: 'High Priority - Follow Up',
      description: 'High priority candidates needing follow-up',
      filters: {
        priority_levels: ['high', 'critical'],
        has_follow_up: true,
      },
      created_by: 'Admin',
      created_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),
      is_default: false,
      usage_count: 67,
    },
  ];
};

/**
 * Generate mock campaigns
 */
export const generateMockCampaigns = (): TalentCampaign[] => {
  return [
    {
      id: 'campaign-1',
      name: 'Q1 Engineering Hiring Drive',
      description: 'Recruiting senior engineers for Q1 expansion',
      type: CampaignType.RECRUITMENT,
      status: CampaignStatus.ACTIVE,
      target_candidates: 50,
      contacted_candidates: 32,
      responded_candidates: 18,
      converted_candidates: 5,
      start_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
      end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      created_by: 'Admin',
      created_at: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000).toISOString(),
      updated_at: new Date().toISOString(),
    },
    {
      id: 'campaign-2',
      name: 'Product Team Expansion',
      description: 'Building out the product management team',
      type: CampaignType.RECRUITMENT,
      status: CampaignStatus.PLANNED,
      target_candidates: 20,
      contacted_candidates: 0,
      responded_candidates: 0,
      converted_candidates: 0,
      start_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      end_date: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString(),
      created_by: 'Admin',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    },
  ];
};

/**
 * Generate mock reports
 */
export const generateMockReports = (): TalentPoolReport[] => {
  return [
    {
      id: 'report-1',
      name: 'Monthly Talent Pipeline Report',
      type: ReportType.PIPELINE_ANALYSIS,
      description: 'Monthly analysis of talent pipeline metrics',
      generated_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
      generated_by: 'System',
      file_url: '/reports/monthly-pipeline-report.pdf',
      status: 'completed',
      parameters: {
        date_range: 'last_30_days',
        include_archived: false,
      },
    },
    {
      id: 'report-2',
      name: 'Engagement Metrics Report',
      type: ReportType.ENGAGEMENT_METRICS,
      description: 'Candidate engagement and response rates',
      generated_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
      generated_by: 'Admin',
      file_url: '/reports/engagement-metrics.pdf',
      status: 'completed',
      parameters: {
        date_range: 'last_quarter',
        group_by: 'source',
      },
    },
  ];
};

/**
 * Generate mock analytics
 */
export const generateMockAnalytics = (): TalentPoolAnalytics => {
  return {
    total_candidates: 486,
    active_candidates: 412,
    archived_candidates: 74,
    
    pipeline_distribution: {
      [TalentPipelineStage.SOURCED]: 145,
      [TalentPipelineStage.CONTACTED]: 98,
      [TalentPipelineStage.ENGAGED]: 76,
      [TalentPipelineStage.SCREENED]: 54,
      [TalentPipelineStage.INTERVIEWING]: 32,
      [TalentPipelineStage.OFFER_EXTENDED]: 12,
      [TalentPipelineStage.HIRED]: 8,
      [TalentPipelineStage.REJECTED]: 45,
      [TalentPipelineStage.WITHDRAWN]: 16,
    },
    
    engagement_metrics: {
      hot_leads: 42,
      warm_leads: 98,
      cold_leads: 156,
      unresponsive: 116,
    },
    
    source_distribution: {
      'LinkedIn': 186,
      'Referral': 124,
      'Direct Application': 89,
      'Job Board': 67,
      'Other': 20,
    },
    
    conversion_rates: {
      sourced_to_contacted: 67.5,
      contacted_to_engaged: 77.5,
      engaged_to_screened: 71.0,
      screened_to_interview: 59.2,
      interview_to_offer: 37.5,
      offer_to_hire: 66.7,
    },
    
    average_time_in_stage: {
      [TalentPipelineStage.SOURCED]: 3.5,
      [TalentPipelineStage.CONTACTED]: 2.8,
      [TalentPipelineStage.ENGAGED]: 4.2,
      [TalentPipelineStage.SCREENED]: 3.1,
      [TalentPipelineStage.INTERVIEWING]: 7.4,
      [TalentPipelineStage.OFFER_EXTENDED]: 2.3,
    },
    
    top_skills: [
      { skill: 'React', count: 234 },
      { skill: 'Node.js', count: 198 },
      { skill: 'Python', count: 176 },
      { skill: 'AWS', count: 145 },
      { skill: 'TypeScript', count: 132 },
    ],
    
    location_distribution: {
      'San Francisco': 145,
      'New York': 112,
      'Seattle': 89,
      'Austin': 67,
      'Boston': 45,
      'Remote': 28,
    },
    
    response_rate: 45.6,
    average_response_time: 2.3,
    
    monthly_trends: {
      candidates_added: [32, 45, 38, 52, 41, 48],
      candidates_hired: [2, 3, 2, 4, 3, 3],
      response_rate: [42.3, 44.5, 45.1, 46.8, 45.2, 45.6],
    },
  };
};

/**
 * Create mock bulk operation response
 */
export const createMockBulkOperation = (type: BulkOperationType, candidateIds: string[]): BulkOperation => {
  return {
    id: `bulk-op-${Date.now()}`,
    type,
    status: 'completed',
    total_items: candidateIds.length,
    processed_items: candidateIds.length,
    failed_items: 0,
    started_at: new Date().toISOString(),
    completed_at: new Date().toISOString(),
    created_by: 'Admin',
    results: {
      successful: candidateIds,
      failed: [],
    },
  };
};

/**
 * Mock service class that mimics the real TalentPoolService
 */
export class TalentPoolMockService {
  private mockCandidates: TalentPoolCandidate[] = generateMockCandidates(50);
  private mockFilterSets: SavedFilterSet[] = generateMockFilterSets();
  private mockCampaigns: TalentCampaign[] = generateMockCampaigns();
  private mockReports: TalentPoolReport[] = generateMockReports();

  async getCandidates(filters: any = {}, skip = 0, limit = 20): Promise<TalentPoolListResponse> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    let filtered = [...this.mockCandidates];
    
    // Apply filters
    if (filters.search) {
      const search = filters.search.toLowerCase();
      filtered = filtered.filter(c => 
        c.name.toLowerCase().includes(search) ||
        c.email.toLowerCase().includes(search) ||
        c.current_position?.toLowerCase().includes(search)
      );
    }
    
    if (filters.pipeline_stages?.length) {
      filtered = filtered.filter(c => filters.pipeline_stages.includes(c.pipeline_stage));
    }
    
    // Pagination
    const total = filtered.length;
    const items = filtered.slice(skip, skip + limit);
    
    return {
      items,
      total,
      skip,
      limit,
    };
  }

  async getSavedFilterSets(): Promise<SavedFilterSet[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return this.mockFilterSets;
  }

  async saveFilterSet(name: string, filters: any): Promise<SavedFilterSet> {
    await new Promise(resolve => setTimeout(resolve, 200));
    const newFilter: SavedFilterSet = {
      id: `filter-${Date.now()}`,
      name,
      filters,
      created_by: 'Admin',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      is_default: false,
      usage_count: 0,
    };
    this.mockFilterSets.push(newFilter);
    return newFilter;
  }

  async getCampaigns(): Promise<TalentCampaign[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return this.mockCampaigns;
  }

  async createCampaign(campaign: Partial<TalentCampaign>): Promise<TalentCampaign> {
    await new Promise(resolve => setTimeout(resolve, 200));
    const newCampaign: TalentCampaign = {
      id: `campaign-${Date.now()}`,
      name: campaign.name || 'New Campaign',
      description: campaign.description || '',
      type: campaign.type || CampaignType.RECRUITMENT,
      status: CampaignStatus.PLANNED,
      target_candidates: campaign.target_candidates || 0,
      contacted_candidates: 0,
      responded_candidates: 0,
      converted_candidates: 0,
      start_date: campaign.start_date || new Date().toISOString(),
      end_date: campaign.end_date || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      created_by: 'Admin',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    };
    this.mockCampaigns.push(newCampaign);
    return newCampaign;
  }

  async getReports(): Promise<TalentPoolReport[]> {
    await new Promise(resolve => setTimeout(resolve, 200));
    return this.mockReports;
  }

  async getAnalytics(): Promise<TalentPoolAnalytics> {
    await new Promise(resolve => setTimeout(resolve, 300));
    return generateMockAnalytics();
  }

  async bulkOperation(type: BulkOperationType, candidateIds: string[], data?: any): Promise<BulkOperation> {
    await new Promise(resolve => setTimeout(resolve, 500));
    return createMockBulkOperation(type, candidateIds);
  }

  async exportData(filters: any): Promise<{ url: string }> {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { url: '/mock-export/talent-pool-export.csv' };
  }

  async importData(file: File): Promise<BulkOperation> {
    await new Promise(resolve => setTimeout(resolve, 1500));
    return createMockBulkOperation(BulkOperationType.IMPORT, ['import-1', 'import-2', 'import-3']);
  }
}

// Export singleton instance
export const talentPoolMockService = new TalentPoolMockService();

// Export default mock service
export default talentPoolMockService;