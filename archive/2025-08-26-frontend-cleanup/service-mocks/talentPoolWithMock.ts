/**
 * Talent Pool Service with <PERSON><PERSON> Fallback
 * Wraps the real TalentPoolService and provides mock data when backend returns 404
 * TODO: Remove this wrapper when backend is fully implemented
 */

import { TalentPoolService } from './talentPool';
import { talentPoolMockService } from './talentPoolMock';
import {
  TalentPoolFilters,
  TalentPoolListResponse,
  SavedFilterSet,
  TalentCampaign,
  TalentPoolReport,
  TalentPoolAnalytics,
  BulkOperation,
  BulkOperationType,
  TalentPipelineStage,
  TalentPoolCandidate,
} from '@/types/recruitment';

/**
 * Helper function to handle 404 errors with mock fallback
 */
async function withMockFallback<T>(
  apiCall: () => Promise<T>,
  mockCall: () => Promise<T>,
  description?: string
): Promise<T> {
  try {
    return await apiCall();
  } catch (error: any) {
    if (error.response?.status === 404) {
      if (description) {
        console.log(`📝 Using mock data for ${description} (backend not implemented)`);
      }
      return await mockCall();
    }
    throw error;
  }
}

/**
 * Enhanced TalentPoolService with automatic mock fallback
 */
class TalentPoolServiceWithMock extends TalentPoolService {
  /**
   * Get talent pool candidates with mock fallback
   */
  async getCandidates(
    filters: TalentPoolFilters = {},
    skip = 0,
    limit = 20
  ): Promise<TalentPoolListResponse> {
    return withMockFallback(
      () => super.getCandidates(filters, skip, limit),
      () => talentPoolMockService.getCandidates(filters, skip, limit),
      'talent pool candidates'
    );
  }

  /**
   * Get saved filter sets with mock fallback
   */
  async getSavedFilterSets(): Promise<SavedFilterSet[]> {
    return withMockFallback(
      () => super.getSavedFilterSets(),
      () => talentPoolMockService.getSavedFilterSets(),
      'saved filter sets'
    );
  }

  /**
   * Save filter set with mock fallback
   */
  async saveFilterSet(
    name: string,
    filters: TalentPoolFilters,
    description?: string,
    is_default = false
  ): Promise<SavedFilterSet> {
    return withMockFallback(
      () => super.saveFilterSet(name, filters, description, is_default),
      () => talentPoolMockService.saveFilterSet(name, filters),
      'save filter set'
    );
  }

  /**
   * Get campaigns with mock fallback
   */
  async getCampaigns(): Promise<{ items: TalentCampaign[] }> {
    return withMockFallback(
      () => super.getCampaigns(),
      async () => ({ items: await talentPoolMockService.getCampaigns() }),
      'campaigns'
    );
  }

  /**
   * Create campaign with mock fallback
   */
  async createCampaign(campaignData: any): Promise<TalentCampaign> {
    return withMockFallback(
      () => super.createCampaign(campaignData),
      () => talentPoolMockService.createCampaign(campaignData),
      'create campaign'
    );
  }

  /**
   * Get reports with mock fallback
   */
  async getReports(): Promise<{ items: TalentPoolReport[] }> {
    return withMockFallback(
      () => super.getReports(),
      async () => ({ items: await talentPoolMockService.getReports() }),
      'reports'
    );
  }

  /**
   * Get analytics with mock fallback
   */
  async getAnalytics(): Promise<TalentPoolAnalytics> {
    return withMockFallback(
      () => super.getAnalytics(),
      () => talentPoolMockService.getAnalytics(),
      'analytics'
    );
  }

  /**
   * Bulk operation with mock fallback
   */
  async bulkOperation(
    type: BulkOperationType,
    candidateIds: string[],
    data?: any
  ): Promise<BulkOperation> {
    return withMockFallback(
      () => super.bulkOperation(type, candidateIds, data),
      () => talentPoolMockService.bulkOperation(type, candidateIds, data),
      'bulk operation'
    );
  }

  /**
   * Move candidate to stage with mock fallback
   */
  async moveToStage(
    candidateId: string,
    newStage: TalentPipelineStage,
    notes?: string
  ): Promise<TalentPoolCandidate> {
    return withMockFallback(
      () => super.moveToStage(candidateId, newStage, notes),
      async () => {
        // Mock implementation
        console.log(`📝 Mock: Moving candidate ${candidateId} to stage ${newStage}`);
        const candidates = await talentPoolMockService.getCandidates();
        const candidate = candidates.items.find(c => c.id === candidateId);
        if (candidate) {
          candidate.pipeline_stage = newStage;
          return candidate;
        }
        throw new Error('Candidate not found');
      },
      'move to stage'
    );
  }

  /**
   * Export candidates with mock fallback
   */
  async exportCandidates(
    filters: TalentPoolFilters,
    format: 'csv' | 'xlsx' = 'xlsx'
  ): Promise<{ url: string; filename: string }> {
    return withMockFallback(
      () => super.exportCandidates(filters, format),
      async () => {
        const result = await talentPoolMockService.exportData(filters);
        return {
          url: result.url,
          filename: `talent-pool-export-${Date.now()}.${format}`,
        };
      },
      'export candidates'
    );
  }

  /**
   * Import candidates with mock fallback
   */
  async importCandidates(
    file: File,
    options?: {
      update_existing?: boolean;
      skip_duplicates?: boolean;
    }
  ): Promise<BulkOperation> {
    return withMockFallback(
      () => super.importCandidates(file, options),
      () => talentPoolMockService.importData(file),
      'import candidates'
    );
  }

  /**
   * Batch move candidates to pipeline stage with mock fallback
   */
  async batchMoveToStage(
    candidateIds: string[],
    newStage: TalentPipelineStage,
    notes?: string
  ): Promise<BulkOperation> {
    return withMockFallback(
      () => super.batchMoveToStage(candidateIds, newStage, notes),
      () => talentPoolMockService.bulkOperation(BulkOperationType.MOVE_STAGE, candidateIds, { stage: newStage, notes }),
      'batch move to stage'
    );
  }

  /**
   * Archive candidates with mock fallback
   */
  async archiveCandidates(
    candidateIds: string[],
    reason?: string
  ): Promise<BulkOperation> {
    return withMockFallback(
      () => super.archiveCandidates(candidateIds, reason),
      () => talentPoolMockService.bulkOperation(BulkOperationType.ARCHIVE, candidateIds, { reason }),
      'archive candidates'
    );
  }

  /**
   * Unarchive candidates with mock fallback
   */
  async unarchiveCandidates(candidateIds: string[]): Promise<BulkOperation> {
    return withMockFallback(
      () => super.unarchiveCandidates(candidateIds),
      () => talentPoolMockService.bulkOperation(BulkOperationType.UNARCHIVE, candidateIds),
      'unarchive candidates'
    );
  }

  /**
   * Get health metrics with mock fallback
   */
  async getHealthMetrics(): Promise<any> {
    return withMockFallback(
      () => super.getHealthMetrics(),
      async () => ({
        status: 'healthy',
        total_candidates: 486,
        active_pipelines: 12,
        response_rate: 45.6,
        average_time_to_hire: 28.5,
      }),
      'health metrics'
    );
  }
}

// Export singleton instance with mock fallback
export const talentPoolService = new TalentPoolServiceWithMock();

// Also export the class for testing
export { TalentPoolServiceWithMock };

// Export as default
export default talentPoolService;