'use client';

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { toast } from '@/components/ui/use-toast';
import {
  Shield,
  Lock,
  Globe,
  Users,
  Clock,
  FileText,
  CheckCircle,
  AlertCircle,
  ChevronRight,
  Sparkles,
  Info,
  Calendar,
  Timer,
  User,
} from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { DynamicQuestionRenderer } from '@/components/questionnaire/DynamicQuestionRenderer';
import { aiQuestionnaireService } from '@/services/aiQuestionnaireService';

interface QuestionnaireAccess {
  id: string;
  title: string;
  description?: string;
  access_type: 'PUBLIC' | 'PASSWORD' | 'PRIVATE';
  status: 'DRAFT' | 'PUBLISHED' | 'REVIEWING' | 'ARCHIVED';
  valid_until?: string;
  max_submissions?: number;
  current_submissions?: number;
  estimated_time?: number;
  ai_generated?: boolean;
  questions: any[];
  settings?: {
    allow_save_progress?: boolean;
    show_progress_bar?: boolean;
    randomize_questions?: boolean;
    require_all_questions?: boolean;
    notification_settings?: {
      send_confirmation?: boolean;
      send_reminder?: boolean;
    };
  };
}

export default function PublicQuestionnairePage() {
  const params = useParams();
  const router = useRouter();
  const slug = params.slug as string;

  const [questionnaire, setQuestionnaire] = useState<QuestionnaireAccess | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [accessGranted, setAccessGranted] = useState(false);
  const [password, setPassword] = useState('');
  const [passwordError, setPasswordError] = useState('');
  const [candidateInfo, setCandidateInfo] = useState({
    name: '',
    email: '',
    phone: '',
    position: '',
  });
  const [responses, setResponses] = useState<Record<string, any>>({});
  const [currentPage, setCurrentPage] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submissionComplete, setSubmissionComplete] = useState(false);
  const [sessionId, setSessionId] = useState<string>('');

  useEffect(() => {
    loadQuestionnaire();
    // Generate session ID for tracking
    setSessionId(`session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`);
  }, [slug]);

  const loadQuestionnaire = async () => {
    try {
      setLoading(true);
      // In real implementation, this would be a public API endpoint
      const data = await aiQuestionnaireService.getPublicQuestionnaire(slug);
      
      if (data) {
        setQuestionnaire(data);
        
        // Check if questionnaire is expired
        if (data.valid_until && new Date(data.valid_until) < new Date()) {
          setError('This questionnaire has expired');
          return;
        }
        
        // Check if max submissions reached
        if (data.max_submissions && data.current_submissions && 
            data.current_submissions >= data.max_submissions) {
          setError('This questionnaire has reached its submission limit');
          return;
        }
        
        // Grant immediate access for PUBLIC questionnaires
        if (data.access_type === 'PUBLIC') {
          setAccessGranted(true);
        }
      } else {
        setError('Questionnaire not found');
      }
    } catch (err) {
      console.error('Failed to load questionnaire:', err);
      setError('Failed to load questionnaire');
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordSubmit = async () => {
    if (!password.trim()) {
      setPasswordError('Please enter a password');
      return;
    }

    try {
      // Verify password with backend
      const isValid = await aiQuestionnaireService.verifyQuestionnairePassword(slug, password);
      
      if (isValid) {
        setAccessGranted(true);
        setPasswordError('');
      } else {
        setPasswordError('Incorrect password');
      }
    } catch (err) {
      setPasswordError('Failed to verify password');
    }
  };

  const handleCandidateInfoSubmit = () => {
    const { name, email, position } = candidateInfo;
    
    if (!name.trim() || !email.trim() || !position.trim()) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in all required fields',
        variant: 'destructive',
      });
      return;
    }

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      toast({
        title: 'Invalid Email',
        description: 'Please enter a valid email address',
        variant: 'destructive',
      });
      return;
    }

    setCurrentPage(1);
  };

  const handleResponseChange = (questionId: string, value: any) => {
    setResponses(prev => ({
      ...prev,
      [questionId]: value,
    }));
  };

  const handleSubmit = async () => {
    if (!questionnaire) return;

    try {
      setIsSubmitting(true);

      const submissionData = {
        questionnaire_id: questionnaire.id,
        session_id: sessionId,
        candidate_info: candidateInfo,
        responses,
        submitted_at: new Date().toISOString(),
        completion_time: calculateCompletionTime(),
      };

      await aiQuestionnaireService.submitQuestionnaireResponse(submissionData);
      
      setSubmissionComplete(true);
      
      toast({
        title: '提交成功',
        description: '感谢您完成问卷调查！',
      });
    } catch (err) {
      console.error('Failed to submit questionnaire:', err);
      toast({
        title: '提交失败',
        description: '提交问卷时出现错误，请重试',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const calculateCompletionTime = () => {
    // Calculate time spent on questionnaire
    return Math.floor((Date.now() - parseInt(sessionId.split('_')[1])) / 1000);
  };

  const calculateProgress = () => {
    if (!questionnaire) return 0;
    const answeredCount = Object.keys(responses).length;
    const totalQuestions = questionnaire.questions.length;
    return Math.round((answeredCount / totalQuestions) * 100);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">加载问卷中...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardHeader>
            <div className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              <CardTitle>无法访问问卷</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={() => router.push('/')} className="w-full">
              返回首页
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (submissionComplete) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardHeader>
            <div className="flex items-center gap-2 text-green-600">
              <CheckCircle className="h-6 w-6" />
              <CardTitle>提交成功</CardTitle>
            </div>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-gray-600 mb-6">
              感谢您完成问卷调查！您的回答已成功提交。
            </p>
            {questionnaire?.settings?.notification_settings?.send_confirmation && (
              <Alert className="mb-6">
                <Info className="h-4 w-4" />
                <AlertDescription>
                  确认邮件已发送至 {candidateInfo.email}
                </AlertDescription>
              </Alert>
            )}
            <Button onClick={() => router.push('/')} className="w-full">
              返回首页
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!questionnaire) {
    return null;
  }

  // Password protection screen
  if (questionnaire.access_type === 'PASSWORD' && !accessGranted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardHeader>
            <div className="flex items-center gap-2">
              <Lock className="h-5 w-5 text-primary" />
              <CardTitle>受密码保护的问卷</CardTitle>
            </div>
            <CardDescription>
              请输入访问密码以继续
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Input
                  type="password"
                  placeholder="输入访问密码"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handlePasswordSubmit()}
                />
                {passwordError && (
                  <p className="text-sm text-red-600 mt-1">{passwordError}</p>
                )}
              </div>
              <Button onClick={handlePasswordSubmit} className="w-full">
                验证密码
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Private questionnaire block
  if (questionnaire.access_type === 'PRIVATE') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
        <Card className="max-w-md w-full">
          <CardHeader>
            <div className="flex items-center gap-2 text-orange-600">
              <Shield className="h-5 w-5" />
              <CardTitle>私有问卷</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              此问卷仅限特定用户访问。如果您收到了访问邀请，请通过邮件中的专属链接访问。
            </p>
            <Button onClick={() => router.push('/')} className="w-full">
              返回首页
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Main questionnaire interface
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-5xl mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {questionnaire.ai_generated && (
                <Sparkles className="h-5 w-5 text-primary" />
              )}
              <h1 className="text-xl font-semibold">{questionnaire.title}</h1>
            </div>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              {questionnaire.estimated_time && (
                <div className="flex items-center gap-1">
                  <Timer className="h-4 w-4" />
                  <span>约{questionnaire.estimated_time}分钟</span>
                </div>
              )}
              {questionnaire.valid_until && (
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>
                    截止: {format(new Date(questionnaire.valid_until), 'MM月dd日', { locale: zhCN })}
                  </span>
                </div>
              )}
            </div>
          </div>
          
          {/* Progress bar */}
          {currentPage > 0 && questionnaire.settings?.show_progress_bar && (
            <div className="mt-4">
              <Progress value={calculateProgress()} className="h-2" />
              <p className="text-xs text-gray-500 mt-1">
                已完成 {calculateProgress()}%
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Content */}
      <div className="max-w-5xl mx-auto px-4 py-8">
        {currentPage === 0 ? (
          // Candidate information collection
          <Card>
            <CardHeader>
              <CardTitle>候选人信息</CardTitle>
              <CardDescription>
                请填写您的基本信息以开始问卷调查
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {questionnaire.description && (
                  <Alert>
                    <Info className="h-4 w-4" />
                    <AlertDescription>{questionnaire.description}</AlertDescription>
                  </Alert>
                )}
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      姓名 <span className="text-red-500">*</span>
                    </label>
                    <Input
                      placeholder="请输入您的姓名"
                      value={candidateInfo.name}
                      onChange={(e) => setCandidateInfo(prev => ({ ...prev, name: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      邮箱 <span className="text-red-500">*</span>
                    </label>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      value={candidateInfo.email}
                      onChange={(e) => setCandidateInfo(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      电话
                    </label>
                    <Input
                      placeholder="请输入您的联系电话"
                      value={candidateInfo.phone}
                      onChange={(e) => setCandidateInfo(prev => ({ ...prev, phone: e.target.value }))}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      应聘岗位 <span className="text-red-500">*</span>
                    </label>
                    <Input
                      placeholder="请输入应聘岗位"
                      value={candidateInfo.position}
                      onChange={(e) => setCandidateInfo(prev => ({ ...prev, position: e.target.value }))}
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button 
                    onClick={handleCandidateInfoSubmit}
                    className="gap-2"
                  >
                    开始答题
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          // Question rendering
          <Card>
            <CardContent className="pt-6">
              <DynamicQuestionRenderer
                questions={questionnaire.questions}
                responses={responses}
                onResponseChange={handleResponseChange}
                onSubmit={handleSubmit}
                settings={{
                  allowSaveProgress: questionnaire.settings?.allow_save_progress,
                  autoSaveInterval: 30000,
                  showProgressBar: questionnaire.settings?.show_progress_bar,
                  requireAllQuestions: questionnaire.settings?.require_all_questions,
                }}
                isSubmitting={isSubmitting}
              />
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}