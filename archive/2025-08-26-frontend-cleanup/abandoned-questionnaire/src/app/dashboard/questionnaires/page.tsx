'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  Plus, 
  Search, 
  Filter,
  MoreVertical,
  Sparkles,
  FileText,
  Edit,
  Copy,
  Trash,
  Eye,
  Send,
  CheckCircle,
  XCircle,
  Clock,
  BarChart,
  Users,
  Download
} from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { useToast } from '@/hooks/use-toast';
import { AIQuestionnaireWizard } from '@/components/questionnaire/AIQuestionnaireWizard';
import { aiQuestionnaireService } from '@/services/aiQuestionnaireService';

interface Questionnaire {
  id: string;
  title: string;
  description?: string;
  position_type?: string;
  status: string;
  ai_generated: boolean;
  question_count: number;
  response_count: number;
  created_at: string;
  updated_at: string;
  created_by: string;
  is_published: boolean;
  slug?: string;
}

export default function QuestionnairesPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [questionnaires, setQuestionnaires] = useState<Questionnaire[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [showWizard, setShowWizard] = useState(false);
  const [selectedTab, setSelectedTab] = useState('all');

  useEffect(() => {
    loadQuestionnaires();
  }, []);

  const loadQuestionnaires = async () => {
    try {
      setLoading(true);
      // Mock data for now - replace with actual API call
      const mockData: Questionnaire[] = [
        {
          id: '1',
          title: '生产技术岗能力评估问卷',
          description: '评估烟草生产技术人员的专业能力和综合素质',
          position_type: '生产技术岗',
          status: 'published',
          ai_generated: true,
          question_count: 20,
          response_count: 15,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          created_by: 'admin',
          is_published: true,
          slug: 'production-tech-assessment'
        },
        {
          id: '2',
          title: '营销管理岗位评估',
          description: '评估营销管理人员的市场洞察力和管理能力',
          position_type: '营销管理岗',
          status: 'draft',
          ai_generated: true,
          question_count: 25,
          response_count: 0,
          created_at: new Date(Date.now() - 86400000).toISOString(),
          updated_at: new Date(Date.now() - 86400000).toISOString(),
          created_by: 'admin',
          is_published: false
        },
        {
          id: '3',
          title: '质检岗专业能力测试',
          description: '评估质检人员的专业检测能力和质量意识',
          position_type: '质检岗',
          status: 'reviewing',
          ai_generated: true,
          question_count: 18,
          response_count: 8,
          created_at: new Date(Date.now() - 172800000).toISOString(),
          updated_at: new Date(Date.now() - 172800000).toISOString(),
          created_by: 'admin',
          is_published: false
        },
      ];
      setQuestionnaires(mockData);
    } catch (error) {
      console.error('Failed to load questionnaires:', error);
      toast({
        title: '加载失败',
        description: '无法加载问卷列表，请稍后重试',
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateNew = () => {
    setShowWizard(true);
  };

  const handleWizardComplete = (questionnaire: any) => {
    setShowWizard(false);
    loadQuestionnaires();
    toast({
      title: '创建成功',
      description: '问卷已成功创建',
    });
  };

  const handleEdit = (id: string) => {
    router.push(`/dashboard/questionnaires/${id}/edit`);
  };

  const handlePreview = (id: string) => {
    router.push(`/dashboard/questionnaires/${id}/preview`);
  };

  const handlePublish = async (id: string) => {
    try {
      // Call publish API
      toast({
        title: '发布成功',
        description: '问卷已成功发布',
      });
      loadQuestionnaires();
    } catch (error) {
      toast({
        title: '发布失败',
        description: '无法发布问卷，请稍后重试',
        variant: 'destructive',
      });
    }
  };

  const handleDelete = async (id: string) => {
    if (confirm('确定要删除这个问卷吗？此操作不可恢复。')) {
      try {
        // Call delete API
        toast({
          title: '删除成功',
          description: '问卷已成功删除',
        });
        loadQuestionnaires();
      } catch (error) {
        toast({
          title: '删除失败',
          description: '无法删除问卷，请稍后重试',
          variant: 'destructive',
        });
      }
    }
  };

  const handleViewResponses = (id: string) => {
    router.push(`/dashboard/questionnaires/${id}/responses`);
  };

  const handleViewAnalytics = (id: string) => {
    router.push(`/dashboard/questionnaires/${id}/analytics`);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { label: string; variant: any; icon: any }> = {
      draft: { label: '草稿', variant: 'secondary', icon: FileText },
      reviewing: { label: '审核中', variant: 'warning', icon: Clock },
      approved: { label: '已批准', variant: 'success', icon: CheckCircle },
      rejected: { label: '已拒绝', variant: 'destructive', icon: XCircle },
      published: { label: '已发布', variant: 'default', icon: Send },
      archived: { label: '已归档', variant: 'outline', icon: FileText },
    };

    const config = statusConfig[status] || statusConfig.draft;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    );
  };

  const filteredQuestionnaires = questionnaires.filter(q => {
    const matchesSearch = q.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                          q.description?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || q.status === filterStatus;
    const matchesTab = selectedTab === 'all' ||
                       (selectedTab === 'ai' && q.ai_generated) ||
                       (selectedTab === 'manual' && !q.ai_generated) ||
                       (selectedTab === 'published' && q.is_published);
    return matchesSearch && matchesStatus && matchesTab;
  });

  if (showWizard) {
    return (
      <div className="container mx-auto py-8">
        <AIQuestionnaireWizard
          onComplete={handleWizardComplete}
          onCancel={() => setShowWizard(false)}
        />
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold">问卷管理</h1>
          <p className="text-muted-foreground mt-2">
            创建和管理AI智能问卷，评估候选人能力
          </p>
        </div>
        <Button onClick={handleCreateNew}>
          <Sparkles className="h-4 w-4 mr-2" />
          AI 创建问卷
        </Button>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-3">
            <CardDescription>总问卷数</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{questionnaires.length}</div>
            <p className="text-xs text-muted-foreground mt-1">
              AI生成: {questionnaires.filter(q => q.ai_generated).length}
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardDescription>已发布</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {questionnaires.filter(q => q.is_published).length}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              活跃问卷
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardDescription>总回复数</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {questionnaires.reduce((acc, q) => acc + q.response_count, 0)}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              候选人回复
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-3">
            <CardDescription>待审核</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {questionnaires.filter(q => q.status === 'reviewing').length}
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              需要审批
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <div className="flex items-center gap-4 mb-6">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="搜索问卷..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-9"
          />
        </div>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="gap-2">
              <Filter className="h-4 w-4" />
              状态筛选
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            <DropdownMenuItem onClick={() => setFilterStatus('all')}>
              全部状态
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setFilterStatus('draft')}>
              草稿
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setFilterStatus('reviewing')}>
              审核中
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setFilterStatus('published')}>
              已发布
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="all">全部</TabsTrigger>
          <TabsTrigger value="ai" className="gap-1">
            <Sparkles className="h-3 w-3" />
            AI 生成
          </TabsTrigger>
          <TabsTrigger value="manual">手动创建</TabsTrigger>
          <TabsTrigger value="published">已发布</TabsTrigger>
        </TabsList>

        <TabsContent value={selectedTab}>
          <Card>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>问卷标题</TableHead>
                  <TableHead>岗位类型</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>题目数</TableHead>
                  <TableHead>回复数</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      加载中...
                    </TableCell>
                  </TableRow>
                ) : filteredQuestionnaires.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      没有找到符合条件的问卷
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredQuestionnaires.map((questionnaire) => (
                    <TableRow key={questionnaire.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {questionnaire.ai_generated && (
                            <Sparkles className="h-4 w-4 text-primary" />
                          )}
                          <div>
                            <div className="font-medium">{questionnaire.title}</div>
                            {questionnaire.description && (
                              <div className="text-sm text-muted-foreground">
                                {questionnaire.description.substring(0, 50)}...
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {questionnaire.position_type || '通用'}
                        </Badge>
                      </TableCell>
                      <TableCell>{getStatusBadge(questionnaire.status)}</TableCell>
                      <TableCell>{questionnaire.question_count}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3 text-muted-foreground" />
                          {questionnaire.response_count}
                        </div>
                      </TableCell>
                      <TableCell>
                        {format(new Date(questionnaire.created_at), 'MM月dd日', {
                          locale: zhCN,
                        })}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>操作</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => handlePreview(questionnaire.id)}>
                              <Eye className="h-4 w-4 mr-2" />
                              预览
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEdit(questionnaire.id)}>
                              <Edit className="h-4 w-4 mr-2" />
                              编辑
                            </DropdownMenuItem>
                            {!questionnaire.is_published && (
                              <DropdownMenuItem onClick={() => handlePublish(questionnaire.id)}>
                                <Send className="h-4 w-4 mr-2" />
                                发布
                              </DropdownMenuItem>
                            )}
                            {questionnaire.response_count > 0 && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem onClick={() => handleViewResponses(questionnaire.id)}>
                                  <Users className="h-4 w-4 mr-2" />
                                  查看回复
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => handleViewAnalytics(questionnaire.id)}>
                                  <BarChart className="h-4 w-4 mr-2" />
                                  分析报告
                                </DropdownMenuItem>
                              </>
                            )}
                            <DropdownMenuSeparator />
                            <DropdownMenuItem>
                              <Copy className="h-4 w-4 mr-2" />
                              复制
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Download className="h-4 w-4 mr-2" />
                              导出
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDelete(questionnaire.id)}
                              className="text-destructive"
                            >
                              <Trash className="h-4 w-4 mr-2" />
                              删除
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}