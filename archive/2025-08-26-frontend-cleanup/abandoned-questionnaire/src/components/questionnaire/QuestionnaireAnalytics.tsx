'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  <PERSON><PERSON><PERSON>, Bar, LineChart, Line, PieChart, Pie, RadarChart, Radar,
  PolarGrid, PolarAngleAxis, PolarRadiusAxis, Cell,
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  Area, AreaChart, <PERSON>atter, <PERSON>atter<PERSON><PERSON>, Composed<PERSON>hart
} from 'recharts';
import {
  Download, FileSpreadsheet, FileText, TrendingUp, TrendingDown,
  Users, Clock, CheckCircle, XCircle, AlertCircle, BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon, Activity, Filter, Calendar,
  RefreshCw, Share2, Printer, Mail, Eye, Sparkles
} from 'lucide-react';
import { format, subDays, startOfWeek, endOfWeek, eachDayOfInterval } from 'date-fns';
import { zhCN } from 'date-fns/locale';

interface AnalyticsData {
  questionnaire_id: string;
  title: string;
  total_responses: number;
  completion_rate: number;
  average_completion_time: number;
  response_timeline: Array<{
    date: string;
    count: number;
    completed: number;
    abandoned: number;
  }>;
  question_analytics: Array<{
    question_id: string;
    question_title: string;
    question_type: string;
    response_count: number;
    skip_count: number;
    average_time: number;
    distribution: Record<string, number>;
  }>;
  dimension_scores: {
    dimension: string;
    average_score: number;
    min_score: number;
    max_score: number;
    std_deviation: number;
    percentiles: {
      p25: number;
      p50: number;
      p75: number;
      p90: number;
    };
  }[];
  candidate_segments: Array<{
    segment: string;
    count: number;
    percentage: number;
    average_score: number;
  }>;
  conversion_funnel: Array<{
    stage: string;
    count: number;
    percentage: number;
  }>;
  geographic_distribution?: Array<{
    location: string;
    count: number;
    average_score: number;
  }>;
  device_stats: {
    desktop: number;
    mobile: number;
    tablet: number;
  };
  browser_stats: Record<string, number>;
}

interface QuestionnaireAnalyticsProps {
  questionnaireId: string;
  dateRange?: { start: Date; end: Date };
  onExport?: (format: 'excel' | 'pdf' | 'csv') => void;
}

const COLORS = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899', '#06b6d4', '#84cc16'];

export const QuestionnaireAnalytics: React.FC<QuestionnaireAnalyticsProps> = ({
  questionnaireId,
  dateRange,
  onExport
}) => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedMetric, setSelectedMetric] = useState<'responses' | 'completion' | 'time'>('responses');
  const [selectedDimension, setSelectedDimension] = useState<string>('all');
  const [refreshing, setRefreshing] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);

  useEffect(() => {
    loadAnalytics();
  }, [questionnaireId, dateRange]);

  const loadAnalytics = async () => {
    try {
      setLoading(true);
      // Simulated data - replace with actual API call
      const mockData: AnalyticsData = {
        questionnaire_id: questionnaireId,
        title: '烟草行业生产管理岗位评估',
        total_responses: 487,
        completion_rate: 78.5,
        average_completion_time: 1260, // seconds
        response_timeline: generateTimelineData(),
        question_analytics: generateQuestionAnalytics(),
        dimension_scores: generateDimensionScores(),
        candidate_segments: generateSegments(),
        conversion_funnel: generateFunnel(),
        geographic_distribution: generateGeoData(),
        device_stats: {
          desktop: 62,
          mobile: 31,
          tablet: 7
        },
        browser_stats: {
          Chrome: 45,
          Safari: 22,
          Firefox: 18,
          Edge: 15
        }
      };
      
      setAnalyticsData(mockData);
    } catch (error) {
      console.error('Failed to load analytics:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadAnalytics();
    setRefreshing(false);
  };

  const handleExport = async (format: 'excel' | 'pdf' | 'csv') => {
    setExportLoading(true);
    try {
      if (onExport) {
        await onExport(format);
      }
    } finally {
      setExportLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">加载分析数据...</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <Alert>
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>无法加载分析数据</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="h-6 w-6" />
            问卷分析报告
          </h2>
          <p className="text-gray-600 mt-1">{analyticsData.title}</p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            刷新
          </Button>
          <Select value="7days" onValueChange={() => {}}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7days">最近7天</SelectItem>
              <SelectItem value="30days">最近30天</SelectItem>
              <SelectItem value="90days">最近90天</SelectItem>
              <SelectItem value="all">全部时间</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('excel')}
            disabled={exportLoading}
          >
            <FileSpreadsheet className="h-4 w-4 mr-2" />
            导出Excel
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleExport('pdf')}
            disabled={exportLoading}
          >
            <FileText className="h-4 w-4 mr-2" />
            导出PDF
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-4 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">总响应数</p>
                <p className="text-2xl font-bold">{analyticsData.total_responses}</p>
                <p className="text-xs text-green-600 flex items-center mt-1">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  +23% vs 上周
                </p>
              </div>
              <Users className="h-8 w-8 text-primary opacity-20" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">完成率</p>
                <p className="text-2xl font-bold">{analyticsData.completion_rate}%</p>
                <Progress value={analyticsData.completion_rate} className="mt-2 h-1" />
              </div>
              <CheckCircle className="h-8 w-8 text-green-500 opacity-20" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">平均完成时间</p>
                <p className="text-2xl font-bold">
                  {Math.floor(analyticsData.average_completion_time / 60)}分钟
                </p>
                <p className="text-xs text-gray-500 mt-1">
                  中位数: 18分钟
                </p>
              </div>
              <Clock className="h-8 w-8 text-blue-500 opacity-20" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">平均得分</p>
                <p className="text-2xl font-bold">82.3</p>
                <div className="flex items-center gap-1 mt-1">
                  <Badge variant="outline" className="text-xs">优秀</Badge>
                  <Sparkles className="h-3 w-3 text-yellow-500" />
                </div>
              </div>
              <Activity className="h-8 w-8 text-purple-500 opacity-20" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Analytics Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="responses">响应分析</TabsTrigger>
          <TabsTrigger value="questions">题目分析</TabsTrigger>
          <TabsTrigger value="dimensions">维度分析</TabsTrigger>
          <TabsTrigger value="segments">人群分析</TabsTrigger>
          <TabsTrigger value="funnel">转化漏斗</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            {/* Response Timeline */}
            <Card>
              <CardHeader>
                <CardTitle>响应趋势</CardTitle>
                <CardDescription>每日响应数量变化</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={analyticsData.response_timeline}>
                    <defs>
                      <linearGradient id="colorResponses" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.8}/>
                        <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Area type="monotone" dataKey="count" stroke="#3b82f6" fillOpacity={1} fill="url(#colorResponses)" />
                  </AreaChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Device Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>设备分布</CardTitle>
                <CardDescription>响应者使用的设备类型</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={[
                        { name: '桌面端', value: analyticsData.device_stats.desktop },
                        { name: '移动端', value: analyticsData.device_stats.mobile },
                        { name: '平板', value: analyticsData.device_stats.tablet }
                      ]}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {[0, 1, 2].map((index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>

          {/* Completion Funnel */}
          <Card>
            <CardHeader>
              <CardTitle>完成漏斗</CardTitle>
              <CardDescription>用户在问卷各阶段的流失情况</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={analyticsData.conversion_funnel} layout="horizontal">
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis dataKey="stage" type="category" />
                  <Tooltip />
                  <Bar dataKey="count" fill="#3b82f6">
                    {analyticsData.conversion_funnel.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Responses Tab */}
        <TabsContent value="responses" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>响应时间分布</CardTitle>
              <CardDescription>完成问卷所需时间的分布情况</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <ComposedChart data={generateTimeDistribution()}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="time" label={{ value: '完成时间(分钟)', position: 'insideBottom', offset: -5 }} />
                  <YAxis yAxisId="left" label={{ value: '人数', angle: -90, position: 'insideLeft' }} />
                  <YAxis yAxisId="right" orientation="right" label={{ value: '累计百分比', angle: 90, position: 'insideRight' }} />
                  <Tooltip />
                  <Bar yAxisId="left" dataKey="count" fill="#3b82f6" />
                  <Line yAxisId="right" type="monotone" dataKey="cumulative" stroke="#ef4444" strokeWidth={2} />
                </ComposedChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>浏览器分布</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(analyticsData.browser_stats).map(([browser, percentage]) => (
                    <div key={browser} className="flex items-center justify-between">
                      <span className="text-sm">{browser}</span>
                      <div className="flex items-center gap-2">
                        <Progress value={percentage} className="w-24 h-2" />
                        <span className="text-sm text-gray-600 w-10">{percentage}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>地理分布 Top 5</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData.geographic_distribution?.slice(0, 5).map((geo) => (
                    <div key={geo.location} className="flex items-center justify-between">
                      <span className="text-sm">{geo.location}</span>
                      <div className="flex items-center gap-4">
                        <Badge variant="outline">{geo.count}人</Badge>
                        <span className="text-sm text-gray-600">均分: {geo.average_score}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Questions Tab */}
        <TabsContent value="questions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>题目响应分析</CardTitle>
              <CardDescription>各题目的回答情况和耗时统计</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.question_analytics.slice(0, 10).map((question) => (
                  <div key={question.question_id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <h4 className="font-medium">{question.question_title}</h4>
                        <p className="text-sm text-gray-600">
                          类型: {question.question_type} | 回答: {question.response_count} | 跳过: {question.skip_count}
                        </p>
                      </div>
                      <Badge variant={question.skip_count > 10 ? 'destructive' : 'default'}>
                        跳过率: {((question.skip_count / (question.response_count + question.skip_count)) * 100).toFixed(1)}%
                      </Badge>
                    </div>
                    
                    {question.question_type === 'single_choice' && (
                      <ResponsiveContainer width="100%" height={200}>
                        <BarChart data={Object.entries(question.distribution).map(([key, value]) => ({ option: key, count: value }))}>
                          <CartesianGrid strokeDasharray="3 3" />
                          <XAxis dataKey="option" />
                          <YAxis />
                          <Tooltip />
                          <Bar dataKey="count" fill="#3b82f6" />
                        </BarChart>
                      </ResponsiveContainer>
                    )}
                    
                    <div className="mt-2 text-sm text-gray-600">
                      平均耗时: {question.average_time}秒
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Dimensions Tab */}
        <TabsContent value="dimensions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>维度得分分析</CardTitle>
              <CardDescription>各评估维度的得分分布</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <RadarChart data={analyticsData.dimension_scores}>
                  <PolarGrid />
                  <PolarAngleAxis dataKey="dimension" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar name="平均分" dataKey="average_score" stroke="#3b82f6" fill="#3b82f6" fillOpacity={0.6} />
                  <Radar name="最高分" dataKey="max_score" stroke="#10b981" fill="#10b981" fillOpacity={0.3} />
                  <Tooltip />
                  <Legend />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          <div className="grid grid-cols-2 gap-4">
            {analyticsData.dimension_scores.map((dimension) => (
              <Card key={dimension.dimension}>
                <CardHeader>
                  <CardTitle className="text-base">{dimension.dimension}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>平均分</span>
                      <span className="font-medium">{dimension.average_score.toFixed(1)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>标准差</span>
                      <span className="font-medium">{dimension.std_deviation.toFixed(2)}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>分数范围</span>
                      <span className="font-medium">{dimension.min_score} - {dimension.max_score}</span>
                    </div>
                    <div className="pt-2 border-t">
                      <p className="text-xs text-gray-600 mb-1">百分位数</p>
                      <div className="grid grid-cols-4 gap-2 text-xs">
                        <div>
                          <span className="text-gray-500">P25:</span> {dimension.percentiles.p25}
                        </div>
                        <div>
                          <span className="text-gray-500">P50:</span> {dimension.percentiles.p50}
                        </div>
                        <div>
                          <span className="text-gray-500">P75:</span> {dimension.percentiles.p75}
                        </div>
                        <div>
                          <span className="text-gray-500">P90:</span> {dimension.percentiles.p90}
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        {/* Segments Tab */}
        <TabsContent value="segments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>候选人群体分析</CardTitle>
              <CardDescription>不同群体的表现对比</CardDescription>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={analyticsData.candidate_segments}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="segment" />
                  <YAxis yAxisId="left" orientation="left" stroke="#8884d8" />
                  <YAxis yAxisId="right" orientation="right" stroke="#82ca9d" />
                  <Tooltip />
                  <Legend />
                  <Bar yAxisId="left" dataKey="count" fill="#3b82f6" name="人数" />
                  <Bar yAxisId="right" dataKey="average_score" fill="#10b981" name="平均分" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Funnel Tab */}
        <TabsContent value="funnel" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>转化漏斗详情</CardTitle>
              <CardDescription>用户在各阶段的转化和流失</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData.conversion_funnel.map((stage, index) => (
                  <div key={stage.stage} className="relative">
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 rounded-full bg-primary text-white flex items-center justify-center font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="font-medium">{stage.stage}</h4>
                          <p className="text-sm text-gray-600">
                            {stage.count} 人 ({stage.percentage}%)
                          </p>
                        </div>
                      </div>
                      {index > 0 && (
                        <div className="text-right">
                          <p className="text-sm text-red-600">
                            流失: {analyticsData.conversion_funnel[index - 1].count - stage.count} 人
                          </p>
                          <p className="text-xs text-gray-500">
                            流失率: {((1 - stage.count / analyticsData.conversion_funnel[index - 1].count) * 100).toFixed(1)}%
                          </p>
                        </div>
                      )}
                    </div>
                    {index < analyticsData.conversion_funnel.length - 1 && (
                      <div className="w-0.5 h-8 bg-gray-300 mx-8"></div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

// Helper functions to generate mock data
function generateTimelineData() {
  const data = [];
  for (let i = 29; i >= 0; i--) {
    const date = subDays(new Date(), i);
    data.push({
      date: format(date, 'MM/dd'),
      count: Math.floor(Math.random() * 30) + 10,
      completed: Math.floor(Math.random() * 25) + 8,
      abandoned: Math.floor(Math.random() * 5) + 2
    });
  }
  return data;
}

function generateQuestionAnalytics() {
  return [
    {
      question_id: 'q1',
      question_title: '您对烟草行业的了解程度如何？',
      question_type: 'single_choice',
      response_count: 465,
      skip_count: 22,
      average_time: 15,
      distribution: {
        '非常了解': 125,
        '比较了解': 198,
        '一般了解': 102,
        '不太了解': 40
      }
    },
    {
      question_id: 'q2',
      question_title: '您有多少年的相关工作经验？',
      question_type: 'single_choice',
      response_count: 470,
      skip_count: 17,
      average_time: 12,
      distribution: {
        '0-2年': 89,
        '3-5年': 156,
        '6-10年': 145,
        '10年以上': 80
      }
    }
  ];
}

function generateDimensionScores() {
  return [
    {
      dimension: '工艺流程理解',
      average_score: 78.5,
      min_score: 45,
      max_score: 98,
      std_deviation: 12.3,
      percentiles: { p25: 68, p50: 79, p75: 89, p90: 94 }
    },
    {
      dimension: '质量控制意识',
      average_score: 82.3,
      min_score: 52,
      max_score: 100,
      std_deviation: 10.8,
      percentiles: { p25: 74, p50: 83, p75: 91, p90: 96 }
    },
    {
      dimension: '设备操作能力',
      average_score: 75.2,
      min_score: 40,
      max_score: 95,
      std_deviation: 13.5,
      percentiles: { p25: 65, p50: 76, p75: 85, p90: 91 }
    },
    {
      dimension: '安全生产意识',
      average_score: 88.7,
      min_score: 60,
      max_score: 100,
      std_deviation: 8.9,
      percentiles: { p25: 82, p50: 89, p75: 95, p90: 98 }
    },
    {
      dimension: '问题解决能力',
      average_score: 71.4,
      min_score: 38,
      max_score: 92,
      std_deviation: 14.2,
      percentiles: { p25: 61, p50: 72, p75: 82, p90: 88 }
    }
  ];
}

function generateSegments() {
  return [
    { segment: '应届毕业生', count: 89, percentage: 18.3, average_score: 68.5 },
    { segment: '1-3年经验', count: 125, percentage: 25.7, average_score: 75.3 },
    { segment: '3-5年经验', count: 156, percentage: 32.0, average_score: 82.1 },
    { segment: '5年以上经验', count: 117, percentage: 24.0, average_score: 88.7 }
  ];
}

function generateFunnel() {
  return [
    { stage: '访问问卷', count: 652, percentage: 100 },
    { stage: '开始填写', count: 487, percentage: 74.7 },
    { stage: '完成50%', count: 425, percentage: 65.2 },
    { stage: '完成75%', count: 398, percentage: 61.0 },
    { stage: '提交完成', count: 382, percentage: 58.6 }
  ];
}

function generateGeoData() {
  return [
    { location: '北京', count: 125, average_score: 82.3 },
    { location: '上海', count: 98, average_score: 84.1 },
    { location: '广州', count: 76, average_score: 79.8 },
    { location: '深圳', count: 65, average_score: 81.2 },
    { location: '成都', count: 54, average_score: 77.5 },
    { location: '杭州', count: 42, average_score: 83.6 },
    { location: '武汉', count: 27, average_score: 76.9 }
  ];
}

function generateTimeDistribution() {
  return [
    { time: '0-5', count: 15, cumulative: 3.1 },
    { time: '5-10', count: 48, cumulative: 13.0 },
    { time: '10-15', count: 125, cumulative: 38.7 },
    { time: '15-20', count: 168, cumulative: 73.2 },
    { time: '20-25', count: 89, cumulative: 91.5 },
    { time: '25-30', count: 32, cumulative: 98.1 },
    { time: '30+', count: 10, cumulative: 100 }
  ];
}