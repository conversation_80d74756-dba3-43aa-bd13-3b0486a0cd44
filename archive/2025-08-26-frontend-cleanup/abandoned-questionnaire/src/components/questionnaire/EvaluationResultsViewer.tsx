'use client';

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  ResponsiveContainer,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from 'recharts';
import {
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Award,
  Target,
  Brain,
  Sparkles,
  User,
  Calendar,
  Clock,
} from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { EvaluationReport } from '@/services/aiQuestionnaireService';

interface EvaluationResultsViewerProps {
  report: EvaluationReport;
  questionnaire?: any;
  candidate?: any;
}

export function EvaluationResultsViewer({
  report,
  questionnaire,
  candidate,
}: EvaluationResultsViewerProps) {
  // Prepare data for radar chart
  const radarData = report.dimension_scores
    ? Object.entries(report.dimension_scores).map(([dimension, score]) => ({
        dimension: getDimensionShortName(dimension),
        score: score,
        fullScore: 100,
      }))
    : [];

  // Prepare data for bar chart
  const barData = report.dimension_scores
    ? Object.entries(report.dimension_scores).map(([dimension, score]) => ({
        name: getDimensionShortName(dimension),
        分数: score,
        及格线: 60,
      }))
    : [];

  function getDimensionShortName(dimension: string): string {
    const nameMap: Record<string, string> = {
      '工艺流程理解': '工艺流程',
      '质量控制意识': '质量控制',
      '设备操作能力': '设备操作',
      '安全生产意识': '安全生产',
      '问题解决能力': '问题解决',
      '专卖法规知识': '法规知识',
      '市场分析能力': '市场分析',
      '客户服务意识': '客户服务',
      '营销策略制定': '营销策略',
      '数据分析能力': '数据分析',
    };
    return nameMap[dimension] || dimension;
  }

  function getRiskLevelConfig(level?: string) {
    const configs = {
      low: { label: '低风险', color: 'text-green-600', bgColor: 'bg-green-50', icon: CheckCircle },
      medium: { label: '中风险', color: 'text-yellow-600', bgColor: 'bg-yellow-50', icon: AlertTriangle },
      high: { label: '高风险', color: 'text-red-600', bgColor: 'bg-red-50', icon: XCircle },
    };
    return configs[level as keyof typeof configs] || configs.medium;
  }

  const riskConfig = getRiskLevelConfig(report.risk_level);
  const RiskIcon = riskConfig.icon;

  return (
    <div className="space-y-6">
      {/* Header with Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-start justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-primary" />
                AI 评估报告
              </CardTitle>
              <CardDescription className="mt-2">
                基于 AI 智能分析的综合能力评估
              </CardDescription>
            </div>
            <div className="text-right">
              {report.is_qualified ? (
                <Badge variant="success" className="gap-1">
                  <CheckCircle className="h-3 w-3" />
                  合格
                </Badge>
              ) : (
                <Badge variant="destructive" className="gap-1">
                  <XCircle className="h-3 w-3" />
                  不合格
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4">
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">总分</p>
              <p className="text-2xl font-bold">{report.total_score?.toFixed(1)}%</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">匹配度</p>
              <p className="text-2xl font-bold">{report.match_score?.toFixed(0)}%</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">百分位</p>
              <p className="text-2xl font-bold">
                前{report.percentile_rank?.toFixed(0)}%
              </p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-muted-foreground">风险等级</p>
              <div className={`flex items-center gap-1 ${riskConfig.color}`}>
                <RiskIcon className="h-4 w-4" />
                <span className="font-semibold">{riskConfig.label}</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">总览</TabsTrigger>
          <TabsTrigger value="dimensions">维度分析</TabsTrigger>
          <TabsTrigger value="insights">洞察建议</TabsTrigger>
          <TabsTrigger value="details">详细信息</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Overall Evaluation */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">综合评价</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm leading-relaxed">{report.overall_evaluation}</p>
            </CardContent>
          </Card>

          {/* Score Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">得分分布</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {report.dimension_scores &&
                Object.entries(report.dimension_scores).map(([dimension, score]) => (
                  <div key={dimension} className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>{dimension}</span>
                      <span className="font-medium">{score.toFixed(1)}%</span>
                    </div>
                    <Progress value={score} className="h-2" />
                  </div>
                ))}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="dimensions" className="space-y-4">
          {/* Radar Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">能力雷达图</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <RadarChart data={radarData}>
                  <PolarGrid strokeDasharray="3 3" />
                  <PolarAngleAxis dataKey="dimension" />
                  <PolarRadiusAxis angle={90} domain={[0, 100]} />
                  <Radar
                    name="得分"
                    dataKey="score"
                    stroke="#8884d8"
                    fill="#8884d8"
                    fillOpacity={0.6}
                  />
                </RadarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Bar Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">维度对比</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={barData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="分数" fill="#8884d8" />
                  <Bar dataKey="及格线" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          {/* Strengths */}
          {report.strengths && report.strengths.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <TrendingUp className="h-4 w-4 text-green-600" />
                  优势能力
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {report.strengths.map((strength, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{strength}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Weaknesses */}
          {report.weaknesses && report.weaknesses.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <TrendingDown className="h-4 w-4 text-orange-600" />
                  待改进领域
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  {report.weaknesses.map((weakness, index) => (
                    <li key={index} className="flex items-start gap-2">
                      <AlertTriangle className="h-4 w-4 text-orange-600 mt-0.5 flex-shrink-0" />
                      <span className="text-sm">{weakness}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          )}

          {/* Recommendations */}
          {report.recommendations && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Target className="h-4 w-4 text-blue-600" />
                  发展建议
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm leading-relaxed">{report.recommendations}</p>
              </CardContent>
            </Card>
          )}

          {/* Key Insights */}
          {report.key_insights && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <Brain className="h-4 w-4 text-purple-600" />
                  关键洞察
                </CardTitle>
              </CardHeader>
              <CardContent>
                <Alert>
                  <Sparkles className="h-4 w-4" />
                  <AlertDescription>{report.key_insights}</AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="details" className="space-y-4">
          {/* Candidate Info */}
          {candidate && (
            <Card>
              <CardHeader>
                <CardTitle className="text-base">候选人信息</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">姓名</p>
                      <p className="font-medium">{candidate.name}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Award className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <p className="text-sm text-muted-foreground">应聘岗位</p>
                      <p className="font-medium">{candidate.position}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Evaluation Meta */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">评估信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">评估时间:</span>
                  <span className="text-sm">
                    {report.evaluated_at &&
                      format(new Date(report.evaluated_at), 'yyyy年MM月dd日 HH:mm', {
                        locale: zhCN,
                      })}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">报告生成:</span>
                  <span className="text-sm">
                    {report.created_at &&
                      format(new Date(report.created_at), 'yyyy年MM月dd日 HH:mm', {
                        locale: zhCN,
                      })}
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Sparkles className="h-4 w-4 text-muted-foreground" />
                  <span className="text-sm text-muted-foreground">评估模型:</span>
                  <span className="text-sm">DeepSeek Reasoner v1.0</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Report ID */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">报告编号</CardTitle>
            </CardHeader>
            <CardContent>
              <code className="text-xs bg-muted px-2 py-1 rounded">{report.id}</code>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}