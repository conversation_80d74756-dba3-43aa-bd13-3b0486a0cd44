'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { toast } from '@/components/ui/use-toast';
import {
  Download, FileSpreadsheet, FileText, FilePlus, Archive,
  Check, X, AlertCircle, Loader2, Settings, Calendar,
  Users, BarC<PERSON>3, <PERSON><PERSON><PERSON>, TrendingUp, FileDown
} from 'lucide-react';
import { format } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { saveAs } from 'file-saver';

interface ExportOptions {
  format: 'excel' | 'pdf' | 'csv' | 'json';
  includeAnalytics: boolean;
  includeResponses: boolean;
  includeEvaluations: boolean;
  includeDimensions: boolean;
  includeCharts: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  responseIds?: string[];
  groupBy?: 'date' | 'position' | 'score' | 'none';
  compression?: boolean;
}

interface ExportData {
  questionnaire: any;
  responses?: any[];
  analytics?: any;
  evaluations?: any[];
}

interface ExportManagerProps {
  questionnaireId: string;
  questionnaireTitle: string;
  onExport?: (options: ExportOptions) => Promise<ExportData>;
}

export const ExportManager: React.FC<ExportManagerProps> = ({
  questionnaireId,
  questionnaireTitle,
  onExport
}) => {
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [exportOptions, setExportOptions] = useState<ExportOptions>({
    format: 'excel',
    includeAnalytics: true,
    includeResponses: true,
    includeEvaluations: true,
    includeDimensions: true,
    includeCharts: false,
    groupBy: 'none',
    compression: false
  });
  const [exporting, setExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [exportStatus, setExportStatus] = useState<'idle' | 'preparing' | 'generating' | 'complete' | 'error'>('idle');

  const handleExport = async () => {
    if (!onExport) {
      console.error('No export handler provided');
      return;
    }

    setExporting(true);
    setExportStatus('preparing');
    setExportProgress(10);

    try {
      // Fetch export data
      const data = await onExport(exportOptions);
      setExportProgress(30);
      setExportStatus('generating');

      switch (exportOptions.format) {
        case 'excel':
          await exportToExcel(data);
          break;
        case 'pdf':
          await exportToPDF(data);
          break;
        case 'csv':
          await exportToCSV(data);
          break;
        case 'json':
          await exportToJSON(data);
          break;
      }

      setExportProgress(100);
      setExportStatus('complete');
      
      toast({
        title: '导出成功',
        description: `文件已成功导出为 ${exportOptions.format.toUpperCase()} 格式`,
      });

      // Close dialog after success
      setTimeout(() => {
        setShowExportDialog(false);
        setExportStatus('idle');
        setExportProgress(0);
      }, 1500);

    } catch (error) {
      console.error('Export failed:', error);
      setExportStatus('error');
      
      toast({
        title: '导出失败',
        description: '导出过程中发生错误，请重试',
        variant: 'destructive',
      });
    } finally {
      setExporting(false);
    }
  };

  const exportToExcel = async (data: ExportData) => {
    setExportProgress(40);
    
    const wb = XLSX.utils.book_new();
    
    // Overview sheet
    if (data.questionnaire) {
      const overviewData = [
        ['问卷信息'],
        ['标题', data.questionnaire.title],
        ['创建时间', format(new Date(data.questionnaire.created_at), 'yyyy-MM-dd HH:mm', { locale: zhCN })],
        ['状态', data.questionnaire.status],
        ['总题目数', data.questionnaire.questions?.length || 0],
        ['总响应数', data.responses?.length || 0]
      ];
      
      const overviewWs = XLSX.utils.aoa_to_sheet(overviewData);
      XLSX.utils.book_append_sheet(wb, overviewWs, '概览');
    }
    
    setExportProgress(50);
    
    // Analytics sheet
    if (exportOptions.includeAnalytics && data.analytics) {
      const analyticsData = [
        ['分析指标', '数值'],
        ['总响应数', data.analytics.total_responses],
        ['完成率', `${data.analytics.completion_rate}%`],
        ['平均完成时间', `${Math.floor(data.analytics.average_completion_time / 60)}分钟`],
        ['平均得分', data.analytics.average_score || 'N/A']
      ];
      
      const analyticsWs = XLSX.utils.aoa_to_sheet(analyticsData);
      XLSX.utils.book_append_sheet(wb, analyticsWs, '分析统计');
    }
    
    setExportProgress(60);
    
    // Responses sheet
    if (exportOptions.includeResponses && data.responses) {
      const responseHeaders = [
        '响应ID', '候选人姓名', '邮箱', '职位', '提交时间', '完成时间(秒)', '总分'
      ];
      
      // Add question headers
      if (data.questionnaire.questions) {
        data.questionnaire.questions.forEach((q: any) => {
          responseHeaders.push(q.title);
        });
      }
      
      const responseData = [responseHeaders];
      
      data.responses.forEach((response: any) => {
        const row = [
          response.id,
          response.candidate_name || '',
          response.candidate_email || '',
          response.candidate_position || '',
          response.submitted_at ? format(new Date(response.submitted_at), 'yyyy-MM-dd HH:mm', { locale: zhCN }) : '',
          response.completion_time || '',
          response.total_score || ''
        ];
        
        // Add question responses
        if (data.questionnaire.questions) {
          data.questionnaire.questions.forEach((q: any) => {
            const answer = response.responses?.[q.id] || '';
            row.push(Array.isArray(answer) ? answer.join(', ') : String(answer));
          });
        }
        
        responseData.push(row);
      });
      
      const responseWs = XLSX.utils.aoa_to_sheet(responseData);
      XLSX.utils.book_append_sheet(wb, responseWs, '响应数据');
    }
    
    setExportProgress(70);
    
    // Evaluations sheet
    if (exportOptions.includeEvaluations && data.evaluations) {
      const evalHeaders = [
        '评估ID', '候选人', '总分', '是否合格', '匹配度', '风险等级', '主要优势', '改进建议'
      ];
      
      const evalData = [evalHeaders];
      
      data.evaluations.forEach((eval: any) => {
        evalData.push([
          eval.id,
          eval.candidate_name || '',
          eval.total_score || '',
          eval.is_qualified ? '合格' : '不合格',
          `${eval.match_score || 0}%`,
          eval.risk_level || '',
          eval.strengths?.join(', ') || '',
          eval.recommendations || ''
        ]);
      });
      
      const evalWs = XLSX.utils.aoa_to_sheet(evalData);
      XLSX.utils.book_append_sheet(wb, evalWs, '评估结果');
    }
    
    setExportProgress(80);
    
    // Dimension scores sheet
    if (exportOptions.includeDimensions && data.evaluations) {
      const dimHeaders = ['候选人'];
      const dimensions = new Set<string>();
      
      // Collect all dimensions
      data.evaluations.forEach((eval: any) => {
        if (eval.dimension_scores) {
          Object.keys(eval.dimension_scores).forEach(dim => dimensions.add(dim));
        }
      });
      
      dimensions.forEach(dim => dimHeaders.push(dim));
      
      const dimData = [dimHeaders];
      
      data.evaluations.forEach((eval: any) => {
        const row = [eval.candidate_name || ''];
        dimensions.forEach(dim => {
          row.push(eval.dimension_scores?.[dim] || 0);
        });
        dimData.push(row);
      });
      
      const dimWs = XLSX.utils.aoa_to_sheet(dimData);
      XLSX.utils.book_append_sheet(wb, dimWs, '维度得分');
    }
    
    setExportProgress(90);
    
    // Write file
    const wbout = XLSX.write(wb, { bookType: 'xlsx', type: 'binary' });
    const blob = new Blob([s2ab(wbout)], { type: 'application/octet-stream' });
    saveAs(blob, `${questionnaireTitle}_${format(new Date(), 'yyyyMMdd_HHmmss')}.xlsx`);
  };

  const exportToPDF = async (data: ExportData) => {
    setExportProgress(40);
    
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();
    let yPosition = 20;
    
    // Add custom font for Chinese support (you would need to add a Chinese font)
    // pdf.addFont('NotoSansCJK.ttf', 'NotoSansCJK', 'normal');
    // pdf.setFont('NotoSansCJK');
    
    // Title
    pdf.setFontSize(20);
    pdf.text(questionnaireTitle, pageWidth / 2, yPosition, { align: 'center' });
    yPosition += 15;
    
    // Date
    pdf.setFontSize(10);
    pdf.text(
      `Generated: ${format(new Date(), 'yyyy-MM-dd HH:mm', { locale: zhCN })}`,
      pageWidth / 2,
      yPosition,
      { align: 'center' }
    );
    yPosition += 15;
    
    setExportProgress(50);
    
    // Overview section
    if (data.questionnaire) {
      pdf.setFontSize(14);
      pdf.text('Questionnaire Overview', 15, yPosition);
      yPosition += 10;
      
      pdf.setFontSize(10);
      pdf.text(`Status: ${data.questionnaire.status}`, 15, yPosition);
      yPosition += 7;
      pdf.text(`Total Questions: ${data.questionnaire.questions?.length || 0}`, 15, yPosition);
      yPosition += 7;
      pdf.text(`Total Responses: ${data.responses?.length || 0}`, 15, yPosition);
      yPosition += 15;
    }
    
    setExportProgress(60);
    
    // Analytics section
    if (exportOptions.includeAnalytics && data.analytics) {
      if (yPosition > pageHeight - 50) {
        pdf.addPage();
        yPosition = 20;
      }
      
      pdf.setFontSize(14);
      pdf.text('Analytics Summary', 15, yPosition);
      yPosition += 10;
      
      pdf.setFontSize(10);
      pdf.text(`Completion Rate: ${data.analytics.completion_rate}%`, 15, yPosition);
      yPosition += 7;
      pdf.text(`Average Completion Time: ${Math.floor(data.analytics.average_completion_time / 60)} minutes`, 15, yPosition);
      yPosition += 7;
      pdf.text(`Average Score: ${data.analytics.average_score || 'N/A'}`, 15, yPosition);
      yPosition += 15;
    }
    
    setExportProgress(70);
    
    // Top candidates
    if (exportOptions.includeEvaluations && data.evaluations) {
      if (yPosition > pageHeight - 80) {
        pdf.addPage();
        yPosition = 20;
      }
      
      pdf.setFontSize(14);
      pdf.text('Top Candidates', 15, yPosition);
      yPosition += 10;
      
      const topCandidates = data.evaluations
        .sort((a: any, b: any) => (b.total_score || 0) - (a.total_score || 0))
        .slice(0, 5);
      
      pdf.setFontSize(10);
      topCandidates.forEach((candidate: any, index: number) => {
        pdf.text(
          `${index + 1}. ${candidate.candidate_name || 'Unknown'} - Score: ${candidate.total_score || 0}`,
          15,
          yPosition
        );
        yPosition += 7;
      });
    }
    
    setExportProgress(90);
    
    // Save PDF
    pdf.save(`${questionnaireTitle}_${format(new Date(), 'yyyyMMdd_HHmmss')}.pdf`);
  };

  const exportToCSV = async (data: ExportData) => {
    setExportProgress(40);
    
    if (!data.responses || data.responses.length === 0) {
      throw new Error('No response data to export');
    }
    
    // Prepare CSV headers
    const headers = [
      'Response ID',
      'Candidate Name',
      'Email',
      'Position',
      'Submission Time',
      'Completion Time (seconds)',
      'Total Score'
    ];
    
    // Add question headers
    if (data.questionnaire.questions) {
      data.questionnaire.questions.forEach((q: any) => {
        headers.push(q.title);
      });
    }
    
    setExportProgress(60);
    
    // Prepare CSV rows
    const rows = data.responses.map((response: any) => {
      const row = [
        response.id,
        response.candidate_name || '',
        response.candidate_email || '',
        response.candidate_position || '',
        response.submitted_at || '',
        response.completion_time || '',
        response.total_score || ''
      ];
      
      // Add question responses
      if (data.questionnaire.questions) {
        data.questionnaire.questions.forEach((q: any) => {
          const answer = response.responses?.[q.id] || '';
          row.push(Array.isArray(answer) ? answer.join('; ') : String(answer));
        });
      }
      
      return row;
    });
    
    setExportProgress(80);
    
    // Convert to CSV string
    const csvContent = [
      headers.join(','),
      ...rows.map(row => row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(','))
    ].join('\n');
    
    // Create and download file
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8' });
    saveAs(blob, `${questionnaireTitle}_${format(new Date(), 'yyyyMMdd_HHmmss')}.csv`);
    
    setExportProgress(90);
  };

  const exportToJSON = async (data: ExportData) => {
    setExportProgress(50);
    
    const jsonData = {
      questionnaire: data.questionnaire,
      exportDate: new Date().toISOString(),
      statistics: {
        totalResponses: data.responses?.length || 0,
        totalEvaluations: data.evaluations?.length || 0
      },
      responses: exportOptions.includeResponses ? data.responses : undefined,
      evaluations: exportOptions.includeEvaluations ? data.evaluations : undefined,
      analytics: exportOptions.includeAnalytics ? data.analytics : undefined
    };
    
    setExportProgress(80);
    
    const jsonString = JSON.stringify(jsonData, null, 2);
    const blob = new Blob([jsonString], { type: 'application/json' });
    saveAs(blob, `${questionnaireTitle}_${format(new Date(), 'yyyyMMdd_HHmmss')}.json`);
    
    setExportProgress(90);
  };

  // Helper function to convert string to ArrayBuffer
  const s2ab = (s: string): ArrayBuffer => {
    const buf = new ArrayBuffer(s.length);
    const view = new Uint8Array(buf);
    for (let i = 0; i < s.length; i++) {
      view[i] = s.charCodeAt(i) & 0xFF;
    }
    return buf;
  };

  return (
    <>
      <Button
        onClick={() => setShowExportDialog(true)}
        className="gap-2"
      >
        <Download className="h-4 w-4" />
        导出数据
      </Button>

      <Dialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>导出数据</DialogTitle>
            <DialogDescription>
              选择导出格式和包含的数据内容
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6 py-4">
            {/* Export Format */}
            <div className="space-y-3">
              <Label>导出格式</Label>
              <RadioGroup
                value={exportOptions.format}
                onValueChange={(value) => setExportOptions({
                  ...exportOptions,
                  format: value as ExportOptions['format']
                })}
              >
                <div className="grid grid-cols-2 gap-4">
                  <div className="flex items-center space-x-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <RadioGroupItem value="excel" id="excel" />
                    <Label htmlFor="excel" className="flex items-center gap-2 cursor-pointer">
                      <FileSpreadsheet className="h-4 w-4 text-green-600" />
                      Excel (.xlsx)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <RadioGroupItem value="pdf" id="pdf" />
                    <Label htmlFor="pdf" className="flex items-center gap-2 cursor-pointer">
                      <FileText className="h-4 w-4 text-red-600" />
                      PDF (.pdf)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <RadioGroupItem value="csv" id="csv" />
                    <Label htmlFor="csv" className="flex items-center gap-2 cursor-pointer">
                      <FileText className="h-4 w-4 text-blue-600" />
                      CSV (.csv)
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                    <RadioGroupItem value="json" id="json" />
                    <Label htmlFor="json" className="flex items-center gap-2 cursor-pointer">
                      <FilePlus className="h-4 w-4 text-purple-600" />
                      JSON (.json)
                    </Label>
                  </div>
                </div>
              </RadioGroup>
            </div>

            {/* Export Content */}
            <div className="space-y-3">
              <Label>包含内容</Label>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeAnalytics"
                    checked={exportOptions.includeAnalytics}
                    onCheckedChange={(checked) => setExportOptions({
                      ...exportOptions,
                      includeAnalytics: checked as boolean
                    })}
                  />
                  <Label htmlFor="includeAnalytics" className="flex items-center gap-2">
                    <BarChart3 className="h-4 w-4" />
                    分析统计
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeResponses"
                    checked={exportOptions.includeResponses}
                    onCheckedChange={(checked) => setExportOptions({
                      ...exportOptions,
                      includeResponses: checked as boolean
                    })}
                  />
                  <Label htmlFor="includeResponses" className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    响应数据
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeEvaluations"
                    checked={exportOptions.includeEvaluations}
                    onCheckedChange={(checked) => setExportOptions({
                      ...exportOptions,
                      includeEvaluations: checked as boolean
                    })}
                  />
                  <Label htmlFor="includeEvaluations" className="flex items-center gap-2">
                    <TrendingUp className="h-4 w-4" />
                    评估结果
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="includeDimensions"
                    checked={exportOptions.includeDimensions}
                    onCheckedChange={(checked) => setExportOptions({
                      ...exportOptions,
                      includeDimensions: checked as boolean
                    })}
                  />
                  <Label htmlFor="includeDimensions" className="flex items-center gap-2">
                    <PieChart className="h-4 w-4" />
                    维度得分
                  </Label>
                </div>
                {exportOptions.format === 'pdf' && (
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="includeCharts"
                      checked={exportOptions.includeCharts}
                      onCheckedChange={(checked) => setExportOptions({
                        ...exportOptions,
                        includeCharts: checked as boolean
                      })}
                    />
                    <Label htmlFor="includeCharts" className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      包含图表
                    </Label>
                  </div>
                )}
              </div>
            </div>

            {/* Group By Option */}
            <div className="space-y-3">
              <Label>数据分组</Label>
              <Select
                value={exportOptions.groupBy}
                onValueChange={(value) => setExportOptions({
                  ...exportOptions,
                  groupBy: value as ExportOptions['groupBy']
                })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="选择分组方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">不分组</SelectItem>
                  <SelectItem value="date">按日期分组</SelectItem>
                  <SelectItem value="position">按职位分组</SelectItem>
                  <SelectItem value="score">按分数段分组</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Export Progress */}
            {exportStatus !== 'idle' && (
              <div className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">
                    {exportStatus === 'preparing' && '准备数据...'}
                    {exportStatus === 'generating' && '生成文件...'}
                    {exportStatus === 'complete' && '导出完成！'}
                    {exportStatus === 'error' && '导出失败'}
                  </span>
                  <span className="font-medium">{exportProgress}%</span>
                </div>
                <Progress value={exportProgress} className="h-2" />
              </div>
            )}
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowExportDialog(false)}
              disabled={exporting}
            >
              取消
            </Button>
            <Button
              onClick={handleExport}
              disabled={exporting}
              className="gap-2"
            >
              {exporting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  导出中...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4" />
                  开始导出
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};