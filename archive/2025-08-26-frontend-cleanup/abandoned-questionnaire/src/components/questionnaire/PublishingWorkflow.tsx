'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import { format, addDays } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import {
  Send,
  Globe,
  Lock,
  Key,
  Calendar as CalendarIcon,
  Clock,
  Users,
  Copy,
  CheckCircle,
  AlertCircle,
  ExternalLink,
  QrCode,
  Mail,
  MessageSquare,
  Shield,
  Eye,
  Settings,
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { aiQuestionnaireService, PublishRequest } from '@/services/aiQuestionnaireService';
import QRCode from 'qrcode';

interface PublishingWorkflowProps {
  questionnaire: any;
  onPublish?: (data: PublishRequest) => void;
  onCancel?: () => void;
}

export function PublishingWorkflow({
  questionnaire,
  onPublish,
  onCancel,
}: PublishingWorkflowProps) {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(1);
  const [isPublishing, setIsPublishing] = useState(false);
  const [publishedUrl, setPublishedUrl] = useState('');
  const [qrCodeUrl, setQrCodeUrl] = useState('');
  const [copied, setCopied] = useState(false);

  // Form state
  const [publishData, setPublishData] = useState<PublishRequest>({
    slug: questionnaire.slug || '',
    valid_until: undefined,
    access_type: 'private',
    max_submissions: undefined,
    password: undefined,
  });

  const [settings, setSettings] = useState({
    enable_expiry: false,
    enable_submission_limit: false,
    enable_email_notification: true,
    enable_response_notification: false,
    enable_analytics_tracking: true,
    allow_anonymous: false,
  });

  const generateSlug = () => {
    const slug = questionnaire.title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);
    setPublishData(prev => ({ ...prev, slug }));
  };

  const handlePublish = async () => {
    if (!publishData.slug) {
      toast({
        title: '请输入URL标识',
        description: 'URL标识是访问问卷的唯一地址',
        variant: 'destructive',
      });
      return;
    }

    setIsPublishing(true);
    try {
      // Add expiry date if enabled
      const requestData = {
        ...publishData,
        valid_until: settings.enable_expiry ? publishData.valid_until : undefined,
        max_submissions: settings.enable_submission_limit
          ? publishData.max_submissions
          : undefined,
      };

      if (onPublish) {
        await onPublish(requestData);
      } else {
        // Call API directly
        const response = await aiQuestionnaireService.publishQuestionnaire(
          questionnaire.id,
          requestData
        );
        setPublishedUrl(response.public_url);
      }

      // Generate QR code
      const fullUrl = `${window.location.origin}/questionnaire/${publishData.slug}`;
      const qrDataUrl = await QRCode.toDataURL(fullUrl, {
        width: 200,
        margin: 1,
      });
      setQrCodeUrl(qrDataUrl);
      setPublishedUrl(fullUrl);

      toast({
        title: '发布成功',
        description: '问卷已成功发布，可以开始收集回复了',
      });

      setCurrentStep(3); // Go to success step
    } catch (error) {
      console.error('Publishing failed:', error);
      toast({
        title: '发布失败',
        description: '无法发布问卷，请检查设置后重试',
        variant: 'destructive',
      });
    } finally {
      setIsPublishing(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
      toast({
        title: '复制成功',
        description: '链接已复制到剪贴板',
      });
    } catch (error) {
      toast({
        title: '复制失败',
        description: '请手动复制链接',
        variant: 'destructive',
      });
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">基础设置</h3>
            </div>

            {/* URL Slug */}
            <div className="space-y-2">
              <Label htmlFor="slug">URL 标识</Label>
              <div className="flex gap-2">
                <Input
                  id="slug"
                  value={publishData.slug}
                  onChange={(e) =>
                    setPublishData(prev => ({ ...prev, slug: e.target.value }))
                  }
                  placeholder="my-questionnaire"
                  className="flex-1"
                />
                <Button variant="outline" onClick={generateSlug}>
                  自动生成
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                访问地址: {window.location.origin}/questionnaire/
                <span className="font-medium">{publishData.slug || 'xxx'}</span>
              </p>
            </div>

            {/* Access Type */}
            <div className="space-y-3">
              <Label>访问权限</Label>
              <RadioGroup
                value={publishData.access_type}
                onValueChange={(value: 'private' | 'public' | 'password') =>
                  setPublishData(prev => ({ ...prev, access_type: value }))
                }
              >
                <div className="flex items-start space-x-3">
                  <RadioGroupItem value="public" id="public" />
                  <div className="flex-1">
                    <Label htmlFor="public" className="flex items-center gap-2 cursor-pointer">
                      <Globe className="h-4 w-4" />
                      公开访问
                    </Label>
                    <p className="text-xs text-muted-foreground mt-1">
                      任何人都可以通过链接访问和填写问卷
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <RadioGroupItem value="password" id="password" />
                  <div className="flex-1">
                    <Label htmlFor="password" className="flex items-center gap-2 cursor-pointer">
                      <Key className="h-4 w-4" />
                      密码保护
                    </Label>
                    <p className="text-xs text-muted-foreground mt-1">
                      需要输入密码才能访问问卷
                    </p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <RadioGroupItem value="private" id="private" />
                  <div className="flex-1">
                    <Label htmlFor="private" className="flex items-center gap-2 cursor-pointer">
                      <Lock className="h-4 w-4" />
                      私密访问
                    </Label>
                    <p className="text-xs text-muted-foreground mt-1">
                      仅登录用户可以访问
                    </p>
                  </div>
                </div>
              </RadioGroup>
            </div>

            {/* Password Input (if password protected) */}
            {publishData.access_type === 'password' && (
              <div className="space-y-2">
                <Label htmlFor="password">访问密码</Label>
                <Input
                  id="password"
                  type="password"
                  value={publishData.password || ''}
                  onChange={(e) =>
                    setPublishData(prev => ({ ...prev, password: e.target.value }))
                  }
                  placeholder="输入访问密码"
                />
              </div>
            )}

            {/* Expiry Date */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="expiry">设置有效期</Label>
                <Switch
                  id="expiry"
                  checked={settings.enable_expiry}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, enable_expiry: checked }))
                  }
                />
              </div>
              {settings.enable_expiry && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        'w-full justify-start text-left font-normal',
                        !publishData.valid_until && 'text-muted-foreground'
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {publishData.valid_until
                        ? format(new Date(publishData.valid_until), 'yyyy年MM月dd日', {
                            locale: zhCN,
                          })
                        : '选择截止日期'}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={
                        publishData.valid_until
                          ? new Date(publishData.valid_until)
                          : undefined
                      }
                      onSelect={(date) =>
                        setPublishData(prev => ({
                          ...prev,
                          valid_until: date?.toISOString(),
                        }))
                      }
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              )}
            </div>

            {/* Submission Limit */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="limit">限制提交数量</Label>
                <Switch
                  id="limit"
                  checked={settings.enable_submission_limit}
                  onCheckedChange={(checked) =>
                    setSettings(prev => ({ ...prev, enable_submission_limit: checked }))
                  }
                />
              </div>
              {settings.enable_submission_limit && (
                <Input
                  type="number"
                  value={publishData.max_submissions || ''}
                  onChange={(e) =>
                    setPublishData(prev => ({
                      ...prev,
                      max_submissions: parseInt(e.target.value),
                    }))
                  }
                  placeholder="最大提交数量"
                  min={1}
                />
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold mb-4">高级设置</h3>
            </div>

            {/* Notification Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">通知设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="email-notify">邮件通知</Label>
                    <p className="text-xs text-muted-foreground">
                      收到新回复时发送邮件通知
                    </p>
                  </div>
                  <Switch
                    id="email-notify"
                    checked={settings.enable_email_notification}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({ ...prev, enable_email_notification: checked }))
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="response-notify">回复确认</Label>
                    <p className="text-xs text-muted-foreground">
                      向填写者发送确认邮件
                    </p>
                  </div>
                  <Switch
                    id="response-notify"
                    checked={settings.enable_response_notification}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({
                        ...prev,
                        enable_response_notification: checked,
                      }))
                    }
                  />
                </div>
              </CardContent>
            </Card>

            {/* Privacy Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="text-base">隐私设置</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="anonymous">匿名提交</Label>
                    <p className="text-xs text-muted-foreground">
                      允许匿名填写问卷
                    </p>
                  </div>
                  <Switch
                    id="anonymous"
                    checked={settings.allow_anonymous}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({ ...prev, allow_anonymous: checked }))
                    }
                  />
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label htmlFor="analytics">数据分析</Label>
                    <p className="text-xs text-muted-foreground">
                      收集访问和填写统计数据
                    </p>
                  </div>
                  <Switch
                    id="analytics"
                    checked={settings.enable_analytics_tracking}
                    onCheckedChange={(checked) =>
                      setSettings(prev => ({
                        ...prev,
                        enable_analytics_tracking: checked,
                      }))
                    }
                  />
                </div>
              </CardContent>
            </Card>

            {/* Preview Card */}
            <Card className="bg-muted/50">
              <CardHeader>
                <CardTitle className="text-base">发布预览</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">访问方式:</span>
                  <Badge variant="outline">
                    {publishData.access_type === 'public'
                      ? '公开'
                      : publishData.access_type === 'password'
                      ? '密码保护'
                      : '私密'}
                  </Badge>
                </div>
                {settings.enable_expiry && publishData.valid_until && (
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">有效期至:</span>
                    <span>
                      {format(new Date(publishData.valid_until), 'yyyy-MM-dd', {
                        locale: zhCN,
                      })}
                    </span>
                  </div>
                )}
                {settings.enable_submission_limit && publishData.max_submissions && (
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">最大提交:</span>
                    <span>{publishData.max_submissions} 份</span>
                  </div>
                )}
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">通知设置:</span>
                  <div className="flex gap-2">
                    {settings.enable_email_notification && (
                      <Mail className="h-4 w-4 text-muted-foreground" />
                    )}
                    {settings.enable_response_notification && (
                      <MessageSquare className="h-4 w-4 text-muted-foreground" />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">发布成功！</h3>
              <p className="text-muted-foreground">
                您的问卷已成功发布，可以开始收集回复了
              </p>
            </div>

            {/* Share Options */}
            <Tabs defaultValue="link" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="link">链接</TabsTrigger>
                <TabsTrigger value="qrcode">二维码</TabsTrigger>
                <TabsTrigger value="embed">嵌入</TabsTrigger>
              </TabsList>

              <TabsContent value="link" className="space-y-4">
                <div className="space-y-2">
                  <Label>问卷链接</Label>
                  <div className="flex gap-2">
                    <Input value={publishedUrl} readOnly className="flex-1" />
                    <Button
                      variant="outline"
                      onClick={() => copyToClipboard(publishedUrl)}
                    >
                      {copied ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </Button>
                    <Button variant="outline" asChild>
                      <a href={publishedUrl} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertTitle>分享提示</AlertTitle>
                  <AlertDescription>
                    您可以通过邮件、社交媒体或即时通讯工具分享此链接
                  </AlertDescription>
                </Alert>
              </TabsContent>

              <TabsContent value="qrcode" className="space-y-4">
                <div className="text-center">
                  {qrCodeUrl && (
                    <img src={qrCodeUrl} alt="QR Code" className="mx-auto mb-4" />
                  )}
                  <p className="text-sm text-muted-foreground mb-4">
                    扫描二维码即可访问问卷
                  </p>
                  <Button variant="outline" asChild>
                    <a href={qrCodeUrl} download="questionnaire-qrcode.png">
                      下载二维码
                    </a>
                  </Button>
                </div>
              </TabsContent>

              <TabsContent value="embed" className="space-y-4">
                <div className="space-y-2">
                  <Label>嵌入代码</Label>
                  <Textarea
                    value={`<iframe src="${publishedUrl}" width="100%" height="600" frameborder="0"></iframe>`}
                    readOnly
                    rows={3}
                  />
                  <Button
                    variant="outline"
                    onClick={() =>
                      copyToClipboard(
                        `<iframe src="${publishedUrl}" width="100%" height="600" frameborder="0"></iframe>`
                      )
                    }
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    复制代码
                  </Button>
                </div>
              </TabsContent>
            </Tabs>

            {/* Access Info */}
            {publishData.access_type === 'password' && publishData.password && (
              <Alert>
                <Key className="h-4 w-4" />
                <AlertTitle>访问密码</AlertTitle>
                <AlertDescription>
                  访问密码: <code className="font-mono">{publishData.password}</code>
                </AlertDescription>
              </Alert>
            )}
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>发布问卷</CardTitle>
            <CardDescription className="mt-2">
              配置访问权限并发布您的问卷
            </CardDescription>
          </div>
          <Badge variant="outline" className="gap-1">
            <Send className="h-3 w-3" />
            {questionnaire.status === 'published' ? '已发布' : '待发布'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent>
        {/* Progress Indicator */}
        <div className="flex items-center justify-between mb-8">
          {[1, 2, 3].map((step) => (
            <div
              key={step}
              className={`flex items-center ${step < 3 ? 'flex-1' : ''}`}
            >
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  step <= currentStep
                    ? 'bg-primary text-primary-foreground border-primary'
                    : 'bg-background text-muted-foreground border-muted'
                }`}
              >
                {step < currentStep ? (
                  <CheckCircle className="h-4 w-4" />
                ) : (
                  <span className="text-sm">{step}</span>
                )}
              </div>
              {step < 3 && (
                <div
                  className={`flex-1 h-0.5 mx-2 ${
                    step < currentStep ? 'bg-primary' : 'bg-muted'
                  }`}
                />
              )}
            </div>
          ))}
        </div>

        {/* Step Content */}
        <div className="min-h-[400px]">{renderStepContent()}</div>

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={() => {
              if (currentStep > 1 && currentStep < 3) {
                setCurrentStep(currentStep - 1);
              } else if (onCancel) {
                onCancel();
              }
            }}
            disabled={isPublishing}
          >
            {currentStep === 1 ? '取消' : currentStep === 3 ? '关闭' : '上一步'}
          </Button>

          {currentStep < 2 ? (
            <Button onClick={() => setCurrentStep(currentStep + 1)}>
              下一步
            </Button>
          ) : currentStep === 2 ? (
            <Button onClick={handlePublish} disabled={isPublishing}>
              {isPublishing ? (
                <>
                  <Clock className="h-4 w-4 mr-2 animate-spin" />
                  发布中...
                </>
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  确认发布
                </>
              )}
            </Button>
          ) : (
            <Button onClick={() => onCancel?.()}>完成</Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}