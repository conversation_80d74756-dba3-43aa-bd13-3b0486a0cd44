import { apiClient } from '@/lib/api-client';

export interface CandidateInfo {
  name: string;
  email: string;
  phone?: string;
  position: string;
  additional_info?: Record<string, any>;
}

export interface QuestionResponse {
  question_id: string;
  value: any;
  answered_at: string;
  time_spent?: number; // seconds
}

export interface QuestionnaireSubmission {
  questionnaire_id: string;
  session_id: string;
  candidate_info: CandidateInfo;
  responses: Record<string, any>;
  submitted_at: string;
  completion_time: number; // seconds
  ip_address?: string;
  user_agent?: string;
}

export interface SaveProgressRequest {
  questionnaire_id: string;
  session_id: string;
  responses: Record<string, any>;
  current_page?: number;
  last_saved_at: string;
}

export interface ValidationResult {
  is_valid: boolean;
  errors: Record<string, string>;
  warnings?: Record<string, string>;
}

export interface SubmissionResult {
  success: boolean;
  submission_id?: string;
  evaluation_available?: boolean;
  evaluation_id?: string;
  message?: string;
  errors?: string[];
}

class ResponseCollectionService {
  private readonly baseUrl = '/api/v1/questionnaire-responses';

  /**
   * Submit a complete questionnaire response
   */
  async submitResponse(submission: QuestionnaireSubmission): Promise<SubmissionResult> {
    try {
      const response = await apiClient.post<SubmissionResult>(
        `${this.baseUrl}/submit`,
        submission
      );
      return response;
    } catch (error) {
      console.error('Failed to submit questionnaire response:', error);
      throw error;
    }
  }

  /**
   * Save progress for later continuation
   */
  async saveProgress(request: SaveProgressRequest): Promise<{ success: boolean; message: string }> {
    try {
      const response = await apiClient.post<{ success: boolean; message: string }>(
        `${this.baseUrl}/save-progress`,
        request
      );
      return response;
    } catch (error) {
      console.error('Failed to save progress:', error);
      throw error;
    }
  }

  /**
   * Load saved progress for a session
   */
  async loadProgress(sessionId: string, questionnaireId: string): Promise<SaveProgressRequest | null> {
    try {
      const response = await apiClient.get<SaveProgressRequest>(
        `${this.baseUrl}/progress/${sessionId}?questionnaire_id=${questionnaireId}`
      );
      return response;
    } catch (error) {
      console.error('Failed to load progress:', error);
      return null;
    }
  }

  /**
   * Validate responses before submission
   */
  async validateResponses(
    questionnaireId: string,
    responses: Record<string, any>
  ): Promise<ValidationResult> {
    try {
      const response = await apiClient.post<ValidationResult>(
        `${this.baseUrl}/validate`,
        {
          questionnaire_id: questionnaireId,
          responses,
        }
      );
      return response;
    } catch (error) {
      console.error('Failed to validate responses:', error);
      throw error;
    }
  }

  /**
   * Get public questionnaire data
   */
  async getPublicQuestionnaire(slug: string): Promise<any> {
    try {
      const response = await apiClient.get<any>(
        `/api/v1/public/questionnaires/${slug}`
      );
      return response;
    } catch (error) {
      console.error('Failed to get public questionnaire:', error);
      return null;
    }
  }

  /**
   * Verify password for protected questionnaire
   */
  async verifyPassword(slug: string, password: string): Promise<boolean> {
    try {
      const response = await apiClient.post<{ valid: boolean }>(
        `/api/v1/public/questionnaires/${slug}/verify-password`,
        { password }
      );
      return response.valid;
    } catch (error) {
      console.error('Failed to verify password:', error);
      return false;
    }
  }

  /**
   * Check if email has already submitted
   */
  async checkDuplicateSubmission(
    questionnaireId: string,
    email: string
  ): Promise<{ exists: boolean; submitted_at?: string }> {
    try {
      const response = await apiClient.get<{ exists: boolean; submitted_at?: string }>(
        `${this.baseUrl}/check-duplicate?questionnaire_id=${questionnaireId}&email=${email}`
      );
      return response;
    } catch (error) {
      console.error('Failed to check duplicate submission:', error);
      throw error;
    }
  }

  /**
   * Calculate response statistics
   */
  calculateResponseStats(responses: Record<string, any>, questions: any[]): {
    totalQuestions: number;
    answeredQuestions: number;
    completionRate: number;
    requiredAnswered: number;
    optionalAnswered: number;
  } {
    const totalQuestions = questions.length;
    const answeredQuestions = Object.keys(responses).filter(key => 
      responses[key] !== null && responses[key] !== undefined && responses[key] !== ''
    ).length;
    
    const requiredQuestions = questions.filter(q => q.required);
    const requiredAnswered = requiredQuestions.filter(q => 
      responses[q.id] !== null && responses[q.id] !== undefined && responses[q.id] !== ''
    ).length;
    
    const optionalQuestions = questions.filter(q => !q.required);
    const optionalAnswered = optionalQuestions.filter(q => 
      responses[q.id] !== null && responses[q.id] !== undefined && responses[q.id] !== ''
    ).length;

    return {
      totalQuestions,
      answeredQuestions,
      completionRate: Math.round((answeredQuestions / totalQuestions) * 100),
      requiredAnswered,
      optionalAnswered,
    };
  }

  /**
   * Format response for display
   */
  formatResponseValue(value: any, questionType: string): string {
    if (value === null || value === undefined) {
      return '未回答';
    }

    switch (questionType) {
      case 'single_choice':
      case 'text_input':
      case 'text_area':
        return String(value);
      
      case 'multiple_choice':
        if (Array.isArray(value)) {
          return value.join(', ');
        }
        return String(value);
      
      case 'rating_scale':
      case 'slider':
        return `${value} 分`;
      
      case 'likert_scale':
        const likertLabels: Record<string, string> = {
          '1': '非常不同意',
          '2': '不同意',
          '3': '中立',
          '4': '同意',
          '5': '非常同意',
        };
        return likertLabels[String(value)] || String(value);
      
      case 'yes_no':
        return value ? '是' : '否';
      
      case 'date':
        return new Date(value).toLocaleDateString('zh-CN');
      
      case 'time':
        return value;
      
      case 'matrix':
        if (typeof value === 'object') {
          return Object.entries(value)
            .map(([row, col]) => `${row}: ${col}`)
            .join('; ');
        }
        return String(value);
      
      case 'ranking':
        if (Array.isArray(value)) {
          return value.map((item, index) => `${index + 1}. ${item}`).join(', ');
        }
        return String(value);
      
      default:
        return String(value);
    }
  }

  /**
   * Validate single response
   */
  validateResponse(
    question: any,
    value: any
  ): { valid: boolean; error?: string } {
    // Check required
    if (question.required && (value === null || value === undefined || value === '')) {
      return { valid: false, error: '此题为必答题' };
    }

    // Type-specific validation
    switch (question.type) {
      case 'text_input':
        if (question.validation?.minLength && value.length < question.validation.minLength) {
          return { valid: false, error: `最少需要${question.validation.minLength}个字符` };
        }
        if (question.validation?.maxLength && value.length > question.validation.maxLength) {
          return { valid: false, error: `最多允许${question.validation.maxLength}个字符` };
        }
        if (question.validation?.pattern) {
          const regex = new RegExp(question.validation.pattern);
          if (!regex.test(value)) {
            return { valid: false, error: question.validation.errorMessage || '格式不正确' };
          }
        }
        break;

      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(value)) {
          return { valid: false, error: '请输入有效的邮箱地址' };
        }
        break;

      case 'number':
        const numValue = Number(value);
        if (isNaN(numValue)) {
          return { valid: false, error: '请输入有效的数字' };
        }
        if (question.validation?.min !== undefined && numValue < question.validation.min) {
          return { valid: false, error: `最小值为${question.validation.min}` };
        }
        if (question.validation?.max !== undefined && numValue > question.validation.max) {
          return { valid: false, error: `最大值为${question.validation.max}` };
        }
        break;

      case 'multiple_choice':
        if (question.validation?.minSelections && value.length < question.validation.minSelections) {
          return { valid: false, error: `至少选择${question.validation.minSelections}项` };
        }
        if (question.validation?.maxSelections && value.length > question.validation.maxSelections) {
          return { valid: false, error: `最多选择${question.validation.maxSelections}项` };
        }
        break;
    }

    return { valid: true };
  }

  /**
   * Track response time
   */
  private responseTimers: Map<string, number> = new Map();

  startTimer(questionId: string): void {
    this.responseTimers.set(questionId, Date.now());
  }

  getElapsedTime(questionId: string): number {
    const startTime = this.responseTimers.get(questionId);
    if (!startTime) return 0;
    return Math.floor((Date.now() - startTime) / 1000);
  }

  clearTimer(questionId: string): void {
    this.responseTimers.delete(questionId);
  }

  clearAllTimers(): void {
    this.responseTimers.clear();
  }
}

export const responseCollectionService = new ResponseCollectionService();