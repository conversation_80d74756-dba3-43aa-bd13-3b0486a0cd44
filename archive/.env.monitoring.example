# Monitoring System Configuration
# Copy this file to .env.monitoring and adjust values as needed

# Refresh intervals (in seconds)
MONITORING_METRICS_CACHE_TTL=30              # System metrics cache TTL (default: 30s, aligned with frontend)
MONITORING_HEALTH_CHECK_INTERVAL=120         # Health check interval for services (default: 120s)
MONITORING_CONTAINER_STATS_CACHE_TTL=10      # Container stats cache TTL (default: 10s)

# Frontend refresh interval (for reference)
MONITORING_FRONTEND_REFRESH_INTERVAL=30      # Frontend auto-refresh interval (default: 30s)

# Data retention (in days)
MONITORING_HEALTH_CHECK_RETENTION=7          # Keep health check records (default: 7 days)
MONITORING_METRICS_RETENTION=7               # Keep raw metrics (default: 7 days)
MONITORING_HOURLY_AGGREGATES_RETENTION=30    # Keep hourly aggregates (default: 30 days)
MONITORING_DAILY_AGGREGATES_RETENTION=365    # Keep daily aggregates (default: 365 days)

# Performance settings
MONITORING_MAX_CONCURRENT_HEALTH_CHECKS=10   # Maximum concurrent health checks (default: 10)
MONITORING_HEALTH_CHECK_TIMEOUT=10           # Health check timeout in seconds (default: 10s)
MONITORING_DOCKER_API_TIMEOUT=5              # Docker API timeout in seconds (default: 5s)

# Alert thresholds (percentages)
MONITORING_CPU_ALERT_THRESHOLD=80.0          # CPU usage alert threshold (default: 80%)
MONITORING_MEMORY_ALERT_THRESHOLD=85.0       # Memory usage alert threshold (default: 85%)
MONITORING_DISK_ALERT_THRESHOLD=90.0         # Disk usage alert threshold (default: 90%)