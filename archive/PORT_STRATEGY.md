# TalentForge Pro Port Strategy

## Overview

This document describes the port exposure strategy for TalentForge Pro, designed to minimize attack surface while maintaining development convenience.

## Production Strategy

In production, **only one port is exposed**: the nginx proxy port (typically 80/443). All services are accessed through reverse proxy paths:

- Frontend: `/`
- Backend API: `/api/`
- MinIO Files: `/files/`
- Documentation: `/docs`, `/redoc`

## Development Strategy

For development, we use a **unified proxy approach** with limited direct access to infrastructure services:

### Primary Access Point

- **Port 8088**: Unified nginx proxy (all application services)
  - Frontend: `http://localhost:8088/`
  - Backend API: `http://localhost:8088/api/`
  - API Docs: `http://localhost:8088/docs`
  - MinIO Files: `http://localhost:8088/files/`
  - MinIO Console: `http://localhost:8088/minio/`

### Infrastructure Admin Ports

For database administration and monitoring, infrastructure services are available on non-standard ports (standard port with '1' prefix):

| Service | Standard Port | Development Port | Purpose |
|---------|--------------|------------------|---------|
| PostgreSQL | 5432 | **15432** | Database administration |
| Redis | 6379 | **16379** | Cache inspection |
| ChromaDB | 8000 | **18000** | Vector DB management |
| Prometheus | 9090 | **19090** | Metrics monitoring |
| MinIO | 9000/9001 | **None** | Access via proxy only |

### Security Benefits

1. **Single Entry Point**: All application traffic goes through nginx on port 8088
2. **Non-Standard Ports**: Infrastructure services use non-standard ports, reducing automated attack attempts
3. **No Direct MinIO Access**: Object storage only accessible through authenticated proxy
4. **Production-Like**: Development setup mirrors production architecture

## Port Configuration

### docker-compose.yml
```yaml
postgres:
  ports:
    - "${POSTGRES_EXTERNAL_PORT:-15432}:5432"

redis:
  ports:
    - "${REDIS_EXTERNAL_PORT:-16379}:6379"

chromadb:
  ports:
    - "${CHROMA_EXTERNAL_PORT:-18000}:8000"

prometheus:
  ports:
    - "${PROMETHEUS_EXTERNAL_PORT:-19090}:9090"

minio:
  # No ports exposed - access via nginx proxy
```

### docker-compose.override.yml
```yaml
nginx:
  ports:
    - "${NGINX_UNIFIED_PORT:-8088}:88"  # Single entry point
```

## Usage Examples

### Application Access
```bash
# Frontend
curl http://localhost:8088/

# API
curl http://localhost:8088/api/v1/health

# MinIO file upload
curl -X PUT http://localhost:8088/files/bucket/file.pdf \
  -H "Authorization: Bearer <token>" \
  --data-binary @file.pdf
```

### Infrastructure Admin
```bash
# PostgreSQL admin
psql -h localhost -p 15432 -U postgres -d hephaestus_forge_db

# Redis CLI
redis-cli -h localhost -p 16379 -a Pass1234

# ChromaDB API
curl http://localhost:18000/api/v1/collections

# Prometheus metrics
curl http://localhost:19090/metrics
```

## Environment Variables

You can customize ports via environment variables or `.env` file:

```env
# Infrastructure ports (development only)
POSTGRES_EXTERNAL_PORT=15432
REDIS_EXTERNAL_PORT=16379
CHROMA_EXTERNAL_PORT=18000
PROMETHEUS_EXTERNAL_PORT=19090

# Main proxy port
NGINX_UNIFIED_PORT=8088
```

## Migration Notes

If upgrading from the old multi-port setup:

1. Update your database connection strings to use port 15432
2. Update Redis clients to use port 16379
3. Access MinIO only through `http://localhost:8088/files/`
4. Remove any references to ports 3000, 3001, 8001, 9000, 9001

## Troubleshooting

### Service Discovery
```bash
# Check all exposed ports
docker-compose ps

# Verify nginx proxy routes
curl http://localhost:8088/dev-info
```

### Common Issues

1. **Port Conflicts**: If infrastructure ports conflict, adjust via environment variables
2. **MinIO Access**: Remember MinIO is only accessible via proxy paths
3. **Database Tools**: Update connection strings in database GUI tools

## Future Considerations

- In production, only port 80/443 should be exposed
- Consider using Docker networks for complete isolation
- Implement mutual TLS for infrastructure services