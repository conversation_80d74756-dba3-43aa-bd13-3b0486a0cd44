FROM python:3.12-slim

# 设置工作目录
WORKDIR /app

# 使用国内镜像源（如果在中国）
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources || \
    sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list || true

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    curl \
    libmagic1 \
    libmagic-dev \
    # PostgreSQL开发库（psycopg2需要）
    libpq-dev \
    # 调试工具
    vim \
    less \
    net-tools \
    iputils-ping \
    telnet \
    htop \
    && rm -rf /var/lib/apt/lists/*

# 设置pip镜像源（使用阿里云）
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

# 安装Poetry
RUN pip install poetry==1.8.3

# 复制依赖文件（关键：包含poetry.lock）
COPY pyproject.toml poetry.lock ./

# 配置Poetry：不创建虚拟环境
RUN poetry config virtualenvs.create false

# 配置Poetry使用国内镜像
RUN poetry config repositories.aliyun https://mirrors.aliyun.com/pypi/simple/

# 安装依赖（使用已存在的lock文件，不重新生成）
# 暂时包含dev依赖以便调试
RUN poetry install --no-interaction --no-ansi --no-root

# 安装额外的Python调试工具
RUN pip install --no-cache-dir \
    ipdb \
    pdbpp \
    rich \
    httpx \
    pytest-asyncio

# 创建非root用户
RUN useradd -m appuser

# 复制应用代码
COPY . .

# 修改文件所有权
RUN chown -R appuser:appuser /app

USER appuser

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]