# TalentForge Pro - 测试环境配置
# 用于 docker-compose.test.yml

# 数据库配置
POSTGRES_SERVER=postgres
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=Pass1234
POSTGRES_DB=hephaestus_forge_db

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=Pass1234
REDIS_DB=0

# 测试管理员账号
TEST_ADMIN_EMAIL=<EMAIL>
TEST_ADMIN_PASSWORD=test123

# API配置
API_BASE_URL=http://nginx:88/api/v1
API_INTERNAL_URL=http://backend:8000/api/v1

# 测试配置
TEST_ENV=docker
TEST_TIMEOUT=30
TEST_RETRY_COUNT=3

# 报告配置
REPORT_DIR=/app/test_reports
COVERAGE_DIR=/app/coverage
COVERAGE_THRESHOLD=80

# 测试类型开关
RUN_UNIT_TESTS=true
RUN_INTEGRATION_TESTS=true
RUN_REGRESSION_TESTS=true
RUN_PERFORMANCE_TESTS=false
RUN_SMOKE_TESTS=true

# 性能测试配置
LOCUST_USERS=10
LOCUST_SPAWN_RATE=1
LOCUST_RUN_TIME=60s

# 通知配置（可选）
SLACK_WEBHOOK_URL=
EMAIL_NOTIFICATIONS=false
NOTIFICATION_EMAIL=