"""
LLM Service with Unified AI Service Manager
使用统一的AIServiceManager管理LLM客户端

Author: TalentForge Pro Team  
Date: 2025-08-11
"""
import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from app.core.ai_config import ai_settings
from app.services.ai_service_manager import ai_service_manager, AIServiceType

logger = logging.getLogger(__name__)


class RateLimitExceeded(Exception):
    """Raised when rate limit is exceeded"""
    pass


class RateLimiter:
    """Simple rate limiter for API calls"""
    
    def __init__(self, max_requests: int = 10, per_minutes: int = 1):
        self.max_requests = max_requests
        self.per_minutes = per_minutes
        self.requests: List[datetime] = []
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """Acquire permission to make a request"""
        async with self.lock:
            now = datetime.now()
            cutoff = now - timedelta(minutes=self.per_minutes)
            
            # Remove old requests
            self.requests = [req_time for req_time in self.requests if req_time > cutoff]
            
            # Check if we can make a request
            if len(self.requests) >= self.max_requests:
                wait_time = (self.requests[0] + timedelta(minutes=self.per_minutes) - now).total_seconds()
                if wait_time > 0:
                    logger.warning(f"Rate limit reached. Waiting {wait_time:.1f} seconds")
                    await asyncio.sleep(wait_time)
                    return await self.acquire()  # Retry after waiting
            
            # Record this request
            self.requests.append(now)


class LLMService:
    """Service for interacting with LLM providers through unified AI Service Manager"""
    
    def __init__(self, provider: Optional[str] = None):
        """初始化LLM服务，使用统一的AIServiceManager"""
        # 使用AI服务管理器，不再需要初始化各种客户端！
        self.ai_manager = ai_service_manager
        self.provider = provider  # 可选的provider覆盖
        
        # Rate limiter still useful for controlling request rate
        self.rate_limiter = RateLimiter(
            max_requests=ai_settings.DEEPSEEK_RATE_LIMIT,
            per_minutes=1
        )
        self._request_count = 0
        self._token_count = 0
        
        logger.info(f"LLMService initialized with AIServiceManager, provider override: {provider}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(Exception)
    )
    async def complete(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[str] = "json"
    ) -> Dict[str, Any]:
        """
        Send a completion request using unified AI Service Manager
        
        Args:
            prompt: The user prompt
            system_prompt: Optional system prompt
            temperature: Override default temperature
            max_tokens: Override default max tokens
            response_format: Expected response format (json or text)
        
        Returns:
            Parsed response from the API
        """
        # Acquire rate limit permission
        await self.rate_limiter.acquire()
        
        try:
            # 获取LLM客户端 - 使用统一的管理器！
            client, config = self.ai_manager.get_llm_client(self.provider)
            
            # Build messages
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            # 根据provider类型调用相应的API
            actual_provider = self.provider or ai_settings.LLM_PROVIDER
            
            if actual_provider.lower() == "ollama":
                # Ollama使用不同的API格式
                import ollama
                response = await asyncio.to_thread(
                    ollama.chat,
                    model=config["llm_model"],
                    messages=messages,
                    options={
                        "temperature": temperature or config.get("temperature", 0.7),
                        "max_tokens": max_tokens or config.get("max_tokens", 1000)
                    }
                )
                content = response['message']['content']
            else:
                # OpenAI兼容的API (DeepSeek, Moonshot, OpenRouter等)
                request_data = {
                    "model": config["llm_model"],
                    "messages": messages,
                    "temperature": temperature or config.get("temperature", 0.7),
                    "max_tokens": max_tokens or config.get("max_tokens", 1000),
                }
                
                # Add response format hint if JSON expected
                if response_format == "json" and actual_provider.lower() in ["deepseek", "openrouter"]:
                    request_data["response_format"] = {"type": "json_object"}
                
                logger.info(f"Sending request via AIServiceManager to {actual_provider} (model: {config['llm_model']})")
                response = await client.chat.completions.create(**request_data)
                content = response.choices[0].message.content
            
            # Parse JSON if expected
            if response_format == "json":
                try:
                    return json.loads(content)
                except json.JSONDecodeError:
                    logger.warning("Failed to parse JSON response, extracting JSON from text")
                    return self._extract_json_from_text(content)
            
            return {"content": content}
            
        except Exception as e:
            logger.error(f"Failed to complete with {self.provider or ai_settings.LLM_PROVIDER}: {e}")
            
            # 尝试降级到其他provider
            fallback_provider = await self.ai_manager.get_fallback_provider(
                AIServiceType.LLM,
                exclude_providers=[self.provider] if self.provider else []
            )
            
            if fallback_provider:
                logger.info(f"Retrying with fallback provider: {fallback_provider}")
                self.provider = fallback_provider
                return await self.complete(prompt, system_prompt, temperature, max_tokens, response_format)
            
            raise
    
    def _extract_json_from_text(self, text: str) -> Dict[str, Any]:
        """Extract JSON from text that may contain other content"""
        import re
        
        # Try to find JSON in the text
        json_patterns = [
            r'\{[\s\S]*\}',  # Match outermost braces
            r'\[[\s\S]*\]',  # Match outermost brackets
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                try:
                    return json.loads(match)
                except json.JSONDecodeError:
                    continue
        
        # If no valid JSON found, return the text as content
        logger.warning("No valid JSON found in response")
        return {"content": text}
    
    async def generate_questionnaire(
        self,
        position_type: str,
        dimensions: List[str],
        question_count: int = 20,
        industry: str = "烟草"
    ) -> Dict[str, Any]:
        """
        Generate a questionnaire using DeepSeek
        
        Args:
            position_type: Type of position
            dimensions: Evaluation dimensions
            question_count: Number of questions to generate
            industry: Industry context
        
        Returns:
            Generated questionnaire data
        """
        system_prompt = """你是一个专业的人才评估专家，精通问卷设计和人才测评。
        请根据要求生成结构化的JSON格式问卷数据。
        每个问题都要贴近实际工作场景，选项要有区分度。"""
        
        user_prompt = f"""请为{industry}行业的{position_type}岗位生成一份人才评估问卷。

要求：
1. 评估维度：{', '.join(dimensions)}
2. 题目数量：{question_count}道
3. 每道题包含4个选项，分值分别为5、3、1、0分
4. 题目要结合{industry}行业实际工作场景
5. 返回严格的JSON格式

返回格式：
{{
    "title": "问卷标题",
    "description": "问卷说明",
    "questions": [
        {{
            "id": 1,
            "title": "问题文本",
            "type": "single_choice",
            "dimension": "所属维度",
            "required": true,
            "options": [
                {{"label": "A", "text": "选项内容", "score": 5}},
                {{"label": "B", "text": "选项内容", "score": 3}},
                {{"label": "C", "text": "选项内容", "score": 1}},
                {{"label": "D", "text": "选项内容", "score": 0}}
            ]
        }}
    ],
    "evaluation_criteria": {{
        "excellent": {{"min": 80, "max": 100, "description": "优秀"}},
        "good": {{"min": 60, "max": 79, "description": "良好"}},
        "pass": {{"min": 40, "max": 59, "description": "及格"}},
        "fail": {{"min": 0, "max": 39, "description": "不及格"}}
    }},
    "dimensions_weight": {{
        "维度名称": 权重百分比
    }}
}}"""
        
        try:
            result = await self.complete(
                prompt=user_prompt,
                system_prompt=system_prompt,
                response_format="json"
            )
            
            # Validate the structure
            if "questions" not in result:
                raise ValueError("Invalid questionnaire structure: missing questions")
            
            logger.info(f"Successfully generated questionnaire with {len(result.get('questions', []))} questions")
            return result
            
        except Exception as e:
            logger.error(f"Failed to generate questionnaire: {str(e)}")
            raise
    
    async def evaluate_responses(
        self,
        questionnaire_title: str,
        position_type: str,
        dimension_scores: Dict[str, float],
        answers_summary: str
    ) -> Dict[str, Any]:
        """
        Evaluate candidate responses using DeepSeek
        
        Args:
            questionnaire_title: Title of the questionnaire
            position_type: Position being evaluated for
            dimension_scores: Scores for each dimension
            answers_summary: Summary of candidate's answers
        
        Returns:
            Evaluation report
        """
        system_prompt = """你是一个专业的人才评估专家。
        请根据候选人的测评结果，提供专业、客观的评价和建议。
        评价要具体、有建设性，避免空泛的描述。"""
        
        user_prompt = f"""请对以下候选人的{position_type}岗位测评结果进行专业评价：

问卷：{questionnaire_title}
各维度得分：
{json.dumps(dimension_scores, ensure_ascii=False, indent=2)}

候选人答题情况摘要：
{answers_summary}

请提供以下内容（JSON格式）：
{{
    "overall_evaluation": "总体评价（200-300字）",
    "strengths": ["优势1", "优势2", "优势3"],
    "weaknesses": ["待改进点1", "待改进点2"],
    "recommendations": "改进建议（150-200字）",
    "is_qualified": true/false,
    "match_score": 0-100,
    "risk_level": "low/medium/high",
    "key_insights": "关键洞察（100字）"
}}"""
        
        try:
            result = await self.complete(
                prompt=user_prompt,
                system_prompt=system_prompt,
                response_format="json"
            )
            
            logger.info("Successfully generated evaluation report")
            return result
            
        except Exception as e:
            logger.error(f"Failed to generate evaluation: {str(e)}")
            raise
    
    async def evaluate_text_response(
        self,
        question: str,
        response: str,
        criteria: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Evaluate a text response using AI
        
        Args:
            question: The question asked
            response: The candidate's response
            criteria: Evaluation criteria
        
        Returns:
            Evaluation result with score and feedback
        """
        system_prompt = """你是一个专业的答案评估专家。
        请根据问题和回答，给出客观的评分和反馈。
        评分范围0-100分，要有明确的评分理由。"""
        
        user_prompt = f"""请评估以下回答：
问题：{question}
回答：{response}

评估标准：
{json.dumps(criteria or {"完整性": 30, "准确性": 40, "深度": 30}, ensure_ascii=False)}

请返回JSON格式：
{{
    "score": 0-100,
    "feedback": "具体反馈",
    "strengths": ["优点1", "优点2"],
    "improvements": ["改进点1", "改进点2"]
}}"""
        
        try:
            result = await self.complete(
                prompt=user_prompt,
                system_prompt=system_prompt,
                response_format="json"
            )
            return result
        except Exception as e:
            logger.error(f"Failed to evaluate text response: {str(e)}")
            raise
    
    async def generate_feedback(
        self,
        scores: Dict[str, float],
        responses: Dict[str, Any]
    ) -> str:
        """
        Generate overall feedback based on scores and responses
        
        Args:
            scores: Dimension scores
            responses: Candidate responses
        
        Returns:
            Generated feedback text
        """
        system_prompt = """你是一个专业的反馈生成专家。
        请根据评分和回答情况，生成建设性的反馈。"""
        
        user_prompt = f"""基于以下评估结果生成反馈：
各维度得分：{json.dumps(scores, ensure_ascii=False)}

请生成200-300字的专业反馈，包括：
1. 总体表现
2. 突出优势
3. 改进建议
4. 发展方向"""
        
        try:
            result = await self.complete(
                prompt=user_prompt,
                system_prompt=system_prompt,
                response_format="text"
            )
            return result.get("content", "")
        except Exception as e:
            logger.error(f"Failed to generate feedback: {str(e)}")
            raise
    
    async def generate_from_template(
        self,
        template_id: str,
        parameters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Generate questionnaire from a template
        
        Args:
            template_id: Template identifier
            parameters: Template parameters
        
        Returns:
            Generated questionnaire
        """
        # Get template based on ID
        templates = {
            "tobacco_quality_inspector": {
                "position": "烟草质量检验员",
                "dimensions": ["技术知识", "质量意识", "细节观察", "合规性"],
                "question_count": 25
            },
            "tobacco_sales_manager": {
                "position": "烟草销售经理",
                "dimensions": ["市场分析", "客户管理", "销售技巧", "团队领导"],
                "question_count": 20
            }
        }
        
        template = templates.get(template_id, {})
        if not template:
            raise ValueError(f"Template {template_id} not found")
        
        # Merge with parameters
        config = {**template, **parameters}
        
        return await self.generate_questionnaire(
            position_type=config.get("position"),
            dimensions=config.get("dimensions"),
            question_count=config.get("question_count", 20),
            industry="烟草"
        )
    
    def _build_generation_prompt(
        self,
        position_type: str,
        dimensions: List[str],
        question_count: int,
        **kwargs
    ) -> str:
        """
        Build prompt for questionnaire generation
        
        Args:
            position_type: Position type
            dimensions: Evaluation dimensions
            question_count: Number of questions
            **kwargs: Additional parameters
        
        Returns:
            Formatted prompt string
        """
        industry = kwargs.get("industry", "烟草")
        difficulty = kwargs.get("difficulty", "medium")
        
        prompt = f"""请为{industry}行业的{position_type}岗位生成{question_count}道评估题目。

评估维度：{', '.join(dimensions)}
难度等级：{difficulty}
题型要求：单选题为主，每题4个选项

要求：
1. 贴近实际工作场景
2. 选项有明显区分度
3. 覆盖所有评估维度
4. 包含评分标准"""
        
        return prompt
    
    def _parse_llm_response(self, response: str) -> Dict[str, Any]:
        """
        Parse LLM response to extract structured data
        
        Args:
            response: Raw LLM response
        
        Returns:
            Parsed structured data
        """
        # Try to parse as JSON first
        try:
            return json.loads(response)
        except json.JSONDecodeError:
            # Extract JSON from text
            return self._extract_json_from_text(response)
    
    def _estimate_tokens(self, text: str) -> int:
        """
        Estimate token count for text
        
        Args:
            text: Input text
        
        Returns:
            Estimated token count
        """
        # Rough estimation: 1 token ≈ 0.75 words for English, 0.5 characters for Chinese
        chinese_chars = len([c for c in text if ord(c) > 0x4e00])
        other_chars = len(text) - chinese_chars
        
        estimated_tokens = chinese_chars * 0.5 + other_chars * 0.25
        return int(estimated_tokens)
    
    async def _check_rate_limit(self) -> bool:
        """
        Check if rate limit allows making a request
        
        Returns:
            True if request can be made, False otherwise
        """
        try:
            # Try to acquire without waiting
            now = datetime.now()
            cutoff = now - timedelta(minutes=self.rate_limiter.per_minutes)
            recent_requests = [
                req for req in self.rate_limiter.requests 
                if req > cutoff
            ]
            return len(recent_requests) < self.rate_limiter.max_requests
        except:
            return True
    
    # _make_api_request method removed - now using AIServiceManager
    # close method removed - AIServiceManager handles client lifecycle


# Global instance
llm_service = LLMService()