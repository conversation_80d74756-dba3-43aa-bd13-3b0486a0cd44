"""
Health Check Service for LLM Providers and System Components
使用统一的AIServiceManager进行AI provider健康检查

Author: TalentForge Pro Team
Date: 2025-08-11
"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
import asyncio

from app.core.config import settings
from app.core.database import AsyncSessionLocal
from app.core.redis import get_redis
from app.storage.minio_client import minio_client
from app.services.ai_service_manager import ai_service_manager

logger = logging.getLogger(__name__)


class HealthCheckService:
    """Service for checking health status of all system components"""
    
    def __init__(self):
        self.components = {
            "database": self._check_database,
            "redis": self._check_redis,
            "minio": self._check_minio,
            "llm": self._check_llm_provider
        }
    
    async def check_all(self) -> Dict[str, Any]:
        """
        Check health status of all components
        
        Returns:
            Dict containing health status of each component
        """
        results = {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "environment": settings.ENVIRONMENT,
            "version": settings.VERSION,
            "components": {}
        }
        
        # Run all health checks concurrently
        tasks = []
        for component_name, check_func in self.components.items():
            tasks.append(self._run_check(component_name, check_func))
        
        check_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        for component_name, result in zip(self.components.keys(), check_results):
            if isinstance(result, Exception):
                results["components"][component_name] = {
                    "status": "unhealthy",
                    "error": str(result),
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
                results["status"] = "degraded"
            else:
                results["components"][component_name] = result
                if result.get("status") != "healthy":
                    results["status"] = "degraded"
        
        return results
    
    async def _run_check(self, name: str, check_func) -> Dict[str, Any]:
        """Run a single health check with error handling"""
        try:
            return await check_func()
        except Exception as e:
            logger.error(f"Health check failed for {name}: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    async def _check_database(self) -> Dict[str, Any]:
        """Check PostgreSQL database connectivity"""
        try:
            async with AsyncSessionLocal() as session:
                # Execute a simple query
                result = await session.execute("SELECT 1")
                result.scalar()
                
                # Check pgvector extension
                vector_result = await session.execute(
                    "SELECT installed_version FROM pg_available_extensions WHERE name = 'vector'"
                )
                vector_version = vector_result.scalar()
                
                return {
                    "status": "healthy",
                    "type": "PostgreSQL",
                    "version": "17",
                    "pgvector_version": vector_version or "not installed",
                    "connection": settings.POSTGRES_SERVER,
                    "database": settings.POSTGRES_DB,
                    "timestamp": datetime.now(timezone.utc).isoformat()
                }
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    async def _check_redis(self) -> Dict[str, Any]:
        """Check Redis connectivity"""
        try:
            redis_client = await get_redis()
            
            # Ping Redis
            await redis_client.ping()
            
            # Get Redis info
            info = await redis_client.info()
            
            return {
                "status": "healthy",
                "type": "Redis",
                "version": info.get("redis_version", "unknown"),
                "connection": f"{settings.REDIS_HOST}:{settings.REDIS_PORT}",
                "memory_used": info.get("used_memory_human", "unknown"),
                "connected_clients": info.get("connected_clients", 0),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    async def _check_minio(self) -> Dict[str, Any]:
        """Check MinIO connectivity"""
        try:
            # List buckets to verify connection
            buckets = minio_client.client.list_buckets()
            bucket_names = [bucket.name for bucket in buckets]
            
            # Check if required buckets exist
            required_buckets = [
                settings.MINIO_BUCKET,
                settings.MINIO_BUCKET_RESUMES,
                settings.MINIO_BUCKET_JDS
            ]
            
            missing_buckets = [b for b in required_buckets if b not in bucket_names]
            
            return {
                "status": "healthy" if not missing_buckets else "degraded",
                "type": "MinIO",
                "endpoint": settings.MINIO_ENDPOINT,
                "buckets": bucket_names,
                "missing_buckets": missing_buckets,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            logger.error(f"MinIO health check failed: {e}")
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    async def _check_llm_provider(self) -> Dict[str, Any]:
        """Check current LLM provider connectivity using AI Service Manager"""
        # 使用AI管理器的健康检查
        ai_health = await ai_service_manager.check_all_providers_health()
        
        # 只返回LLM相关的健康信息
        llm_health = {
            "status": ai_health["status"],
            "timestamp": ai_health["timestamp"],
            "current_provider": ai_health["current_llm_provider"],
            "llm_providers": {},
        }
        
        # 筛选LLM可用的provider
        for provider, status in ai_health["providers"].items():
            config = ai_service_manager.get_provider_config(provider)
            if config and config.get("llm_model"):
                llm_health["llm_providers"][provider] = {
                    "status": status["status"],
                    "model": config["llm_model"],
                    "temperature": config.get("temperature"),
                    "max_tokens": config.get("max_tokens")
                }
        
        return llm_health
    
    async def check_all_llm_providers(self) -> Dict[str, Any]:
        """
        Check connectivity and API key validity for all configured LLM providers
        
        Returns:
            Dict containing health status of each provider
        """
        # 使用AI管理器的统一健康检查
        return await ai_service_manager.check_all_providers_health()
    
    # _check_single_llm_provider method removed - now using AIServiceManager
    
    def _sanitize_error(self, error_message: str) -> str:
        """Remove sensitive information from error messages"""
        # Remove API keys from error messages
        import re
        # Match common API key patterns
        sanitized = re.sub(r'(api[_-]?key["\']?\s*[:=]\s*["\']?)([^"\']+)(["\']?)', 
                          r'\1***REDACTED***\3', error_message, flags=re.IGNORECASE)
        sanitized = re.sub(r'(sk-[a-zA-Z0-9]{48})', '***REDACTED***', sanitized)
        return sanitized
    
    async def quick_health(self) -> Dict[str, Any]:
        """Quick health check - just check if services are responding"""
        return {
            "status": "healthy",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "environment": settings.ENVIRONMENT,
            "version": settings.VERSION
        }


# Create global instance
health_service = HealthCheckService()