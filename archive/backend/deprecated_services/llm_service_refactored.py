"""
Refactored LLM Service with Multi-Provider Support
"""
import asyncio
import json
import logging
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

from app.core.config import settings
from app.services.llm_provider import LLMProviderFactory, BaseLLMProvider

logger = logging.getLogger(__name__)


class RateLimiter:
    """Simple rate limiter for API calls"""
    
    def __init__(self, max_requests: int = 10, per_minutes: int = 1):
        self.max_requests = max_requests
        self.per_minutes = per_minutes
        self.requests: List[datetime] = []
        self.lock = asyncio.Lock()
    
    async def acquire(self):
        """Acquire permission to make a request"""
        async with self.lock:
            now = datetime.now()
            cutoff = now - timedelta(minutes=self.per_minutes)
            
            # Remove old requests
            self.requests = [req_time for req_time in self.requests if req_time > cutoff]
            
            # Check if we can make a request
            if len(self.requests) >= self.max_requests:
                wait_time = (self.requests[0] + timedelta(minutes=self.per_minutes) - now).total_seconds()
                if wait_time > 0:
                    logger.warning(f"Rate limit reached. Waiting {wait_time:.1f} seconds")
                    await asyncio.sleep(wait_time)
                    return await self.acquire()  # Retry after waiting
            
            # Record this request
            self.requests.append(now)


class LLMService:
    """Service for interacting with multiple LLM providers"""
    
    def __init__(self, provider: Optional[str] = None):
        """
        Initialize LLM Service with specified provider
        
        Args:
            provider: Provider name (deepseek, moonshot, gemini, openrouter, qwen, openai)
                     If None, uses LLM_PROVIDER from settings
        """
        self.provider_name = provider or settings.LLM_PROVIDER
        self.provider: BaseLLMProvider = LLMProviderFactory.create(self.provider_name)
        
        # Set up rate limiter based on provider
        self.rate_limiter = RateLimiter(
            max_requests=self.provider.rate_limit,
            per_minutes=1
        )
        
        # Maintain backward compatibility attributes
        self.api_key = self.provider.api_key
        self.api_base = self.provider.api_base
        self.model = self.provider.model
        self.temperature = self.provider.temperature
        self.max_tokens = self.provider.max_tokens
        
        self._request_count = 0
        self._token_count = 0
        
        logger.info(f"LLMService initialized with provider: {self.provider_name}")
    
    async def complete(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[str] = "json"
    ) -> Dict[str, Any]:
        """
        Send a completion request to the configured LLM provider
        
        Args:
            prompt: The user prompt
            system_prompt: Optional system prompt
            temperature: Override default temperature
            max_tokens: Override default max tokens
            response_format: Expected response format (json or text)
        
        Returns:
            Parsed response from the API
        """
        if not self.provider.api_key:
            logger.error(f"{self.provider_name} API key not configured")
            raise ValueError(f"{self.provider_name.upper()}_API_KEY not configured")
        
        # Acquire rate limit permission
        await self.rate_limiter.acquire()
        
        # Log request
        self._request_count += 1
        logger.info(f"LLM request #{self._request_count} to {self.provider_name}")
        
        # Ensure JSON is mentioned in prompt for JSON responses
        if response_format == "json" and "json" not in prompt.lower():
            prompt = f"{prompt}\nPlease respond in valid JSON format."
        
        try:
            result = await self.provider.complete(
                prompt=prompt,
                system_prompt=system_prompt or "",
                temperature=temperature or self.temperature,
                max_tokens=max_tokens or self.max_tokens
            )
            
            # Log token usage estimate
            self._token_count += len(prompt) // 4 + (max_tokens or self.max_tokens)
            
            return result
            
        except Exception as e:
            logger.error(f"LLM completion error with {self.provider_name}: {e}")
            raise
    
    async def chat(
        self,
        messages: List[Dict[str, str]],
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[str] = "json"
    ) -> Dict[str, Any]:
        """
        Send a chat completion request to the configured LLM provider
        
        Args:
            messages: List of message dictionaries with 'role' and 'content'
            temperature: Override default temperature
            max_tokens: Override default max tokens
            response_format: Expected response format (json or text)
        
        Returns:
            Parsed response from the API
        """
        if not self.provider.api_key:
            logger.error(f"{self.provider_name} API key not configured")
            raise ValueError(f"{self.provider_name.upper()}_API_KEY not configured")
        
        # Acquire rate limit permission
        await self.rate_limiter.acquire()
        
        # Log request
        self._request_count += 1
        logger.info(f"LLM chat request #{self._request_count} to {self.provider_name}")
        
        # Ensure JSON is mentioned for JSON responses
        if response_format == "json" and messages:
            last_msg = messages[-1]
            if last_msg.get("role") == "user" and "json" not in last_msg["content"].lower():
                messages[-1]["content"] = f"{last_msg['content']}\nPlease respond in valid JSON format."
        
        try:
            result = await self.provider.chat(
                messages=messages,
                temperature=temperature or self.temperature,
                max_tokens=max_tokens or self.max_tokens
            )
            
            # Log token usage estimate
            total_content = " ".join([msg.get("content", "") for msg in messages])
            self._token_count += len(total_content) // 4 + (max_tokens or self.max_tokens)
            
            return result
            
        except Exception as e:
            logger.error(f"LLM chat error with {self.provider_name}: {e}")
            raise
    
    async def generate_questionnaire(
        self,
        position_type: str,
        dimensions: List[str],
        question_count: int = 20,
        industry: str = "烟草"
    ) -> Dict[str, Any]:
        """
        Generate a questionnaire using the configured LLM provider
        
        Args:
            position_type: Type of position
            dimensions: List of dimensions to evaluate
            question_count: Number of questions to generate
            industry: Industry context
        
        Returns:
            Generated questionnaire with questions
        """
        system_prompt = f"""你是一个专业的{industry}行业人才评估专家。
        请根据岗位要求生成高质量的评估问卷。
        必须返回严格的JSON格式，包含标题、描述和问题列表。"""
        
        user_prompt = f"""请为{position_type}岗位生成{question_count}道评估题目。

评估维度：{', '.join(dimensions)}

要求：
1. 题目要符合{industry}行业特点
2. 覆盖所有评估维度
3. 包含不同题型（单选、多选、文本、评分等）
4. 难度适中，有区分度

请返回JSON格式：
{{
    "title": "问卷标题",
    "description": "问卷描述",
    "position_type": "{position_type}",
    "industry": "{industry}",
    "dimensions": {json.dumps(dimensions, ensure_ascii=False)},
    "questions": [
        {{
            "id": "q1",
            "content": "题目内容",
            "type": "single_choice/multiple_choice/text/rating/numeric",
            "dimension": "所属维度",
            "required": true,
            "options": ["选项1", "选项2"],  // 选择题才有
            "scoring": {{
                "max_score": 10,
                "scoring_type": "direct/calculated"
            }}
        }}
    ]
}}"""
        
        try:
            result = await self.complete(
                prompt=user_prompt,
                system_prompt=system_prompt,
                response_format="json"
            )
            
            # Validate questionnaire structure
            if isinstance(result, dict) and "questions" in result:
                logger.info(f"Generated questionnaire with {len(result.get('questions', []))} questions")
                return result
            else:
                logger.warning("Invalid questionnaire format, using fallback")
                return self._generate_fallback_questionnaire(
                    position_type, dimensions, question_count, industry
                )
                
        except Exception as e:
            logger.error(f"Error generating questionnaire: {e}")
            return self._generate_fallback_questionnaire(
                position_type, dimensions, question_count, industry
            )
    
    async def evaluate_text_response(
        self,
        question: str,
        response: str,
        criteria: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Evaluate a text response using the configured LLM provider
        
        Args:
            question: The question asked
            response: The candidate's response
            criteria: Evaluation criteria
        
        Returns:
            Evaluation result with score and feedback
        """
        system_prompt = """你是一个专业的答案评估专家。
        请根据问题和回答，给出客观的评分和反馈。
        评分范围0-100分，要有明确的评分理由。"""
        
        user_prompt = f"""请评估以下回答：
问题：{question}
回答：{response}

评估标准：
{json.dumps(criteria or {"完整性": 30, "准确性": 40, "深度": 30}, ensure_ascii=False)}

请返回JSON格式：
{{
    "score": 0-100,
    "feedback": "具体反馈",
    "strengths": ["优点1", "优点2"],
    "improvements": ["改进点1", "改进点2"]
}}"""
        
        try:
            result = await self.complete(
                prompt=user_prompt,
                system_prompt=system_prompt,
                response_format="json"
            )
            
            # Validate evaluation structure
            if isinstance(result, dict) and "score" in result:
                logger.info(f"Text evaluation completed with score: {result.get('score')}")
                return result
            else:
                logger.warning("Invalid evaluation format, using fallback")
                return {
                    "score": 70,
                    "feedback": "回答基本符合要求",
                    "strengths": ["内容完整"],
                    "improvements": ["可以增加更多细节"]
                }
                
        except Exception as e:
            logger.error(f"Error evaluating text response: {e}")
            return {
                "score": 70,
                "feedback": "评估服务暂时不可用",
                "strengths": [],
                "improvements": []
            }
    
    def _generate_fallback_questionnaire(
        self,
        position_type: str,
        dimensions: List[str],
        question_count: int,
        industry: str
    ) -> Dict[str, Any]:
        """Generate a fallback questionnaire when LLM fails"""
        questions = []
        for i in range(min(question_count, 5)):
            dimension = dimensions[i % len(dimensions)]
            questions.append({
                "id": f"q{i+1}",
                "content": f"{dimension}相关问题 {i+1}",
                "type": "text",
                "dimension": dimension,
                "required": True,
                "scoring": {
                    "max_score": 10,
                    "scoring_type": "direct"
                }
            })
        
        return {
            "title": f"{position_type}能力评估问卷",
            "description": f"用于评估{position_type}岗位候选人的能力",
            "position_type": position_type,
            "industry": industry,
            "dimensions": dimensions,
            "questions": questions
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service usage statistics"""
        return {
            "provider": self.provider_name,
            "request_count": self._request_count,
            "estimated_tokens": self._token_count,
            "rate_limit": self.provider.rate_limit,
            "model": self.model
        }
    
    @classmethod
    def list_available_providers(cls) -> List[str]:
        """List all available LLM providers"""
        return LLMProviderFactory.list_providers()
    
    async def switch_provider(self, provider_name: str):
        """Switch to a different LLM provider"""
        logger.info(f"Switching from {self.provider_name} to {provider_name}")
        self.provider_name = provider_name
        self.provider = LLMProviderFactory.create(provider_name)
        
        # Update attributes
        self.api_key = self.provider.api_key
        self.api_base = self.provider.api_base
        self.model = self.provider.model
        self.temperature = self.provider.temperature
        self.max_tokens = self.provider.max_tokens
        
        # Update rate limiter
        self.rate_limiter = RateLimiter(
            max_requests=self.provider.rate_limit,
            per_minutes=1
        )
        
        logger.info(f"Successfully switched to {provider_name}")

# Global instance for compatibility
llm_service_refactored = LLMService()
