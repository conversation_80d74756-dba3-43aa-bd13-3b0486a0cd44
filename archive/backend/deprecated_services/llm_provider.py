"""
LLM Provider Factory for Multi-Provider Support
All providers use OpenAI-compatible APIs
Supports: DeepSeek, Moonshot, OpenRouter, Qwen
"""
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod
import logging
from openai import AsyncOpenAI

from app.core.config import settings

logger = logging.getLogger(__name__)


class BaseLLMProvider(ABC):
    """Base class for all LLM providers"""
    
    def __init__(self):
        self.api_key: Optional[str] = None
        self.api_base: str = ""
        self.model: str = ""
        self.temperature: float = 0.4
        self.max_tokens: int = 4000
        self.rate_limit: int = 10
        
    @abstractmethod
    async def complete(self, prompt: str, system_prompt: str = "", **kwargs) -> Dict[str, Any]:
        """Complete a prompt"""
        pass
    
    @abstractmethod
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """Chat completion with message history"""
        pass


class OpenAICompatibleProvider(BaseLLMProvider):
    """Provider for OpenAI-compatible APIs (DeepSeek, Moonshot, OpenRouter, Qwen)"""
    
    def __init__(self, provider_name: str):
        super().__init__()
        self.provider_name = provider_name
        self._load_config()
        self.client = None
        if self.api_key:
            self.client = AsyncOpenAI(
                api_key=self.api_key,
                base_url=self.api_base
            )
    
    def _load_config(self):
        """Load configuration based on provider name"""
        prefix = self.provider_name.upper()
        self.api_key = getattr(settings, f"{prefix}_API_KEY", None)
        self.api_base = getattr(settings, f"{prefix}_API_BASE", "")
        self.model = getattr(settings, f"{prefix}_MODEL", "")
        self.temperature = getattr(settings, f"{prefix}_TEMPERATURE", 0.4)
        self.max_tokens = getattr(settings, f"{prefix}_MAX_TOKENS", 4000)
        self.rate_limit = getattr(settings, f"{prefix}_RATE_LIMIT", 10)
    
    async def complete(self, prompt: str, system_prompt: str = "", **kwargs) -> Dict[str, Any]:
        """Complete a prompt using OpenAI-compatible API"""
        if not self.client:
            raise ValueError(f"{self.provider_name} API key not configured")
        
        messages = []
        if system_prompt:
            messages.append({"role": "system", "content": system_prompt})
        messages.append({"role": "user", "content": prompt})
        
        try:
            response = await self.client.chat.completions.create(
                model=kwargs.get("model", self.model),
                messages=messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", self.max_tokens)
            )
            
            content = response.choices[0].message.content
            
            # Try to parse as JSON if requested
            if kwargs.get("response_format") == "json" and content:
                import json
                if content.strip().startswith('{') or content.strip().startswith('['):
                    try:
                        return json.loads(content)
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse JSON response from {self.provider_name}")
            
            return {"content": content}
            
        except Exception as e:
            logger.error(f"{self.provider_name} completion error: {e}")
            raise
    
    async def chat(self, messages: List[Dict[str, str]], **kwargs) -> Dict[str, Any]:
        """Chat completion with message history"""
        if not self.client:
            raise ValueError(f"{self.provider_name} API key not configured")
        
        try:
            response = await self.client.chat.completions.create(
                model=kwargs.get("model", self.model),
                messages=messages,
                temperature=kwargs.get("temperature", self.temperature),
                max_tokens=kwargs.get("max_tokens", self.max_tokens)
            )
            
            content = response.choices[0].message.content
            
            # Try to parse as JSON if requested
            if kwargs.get("response_format") == "json" and content:
                import json
                if content.strip().startswith('{') or content.strip().startswith('['):
                    try:
                        return json.loads(content)
                    except json.JSONDecodeError:
                        logger.warning(f"Failed to parse JSON response from {self.provider_name}")
            
            return {"content": content}
            
        except Exception as e:
            logger.error(f"{self.provider_name} chat error: {e}")
            raise


class LLMProviderFactory:
    """Factory for creating LLM providers"""
    
    # All providers now use the same OpenAI-compatible implementation
    _providers = {
        "deepseek": lambda: OpenAICompatibleProvider("deepseek"),
        "moonshot": lambda: OpenAICompatibleProvider("moonshot"),
        "openrouter": lambda: OpenAICompatibleProvider("openrouter"),
        "qwen": lambda: OpenAICompatibleProvider("qwen"),  # Now using compatible-mode URL
    }
    
    @classmethod
    def create(cls, provider_name: str) -> BaseLLMProvider:
        """Create an LLM provider instance"""
        if provider_name not in cls._providers:
            raise ValueError(f"Unknown provider: {provider_name}. Available: {list(cls._providers.keys())}")
        
        return cls._providers[provider_name]()
    
    @classmethod
    def get_available_providers(cls) -> List[str]:
        """Get list of available provider names"""
        return list(cls._providers.keys())