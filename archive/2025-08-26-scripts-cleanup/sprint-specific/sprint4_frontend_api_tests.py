#!/usr/bin/env python3
"""
Sprint 4 前端API功能验证测试套件
================================
专门验证前端需要的所有智能招聘API端点

根据前端需求文档测试以下API:
- 智能招聘仪表盘API
- 智能匹配API  
- 五维度能力评估API
- 简历解析API
- 批量处理API

执行方式:
    python sprint4_frontend_api_tests.py [--base-url URL] [--verbose]
"""

import asyncio
import httpx
import json
import time
import sys
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import argparse
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestStatus(Enum):
    """测试状态枚举"""
    PASSED = "PASSED"
    FAILED = "FAILED" 
    SKIPPED = "SKIPPED"
    MISSING = "MISSING"  # API端点不存在
    SCHEMA_ERROR = "SCHEMA_ERROR"  # Schema不匹配

@dataclass
class TestResult:
    """测试结果数据类"""
    test_name: str
    endpoint: str
    method: str
    status: TestStatus
    response_code: Optional[int] = None
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    expected_schema: Optional[Dict] = None
    actual_response: Optional[Any] = None
    notes: Optional[str] = None

class Sprint4FrontendAPITester:
    """Sprint 4前端API测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8088/api/v1"):
        self.base_url = base_url
        self.auth_token: Optional[str] = None
        self.results: List[TestResult] = []
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """执行所有测试"""
        print("🚀 Sprint 4 前端API功能验证测试")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            # 1. 认证测试
            await self._test_authentication()
            
            if not self.auth_token:
                logger.error("认证失败，跳过其他测试")
                return self._generate_report(time.time() - start_time)
            
            # 2. 仪表盘API测试
            await self._test_dashboard_apis()
            
            # 3. 智能匹配API测试
            await self._test_matching_apis()
            
            # 4. 能力评估API测试
            await self._test_assessment_apis()
            
            # 5. 简历解析API测试
            await self._test_resume_apis()
            
            # 6. 批量处理API测试
            await self._test_batch_apis()
            
        except Exception as e:
            logger.error(f"测试执行过程中发生错误: {str(e)}")
            
        total_time = time.time() - start_time
        return self._generate_report(total_time)
    
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict] = None,
        files: Optional[Dict] = None,
        auth: bool = True
    ) -> TestResult:
        """发送HTTP请求"""
        url = f"{self.base_url}{endpoint}"
        headers = {"Content-Type": "application/json"}
        
        if auth and self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        
        start_time = time.time()
        
        try:
            async with httpx.AsyncClient() as client:
                kwargs = {"headers": headers, "timeout": 30.0}
                
                if data:
                    kwargs["json"] = data
                
                response = await client.request(method, url, **kwargs)
                response_time = (time.time() - start_time) * 1000
                
                try:
                    response_data = response.json() if response.text else None
                except json.JSONDecodeError:
                    response_data = response.text
                
                return TestResult(
                    test_name=f"{method} {endpoint}",
                    endpoint=endpoint,
                    method=method,
                    status=TestStatus.PASSED if 200 <= response.status_code < 300 else TestStatus.FAILED,
                    response_code=response.status_code,
                    response_time=response_time,
                    actual_response=response_data,
                    error_message=response.text if response.status_code >= 400 else None
                )
                    
        except asyncio.TimeoutError:
            return TestResult(
                test_name=f"{method} {endpoint}",
                endpoint=endpoint,
                method=method,
                status=TestStatus.FAILED,
                error_message="请求超时 (>30s)"
            )
        except Exception as e:
            return TestResult(
                test_name=f"{method} {endpoint}",
                endpoint=endpoint,
                method=method,
                status=TestStatus.FAILED,
                error_message=str(e)
            )
    
    async def _test_authentication(self):
        """测试认证"""
        logger.info("🔐 测试认证...")
        
        login_data = {
            "username": "<EMAIL>",
            "password": "test123"
        }
        
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{self.base_url}/auth/login",
                    data=login_data,  # OAuth2使用form data
                    timeout=30.0
                )
                
                if response.status_code == 200:
                    data = response.json()
                    self.auth_token = data.get("access_token")
                    self.results.append(TestResult(
                        test_name="认证登录",
                        endpoint="/auth/login",
                        method="POST",
                        status=TestStatus.PASSED,
                        response_code=200,
                        notes="成功获取访问令牌"
                    ))
                else:
                    self.results.append(TestResult(
                        test_name="认证登录",
                        endpoint="/auth/login", 
                        method="POST",
                        status=TestStatus.FAILED,
                        response_code=response.status_code,
                        error_message=response.text
                    ))
        except Exception as e:
            self.results.append(TestResult(
                test_name="认证登录",
                endpoint="/auth/login",
                method="POST", 
                status=TestStatus.FAILED,
                error_message=str(e)
            ))
    
    async def _test_dashboard_apis(self):
        """测试仪表盘API"""
        logger.info("📊 测试仪表盘API...")
        
        # 测试统计API
        result = await self._make_request("GET", "/recruitment/dashboard/stats/")
        result.test_name = "仪表盘统计"
        result.expected_schema = {
            "new_candidates": "int",
            "pending_matches": "int", 
            "avg_dci_score": "float",
            "weekly_assessments": "int"
        }
        self.results.append(result)
        
        # 测试趋势API
        result = await self._make_request("GET", "/recruitment/dashboard/trends/")
        result.test_name = "仪表盘趋势"
        result.expected_schema = {
            "period": "str",
            "metrics": "dict"
        }
        self.results.append(result)
        
        # 测试活动API
        result = await self._make_request("GET", "/recruitment/dashboard/activities/")
        result.test_name = "仪表盘活动"
        result.expected_schema = {
            "items": "list",
            "total": "int"
        }
        self.results.append(result)
    
    async def _test_matching_apis(self):
        """测试智能匹配API"""
        logger.info("🧠 测试智能匹配API...")
        
        # 测试候选人-岗位匹配API
        matching_data = {
            "candidate_id": "610489142907899904",  # 前端期望的格式
            "top_k": 3,
            "min_score": 0.1,
            "include_details": True
        }
        
        result = await self._make_request("POST", "/matching/candidate-jobs", matching_data)
        result.test_name = "候选人岗位匹配"
        result.expected_schema = {
            "candidate_id": "str",
            "matches": "list",
            "dci_score": "float",
            "processing_time_ms": "float"
        }
        
        # 检查Schema兼容性
        if result.response_code == 422:
            result.status = TestStatus.SCHEMA_ERROR
            result.notes = "前端期望candidate_id(单数)，后端要求candidate_ids(复数)"
        
        self.results.append(result)
        
        # 尝试用正确的schema再次测试
        matching_data_corrected = {
            "candidate_ids": ["610489142907899904"],  # 后端实际格式
            "top_k": 3,
            "min_score": 0.1,
            "include_details": True
        }
        
        result2 = await self._make_request("POST", "/matching/candidate-jobs", matching_data_corrected)
        result2.test_name = "候选人岗位匹配(修正schema)"
        result2.notes = "使用后端实际要求的candidate_ids格式"
        self.results.append(result2)
    
    async def _test_assessment_apis(self):
        """测试能力评估API"""
        logger.info("📈 测试能力评估API...")
        
        # 测试生成评估API
        assessment_data = {
            "candidate_id": "610489142907899904",
            "include_recommendations": True,
            "force_regenerate": False
        }
        
        result = await self._make_request("POST", "/assessment/generate", assessment_data)
        result.test_name = "生成能力评估"
        result.expected_schema = {
            "success": "bool",
            "assessment_id": "int",
            "assessment": "dict"
        }
        self.results.append(result)
        
        # 测试JFS计算API
        jfs_data = {
            "candidate_id": "610489142907899904",
            "position_id": "609923316945391616"
        }
        
        result = await self._make_request("POST", "/assessment/calculate-jfs", jfs_data)
        result.test_name = "JFS分数计算"
        result.expected_schema = {
            "jfs_score": "float",
            "match_confidence": "float"
        }
        self.results.append(result)
        
        # 测试候选人对比API
        compare_data = {
            "candidate_ids": ["610489142907899904", "610489142840791040"],
            "position_id": "609923316945391616"
        }
        
        result = await self._make_request("POST", "/assessment/compare", compare_data)
        result.test_name = "候选人对比"
        result.expected_schema = {
            "success": "bool",
            "candidates_compared": "int",
            "comparison_summary": "dict"
        }
        self.results.append(result)
    
    async def _test_resume_apis(self):
        """测试简历解析API"""
        logger.info("📄 测试简历解析API...")
        
        # 测试简历解析路径是否存在
        result = await self._make_request("GET", "/resume/")
        result.test_name = "简历解析API路径"
        
        if result.response_code == 404:
            result.status = TestStatus.MISSING
            result.notes = "前端需要的简历解析API路径不存在"
        
        self.results.append(result)
        
        # 也测试legacy路径
        result2 = await self._make_request("GET", "/matching-legacy/parse-resume")
        result2.test_name = "简历解析API(Legacy)"
        result2.notes = "检查是否在legacy路径下实现"
        self.results.append(result2)
    
    async def _test_batch_apis(self):
        """测试批量处理API"""
        logger.info("⚡ 测试批量处理API...")
        
        # 测试批量处理路径是否存在
        result = await self._make_request("GET", "/batch/")
        result.test_name = "批量处理API路径"
        
        if result.response_code == 404:
            result.status = TestStatus.MISSING
            result.notes = "前端需要的批量处理API路径不存在"
        
        self.results.append(result)
        
        # 测试批量任务状态API路径
        result2 = await self._make_request("GET", "/batch/status/test-task-id")
        result2.test_name = "批量任务状态API"
        self.results.append(result2)
    
    def _serialize_result(self, result: TestResult) -> Dict[str, Any]:
        """序列化TestResult为JSON兼容格式"""
        result_dict = asdict(result)
        result_dict['status'] = result.status.value  # 转换枚举为字符串
        return result_dict
    
    def _generate_report(self, total_time: float) -> Dict[str, Any]:
        """生成测试报告"""
        passed = len([r for r in self.results if r.status == TestStatus.PASSED])
        failed = len([r for r in self.results if r.status == TestStatus.FAILED])
        schema_errors = len([r for r in self.results if r.status == TestStatus.SCHEMA_ERROR])
        missing = len([r for r in self.results if r.status == TestStatus.MISSING])
        
        print("\n" + "=" * 60)
        print("📊 Sprint 4 前端API测试报告")
        print("=" * 60)
        print(f"总测试数: {len(self.results)}")
        print(f"✅ 通过: {passed}")
        print(f"❌ 失败: {failed}")
        print(f"🔧 Schema错误: {schema_errors}")
        print(f"🚫 API缺失: {missing}")
        print(f"⏱️ 总耗时: {total_time:.2f}秒")
        
        # 详细结果
        print("\n📋 详细测试结果:")
        print("-" * 60)
        for result in self.results:
            status_icon = {
                TestStatus.PASSED: "✅",
                TestStatus.FAILED: "❌", 
                TestStatus.SCHEMA_ERROR: "🔧",
                TestStatus.MISSING: "🚫"
            }.get(result.status, "❓")
            
            print(f"{status_icon} {result.test_name}")
            print(f"   端点: {result.method} {result.endpoint}")
            if result.response_code:
                print(f"   状态码: {result.response_code}")
            if result.response_time:
                print(f"   响应时间: {result.response_time:.1f}ms")
            if result.error_message:
                print(f"   错误: {result.error_message[:100]}")
            if result.notes:
                print(f"   备注: {result.notes}")
            print()
        
        return {
            "summary": {
                "total": len(self.results),
                "passed": passed,
                "failed": failed,
                "schema_errors": schema_errors,
                "missing": missing,
                "success_rate": passed / len(self.results) * 100 if self.results else 0,
                "total_time": total_time
            },
            "results": [self._serialize_result(r) for r in self.results],
            "generated_at": datetime.now().isoformat()
        }

async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Sprint 4前端API功能验证测试")
    parser.add_argument("--base-url", default="http://localhost:8088/api/v1", 
                       help="API基础URL")
    parser.add_argument("--verbose", action="store_true", 
                       help="详细输出")
    parser.add_argument("--output", help="输出JSON报告到文件")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    tester = Sprint4FrontendAPITester(args.base_url)
    report = await tester.run_all_tests()
    
    if args.output:
        with open(args.output, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        print(f"📄 测试报告已保存到: {args.output}")
    
    # 根据测试结果设置退出码
    if report["summary"]["failed"] > 0 or report["summary"]["schema_errors"] > 0:
        sys.exit(1)
    else:
        sys.exit(0)

if __name__ == "__main__":
    asyncio.run(main())