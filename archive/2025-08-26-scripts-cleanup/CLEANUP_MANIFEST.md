# Scripts Cleanup Manifest - 2025-08-26

## 📊 Cleanup Summary

Migrated **15 temporary/one-time scripts** from `/app/scripts/` to `/archive/2025-08-26-scripts-cleanup/`

## 📁 Migration Details

### Sprint-Specific Scripts (2 files)
- `validate_sprint4.py` - Sprint 4 validation script (completed sprint)
- `sprint4_frontend_api_tests.py` - Sprint 4 specific tests

### Temporary/One-time Scripts (13 files)

#### One-time Fixes & Additions
- `add-network-health-check.py` - Network health check addition (already integrated)
- `fix-docker-startup.sh` - Docker startup fix (issue resolved)
- `frontend-add-deps.sh` - Added react-day-picker dependency (completed)
- `update-frontend-deps.sh` - Frontend dependency update (completed)
- `update-poetry-lock.sh` - Poetry lock update (completed)
- `rebuild-backend-docker.sh` - Redundant with Makefile commands

#### One-time Tests & Validations
- `test_id_compatibility.py` - ID type migration test (migration complete)
- `test_docker_metrics.py` - Docker metrics test (validation complete)
- `test_ollama_integration.sh` - Ollama integration test (integration verified)
- `verify_deepseek.sh` - DeepSeek API verification (verified)
- `validate_i18n.js` - i18n validation (validation complete)
- `audit-hardcoded-strings.py` - Hardcoded strings audit (audit complete)
- `diagnose-docker-network.sh` - Docker network diagnostic (issue resolved)

## ✅ Scripts Retained (Persistent/Reusable)

### Development Tools (`development/`)
All scripts retained - essential for daily development:
- Development environment management
- Container operations
- Log viewing and debugging

### Docker Management (`docker/`)
All scripts retained - core Docker operations:
- Build and rebuild scripts
- Health checks and status monitoring
- Cleanup utilities

### Validation & Monitoring (`validation/`)
All scripts retained - continuous system validation:
- Infrastructure validation
- Setup verification
- Nginx monitoring

### Test Infrastructure (`test/`)
Core testing framework retained:
- `api_test_suite.py` - Reusable API testing
- `run_integration_tests.sh` - Integration test runner
- `run_regression_tests.sh` - Regression test runner
- Other essential testing utilities

## 📝 Notes

1. **Archived scripts remain accessible** if needed for reference or re-use
2. **Makefile commands** provide better alternatives for many archived scripts
3. **Development workflow** simplified by removing one-time scripts
4. **Test organization** improved by removing sprint-specific tests

## 🎯 Impact

- **Before**: 40+ scripts in `/app/scripts/`
- **After**: ~25 persistent scripts (37% reduction)
- **Benefit**: Cleaner project structure, easier navigation, focused tooling