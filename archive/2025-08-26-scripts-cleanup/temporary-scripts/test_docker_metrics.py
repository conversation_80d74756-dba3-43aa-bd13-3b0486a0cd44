#\!/usr/bin/env python3
"""
测试Docker指标采集功能
"""
import asyncio
import sys
sys.path.insert(0, '/app')

from app.services.monitoring import monitoring_service
from app.core.database import AsyncSessionLocal

async def test_docker_metrics():
    """测试Docker容器指标采集"""
    print("🔍 测试Docker容器指标采集...\n")
    
    # 获取Docker容器统计信息
    container_stats = await monitoring_service._get_docker_container_stats()
    
    if not container_stats:
        print("❌ 未能获取到容器指标")
        return
    
    print(f"✅ 成功获取 {len(container_stats)} 个容器的指标:\n")
    
    for service_name, stats in container_stats.items():
        print(f"📦 {service_name}:")
        print(f"   CPU使用率: {stats.get('cpu_percent', 0):.2f}%")
        print(f"   内存使用率: {stats.get('memory_percent', 0):.2f}%")
        print(f"   内存使用: {stats.get('memory_usage', 'N/A')}")
        print(f"   容器状态: {stats.get('status', 'unknown')}")
        print()
    
    # 测试服务列表中的指标集成
    print("\n🔍 测试监控服务集成...\n")
    
    async with AsyncSessionLocal() as db:
        services_response = await monitoring_service.list_services_health(db, refresh=True)
        
        print(f"📊 服务健康状态 (共{services_response.total}个服务):\n")
        
        for service in services_response.items:
            print(f"✨ {service.display_name} ({service.name}):")
            print(f"   状态: {service.status}")
            
            if service.details and 'container_cpu' in service.details:
                print(f"   容器CPU: {service.details.get('container_cpu', 'N/A')}")
                print(f"   容器内存: {service.details.get('container_memory', 'N/A')}")
            else:
                print(f"   容器指标: 未采集")
            
            if service.metrics:
                print(f"   CPU指标: {service.metrics.get('cpu_usage', 0):.2f}%")
                print(f"   内存指标: {service.metrics.get('memory_usage', 0):.2f}%")
            print()
    
    print("✅ Docker指标测试完成\!")

if __name__ == "__main__":
    asyncio.run(test_docker_metrics())
