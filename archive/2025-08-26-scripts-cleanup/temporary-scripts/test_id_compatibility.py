#!/usr/bin/env python3
"""
ID类型兼容性测试
验证系统对各种ID格式的处理能力
"""
import asyncio
import json
import httpx
import sys
from pathlib import Path
from typing import List, Dict, Any
import uuid
import hashlib

# 由于导入会触发数据库连接，我们直接复制必要的函数
def normalize_id(id_value):
    """标准化ID值为整数"""
    if id_value is None:
        return None
    
    if isinstance(id_value, int):
        return id_value
    
    if isinstance(id_value, str):
        id_value = id_value.strip()
        if not id_value:
            return None
        
        try:
            return int(id_value)
        except ValueError:
            # 生成基于字符串的ID
            hash_obj = hashlib.sha256(id_value.encode())
            return int.from_bytes(hash_obj.digest()[:8], byteorder='big') & 0x7FFFFFFFFFFFFFFF
    
    try:
        return int(id_value)
    except (ValueError, TypeError):
        return None

def is_valid_snowflake_id(value):
    """检查值是否为有效的Snowflake ID"""
    if value is None:
        return False
    
    try:
        id_int = normalize_id(value)
        return id_int is not None and 0 <= id_int <= 2**63 - 1
    except:
        return False


class IDCompatibilityTester:
    """ID兼容性测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8001/api/v1"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
        self.test_results = []
    
    def test_normalize_id(self):
        """测试ID标准化函数"""
        print("\n📝 测试ID标准化函数...")
        
        test_cases = [
            # (输入, 期望输出, 描述)
            (123456789, 123456789, "整数ID"),
            ("123456789", 123456789, "字符串数字ID"),
            ("  123456789  ", 123456789, "带空格的字符串ID"),
            ("", None, "空字符串"),
            (None, None, "None值"),
            ("test-id-123", None, "非数字字符串ID（应生成hash）"),
            (str(uuid.uuid4()), None, "UUID字符串（应生成hash）"),
        ]
        
        passed = 0
        failed = 0
        
        for input_val, expected, description in test_cases:
            result = normalize_id(input_val)
            
            # 对于非数字字符串，应该生成一个稳定的hash ID
            if isinstance(input_val, str) and input_val and not input_val.strip().isdigit():
                if result is not None and isinstance(result, int) and result > 0:
                    print(f"  ✅ {description}: {input_val} → {result} (hash生成)")
                    passed += 1
                else:
                    print(f"  ❌ {description}: {input_val} → {result} (期望生成hash)")
                    failed += 1
            elif result == expected:
                print(f"  ✅ {description}: {input_val} → {result}")
                passed += 1
            else:
                print(f"  ❌ {description}: {input_val} → {result} (期望: {expected})")
                failed += 1
        
        print(f"\n结果: {passed} 通过, {failed} 失败")
        return failed == 0
    
    def test_is_valid_snowflake_id(self):
        """测试Snowflake ID验证函数"""
        print("\n📝 测试Snowflake ID验证...")
        
        test_cases = [
            (7123456789012345678, True, "有效的Snowflake ID"),
            ("7123456789012345678", True, "字符串格式的Snowflake ID"),
            (0, True, "最小值ID"),
            (2**63 - 1, True, "最大值ID"),
            (-1, False, "负数ID"),
            (2**63, False, "超出范围的ID"),
            (None, False, "None值"),
            ("not-a-number", False, "非数字字符串"),
        ]
        
        passed = 0
        failed = 0
        
        for input_val, expected, description in test_cases:
            result = is_valid_snowflake_id(input_val)
            if result == expected:
                print(f"  ✅ {description}: {result}")
                passed += 1
            else:
                print(f"  ❌ {description}: {result} (期望: {expected})")
                failed += 1
        
        print(f"\n结果: {passed} 通过, {failed} 失败")
        return failed == 0
    
    async def test_api_id_acceptance(self):
        """测试API对各种ID格式的接受度"""
        print("\n🌐 测试API ID格式接受度...")
        
        test_ids = [
            ("标准Snowflake ID", "7123456789012345678"),
            ("简单字符串ID", "test-candidate-001"),
            ("UUID格式", str(uuid.uuid4())),
            ("纯数字字符串", "123456"),
            ("包含特殊字符", "user@123#456"),
            ("中文ID", "测试候选人001"),
        ]
        
        results = []
        
        for description, test_id in test_ids:
            try:
                # 测试匹配API
                response = await self.client.post(
                    f"{self.base_url}/matching/candidate-jobs",
                    json={
                        "candidate_id": test_id,
                        "job_ids": [],
                        "top_k": 5,
                        "min_score": 0.5
                    },
                    headers={"Content-Type": "application/json"}
                )
                
                # 检查是否不是UUID验证错误（422）
                if response.status_code == 422:
                    error_detail = response.json().get("detail", "")
                    if "UUID" in str(error_detail):
                        print(f"  ❌ {description}: UUID验证错误")
                        results.append(False)
                    else:
                        print(f"  ⚠️  {description}: 其他验证错误 - {error_detail}")
                        results.append(True)  # 不是UUID问题
                elif response.status_code in [200, 404]:
                    print(f"  ✅ {description}: 接受ID格式")
                    results.append(True)
                else:
                    print(f"  ⚠️  {description}: 状态码 {response.status_code}")
                    results.append(True)  # 不是ID格式问题
                    
            except Exception as e:
                print(f"  ❌ {description}: 异常 - {str(e)}")
                results.append(False)
        
        passed = sum(results)
        failed = len(results) - passed
        acceptance_rate = passed / len(results) * 100 if results else 0
        
        print(f"\n结果: {passed}/{len(results)} 通过 (接受率: {acceptance_rate:.1f}%)")
        return acceptance_rate >= 95  # 95%以上接受率才算通过
    
    async def test_schema_imports(self):
        """测试Schema文件是否正确导入ID类型"""
        print("\n📦 测试Schema导入...")
        
        schema_dir = Path(__file__).parent.parent / "backend" / "app" / "schemas"
        schema_files = [
            "matching.py",
            "assessment.py"
        ]
        
        all_correct = True
        
        for file_name in schema_files:
            file_path = schema_dir / file_name
            if file_path.exists():
                with open(file_path, 'r') as f:
                    content = f.read()
                
                # 检查是否导入了ID类型
                has_import = "from app.core.id_types import" in content
                # 检查是否还有UUID类型的ID定义
                has_uuid_id = "id: UUID" in content or "id: Optional[UUID]" in content
                
                if has_import and not has_uuid_id:
                    print(f"  ✅ {file_name}: 正确导入ID类型")
                else:
                    if not has_import:
                        print(f"  ❌ {file_name}: 缺少ID类型导入")
                    if has_uuid_id:
                        print(f"  ❌ {file_name}: 仍有UUID类型的ID定义")
                    all_correct = False
            else:
                print(f"  ⚠️  {file_name}: 文件不存在")
        
        return all_correct
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("=" * 80)
        print("🔍 ID类型兼容性测试套件")
        print("=" * 80)
        
        results = {}
        
        # 1. 测试核心函数
        results["normalize_id"] = self.test_normalize_id()
        results["is_valid_snowflake_id"] = self.test_is_valid_snowflake_id()
        
        # 2. 测试Schema导入
        results["schema_imports"] = await self.test_schema_imports()
        
        # 3. 测试API（如果服务运行中）
        try:
            health_response = await self.client.get(f"{self.base_url}/health")
            if health_response.status_code in [200, 207]:
                results["api_acceptance"] = await self.test_api_id_acceptance()
            else:
                print("\n⚠️  API服务未运行，跳过API测试")
                results["api_acceptance"] = None
        except:
            print("\n⚠️  无法连接到API服务，跳过API测试")
            results["api_acceptance"] = None
        
        # 总结
        print("\n" + "=" * 80)
        print("📊 测试总结")
        print("=" * 80)
        
        total_tests = sum(1 for v in results.values() if v is not None)
        passed_tests = sum(1 for v in results.values() if v is True)
        
        for test_name, passed in results.items():
            if passed is None:
                status = "⏭️  跳过"
            elif passed:
                status = "✅ 通过"
            else:
                status = "❌ 失败"
            print(f"{test_name:25} {status}")
        
        print(f"\n总计: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("\n🎉 所有测试通过！ID类型统一成功")
            return 0
        else:
            print("\n⚠️  部分测试失败，请检查并修复")
            return 1
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()


async def main():
    """主函数"""
    tester = IDCompatibilityTester()
    
    try:
        exit_code = await tester.run_all_tests()
        return exit_code
    finally:
        await tester.close()


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)