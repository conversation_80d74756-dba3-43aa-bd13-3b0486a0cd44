#!/bin/bash

# 后端 Docker 镜像重建脚本
# 解决 psycopg2-binary 缺失和网络连接问题

set -e  # 遇到错误立即停止

echo "🏗️ TalentForge Pro 后端服务 Docker 镜像重建"
echo "================================================"

BACKEND_DIR="/home/<USER>/source_code/talent_forge_pro/app/backend"
APP_DIR="/home/<USER>/source_code/talent_forge_pro/app"
DOCKER_IMAGE="app_backend:latest"

# 步骤 1: 停止现有后端服务
echo ""
echo "📋 步骤 1: 停止现有后端服务..."
docker-compose -f $APP_DIR/docker-compose.yml stop backend

# 步骤 2: 删除旧镜像
echo ""
echo "📋 步骤 2: 删除旧镜像..."
docker rmi $DOCKER_IMAGE || true

# 步骤 3: 使用修复后的 Dockerfile 构建新镜像
echo ""
echo "📋 步骤 3: 构建新镜像 (使用 --network host)..."
cd $BACKEND_DIR

# 检查是否使用修复后的 Dockerfile
if [ -f "Dockerfile.fixed" ]; then
    echo "✅ 使用修复后的 Dockerfile.fixed"
    # 备份原始 Dockerfile
    cp Dockerfile Dockerfile.backup
    # 使用修复后的版本
    cp Dockerfile.fixed Dockerfile
    
    # 构建镜像
    docker build --network host --no-cache -t $DOCKER_IMAGE .
    
    # 恢复原始 Dockerfile（保留备份）
    # cp Dockerfile.backup Dockerfile
else
    echo "⚠️ 使用现有 Dockerfile (可能有问题)"
    docker build --network host --no-cache -t $DOCKER_IMAGE .
fi

# 步骤 4: 启动后端服务
echo ""
echo "📋 步骤 4: 启动后端服务..."
docker-compose -f $APP_DIR/docker-compose.yml up -d backend

# 步骤 5: 等待服务启动
echo ""
echo "📋 步骤 5: 等待服务启动 (30秒)..."
sleep 30

# 步骤 6: 验证服务状态
echo ""
echo "📋 步骤 6: 验证服务状态..."
docker-compose -f $APP_DIR/docker-compose.yml ps backend

# 步骤 7: 检查服务日志
echo ""
echo "📋 步骤 7: 检查服务日志..."
docker logs hephaestus_backend --tail 20

# 步骤 8: 测试 API 端点
echo ""
echo "📋 步骤 8: 测试 API 端点..."
curl -I http://localhost:8001/api/v1/health || echo "⚠️ API 测试失败，请检查日志"

echo ""
echo "================================================"
echo "✅ Docker 镜像重建完成！"
echo ""
echo "📊 后续验证命令:"
echo "  - 查看日志: docker logs hephaestus_backend -f"
echo "  - 检查状态: docker-compose ps"
echo "  - 测试 API: curl http://localhost:8001/api/v1/health"
echo ""