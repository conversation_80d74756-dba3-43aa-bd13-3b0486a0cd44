#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Load translation files
const enPath = path.join(__dirname, '../frontend/messages/en.json');
const zhPath = path.join(__dirname, '../frontend/messages/zh.json');

const en = JSON.parse(fs.readFileSync(enPath, 'utf8'));
const zh = JSON.parse(fs.readFileSync(zhPath, 'utf8'));

// Critical keys that were previously missing
const criticalKeys = [
  'common.email',
  'common.password',
  'common.appName',
  'common.login',
  'common.back',
  'auth.loginTitle',
  'auth.loginError',
  'monitoring.statusPage.allSystemsOperational',
  'monitoring.statusPage.healthScore',
  'monitoring.statusPage.lastUpdated'
];

console.log('🔍 Validating i18n translation files...\n');

let allValid = true;
const errors = [];

// Check each critical key
for (const keyPath of criticalKeys) {
  const keys = keyPath.split('.');
  
  // Check in English
  let enValue = en;
  let zhValue = zh;
  let enFound = true;
  let zhFound = true;
  
  for (const key of keys) {
    if (enValue && typeof enValue === 'object' && key in enValue) {
      enValue = enValue[key];
    } else {
      enFound = false;
      break;
    }
    
    if (zhValue && typeof zhValue === 'object' && key in zhValue) {
      zhValue = zhValue[key];
    } else {
      zhFound = false;
      break;
    }
  }
  
  if (!enFound) {
    errors.push(`❌ Missing in en.json: ${keyPath}`);
    allValid = false;
  }
  if (!zhFound) {
    errors.push(`❌ Missing in zh.json: ${keyPath}`);
    allValid = false;
  }
  
  if (enFound && zhFound) {
    console.log(`✅ ${keyPath}: OK`);
  }
}

// Check for duplicate keys (which was the root cause of the problem)
function findDuplicateKeys(obj, prefix = '') {
  const seen = new Set();
  const duplicates = [];
  
  for (const key in obj) {
    const fullKey = prefix ? `${prefix}.${key}` : key;
    if (seen.has(key)) {
      duplicates.push(fullKey);
    }
    seen.add(key);
  }
  
  return duplicates;
}

const enDuplicates = findDuplicateKeys(en);
const zhDuplicates = findDuplicateKeys(zh);

if (enDuplicates.length > 0) {
  errors.push(`❌ Duplicate keys in en.json: ${enDuplicates.join(', ')}`);
  allValid = false;
}

if (zhDuplicates.length > 0) {
  errors.push(`❌ Duplicate keys in zh.json: ${zhDuplicates.join(', ')}`);
  allValid = false;
}

// Summary
console.log('\n📊 Summary:');
console.log(`Total keys in en.json: ${Object.keys(en).length}`);
console.log(`Total keys in zh.json: ${Object.keys(zh).length}`);

if (allValid) {
  console.log('\n✅ All critical translation keys are present!');
  console.log('✅ No duplicate keys found!');
  process.exit(0);
} else {
  console.log('\n❌ Validation failed:');
  errors.forEach(error => console.log(error));
  process.exit(1);
}