#!/bin/bash

# Ollama Integration Test Script
# Tests the Ollama service integration in Docker environment

set -e

echo "🔍 Testing Ollama Service Integration..."
echo "========================================="

# Color codes for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check service
check_service() {
    local service=$1
    local check_cmd=$2
    
    echo -n "Checking $service... "
    if eval $check_cmd > /dev/null 2>&1; then
        echo -e "${GREEN}✓ Running${NC}"
        return 0
    else
        echo -e "${RED}✗ Not running${NC}"
        return 1
    fi
}

# Check if Ollama container is running
echo "1. Container Status:"
check_service "Ollama container" "docker ps | grep hephaestus_ollama"

# Check Ollama health endpoint from host
echo -e "\n2. Health Check (from host):"
if docker exec hephaestus_ollama curl -s http://localhost:11434/api/tags > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Ollama API is responsive${NC}"
else
    echo -e "${RED}✗ Ollama API is not responding${NC}"
fi

# Check network connectivity from backend container
echo -e "\n3. Network Connectivity (from backend):"
if docker exec hephaestus_backend curl -s http://ollama:11434/api/tags > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Backend can reach Ollama service${NC}"
else
    echo -e "${YELLOW}⚠ Backend cannot reach Ollama (may need to restart backend)${NC}"
fi

# Check if models are accessible
echo -e "\n4. Model Directory:"
if docker exec hephaestus_ollama ls /root/.ollama > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Model directory is mounted${NC}"
    
    # List available models
    echo -e "\n5. Available Models:"
    models=$(docker exec hephaestus_ollama curl -s http://localhost:11434/api/tags 2>/dev/null | python3 -c "import sys, json; data = json.load(sys.stdin); [print(f'   - {m[\"name\"]}') for m in data.get('models', [])]" 2>/dev/null || echo "   No models loaded yet")
    
    if [ "$models" = "   No models loaded yet" ]; then
        echo -e "${YELLOW}$models${NC}"
        echo -e "\n   To pull a model, run:"
        echo "   docker exec -it hephaestus_ollama ollama pull bge-m3"
    else
        echo "$models"
    fi
else
    echo -e "${RED}✗ Model directory not accessible${NC}"
fi

# Test embedding generation (if bge-m3 is available)
echo -e "\n6. Test Embedding Generation:"
if docker exec hephaestus_ollama ollama list 2>/dev/null | grep -q "bge-m3"; then
    echo "   Testing BGE-M3 embedding..."
    if docker exec hephaestus_ollama curl -s -X POST http://localhost:11434/api/embeddings \
        -H "Content-Type: application/json" \
        -d '{"model": "bge-m3", "prompt": "test"}' | grep -q "embedding"; then
        echo -e "${GREEN}   ✓ Embedding generation successful${NC}"
    else
        echo -e "${RED}   ✗ Embedding generation failed${NC}"
    fi
else
    echo -e "${YELLOW}   ⚠ BGE-M3 model not found. Pull it with:${NC}"
    echo "   docker exec -it hephaestus_ollama ollama pull bge-m3"
fi

echo -e "\n========================================="
echo "📊 Summary:"

# Final status
all_good=true
if ! docker ps | grep -q hephaestus_ollama; then
    all_good=false
    echo -e "${RED}• Ollama container is not running${NC}"
    echo "  Run: make up or docker-compose up -d ollama"
fi

if ! docker exec hephaestus_backend curl -s http://ollama:11434/api/tags > /dev/null 2>&1; then
    echo -e "${YELLOW}• Backend may need restart to connect to Ollama${NC}"
    echo "  Run: docker-compose restart backend"
fi

if $all_good && docker exec hephaestus_backend curl -s http://ollama:11434/api/tags > /dev/null 2>&1; then
    echo -e "${GREEN}✓ Ollama integration is fully operational!${NC}"
fi

echo -e "\n💡 Useful Commands:"
echo "• View logs: docker logs -f hephaestus_ollama"
echo "• Pull model: docker exec -it hephaestus_ollama ollama pull bge-m3"
echo "• List models: docker exec hephaestus_ollama ollama list"
echo "• Test from backend: docker exec hephaestus_backend curl http://ollama:11434/api/tags"