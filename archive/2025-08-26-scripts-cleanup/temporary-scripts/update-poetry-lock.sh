#!/bin/bash

# 使用 Docker 容器更新 poetry.lock 文件
# 解决主机没有安装 poetry 的问题

echo "🔧 使用 Docker 容器更新 poetry.lock..."

cd /home/<USER>/source_code/talent_forge_pro/app/backend

# 使用 Python 镜像运行 poetry lock
docker run --rm \
  --network host \
  -v $(pwd):/app \
  -w /app \
  python:3.12-slim bash -c "
    pip install poetry==1.8.3 && \
    poetry lock --no-update
  "

if [ $? -eq 0 ]; then
    echo "✅ poetry.lock 更新成功"
else
    echo "❌ poetry.lock 更新失败"
    exit 1
fi