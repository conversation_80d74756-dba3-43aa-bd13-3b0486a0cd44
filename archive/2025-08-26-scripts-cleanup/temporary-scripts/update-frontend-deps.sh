#!/bin/bash
# 前端依赖更新脚本
# 用于在 Docker 环境中更新前端依赖

set -e

echo "🔄 更新前端依赖..."

# 检查是否在项目根目录
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ 请在项目根目录 (包含 docker-compose.yml) 运行此脚本"
    exit 1
fi

# 在容器内执行 pnpm install
echo "📦 在前端容器内安装依赖..."
docker-compose exec frontend sh -c "cd /app && pnpm install"

echo "✅ 依赖更新完成！"
echo ""
echo "💡 提示："
echo "   - 如果添加了新的依赖，请确保已更新 package.json"
echo "   - 容器会自动热重载，无需重启"
echo "   - 如果遇到问题，可以尝试重启前端服务："
echo "     docker-compose restart frontend"