#!/bin/bash
# Script to fix Docker container startup issues
# This script handles common issues with container startup and port mapping

set -e

echo "🔧 Fixing Docker container startup issues..."

# Navigate to app directory
cd /home/<USER>/source_code/talent_forge_pro/app

# Step 1: Clean up any problematic containers
echo "📦 Cleaning up problematic containers..."
docker rm -f nginx 2>/dev/null || true
docker ps -a | grep -E "frontend|backend" | grep Exited | awk '{print $1}' | xargs -r docker rm -f 2>/dev/null || true

# Step 2: Rebuild containers if needed
echo "🔨 Rebuilding containers..."
docker-compose build --no-cache backend frontend

# Step 3: Start core services
echo "🚀 Starting core services..."
docker-compose up -d postgres redis minio ollama

# Wait for core services to be healthy
echo "⏳ Waiting for core services to be healthy..."
sleep 10

# Step 4: Start backend
echo "🔧 Starting backend service..."
docker-compose up -d backend

# Step 5: Start frontend
echo "🎨 Starting frontend service..."
docker-compose up -d frontend

# Step 6: Start worker services
echo "⚙️ Starting worker services..."
docker-compose up -d celery_worker

# Step 7: Start nginx with proper port mapping
echo "🌐 Starting nginx proxy with port mapping..."
docker run -d \
  --name nginx \
  --network hephaestus_network \
  -p 8088:88 \
  -v /home/<USER>/source_code/talent_forge_pro/app/configs/nginx/nginx.conf:/etc/nginx/nginx.conf:ro \
  -v /home/<USER>/source_code/talent_forge_pro/app/configs/nginx/conf.d:/etc/nginx/conf.d:ro \
  --restart unless-stopped \
  nginx:alpine

# Wait for nginx to start
sleep 5

# Step 8: Verify services
echo "✅ Verifying services..."
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

# Step 9: Test endpoints
echo "🧪 Testing endpoints..."
echo -n "Backend health: "
curl -s http://localhost:8088/health || echo "Failed"
echo ""
echo -n "API health: "
curl -s http://localhost:8088/api/v1/health || echo "Failed"
echo ""

echo "✨ Docker startup fix complete!"
echo "🌐 Application available at: http://localhost:8088"
echo "📖 API docs available at: http://localhost:8088/docs"