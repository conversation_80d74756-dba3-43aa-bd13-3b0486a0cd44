#!/usr/bin/env python3
"""
添加外网连通性检查到监控系统
防止Docker网络问题未被及时发现
"""

import asyncio
import httpx
from datetime import datetime

async def check_external_connectivity():
    """检查容器的外网连通性"""
    test_urls = [
        ("Google DNS", "https://dns.google/dns-query"),
        ("Cloudflare", "https://*******/dns-query"),
        ("DeepSeek API", "https://api.deepseek.com"),
    ]
    
    results = {
        "service": "external_network",
        "display_name": "External Network Connectivity",
        "timestamp": datetime.now().isoformat(),
        "checks": []
    }
    
    async with httpx.AsyncClient(timeout=5.0) as client:
        for name, url in test_urls:
            try:
                response = await client.head(url)
                results["checks"].append({
                    "target": name,
                    "url": url,
                    "status": "healthy",
                    "response_time": response.elapsed.total_seconds() * 1000,
                    "status_code": response.status_code
                })
            except httpx.TimeoutException:
                results["checks"].append({
                    "target": name,
                    "url": url,
                    "status": "timeout",
                    "error": "Connection timeout after 5 seconds"
                })
            except Exception as e:
                results["checks"].append({
                    "target": name,
                    "url": url,
                    "status": "error",
                    "error": str(e)
                })
    
    # 判断整体状态
    successful_checks = [c for c in results["checks"] if c["status"] == "healthy"]
    if len(successful_checks) == len(test_urls):
        results["overall_status"] = "healthy"
        results["message"] = "All external endpoints reachable"
    elif len(successful_checks) > 0:
        results["overall_status"] = "degraded"
        results["message"] = f"Only {len(successful_checks)}/{len(test_urls)} endpoints reachable"
    else:
        results["overall_status"] = "unhealthy"
        results["message"] = "No external endpoints reachable - possible NAT/iptables issue"
    
    return results


async def add_to_monitoring_service():
    """将外网检查集成到监控服务中"""
    
    code = '''
# Add this to app/backend/app/services/monitoring.py

async def check_external_network(self) -> ServiceHealth:
    """Check external network connectivity - critical for detecting Docker NAT issues"""
    try:
        test_urls = [
            ("https://dns.google", 2),  # Google DNS
            ("https://*******", 2),      # Cloudflare  
            ("https://api.deepseek.com", 3),  # DeepSeek API
        ]
        
        successful = 0
        total_time = 0
        errors = []
        
        async with httpx.AsyncClient() as client:
            for url, timeout in test_urls:
                try:
                    start = time.time()
                    response = await client.head(url, timeout=timeout)
                    elapsed = (time.time() - start) * 1000
                    total_time += elapsed
                    successful += 1
                except Exception as e:
                    errors.append(f"{url}: {str(e)}")
        
        if successful == len(test_urls):
            status = ServiceStatus.HEALTHY
            details = {"message": "All external endpoints reachable"}
        elif successful > 0:
            status = ServiceStatus.DEGRADED
            details = {"warning": f"Some endpoints unreachable", "errors": errors}
        else:
            status = ServiceStatus.UNHEALTHY
            details = {"error": "No external connectivity - Docker NAT issue?", "errors": errors}
        
        return ServiceHealth(
            name="external_network",
            displayName="External Network",
            status=status,
            responseTime=total_time / max(successful, 1),
            lastCheck=datetime.now(timezone.utc),
            details=ServiceDetails(**details)
        )
    except Exception as e:
        return self._create_error_health("external_network", "External Network", str(e))

# Add to DEFAULT_SERVICES list:
{
    "name": "external_network",
    "display_name": "External Network Connectivity",
    "check_function": "check_external_network",
    "critical": True,  # Critical because it indicates Docker networking issues
    "timeout": 10
}
'''
    
    print("📝 Integration code for monitoring service:")
    print("=" * 60)
    print(code)
    print("=" * 60)
    print("\n✅ Add the above code to monitoring.py to enable external network monitoring")


if __name__ == "__main__":
    print("🌐 Testing External Network Connectivity Check")
    print("=" * 60)
    
    # Run the connectivity check
    result = asyncio.run(check_external_connectivity())
    
    print(f"\nOverall Status: {result['overall_status']}")
    print(f"Message: {result['message']}")
    print("\nDetailed Results:")
    for check in result["checks"]:
        status_emoji = "✅" if check["status"] == "healthy" else "❌"
        print(f"  {status_emoji} {check['target']}: {check['status']}")
        if "error" in check:
            print(f"     Error: {check['error']}")
        elif "response_time" in check:
            print(f"     Response: {check['response_time']:.0f}ms")
    
    print("\n" + "=" * 60)
    asyncio.run(add_to_monitoring_service())