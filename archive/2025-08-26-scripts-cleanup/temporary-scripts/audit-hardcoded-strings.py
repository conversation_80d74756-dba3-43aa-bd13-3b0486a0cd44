#!/usr/bin/env python3
"""
Audit script to find hardcoded strings in API responses
Ensures zero hardcoded user-facing messages in backend code
"""
import re
import os
import sys
from pathlib import Path
from typing import List


def find_hardcoded_strings(directory: str) -> List[str]:
    """Find hardcoded strings in backend API code"""
    hardcoded_patterns = [
        r'raise\s+\w+\("([^"]+)"\)',  # Exception messages like raise unauthorized("message")
        r'HTTPException\([^)]*detail\s*=\s*"([^"]+)"',  # HTTP exceptions with string details
        r'return\s+.*["\'](?:error|message)["\']\s*:\s*"([^"]+)"',  # Response messages
        r'\.detail\s*=\s*"([^"]+)"',  # Direct detail assignment
    ]
    
    violations = []
    backend_dir = Path(directory)
    
    if not backend_dir.exists():
        print(f"❌ Directory {directory} does not exist")
        return violations
    
    # Focus on API and core modules where user-facing messages should not exist
    target_patterns = [
        "app/api/**/*.py",
        "app/core/exceptions.py",
        "app/services/**/*.py"
    ]
    
    python_files = []
    for pattern in target_patterns:
        python_files.extend(backend_dir.glob(pattern))
    
    for py_file in python_files:
        if not py_file.is_file():
            continue
            
        try:
            content = py_file.read_text(encoding='utf-8')
        except Exception as e:
            print(f"⚠️  Could not read {py_file}: {e}")
            continue
        
        for pattern in hardcoded_patterns:
            matches = re.finditer(pattern, content, re.MULTILINE)
            for match in matches:
                message = match.group(1) if match.groups() else match.group()
                
                # Skip error codes (they should be all caps with underscores)
                if re.match(r'^[A-Z_]+$', message):
                    continue
                
                # Skip generic programming messages (not user-facing)
                skip_messages = [
                    "Authentication failed",
                    "Access forbidden", 
                    "Bad request",
                    "Resource not found",
                    "Resource conflict",
                    "Validation failed",
                    "Internal server error"
                ]
                
                if message in skip_messages:
                    continue
                
                # Find line number
                line_num = content[:match.start()].count('\n') + 1
                violation = f"{py_file}:{line_num}: Hardcoded message: '{message}'"
                violations.append(violation)
    
    return violations


def main():
    """Main audit function"""
    if len(sys.argv) > 1:
        directory = sys.argv[1]
    else:
        # Default to backend directory
        script_dir = Path(__file__).parent
        directory = str(script_dir.parent / "backend" / "app")
    
    print(f"🔍 Auditing hardcoded strings in: {directory}")
    violations = find_hardcoded_strings(directory)
    
    if violations:
        print(f"\n❌ Found {len(violations)} hardcoded strings:")
        for violation in violations:
            print(f"  {violation}")
        print("\n💡 All user-facing messages should use error codes instead of hardcoded strings")
        sys.exit(1)
    else:
        print("✅ No hardcoded user-facing strings found")
        print("🎉 All error messages properly use error codes")
        sys.exit(0)


if __name__ == "__main__":
    main()