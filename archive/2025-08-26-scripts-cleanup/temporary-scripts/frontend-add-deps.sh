#\!/bin/bash
echo "=== 在Docker容器中安装前端新依赖 ==="
echo "正在重启前端容器并安装依赖..."
cd /home/<USER>/source_code/talent_forge_pro/app
docker-compose stop frontend
docker-compose rm -f frontend
docker-compose up -d frontend
echo ""
echo "等待容器启动并安装依赖..."
sleep 10
echo ""
echo "检查react-day-picker是否安装成功..."
docker exec hephaestus_frontend ls /app/node_modules/react-day-picker 2>/dev/null && echo "✅ react-day-picker安装成功！" || echo "❌ 安装失败，请检查日志"

