#!/bin/bash

# DeepSeek API集成验证脚本
# 用于快速验证DeepSeek服务是否正常工作

echo "======================================================================"
echo "🚀 TalentForge Pro - DeepSeek API验证"
echo "======================================================================"
echo ""

# 颜色定义
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查Docker服务
echo "🔍 检查Docker服务状态..."
if docker ps | grep -q hephaestus_backend; then
    echo -e "${GREEN}✅ 后端服务正在运行${NC}"
else
    echo -e "${RED}❌ 后端服务未运行，请先启动：make up${NC}"
    exit 1
fi

# 检查环境配置
echo ""
echo "🔧 检查DeepSeek配置..."
if docker exec hephaestus_backend printenv | grep -q "AI_PROVIDER=deepseek"; then
    echo -e "${GREEN}✅ AI_PROVIDER已设置为deepseek${NC}"
else
    echo -e "${YELLOW}⚠️  AI_PROVIDER未设置，正在配置...${NC}"
    docker exec hephaestus_backend bash -c 'echo "AI_PROVIDER=deepseek" >> /app/.env'
fi

# 运行简单的连接测试
echo ""
echo "📡 测试DeepSeek API连接..."
docker exec hephaestus_backend python -c "
import asyncio
from openai import AsyncOpenAI

async def test():
    client = AsyncOpenAI(
        api_key='sk-d0d71ef3211840feaf23aafeecc16afb',
        base_url='https://api.deepseek.com/v1'
    )
    try:
        response = await client.chat.completions.create(
            model='deepseek-chat',
            messages=[{'role': 'user', 'content': 'Hi'}],
            max_tokens=10
        )
        print('✅ DeepSeek API连接成功！')
        return True
    except Exception as e:
        print(f'❌ 连接失败: {e}')
        return False

asyncio.run(test())
" 2>/dev/null

if [ $? -eq 0 ]; then
    echo -e "${GREEN}API连接测试通过${NC}"
else
    echo -e "${RED}API连接测试失败${NC}"
fi

# 运行完整测试（可选）
echo ""
read -p "是否运行完整的集成测试？(y/n) " -n 1 -r
echo ""
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "运行完整测试..."
    docker exec hephaestus_backend python /app/test_deepseek_simple.py
fi

echo ""
echo "======================================================================"
echo "验证完成！"
echo ""
echo "📝 使用说明："
echo "1. 访问系统: http://localhost:8088"
echo "2. API文档: http://localhost:8088/api/docs"
echo "3. 查看日志: make logs"
echo "4. 停止服务: make down"
echo "======================================================================"