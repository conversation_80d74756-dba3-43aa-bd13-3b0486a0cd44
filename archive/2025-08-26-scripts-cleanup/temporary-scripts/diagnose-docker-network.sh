#!/bin/bash

# Docker网络诊断脚本
# 用于快速定位Docker容器网络问题

set -e

echo "🔍 Docker Network Diagnostic Tool"
echo "================================="
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试容器名称（可修改）
TEST_CONTAINER="hephaestus_backend"

# 1. 检查Docker服务状态
echo "1️⃣ Checking Docker service status..."
if systemctl is-active --quiet docker; then
    echo -e "${GREEN}✅ Docker service is running${NC}"
else
    echo -e "${RED}❌ Docker service is not running${NC}"
    exit 1
fi
echo ""

# 2. 检查IP转发
echo "2️⃣ Checking IP forwarding..."
IP_FORWARD=$(cat /proc/sys/net/ipv4/ip_forward)
if [ "$IP_FORWARD" = "1" ]; then
    echo -e "${GREEN}✅ IP forwarding is enabled${NC}"
else
    echo -e "${RED}❌ IP forwarding is disabled${NC}"
    echo "Fix: sudo sysctl net.ipv4.ip_forward=1"
fi
echo ""

# 3. 检查容器是否运行
echo "3️⃣ Checking if test container is running..."
if docker ps | grep -q "$TEST_CONTAINER"; then
    echo -e "${GREEN}✅ Container $TEST_CONTAINER is running${NC}"
else
    echo -e "${YELLOW}⚠️ Container $TEST_CONTAINER is not running${NC}"
    echo "Please start the container first or modify TEST_CONTAINER variable"
    exit 1
fi
echo ""

# 4. 测试DNS解析
echo "4️⃣ Testing DNS resolution in container..."
if docker exec "$TEST_CONTAINER" python -c "import socket; print(socket.gethostbyname('google.com'))" 2>/dev/null; then
    echo -e "${GREEN}✅ DNS resolution works${NC}"
else
    echo -e "${RED}❌ DNS resolution failed${NC}"
    echo "Check: docker exec $TEST_CONTAINER cat /etc/resolv.conf"
fi
echo ""

# 5. 测试容器间通信
echo "5️⃣ Testing container-to-container communication..."
# 尝试连接到Redis容器
if docker exec "$TEST_CONTAINER" python -c "import socket; s=socket.socket(); s.settimeout(2); s.connect(('redis', 6379)); print('Connected to Redis')" 2>/dev/null; then
    echo -e "${GREEN}✅ Container-to-container communication works${NC}"
else
    echo -e "${YELLOW}⚠️ Cannot connect to Redis container${NC}"
    echo "This might be normal if Redis is not running"
fi
echo ""

# 6. 测试外网连接
echo "6️⃣ Testing external network connectivity..."
if docker exec "$TEST_CONTAINER" curl -s --max-time 5 -I https://www.google.com > /dev/null 2>&1; then
    echo -e "${GREEN}✅ External network connectivity works${NC}"
else
    echo -e "${RED}❌ Cannot connect to external network${NC}"
    echo "This is likely an iptables/NAT issue"
fi
echo ""

# 7. 检查iptables NAT规则
echo "7️⃣ Checking iptables NAT rules..."
MASQ_COUNT=$(sudo iptables -t nat -L POSTROUTING -n | grep -c MASQUERADE || true)
if [ "$MASQ_COUNT" -gt 0 ]; then
    echo -e "${GREEN}✅ Found $MASQ_COUNT MASQUERADE rules${NC}"
    sudo iptables -t nat -L POSTROUTING -n | grep MASQUERADE
else
    echo -e "${RED}❌ No MASQUERADE rules found${NC}"
    echo "Docker NAT is broken!"
fi
echo ""

# 8. 检查Docker网络
echo "8️⃣ Checking Docker networks..."
docker network ls | grep -E "bridge|host|none|hephaestus"
echo ""

# 9. 检查Docker链
echo "9️⃣ Checking Docker iptables chains..."
DOCKER_CHAIN=$(sudo iptables -L -n | grep -c "Chain DOCKER" || true)
if [ "$DOCKER_CHAIN" -gt 0 ]; then
    echo -e "${GREEN}✅ Docker iptables chains exist${NC}"
else
    echo -e "${RED}❌ Docker iptables chains missing${NC}"
fi
echo ""

# 诊断结果总结
echo "📊 Diagnosis Summary:"
echo "===================="

# 判断问题类型
if docker exec "$TEST_CONTAINER" curl -s --max-time 5 -I https://www.google.com > /dev/null 2>&1; then
    echo -e "${GREEN}✅ No network issues detected${NC}"
else
    if [ "$MASQ_COUNT" -eq 0 ] || [ "$DOCKER_CHAIN" -eq 0 ]; then
        echo -e "${RED}❌ Docker iptables/NAT configuration is broken${NC}"
        echo ""
        echo "🔧 Recommended fix:"
        echo "  1. make down (or docker-compose down)"
        echo "  2. sudo systemctl restart docker"
        echo "  3. make up (or docker-compose up -d)"
    elif [ "$IP_FORWARD" != "1" ]; then
        echo -e "${RED}❌ IP forwarding is disabled${NC}"
        echo ""
        echo "🔧 Fix:"
        echo "  sudo sysctl net.ipv4.ip_forward=1"
        echo "  echo 'net.ipv4.ip_forward=1' | sudo tee -a /etc/sysctl.conf"
    else
        echo -e "${YELLOW}⚠️ Unknown network issue${NC}"
        echo "Please check firewall settings or network policies"
    fi
fi

echo ""
echo "✨ Diagnostic complete!"