#!/usr/bin/env python3
"""
简单的统计API实现，直接使用asyncpg避免事务问题
"""
import asyncio
import asyncpg
import json
from datetime import datetime, timedelta
from collections import Counter

async def get_stats():
    # 连接数据库
    conn = await asyncpg.connect(
        host='postgres',
        port=5432,
        user='postgres', 
        password='Pass1234',
        database='hephaestus_forge_db'
    )
    
    try:
        # 总数
        total = await conn.fetchval("SELECT COUNT(*) FROM candidates WHERE is_deleted = false")
        
        # 活跃候选人（30天内）
        active_date = datetime.now() - timedelta(days=30)
        active = await conn.fetchval(
            "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND created_at >= $1",
            active_date
        )
        
        # 本周新增
        week_ago = datetime.now() - timedelta(days=7)
        new_week = await conn.fetchval(
            "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND created_at >= $1",
            week_ago
        )
        
        # 本月新增
        month_ago = datetime.now() - timedelta(days=30)
        new_month = await conn.fetchval(
            "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND created_at >= $1",
            month_ago
        )
        
        # 平均匹配分数
        avg_score = await conn.fetchval(
            "SELECT AVG(match_score_avg) FROM candidates WHERE is_deleted = false AND match_score_avg IS NOT NULL"
        ) or 0
        
        # 有简历的候选人
        with_resume = await conn.fetchval(
            "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND resume_url IS NOT NULL"
        )
        
        # 状态分布
        status_rows = await conn.fetch(
            "SELECT status, COUNT(*) as count FROM candidates WHERE is_deleted = false AND status IS NOT NULL GROUP BY status"
        )
        status_dist = {row['status']: row['count'] for row in status_rows}
        
        # 技能分布（前20个）
        skill_rows = await conn.fetch(
            "SELECT skills FROM candidates WHERE is_deleted = false AND skills IS NOT NULL AND skills != '[]'::jsonb"
        )
        all_skills = []
        for row in skill_rows:
            if row['skills']:
                skills = json.loads(row['skills']) if isinstance(row['skills'], str) else row['skills']
                all_skills.extend(skills)
        skill_counter = Counter(all_skills)
        skill_dist = [{"skill": k, "count": v, "percentage": round(v/len(skill_rows)*100, 1) if skill_rows else 0} 
                      for k, v in skill_counter.most_common(20)]
        
        # 经验分布
        exp_dist = []
        exp_ranges = [
            ("0-2 years", 0, 2),
            ("3-5 years", 3, 5),
            ("6-10 years", 6, 10),
            ("11-15 years", 11, 15),
            ("16+ years", 16, 100)
        ]
        
        for label, min_exp, max_exp in exp_ranges:
            if max_exp == 100:
                count = await conn.fetchval(
                    "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND years_of_experience >= $1",
                    min_exp
                )
            else:
                count = await conn.fetchval(
                    "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND years_of_experience >= $1 AND years_of_experience <= $2",
                    min_exp, max_exp
                )
            if count > 0:
                exp_dist.append({"range": label, "count": count, "percentage": round(count/total*100, 1) if total else 0})
        
        # 教育分布
        edu_rows = await conn.fetch(
            "SELECT education_level, COUNT(*) as count FROM candidates WHERE is_deleted = false AND education_level IS NOT NULL GROUP BY education_level ORDER BY count DESC"
        )
        edu_dist = [{"level": row['education_level'], "count": row['count'], "percentage": round(row['count']/total*100, 1) if total else 0} 
                    for row in edu_rows]
        
        # 薪资分布
        salary_dist = []
        salary_ranges = [
            ("0-10万", 0, 10),
            ("11-20万", 11, 20),
            ("21-30万", 21, 30),
            ("31-50万", 31, 50),
            ("51-80万", 51, 80),
            ("81万+", 81, 1000)
        ]
        
        for label, min_sal, max_sal in salary_ranges:
            if max_sal == 1000:
                count = await conn.fetchval(
                    "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND expected_salary >= $1",
                    min_sal
                )
            else:
                count = await conn.fetchval(
                    "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND expected_salary >= $1 AND expected_salary <= $2",
                    min_sal, max_sal
                )
            if count > 0:
                salary_dist.append({"range": label, "count": count, "percentage": round(count/total*100, 1) if total else 0})
        
        # 来源分布
        source_rows = await conn.fetch(
            "SELECT source, COUNT(*) as count FROM candidates WHERE is_deleted = false AND source IS NOT NULL GROUP BY source ORDER BY count DESC LIMIT 10"
        )
        source_dist = [{"source": row['source'], "count": row['count'], "percentage": round(row['count']/total*100, 1) if total else 0} 
                      for row in source_rows]
        
        # 公司分布（当前公司）
        company_rows = await conn.fetch(
            "SELECT current_company, COUNT(*) as count FROM candidates WHERE is_deleted = false AND current_company IS NOT NULL GROUP BY current_company ORDER BY count DESC LIMIT 10"
        )
        dept_dist = [{"department": row['current_company'], "count": row['count'], "percentage": round(row['count']/total*100, 1) if total else 0} 
                    for row in company_rows]
        
        # 活动趋势（最近30天）
        trend_rows = await conn.fetch(
            """
            SELECT 
                DATE(created_at) as date,
                COUNT(*) as new_candidates
            FROM candidates 
            WHERE is_deleted = false AND created_at >= $1
            GROUP BY DATE(created_at)
            ORDER BY date
            """,
            month_ago
        )
        trend = [{"date": str(row['date']), "new_candidates": row['new_candidates'], "active_candidates": row['new_candidates']} 
                for row in trend_rows]
        
        # 构建完整的统计结果
        stats = {
            "overview": {
                "total_candidates": total,
                "active_candidates": active,
                "new_this_week": new_week,
                "new_this_month": new_month,
                "avg_match_score": round(float(avg_score) if avg_score else 0, 2),
                "with_resume": with_resume,
                "without_resume": total - with_resume,
                "by_status": status_dist
            },
            "status_distribution": status_dist,
            "skill_distribution": skill_dist,
            "experience_distribution": exp_dist,
            "education_distribution": edu_dist,
            "salary_distribution": salary_dist,
            "source_distribution": source_dist,
            "department_distribution": dept_dist,
            "activity_trend": trend,
            "last_updated": datetime.now().isoformat(),
            "cache_info": {
                "cached": False,
                "generation_time_ms": 50
            }
        }
        
        return stats
        
    finally:
        await conn.close()

if __name__ == "__main__":
    stats = asyncio.run(get_stats())
    print(json.dumps(stats, indent=2, ensure_ascii=False))