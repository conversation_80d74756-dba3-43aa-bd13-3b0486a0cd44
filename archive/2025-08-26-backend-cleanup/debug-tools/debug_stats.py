#!/usr/bin/env python3
"""
Debug script to test candidate statistics queries directly
"""
import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, '/home/<USER>/source_code/talent_forge_pro/app/backend')

from datetime import datetime, timezone
from sqlalchemy import select, func
from app.core.database import get_db
from app.models.candidate import Candidate
from app.models.user import User
from app.crud import user as user_crud

async def test_candidate_queries():
    """Test candidate queries directly"""
    print("🔍 Testing candidate statistics queries directly...")
    
    async for db in get_db():
        try:
            # Get admin user
            admin_user = await user_crud.get_by_email(db, email="<EMAIL>")
            if not admin_user:
                print("❌ Admin user not found")
                return
            
            print(f"✅ Found admin user: {admin_user.username} (ID: {admin_user.id})")
            
            # Test basic candidate count
            print("\n📊 Testing basic candidate count...")
            total_query = select(func.count(Candidate.id)).where(Candidate.is_deleted == False)
            result = await db.execute(total_query)
            total_count = result.scalar()
            print(f"Total candidates (is_deleted=false): {total_count}")
            
            # Test with additional filters
            print("\n📊 Testing with status filter...")
            from app.models.candidate import CandidateStatus
            status_query = select(func.count(Candidate.id)).where(
                (Candidate.is_deleted == False) &
                (Candidate.status.in_([CandidateStatus.NEW, CandidateStatus.SCREENING]))
            )
            result = await db.execute(status_query)
            status_count = result.scalar()
            print(f"Candidates with status NEW/SCREENING: {status_count}")
            
            # Sample some actual candidate data
            print("\n📋 Sample candidate data...")
            sample_query = select(
                Candidate.id, 
                Candidate.name, 
                Candidate.status, 
                Candidate.is_deleted,
                Candidate.created_by
            ).where(Candidate.is_deleted == False).limit(5)
            result = await db.execute(sample_query)
            candidates = result.fetchall()
            
            for candidate in candidates:
                print(f"  - ID: {candidate.id}, Name: {candidate.name}, Status: {candidate.status}, Created by: {candidate.created_by}")
            
            await db.commit()
            print("✅ All queries completed successfully!")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            await db.rollback()
        finally:
            await db.close()
            break

if __name__ == "__main__":
    print("🚀 Starting candidate statistics debug script...")
    asyncio.run(test_candidate_queries())