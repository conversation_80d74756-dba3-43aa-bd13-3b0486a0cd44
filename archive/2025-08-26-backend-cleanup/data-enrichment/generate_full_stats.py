#!/usr/bin/env python3
"""
直接生成完整的统计数据
"""
import asyncio
import asyncpg
import json
from datetime import datetime, timedelta
from collections import Counter

async def generate_stats():
    # 连接数据库
    conn = await asyncpg.connect(
        host='postgres',
        port=5432,
        user='postgres', 
        password='Pass1234',
        database='hephaestus_forge_db'
    )
    
    print("📊 生成完整的候选人统计数据...")
    
    # 总数
    total = await conn.fetchval("SELECT COUNT(*) FROM candidates WHERE is_deleted = false")
    
    # 活跃候选人（30天内）
    active_date = datetime.now() - timedelta(days=30)
    active = await conn.fetchval(
        "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND last_active_at >= $1",
        active_date
    )
    
    # 本周新增
    week_ago = datetime.now() - timedelta(days=7)
    new_week = await conn.fetchval(
        "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND created_at >= $1",
        week_ago
    )
    
    # 本月新增
    month_ago = datetime.now() - timedelta(days=30)
    new_month = await conn.fetchval(
        "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND created_at >= $1",
        month_ago
    )
    
    # 平均匹配分数
    avg_score = await conn.fetchval(
        "SELECT AVG(match_score_avg) FROM candidates WHERE is_deleted = false AND match_score_avg IS NOT NULL"
    ) or 0
    
    # 有简历的候选人
    with_resume = await conn.fetchval(
        "SELECT COUNT(*) FROM candidates WHERE is_deleted = false AND resume_url IS NOT NULL"
    )
    
    # 状态分布
    status_rows = await conn.fetch(
        "SELECT status, COUNT(*) as count FROM candidates WHERE is_deleted = false AND status IS NOT NULL GROUP BY status"
    )
    status_dist = {row['status']: row['count'] for row in status_rows}
    
    # 技能分布
    skill_rows = await conn.fetch(
        "SELECT skills FROM candidates WHERE is_deleted = false AND skills IS NOT NULL"
    )
    all_skills = []
    for row in skill_rows:
        if row['skills']:
            skills = json.loads(row['skills']) if isinstance(row['skills'], str) else row['skills']
            all_skills.extend(skills)
    skill_counter = Counter(all_skills)
    skill_dist = [{"skill": k, "count": v} for k, v in skill_counter.most_common(20)]
    
    # 经验分布
    exp_rows = await conn.fetch(
        """
        SELECT 
            CASE 
                WHEN years_of_experience = 0 THEN '应届生'
                WHEN years_of_experience BETWEEN 1 AND 2 THEN '1-2年'
                WHEN years_of_experience BETWEEN 3 AND 5 THEN '3-5年'
                WHEN years_of_experience BETWEEN 6 AND 10 THEN '6-10年'
                ELSE '10年以上'
            END as range,
            COUNT(*) as count
        FROM candidates 
        WHERE is_deleted = false AND years_of_experience IS NOT NULL
        GROUP BY range
        """
    )
    exp_dist = [{"range": row['range'], "count": row['count']} for row in exp_rows]
    
    # 教育分布
    edu_rows = await conn.fetch(
        "SELECT education_level, COUNT(*) as count FROM candidates WHERE is_deleted = false AND education_level IS NOT NULL GROUP BY education_level"
    )
    edu_dist = [{"level": row['education_level'], "count": row['count']} for row in edu_rows]
    
    # 薪资分布
    salary_rows = await conn.fetch(
        """
        SELECT 
            CASE 
                WHEN expected_salary < 10 THEN '<10万'
                WHEN expected_salary BETWEEN 10 AND 20 THEN '10-20万'
                WHEN expected_salary BETWEEN 20 AND 30 THEN '20-30万'
                WHEN expected_salary BETWEEN 30 AND 50 THEN '30-50万'
                ELSE '50万以上'
            END as range,
            COUNT(*) as count
        FROM candidates 
        WHERE is_deleted = false AND expected_salary IS NOT NULL
        GROUP BY range
        """
    )
    salary_dist = [{"range": row['range'], "count": row['count']} for row in salary_rows]
    
    # 来源分布
    source_rows = await conn.fetch(
        "SELECT source, COUNT(*) as count FROM candidates WHERE is_deleted = false AND source IS NOT NULL GROUP BY source ORDER BY count DESC LIMIT 10"
    )
    source_dist = [{"source": row['source'], "count": row['count']} for row in source_rows]
    
    # 公司分布
    company_rows = await conn.fetch(
        "SELECT current_company, COUNT(*) as count FROM candidates WHERE is_deleted = false AND current_company IS NOT NULL GROUP BY current_company ORDER BY count DESC LIMIT 10"
    )
    dept_dist = [{"department": row['current_company'], "count": row['count']} for row in company_rows]
    
    # 活动趋势（最近30天）
    trend_rows = await conn.fetch(
        """
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as new_candidates
        FROM candidates 
        WHERE is_deleted = false AND created_at >= $1
        GROUP BY DATE(created_at)
        ORDER BY date
        """,
        month_ago
    )
    trend = [{"date": str(row['date']), "new": row['new_candidates']} for row in trend_rows]
    
    await conn.close()
    
    # 构建完整的统计结果
    stats = {
        "overview": {
            "total_candidates": total,
            "active_candidates": active,
            "new_this_week": new_week,
            "new_this_month": new_month,
            "avg_match_score": round(float(avg_score) if avg_score else 0, 2),
            "with_resume": with_resume,
            "without_resume": total - with_resume,
            "by_status": status_dist
        },
        "status_distribution": status_dist,
        "skill_distribution": skill_dist,
        "experience_distribution": exp_dist,
        "education_distribution": edu_dist,
        "salary_distribution": salary_dist,
        "source_distribution": source_dist,
        "department_distribution": dept_dist,
        "activity_trend": trend,
        "last_updated": datetime.now().isoformat(),
        "cache_info": {
            "cached": False,
            "generation_time_ms": 50
        }
    }
    
    print("\n✅ 统计数据生成完成！")
    print(json.dumps(stats, indent=2, ensure_ascii=False))
    return stats

if __name__ == "__main__":
    asyncio.run(generate_stats())