#!/usr/bin/env python3
"""
使用智能测试数据生成器插入候选人数据到数据库
配合 smart_test_data_generator.py 使用
"""
import asyncio
import json
import sys
import os
from datetime import datetime, timedelta, timezone
from pathlib import Path

# 添加项目路径
sys.path.insert(0, '/home/<USER>/source_code/talent_forge_pro/app/backend')

from app.core.database import get_db
from app.models.candidate import Candidate, CandidateStatus
from app.models.user import User
from sqlalchemy import select
from smart_test_data_generator import SmartTestDataGenerator

async def insert_candidates_from_generator():
    """使用智能生成器创建候选人并插入数据库"""
    print("🚀 开始使用智能数据生成器插入候选人...")
    
    # 初始化生成器
    generator = SmartTestDataGenerator(output_dir="temp/generated_candidates")
    
    # 获取数据库会话
    async for db in get_db():
        try:
            # 获取默认用户作为创建者
            admin_user = await db.execute(
                select(User).where(User.email == "<EMAIL>")
            )
            admin_user = admin_user.scalar_one_or_none()
            
            if not admin_user:
                print("❌ 未找到admin用户，请先创建admin用户")
                return
            
            # 定义要生成的候选人配置
            candidate_configs = [
                # 初级开发者
                {"level": "junior", "industry": "互联网", "tech_stack": "前端", "count": 3},
                {"level": "junior", "industry": "互联网", "tech_stack": "后端", "count": 2},
                
                # 中级开发者
                {"level": "mid", "industry": "金融科技", "tech_stack": "全栈", "count": 3},
                {"level": "mid", "industry": "人工智能", "tech_stack": "AI/ML", "count": 2},
                
                # 高级开发者
                {"level": "senior", "industry": "互联网", "tech_stack": "后端", "count": 3},
                {"level": "senior", "industry": "电商", "tech_stack": "DevOps", "count": 2},
                
                # 专家级
                {"level": "expert", "industry": "人工智能", "tech_stack": "AI/ML", "count": 2},
                {"level": "expert", "industry": "金融科技", "tech_stack": "后端", "count": 1},
            ]
            
            total_created = 0
            status_cycle = [
                CandidateStatus.NEW,
                CandidateStatus.NEW,
                CandidateStatus.SCREENING,
                CandidateStatus.SCREENING,
                CandidateStatus.INTERVIEW,
                CandidateStatus.INTERVIEW,
                CandidateStatus.OFFER,
                CandidateStatus.HIRED,
                CandidateStatus.REJECTED,
                CandidateStatus.WITHDRAWN
            ]
            status_index = 0
            
            for config in candidate_configs:
                print(f"\n📋 生成 {config['count']} 个 {config['level']} 级别的 {config['tech_stack']} 候选人 ({config['industry']}行业)...")
                
                for i in range(config['count']):
                    # 使用生成器生成候选人数据
                    resume_data = generator.generate_resume_data(
                        level=config['level'],
                        industry=config['industry'],
                        tech_stack=config['tech_stack']
                    )
                    
                    # 创建候选人对象
                    candidate = Candidate(
                        # 基本信息
                        name=resume_data['name'],
                        email=resume_data['email'],
                        phone=resume_data['phone'],
                        gender=resume_data.get('gender'),
                        birth_date=datetime.now(timezone.utc) - timedelta(days=resume_data.get('age', 25) * 365),
                        
                        # 专业信息
                        current_position=resume_data.get('current_position'),
                        current_company=resume_data.get('current_company'),
                        years_of_experience=resume_data.get('experience_years', 0),
                        
                        # 薪资信息
                        current_salary=resume_data.get('current_salary', 0) // 1000,  # 转换为万/年
                        expected_salary=resume_data.get('expected_salary', 0) // 1000,
                        expected_salary_min=(resume_data.get('expected_salary', 0) // 1000) * 0.9,
                        expected_salary_max=(resume_data.get('expected_salary', 0) // 1000) * 1.2,
                        
                        # 教育背景
                        education_level=resume_data.get('education', {}).get('degree', '本科'),
                        education=[resume_data.get('education')] if resume_data.get('education') else [],
                        
                        # 工作经验
                        work_experience=resume_data.get('work_experience', []),
                        
                        # 技能
                        skills=resume_data.get('skills', []),
                        
                        # 项目经验
                        project_experience=resume_data.get('projects', []),
                        
                        # 简历信息
                        resume_url=f"https://minio.talentforge.com/resumes/{total_created + 1}.pdf",
                        resume_text=resume_data.get('summary', ''),
                        resume_parsed_at=datetime.now(timezone.utc) - timedelta(days=i),
                        
                        # 评分信息（部分候选人有评分）
                        dci_score=round(75 + (config['level'] == 'expert') * 15 + i * 1.5, 1) if i % 2 == 0 else None,
                        jfs_score=round(70 + (config['level'] == 'expert') * 20 + i * 2, 1) if i % 2 == 0 else None,
                        
                        # 状态
                        status=status_cycle[status_index % len(status_cycle)],
                        
                        # 来源
                        source=resume_data.get('location', '智能生成'),
                        
                        # 标签
                        tags=['优秀候选人', config['level'], config['tech_stack']],
                        
                        # 备注
                        notes=f"通过智能生成器创建的{config['level']}级{config['tech_stack']}候选人",
                        
                        # 活跃时间
                        last_active_at=datetime.now(timezone.utc) - timedelta(days=(status_index % 30)),
                        
                        # 元数据
                        created_by_id=admin_user.id,
                        created_at=datetime.now(timezone.utc) - timedelta(days=(status_index % 60)),
                        updated_at=datetime.now(timezone.utc)
                    )
                    
                    # 如果状态是面试及以后，设置面试次数
                    if candidate.status in [CandidateStatus.INTERVIEW, CandidateStatus.OFFER, CandidateStatus.HIRED]:
                        candidate.interview_count = (status_index % 3) + 1
                    
                    # 设置评估数据（部分候选人）
                    if candidate.dci_score:
                        candidate.assessment_data = {
                            "digital_literacy": round(candidate.dci_score * 0.9, 1),
                            "industry_skills": round(candidate.dci_score * 0.95, 1),
                            "job_skills": round(candidate.jfs_score * 1.0, 1),
                            "innovation": round(candidate.dci_score * 0.85, 1),
                            "learning_potential": round(candidate.jfs_score * 0.9, 1),
                            "assessment_date": (datetime.now(timezone.utc) - timedelta(days=i)).isoformat()
                        }
                        candidate.match_score_avg = round((candidate.dci_score + candidate.jfs_score) / 2, 1)
                    
                    # 保存到数据库
                    db.add(candidate)
                    total_created += 1
                    status_index += 1
                    
                    print(f"  ✅ 创建候选人: {candidate.name} - {candidate.current_position} @ {candidate.current_company}")
            
            # 提交所有更改
            await db.commit()
            
            print(f"\n🎉 成功创建 {total_created} 个候选人!")
            
            # 显示统计信息
            print("\n📊 数据统计:")
            
            # 查询各状态的候选人数量
            for status in CandidateStatus:
                count_result = await db.execute(
                    select(Candidate).where(
                        Candidate.status == status,
                        Candidate.is_deleted == False
                    )
                )
                count = len(count_result.scalars().all())
                if count > 0:
                    print(f"  - {status.value}: {count} 人")
            
            # 查询有评分的候选人
            scored_result = await db.execute(
                select(Candidate).where(
                    Candidate.dci_score.isnot(None),
                    Candidate.is_deleted == False
                )
            )
            scored_count = len(scored_result.scalars().all())
            print(f"  - 有评分记录: {scored_count} 人")
            
            # 查询活跃候选人（30天内）
            active_date = datetime.now(timezone.utc) - timedelta(days=30)
            active_result = await db.execute(
                select(Candidate).where(
                    Candidate.last_active_at >= active_date,
                    Candidate.is_deleted == False
                )
            )
            active_count = len(active_result.scalars().all())
            print(f"  - 30天内活跃: {active_count} 人")
            
            # 查询本周创建的候选人
            week_ago = datetime.now(timezone.utc) - timedelta(days=7)
            week_result = await db.execute(
                select(Candidate).where(
                    Candidate.created_at >= week_ago,
                    Candidate.is_deleted == False
                )
            )
            week_count = len(week_result.scalars().all())
            print(f"  - 本周新增: {week_count} 人")
            
            print("\n✅ 数据插入完成！现在统计API应该能显示完整的数据了。")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
            await db.rollback()
            import traceback
            print(traceback.format_exc())
        finally:
            await db.close()
            break

if __name__ == "__main__":
    print("=" * 60)
    print("🎯 TalentForge Pro - 智能候选人数据插入")
    print("=" * 60)
    print("使用 smart_test_data_generator.py 生成真实的候选人数据")
    print("-" * 60)
    asyncio.run(insert_candidates_from_generator())