#!/usr/bin/env python3
"""
Direct SQL script to enrich candidates
"""
import asyncio
import random
import json
from datetime import datetime, timedelta, timezone
import asyncpg

# Skills pool
SKILLS = ["Python", "Java", "JavaScript", "React", "Docker", "AWS", "MySQL", "Git", "微服务", "算法"]
EDUCATION = ["本科", "硕士", "博士", "专科"]
SOURCES = ["BOSS直聘", "猎聘网", "内部推荐", "LinkedIn"]
COMPANIES = ["阿里巴巴", "腾讯", "字节跳动", "百度", "华为", "美团"]
POSITIONS = ["软件工程师", "高级工程师", "技术经理", "架构师"]

async def enrich():
    conn = await asyncpg.connect(
        host='localhost',
        port=54320,
        user='postgres',
        password='your_secure_password_here',
        database='hephaestus_forge_db'
    )
    
    # Get candidates
    candidates = await conn.fetch("SELECT id, name FROM candidates WHERE is_deleted = false")
    print(f"Found {len(candidates)} candidates")
    
    for i, row in enumerate(candidates, 1):
        cid = row['id']
        name = row['name']
        print(f"Enriching {i}/{len(candidates)}: {name}")
        
        # Generate data
        years = random.randint(1, 15)
        salary = 10 + years * 3 + random.randint(-5, 15)
        
        education = [{
            "school": "清华大学",
            "major": "计算机",
            "degree": random.choice(EDUCATION),
            "start_date": "2015-09",
            "end_date": "2019-07"
        }]
        
        work_exp = [{
            "company": random.choice(COMPANIES),
            "position": random.choice(POSITIONS),
            "start_date": "2019-07",
            "end_date": "至今",
            "description": "开发工作"
        }]
        
        # Update candidate
        await conn.execute("""
            UPDATE candidates SET
                phone = $1,
                current_position = $2,
                current_company = $3,
                years_of_experience = $4,
                current_salary = $5,
                expected_salary = $6,
                education_level = $7,
                education = $8,
                work_experience = $9,
                skills = $10,
                source = $11,
                last_active_at = $12,
                dci_score = $13,
                jfs_score = $14,
                match_score_avg = $15
            WHERE id = $16
        """,
            f"1{random.randint(30,99)}{random.randint(10000000,99999999)}",
            random.choice(POSITIONS),
            random.choice(COMPANIES),
            years,
            salary,
            int(salary * 1.3),
            random.choice(EDUCATION),
            json.dumps(education),
            json.dumps(work_exp),
            json.dumps(random.sample(SKILLS, 5)),
            random.choice(SOURCES),
            datetime.now(timezone.utc) - timedelta(days=random.randint(0, 60)),
            round(random.uniform(60, 95), 1),
            round(random.uniform(65, 98), 1),
            round(random.uniform(70, 90), 1),
            cid
        )
    
    # Update statuses for variety
    statuses = ['new', 'screening', 'interview', 'offer', 'hired', 'rejected']
    for i, row in enumerate(candidates):
        if i < len(statuses):
            await conn.execute(
                "UPDATE candidates SET status = $1 WHERE id = $2",
                statuses[i], row['id']
            )
    
    await conn.close()
    print("✅ Done!")

if __name__ == "__main__":
    asyncio.run(enrich())