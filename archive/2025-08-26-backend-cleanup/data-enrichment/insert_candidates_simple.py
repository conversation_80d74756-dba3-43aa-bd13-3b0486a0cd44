#!/usr/bin/env python3
"""
简化版本的候选人数据插入脚本，不依赖faker库
"""
import asyncio
import random
import json
from datetime import datetime, timedelta, timezone
import sys
import os

# 添加项目路径
sys.path.insert(0, '/app/backend' if os.path.exists('/.dockerenv') else '../app/backend')

from app.core.database import get_db
from app.models.candidate import Candidate, CandidateStatus
from app.models.user import User
from sqlalchemy import select

# 名字池
FIRST_NAMES = ["张", "李", "王", "刘", "陈", "杨", "黄", "赵", "周", "吴", "徐", "孙", "马", "朱", "胡"]
LAST_NAMES = ["伟", "芳", "娜", "敏", "静", "强", "磊", "洋", "勇", "艳", "军", "杰", "涛", "明", "超"]

# 公司池
COMPANIES = [
    "阿里巴巴", "腾讯", "字节跳动", "美团", "滴滴", "小米", "华为", "百度",
    "网易", "京东", "拼多多", "快手", "B站", "携程", "爱奇艺", "商汤科技",
    "旷视科技", "依图科技", "云从科技", "第四范式", "明略科技"
]

# 职位池
POSITIONS = [
    "软件工程师", "高级软件工程师", "技术经理", "架构师", "前端工程师",
    "后端工程师", "全栈工程师", "数据工程师", "算法工程师", "测试工程师",
    "DevOps工程师", "产品经理", "项目经理", "技术专家", "研发总监"
]

# 技能池
SKILLS = [
    "Python", "Java", "JavaScript", "TypeScript", "React", "Vue.js", "Angular",
    "Node.js", "Django", "FastAPI", "Spring Boot", "Docker", "Kubernetes",
    "AWS", "Azure", "MySQL", "PostgreSQL", "MongoDB", "Redis", "Elasticsearch",
    "机器学习", "深度学习", "数据分析", "产品管理", "项目管理", "敏捷开发",
    "Git", "CI/CD", "微服务", "系统架构", "算法", "数据结构", "Golang", "Rust"
]

# 学校池
SCHOOLS = [
    "清华大学", "北京大学", "复旦大学", "上海交通大学", 
    "浙江大学", "南京大学", "中山大学", "武汉大学",
    "哈尔滨工业大学", "西安交通大学", "北京理工大学", "同济大学"
]

# 专业池
MAJORS = [
    "计算机科学与技术", "软件工程", "信息管理与信息系统", 
    "电子工程", "自动化", "数学与应用数学", "物理学", "通信工程"
]

# 来源池
SOURCES = ["智联招聘", "前程无忧", "BOSS直聘", "猎聘网", "内部推荐", "校园招聘", "LinkedIn", "脉脉"]

def generate_name():
    """生成随机中文名字"""
    return random.choice(FIRST_NAMES) + random.choice(LAST_NAMES) + random.choice(["", random.choice(LAST_NAMES)])

def generate_email(name, index):
    """生成邮箱"""
    domains = ["qq.com", "163.com", "gmail.com", "outlook.com", "126.com"]
    prefix = f"candidate{index:03d}"
    return f"{prefix}@{random.choice(domains)}"

def generate_phone():
    """生成手机号"""
    prefixes = ["130", "131", "132", "133", "134", "135", "136", "137", "138", "139",
                "150", "151", "152", "153", "155", "156", "157", "158", "159",
                "180", "181", "182", "183", "185", "186", "187", "188", "189"]
    return f"{random.choice(prefixes)}{random.randint(10000000, 99999999)}"

async def insert_test_candidates():
    """插入测试候选人数据"""
    print("🚀 开始插入测试候选人数据...")
    
    async for db in get_db():
        try:
            # 获取admin用户
            admin_user = await db.execute(
                select(User).where(User.email == "<EMAIL>")
            )
            admin_user = admin_user.scalar_one_or_none()
            
            if not admin_user:
                print("❌ 未找到admin用户")
                return
            
            # 状态分布
            status_distribution = [
                CandidateStatus.NEW,        # 1-3
                CandidateStatus.NEW,
                CandidateStatus.NEW,
                CandidateStatus.SCREENING,  # 4-6
                CandidateStatus.SCREENING,
                CandidateStatus.SCREENING,
                CandidateStatus.INTERVIEW,  # 7-9
                CandidateStatus.INTERVIEW,
                CandidateStatus.INTERVIEW,
                CandidateStatus.OFFER,      # 10-11
                CandidateStatus.OFFER,
                CandidateStatus.HIRED,      # 12-13
                CandidateStatus.HIRED,
                CandidateStatus.REJECTED,   # 14-15
                CandidateStatus.REJECTED,
                CandidateStatus.WITHDRAWN,  # 16-18
                CandidateStatus.WITHDRAWN,
                CandidateStatus.NEW,        # 额外的
            ]
            
            num_candidates = 18
            
            for i in range(num_candidates):
                # 生成基本信息
                name = generate_name()
                email = generate_email(name, i + 1)
                phone = generate_phone()
                gender = random.choice(["男", "女"])
                age = random.randint(22, 45)
                birth_date = datetime.now(timezone.utc) - timedelta(days=age * 365)
                
                # 工作经验（基于年龄）
                years_of_experience = min(age - 22, random.randint(0, 20))
                
                # 当前职位和公司
                current_position = random.choice(POSITIONS)
                current_company = random.choice(COMPANIES)
                
                # 薪资（基于经验）
                base_salary = 10 + years_of_experience * 2.5 + random.randint(-5, 15)
                current_salary = max(8, int(base_salary))
                expected_salary = int(current_salary * random.uniform(1.2, 1.5))
                expected_salary_min = int(current_salary * 1.1)
                expected_salary_max = int(current_salary * 1.6)
                
                # 教育背景
                education_level = random.choice(["本科", "硕士", "博士", "专科"])
                education = [{
                    "school": random.choice(SCHOOLS),
                    "major": random.choice(MAJORS),
                    "degree": education_level,
                    "start_date": f"{2024 - years_of_experience - 4}-09",
                    "end_date": f"{2024 - years_of_experience}-07"
                }]
                
                # 工作经验
                work_experience = []
                if years_of_experience > 0:
                    num_jobs = min(3, max(1, years_of_experience // 3))
                    for j in range(num_jobs):
                        is_current = (j == 0)
                        work_experience.append({
                            "company": current_company if is_current else random.choice(COMPANIES),
                            "position": current_position if is_current else random.choice(POSITIONS),
                            "department": random.choice(["技术部", "研发部", "产品技术部", "创新业务部"]),
                            "start_date": f"{2024 - years_of_experience + j * 2}-01",
                            "end_date": "至今" if is_current else f"{2024 - years_of_experience + j * 2 + 2}-12",
                            "description": f"负责核心系统的开发和维护，使用微服务架构，提升系统性能30%"
                        })
                
                # 技能
                num_skills = min(12, 5 + years_of_experience // 2)
                skills = random.sample(SKILLS, num_skills)
                
                # 项目经验
                projects = []
                for j in range(min(3, max(1, years_of_experience // 2))):
                    projects.append({
                        "name": f"{random.choice(['电商', '金融', '社交', '教育'])}平台{random.choice(['重构', '优化', '开发'])}项目",
                        "role": random.choice(["技术负责人", "核心开发", "项目经理"]),
                        "duration": f"{random.randint(3, 12)}个月",
                        "description": "使用微服务架构，实现高并发处理",
                        "technologies": random.sample(skills, min(4, len(skills)))
                    })
                
                # 状态
                status = status_distribution[i] if i < len(status_distribution) else CandidateStatus.NEW
                
                # 评分（60%的候选人有评分）
                has_score = random.random() < 0.6 or i < 10
                dci_score = round(random.uniform(65, 95), 1) if has_score else None
                jfs_score = round(random.uniform(60, 98), 1) if has_score else None
                match_score_avg = round((dci_score + jfs_score) / 2, 1) if has_score else None
                
                # 面试次数
                interview_count = 0
                if status in [CandidateStatus.INTERVIEW, CandidateStatus.OFFER, CandidateStatus.HIRED]:
                    interview_count = random.randint(1, 4)
                
                # 活跃时间
                if i < 5:  # 本周活跃
                    last_active_at = datetime.now(timezone.utc) - timedelta(days=random.randint(0, 6))
                elif i < 10:  # 本月活跃
                    last_active_at = datetime.now(timezone.utc) - timedelta(days=random.randint(7, 29))
                else:  # 更早
                    last_active_at = datetime.now(timezone.utc) - timedelta(days=random.randint(30, 90))
                
                # 创建时间
                if i < 3:  # 本周创建
                    created_at = datetime.now(timezone.utc) - timedelta(days=random.randint(0, 6))
                elif i < 6:  # 本月创建
                    created_at = datetime.now(timezone.utc) - timedelta(days=random.randint(7, 29))
                else:  # 更早创建
                    created_at = datetime.now(timezone.utc) - timedelta(days=random.randint(30, 180))
                
                # 创建候选人对象
                candidate = Candidate(
                    name=name,
                    email=email,
                    phone=phone,
                    gender=gender,
                    birth_date=birth_date,
                    current_position=current_position,
                    current_company=current_company,
                    years_of_experience=years_of_experience,
                    current_salary=current_salary,
                    expected_salary=expected_salary,
                    expected_salary_min=expected_salary_min,
                    expected_salary_max=expected_salary_max,
                    education_level=education_level,
                    education=education,
                    work_experience=work_experience,
                    skills=skills,
                    project_experience=projects,
                    resume_url=f"https://minio.talentforge.com/resumes/{i+1}.pdf" if random.random() < 0.7 else None,
                    resume_text=f"{name}的简历内容，{years_of_experience}年经验的{current_position}",
                    resume_parsed_at=datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30)) if random.random() < 0.7 else None,
                    dci_score=dci_score,
                    jfs_score=jfs_score,
                    match_score_avg=match_score_avg,
                    status=status,
                    interview_count=interview_count,
                    source=random.choice(SOURCES),
                    source_channel=f"channel_{random.randint(1, 5)}",
                    tags=random.sample(["优秀", "潜力股", "经验丰富", "技术扎实", "沟通能力强"], random.randint(1, 3)),
                    notes=f"候选人评价：技术{random.choice(['优秀', '良好', '扎实'])}，{random.choice(['强烈推荐', '推荐', '可以考虑'])}",
                    last_active_at=last_active_at,
                    created_by_id=admin_user.id,
                    created_at=created_at,
                    updated_at=datetime.now(timezone.utc)
                )
                
                # 评估数据
                if has_score:
                    candidate.assessment_data = {
                        "digital_literacy": round(random.uniform(70, 95), 1),
                        "industry_skills": round(random.uniform(65, 90), 1),
                        "job_skills": round(random.uniform(60, 95), 1),
                        "innovation": round(random.uniform(55, 85), 1),
                        "learning_potential": round(random.uniform(70, 98), 1),
                        "assessment_date": (datetime.now(timezone.utc) - timedelta(days=random.randint(1, 20))).isoformat()
                    }
                
                db.add(candidate)
                print(f"✅ 创建候选人 {i+1}/{num_candidates}: {name} - {current_position} @ {current_company}")
            
            # 提交所有更改
            await db.commit()
            
            print(f"\n🎉 成功创建 {num_candidates} 个候选人!")
            
            # 显示统计
            print("\n📊 数据统计:")
            
            # 统计各状态数量
            for status in CandidateStatus:
                result = await db.execute(
                    select(Candidate).where(
                        Candidate.status == status,
                        Candidate.is_deleted == False
                    )
                )
                count = len(result.scalars().all())
                if count > 0:
                    print(f"  - {status.value}: {count} 人")
            
            # 统计有评分的候选人
            scored_result = await db.execute(
                select(Candidate).where(
                    Candidate.dci_score.isnot(None),
                    Candidate.is_deleted == False
                )
            )
            print(f"  - 有评分记录: {len(scored_result.scalars().all())} 人")
            
            # 统计30天内活跃
            active_date = datetime.now(timezone.utc) - timedelta(days=30)
            active_result = await db.execute(
                select(Candidate).where(
                    Candidate.last_active_at >= active_date,
                    Candidate.is_deleted == False
                )
            )
            print(f"  - 30天内活跃: {len(active_result.scalars().all())} 人")
            
            # 统计本周新增
            week_ago = datetime.now(timezone.utc) - timedelta(days=7)
            week_result = await db.execute(
                select(Candidate).where(
                    Candidate.created_at >= week_ago,
                    Candidate.is_deleted == False
                )
            )
            print(f"  - 本周新增: {len(week_result.scalars().all())} 人")
            
            print("\n✅ 数据插入完成！现在可以测试统计API了。")
            
        except Exception as e:
            print(f"❌ 错误: {e}")
            await db.rollback()
            import traceback
            print(traceback.format_exc())
        finally:
            await db.close()
            break

if __name__ == "__main__":
    print("=" * 60)
    print("🎯 TalentForge Pro - 测试候选人数据插入（简化版）")
    print("=" * 60)
    asyncio.run(insert_test_candidates())