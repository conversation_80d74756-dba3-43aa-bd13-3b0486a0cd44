#!/usr/bin/env python3
"""
Complete script to enrich all candidates with full fake data
"""
import asyncio
import random
import json
from datetime import datetime, timedelta, timezone
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, text
from app.core.database import get_db
from app.models.candidate import Candidate, CandidateStatus

# Data pools
SKILLS_POOL = [
    "Python", "Java", "JavaScript", "TypeScript", "React", "Vue.js", "Angular",
    "Node.js", "Django", "FastAPI", "Spring Boot", "Docker", "Kubernetes",
    "AWS", "Azure", "MySQL", "PostgreSQL", "MongoDB", "Redis", "Elasticsearch",
    "机器学习", "深度学习", "数据分析", "产品管理", "项目管理", "敏捷开发",
    "Git", "CI/CD", "微服务", "系统架构", "算法", "数据结构", "Golang", "Rust"
]

EDUCATION_LEVELS = ["高中", "专科", "本科", "硕士", "博士"]

SOURCES = ["智联招聘", "前程无忧", "BOSS直聘", "猎聘网", "内部推荐", "校园招聘", "LinkedIn", "脉脉"]

COMPANIES = [
    "阿里巴巴", "腾讯", "字节跳动", "百度", "华为", "美团", "京东", "滴滴",
    "小米", "网易", "快手", "拼多多", "B站", "携程", "爱奇艺", "微软中国",
    "谷歌中国", "IBM", "Oracle", "SAP", "Amazon", "Apple"
]

POSITIONS = [
    "软件工程师", "高级软件工程师", "技术经理", "架构师", "前端工程师",
    "后端工程师", "全栈工程师", "数据工程师", "算法工程师", "测试工程师",
    "DevOps工程师", "产品经理", "项目经理", "技术总监", "资深工程师"
]

SCHOOLS = [
    "清华大学", "北京大学", "复旦大学", "上海交通大学", 
    "浙江大学", "南京大学", "中山大学", "武汉大学",
    "哈尔滨工业大学", "西安交通大学", "北京理工大学", "同济大学"
]

MAJORS = [
    "计算机科学与技术", "软件工程", "信息管理与信息系统", 
    "电子工程", "自动化", "数学与应用数学", "物理学", "通信工程"
]

async def enrich_all_candidates():
    """Enrich all existing candidates with complete fake data"""
    print("🚀 Starting complete candidate enrichment...")
    
    async for db in get_db():
        try:
            # Get all non-deleted candidates
            query = select(Candidate).where(Candidate.is_deleted == False)
            result = await db.execute(query)
            candidates = result.scalars().all()
            
            print(f"📊 Found {len(candidates)} candidates to enrich")
            
            # Define status distribution (for 12 candidates)
            status_distribution = [
                CandidateStatus.NEW,        # 1-3
                CandidateStatus.NEW,
                CandidateStatus.NEW,
                CandidateStatus.SCREENING,  # 4-6
                CandidateStatus.SCREENING,
                CandidateStatus.SCREENING,
                CandidateStatus.INTERVIEW,  # 7-8
                CandidateStatus.INTERVIEW,
                CandidateStatus.OFFER,      # 9
                CandidateStatus.HIRED,      # 10
                CandidateStatus.REJECTED,   # 11
                CandidateStatus.WITHDRAWN,  # 12
            ]
            
            for i, candidate in enumerate(candidates, 1):
                print(f"\n🔄 Processing candidate {i}/{len(candidates)}: {candidate.name}")
                
                # Basic info
                if not candidate.email:
                    candidate.email = f"candidate{i}@example.com"
                if not candidate.phone:
                    candidate.phone = f"1{random.choice([3,5,7,8])}{random.randint(*********, *********)}"
                if not candidate.gender:
                    candidate.gender = random.choice(["男", "女"])
                if not candidate.birth_date:
                    age = random.randint(23, 45)
                    candidate.birth_date = datetime.now(timezone.utc) - timedelta(days=age*365)
                
                # Professional info
                candidate.current_position = random.choice(POSITIONS)
                candidate.current_company = random.choice(COMPANIES)
                candidate.years_of_experience = random.randint(1, 20)
                
                # Salary (in 万/year) - based on experience
                base_salary = 15 + (candidate.years_of_experience * 2.5) + random.randint(-10, 20)
                candidate.current_salary = max(10, int(base_salary))
                candidate.expected_salary = int(candidate.current_salary * random.uniform(1.2, 1.5))
                candidate.expected_salary_min = int(candidate.current_salary * 1.1)
                candidate.expected_salary_max = int(candidate.current_salary * 1.6)
                
                # Education
                edu_level = random.choice(EDUCATION_LEVELS)
                candidate.education_level = edu_level
                
                education_list = []
                if edu_level in ["本科", "硕士", "博士"]:
                    # Bachelor degree
                    bach_start = 2024 - candidate.years_of_experience - 4
                    education_list.append({
                        "school": random.choice(SCHOOLS),
                        "major": random.choice(MAJORS),
                        "degree": "本科",
                        "start_date": f"{bach_start}-09",
                        "end_date": f"{bach_start + 4}-07"
                    })
                    
                    if edu_level in ["硕士", "博士"]:
                        # Master degree
                        master_start = bach_start + 4
                        education_list.append({
                            "school": random.choice(SCHOOLS[:6]),  # Top schools for graduate
                            "major": random.choice(MAJORS[:3]),
                            "degree": "硕士",
                            "start_date": f"{master_start}-09",
                            "end_date": f"{master_start + 3}-07"
                        })
                        
                        if edu_level == "博士":
                            # PhD
                            phd_start = master_start + 3
                            education_list.append({
                                "school": random.choice(SCHOOLS[:3]),  # Top schools for PhD
                                "major": random.choice(MAJORS[:2]),
                                "degree": "博士",
                                "start_date": f"{phd_start}-09",
                                "end_date": f"{phd_start + 4}-07"
                            })
                
                candidate.education = education_list if education_list else [{
                    "school": "职业技术学院",
                    "major": "计算机应用",
                    "degree": edu_level,
                    "start_date": "2015-09",
                    "end_date": "2018-07"
                }]
                
                # Work experience - create realistic career progression
                work_experience = []
                remaining_years = candidate.years_of_experience
                current_year = 2024
                job_count = min(4, max(1, candidate.years_of_experience // 3))
                
                for j in range(job_count):
                    years_in_job = min(remaining_years, random.randint(1, 5))
                    if years_in_job <= 0:
                        break
                        
                    is_current = (j == 0)
                    start_year = current_year - remaining_years
                    end_year = start_year + years_in_job if not is_current else current_year
                    
                    work_experience.append({
                        "company": candidate.current_company if is_current else random.choice(COMPANIES),
                        "position": candidate.current_position if is_current else random.choice(POSITIONS),
                        "department": random.choice(["技术部", "研发部", "产品技术部", "创新业务部", "基础架构部"]),
                        "start_date": f"{start_year}-{random.randint(1,12):02d}",
                        "end_date": "至今" if is_current else f"{end_year}-{random.randint(1,12):02d}",
                        "description": f"负责公司{random.choice(['核心', '创新', '基础', '前端', '后端'])}系统的"
                                     f"{random.choice(['架构设计', '开发实现', '性能优化', '技术升级', '团队管理'])}，"
                                     f"使用{random.choice(['微服务', '分布式', '云原生', '大数据', 'AI'])}技术，"
                                     f"提升系统{random.choice(['性能', '稳定性', '可扩展性', '用户体验'])} "
                                     f"{random.randint(20, 80)}%"
                    })
                    
                    remaining_years -= years_in_job
                
                candidate.work_experience = work_experience
                
                # Skills - more skills for experienced candidates
                num_skills = min(15, 5 + candidate.years_of_experience // 2)
                candidate.skills = random.sample(SKILLS_POOL, num_skills)
                
                # Resume URL and text (70% have resumes)
                if random.random() < 0.7:
                    candidate.resume_url = f"https://minio.talentforge.com/resumes/{candidate.id}.pdf"
                    candidate.resume_text = f"""
                    {candidate.name} - {candidate.current_position}
                    
                    工作经验：{candidate.years_of_experience}年
                    当前公司：{candidate.current_company}
                    教育背景：{candidate.education_level}
                    
                    技能专长：{', '.join(candidate.skills[:8])}
                    
                    项目经验：
                    1. 主导开发高并发分布式系统，日活用户超过1000万
                    2. 优化核心算法，提升系统性能50%
                    3. 带领团队完成微服务架构升级
                    
                    个人优势：
                    - 扎实的技术功底，熟悉主流技术栈
                    - 优秀的问题解决能力和创新思维
                    - 良好的团队协作和沟通能力
                    """
                    candidate.resume_parsed_at = datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30))
                
                # Assessment scores (60% have been assessed)
                if random.random() < 0.6 or i <= 8:  # Ensure first 8 have scores
                    candidate.dci_score = round(random.uniform(65, 95), 1)
                    candidate.jfs_score = round(random.uniform(60, 98), 1)
                    candidate.match_score_avg = round((candidate.dci_score + candidate.jfs_score) / 2, 1)
                    
                    # Assessment data
                    candidate.assessment_data = {
                        "digital_literacy": round(random.uniform(70, 95), 1),
                        "industry_skills": round(random.uniform(65, 90), 1),
                        "job_skills": round(random.uniform(60, 95), 1),
                        "innovation": round(random.uniform(55, 85), 1),
                        "learning_potential": round(random.uniform(70, 98), 1),
                        "assessment_date": (datetime.now(timezone.utc) - timedelta(days=random.randint(1, 20))).isoformat()
                    }
                
                # Status assignment
                if i <= len(status_distribution):
                    candidate.status = status_distribution[i-1]
                else:
                    candidate.status = random.choice(list(CandidateStatus))
                
                # Interview count based on status
                if candidate.status in [CandidateStatus.INTERVIEW, CandidateStatus.OFFER, CandidateStatus.HIRED]:
                    candidate.interview_count = random.randint(1, 4)
                else:
                    candidate.interview_count = 0
                
                # Activity - distribute across time ranges
                if i <= 3:  # Recent activity (this week)
                    days_ago = random.randint(0, 6)
                elif i <= 6:  # This month
                    days_ago = random.randint(7, 29)
                elif i <= 9:  # Active in last 30 days
                    days_ago = random.randint(15, 29)
                else:  # Older
                    days_ago = random.randint(30, 90)
                    
                candidate.last_active_at = datetime.now(timezone.utc) - timedelta(days=days_ago)
                
                # Creation date - some recent, some older
                if i <= 2:  # Created this week
                    candidate.created_at = datetime.now(timezone.utc) - timedelta(days=random.randint(0, 6))
                elif i <= 5:  # Created this month
                    candidate.created_at = datetime.now(timezone.utc) - timedelta(days=random.randint(7, 29))
                
                # Source and referrer
                candidate.source = random.choice(SOURCES)
                candidate.source_channel = f"{candidate.source}_channel_{random.randint(1, 5)}"
                
                # Tags
                possible_tags = [
                    "优秀", "潜力股", "经验丰富", "技术扎实", "沟通能力强",
                    "团队合作", "创新思维", "快速学习", "项目经验丰富", "行业专家",
                    "领导力强", "执行力强", "高潜力", "核心人才"
                ]
                candidate.tags = random.sample(possible_tags, random.randint(2, 5))
                
                # Notes
                candidate.notes = f"""
                候选人评价：
                - 技术能力：{random.choice(['优秀', '良好', '扎实'])}
                - 沟通表达：{random.choice(['清晰流畅', '表达清楚', '有条理'])}
                - 团队协作：{random.choice(['配合度高', '有团队精神', '协作能力强'])}
                - 发展潜力：{random.choice(['高', '中高', '有潜力'])}
                
                面试官建议：{random.choice(['强烈推荐', '推荐', '可以考虑', '待定'])}
                """
                
                print(f"  ✅ {candidate.current_position} @ {candidate.current_company}")
                print(f"  💰 {candidate.current_salary}万 → {candidate.expected_salary}万")
                print(f"  📊 Status: {candidate.status.value}, DCI: {candidate.dci_score}")
                print(f"  🎓 {candidate.education_level}, {candidate.years_of_experience}年经验")
            
            # Commit all changes
            await db.commit()
            print(f"\n✅ Successfully enriched all {len(candidates)} candidates with complete data!")
            
            # Show summary
            print("\n📊 Data Summary:")
            print(f"  - Total candidates: {len(candidates)}")
            print(f"  - With scores: {sum(1 for c in candidates if c.dci_score)}")
            print(f"  - With resumes: {sum(1 for c in candidates if c.resume_url)}")
            print(f"  - Active (30 days): {sum(1 for c in candidates if c.last_active_at and (datetime.now(timezone.utc) - c.last_active_at).days <= 30)}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            await db.rollback()
            import traceback
            print(traceback.format_exc())
        finally:
            await db.close()
            break

if __name__ == "__main__":
    print("=" * 60)
    print("🎯 Complete Candidate Data Enrichment Script")
    print("=" * 60)
    asyncio.run(enrich_all_candidates())