#!/usr/bin/env python3
"""
Simple script to enrich existing candidates with fake data (no external dependencies)
"""
import asyncio
import random
import json
from datetime import datetime, timedelta, timezone
import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, '/home/<USER>/source_code/talent_forge_pro/app/backend')

from app.core.database import get_db
from app.models.candidate import Candidate, CandidateStatus
from sqlalchemy import select

# Skills pool
SKILLS_POOL = [
    "Python", "Java", "JavaScript", "TypeScript", "React", "Vue.js", "Angular",
    "Node.js", "Django", "FastAPI", "Spring Boot", "Docker", "Kubernetes",
    "AWS", "Azure", "MySQL", "PostgreSQL", "MongoDB", "Redis", "Elasticsearch",
    "机器学习", "深度学习", "数据分析", "产品管理", "项目管理", "敏捷开发",
    "Git", "CI/CD", "微服务", "系统架构", "算法", "数据结构"
]

# Education levels
EDUCATION_LEVELS = ["高中", "专科", "本科", "硕士", "博士"]

# Sources
SOURCES = ["智联招聘", "前程无忧", "BOSS直聘", "猎聘网", "内部推荐", "校园招聘", "LinkedIn", "脉脉"]

# Companies
COMPANIES = [
    "阿里巴巴", "腾讯", "字节跳动", "百度", "华为", "美团", "京东", "滴滴",
    "小米", "网易", "快手", "拼多多", "B站", "携程", "爱奇艺", "微软中国"
]

# Positions
POSITIONS = [
    "软件工程师", "高级软件工程师", "技术经理", "架构师", "前端工程师",
    "后端工程师", "全栈工程师", "数据工程师", "算法工程师", "测试工程师"
]

# Names for fake data
FIRST_NAMES = ["张", "李", "王", "刘", "陈", "杨", "黄", "赵", "周", "吴"]
LAST_NAMES = ["伟", "芳", "娜", "敏", "静", "强", "磊", "洋", "勇", "艳"]

async def enrich_candidates():
    """Enrich existing candidates with fake data"""
    print("🚀 Starting candidate enrichment (simple version)...")
    
    async for db in get_db():
        try:
            # Get all non-deleted candidates
            query = select(Candidate).where(Candidate.is_deleted == False)
            result = await db.execute(query)
            candidates = result.scalars().all()
            
            print(f"📊 Found {len(candidates)} candidates to enrich")
            
            for i, candidate in enumerate(candidates, 1):
                print(f"\n🔄 Enriching candidate {i}/{len(candidates)}: {candidate.name}")
                
                # Update basic info if missing
                if not candidate.email:
                    candidate.email = f"user{random.randint(1000,9999)}@example.com"
                if not candidate.phone:
                    candidate.phone = f"1{random.randint(3,9)}{random.randint(*********,*********)}"
                if not candidate.gender:
                    candidate.gender = random.choice(["男", "女"])
                
                # Professional info
                candidate.current_position = random.choice(POSITIONS)
                candidate.current_company = random.choice(COMPANIES)
                candidate.years_of_experience = random.randint(1, 15)
                
                # Salary (in 万/year)
                base_salary = 10 + candidate.years_of_experience * 3 + random.randint(-5, 15)
                candidate.current_salary = base_salary
                candidate.expected_salary = int(base_salary * 1.3)
                candidate.expected_salary_min = int(base_salary * 1.1)
                candidate.expected_salary_max = int(base_salary * 1.5)
                
                # Education
                edu_level = random.choice(EDUCATION_LEVELS)
                candidate.education_level = edu_level
                candidate.education = [{
                    "school": "北京大学" if edu_level in ["硕士", "博士"] else "清华大学",
                    "major": "计算机科学与技术",
                    "degree": edu_level,
                    "start_date": f"{2024 - candidate.years_of_experience - 4}-09",
                    "end_date": f"{2024 - candidate.years_of_experience}-07"
                }]
                
                # Work experience
                candidate.work_experience = [{
                    "company": candidate.current_company,
                    "position": candidate.current_position,
                    "department": "技术部",
                    "start_date": f"{2024 - candidate.years_of_experience}-01",
                    "end_date": "至今",
                    "description": "负责核心系统的开发和维护工作"
                }]
                
                # Skills - random 5-10 skills
                num_skills = random.randint(5, 10)
                candidate.skills = random.sample(SKILLS_POOL, num_skills)
                
                # Resume and assessment
                if random.random() > 0.3:  # 70% have resume
                    candidate.resume_url = f"https://minio.example.com/resumes/{candidate.id}.pdf"
                    candidate.resume_text = f"{candidate.name}的简历内容..."
                    candidate.resume_parsed_at = datetime.now(timezone.utc) - timedelta(days=random.randint(1, 30))
                
                # Scores
                if random.random() > 0.4:  # 60% have scores
                    candidate.dci_score = round(random.uniform(60, 95), 1)
                    candidate.jfs_score = round(random.uniform(65, 98), 1)
                    candidate.match_score_avg = round((candidate.dci_score + candidate.jfs_score) / 2, 1)
                
                # Interview count
                if candidate.status in [CandidateStatus.INTERVIEW, CandidateStatus.OFFER, CandidateStatus.HIRED]:
                    candidate.interview_count = random.randint(1, 4)
                else:
                    candidate.interview_count = 0
                
                # Activity - make some candidates active in last 30 days
                if i <= 8:  # Make 8 candidates active
                    days_ago = random.randint(0, 29)
                else:
                    days_ago = random.randint(30, 90)
                candidate.last_active_at = datetime.now(timezone.utc) - timedelta(days=days_ago)
                
                # Status distribution
                statuses = [
                    CandidateStatus.NEW,
                    CandidateStatus.NEW,
                    CandidateStatus.NEW,
                    CandidateStatus.SCREENING,
                    CandidateStatus.SCREENING,
                    CandidateStatus.SCREENING,
                    CandidateStatus.INTERVIEW,
                    CandidateStatus.INTERVIEW,
                    CandidateStatus.OFFER,
                    CandidateStatus.HIRED,
                    CandidateStatus.REJECTED,
                    CandidateStatus.WITHDRAWN
                ]
                candidate.status = statuses[i-1] if i <= len(statuses) else random.choice(list(CandidateStatus))
                
                # Source
                candidate.source = random.choice(SOURCES)
                
                # Tags
                candidate.tags = random.sample([
                    "优秀", "潜力股", "经验丰富", "技术扎实", "沟通能力强"
                ], random.randint(1, 3))
                
                # Notes
                candidate.notes = f"候选人备注信息"
                
                # Make some candidates created this week/month
                if i <= 3:  # 3 candidates created this week
                    candidate.created_at = datetime.now(timezone.utc) - timedelta(days=random.randint(0, 6))
                elif i <= 6:  # 3 more created this month
                    candidate.created_at = datetime.now(timezone.utc) - timedelta(days=random.randint(7, 29))
                
                print(f"  ✅ {candidate.current_position} @ {candidate.current_company}")
                print(f"  💰 Salary: {candidate.current_salary}万 → {candidate.expected_salary}万")
                print(f"  📊 Status: {candidate.status.value}")
            
            # Commit all changes
            await db.commit()
            print(f"\n✅ Successfully enriched {len(candidates)} candidates!")
            
        except Exception as e:
            print(f"❌ Error: {e}")
            await db.rollback()
            import traceback
            print(traceback.format_exc())
        finally:
            await db.close()
            break

if __name__ == "__main__":
    print("🚀 Candidate Data Enrichment Script")
    print("📝 This will add fake data to existing candidates")
    print("-" * 50)
    asyncio.run(enrich_candidates())