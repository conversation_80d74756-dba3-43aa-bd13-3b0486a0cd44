#!/usr/bin/env python3
"""
修复候选人数据中缺失的字段
"""
import asyncio
import asyncpg

async def fix_candidates():
    # 连接数据库
    conn = await asyncpg.connect(
        host='postgres',
        port=5432,
        user='postgres', 
        password='Pass1234',
        database='hephaestus_forge_db'
    )
    
    print("🔧 修复候选人数据中缺失的字段...")
    
    # 更新所有缺失data_permission的记录
    result = await conn.execute("""
        UPDATE candidates 
        SET data_permission = 'PUBLIC',
            shared_with = '[]'::jsonb
        WHERE data_permission IS NULL
    """)
    
    print(f"✅ 已修复 {result.split()[-1]} 条记录")
    
    # 验证修复结果
    count = await conn.fetchval("""
        SELECT COUNT(*) FROM candidates 
        WHERE data_permission IS NULL OR shared_with IS NULL
    """)
    
    if count == 0:
        print("✅ 所有记录已修复完成！")
    else:
        print(f"⚠️ 仍有 {count} 条记录未修复")
    
    await conn.close()

if __name__ == "__main__":
    asyncio.run(fix_candidates())