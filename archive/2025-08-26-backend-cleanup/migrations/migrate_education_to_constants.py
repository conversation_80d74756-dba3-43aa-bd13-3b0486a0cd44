#!/usr/bin/env python3
"""
Migrate education_level from Chinese to constants
将教育水平从中文迁移到常量代码
"""
import asyncio
import asyncpg
from datetime import datetime

# Mapping from Chinese to constants
EDUCATION_MIGRATION_MAP = {
    "高中": "high_school",
    "专科": "associate",
    "大专": "associate",  # 大专也是associate
    "本科": "bachelor",
    "硕士": "master", 
    "博士": "doctorate",
    "其他": "other"
}

async def migrate_education_levels():
    """Migrate education levels from Chinese to constants"""
    
    # Connect to database
    conn = await asyncpg.connect(
        host='postgres',
        port=5432,
        user='postgres',
        password='Pass1234',
        database='hephaestus_forge_db'
    )
    
    try:
        print("Starting education level migration...")
        print("=" * 50)
        
        # 1. Check current data
        print("\n1. Current education_level values:")
        current_values = await conn.fetch(
            "SELECT DISTINCT education_level, COUNT(*) as count FROM candidates "
            "WHERE education_level IS NOT NULL GROUP BY education_level ORDER BY count DESC"
        )
        
        for row in current_values:
            print(f"  {row['education_level']}: {row['count']} records")
        
        # 2. Perform migration
        print("\n2. Starting migration...")
        total_updated = 0
        
        for chinese, constant in EDUCATION_MIGRATION_MAP.items():
            result = await conn.execute(
                "UPDATE candidates SET education_level = $1, updated_at = $2 "
                "WHERE education_level = $3",
                constant, datetime.utcnow(), chinese
            )
            
            # Extract count from result
            count = int(result.split()[-1]) if result else 0
            if count > 0:
                print(f"  Updated {count} records: '{chinese}' → '{constant}'")
                total_updated += count
        
        # 3. Verify migration
        print(f"\n3. Migration complete! Total records updated: {total_updated}")
        print("\n4. New education_level values:")
        
        new_values = await conn.fetch(
            "SELECT DISTINCT education_level, COUNT(*) as count FROM candidates "
            "WHERE education_level IS NOT NULL GROUP BY education_level ORDER BY count DESC"
        )
        
        for row in new_values:
            print(f"  {row['education_level']}: {row['count']} records")
        
        # 4. Also update the education JSON field (if it contains degree in Chinese)
        print("\n5. Updating education JSON fields...")
        
        # Get all candidates with education data
        candidates = await conn.fetch(
            "SELECT id, education FROM candidates WHERE education IS NOT NULL AND education != '[]'"
        )
        
        import json
        updated_json_count = 0
        
        for candidate in candidates:
            education_data = json.loads(candidate['education'])
            updated = False
            
            for edu in education_data:
                if 'degree' in edu and edu['degree'] in EDUCATION_MIGRATION_MAP:
                    edu['degree'] = EDUCATION_MIGRATION_MAP[edu['degree']]
                    updated = True
            
            if updated:
                await conn.execute(
                    "UPDATE candidates SET education = $1, updated_at = $2 WHERE id = $3",
                    json.dumps(education_data, ensure_ascii=False),
                    datetime.utcnow(),
                    candidate['id']
                )
                updated_json_count += 1
        
        print(f"  Updated {updated_json_count} education JSON records")
        
        print("\n✅ Migration completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error during migration: {e}")
        raise
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(migrate_education_levels())