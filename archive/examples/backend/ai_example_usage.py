"""
AI Service Manager Usage Examples
Demonstrates how to use the enhanced AIServiceManager with all service methods
"""
import asyncio
import logging
from typing import List, Dict, Any
from app.services.ai_service_manager import ai_service_manager

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def example_text_generation():
    """Example of text generation with different providers"""
    print("\n=== Text Generation Examples ===")
    
    # Basic text generation
    try:
        response = await ai_service_manager.generate_text(
            messages="Explain Python programming in one sentence.",
            max_tokens=100
        )
        print(f"Generated text: {response}")
    except Exception as e:
        print(f"Text generation failed: {e}")
    
    # Text generation with specific provider
    try:
        response = await ai_service_manager.generate_text(
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "What is machine learning?"}
            ],
            provider="deepseek",
            temperature=0.3,
            max_tokens=150
        )
        print(f"Generated text (DeepSeek): {response}")
    except Exception as e:
        print(f"Text generation with DeepSeek failed: {e}")


async def example_embedding_generation():
    """Example of embedding generation with caching"""
    print("\n=== Embedding Generation Examples ===")
    
    # Basic embedding generation
    try:
        text = "Natural language processing is a field of artificial intelligence."
        embedding = await ai_service_manager.generate_embedding(text)
        print(f"Embedding dimension: {len(embedding)}")
        print(f"First 5 values: {embedding[:5]}")
    except Exception as e:
        print(f"Embedding generation failed: {e}")
    
    # Test caching with same text
    try:
        text = "Natural language processing is a field of artificial intelligence."
        embedding2 = await ai_service_manager.generate_embedding(text)
        print(f"Second call (should be cached): {len(embedding2)} dimensions")
    except Exception as e:
        print(f"Cached embedding generation failed: {e}")
    
    # Different provider
    try:
        embedding3 = await ai_service_manager.generate_embedding(
            "Another text to embed",
            provider="ollama"
        )
        print(f"Ollama embedding dimension: {len(embedding3)}")
    except Exception as e:
        print(f"Ollama embedding generation failed: {e}")


async def example_document_reranking():
    """Example of document reranking with both native and fallback methods"""
    print("\n=== Document Reranking Examples ===")
    
    query = "Python web development framework"
    documents = [
        "Django is a high-level Python web framework that encourages rapid development.",
        "React is a JavaScript library for building user interfaces.",
        "FastAPI is a modern, fast Python web framework for building APIs.",
        "Vue.js is a progressive JavaScript framework.",
        "Flask is a lightweight Python web application framework.",
        "Angular is a platform for building mobile and desktop web applications."
    ]
    
    # Basic reranking
    try:
        results = await ai_service_manager.rerank_documents(
            query=query,
            documents=documents,
            top_k=3
        )
        
        print(f"Top 3 reranked documents:")
        for i, result in enumerate(results, 1):
            print(f"{i}. Score: {result['score']:.4f}")
            print(f"   Document: {result['document'][:80]}...")
            print()
    except Exception as e:
        print(f"Document reranking failed: {e}")
    
    # Reranking with specific provider
    try:
        results2 = await ai_service_manager.rerank_documents(
            query="Machine learning in Python",
            documents=[
                "Scikit-learn is a Python machine learning library",
                "TensorFlow is Google's machine learning framework", 
                "JavaScript is used for web development",
                "PyTorch is a deep learning framework"
            ],
            provider="openrouter",
            top_k=2
        )
        
        print(f"OpenRouter reranking results:")
        for result in results2:
            print(f"Score: {result['score']:.4f} - {result['document'][:50]}...")
    except Exception as e:
        print(f"OpenRouter reranking failed: {e}")


async def example_provider_switching():
    """Example of runtime provider switching"""
    print("\n=== Provider Switching Examples ===")
    
    # Show current providers
    print(f"Current LLM provider: {ai_service_manager.llm_provider}")
    print(f"Current embedding provider: {ai_service_manager.embedding_provider}")
    print(f"Current rerank provider: {ai_service_manager.rerank_provider}")
    
    # Switch providers temporarily
    original_llm = ai_service_manager.llm_provider
    original_embedding = ai_service_manager.embedding_provider
    
    try:
        # Switch to different providers
        ai_service_manager.llm_provider = "ollama"
        ai_service_manager.embedding_provider = "deepseek"
        
        print(f"\nAfter switching:")
        print(f"New LLM provider: {ai_service_manager.llm_provider}")
        print(f"New embedding provider: {ai_service_manager.embedding_provider}")
        
        # Test with new providers
        text_result = await ai_service_manager.generate_text(
            "Hello from switched provider!",
            max_tokens=20
        )
        print(f"Text from new provider: {text_result}")
        
        embed_result = await ai_service_manager.generate_embedding(
            "Embedding from switched provider"
        )
        print(f"Embedding dimension from new provider: {len(embed_result)}")
        
    except Exception as e:
        print(f"Provider switching test failed: {e}")
    finally:
        # Restore original providers
        ai_service_manager.llm_provider = original_llm
        ai_service_manager.embedding_provider = original_embedding
        print(f"\nRestored to original providers")


async def example_health_checking():
    """Example of comprehensive health checking"""
    print("\n=== Health Checking Examples ===")
    
    # Check all providers health
    try:
        health_status = await ai_service_manager.check_all_providers_health()
        print(f"Overall health status: {health_status['status']}")
        print(f"Check duration: {health_status['total_check_time_ms']:.2f}ms")
        
        print("\nProvider details:")
        for provider, details in health_status['providers'].items():
            status = details.get('status', 'unknown')
            response_time = details.get('response_time_ms', 'N/A')
            print(f"  {provider}: {status} ({response_time}ms)")
    except Exception as e:
        print(f"Provider health check failed: {e}")
    
    # Check service methods health
    try:
        service_health = await ai_service_manager.check_service_method_health()
        print(f"\nService methods health: {service_health['status']}")
        
        for method, details in service_health['service_methods'].items():
            status = details.get('status', 'unknown')
            response_time = details.get('response_time_ms', 'N/A')
            provider = details.get('provider', 'unknown')
            print(f"  {method}: {status} ({response_time}ms) via {provider}")
    except Exception as e:
        print(f"Service method health check failed: {e}")


async def example_error_handling():
    """Example of error handling and fallback behavior"""
    print("\n=== Error Handling Examples ===")
    
    # Test with invalid provider
    try:
        await ai_service_manager.generate_text(
            "Test message",
            provider="invalid_provider"
        )
    except Exception as e:
        print(f"Expected error with invalid provider: {type(e).__name__}: {e}")
    
    # Test fallback behavior
    original_provider = ai_service_manager.llm_provider
    try:
        # Switch to a provider that might not be available
        ai_service_manager.llm_provider = "moonshot"  # Might not have API key
        
        result = await ai_service_manager.generate_text(
            "Test fallback behavior",
            max_tokens=20
        )
        print(f"Fallback result: {result}")
    except Exception as e:
        print(f"All providers failed: {type(e).__name__}: {e}")
    finally:
        ai_service_manager.llm_provider = original_provider


async def run_all_examples():
    """Run all examples in sequence"""
    print("🚀 AI Service Manager Usage Examples")
    print("=" * 50)
    
    examples = [
        example_text_generation,
        example_embedding_generation,
        example_document_reranking,
        example_provider_switching,
        example_health_checking,
        example_error_handling
    ]
    
    for example_func in examples:
        try:
            await example_func()
        except Exception as e:
            print(f"Example {example_func.__name__} failed: {e}")
        
        print("\n" + "-" * 50)
    
    print("\n✨ All examples completed!")


if __name__ == "__main__":
    asyncio.run(run_all_examples())