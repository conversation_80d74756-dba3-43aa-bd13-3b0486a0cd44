#!/usr/bin/env python3
"""
Simple test runner to check OCR test pass rate
"""
import os
import sys
import subprocess
import re
from pathlib import Path

def main():
    # Set up paths
    backend_path = Path(__file__).parent / "app" / "backend"
    
    # Change to backend directory
    os.chdir(backend_path)
    
    # Add backend to Python path
    sys.path.insert(0, str(backend_path))
    os.environ['PYTHONPATH'] = str(backend_path)
    
    try:
        # Run just the OCR tests
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/test_ocr_service.py",
            "tests/test_resume_parser_ocr_integration.py",
            "-v", "--tb=short", "--maxfail=10"
        ]
        
        print("Running OCR tests...")
        print("Command:", " ".join(cmd))
        
        result = subprocess.run(
            cmd, 
            cwd=backend_path, 
            capture_output=True, 
            text=True, 
            timeout=300
        )
        
        print("STDOUT:")
        print(result.stdout)
        print("\nSTDERR:")  
        print(result.stderr)
        
        # Parse test results
        output = result.stdout + result.stderr
        
        # Look for test result summary
        passed_match = re.search(r'(\d+) passed', output)
        failed_match = re.search(r'(\d+) failed', output)
        error_match = re.search(r'(\d+) error', output)
        
        passed = int(passed_match.group(1)) if passed_match else 0
        failed = int(failed_match.group(1)) if failed_match else 0
        errors = int(error_match.group(1)) if error_match else 0
        
        total = passed + failed + errors
        pass_rate = (passed / total * 100) if total > 0 else 0
        
        print(f"\n{'='*50}")
        print(f"TEST RESULTS SUMMARY")
        print(f"{'='*50}")
        print(f"Passed: {passed}")
        print(f"Failed: {failed}")
        print(f"Errors: {errors}")
        print(f"Total: {total}")
        print(f"Pass Rate: {pass_rate:.1f}%")
        print(f"Return Code: {result.returncode}")
        
        # Quality assessment
        if pass_rate >= 80:
            print(f"✅ EXCELLENT: Pass rate meets target (>80%)")
        elif pass_rate >= 70:
            print(f"⚠️  GOOD: Pass rate acceptable (>70%)")  
        else:
            print(f"❌ POOR: Pass rate below acceptable (<70%)")
        
        return result.returncode
        
    except subprocess.TimeoutExpired:
        print("❌ Test execution timed out")
        return 1
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())