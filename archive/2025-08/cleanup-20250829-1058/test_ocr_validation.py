#!/usr/bin/env python3
"""
OCR Test Validation Script

This script validates that our OCR test fixes work correctly by running
a subset of tests that can execute without full database dependencies.
"""

import sys
import os
from pathlib import Path
from unittest.mock import Mock, patch

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Define comprehensive mocks at module level
COMPREHENSIVE_MOCKS = {
    'paddle': <PERSON>ck(),
    'paddleocr': <PERSON><PERSON>(),
    'PIL': <PERSON>ck(),
    'cv2': <PERSON>ck(),
    'numpy': <PERSON>ck(),
    'torch': <PERSON>ck(),
    'torch.cuda': <PERSON>ck(),
    'PyPDF2': <PERSON>ck(),
    'asyncpg': <PERSON>ck(),
    'psycopg2': <PERSON>ck(),
    'redis': <PERSON>ck(),
    'redis.asyncio': Mock(),
    'sqlalchemy': <PERSON>ck(),
    'sqlalchemy.ext': <PERSON><PERSON>(),
    'sqlalchemy.ext.asyncio': <PERSON><PERSON>(),
    'pydantic': <PERSON><PERSON>(),
    'fastapi': <PERSON><PERSON>()
}

def test_ocr_service_imports():
    """Test that OCR service can be imported with our mock fixes"""
    print("Testing OCR service imports...")
    
    try:
        # Mock all the heavy dependencies before importing
        with patch.dict('sys.modules', COMPREHENSIVE_MOCKS):
            from app.services.ocr_service import OCRService, OCRError, ocr_service
            print("✅ OCR service imported successfully")
            return True
    except Exception as e:
        print(f"❌ OCR service import failed: {e}")
        return False

def test_ocr_singleton_pattern():
    """Test that OCR singleton works with our reset fixture approach"""
    print("Testing OCR singleton pattern...")
    
    try:
        with patch.dict('sys.modules', COMPREHENSIVE_MOCKS):
            from app.services.ocr_service import OCRService
            
            # Clear singleton state
            OCRService._instance = None
            OCRService._ocr_engine = None
            OCRService._gpu_available = None
            OCRService._model_loaded = False
            
            # Create instances
            service1 = OCRService()
            service2 = OCRService()
            
            # Test singleton pattern
            assert service1 is service2, "Singleton pattern not working"
            
            # Test state reset simulation
            original_instance = OCRService._instance
            original_model_loaded = OCRService._model_loaded
            
            # Reset for test
            OCRService._instance = None
            OCRService._model_loaded = False
            
            # Restore state
            OCRService._instance = original_instance
            OCRService._model_loaded = original_model_loaded
            
            print("✅ OCR singleton pattern works correctly")
            return True
    except Exception as e:
        print(f"❌ OCR singleton test failed: {e}")
        return False

def test_mock_configuration():
    """Test that mock configuration works as expected"""
    print("Testing mock configuration...")
    
    try:
        with patch.dict('sys.modules', COMPREHENSIVE_MOCKS):
            # Mock PaddleOCR consistently
            with patch('builtins.__import__') as mock_import:
                mock_paddle_module = Mock()
                mock_paddle_ocr_class = Mock()
                mock_paddleocr_module = Mock()
                mock_paddleocr_module.PaddleOCR = mock_paddle_ocr_class
                
                def import_side_effect(name, *args, **kwargs):
                    if name == 'paddle':
                        return mock_paddle_module
                    elif name == 'paddleocr':
                        return mock_paddleocr_module
                    elif name in ['PIL', 'cv2', 'numpy', 'torch']:
                        return Mock()
                    return __import__(name, *args, **kwargs)
                
                mock_import.side_effect = import_side_effect
                
                # Test that our mock setup can import OCR service
                from app.services.ocr_service import OCRService
                service = OCRService()
                
                # Test that service has expected attributes
                assert hasattr(service, 'enabled'), "Service missing enabled attribute"
                assert hasattr(service, 'use_gpu'), "Service missing use_gpu attribute"
                
                print("✅ Mock configuration works correctly")
                return True
    except Exception as e:
        print(f"❌ Mock configuration test failed: {e}")
        return False

def test_helper_functions():
    """Test that helper functions work with mocked dependencies"""
    print("Testing helper functions...")
    
    try:
        # Import helper functions from integration test
        sys.path.insert(0, str(backend_dir / 'tests'))
        
        with patch.dict('sys.modules', {**COMPREHENSIVE_MOCKS, 'docx': Mock()}):
            from test_resume_parser_ocr_integration import (
                create_minimal_pdf, create_resume_image, 
                create_docx_content, create_resume_image_with_text
            )
            
            # Test that helper functions return expected types
            pdf_data = create_minimal_pdf()
            assert isinstance(pdf_data, bytes), "PDF helper should return bytes"
            
            img_data = create_resume_image()
            assert isinstance(img_data, bytes), "Image helper should return bytes"
            
            docx_data = create_docx_content()
            assert isinstance(docx_data, bytes), "DOCX helper should return bytes"
            
            detailed_img = create_resume_image_with_text()
            assert isinstance(detailed_img, bytes), "Detailed image helper should return bytes"
            
            print("✅ Helper functions work correctly")
            return True
    except Exception as e:
        print(f"❌ Helper functions test failed: {e}")
        return False

def main():
    """Run all validation tests"""
    print("🔍 OCR Test Validation Starting...")
    print("=" * 50)
    
    tests = [
        test_ocr_service_imports,
        test_ocr_singleton_pattern,
        test_mock_configuration,
        test_helper_functions
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Unexpected error in {test_func.__name__}: {e}")
    
    print("=" * 50)
    print(f"📊 Validation Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All validation tests passed! OCR fixes are working correctly.")
        return 0
    else:
        print(f"⚠️  {total - passed} validation test(s) failed. Issues still need fixing.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)