#!/usr/bin/env python3
"""
Run OCR test validation with proper environment setup
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    # Set up paths
    backend_path = Path(__file__).parent / "app" / "backend"
    validation_script = Path(__file__).parent / "app" / "scripts" / "test" / "validate_ocr_tests.py"
    
    # Change to backend directory
    os.chdir(backend_path)
    
    # Add backend to Python path
    sys.path.insert(0, str(backend_path))
    
    # Set environment variables
    os.environ['PYTHONPATH'] = str(backend_path)
    
    try:
        # Run validation script
        result = subprocess.run([
            sys.executable, str(validation_script),
            "--backend-path", str(backend_path)
        ], cwd=backend_path, capture_output=True, text=True, timeout=300)
        
        print("STDOUT:")
        print(result.stdout)
        print("\nSTDERR:")
        print(result.stderr)
        print(f"\nReturn Code: {result.returncode}")
        
        return result.returncode
        
    except subprocess.TimeoutExpired:
        print("❌ Validation script timed out")
        return 1
    except Exception as e:
        print(f"❌ Error running validation: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())