# Archive Reason: Redundant Tools Directory

## Archive Date
2025-08-26

## Reason for Archival
This `tools/` directory was archived because it violates the project's architectural principles and provides zero unique value.

## Architecture Violations
1. **Root Directory Pollution**: Violates CLAUDE.md mandatory rules that forbid test files at root level
2. **Complete Redundancy**: 100% of files are duplicates of files in proper locations
3. **Confusion Source**: Creates developer confusion about canonical test locations

## Duplicate File Mapping
All files in this directory exist in their proper locations:

| Archived File | Proper Location |
|--------------|-----------------|
| `tools/testing/api/comprehensive_regression_test.py` | `app/scripts/test/comprehensive_regression_test.py` |
| `tools/testing/api/run_regression_tests.sh` | `app/scripts/test/run_regression_tests.sh` |
| `tools/testing/integration/jwt-refresh-test.js` | `app/frontend/__tests__/utils/jwt-refresh-test.js` |
| `tools/testing/integration/test_pgvector.py` | `app/scripts/test/test_pgvector.py` |
| `tools/testing/integration/comprehensive_resume_test.py` | `app/backend/tests/comprehensive_resume_test.py` |
| `tools/testing/utils/run_coverage.py` | `app/scripts/test/run_coverage.py` |

## Proper Test Locations
According to the project architecture:
- **Backend unit tests**: `app/backend/tests/`
- **Frontend tests**: `app/frontend/__tests__/`
- **Integration/scripts**: `app/scripts/test/`
- **Performance tests**: `app/scripts/test/` (with performance prefix)

## Impact Assessment
- **External Dependencies**: None found
- **Script References**: Only self-references within tools/testing/README.md
- **Risk Level**: LOW - Safe to archive without breaking functionality

## Decision
Archived to maintain architectural integrity and eliminate confusion. All functionality remains available in the proper locations.