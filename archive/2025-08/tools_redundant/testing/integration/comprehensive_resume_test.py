#!/usr/bin/env python3
"""
TalentForge Pro - 综合简历系统测试套件
使用DeepSeek生成测试数据，全面测试简历解析、入库和搜索功能
QA专家级测试方案
"""

import asyncio
import base64
import json
import logging
import random
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import os
import sys
import hashlib

# 添加项目路径
sys.path.insert(0, '/app/backend' if os.path.exists('/.dockerenv') else 'backend')
sys.path.insert(0, '/app' if os.path.exists('/.dockerenv') else '.')

from openai import AsyncOpenAI
import aiohttp

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# API配置
API_BASE_URL = "http://localhost:8001/api/v1" if os.path.exists('/.dockerenv') else "http://localhost:8088/api/v1"
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_API_BASE = "https://api.deepseek.com/v1"


class DeepSeekResumeGenerator:
    """使用DeepSeek生成多样化测试简历"""
    
    def __init__(self):
        self.client = AsyncOpenAI(
            api_key=DEEPSEEK_API_KEY,
            base_url=DEEPSEEK_API_BASE
        )
        self.templates = self._load_templates()
        
    def _load_templates(self) -> Dict[str, Any]:
        """加载简历模板配置"""
        return {
            "junior": {
                "experience_years": "1-3年",
                "skills": ["基础技能", "学习能力强"],
                "education": "本科",
                "salary": "8-15K"
            },
            "mid": {
                "experience_years": "3-5年",
                "skills": ["独立开发", "项目经验"],
                "education": "本科/硕士",
                "salary": "15-25K"
            },
            "senior": {
                "experience_years": "5-10年",
                "skills": ["架构设计", "团队管理"],
                "education": "本科/硕士",
                "salary": "25-50K"
            },
            "expert": {
                "experience_years": "10年以上",
                "skills": ["技术专家", "战略规划"],
                "education": "硕士/博士",
                "salary": "50K+"
            }
        }
    
    async def generate_resume(
        self,
        profile_type: str = "mid",
        tech_stack: str = "backend",
        language: str = "zh",
        special_requirements: Optional[str] = None
    ) -> Dict[str, Any]:
        """生成单个测试简历"""
        
        template = self.templates.get(profile_type, self.templates["mid"])
        
        # 构建生成提示
        prompt = self._build_prompt(profile_type, tech_stack, language, template, special_requirements)
        
        try:
            response = await self.client.chat.completions.create(
                model="deepseek-chat",
                messages=[
                    {
                        "role": "system",
                        "content": "你是一个专业的简历生成专家，能够生成真实、详细的测试简历数据。"
                    },
                    {"role": "user", "content": prompt}
                ],
                temperature=0.7,
                max_tokens=2000
            )
            
            resume_text = response.choices[0].message.content
            
            # 解析JSON响应
            import re
            json_match = re.search(r'\{.*\}', resume_text, re.DOTALL)
            if json_match:
                resume_data = json.loads(json_match.group())
            else:
                # 如果无法解析JSON，返回纯文本
                resume_data = {
                    "raw_text": resume_text,
                    "profile_type": profile_type,
                    "tech_stack": tech_stack
                }
            
            logger.info(f"✅ 生成{profile_type}级别{tech_stack}简历")
            return resume_data
            
        except Exception as e:
            logger.error(f"生成简历失败: {str(e)}")
            return self._generate_fallback_resume(profile_type, tech_stack)
    
    def _build_prompt(
        self,
        profile_type: str,
        tech_stack: str,
        language: str,
        template: Dict,
        special_requirements: Optional[str]
    ) -> str:
        """构建简历生成提示"""
        
        tech_stacks = {
            "frontend": "React, Vue, TypeScript, Webpack, CSS3, HTML5",
            "backend": "Python, FastAPI, Django, PostgreSQL, Redis, Docker",
            "fullstack": "React, Node.js, Python, MongoDB, Docker, Kubernetes",
            "ai_ml": "Python, TensorFlow, PyTorch, Scikit-learn, NLP, Computer Vision",
            "devops": "Docker, Kubernetes, Jenkins, AWS, Terraform, Ansible",
            "mobile": "React Native, Flutter, iOS, Android, Swift, Kotlin"
        }
        
        skills = tech_stacks.get(tech_stack, tech_stacks["backend"])
        
        lang_instruction = "用中文" if language == "zh" else "in English"
        
        prompt = f"""
        请{lang_instruction}生成一份{template['experience_years']}经验的{tech_stack}开发工程师简历。
        
        要求：
        1. 经验级别：{profile_type}（{template['experience_years']}）
        2. 技术栈：{skills}
        3. 教育背景：{template['education']}
        4. 期望薪资：{template['salary']}
        {f'5. 特殊要求：{special_requirements}' if special_requirements else ''}
        
        请返回JSON格式：
        {{
            "name": "姓名",
            "email": "邮箱",
            "phone": "电话",
            "experience_years": 工作年限数字,
            "current_position": "当前职位",
            "education": {{
                "degree": "学位",
                "major": "专业",
                "school": "学校",
                "graduation_year": "毕业年份"
            }},
            "work_experience": [
                {{
                    "company": "公司名",
                    "position": "职位",
                    "duration": "时间段",
                    "responsibilities": ["职责1", "职责2"],
                    "achievements": ["成就1", "成就2"]
                }}
            ],
            "skills": {{
                "programming": ["语言1", "语言2"],
                "frameworks": ["框架1", "框架2"],
                "databases": ["数据库1", "数据库2"],
                "tools": ["工具1", "工具2"]
            }},
            "projects": [
                {{
                    "name": "项目名",
                    "role": "角色",
                    "description": "描述",
                    "technologies": ["技术1", "技术2"],
                    "outcome": "成果"
                }}
            ],
            "certifications": ["证书1", "证书2"],
            "languages": ["中文", "英文"],
            "expected_salary": "{template['salary']}",
            "self_introduction": "个人简介（100-200字）"
        }}
        """
        
        return prompt
    
    def _generate_fallback_resume(self, profile_type: str, tech_stack: str) -> Dict[str, Any]:
        """生成备用简历（当API调用失败时）"""
        
        names = ["张伟", "李娜", "王强", "刘洋", "陈敏", "赵磊", "孙悦", "周涛", "吴晨", "郑欣"]
        companies = ["科技创新公司", "互联网巨头", "创业公司", "外企", "国企研发中心"]
        
        template = self.templates.get(profile_type, self.templates["mid"])
        name = random.choice(names)
        
        return {
            "name": name,
            "email": f"{name.lower()}@example.com",
            "phone": f"138{random.randint(10000000, 99999999)}",
            "experience_years": int(template['experience_years'].split('-')[0]),
            "current_position": f"{tech_stack}工程师",
            "education": {
                "degree": "本科" if profile_type in ["junior", "mid"] else "硕士",
                "major": "计算机科学与技术",
                "school": "知名大学",
                "graduation_year": str(2024 - int(template['experience_years'].split('-')[0]) - 4)
            },
            "work_experience": [
                {
                    "company": random.choice(companies),
                    "position": f"{profile_type.title()} {tech_stack} Engineer",
                    "duration": f"2020-{datetime.now().year}",
                    "responsibilities": ["开发和维护系统", "技术方案设计"],
                    "achievements": ["提升系统性能50%", "完成重要项目"]
                }
            ],
            "skills": {
                "programming": ["Python", "JavaScript", "Go"],
                "frameworks": ["FastAPI", "React", "Django"],
                "databases": ["PostgreSQL", "MongoDB", "Redis"],
                "tools": ["Docker", "Git", "Kubernetes"]
            },
            "expected_salary": template['salary'],
            "self_introduction": f"经验丰富的{tech_stack}工程师，{template['experience_years']}工作经验。"
        }
    
    async def generate_batch(
        self,
        count: int = 10,
        diversity: bool = True
    ) -> List[Dict[str, Any]]:
        """批量生成测试简历"""
        
        resumes = []
        
        if diversity:
            # 生成多样化的简历
            profiles = ["junior", "mid", "senior", "expert"]
            stacks = ["frontend", "backend", "fullstack", "ai_ml", "devops"]
            
            for i in range(count):
                profile = profiles[i % len(profiles)]
                stack = stacks[i % len(stacks)]
                
                resume = await self.generate_resume(
                    profile_type=profile,
                    tech_stack=stack,
                    language="zh" if i % 2 == 0 else "en"
                )
                resumes.append(resume)
                
                # 避免API限流
                await asyncio.sleep(1)
        else:
            # 生成相似的简历（用于测试搜索精度）
            for i in range(count):
                resume = await self.generate_resume(
                    profile_type="mid",
                    tech_stack="backend",
                    language="zh"
                )
                resumes.append(resume)
                await asyncio.sleep(1)
        
        logger.info(f"✅ 批量生成{count}份简历完成")
        return resumes


class ResumeParsingTester:
    """简历解析功能测试器"""
    
    def __init__(self):
        self.session: Optional[aiohttp.ClientSession] = None
        self.auth_token: Optional[str] = None
        self.test_results = {
            "total": 0,
            "success": 0,
            "failed": 0,
            "parse_accuracy": [],
            "response_times": [],
            "errors": []
        }
    
    async def setup(self):
        """初始化测试环境"""
        self.session = aiohttp.ClientSession()
        await self._authenticate()
    
    async def teardown(self):
        """清理测试环境"""
        if self.session:
            await self.session.close()
    
    async def _authenticate(self):
        """获取认证token"""
        try:
            # 使用测试账户登录
            login_data = {
                "username": "<EMAIL>",
                "password": "Admin123456!"
            }
            
            async with self.session.post(
                f"{API_BASE_URL}/auth/login",
                data=login_data,
                headers={"Content-Type": "application/x-www-form-urlencoded"}
            ) as resp:
                if resp.status == 200:
                    data = await resp.json()
                    self.auth_token = data.get("access_token")
                    logger.info("✅ 认证成功")
                else:
                    # 尝试注册
                    await self._register_test_user()
                    await self._authenticate()  # 递归重试
        except Exception as e:
            logger.error(f"认证失败: {str(e)}")
    
    async def _register_test_user(self):
        """注册测试用户"""
        register_data = {
            "email": "<EMAIL>",
            "password": "Admin123456!",
            "full_name": "测试管理员",
            "username": "admin"
        }
        
        async with self.session.post(
            f"{API_BASE_URL}/auth/register",
            json=register_data
        ) as resp:
            if resp.status in [201, 409]:  # 创建成功或已存在
                logger.info("测试用户准备就绪")
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        if self.auth_token:
            return {
                "Authorization": f"Bearer {self.auth_token}",
                "Content-Type": "application/json"
            }
        return {"Content-Type": "application/json"}
    
    async def test_parse_single(self, resume_data: Dict[str, Any]) -> Dict[str, Any]:
        """测试单个简历解析"""
        
        start_time = time.time()
        result = {
            "success": False,
            "parse_time": 0,
            "accuracy": 0,
            "parsed_data": None,
            "error": None
        }
        
        try:
            # 准备简历文本
            resume_text = self._format_resume_text(resume_data)
            
            # Base64编码
            encoded_content = base64.b64encode(resume_text.encode('utf-8')).decode('utf-8')
            
            # 创建候选人
            candidate_data = {
                "name": resume_data.get("name", "测试候选人"),
                "email": resume_data.get("email", f"test_{random.randint(1000, 9999)}@example.com"),
                "phone": resume_data.get("phone", "13800138000"),
                "current_position": resume_data.get("current_position", "工程师"),
                "years_of_experience": resume_data.get("experience_years", 3),
                "skills": self._extract_skills(resume_data),
                "education": resume_data.get("education", {}).get("degree", "本科"),
                "expected_salary": resume_data.get("expected_salary", "面议")
            }
            
            # 创建候选人记录
            async with self.session.post(
                f"{API_BASE_URL}/candidates/",
                json=candidate_data,
                headers=self._get_headers()
            ) as resp:
                if resp.status in [200, 201]:
                    candidate = await resp.json()
                    candidate_id = candidate.get("id")
                    
                    # 解析简历
                    parse_request = {
                        "file_content": encoded_content,
                        "file_type": "txt",
                        "candidate_id": candidate_id
                    }
                    
                    async with self.session.post(
                        f"{API_BASE_URL}/matching/parse-resume",
                        json=parse_request,
                        headers=self._get_headers()
                    ) as parse_resp:
                        result["parse_time"] = time.time() - start_time
                        
                        if parse_resp.status == 200:
                            parsed = await parse_resp.json()
                            result["success"] = True
                            result["parsed_data"] = parsed
                            
                            # 计算解析准确率
                            result["accuracy"] = self._calculate_accuracy(
                                resume_data, 
                                parsed.get("parsed_data", {})
                            )
                            
                            logger.info(f"✅ 简历解析成功 - 准确率: {result['accuracy']:.1%}")
                        else:
                            error = await parse_resp.text()
                            result["error"] = f"解析失败: {parse_resp.status} - {error}"
                            logger.error(result["error"])
                else:
                    result["error"] = f"创建候选人失败: {resp.status}"
                    
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"测试异常: {str(e)}")
        
        # 更新统计
        self.test_results["total"] += 1
        if result["success"]:
            self.test_results["success"] += 1
            self.test_results["parse_accuracy"].append(result["accuracy"])
            self.test_results["response_times"].append(result["parse_time"])
        else:
            self.test_results["failed"] += 1
            self.test_results["errors"].append(result["error"])
        
        return result
    
    def _format_resume_text(self, resume_data: Dict[str, Any]) -> str:
        """格式化简历为文本"""
        
        text_parts = []
        
        # 基本信息
        text_parts.append(f"姓名：{resume_data.get('name', '未知')}")
        text_parts.append(f"邮箱：{resume_data.get('email', '未知')}")
        text_parts.append(f"电话：{resume_data.get('phone', '未知')}")
        text_parts.append(f"当前职位：{resume_data.get('current_position', '未知')}")
        text_parts.append(f"工作年限：{resume_data.get('experience_years', 0)}年")
        
        # 教育背景
        if "education" in resume_data:
            edu = resume_data["education"]
            text_parts.append(f"\n教育背景：")
            text_parts.append(f"{edu.get('school', '未知')} - {edu.get('major', '未知')} - {edu.get('degree', '未知')}")
        
        # 工作经历
        if "work_experience" in resume_data:
            text_parts.append(f"\n工作经历：")
            for exp in resume_data["work_experience"]:
                text_parts.append(f"{exp.get('company', '未知')} - {exp.get('position', '未知')} ({exp.get('duration', '未知')})")
                if "responsibilities" in exp:
                    for resp in exp["responsibilities"]:
                        text_parts.append(f"  - {resp}")
        
        # 技能
        if "skills" in resume_data:
            text_parts.append(f"\n技能：")
            skills = resume_data["skills"]
            if isinstance(skills, dict):
                for category, items in skills.items():
                    text_parts.append(f"{category}: {', '.join(items)}")
            else:
                text_parts.append(str(skills))
        
        # 项目经验
        if "projects" in resume_data:
            text_parts.append(f"\n项目经验：")
            for proj in resume_data["projects"]:
                text_parts.append(f"项目：{proj.get('name', '未知')}")
                text_parts.append(f"角色：{proj.get('role', '未知')}")
                text_parts.append(f"描述：{proj.get('description', '未知')}")
        
        # 自我介绍
        if "self_introduction" in resume_data:
            text_parts.append(f"\n自我介绍：")
            text_parts.append(resume_data["self_introduction"])
        
        return "\n".join(text_parts)
    
    def _extract_skills(self, resume_data: Dict[str, Any]) -> List[str]:
        """提取技能列表"""
        skills = []
        
        if "skills" in resume_data:
            skill_data = resume_data["skills"]
            if isinstance(skill_data, dict):
                for category, items in skill_data.items():
                    if isinstance(items, list):
                        skills.extend(items)
            elif isinstance(skill_data, list):
                skills = skill_data
        
        return skills[:10]  # 限制最多10个技能
    
    def _calculate_accuracy(self, original: Dict[str, Any], parsed: Dict[str, Any]) -> float:
        """计算解析准确率"""
        
        score = 0
        total = 0
        
        # 检查基本信息
        fields = ["name", "email", "phone"]
        for field in fields:
            total += 1
            if field in parsed.get("basic_info", {}):
                if str(original.get(field, "")).lower() in str(parsed["basic_info"][field]).lower():
                    score += 1
        
        # 检查技能
        original_skills = self._extract_skills(original)
        parsed_skills = []
        if "skills" in parsed:
            skill_data = parsed["skills"]
            if isinstance(skill_data, dict):
                for category, items in skill_data.items():
                    if isinstance(items, list):
                        parsed_skills.extend(items)
        
        if original_skills:
            total += 1
            matched_skills = sum(1 for skill in original_skills if any(skill.lower() in p.lower() for p in parsed_skills))
            if matched_skills > 0:
                score += matched_skills / len(original_skills)
        
        # 检查工作经历
        if "work_experience" in original and "work_experience" in parsed:
            total += 1
            if len(parsed["work_experience"]) > 0:
                score += 0.5  # 部分分数
                # 检查公司名称匹配
                original_companies = [exp.get("company", "") for exp in original["work_experience"]]
                parsed_companies = [exp.get("company", "") for exp in parsed["work_experience"]]
                for oc in original_companies:
                    if any(oc.lower() in pc.lower() for pc in parsed_companies):
                        score += 0.5
                        break
        
        return score / total if total > 0 else 0
    
    async def test_parse_batch(self, resumes: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量测试简历解析"""
        
        logger.info(f"开始批量测试{len(resumes)}份简历...")
        
        for i, resume in enumerate(resumes, 1):
            logger.info(f"测试进度: {i}/{len(resumes)}")
            await self.test_parse_single(resume)
            
            # 避免过载
            if i % 5 == 0:
                await asyncio.sleep(1)
        
        return self.get_test_summary()
    
    def get_test_summary(self) -> Dict[str, Any]:
        """获取测试摘要"""
        
        success_rate = (self.test_results["success"] / self.test_results["total"] * 100) if self.test_results["total"] > 0 else 0
        
        avg_accuracy = sum(self.test_results["parse_accuracy"]) / len(self.test_results["parse_accuracy"]) if self.test_results["parse_accuracy"] else 0
        
        avg_response_time = sum(self.test_results["response_times"]) / len(self.test_results["response_times"]) if self.test_results["response_times"] else 0
        
        return {
            "total_tests": self.test_results["total"],
            "success_count": self.test_results["success"],
            "failed_count": self.test_results["failed"],
            "success_rate": success_rate,
            "average_accuracy": avg_accuracy,
            "average_response_time": avg_response_time,
            "errors": self.test_results["errors"][:5]  # 最多显示5个错误
        }


class SearchFunctionalityTester:
    """搜索功能测试器"""
    
    def __init__(self, session: aiohttp.ClientSession, auth_token: str):
        self.session = session
        self.auth_token = auth_token
        self.test_results = {
            "keyword_search": [],
            "skill_match": [],
            "experience_filter": [],
            "combined_search": []
        }
    
    def _get_headers(self) -> Dict[str, str]:
        """获取请求头"""
        return {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json"
        }
    
    async def test_keyword_search(self, keyword: str) -> Dict[str, Any]:
        """测试关键词搜索"""
        
        result = {
            "keyword": keyword,
            "success": False,
            "count": 0,
            "relevance": 0,
            "response_time": 0
        }
        
        start_time = time.time()
        
        try:
            async with self.session.get(
                f"{API_BASE_URL}/candidates/search",
                params={"keyword": keyword, "limit": 20},
                headers=self._get_headers()
            ) as resp:
                result["response_time"] = time.time() - start_time
                
                if resp.status == 200:
                    data = await resp.json()
                    result["success"] = True
                    result["count"] = len(data.get("items", []))
                    
                    # 计算相关性
                    relevant = sum(
                        1 for item in data.get("items", [])
                        if keyword.lower() in str(item).lower()
                    )
                    result["relevance"] = relevant / result["count"] if result["count"] > 0 else 0
                    
                    logger.info(f"✅ 关键词搜索'{keyword}': 找到{result['count']}个结果")
                    
        except Exception as e:
            logger.error(f"关键词搜索失败: {str(e)}")
        
        self.test_results["keyword_search"].append(result)
        return result
    
    async def test_skill_match(self, skills: List[str]) -> Dict[str, Any]:
        """测试技能匹配搜索"""
        
        result = {
            "skills": skills,
            "success": False,
            "count": 0,
            "match_rate": 0,
            "response_time": 0
        }
        
        start_time = time.time()
        
        try:
            async with self.session.post(
                f"{API_BASE_URL}/matching/search-by-skills",
                json={"skills": skills, "min_match": 50},
                headers=self._get_headers()
            ) as resp:
                result["response_time"] = time.time() - start_time
                
                if resp.status == 200:
                    data = await resp.json()
                    result["success"] = True
                    result["count"] = len(data.get("results", []))
                    
                    # 计算匹配率
                    if result["count"] > 0:
                        match_scores = [r.get("match_score", 0) for r in data.get("results", [])]
                        result["match_rate"] = sum(match_scores) / len(match_scores)
                    
                    logger.info(f"✅ 技能匹配搜索: 找到{result['count']}个候选人")
                    
        except Exception as e:
            logger.error(f"技能匹配搜索失败: {str(e)}")
        
        self.test_results["skill_match"].append(result)
        return result
    
    async def test_experience_filter(self, min_years: int, max_years: int) -> Dict[str, Any]:
        """测试经验筛选"""
        
        result = {
            "experience_range": f"{min_years}-{max_years}年",
            "success": False,
            "count": 0,
            "accuracy": 0,
            "response_time": 0
        }
        
        start_time = time.time()
        
        try:
            async with self.session.get(
                f"{API_BASE_URL}/candidates/",
                params={
                    "min_experience": min_years,
                    "max_experience": max_years,
                    "limit": 50
                },
                headers=self._get_headers()
            ) as resp:
                result["response_time"] = time.time() - start_time
                
                if resp.status == 200:
                    data = await resp.json()
                    result["success"] = True
                    result["count"] = len(data.get("items", []))
                    
                    # 验证筛选准确性
                    if result["count"] > 0:
                        correct = sum(
                            1 for item in data.get("items", [])
                            if min_years <= item.get("years_of_experience", 0) <= max_years
                        )
                        result["accuracy"] = correct / result["count"]
                    
                    logger.info(f"✅ 经验筛选{min_years}-{max_years}年: 找到{result['count']}个候选人")
                    
        except Exception as e:
            logger.error(f"经验筛选失败: {str(e)}")
        
        self.test_results["experience_filter"].append(result)
        return result
    
    async def test_combined_search(
        self,
        keyword: str,
        skills: List[str],
        min_experience: int
    ) -> Dict[str, Any]:
        """测试组合搜索"""
        
        result = {
            "criteria": {
                "keyword": keyword,
                "skills": skills,
                "min_experience": min_experience
            },
            "success": False,
            "count": 0,
            "precision": 0,
            "response_time": 0
        }
        
        start_time = time.time()
        
        try:
            async with self.session.post(
                f"{API_BASE_URL}/matching/advanced-search",
                json={
                    "keyword": keyword,
                    "skills": skills,
                    "min_experience": min_experience,
                    "limit": 20
                },
                headers=self._get_headers()
            ) as resp:
                result["response_time"] = time.time() - start_time
                
                if resp.status == 200:
                    data = await resp.json()
                    result["success"] = True
                    result["count"] = len(data.get("results", []))
                    
                    # 计算精确度
                    if result["count"] > 0:
                        matches = 0
                        for item in data.get("results", []):
                            # 检查所有条件
                            if (keyword.lower() in str(item).lower() and
                                item.get("years_of_experience", 0) >= min_experience):
                                matches += 1
                        result["precision"] = matches / result["count"]
                    
                    logger.info(f"✅ 组合搜索: 找到{result['count']}个结果，精确度{result['precision']:.1%}")
                    
        except Exception as e:
            logger.error(f"组合搜索失败: {str(e)}")
        
        self.test_results["combined_search"].append(result)
        return result
    
    def get_search_summary(self) -> Dict[str, Any]:
        """获取搜索测试摘要"""
        
        summary = {}
        
        for test_type, results in self.test_results.items():
            if results:
                success_count = sum(1 for r in results if r.get("success", False))
                avg_response_time = sum(r.get("response_time", 0) for r in results) / len(results)
                
                summary[test_type] = {
                    "total_tests": len(results),
                    "success_count": success_count,
                    "success_rate": success_count / len(results) * 100,
                    "average_response_time": avg_response_time
                }
                
                # 特定指标
                if test_type == "keyword_search":
                    relevances = [r.get("relevance", 0) for r in results if r.get("relevance") is not None]
                    summary[test_type]["average_relevance"] = sum(relevances) / len(relevances) if relevances else 0
                    
                elif test_type == "skill_match":
                    match_rates = [r.get("match_rate", 0) for r in results if r.get("match_rate") is not None]
                    summary[test_type]["average_match_rate"] = sum(match_rates) / len(match_rates) if match_rates else 0
                    
                elif test_type == "experience_filter":
                    accuracies = [r.get("accuracy", 0) for r in results if r.get("accuracy") is not None]
                    summary[test_type]["average_accuracy"] = sum(accuracies) / len(accuracies) if accuracies else 0
                    
                elif test_type == "combined_search":
                    precisions = [r.get("precision", 0) for r in results if r.get("precision") is not None]
                    summary[test_type]["average_precision"] = sum(precisions) / len(precisions) if precisions else 0
        
        return summary


class ComprehensiveTestRunner:
    """综合测试运行器"""
    
    def __init__(self):
        self.generator = DeepSeekResumeGenerator()
        self.parser_tester = ResumeParsingTester()
        self.search_tester: Optional[SearchFunctionalityTester] = None
        self.test_report = {
            "test_date": datetime.now().isoformat(),
            "test_type": "综合简历系统测试",
            "modules_tested": ["简历生成", "简历解析", "数据入库", "搜索功能"],
            "results": {}
        }
    
    async def run_comprehensive_test(self):
        """运行综合测试"""
        
        logger.info("\n" + "="*80)
        logger.info("🧪 TalentForge Pro 综合简历系统测试")
        logger.info("="*80)
        
        start_time = time.time()
        
        try:
            # 1. 设置测试环境
            await self.parser_tester.setup()
            
            # 2. 生成测试数据
            logger.info("\n📝 阶段1: 使用DeepSeek生成测试简历")
            resumes = await self.generator.generate_batch(count=10, diversity=True)
            self.test_report["results"]["resume_generation"] = {
                "total_generated": len(resumes),
                "status": "成功"
            }
            
            # 3. 测试简历解析
            logger.info("\n🔍 阶段2: 测试简历解析功能")
            parse_results = await self.parser_tester.test_parse_batch(resumes)
            self.test_report["results"]["resume_parsing"] = parse_results
            
            # 4. 测试搜索功能
            if self.parser_tester.auth_token:
                logger.info("\n🔎 阶段3: 测试搜索功能")
                self.search_tester = SearchFunctionalityTester(
                    self.parser_tester.session,
                    self.parser_tester.auth_token
                )
                
                # 执行各种搜索测试
                await self.search_tester.test_keyword_search("Python")
                await self.search_tester.test_keyword_search("工程师")
                await self.search_tester.test_skill_match(["Python", "FastAPI", "Docker"])
                await self.search_tester.test_experience_filter(3, 8)
                await self.search_tester.test_combined_search(
                    "后端",
                    ["Python", "PostgreSQL"],
                    3
                )
                
                search_summary = self.search_tester.get_search_summary()
                self.test_report["results"]["search_functionality"] = search_summary
            
            # 5. 性能测试
            logger.info("\n⚡ 阶段4: 性能测试")
            perf_results = await self._run_performance_test()
            self.test_report["results"]["performance"] = perf_results
            
            # 6. 边界条件测试
            logger.info("\n🔧 阶段5: 边界条件测试")
            edge_results = await self._run_edge_case_tests()
            self.test_report["results"]["edge_cases"] = edge_results
            
        finally:
            await self.parser_tester.teardown()
        
        # 计算总体指标
        total_time = time.time() - start_time
        self.test_report["total_execution_time"] = f"{total_time:.2f}秒"
        
        # 生成最终报告
        self._generate_final_report()
    
    async def _run_performance_test(self) -> Dict[str, Any]:
        """运行性能测试"""
        
        logger.info("执行批量处理性能测试...")
        
        # 生成5个简历进行快速性能测试
        start_time = time.time()
        test_resumes = await self.generator.generate_batch(count=5, diversity=False)
        generation_time = time.time() - start_time
        
        # 测试批量解析
        parse_times = []
        for resume in test_resumes:
            parse_start = time.time()
            await self.parser_tester.test_parse_single(resume)
            parse_times.append(time.time() - parse_start)
        
        return {
            "batch_generation": {
                "count": 5,
                "total_time": generation_time,
                "avg_time_per_resume": generation_time / 5
            },
            "batch_parsing": {
                "count": len(parse_times),
                "total_time": sum(parse_times),
                "avg_time": sum(parse_times) / len(parse_times) if parse_times else 0,
                "min_time": min(parse_times) if parse_times else 0,
                "max_time": max(parse_times) if parse_times else 0
            }
        }
    
    async def _run_edge_case_tests(self) -> Dict[str, Any]:
        """运行边界条件测试"""
        
        logger.info("测试边界条件...")
        
        results = {
            "empty_resume": False,
            "oversized_resume": False,
            "special_characters": False,
            "non_utf8": False
        }
        
        # 测试空简历
        try:
            empty_resume = {"name": "", "email": "", "skills": []}
            result = await self.parser_tester.test_parse_single(empty_resume)
            results["empty_resume"] = result.get("success", False)
        except:
            pass
        
        # 测试超长简历
        try:
            oversized = {
                "name": "测试用户",
                "self_introduction": "x" * 10000  # 10000字符
            }
            result = await self.parser_tester.test_parse_single(oversized)
            results["oversized_resume"] = result.get("success", False)
        except:
            pass
        
        # 测试特殊字符
        try:
            special = {
                "name": "测试<script>alert('xss')</script>",
                "email": "<EMAIL>",
                "skills": ["Python", "SQL注入'OR'1'='1"]
            }
            result = await self.parser_tester.test_parse_single(special)
            results["special_characters"] = result.get("success", False)
        except:
            pass
        
        return results
    
    def _generate_final_report(self):
        """生成最终测试报告"""
        
        logger.info("\n" + "="*80)
        logger.info("📊 测试报告")
        logger.info("="*80)
        
        # 简历解析结果
        parse_results = self.test_report["results"].get("resume_parsing", {})
        if parse_results:
            logger.info("\n📄 简历解析测试:")
            logger.info(f"  - 总测试数: {parse_results.get('total_tests', 0)}")
            logger.info(f"  - 成功数: {parse_results.get('success_count', 0)}")
            logger.info(f"  - 成功率: {parse_results.get('success_rate', 0):.1f}%")
            logger.info(f"  - 平均准确率: {parse_results.get('average_accuracy', 0):.1%}")
            logger.info(f"  - 平均响应时间: {parse_results.get('average_response_time', 0):.2f}秒")
        
        # 搜索功能结果
        search_results = self.test_report["results"].get("search_functionality", {})
        if search_results:
            logger.info("\n🔍 搜索功能测试:")
            for test_type, metrics in search_results.items():
                logger.info(f"\n  {test_type}:")
                logger.info(f"    - 成功率: {metrics.get('success_rate', 0):.1f}%")
                logger.info(f"    - 平均响应时间: {metrics.get('average_response_time', 0):.3f}秒")
                
                if "average_relevance" in metrics:
                    logger.info(f"    - 平均相关性: {metrics['average_relevance']:.1%}")
                if "average_match_rate" in metrics:
                    logger.info(f"    - 平均匹配率: {metrics['average_match_rate']:.1f}%")
                if "average_accuracy" in metrics:
                    logger.info(f"    - 平均准确率: {metrics['average_accuracy']:.1%}")
                if "average_precision" in metrics:
                    logger.info(f"    - 平均精确度: {metrics['average_precision']:.1%}")
        
        # 性能测试结果
        perf_results = self.test_report["results"].get("performance", {})
        if perf_results:
            logger.info("\n⚡ 性能测试:")
            if "batch_generation" in perf_results:
                gen = perf_results["batch_generation"]
                logger.info(f"  批量生成: {gen['avg_time_per_resume']:.2f}秒/份")
            if "batch_parsing" in perf_results:
                parse = perf_results["batch_parsing"]
                logger.info(f"  批量解析: 平均{parse['avg_time']:.2f}秒，最快{parse['min_time']:.2f}秒，最慢{parse['max_time']:.2f}秒")
        
        # 边界测试结果
        edge_results = self.test_report["results"].get("edge_cases", {})
        if edge_results:
            logger.info("\n🔧 边界条件测试:")
            for test, passed in edge_results.items():
                status = "✅ 通过" if passed else "❌ 失败"
                logger.info(f"  - {test}: {status}")
        
        # 总体评估
        logger.info("\n" + "="*80)
        logger.info("📈 总体评估")
        logger.info("="*80)
        
        # 计算总体质量分数
        quality_score = self._calculate_quality_score()
        
        if quality_score >= 90:
            grade = "A - 优秀"
        elif quality_score >= 80:
            grade = "B - 良好"
        elif quality_score >= 70:
            grade = "C - 合格"
        else:
            grade = "D - 需改进"
        
        logger.info(f"质量评级: {grade} ({quality_score:.1f}分)")
        logger.info(f"测试耗时: {self.test_report['total_execution_time']}")
        
        # 保存报告
        report_file = "comprehensive_test_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_report, f, ensure_ascii=False, indent=2, default=str)
        logger.info(f"\n📁 详细报告已保存到: {report_file}")
        
        # 建议
        logger.info("\n💡 改进建议:")
        if parse_results.get('average_accuracy', 0) < 0.9:
            logger.info("  - 提升简历解析准确率，优化NLP模型")
        if parse_results.get('average_response_time', 0) > 2:
            logger.info("  - 优化解析性能，考虑使用缓存")
        if search_results and any(m.get('success_rate', 0) < 90 for m in search_results.values()):
            logger.info("  - 改进搜索算法，提高搜索成功率")
    
    def _calculate_quality_score(self) -> float:
        """计算总体质量分数"""
        
        score = 0
        weight = 0
        
        # 解析准确率 (权重30%)
        parse_results = self.test_report["results"].get("resume_parsing", {})
        if parse_results:
            score += parse_results.get("average_accuracy", 0) * 100 * 0.3
            weight += 0.3
        
        # 解析成功率 (权重20%)
        if parse_results:
            score += parse_results.get("success_rate", 0) * 0.2
            weight += 0.2
        
        # 搜索功能 (权重30%)
        search_results = self.test_report["results"].get("search_functionality", {})
        if search_results:
            avg_success = sum(m.get("success_rate", 0) for m in search_results.values()) / len(search_results)
            score += avg_success * 0.3
            weight += 0.3
        
        # 性能 (权重10%)
        perf_results = self.test_report["results"].get("performance", {})
        if perf_results and "batch_parsing" in perf_results:
            # 如果平均解析时间<2秒，给满分；>5秒给0分
            avg_time = perf_results["batch_parsing"]["avg_time"]
            perf_score = max(0, min(100, (5 - avg_time) / 3 * 100))
            score += perf_score * 0.1
            weight += 0.1
        
        # 边界测试 (权重10%)
        edge_results = self.test_report["results"].get("edge_cases", {})
        if edge_results:
            passed = sum(1 for v in edge_results.values() if v)
            edge_score = (passed / len(edge_results)) * 100
            score += edge_score * 0.1
            weight += 0.1
        
        return score / weight if weight > 0 else 0


async def main():
    """主测试函数"""
    runner = ComprehensiveTestRunner()
    await runner.run_comprehensive_test()


if __name__ == "__main__":
    asyncio.run(main())