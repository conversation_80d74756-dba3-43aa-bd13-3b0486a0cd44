#!/usr/bin/env python3
"""
pgvector 功能测试脚本
测试PostgreSQL向量扩展的完整功能集

作者: TalentForge Pro Team
日期: 2025-01-07
"""

import asyncio
import time
import random
import numpy as np
from typing import List, Tuple, Optional
import asyncpg
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich import print as rprint
import httpx

# 配置
DB_CONFIG = {
    "host": "localhost",
    "port": 15432,
    "database": "hephaestus_forge_db",
    "user": "postgres",
    "password": "Pass1234"
}

OLLAMA_HOST = "http://localhost:11434"
console = Console()

class PgVectorTester:
    """pgvector功能测试器"""
    
    def __init__(self):
        self.conn: Optional[asyncpg.Connection] = None
        self.test_results = []
        
    async def connect(self):
        """连接数据库"""
        try:
            self.conn = await asyncpg.connect(**DB_CONFIG)
            return True, "数据库连接成功"
        except Exception as e:
            return False, f"数据库连接失败: {e}"
    
    async def test_extension(self):
        """测试1: pgvector扩展安装"""
        try:
            # 创建扩展
            await self.conn.execute("CREATE EXTENSION IF NOT EXISTS vector;")
            
            # 验证扩展
            result = await self.conn.fetchval(
                "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'vector');"
            )
            
            if result:
                # 获取版本
                version = await self.conn.fetchval(
                    "SELECT extversion FROM pg_extension WHERE extname = 'vector';"
                )
                return True, f"pgvector扩展已安装 (版本: {version})"
            else:
                return False, "pgvector扩展未安装"
        except Exception as e:
            return False, f"扩展测试失败: {e}"
    
    async def test_vector_operations(self):
        """测试2: 向量基本操作"""
        try:
            # 创建测试表
            await self.conn.execute("""
                DROP TABLE IF EXISTS test_vectors CASCADE;
                CREATE TABLE test_vectors (
                    id SERIAL PRIMARY KEY,
                    name TEXT,
                    embedding vector(3)
                );
            """)
            
            # 插入向量
            vectors = [
                ("vec1", "[1.0, 0.0, 0.0]"),
                ("vec2", "[0.0, 1.0, 0.0]"),
                ("vec3", "[0.0, 0.0, 1.0]"),
                ("vec4", "[0.5, 0.5, 0.0]"),
            ]
            
            for name, vec in vectors:
                await self.conn.execute(
                    "INSERT INTO test_vectors (name, embedding) VALUES ($1, $2::vector)",
                    name, vec
                )
            
            # 查询向量
            count = await self.conn.fetchval("SELECT COUNT(*) FROM test_vectors")
            
            # 测试距离计算
            distances = await self.conn.fetch("""
                SELECT name, 
                       embedding <-> '[1, 0, 0]'::vector as l2_distance,
                       embedding <=> '[1, 0, 0]'::vector as cosine_distance,
                       embedding <#> '[1, 0, 0]'::vector as inner_product
                FROM test_vectors
                ORDER BY l2_distance
                LIMIT 2;
            """)
            
            return True, f"向量操作成功: 插入{count}条, 最近邻: {distances[0]['name']}"
        except Exception as e:
            return False, f"向量操作失败: {e}"
    
    async def test_high_dimension_vectors(self):
        """测试3: 高维向量 (1024维 - BGE-M3)"""
        try:
            # 创建高维向量表
            await self.conn.execute("""
                DROP TABLE IF EXISTS test_high_dim CASCADE;
                CREATE TABLE test_high_dim (
                    id SERIAL PRIMARY KEY,
                    embedding vector(1024)
                );
            """)
            
            # 生成随机1024维向量
            vec = np.random.randn(1024).tolist()
            vec_str = f"[{','.join(map(str, vec))}]"
            
            # 插入和查询
            await self.conn.execute(
                "INSERT INTO test_high_dim (embedding) VALUES ($1::vector)",
                vec_str
            )
            
            result = await self.conn.fetchval(
                "SELECT array_length(embedding::real[], 1) FROM test_high_dim LIMIT 1"
            )
            
            return True, f"高维向量测试成功: {result}维向量"
        except Exception as e:
            return False, f"高维向量测试失败: {e}"
    
    async def test_index_performance(self):
        """测试4: 索引性能测试"""
        try:
            # 创建测试表
            await self.conn.execute("""
                DROP TABLE IF EXISTS test_performance CASCADE;
                CREATE TABLE test_performance (
                    id SERIAL PRIMARY KEY,
                    embedding vector(128)
                );
            """)
            
            # 批量插入向量
            console.print("[yellow]生成测试数据...[/yellow]")
            batch_size = 100
            num_batches = 10
            
            for batch in range(num_batches):
                values = []
                for _ in range(batch_size):
                    vec = np.random.randn(128).tolist()
                    vec_str = f"[{','.join(map(str, vec))}]"
                    values.append(vec_str)
                
                await self.conn.executemany(
                    "INSERT INTO test_performance (embedding) VALUES ($1::vector)",
                    [(v,) for v in values]
                )
            
            total_count = batch_size * num_batches
            
            # 无索引查询
            query_vec = np.random.randn(128).tolist()
            query_vec_str = f"[{','.join(map(str, query_vec))}]"
            
            start = time.time()
            await self.conn.fetch(f"""
                SELECT id FROM test_performance
                ORDER BY embedding <=> '{query_vec_str}'::vector
                LIMIT 10
            """)
            no_index_time = (time.time() - start) * 1000
            
            # 创建HNSW索引
            await self.conn.execute("""
                CREATE INDEX idx_perf_hnsw ON test_performance 
                USING hnsw (embedding vector_cosine_ops)
                WITH (m = 16, ef_construction = 64);
            """)
            
            # 有索引查询
            start = time.time()
            await self.conn.fetch(f"""
                SELECT id FROM test_performance
                ORDER BY embedding <=> '{query_vec_str}'::vector
                LIMIT 10
            """)
            with_index_time = (time.time() - start) * 1000
            
            speedup = no_index_time / with_index_time if with_index_time > 0 else float('inf')
            
            return True, f"索引性能: {total_count}条数据, 无索引{no_index_time:.2f}ms, 有索引{with_index_time:.2f}ms, 加速{speedup:.2f}x"
        except Exception as e:
            return False, f"索引性能测试失败: {e}"
    
    async def test_ollama_integration(self):
        """测试5: Ollama嵌入服务集成"""
        try:
            async with httpx.AsyncClient() as client:
                # 检查Ollama服务
                try:
                    response = await client.get(f"{OLLAMA_HOST}/api/tags")
                    models = response.json().get("models", [])
                    
                    # 检查BGE-M3模型
                    has_bge = any(m.get("name") == "bge-m3" for m in models)
                    
                    if has_bge:
                        # 测试嵌入生成
                        embed_response = await client.post(
                            f"{OLLAMA_HOST}/api/embed",
                            json={
                                "model": "bge-m3",
                                "input": "测试文本：智能招聘系统"
                            },
                            timeout=30.0
                        )
                        
                        if embed_response.status_code == 200:
                            embeddings = embed_response.json().get("embeddings", [])
                            if embeddings:
                                dim = len(embeddings[0]) if isinstance(embeddings[0], list) else len(embeddings)
                                return True, f"Ollama集成成功: BGE-M3模型可用, 生成{dim}维向量"
                        
                        return False, "Ollama嵌入生成失败"
                    else:
                        return False, "BGE-M3模型未安装 (运行: ollama pull bge-m3)"
                        
                except httpx.ConnectError:
                    return False, "Ollama服务未运行 (请启动: ollama serve)"
                    
        except Exception as e:
            return False, f"Ollama集成测试失败: {e}"
    
    async def test_concurrent_operations(self):
        """测试6: 并发操作测试"""
        try:
            # 创建测试表
            await self.conn.execute("""
                DROP TABLE IF EXISTS test_concurrent CASCADE;
                CREATE TABLE test_concurrent (
                    id SERIAL PRIMARY KEY,
                    embedding vector(64)
                );
            """)
            
            # 并发插入
            async def insert_vector():
                vec = np.random.randn(64).tolist()
                vec_str = f"[{','.join(map(str, vec))}]"
                await self.conn.execute(
                    "INSERT INTO test_concurrent (embedding) VALUES ($1::vector)",
                    vec_str
                )
            
            # 运行并发任务
            start = time.time()
            tasks = [insert_vector() for _ in range(100)]
            await asyncio.gather(*tasks)
            elapsed = time.time() - start
            
            count = await self.conn.fetchval("SELECT COUNT(*) FROM test_concurrent")
            qps = count / elapsed if elapsed > 0 else 0
            
            return True, f"并发测试成功: {count}条插入, 耗时{elapsed:.2f}s, QPS={qps:.0f}"
        except Exception as e:
            return False, f"并发测试失败: {e}"
    
    async def test_backup_restore(self):
        """测试7: 备份恢复测试"""
        try:
            # 检查向量数据是否可以正确导出
            result = await self.conn.fetch("""
                SELECT 
                    table_name,
                    column_name,
                    data_type
                FROM information_schema.columns 
                WHERE data_type = 'USER-DEFINED'
                AND udt_name = 'vector'
                ORDER BY table_name, column_name;
            """)
            
            vector_tables = [f"{r['table_name']}.{r['column_name']}" for r in result]
            
            if vector_tables:
                return True, f"向量表支持备份: {', '.join(vector_tables[:3])}..."
            else:
                return True, "暂无向量表需要备份"
                
        except Exception as e:
            return False, f"备份测试失败: {e}"
    
    async def cleanup(self):
        """清理测试数据"""
        try:
            tables = [
                "test_vectors",
                "test_high_dim",
                "test_performance",
                "test_concurrent"
            ]
            
            for table in tables:
                await self.conn.execute(f"DROP TABLE IF EXISTS {table} CASCADE;")
            
            return True, "测试数据清理完成"
        except Exception as e:
            return False, f"清理失败: {e}"
    
    async def run_all_tests(self):
        """运行所有测试"""
        console.print(Panel.fit("🔬 [bold cyan]pgvector 功能测试套件[/bold cyan] 🔬"))
        
        # 连接数据库
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            console=console
        ) as progress:
            task = progress.add_task("[cyan]连接数据库...", total=None)
            success, message = await self.connect()
            progress.remove_task(task)
            
            if not success:
                console.print(f"[red]✗[/red] {message}")
                return
            console.print(f"[green]✓[/green] {message}")
        
        # 测试列表
        tests = [
            ("pgvector扩展", self.test_extension),
            ("向量基本操作", self.test_vector_operations),
            ("高维向量(1024维)", self.test_high_dimension_vectors),
            ("索引性能", self.test_index_performance),
            ("Ollama集成", self.test_ollama_integration),
            ("并发操作", self.test_concurrent_operations),
            ("备份恢复", self.test_backup_restore),
            ("清理测试数据", self.cleanup),
        ]
        
        # 运行测试
        results = []
        for test_name, test_func in tests:
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task(f"[yellow]测试: {test_name}...", total=None)
                
                try:
                    success, message = await test_func()
                    results.append((test_name, success, message))
                except Exception as e:
                    success = False
                    message = f"异常: {e}"
                    results.append((test_name, success, message))
                
                progress.remove_task(task)
                
                if success:
                    console.print(f"[green]✓[/green] {test_name}: {message}")
                else:
                    console.print(f"[red]✗[/red] {test_name}: {message}")
        
        # 显示测试报告
        console.print("\n" + "="*80)
        table = Table(title="📊 测试报告", show_header=True, header_style="bold magenta")
        table.add_column("测试项", style="cyan", width=30)
        table.add_column("状态", justify="center", width=10)
        table.add_column("结果", style="dim", width=50)
        
        passed = 0
        for test_name, success, message in results:
            status = "[green]通过[/green]" if success else "[red]失败[/red]"
            if success:
                passed += 1
            table.add_row(test_name, status, message[:50] + "..." if len(message) > 50 else message)
        
        console.print(table)
        
        # 总结
        total = len(results)
        pass_rate = (passed / total * 100) if total > 0 else 0
        
        if pass_rate == 100:
            console.print(f"\n[bold green]🎉 所有测试通过! ({passed}/{total})[/bold green]")
        elif pass_rate >= 80:
            console.print(f"\n[bold yellow]⚠️  大部分测试通过 ({passed}/{total}, {pass_rate:.0f}%)[/bold yellow]")
        else:
            console.print(f"\n[bold red]❌ 测试未通过 ({passed}/{total}, {pass_rate:.0f}%)[/bold red]")
        
        # 关闭连接
        if self.conn:
            await self.conn.close()
        
        return pass_rate == 100

async def main():
    """主函数"""
    tester = PgVectorTester()
    success = await tester.run_all_tests()
    exit(0 if success else 1)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        console.print("\n[yellow]测试被用户中断[/yellow]")
        exit(1)
    except Exception as e:
        console.print(f"\n[red]测试失败: {e}[/red]")
        exit(1)