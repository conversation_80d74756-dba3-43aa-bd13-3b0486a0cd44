#!/usr/bin/env node

/**
 * JWT Refresh Integration Test
 * 
 * This test verifies that the frontend JWT refresh mechanism works correctly
 * with the new RFC 6750 compliant Authorization header method.
 */

const axios = require('axios');

// Configuration
const API_BASE = process.env.API_BASE || 'http://localhost:8088/api/v1';
const TEST_USERNAME = '<EMAIL>';
const TEST_PASSWORD = 'test123';

console.log('🧪 JWT Refresh Integration Test');
console.log('================================');
console.log(`API Base: ${API_BASE}`);
console.log(`Test User: ${TEST_USERNAME}`);

async function testJWTRefresh() {
  try {
    console.log('\n1️⃣ Step 1: Login to get initial tokens');
    
    // Step 1: Login to get tokens
    const loginData = new URLSearchParams();
    loginData.append('username', TEST_USERNAME);
    loginData.append('password', TEST_PASSWORD);

    const loginResponse = await axios.post(`${API_BASE}/auth/login`, loginData, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      }
    });

    console.log('✅ Login successful');
    const { access_token, refresh_token } = loginResponse.data;
    console.log(`   Access Token: ${access_token.substring(0, 20)}...`);
    console.log(`   Refresh Token: ${refresh_token.substring(0, 20)}...`);

    console.log('\n2️⃣ Step 2: Test RFC 6750 compliant token refresh (Authorization header)');
    
    // Step 2: Test token refresh using new Authorization header method
    const refreshResponse = await axios.post(`${API_BASE}/auth/refresh`, null, {
      headers: {
        'Authorization': `Bearer ${refresh_token}`
      }
    });

    console.log('✅ Token refresh successful with Authorization header');
    const refreshData = refreshResponse.data;
    console.log(`   New Access Token: ${refreshData.access_token.substring(0, 20)}...`);
    console.log(`   New Refresh Token: ${refreshData.refresh_token.substring(0, 20)}...`);
    
    if (refreshData.method_used) {
      console.log(`   Method Used: ${refreshData.method_used}`);
    }
    
    if (refreshData.deprecation_warning) {
      console.log(`   ⚠️  Warning: ${refreshData.deprecation_warning}`);
    }

    console.log('\n3️⃣ Step 3: Verify new tokens work by calling protected endpoint');
    
    // Step 3: Test that new access token works
    const userResponse = await axios.get(`${API_BASE}/auth/me`, {
      headers: {
        'Authorization': `Bearer ${refreshData.access_token}`
      }
    });

    console.log('✅ Protected endpoint call successful with new token');
    console.log(`   User ID: ${userResponse.data.id}`);
    console.log(`   Username: ${userResponse.data.username}`);

    console.log('\n4️⃣ Step 4: Test legacy method (request body) for backward compatibility');
    
    // Step 4: Test legacy refresh method (should still work but with deprecation warning)
    try {
      const legacyRefreshResponse = await axios.post(`${API_BASE}/auth/refresh`, {
        refresh_token: refreshData.refresh_token
      });

      console.log('✅ Legacy method also works');
      const legacyData = legacyRefreshResponse.data;
      
      if (legacyData.method_used) {
        console.log(`   Method Used: ${legacyData.method_used}`);
      }
      
      if (legacyData.deprecation_warning) {
        console.log(`   ⚠️  Deprecation Warning: ${legacyData.deprecation_warning}`);
      }
    } catch (error) {
      console.log('ℹ️  Legacy method may have been disabled');
    }

    console.log('\n🎉 JWT Refresh Test Results');
    console.log('============================');
    console.log('✅ All tests passed!');
    console.log('✅ RFC 6750 Authorization header method works');
    console.log('✅ New tokens are valid and functional');
    console.log('✅ Backend returns method_used information');
    console.log('✅ Integration is working correctly');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    }
    process.exit(1);
  }
}

// Run the test
testJWTRefresh().catch(error => {
  console.error('Test execution failed:', error);
  process.exit(1);
});