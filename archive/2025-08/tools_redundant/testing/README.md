# TalentForge Pro 测试工具集

## 📋 概览

整理后的测试工具集，提供完整的测试解决方案，包括API测试、集成测试、性能测试和工具脚本。

## 🗂️ 目录结构

```
tools/testing/
├── api/                     # API测试工具
│   ├── run_regression_tests.sh         # API回归测试启动器
│   └── comprehensive_regression_test.py # 综合API回归测试
├── integration/             # 集成测试工具
│   ├── jwt-refresh-test.js             # JWT刷新集成测试
│   ├── test_pgvector.py                # 向量数据库测试
│   └── comprehensive_resume_test.py    # 综合简历系统测试
├── performance/             # 性能测试工具
├── utils/                   # 测试工具和实用脚本
│   └── run_coverage.py                 # 代码覆盖率测试
└── README.md               # 本文档
```

## 🔧 工具说明

### API测试工具

#### 1. API回归测试启动器 (`api/run_regression_tests.sh`)
- **功能**: 全面的API回归测试工具
- **特性**: 
  - 支持Docker和本地环境
  - 多种测试类型（all, unit, integration, regression, performance）
  - 生成HTML报告和覆盖率报告
  - 服务状态检查
- **使用方法**:
  ```bash
  # 运行回归测试
  ./tools/testing/api/run_regression_tests.sh
  
  # 运行性能测试
  ./tools/testing/api/run_regression_tests.sh -t performance
  
  # 生成报告和覆盖率
  ./tools/testing/api/run_regression_tests.sh -r -c
  ```

#### 2. 综合API回归测试 (`api/comprehensive_regression_test.py`)
- **功能**: Python版本的综合API测试
- **特性**: 系统性API端点测试
- **使用方法**:
  ```bash
  cd tools/testing/api
  python comprehensive_regression_test.py
  ```

### 集成测试工具

#### 1. JWT刷新集成测试 (`integration/jwt-refresh-test.js`)
- **功能**: 验证JWT刷新机制
- **特性**: 
  - RFC 6750合规性测试
  - 向后兼容性验证
  - Authorization header方法测试
- **使用方法**:
  ```bash
  cd tools/testing/integration
  node jwt-refresh-test.js
  ```

#### 2. 向量数据库测试 (`integration/test_pgvector.py`)
- **功能**: pgvector数据库功能测试
- **特性**: 向量存储和搜索功能验证
- **使用方法**:
  ```bash
  cd tools/testing/integration
  python test_pgvector.py
  ```

#### 3. 综合简历系统测试 (`integration/comprehensive_resume_test.py`)
- **功能**: 简历系统端到端测试
- **特性**: 
  - DeepSeek生成测试数据
  - 简历解析、入库和搜索功能
  - QA专家级测试方案
- **使用方法**:
  ```bash
  cd tools/testing/integration
  python comprehensive_resume_test.py
  ```

### 测试工具

#### 1. 代码覆盖率测试 (`utils/run_coverage.py`)
- **功能**: 生成代码覆盖率报告
- **特性**: 
  - 80%覆盖率目标
  - HTML报告生成
  - 模块级覆盖率统计
- **使用方法**:
  ```bash
  cd tools/testing/utils
  python run_coverage.py
  ```

## 🚀 快速开始

### 运行完整测试套件

```bash
# 1. 确保服务运行
make up

# 2. 运行API回归测试
./tools/testing/api/run_regression_tests.sh -r -c

# 3. 运行集成测试
./tools/testing/integration/jwt-refresh-test.js

# 4. 生成覆盖率报告
cd app/backend && python ../../tools/testing/utils/run_coverage.py
```

### 开发流程测试

```bash
# 开发时快速验证
./tools/testing/api/run_regression_tests.sh -t unit

# 代码提交前完整验证
./tools/testing/api/run_regression_tests.sh -t all -r -c
```

## 📊 测试报告

- **HTML测试报告**: `app/test_reports/test_report.html`
- **覆盖率报告**: `app/coverage/index.html` 
- **回归测试结果**: `app/backend/tests/api/regression/test_report.html`

## ⚠️ 注意事项

1. **环境要求**: 确保PostgreSQL、Redis、MinIO等服务正常运行
2. **权限设置**: 测试脚本可能需要执行权限 (`chmod +x`)
3. **依赖检查**: 运行前检查必要的Python包和Node.js模块
4. **网络访问**: 部分测试需要访问外部API（如DeepSeek）

## 🔄 维护指南

- **添加新测试**: 根据功能类型放入对应目录
- **更新工具**: 保持工具脚本与项目结构同步
- **清理规则**: 定期清理过时的临时测试文件
- **文档更新**: 新增工具时更新本README

## 📝 已清理的文件

以下过时/无用的测试文件已被清理：

- `app/frontend/test-i18n.js` - 简单i18n验证脚本（已被完整测试覆盖）
- `app/backend/tests/test_deepseek_simple.py` - 包含硬编码API密钥的简单测试
- `app/backend/tests/minimal_resume_test.py` - 临时最小化测试脚本  
- `app/backend/tests/quick_resume_test.py` - 临时快速测试脚本

## 🎯 最佳实践

1. **使用分层测试**: 单元测试 → 集成测试 → 端到端测试
2. **保持测试独立**: 每个测试应该能独立运行
3. **数据隔离**: 使用测试数据库，避免污染生产数据
4. **持续集成**: 将测试集成到CI/CD流程中