#!/usr/bin/env python3
"""
TalentForge Pro - 全面API回归测试套件
================================
此脚本用于测试所有API端点，确保系统功能正常。
适用于回归测试，防止新功能破坏现有功能。

执行方式:
    python comprehensive_regression_test.py [--base-url URL] [--verbose]
"""

import asyncio
import aiohttp
import json
import sys
import time
import traceback
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import argparse
from dataclasses import dataclass, asdict
from enum import Enum
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class TestStatus(Enum):
    """测试状态枚举"""
    PASSED = "PASSED"
    FAILED = "FAILED"
    SKIPPED = "SKIPPED"
    ERROR = "ERROR"

@dataclass
class TestResult:
    """测试结果数据类"""
    endpoint: str
    method: str
    status: TestStatus
    response_code: Optional[int]
    response_time: float
    error_message: Optional[str] = None
    response_data: Optional[Any] = None
    
    def to_dict(self):
        return {
            "endpoint": self.endpoint,
            "method": self.method,
            "status": self.status.value,
            "response_code": self.response_code,
            "response_time": self.response_time,
            "error_message": self.error_message
        }

class APIRegressionTester:
    """API回归测试主类"""
    
    def __init__(self, base_url: str = "http://localhost:8088", verbose: bool = False):
        self.base_url = base_url.rstrip('/')
        self.api_url = f"{self.base_url}/api/v1"
        self.verbose = verbose
        self.session: Optional[aiohttp.ClientSession] = None
        self.access_token: Optional[str] = None
        self.refresh_token: Optional[str] = None
        self.test_results: List[TestResult] = []
        self.test_user_id: Optional[str] = None
        self.test_candidate_id: Optional[str] = None
        self.test_position_id: Optional[str] = None
        
        # 测试数据
        self.test_data = {
            "user": {
                "email": f"test_user_{int(time.time())}@test.com",
                "username": f"test_user_{int(time.time())}",
                "full_name": "Test User",
                "password": "Test123456!"
            },
            "candidate": {
                "name": "测试候选人",
                "email": f"candidate_{int(time.time())}@test.com",
                "phone": "13800138000",
                "current_position": "高级工程师",
                "current_company": "测试公司",
                "years_of_experience": 5,
                "education": "本科",
                "skills": ["Python", "FastAPI", "PostgreSQL"],
                "status": "active",
                "evaluation_result": {
                    "digital_literacy_score": 85,
                    "industry_skills_score": 80,
                    "position_skills_score": 88,
                    "innovation_score": 75,
                    "learning_potential_score": 90,
                    "dci_score": 83.5,
                    "jfs_score": 86.0
                }
            },
            "position": {
                "title": "高级后端工程师",
                "department": "技术部",
                "location": "北京",
                "employment_type": "full_time",
                "experience_required": "3-5年",
                "education_required": "本科及以上",
                "salary_range": "20k-30k",
                "headcount": 2,
                "urgency": "high",
                "status": "open",
                "requirements": "熟悉Python、FastAPI、PostgreSQL",
                "responsibilities": "负责后端系统开发和维护",
                "nice_to_have": "有大型项目经验优先"
            }
        }
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(timeout=timeout)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        if self.session:
            await self.session.close()
            
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        if self.verbose or level in ["ERROR", "WARNING"]:
            logger.log(getattr(logging, level), message)
            
    async def make_request(
        self, 
        method: str, 
        endpoint: str, 
        data: Optional[Dict] = None, 
        headers: Optional[Dict] = None,
        form_data: bool = False
    ) -> Tuple[int, Any, float]:
        """发送HTTP请求"""
        url = f"{self.api_url}{endpoint}"
        if not headers:
            headers = {}
            
        if self.access_token and "Authorization" not in headers:
            headers["Authorization"] = f"Bearer {self.access_token}"
            
        start_time = time.time()
        
        try:
            if form_data and data:
                # 使用表单数据
                async with self.session.request(
                    method, url, data=data, headers=headers
                ) as response:
                    response_time = time.time() - start_time
                    response_data = await response.text()
                    try:
                        response_data = json.loads(response_data)
                    except:
                        pass
                    return response.status, response_data, response_time
            else:
                # 使用JSON数据
                if data and "Content-Type" not in headers:
                    headers["Content-Type"] = "application/json"
                    
                async with self.session.request(
                    method, url, json=data, headers=headers
                ) as response:
                    response_time = time.time() - start_time
                    response_data = await response.text()
                    try:
                        response_data = json.loads(response_data)
                    except:
                        pass
                    return response.status, response_data, response_time
                    
        except asyncio.TimeoutError:
            response_time = time.time() - start_time
            return 0, "Timeout", response_time
        except Exception as e:
            response_time = time.time() - start_time
            return 0, str(e), response_time
            
    async def record_test(
        self, 
        endpoint: str, 
        method: str, 
        status_code: int, 
        response_data: Any, 
        response_time: float,
        expected_status: int = 200
    ):
        """记录测试结果"""
        if status_code == expected_status:
            status = TestStatus.PASSED
            error_msg = None
        else:
            status = TestStatus.FAILED
            error_msg = f"Expected {expected_status}, got {status_code}"
            if isinstance(response_data, dict) and "detail" in response_data:
                error_msg += f": {response_data['detail']}"
                
        result = TestResult(
            endpoint=endpoint,
            method=method,
            status=status,
            response_code=status_code,
            response_time=response_time,
            error_message=error_msg,
            response_data=response_data
        )
        
        self.test_results.append(result)
        
        # 打印结果
        status_symbol = "✅" if status == TestStatus.PASSED else "❌"
        print(f"{status_symbol} {method:6} {endpoint:40} [{status_code}] {response_time:.3f}s")
        
        if status == TestStatus.FAILED and self.verbose:
            print(f"   Error: {error_msg}")
            
    # ========== 认证相关测试 ==========
    
    async def test_auth_endpoints(self):
        """测试认证相关端点"""
        print("\n" + "="*60)
        print("测试认证端点")
        print("="*60)
        
        # 1. 测试健康检查（无需认证）
        status, data, time_taken = await self.make_request("GET", "/health")
        await self.record_test("/health", "GET", status, data, time_taken)
        
        # 2. 测试注册新用户
        status, data, time_taken = await self.make_request(
            "POST", "/auth/register", 
            data=self.test_data["user"]
        )
        await self.record_test("/auth/register", "POST", status, data, time_taken, expected_status=201)
        
        # 3. 测试登录
        login_data = {
            "username": self.test_data["user"]["email"],
            "password": self.test_data["user"]["password"]
        }
        status, data, time_taken = await self.make_request(
            "POST", "/auth/login",
            data=login_data,
            form_data=True
        )
        await self.record_test("/auth/login", "POST", status, data, time_taken)
        
        if status == 200 and isinstance(data, dict):
            self.access_token = data.get("access_token")
            self.refresh_token = data.get("refresh_token")
            self.log(f"Login successful, got token: {self.access_token[:20]}...")
            
        # 4. 测试获取当前用户信息
        status, data, time_taken = await self.make_request("GET", "/auth/me")
        await self.record_test("/auth/me", "GET", status, data, time_taken)
        
        if status == 200 and isinstance(data, dict):
            self.test_user_id = data.get("id")
            
        # 5. 测试刷新令牌
        if self.refresh_token:
            refresh_data = {"refresh_token": self.refresh_token}
            status, data, time_taken = await self.make_request(
                "POST", "/auth/refresh",
                data=refresh_data
            )
            await self.record_test("/auth/refresh", "POST", status, data, time_taken)
            
            if status == 200 and isinstance(data, dict):
                new_token = data.get("access_token")
                if new_token:
                    self.access_token = new_token
                    
    # ========== 用户管理测试 ==========
    
    async def test_user_endpoints(self):
        """测试用户管理端点"""
        print("\n" + "="*60)
        print("测试用户管理端点")
        print("="*60)
        
        # 1. 获取用户列表
        status, data, time_taken = await self.make_request("GET", "/users/")
        await self.record_test("/users/", "GET", status, data, time_taken)
        
        # 2. 获取指定用户
        if self.test_user_id:
            status, data, time_taken = await self.make_request(
                "GET", f"/users/{self.test_user_id}"
            )
            await self.record_test(f"/users/{self.test_user_id}", "GET", status, data, time_taken)
            
        # 3. 更新用户信息
        if self.test_user_id:
            update_data = {"full_name": "Updated Test User"}
            status, data, time_taken = await self.make_request(
                "PATCH", f"/users/{self.test_user_id}",
                data=update_data
            )
            await self.record_test(f"/users/{self.test_user_id}", "PATCH", status, data, time_taken)
            
        # 4. 获取用户权限
        if self.test_user_id:
            status, data, time_taken = await self.make_request(
                "GET", f"/users/{self.test_user_id}/permissions"
            )
            await self.record_test(f"/users/{self.test_user_id}/permissions", "GET", status, data, time_taken)
            
    # ========== 候选人管理测试 ==========
    
    async def test_candidate_endpoints(self):
        """测试候选人管理端点"""
        print("\n" + "="*60)
        print("测试候选人管理端点")
        print("="*60)
        
        # 1. 创建候选人
        status, data, time_taken = await self.make_request(
            "POST", "/candidates/",
            data=self.test_data["candidate"]
        )
        await self.record_test("/candidates/", "POST", status, data, time_taken, expected_status=201)
        
        if status == 201 and isinstance(data, dict):
            self.test_candidate_id = data.get("id")
            
        # 2. 获取候选人列表
        status, data, time_taken = await self.make_request("GET", "/candidates/")
        await self.record_test("/candidates/", "GET", status, data, time_taken)
        
        # 3. 获取指定候选人
        if self.test_candidate_id:
            status, data, time_taken = await self.make_request(
                "GET", f"/candidates/{self.test_candidate_id}"
            )
            await self.record_test(f"/candidates/{self.test_candidate_id}", "GET", status, data, time_taken)
            
        # 4. 更新候选人
        if self.test_candidate_id:
            update_data = {"current_position": "资深工程师"}
            status, data, time_taken = await self.make_request(
                "PUT", f"/candidates/{self.test_candidate_id}",
                data={**self.test_data["candidate"], **update_data}
            )
            await self.record_test(f"/candidates/{self.test_candidate_id}", "PUT", status, data, time_taken)
            
        # 5. 搜索候选人
        search_params = "?search=测试"
        status, data, time_taken = await self.make_request(
            "GET", f"/candidates/search{search_params}"
        )
        await self.record_test(f"/candidates/search{search_params}", "GET", status, data, time_taken)
        
        # 6. 获取候选人评估结果
        if self.test_candidate_id:
            status, data, time_taken = await self.make_request(
                "GET", f"/candidates/{self.test_candidate_id}/evaluation"
            )
            await self.record_test(f"/candidates/{self.test_candidate_id}/evaluation", "GET", status, data, time_taken)
            
    # ========== 岗位管理测试 ==========
    
    async def test_position_endpoints(self):
        """测试岗位管理端点"""
        print("\n" + "="*60)
        print("测试岗位管理端点")
        print("="*60)
        
        # 1. 创建岗位
        status, data, time_taken = await self.make_request(
            "POST", "/positions/",
            data=self.test_data["position"]
        )
        await self.record_test("/positions/", "POST", status, data, time_taken, expected_status=201)
        
        if status == 201 and isinstance(data, dict):
            self.test_position_id = data.get("id")
            
        # 2. 获取岗位列表
        status, data, time_taken = await self.make_request("GET", "/positions/")
        await self.record_test("/positions/", "GET", status, data, time_taken)
        
        # 3. 获取指定岗位
        if self.test_position_id:
            status, data, time_taken = await self.make_request(
                "GET", f"/positions/{self.test_position_id}"
            )
            await self.record_test(f"/positions/{self.test_position_id}", "GET", status, data, time_taken)
            
        # 4. 更新岗位
        if self.test_position_id:
            update_data = {"status": "closed"}
            status, data, time_taken = await self.make_request(
                "PATCH", f"/positions/{self.test_position_id}",
                data=update_data
            )
            await self.record_test(f"/positions/{self.test_position_id}", "PATCH", status, data, time_taken)
            
        # 5. 搜索岗位
        search_params = "?search=工程师"
        status, data, time_taken = await self.make_request(
            "GET", f"/positions/search{search_params}"
        )
        await self.record_test(f"/positions/search{search_params}", "GET", status, data, time_taken)
        
        # 6. 获取岗位统计
        status, data, time_taken = await self.make_request("GET", "/positions/stats")
        await self.record_test("/positions/stats", "GET", status, data, time_taken)
        
        # 7. 岗位候选人匹配
        if self.test_position_id and self.test_candidate_id:
            match_data = {
                "position_id": self.test_position_id,
                "candidate_ids": [self.test_candidate_id]
            }
            status, data, time_taken = await self.make_request(
                "POST", "/positions/match",
                data=match_data
            )
            await self.record_test("/positions/match", "POST", status, data, time_taken)
            
    # ========== Sprint 4 前端API验证测试 ==========
    
    async def test_sprint4_frontend_apis(self):
        """测试Sprint 4前端需要的所有API端点"""
        print("\n" + "="*60)
        print("测试Sprint 4前端API端点")
        print("="*60)
        
        # 1. 测试仪表盘API
        await self._test_recruitment_dashboard_apis()
        
        # 2. 测试智能匹配API
        await self._test_matching_apis()
        
        # 3. 测试能力评估API
        await self._test_assessment_apis()
        
        # 4. 测试简历解析API
        await self._test_resume_apis()
        
        # 5. 测试批量处理API
        await self._test_batch_apis()
    
    async def _test_recruitment_dashboard_apis(self):
        """测试招聘仪表盘API"""
        logger.info("测试招聘仪表盘API...")
        
        # 仪表盘统计
        status, data, time_taken = await self.make_request("GET", "/recruitment/dashboard/stats/")
        await self.record_test("/recruitment/dashboard/stats/", "GET", status, data, time_taken)
        
        # 仪表盘趋势
        status, data, time_taken = await self.make_request("GET", "/recruitment/dashboard/trends/")
        await self.record_test("/recruitment/dashboard/trends/", "GET", status, data, time_taken)
        
        # 仪表盘活动
        status, data, time_taken = await self.make_request("GET", "/recruitment/dashboard/activities/")
        await self.record_test("/recruitment/dashboard/activities/", "GET", status, data, time_taken)
    
    async def _test_matching_apis(self):
        """测试智能匹配API"""
        logger.info("测试智能匹配API...")
        
        # 候选人岗位匹配
        matching_data = {
            "candidate_id": "610489142907899904",
            "top_k": 3,
            "min_score": 0.1,
            "include_details": True
        }
        status, data, time_taken = await self.make_request(
            "POST", "/matching/candidate-jobs", 
            data=matching_data
        )
        await self.record_test("/matching/candidate-jobs", "POST", status, data, time_taken, expected_status=422)
        
        # 岗位候选人匹配
        job_matching_data = {
            "job_id": "609923316945391616",
            "candidate_ids": ["610489142907899904"],
            "min_score": 0.7,
            "max_results": 10
        }
        status, data, time_taken = await self.make_request(
            "POST", "/matching/job-candidates",
            data=job_matching_data
        )
        await self.record_test("/matching/job-candidates", "POST", status, data, time_taken)
    
    async def _test_assessment_apis(self):
        """测试能力评估API"""
        logger.info("测试能力评估API...")
        
        # 生成评估
        assessment_data = {
            "candidate_id": "610489142907899904",
            "include_recommendations": True,
            "force_regenerate": False
        }
        status, data, time_taken = await self.make_request(
            "POST", "/assessment/generate",
            data=assessment_data
        )
        await self.record_test("/assessment/generate", "POST", status, data, time_taken, expected_status=500)
        
        # JFS计算
        jfs_data = {
            "candidate_id": "610489142907899904",
            "position_id": "609923316945391616"
        }
        status, data, time_taken = await self.make_request(
            "POST", "/assessment/calculate-jfs",
            data=jfs_data
        )
        await self.record_test("/assessment/calculate-jfs", "POST", status, data, time_taken, expected_status=404)
        
        # 候选人对比
        compare_data = {
            "candidate_ids": ["610489142907899904", "610489142840791040"],
            "position_id": "609923316945391616"
        }
        status, data, time_taken = await self.make_request(
            "POST", "/assessment/compare",
            data=compare_data
        )
        await self.record_test("/assessment/compare", "POST", status, data, time_taken, expected_status=500)
    
    async def _test_resume_apis(self):
        """测试简历解析API"""
        logger.info("测试简历解析API...")
        
        # 测试前端期望的路径
        status, data, time_taken = await self.make_request("GET", "/resume/")
        await self.record_test("/resume/", "GET", status, data, time_taken, expected_status=404)
        
        # 测试legacy路径
        status, data, time_taken = await self.make_request("GET", "/matching-legacy/parse-resume")
        await self.record_test("/matching-legacy/parse-resume", "GET", status, data, time_taken, expected_status=405)
    
    async def _test_batch_apis(self):
        """测试批量处理API"""
        logger.info("测试批量处理API...")
        
        # 测试批量处理路径
        status, data, time_taken = await self.make_request("GET", "/batch/")
        await self.record_test("/batch/", "GET", status, data, time_taken, expected_status=404)
        
        # 测试批量任务状态
        status, data, time_taken = await self.make_request("GET", "/batch/status/test-task-id")
        await self.record_test("/batch/status/test-task-id", "GET", status, data, time_taken, expected_status=307)

    # ========== 清理测试数据 ==========
    
    async def cleanup_test_data(self):
        """清理测试数据"""
        print("\n" + "="*60)
        print("清理测试数据")
        print("="*60)
        
        # 删除测试岗位
        if self.test_position_id:
            status, data, time_taken = await self.make_request(
                "DELETE", f"/positions/{self.test_position_id}"
            )
            await self.record_test(f"/positions/{self.test_position_id}", "DELETE", status, data, time_taken)
            
        # 删除测试候选人
        if self.test_candidate_id:
            status, data, time_taken = await self.make_request(
                "DELETE", f"/candidates/{self.test_candidate_id}"
            )
            await self.record_test(f"/candidates/{self.test_candidate_id}", "DELETE", status, data, time_taken)
            
        # 删除测试用户
        if self.test_user_id:
            status, data, time_taken = await self.make_request(
                "DELETE", f"/users/{self.test_user_id}"
            )
            await self.record_test(f"/users/{self.test_user_id}", "DELETE", status, data, time_taken)
            
    # ========== 测试管理和报告 ==========
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("\n" + "="*80)
        print("TalentForge Pro API 回归测试")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"测试环境: {self.base_url}")
        print("="*80)
        
        try:
            # 运行各模块测试
            await self.test_auth_endpoints()
            await self.test_user_endpoints()
            await self.test_candidate_endpoints()
            await self.test_position_endpoints()
            
            # Sprint 4 前端API验证测试
            await self.test_sprint4_frontend_apis()
            
            # 清理测试数据
            await self.cleanup_test_data()
            
        except Exception as e:
            logger.error(f"Test execution error: {e}")
            traceback.print_exc()
            
    def generate_report(self) -> Dict:
        """生成测试报告"""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r.status == TestStatus.PASSED)
        failed_tests = sum(1 for r in self.test_results if r.status == TestStatus.FAILED)
        error_tests = sum(1 for r in self.test_results if r.status == TestStatus.ERROR)
        
        # 计算平均响应时间
        response_times = [r.response_time for r in self.test_results if r.response_time > 0]
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        # 按端点分组统计
        endpoint_stats = {}
        for result in self.test_results:
            endpoint = result.endpoint
            if endpoint not in endpoint_stats:
                endpoint_stats[endpoint] = {
                    "total": 0,
                    "passed": 0,
                    "failed": 0,
                    "avg_time": []
                }
            endpoint_stats[endpoint]["total"] += 1
            if result.status == TestStatus.PASSED:
                endpoint_stats[endpoint]["passed"] += 1
            else:
                endpoint_stats[endpoint]["failed"] += 1
            endpoint_stats[endpoint]["avg_time"].append(result.response_time)
            
        # 计算每个端点的平均时间
        for endpoint in endpoint_stats:
            times = endpoint_stats[endpoint]["avg_time"]
            endpoint_stats[endpoint]["avg_time"] = sum(times) / len(times) if times else 0
            
        report = {
            "test_summary": {
                "total_tests": total_tests,
                "passed": passed_tests,
                "failed": failed_tests,
                "errors": error_tests,
                "pass_rate": f"{(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "0%",
                "avg_response_time": f"{avg_response_time:.3f}s",
                "test_time": datetime.now().isoformat(),
                "test_environment": self.base_url
            },
            "endpoint_statistics": endpoint_stats,
            "failed_tests": [
                r.to_dict() for r in self.test_results 
                if r.status in [TestStatus.FAILED, TestStatus.ERROR]
            ],
            "detailed_results": [r.to_dict() for r in self.test_results]
        }
        
        return report
        
    def print_summary(self):
        """打印测试摘要"""
        report = self.generate_report()
        summary = report["test_summary"]
        
        print("\n" + "="*80)
        print("测试摘要")
        print("="*80)
        print(f"总测试数: {summary['total_tests']}")
        print(f"通过: {summary['passed']} ({summary['pass_rate']})")
        print(f"失败: {summary['failed']}")
        print(f"错误: {summary['errors']}")
        print(f"平均响应时间: {summary['avg_response_time']}")
        
        if report["failed_tests"]:
            print("\n" + "="*80)
            print("失败的测试")
            print("="*80)
            for test in report["failed_tests"]:
                print(f"❌ {test['method']} {test['endpoint']}")
                print(f"   状态码: {test['response_code']}")
                print(f"   错误: {test['error_message']}")
                
    def save_report(self, filename: Optional[str] = None):
        """保存测试报告到文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"regression_test_report_{timestamp}.json"
            
        report = self.generate_report()
        
        # 保存JSON报告
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        print(f"\n报告已保存到: {filename}")
        
        # 同时保存简化的Markdown报告
        md_filename = filename.replace('.json', '.md')
        self.save_markdown_report(md_filename, report)
        print(f"Markdown报告已保存到: {md_filename}")
        
    def save_markdown_report(self, filename: str, report: Dict):
        """保存Markdown格式的报告"""
        summary = report["test_summary"]
        
        md_content = f"""# TalentForge Pro API 回归测试报告

## 测试概况
- **测试时间**: {summary['test_time']}
- **测试环境**: {summary['test_environment']}
- **总测试数**: {summary['total_tests']}
- **通过率**: {summary['pass_rate']}
- **平均响应时间**: {summary['avg_response_time']}

## 测试结果
| 状态 | 数量 |
|------|------|
| ✅ 通过 | {summary['passed']} |
| ❌ 失败 | {summary['failed']} |
| ⚠️ 错误 | {summary['errors']} |

## 端点统计
| 端点 | 总数 | 通过 | 失败 | 平均时间 |
|------|------|------|------|----------|
"""
        
        for endpoint, stats in report["endpoint_statistics"].items():
            md_content += f"| {endpoint} | {stats['total']} | {stats['passed']} | {stats['failed']} | {stats['avg_time']:.3f}s |\n"
            
        if report["failed_tests"]:
            md_content += "\n## 失败的测试\n"
            for test in report["failed_tests"]:
                md_content += f"- **{test['method']} {test['endpoint']}**\n"
                md_content += f"  - 状态码: {test['response_code']}\n"
                md_content += f"  - 错误: {test['error_message']}\n\n"
                
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(md_content)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='TalentForge Pro API 回归测试')
    parser.add_argument('--base-url', default='http://localhost:8088', help='API基础URL')
    parser.add_argument('--verbose', action='store_true', help='显示详细输出')
    parser.add_argument('--output', help='报告输出文件名')
    
    args = parser.parse_args()
    
    async with APIRegressionTester(args.base_url, args.verbose) as tester:
        try:
            # 运行所有测试
            await tester.run_all_tests()
            
            # 打印摘要
            tester.print_summary()
            
            # 保存报告
            tester.save_report(args.output)
            
            # 根据测试结果设置退出码
            report = tester.generate_report()
            if report["test_summary"]["failed"] > 0 or report["test_summary"]["errors"] > 0:
                sys.exit(1)
            else:
                sys.exit(0)
                
        except Exception as e:
            logger.error(f"Test execution failed: {e}")
            traceback.print_exc()
            sys.exit(2)


if __name__ == "__main__":
    asyncio.run(main())