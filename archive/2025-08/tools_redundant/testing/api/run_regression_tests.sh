#!/bin/bash
# TalentForge Pro - API回归测试启动脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 脚本目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/../../.." && pwd )"
APP_DIR="$PROJECT_ROOT/app"

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
使用方式: $(basename "$0") [OPTIONS]

TalentForge Pro API回归测试工具

选项:
    -h, --help          显示帮助信息
    -t, --type TYPE     测试类型 (all|unit|integration|regression|performance)
                        默认: regression
    -e, --env ENV       环境 (local|docker)
                        默认: docker
    -r, --report        生成HTML测试报告
    -c, --coverage      生成代码覆盖率报告
    -v, --verbose       详细输出
    -b, --build         重新构建测试容器
    -k, --keep          测试后保留容器
    --no-cache          构建时不使用缓存

示例:
    # 运行所有回归测试
    $(basename "$0")
    
    # 运行性能测试
    $(basename "$0") -t performance
    
    # 本地运行测试（不使用Docker）
    $(basename "$0") -e local
    
    # 生成测试报告和覆盖率
    $(basename "$0") -r -c

EOF
}

# 默认参数
TEST_TYPE="regression"
TEST_ENV="docker"
GENERATE_REPORT=false
GENERATE_COVERAGE=false
VERBOSE=false
BUILD_CONTAINER=false
KEEP_CONTAINER=false
NO_CACHE=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--type)
            TEST_TYPE="$2"
            shift 2
            ;;
        -e|--env)
            TEST_ENV="$2"
            shift 2
            ;;
        -r|--report)
            GENERATE_REPORT=true
            shift
            ;;
        -c|--coverage)
            GENERATE_COVERAGE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -b|--build)
            BUILD_CONTAINER=true
            shift
            ;;
        -k|--keep)
            KEEP_CONTAINER=true
            shift
            ;;
        --no-cache)
            NO_CACHE="--no-cache"
            shift
            ;;
        *)
            print_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 切换到应用目录
cd "$APP_DIR"

# 检查服务状态
check_services() {
    print_info "检查服务状态..."
    
    # 检查必要的服务是否运行
    if ! docker ps | grep -q hephaestus_postgres; then
        print_error "PostgreSQL服务未运行"
        print_info "请先运行: make up-basic"
        exit 1
    fi
    
    if ! docker ps | grep -q hephaestus_redis; then
        print_error "Redis服务未运行"
        print_info "请先运行: make up-basic"
        exit 1
    fi
    
    print_success "服务状态检查通过"
}

# 构建测试容器
build_test_container() {
    print_info "构建测试容器..."
    
    docker build $NO_CACHE \
        -f backend/Dockerfile.test \
        -t hephaestus_api_tester:latest \
        backend/
    
    print_success "测试容器构建完成"
}

# Docker环境运行测试
run_docker_tests() {
    print_info "在Docker环境中运行测试..."
    
    # 构建容器（如果需要）
    if [ "$BUILD_CONTAINER" = true ]; then
        build_test_container
    fi
    
    # 准备测试命令
    TEST_CMD="pytest"
    
    case $TEST_TYPE in
        all)
            TEST_CMD="pytest tests/"
            ;;
        unit)
            TEST_CMD="pytest tests/unit/"
            ;;
        integration)
            TEST_CMD="pytest tests/integration/"
            ;;
        regression)
            TEST_CMD="pytest tests/api/regression/ --tavern-global-cfg tests/api/regression/common.yaml"
            ;;
        performance)
            # 使用Locust进行性能测试
            docker-compose -f docker-compose.test.yml run --rm performance-tester
            print_success "性能测试完成"
            return
            ;;
    esac
    
    # 添加报告和覆盖率选项
    if [ "$GENERATE_REPORT" = true ]; then
        TEST_CMD="$TEST_CMD --html=/app/test_reports/test_report.html --self-contained-html"
    fi
    
    if [ "$GENERATE_COVERAGE" = true ]; then
        TEST_CMD="$TEST_CMD --cov=app --cov-report=html:/app/coverage --cov-report=term"
    fi
    
    if [ "$VERBOSE" = true ]; then
        TEST_CMD="$TEST_CMD -v"
    fi
    
    # 运行测试
    docker-compose -f docker-compose.test.yml run --rm api-tester $TEST_CMD
    
    # 清理容器（如果不保留）
    if [ "$KEEP_CONTAINER" = false ]; then
        docker-compose -f docker-compose.test.yml down
    fi
    
    print_success "测试执行完成"
}

# 本地环境运行测试
run_local_tests() {
    print_info "在本地环境中运行测试..."
    
    # 切换到后端目录
    cd backend
    
    # 设置环境变量
    export DATABASE_URL="postgresql+asyncpg://postgres:Pass1234@localhost:15432/hephaestus_forge_db"
    export REDIS_URL="redis://:Pass1234@localhost:16379/0"
    export API_BASE_URL="http://localhost:8001"
    
    # 激活虚拟环境（如果存在）
    if [ -f "venv/bin/activate" ]; then
        source venv/bin/activate
    fi
    
    # 安装测试依赖
    print_info "安装测试依赖..."
    pip install -q tavern pytest-tavern pytest-html pytest-cov
    
    # 运行测试
    case $TEST_TYPE in
        all)
            pytest tests/
            ;;
        unit)
            pytest tests/unit/
            ;;
        integration)
            pytest tests/integration/
            ;;
        regression)
            pytest tests/api/regression/ --tavern-global-cfg tests/api/regression/common.yaml
            ;;
        performance)
            print_warning "本地性能测试需要安装Locust"
            locust -f tests/performance/locustfile.py --headless -u 10 -r 1 -t 60s
            ;;
    esac
    
    print_success "测试执行完成"
}

# 显示测试报告
show_test_report() {
    if [ "$GENERATE_REPORT" = true ]; then
        print_info "测试报告已生成:"
        echo "  - HTML报告: $APP_DIR/test_reports/test_report.html"
        
        # 如果是WSL环境，尝试用Windows浏览器打开
        if grep -qi microsoft /proc/version; then
            explorer.exe "$APP_DIR/test_reports/test_report.html" 2>/dev/null || true
        fi
    fi
    
    if [ "$GENERATE_COVERAGE" = true ]; then
        print_info "覆盖率报告已生成:"
        echo "  - 覆盖率报告: $APP_DIR/coverage/index.html"
    fi
}

# 主流程
main() {
    print_info "TalentForge Pro API回归测试"
    print_info "测试类型: $TEST_TYPE"
    print_info "测试环境: $TEST_ENV"
    
    # 检查服务状态
    check_services
    
    # 根据环境运行测试
    if [ "$TEST_ENV" = "docker" ]; then
        run_docker_tests
    else
        run_local_tests
    fi
    
    # 显示报告
    show_test_report
    
    print_success "所有测试任务完成！"
}

# 运行主流程
main